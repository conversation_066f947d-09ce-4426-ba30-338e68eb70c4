<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Report\Gmc;

use Model\Smc\DealModel;

class  GmcCrmReportModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();
    public $company_isassist = 0;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //渠道版块统计报表
    function channelSectionReport($request)
    {
        $datawhere = "l.company_id='{$request['company_id']}'";
        $trackwhere = "c.company_id ='{$request['company_id']}' AND c.client_id = t.client_id AND t.track_isactive = '1' AND t.track_validinc = '1'";
        $auditionwhere = "c.company_id ='{$request['company_id']}' AND c.client_id = a.client_id";
        $invitenwhere = "c.company_id ='{$request['company_id']}' AND c.client_id = iv.client_id ";
        $atprewhere = "c.company_id ='{$request['company_id']}'";
        $clientwhere = "c.company_id ='{$request['company_id']}'";
        $positivewhere = "c.company_id ='{$request['company_id']}' AND e.client_id = c.client_id";
        $registerwhere = " i.company_id='{$request['company_id']}' and info_status =1";
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (cc.frommedia_name like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $trackwhere .= " and t.track_createtime >='{$stattime}'";
            $auditionwhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $invitenwhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $positivewhere .= " and DATE_FORMAT(e.positivelog_time,'%Y-%m-%d') >='{$request['start_time']}'";
            $registerwhere .= " and i.pay_successtime > '{$stattime}'";
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {

            $endtime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endtime);
            $trackwhere .= " and t.track_createtime <='{$endime}'";
            $auditionwhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <= '{$request['end_time']}'";
            $invitenwhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $positivewhere .= " and DATE_FORMAT(e.positivelog_time,'%Y-%m-%d') <= '{$request['end_time']}'";
            $registerwhere .= " and i.pay_successtime < '{$endime}'";
            $clientwhere .= " and c.client_createtime <='{$endime}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_openclass", "school_id='{$request['school_id']}'");
            $trackwhere .= " and t.school_id ='{$request['school_id']}'";
            $auditionwhere .= " and a.school_id ='{$request['school_id']}'";
            $invitenwhere .= " and iv.school_id ='{$request['school_id']}'";
            $atprewhere .= " and e.school_id ='{$request['school_id']}'";
            $positivewhere .= " and e.school_id ='{$request['school_id']}'";
            $registerwhere .= " and i.school_id='{$request['school_id']}'";
            $clientwhere .= " and e.school_id ='{$request['school_id']}'";
        }

        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p
WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            //$trackwhere .= " and t.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            $auditionwhere .= " and a.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            $invitenwhere .= " and iv.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            //$atprewhere .= " and e.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            $positivewhere .= " and e.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            $registerwhere .= " and i.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            $clientwhere .= " and e.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                //$trackwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auditionwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invitenwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                //$atprewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $positivewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $registerwhere .= " AND i.coursetype_id = '{$request['coursetype_id']}'";
            } else {
                //$trackwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auditionwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitenwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                //$atprewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $positivewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $registerwhere .= " AND i.coursetype_id = '{$request['coursetype_id']}'";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            //$trackwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auditionwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitenwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            //$atprewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $positivewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $registerwhere .= " AND i.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "SELECT GROUP_CONCAT(l.channel_id) AS channelidstring,COUNT(l.channel_id) AS channel_num,l.channel_way,l.channel_isbazaar,l.channel_board 
        FROM crm_code_channel AS l WHERE {$datawhere} GROUP BY l.channel_board";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->AnalyzeControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['section_name'] = $val['channel_board'];
                    $datearray['channel_num'] = $val['channel_num'];

                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newaddnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.channel_id IN({$val['channelidstring']})  LIMIT 0,1");
                    $datearray['client_newaddnums'] = $clientCount['client_newaddnums'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newvalidnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' AND c.channel_id IN({$val['channelidstring']}) LIMIT 0,1");
                    $datearray['client_newvalidnums'] = $clientCount['client_newvalidnums'];
                    if ($datearray['client_newaddnums'] > 0) {
                        $datearray['client_validrate'] = round(($datearray['client_newvalidnums'] / $datearray['client_newaddnums']) * 100, 2) . '%';
                    } else {
                        $datearray['client_validrate'] = "0" . '%';
                    }
                    $unClientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_nums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus = '-1' AND c.channel_id IN({$val['channelidstring']}) ");
                    $datearray['client_unvalidnums'] = $unClientCount['client_nums'];
                    $inv_aud_num = $this->DataControl->selectOne(" select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}))
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} AND c.channel_id IN({$val['channelidstring']}) and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}) and iv.client_id =a.client_id ) ) as nums ");
                    $datearray['inv_aud_num'] = $inv_aud_num['nums'];
                    $inv_aud_arrivenum = $this->DataControl->selectOne("select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 AND c.channel_id IN({$val['channelidstring']}))
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_isvisit =1  AND c.channel_id IN({$val['channelidstring']}) and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}) and iv.client_id =a.client_id ) ) as nums ");
                    $datearray['inv_aud_arrivenum'] = $inv_aud_arrivenum['nums'];
                    $datearray['inv_aud_rate'] = $inv_aud_num['nums'] > 0 ? round($inv_aud_arrivenum['nums'] / $inv_aud_num['nums'], 4) * 100 . '%' : '0%';

                    $invite_num = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}) ");
                    $datearray['invite_num'] = $invite_num['nums'];
                    $invite_arrivenum = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 AND c.channel_id IN({$val['channelidstring']}) ");
                    $datearray['invite_arrivenum'] = $invite_arrivenum['nums'];
                    if (isset($request['school_id']) && $request['school_id'] !== "" && $schoolOne['school_openclass'] == 0) {
                        $datearray['OH_audition_num'] = '--';
                        $datearray['OH_audition_arrivenum'] = '--';
                    } else {
                        $OH_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0 AND c.channel_id IN({$val['channelidstring']}) ");
                        $datearray['OH_audition_num'] = $OH_audition_num['nums'];
                        $OH_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0 and a.audition_isvisit =1 AND c.channel_id IN({$val['channelidstring']}) ");
                        $datearray['OH_audition_arrivenum'] = $OH_audition_arrivenum['nums'];
                    }
                    $Class_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1 AND c.channel_id IN({$val['channelidstring']}) ");
                    $datearray['Class_audition_num'] = $Class_audition_num['nums'];
                    $Class_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1 and a.audition_isvisit =1 AND c.channel_id IN({$val['channelidstring']}) ");
                    $datearray['Class_audition_arrivenum'] = $Class_audition_arrivenum['nums'];
                    $positivenum = $this->DataControl->selectOne("select count(e.client_id) as nums from crm_client_positivelog  as e,crm_client as c where e.client_id = c.client_id AND c.channel_id IN({$val['channelidstring']}) and {$positivewhere}");
                    $datearray['positivenum'] = $positivenum['nums'];
                    $registernum = $this->DataControl->selectOne("select count(st.student_id) as nums from  smc_student_registerinfo as i,smc_student as st,crm_client as c where i.student_id=st.student_id and st.from_client_id=c.client_id AND c.channel_id IN({$val['channelidstring']}) and {$registerwhere}");
                    $datearray['registernum'] = $registernum['nums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('渠道版块名称', '渠道明细数', '新增毛名单', '新增有效名单', '新增有效率', '新增无意向名单数', '邀约名单数', '到访名单数', '到访率', '柜询邀约名单数', '柜询邀约到访名单数', 'OH邀约名单数', 'OH邀约到访名单数', "插班试听邀约名单数", '插班试听邀约到访名单数', '报名名单数', "实缴名单"));
            $excelfileds = array('section_name', 'channel_num', 'client_newaddnums', 'client_newvalidnums', 'client_validrate', 'client_unvalidnums', "inv_aud_num", 'inv_aud_arrivenum', 'inv_aud_rate', 'invite_num', 'invite_arrivenum', 'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'positivenum', 'registernum');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("渠道招生统计报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as &$val) {
                    $val['section_name'] = $val['channel_board'];

                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newaddnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.channel_id IN({$val['channelidstring']}) LIMIT 0,1");
                    $val['client_newaddnums'] = $clientCount['client_newaddnums'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newvalidnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' AND c.channel_id IN({$val['channelidstring']}) LIMIT 0,1");
                    $val['client_newvalidnums'] = $clientCount['client_newvalidnums'];
                    if ($val['client_newaddnums'] > 0) {
                        $val['client_validrate'] = round(($val['client_newvalidnums'] / $val['client_newaddnums']) * 100, 2) . '%';
                    } else {
                        $val['client_validrate'] = "0" . '%';
                    }
                    $unClientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_nums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus = '-1' AND c.channel_id IN({$val['channelidstring']}) ");
                    $val['client_unvalidnums'] = $unClientCount['client_nums'];
                    $inv_aud_num = $this->DataControl->selectOne("select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}))
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} AND c.channel_id IN({$val['channelidstring']}) and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}) and iv.client_id =a.client_id )  ) as nums ");
                    $val['inv_aud_num'] = $inv_aud_num['nums'];
                    $inv_aud_arrivenum = $this->DataControl->selectOne("select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 AND c.channel_id IN({$val['channelidstring']}))
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_isvisit =1  AND c.channel_id IN({$val['channelidstring']}) and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} AND c.channel_id IN({$val['channelidstring']}) and iv.client_id =a.client_id ) ) as nums ");
                    $val['inv_aud_arrivenum'] = $inv_aud_arrivenum['nums'];
                    $val['inv_aud_rate'] = $val['inv_aud_num'] > 0 ? round($val['inv_aud_arrivenum'] / $val['inv_aud_num'], 4) * 100 . '%' : '0%';
                    $invite_num = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere}  AND c.channel_id IN({$val['channelidstring']}) ");
                    $val['invite_num'] = $invite_num['nums'];
                    $invite_arrivenum = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 AND c.channel_id IN({$val['channelidstring']}) ");
                    $val['invite_arrivenum'] = $invite_arrivenum['nums'];

                    if (isset($request['school_id']) && $request['school_id'] !== "" && $schoolOne['school_openclass'] == 0) {
                        $val['OH_audition_num'] = '--';
                        $val['OH_audition_arrivenum'] = '--';
                    } else {
                        $OH_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0  AND c.channel_id IN({$val['channelidstring']}) ");
                        $val['OH_audition_num'] = $OH_audition_num['nums'];
                        $OH_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0 and a.audition_isvisit =1  AND c.channel_id IN({$val['channelidstring']}) ");
                        $val['OH_audition_arrivenum'] = $OH_audition_arrivenum['nums'];

                    }
                    $Class_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1  AND c.channel_id IN({$val['channelidstring']}) ");
                    $val['Class_audition_num'] = $Class_audition_num['nums'];
                    $Class_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1 and a.audition_isvisit =1  AND c.channel_id IN({$val['channelidstring']}) ");
                    $val['Class_audition_arrivenum'] = $Class_audition_arrivenum['nums'];
                    $positivenum = $this->DataControl->selectOne("select count(e.client_id) as nums from crm_client_positivelog  as e,crm_client as c where e.client_id = c.client_id AND c.channel_id IN({$val['channelidstring']}) and {$positivewhere}");
                    $val['positivenum'] = $positivenum['nums'];
                    $registernum = $this->DataControl->selectOne("select count(st.student_id) as nums from  smc_student_registerinfo as i,smc_student as st,crm_client as c where i.student_id=st.student_id and st.from_client_id=c.client_id AND c.channel_id IN({$val['channelidstring']}) and {$registerwhere}");
                    $val['registernum'] = $registernum['nums'];
                }
            }
            if (!$datalist) {
                $datalist = array();
            }
            $data['list'] = $datalist;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $mediaCount = $this->AnalyzeControl->selectOne("SELECT COUNT(DISTINCT l.channel_way,l.channel_isbazaar) AS countnums
FROM crm_code_channel AS l WHERE {$datawhere}");
            if ($mediaCount) {
                $data['allnums'] = $mediaCount['countnums'];
            } else {
                $data['allnums'] = 0;
            }
        }

        return $data;

    }

    //   渠道招生统计报表
    function channelClientReport($request)
    {
        $datawhere = "cc.company_id='{$request['company_id']}'";
        $trackwhere = "c.company_id ='{$request['company_id']}' AND c.client_id = t.client_id AND t.track_isactive = '1' AND t.track_validinc = '1'";
        $auditionwhere = "c.company_id ='{$request['company_id']}' AND c.client_id = a.client_id";
        $invitenwhere = "c.company_id ='{$request['company_id']}' AND c.client_id = iv.client_id ";
        $atprewhere = "c.company_id ='{$request['company_id']}'";
        $clientwhere = "c.company_id ='{$request['company_id']}'";
        $positivewhere = "c.company_id ='{$request['company_id']}' AND e.client_id = c.client_id";
        $registerwhere = " i.company_id='{$request['company_id']}' and info_status =1";
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (cc.frommedia_name like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $trackwhere .= " and t.track_createtime >='{$stattime}'";
            $auditionwhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $invitenwhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $positivewhere .= " and DATE_FORMAT(e.positivelog_time,'%Y-%m-%d') >='{$request['start_time']}'";
            $registerwhere .= " and i.pay_successtime > '{$stattime}'";
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {

            $endtime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endtime);
            $trackwhere .= " and t.track_createtime <='{$endime}'";
            $auditionwhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <= '{$request['end_time']}'";
            $invitenwhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $positivewhere .= " and DATE_FORMAT(e.positivelog_time,'%Y-%m-%d') <= '{$request['end_time']}'";
            $registerwhere .= " and i.pay_successtime < '{$endime}'";
            $clientwhere .= " and c.client_createtime <='{$endime}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_openclass", "school_id='{$request['school_id']}'");
            $trackwhere .= " and t.school_id ='{$request['school_id']}'";
            $auditionwhere .= " and a.school_id ='{$request['school_id']}'";
            $invitenwhere .= " and iv.school_id ='{$request['school_id']}'";
            $atprewhere .= " and e.school_id ='{$request['school_id']}'";
            $positivewhere .= " and e.school_id ='{$request['school_id']}'";
            $registerwhere .= " and i.school_id='{$request['school_id']}'";
            $clientwhere .= " and e.school_id ='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $trackwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $auditionwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invitenwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $atprewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $positivewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $registerwhere .= " AND i.coursetype_id = '{$request['coursetype_id']}'";
            } else {
                $trackwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auditionwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitenwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $atprewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $positivewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $registerwhere .= " AND i.coursetype_id = '{$request['coursetype_id']}'";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $trackwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auditionwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitenwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $atprewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $positivewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $registerwhere .= " AND i.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select cc.frommedia_name
,(SELECT count(l.channel_id) FROM crm_code_channel AS l WHERE l.company_id = cc.company_id and l.channel_medianame = cc.frommedia_name and  exists (select t.client_id from crm_client as t where t.channel_id =l.channel_id )) AS channel_num
from crm_code_frommedia as cc where {$datawhere} order by cc.frommedia_sort ASC,cc.frommedia_id ASC";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->AnalyzeControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['frommedia_name'] = $val['frommedia_name'];
                    $datearray['channel_num'] = $val['channel_num'];

                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newaddnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $datearray['client_newaddnums'] = $clientCount['client_newaddnums'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newvalidnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' AND c.client_source = '{$val['frommedia_name']}' and c.client_isgross = '0' LIMIT 0,1");
                    $datearray['client_newvalidnums'] = $clientCount['client_newvalidnums'];
                    if ($datearray['client_newaddnums'] > 0) {
                        $datearray['client_validrate'] = round(($datearray['client_newvalidnums'] / $datearray['client_newaddnums']) * 100, 2) . '%';
                    } else {
                        $datearray['client_validrate'] = "0" . '%';
                    }
                    $unClientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_nums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus = '-1' AND c.client_source = '{$val['frommedia_name']}' ");
                    $datearray['client_unvalidnums'] = $unClientCount['client_nums'];
                    $inv_aud_num = $this->DataControl->selectOne(" select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and c.client_source = '{$val['frommedia_name']}')
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} and c.client_source = '{$val['frommedia_name']}' and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} and c.client_source = '{$val['frommedia_name']}' and iv.client_id =a.client_id ) ) as nums ");
                    $datearray['inv_aud_num'] = $inv_aud_num['nums'];
                    $inv_aud_arrivenum = $this->DataControl->selectOne("select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 and c.client_source = '{$val['frommedia_name']}')
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_isvisit =1  and c.client_source = '{$val['frommedia_name']}' and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} and c.client_source = '{$val['frommedia_name']}' and iv.client_id =a.client_id ) ) as nums ");
                    $datearray['inv_aud_arrivenum'] = $inv_aud_arrivenum['nums'];
                    $datearray['inv_aud_rate'] = $inv_aud_num['nums'] > 0 ? round($inv_aud_arrivenum['nums'] / $inv_aud_num['nums'], 4) * 100 . '%' : '0%';

                    $invite_num = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere}  and c.client_source = '{$val['frommedia_name']}' ");
                    $datearray['invite_num'] = $invite_num['nums'];
                    $invite_arrivenum = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 and c.client_source = '{$val['frommedia_name']}' ");
                    $datearray['invite_arrivenum'] = $invite_arrivenum['nums'];
                    if (isset($request['school_id']) && $request['school_id'] !== "" && $schoolOne['school_openclass'] == 0) {
                        $datearray['OH_audition_num'] = '--';
                        $datearray['OH_audition_arrivenum'] = '--';
                    } else {
                        $OH_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0  and c.client_source = '{$val['frommedia_name']}' ");
                        $datearray['OH_audition_num'] = $OH_audition_num['nums'];
                        $OH_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0 and a.audition_isvisit =1  and c.client_source = '{$val['frommedia_name']}' ");
                        $datearray['OH_audition_arrivenum'] = $OH_audition_arrivenum['nums'];
                    }
                    $Class_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1  and c.client_source = '{$val['frommedia_name']}' ");
                    $datearray['Class_audition_num'] = $Class_audition_num['nums'];
                    $Class_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1 and a.audition_isvisit =1  and c.client_source = '{$val['frommedia_name']}' ");
                    $datearray['Class_audition_arrivenum'] = $Class_audition_arrivenum['nums'];
                    $positivenum = $this->DataControl->selectOne("select count(e.client_id) as nums from crm_client_positivelog  as e,crm_client as c where e.client_id = c.client_id and c.client_source = '{$val['frommedia_name']}' and {$positivewhere}");
                    $datearray['positivenum'] = $positivenum['nums'];
                    $registernum = $this->DataControl->selectOne("select count(st.student_id) as nums from  smc_student_registerinfo as i,smc_student as st,crm_client as c where i.student_id=st.student_id and st.from_client_id=c.client_id and c.client_source = '{$val['frommedia_name']}' and {$registerwhere}");
                    $datearray['registernum'] = $registernum['nums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('渠道类型名称', '渠道明细数', '新增毛名单', '新增有效名单', '新增有效率', '新增无意向名单数', '邀约名单数', '到访名单数', '到访率', '柜询邀约名单数', '柜询邀约到访名单数', 'OH邀约名单数', 'OH邀约到访名单数', "插班试听邀约名单数", '插班试听邀约到访名单数', '报名名单数', "实缴名单"));
            $excelfileds = array('frommedia_name', 'channel_num', 'client_newaddnums', 'client_newvalidnums', 'client_validrate', 'client_unvalidnums', "inv_aud_num", 'inv_aud_arrivenum', 'inv_aud_rate', 'invite_num', 'invite_arrivenum', 'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'positivenum', 'registernum');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("渠道招生统计报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as &$val) {
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newaddnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $val['client_newaddnums'] = $clientCount['client_newaddnums'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newvalidnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' AND c.client_source = '{$val['frommedia_name']}' and c.client_isgross = '0' LIMIT 0,1");
                    $val['client_newvalidnums'] = $clientCount['client_newvalidnums'];
                    if ($val['client_newaddnums'] > 0) {
                        $val['client_validrate'] = round(($val['client_newvalidnums'] / $val['client_newaddnums']) * 100, 2) . '%';
                    } else {
                        $val['client_validrate'] = "0" . '%';
                    }
                    $unClientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_nums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus = '-1' AND c.client_source = '{$val['frommedia_name']}' ");
                    $val['client_unvalidnums'] = $unClientCount['client_nums'];
                    $inv_aud_num = $this->DataControl->selectOne("select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and c.client_source = '{$val['frommedia_name']}')
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} and c.client_source = '{$val['frommedia_name']}' and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} and c.client_source = '{$val['frommedia_name']}' and iv.client_id =a.client_id )  ) as nums ");
                    $val['inv_aud_num'] = $inv_aud_num['nums'];
                    $inv_aud_arrivenum = $this->DataControl->selectOne("select (select count(DISTINCT iv.client_id ) from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 and c.client_source = '{$val['frommedia_name']}')
  + (select count(DISTINCT a.client_id) from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_isvisit =1  and c.client_source = '{$val['frommedia_name']}' and c.client_id not in (select client_id from crm_client_invite as iv where {$invitenwhere} and c.client_source = '{$val['frommedia_name']}' and iv.client_id =a.client_id ) ) as nums ");
                    $val['inv_aud_arrivenum'] = $inv_aud_arrivenum['nums'];
                    $val['inv_aud_rate'] = $val['inv_aud_num'] > 0 ? round($val['inv_aud_arrivenum'] / $val['inv_aud_num'], 4) * 100 . '%' : '0%';
                    $invite_num = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere}  and c.client_source = '{$val['frommedia_name']}' ");
                    $val['invite_num'] = $invite_num['nums'];
                    $invite_arrivenum = $this->DataControl->selectOne("select count(DISTINCT iv.client_id ) as nums from crm_client_invite as iv,crm_client as c  where {$invitenwhere} and iv.invite_isvisit =1 and c.client_source = '{$val['frommedia_name']}' ");
                    $val['invite_arrivenum'] = $invite_arrivenum['nums'];

                    if (isset($request['school_id']) && $request['school_id'] !== "" && $schoolOne['school_openclass'] == 0) {
                        $val['OH_audition_num'] = '--';
                        $val['OH_audition_arrivenum'] = '--';
                    } else {
                        $OH_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0  and c.client_source = '{$val['frommedia_name']}' ");
                        $val['OH_audition_num'] = $OH_audition_num['nums'];
                        $OH_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =0 and a.audition_isvisit =1  and c.client_source = '{$val['frommedia_name']}' ");
                        $val['OH_audition_arrivenum'] = $OH_audition_arrivenum['nums'];

                    }
                    $Class_audition_num = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1  and c.client_source = '{$val['frommedia_name']}' ");
                    $val['Class_audition_num'] = $Class_audition_num['nums'];
                    $Class_audition_arrivenum = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as nums from crm_client_audition as a,crm_client as c where {$auditionwhere} and a.audition_genre =1 and a.audition_isvisit =1  and c.client_source = '{$val['frommedia_name']}' ");
                    $val['Class_audition_arrivenum'] = $Class_audition_arrivenum['nums'];
                    $positivenum = $this->DataControl->selectOne("select count(e.client_id) as nums from crm_client_positivelog  as e,crm_client as c where e.client_id = c.client_id and c.client_source = '{$val['frommedia_name']}' and {$positivewhere}");
                    $val['positivenum'] = $positivenum['nums'];
                    $registernum = $this->DataControl->selectOne("select count(st.student_id) as nums from  smc_student_registerinfo as i,smc_student as st,crm_client as c where i.student_id=st.student_id and st.from_client_id=c.client_id and c.client_source = '{$val['frommedia_name']}' and {$registerwhere}");
                    $val['registernum'] = $registernum['nums'];
                }
            }
            if (!$datalist) {
                $datalist = array();
            }
            $data['list'] = $datalist;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $mediaCount = $this->AnalyzeControl->selectOne("select COUNT(cc.frommedia_id) as countnums from crm_code_frommedia as cc
WHERE {$datawhere} limit 0,1");
            if ($mediaCount) {
                $data['allnums'] = $mediaCount['countnums'];
            } else {
                $data['allnums'] = 0;
            }
        }

        return $data;

    }

    //校园招生统计报表
    function schoolErollClient($request)
    {
        $schoolwhere = "s.company_id='{$request['company_id']}' and s.school_isclose <> '1' and s.school_istest <> '1'";
        $clientwhere = "c.company_id='{$request['company_id']}'";
        $trackwhere = "t.school_id = s.school_id and t.track_isactive = '1'";
        $auwhere = "au.school_id=s.school_id and au.company_id='{$request['company_id']}'";
        $invitelogwhere = "i.company_id='{$request['company_id']}' AND i.school_id = s.school_id";
        $conwhere = "p.school_id=s.school_id";
        $invitewhere = "iv.school_id = s.school_id and iv.company_id='{$request['company_id']}'";
        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $schoolwhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $statday = date("Y-m-d", $stattime);
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
            $trackwhere .= " and t.track_createtime >='{$stattime}'";
            $auwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $conwhere .= " and p.positivelog_time >='{$statday}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $invitelogwhere .= " AND i.visittime >= '{$statday}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $endday = date("Y-m-d", $endime);
            $clientwhere .= " and c.client_createtime <='{$endime}'";
            $trackwhere .= " and t.track_createtime <='{$endime}'";
            $auwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d')  <='{$request['end_time']}'";
            $conwhere .= " and p.positivelog_time <='{$endday}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $invitelogwhere .= " AND i.visittime <= '{$endday}'";
        }


        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $trackwhere .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $conwhere .= " AND p.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invitelogwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $trackwhere .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $conwhere .= " AND p.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitelogwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $trackwhere .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $conwhere .= " AND p.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitelogwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $schoolwhere .= " and s.school_id ='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_cnname like  '%{$request['keyword']}%') ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;
        $sql = "select s.school_id,s.school_cnname,s.school_branch,s.school_openclass,
                (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
                (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname,
                (select count(c.client_id) FROM crm_client as c LEFT JOIN crm_client_schoolenter as e ON c.client_id = e.client_id
                where {$clientwhere} AND e.school_id = s.school_id) as client_maonum,
                (select count(c.client_id) FROM crm_client as c LEFT JOIN crm_client_schoolenter as e ON c.client_id = e.client_id
                where {$clientwhere} AND e.school_id=s.school_id AND c.client_tracestatus <> '-2' and c.client_isgross = '0') as  client_validnum,
                (select count(DISTINCT t.client_id) from crm_client_track as t where {$trackwhere}) as client_tracknum,
                (select count(t.track_id) from crm_client_track as t where {$trackwhere}) as school_track_num,
                (select count(t.track_id) from crm_client_track as t where {$trackwhere} and t.track_linktype='电话沟通') as track_moblie_num,
	            (SELECT COUNT(DISTINCT i.client_id) FROM view_crm_invitelog AS i WHERE {$invitelogwhere}) AS inv_aud_num,
	            (SELECT COUNT(DISTINCT i.client_id) FROM view_crm_invitelog AS i WHERE {$invitelogwhere} AND i.isvisit = '1') AS inv_aud_arrivenum,
                (select count(DISTINCT iv.client_id) from crm_client_invite as iv where {$invitewhere}) as invite_num,
                (select count(DISTINCT iv.client_id) from crm_client_invite as iv where {$invitewhere} and iv.invite_isvisit = '1') as invite_arrivenum,
                (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_genre = '0') as OH_client_num,
                (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_isvisit = '1' and au.audition_genre = '0') as OH_client_arrive_num,
                (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_genre = '1') as Class_client_num,
                (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_isvisit = '1' and au.audition_genre = '1') as Class_client_arrivenum,
                (select count(p.client_id) from crm_client_positivelog as p where {$conwhere}) as positive_num,
                (select count(c.client_id) from crm_client as c  left join  crm_client_schoolenter as cs On  c.client_id= cs.client_id  where  c.client_fromtype = 0 and cs.school_id=s.school_id  and c.company_id='{$request['company_id']}' and c.client_tracestatus <> -2 and {$clientwhere}) as  client_outnum,
                (select count(c.client_id) from crm_client as c  left join  crm_client_schoolenter as cs On  c.client_id= cs.client_id  where  c.client_fromtype = 1 and cs.school_id=s.school_id  and c.company_id='{$request['company_id']}' and c.client_tracestatus <> -2  and {$clientwhere}) as  client_innernum,
                (select count(c.client_id) from crm_client as c  left join  crm_client_schoolenter as cs On  c.client_id= cs.client_id  where  c.client_fromtype = 2 and cs.school_id=s.school_id  and c.company_id='{$request['company_id']}' and c.client_tracestatus <> -2  and {$clientwhere}) as  client_casenum,
                (select count(c.client_id) from crm_client as c  left join  crm_client_schoolenter as cs On  c.client_id= cs.client_id  where  cs.school_id=s.school_id  and c.company_id='{$request['company_id']}' and client_tracestatus  not in (-1,-2) and c.client_id not in (select pl.client_id from crm_client_principal as pl where pl.client_id = cs.client_id  and pl.principal_leave= 0) and c.client_isgross = '0') as  client_noprincipal_num,
                (select count(c.client_id) from crm_client as c  left join  crm_client_schoolenter as cs On  c.client_id= cs.client_id  where c.client_distributionstatus =1 and cs.school_id=s.school_id  and c.company_id='{$request['company_id']}' and c.client_id not in (select t.client_id from crm_client_track as t where t.school_id = cs.school_id and t.track_isactive= 1) ) as  client_notrack_num,
                (select count(c.client_id) from crm_client as c  left join  crm_client_schoolenter as cs On  c.client_id= cs.client_id  where  cs.school_id=s.school_id  and c.company_id='{$request['company_id']}' and c.client_id not in (select t.client_id from crm_client_track as t where t.school_id = cs.school_id and t.track_isactive= 1) ) as  client_zerotrack_num,
                (select count(DISTINCT pl.marketer_id)  from crm_client_principal as  pl,crm_marketer as m,smc_staffer as sf where pl.school_id = s.school_id and m.marketer_id =pl.marketer_id  
                 and sf.staffer_id=m.staffer_id and  sf.account_class =0 and sf.staffer_leave =0 and m.marketer_tokencode <> '-1' ) as marketer_num
          FROM smc_school as s where {$schoolwhere} 
          order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc,s.school_branch ASC ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['client_maonum'] = $dateexcelvar['client_maonum'];
                    $datearray['client_validnum'] = $dateexcelvar['client_validnum'];
                    $datearray['client_tracknum'] = $dateexcelvar['client_tracknum'];
                    $datearray['school_track_num'] = $dateexcelvar['school_track_num'];
                    $datearray['track_moblie_num'] = $dateexcelvar['track_moblie_num'];
                    $datearray['inv_aud_num'] = $dateexcelvar['inv_aud_num'];
                    $datearray['inv_aud_arrivenum'] = $dateexcelvar['inv_aud_arrivenum'];
                    $datearray['invite_num'] = $dateexcelvar['invite_num'];
                    $datearray['invite_arrivenum'] = $dateexcelvar['invite_arrivenum'];
                    if ($dateexcelvar['school_openclass'] == 1) {
                        $datearray['OH_client_num'] = $dateexcelvar['OH_client_num'];
                        $datearray['OH_client_arrive_num'] = $dateexcelvar['OH_client_arrive_num'];
                    } else {
                        $datearray['OH_client_num'] = '--';
                        $datearray['OH_client_arrive_num'] = '--';
                    }

                    $datearray['Class_client_num'] = $dateexcelvar['Class_client_num'];
                    $datearray['Class_client_arrivenum'] = $dateexcelvar['Class_client_arrivenum'];
                    $datearray['positive_num'] = $dateexcelvar['positive_num'];
                    $datearray['client_outnum'] = $dateexcelvar['client_outnum'];
                    $datearray['client_innernum'] = $dateexcelvar['client_innernum'];
                    $datearray['client_casenum'] = $dateexcelvar['client_casenum'];
                    $datearray['client_noprincipal_num'] = $dateexcelvar['client_noprincipal_num'];
                    $datearray['client_notrack_num'] = $dateexcelvar['client_notrack_num'];
                    $datearray['client_zerotrack_num'] = $dateexcelvar['client_zerotrack_num'];
                    $datearray['marketer_num'] = $dateexcelvar['marketer_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '所属区域', '新增毛名单数', '新增有效名单数', '新增跟踪人数', '新增跟踪人次', '新增电话沟通人次', '新增邀约名单数', '新增到访名单数', '新增柜询邀约名单数', '新增柜询邀约到访名单数', '新增OH邀约名单数', '新增OH邀约到访名单数', '新增插班邀约名单数', '新增插班邀约到访名单数', '新增累计报名人数', '新增外部招生人数', '新增内部招生人数', '新增专案招生人数', '当前待分配名单数', '当前待跟踪名单数', '0跟踪记录名单数', '当前招生老师数'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'district_cnname', 'client_maonum', 'client_validnum', 'client_tracknum', 'school_track_num', 'track_moblie_num', 'inv_aud_num', 'inv_aud_arrivenum', 'invite_num', 'invite_arrivenum', 'OH_client_num', 'OH_client_arrive_num', 'Class_client_num', 'Class_client_arrivenum', 'positive_num', 'client_outnum', 'client_innernum', 'client_casenum', 'client_noprincipal_num', 'client_notrack_num', 'client_zerotrack_num', 'marketer_num');

            $fielname = $this->LgStringSwitch("校园招生统计报表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;

        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    if ($value['school_openclass'] == 0) {
                        $datalist[$key]['OH_client_num'] = "--";
                        $datalist[$key]['OH_client_arrive_num'] = "--";
                    }
                }
            }
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $all_num = $this->DataControl->selectOne("select count(s.school_id) as num
                FROM smc_school as s where {$schoolwhere}");
                if ($all_num) {
                    $data['allnums'] = $all_num['num'];
                } else {
                    $data['allnums'] = 0;
                }
            }

            if (!$datalist) {
                $datalist = array();
            }
            $data['list'] = $datalist;
            return $data;
        }

    }

    //招生业绩明细报表
    function enrollPerDetails($request)
    {
        $datawhere = "r.student_id = s.student_id AND r.school_id = l.school_id AND s.from_client_id = c.client_id AND l.school_istest = 0 and l.school_isclose = 0 AND r.company_id = '{$request['company_id']}'";
        if (isset($request['starttime']) && $request['starttime']) {
            $stattime = strtotime($request['starttime']);
            $datawhere .= " and r.pay_successtime >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and r.pay_successtime <='{$endime}'";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and r.school_id ='{$request['school_id']}'";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND r.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== "") {
            $datawhere .= " and r.school_id in (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname  like '%{$request['keyword']}%' or c.client_enname  like '%{$request['keyword']}%' or c.client_mobile  like '%{$request['keyword']}%'  ) ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND r.coursetype_id = '{$request['coursetype_id']}' AND r.coursecat_id = '{$request['coursecat_id']}'";
            } else {
                $datawhere .= " AND r.coursetype_id = '{$request['coursetype_id']}'";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND r.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "r.info_id,l.school_branch, l.school_cnname, s.student_branch, s.student_cnname, r.coursetype_cnname, r.coursecat_cnname,r.info_type, c.client_mobile, c.client_frompage
        , ( SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = l.school_province ) as province_name
        , ( SELECT p.promotion_jobnumber FROM crm_ground_promotion AS p WHERE p.promotion_id=c.promotion_id) as promotion_jobnumber
        , ( SELECT p.promotion_type FROM crm_ground_promotion AS p WHERE p.promotion_id=c.promotion_id) as promotion_type
        , ( SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = l.district_id ) AS district_cnname
        , ( SELECT concat( k.marketer_name, ( CASE WHEN ifnull(sf.staffer_enname, '') = '' THEN '' ELSE concat('-', sf.staffer_enname) END ) ) 
			FROM crm_marketer AS k , smc_staffer AS sf  
			WHERE sf.staffer_id = k.staffer_id  AND k.marketer_id = r.xz_marketer_id  ) as xz_marketer_name
			,
			( SELECT concat( k.marketer_name, ( CASE WHEN ifnull(sf.staffer_enname, '') = '' THEN '' ELSE concat('-', sf.staffer_enname) END ) ) 
			FROM crm_marketer AS k , smc_staffer AS sf 
			WHERE sf.staffer_id = k.staffer_id  AND k.marketer_id = r.kz_marketer_id ) as kz_marketer_name
			,
			( SELECT  post_name
				FROM crm_marketer AS k,smc_staffer AS sf ,gmc_staffer_postbe as b,gmc_company_post as pt
				WHERE k.marketer_id = r.xz_marketer_id and sf.staffer_id = k.staffer_id  
				and b.staffer_id = sf.staffer_id and b.school_id = l.school_id and b.postbe_status = '1' and pt.post_id = b.post_id  LIMIT 0,1 
			) as xz_school_post_name
			,
			( SELECT  post_name
				FROM crm_marketer AS k,smc_staffer AS sf ,gmc_staffer_postbe as b,gmc_company_post as pt
				WHERE k.marketer_id = r.kz_marketer_id and sf.staffer_id = k.staffer_id  
				and b.staffer_id = sf.staffer_id and b.school_id = l.school_id and b.postbe_status = '1' and pt.post_id = b.post_id  LIMIT 0,1 
			) as kz_school_post_name  
        , r.pay_price, from_unixtime( r.pay_successtime, '%Y-%m-%d' ) AS pay_successtime, c.client_source AS channel_sourcename
        , ( SELECT CONCAT( e.channel_name, '-', e.channel_way, '-', e.channel_isbazaar,'-', e.channel_board ) FROM crm_code_channel AS e WHERE c.channel_id = e.channel_id ) AS channel_fors
        , from_unixtime( c.client_createtime, '%Y-%m-%d' ) AS client_createtime";

//        ( SELECT concat( k.marketer_name, ( CASE WHEN ifnull(sf.staffer_enname, '') = '' THEN '' ELSE concat('-', sf.staffer_enname) END ) ) FROM crm_client_principal AS p
//        , crm_marketer AS k, smc_staffer AS sf WHERE sf.staffer_id = k.staffer_id AND k.marketer_id = p.marketer_id AND p.school_id = l.school_id AND p.client_id = c.client_id
//    AND p.principal_ismajor = 1 AND p.principal_leave = 0 ORDER BY p.principal_createtime DESC LIMIT 0, 1 ) AS zhu_marketer_name,

//        ( SELECT  post_name
//			FROM crm_client_principal AS p,crm_marketer AS k, smc_staffer AS sf ,gmc_staffer_postbe as b,gmc_company_post as pt
//			WHERE sf.staffer_id = k.staffer_id AND k.marketer_id = p.marketer_id AND p.school_id = l.school_id AND p.client_id = c.client_id
//    AND p.principal_ismajor = 1 AND p.principal_leave = 0
//    and b.staffer_id = sf.staffer_id and b.school_id = p.school_id and b.postbe_status = '1' and pt.post_id = b.post_id
//			ORDER BY p.principal_createtime DESC LIMIT 0, 1
//		) AS school_post_name

        $sql = "SELECT {$fields} FROM smc_student_registerinfo AS r, smc_student AS s, smc_school AS l, crm_client AS c
        WHERE {$datawhere} and r.info_status=1 ORDER BY l.district_id,l.school_branch,r.pay_successtime";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            ini_set("memory_limit", '-1');
            set_time_limit(600);
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($dateexcelarray as &$val) {
                $channelfors = explode("-", $val['channel_fors']);
                $val['channel_name'] = $channelfors[0];
                $val['channel_way'] = $channelfors[1];
                $val['channel_isbazaar'] = $channelfors[2];
                $val['section_name'] = $channelfors[3];

                $val['client_mobile'] = hideNumberString($val['client_mobile']);

                if (!$val['promotion_jobnumber']) {
                    $val['promotion_jobnumber'] = '--';
                }
                if (is_null($val['promotion_type'])) {
                    $val['post_name'] = '--';
                } else {
                    if ($val['promotion_type'] == 0) {
                        $val['post_name'] = '市场';
                    } else {
                        $val['post_name'] = '销售';
                    }
                }
                if($val['info_type'] == '1'){
                    $val['zhu_marketer_name'] = $val['kz_marketer_name'];
                }elseif($val['info_type'] == '0'){
                    $val['zhu_marketer_name'] = $val['xz_marketer_name'];
                }
                if($val['info_type'] == '1'){
                    $val['school_post_name'] = $val['kz_school_post_name'];
                }else{
                    $val['school_post_name'] = $val['xz_school_post_name'];
                }
                if (!$val['client_frompage']) {
                    $val['client_frompage'] = '--';
                }
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    $datearray['zhu_marketer_name'] = $dateexcelvar['zhu_marketer_name'];
                    $datearray['school_post_name'] = $dateexcelvar['school_post_name'];
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    $datearray['pay_successtime'] = $dateexcelvar['pay_successtime'];
                    $datearray['section_name'] = $dateexcelvar['section_name'];
                    $datearray['channel_sourcename'] = $dateexcelvar['channel_sourcename'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '所属区域', '学员编号', '学员姓名', '班组名称', '班种名称', '联系手机', '主要负责人', '主要负责人职务', '首缴金额', '报名日期', '渠道版块', '渠道类型', '渠道明细', '地推工号', '职务', '接触点', '名单创建时间'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'district_cnname', 'student_branch', 'student_cnname', 'coursetype_cnname', 'coursecat_cnname', 'client_mobile', 'zhu_marketer_name', 'school_post_name', 'pay_price', 'pay_successtime', 'section_name', 'channel_sourcename', 'channel_name', 'promotion_jobnumber', 'post_name', 'client_frompage', 'client_createtime');
            $fielname = $this->LgStringSwitch("招生业绩明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    $channelfors = explode("-", $value['channel_fors']);
                    $datalist[$key]['channel_name'] = $channelfors[0];
                    $datalist[$key]['channel_way'] = $channelfors[1];
                    $datalist[$key]['channel_isbazaar'] = $channelfors[2];
                    $datalist[$key]['section_name'] = $channelfors[3];

                    if ($channelfors[1] == '0') {
                        $datalist[$key]['section_name'] = '空军';
                    } elseif ($channelfors[1] == '1' && $channelfors[2] == '0') {
                        $datalist[$key]['section_name'] = '校区自营';
                    } elseif ($channelfors[1] == '1' && $channelfors[2] == '1') {
                        $datalist[$key]['section_name'] = '陆军';
                    }
                    $datalist[$key]['client_mobile'] = hideNumberString($value['client_mobile']);

                    if (!$value['promotion_jobnumber']) {
                        $datalist[$key]['promotion_jobnumber'] = '--';
                    }
                    if (is_null($value['promotion_type'])) {
                        $datalist[$key]['post_name'] = '--';
                    } else {
                        if ($value['promotion_type'] == 0) {
                            $datalist[$key]['post_name'] = '市场';
                        } else {
                            $datalist[$key]['post_name'] = '销售';
                        }
                    }
                    if($value['info_type'] == '1'){
                        $datalist[$key]['zhu_marketer_name'] = $value['kz_marketer_name']?$value['kz_marketer_name']:'--';
                    }elseif($value['info_type'] == '0'){
                        $datalist[$key]['zhu_marketer_name'] = $value['xz_marketer_name']?$value['xz_marketer_name']:'--';
                    }
                    if($value['info_type'] == '1'){
                        $datalist[$key]['school_post_name'] = $value['kz_school_post_name']?$value['kz_school_post_name']:'--';
                    }else{
                        $datalist[$key]['school_post_name'] = $value['xz_school_post_name']?$value['xz_school_post_name']:'--';
                    }

                    if (!$value['client_frompage']) {
                        $datalist[$key]['client_frompage'] = '--';
                    }
                }
            } else {
                $datalist = array();
            }
            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT COUNT(r.info_id) AS cnums
FROM smc_student_registerinfo AS r, smc_student AS s, smc_school AS l, crm_client AS c WHERE {$datawhere} and r.info_status=1 ");
            if ($allNum) {
                $data['allnums'] = $allNum['cnums'];
            } else {
                $data['allnums'] = 0;
            }
        }
        return $data;
    }

    //招生业绩统计报表
    function marketerClientReport($request)
    {
        $trackwhere = "1";
////        $conwhere = "1";
        $registerwhere = "1";
        $auwhere = "1";
        $invwhere = "1";
        $principalwhere = "1";
        $schoolwhere = '1';
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $trackwhere .= " and ct.track_createtime >='{$stattime}'";
//            $conwhere .= " and cg.conversionlog_time >='{$stattime}'";
            $registerwhere .= " and r.pay_successtime >='{$stattime}'";
            $auwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $invwhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $principalwhere .= " and cp.principal_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $trackwhere .= " and ct.track_createtime <='{$endime}'";
//            $conwhere .= " and cg.conversionlog_time <='{$endime}'";
            $registerwhere .= " and r.pay_successtime <='{$endime}'";
            $auwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $invwhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $principalwhere .= " and cp.principal_createtime <='{$endime}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
//                $conwhere .= " AND cg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $registerwhere .= " AND r.coursecat_id = '{$request['coursecat_id']}' ";
                $invwhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $principalwhere .= " AND cp.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $conwhere .= " AND cg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $registerwhere .= " AND r.coursetype_id = '{$request['coursetype_id']}' ";
                $invwhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $principalwhere .= " AND cp.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $conwhere .= " AND cg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $registerwhere .= " AND r.coursecat_id = '{$request['coursecat_id']}' ";
            $invwhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $principalwhere .= " AND cp.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $trackwhere .= " and ct.school_id ='{$request['school_id']}'";
            $schoolwhere .= " and sc.school_id='{$request['school_id']}'";

        }
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (m.marketer_name like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%') ";
        }
        $postbeOne = $this->AnalyzeControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $schoolwhere .= " AND sc.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " select cp.marketer_id,m.marketer_name,
                        (select r.region_name from smc_code_region as r where r.region_id = sc.school_province ) as province_name,
                        (select y.post_name from gmc_staffer_postbe x,gmc_company_post y 
                        where x.post_id=y.post_id and x.staffer_id=s.staffer_id and x.school_id=sc.school_id and y.post_type = 1 and x.postbe_status=1 
                        order by x.postbe_ismianjob desc limit 0,1) as post_name
                        ,s.staffer_enname,s.staffer_branch,sc.school_cnname,sc.school_branch,sc.school_id,sc.school_openclass
                      from crm_client_principal as cp
                      left join crm_marketer as m ON m.marketer_id = cp.marketer_id
                      left join  smc_staffer  as s On s.staffer_id= m.staffer_id
                      left join  smc_school as sc ON cp.school_id = sc.school_id 
                      LEFT JOIN crm_client as cc ON cp.client_id = cc.client_id
                      where {$schoolwhere} and s.company_id='{$request['company_id']}'   and  sc.school_branch <>'' and s.account_class =0
                       and  s.staffer_leave = 0  
                        and cp.principal_leave <> '1' AND cp.school_id = sc.school_id and cp.principal_id > 0 AND cc.client_tracestatus <> '4' 
                       group by  cp.marketer_id,cp.school_id
                       order by (case when sc.school_istest=0 and sc.school_isclose=0 then 1 when sc.school_isclose=0 then 2 when sc.school_istest=0 then 3 else 4 end),sc.school_istest asc,field(sc.school_sort,0),sc.school_sort asc,sc.school_createtime asc
             ";//{$principalwhere} and
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $sql .= "";
        } else {
            $sql .= " limit {$pagestart},{$num}";
        }
        $marketList = $this->AnalyzeControl->selectClear($sql);

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $all_num = $this->AnalyzeControl->selectClear("select cp.marketer_id
                      from crm_client_principal as cp
                      left join crm_marketer as m ON m.marketer_id = cp.marketer_id
                      left join  smc_staffer  as s On s.staffer_id= m.staffer_id
                      left join  smc_school as sc ON cp.school_id = sc.school_id
                      LEFT JOIN crm_client as cc ON cp.client_id = cc.client_id
                      where {$schoolwhere} and s.company_id='{$request['company_id']}'   and  sc.school_branch <>'' and s.account_class =0
                       and  s.staffer_leave = 0
                        and cp.principal_leave <> '1' AND cp.school_id = sc.school_id and cp.principal_id > 0 AND cc.client_tracestatus <> '4' 
                       group by  cp.marketer_id,cp.school_id
                 ");//{$principalwhere} and

            if ($all_num) {
                $data['allnums'] = is_array($all_num) ? count($all_num) : 0;
            } else {
                $data['allnums'] = 0;
            }
        }

        if (!$marketList) {
            return array();
        } else {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $sql = " select
                    ( select count(cp.client_id) from crm_client_principal as cp,crm_client as cl where cl.client_id=cp.client_id and  cl.client_tracestatus <>4 and cp.school_id='{$value['school_id']}' and cp.marketer_id =m.marketer_id and cp.principal_leave =0 and {$principalwhere}   ) as princ_clientnum,
                    (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and  ct.school_id='{$value['school_id']}' and ct.track_isactive =1 and  ct.marketer_id =m.marketer_id ) as track_client_num,
                    (select count(ct.track_id) from crm_client_track as ct where {$trackwhere} and  ct.school_id='{$value['school_id']}' and ct.track_isactive =1 and  ct.marketer_id =m.marketer_id ) as client_track_num,
                    ( (select count(DISTINCT iv.client_id) from crm_client_invite as iv where {$invwhere} and iv.school_id='{$value['school_id']}' and iv.marketer_id=m.marketer_id ) + (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.school_id='{$value['school_id']}' and au.marketer_id=m.marketer_id  and au.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id=au.client_id and {$invwhere} and iv.marketer_id=au.marketer_id and iv.invite_isvisit =1 ))) as inv_aud_num,
                    ( (select count(DISTINCT iv.client_id) from crm_client_invite as iv where {$invwhere} and iv.school_id='{$value['school_id']}' and iv.invite_isvisit =1 and iv.marketer_id=m.marketer_id ) + (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_isvisit =1 and au.school_id='{$value['school_id']}' and au.marketer_id=m.marketer_id and au.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id=au.client_id and {$invwhere} and iv.marketer_id=au.marketer_id and iv.invite_isvisit =1 ) )) as inv_aud_arrivenum,
                    (select count(DISTINCT iv.client_id) from crm_client_invite as iv where {$invwhere} and iv.school_id='{$value['school_id']}'  and iv.marketer_id=m.marketer_id ) as invite_num,  
                    (select count(DISTINCT iv.client_id) from crm_client_invite as iv where {$invwhere} and iv.school_id='{$value['school_id']}' and iv.invite_isvisit =1 and iv.marketer_id=m.marketer_id ) as invite_arrivenum,  
                    (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and  au.audition_genre =0  and au.school_id='{$value['school_id']}' and au.marketer_id=m.marketer_id) as OH_audtion_num,  
                    (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_isvisit =1 and au.audition_genre =0  and au.school_id='{$value['school_id']}' and au.marketer_id=m.marketer_id) as OH_audtion_arrivenum,  
                    (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and  au.audition_genre =1  and au.school_id='{$value['school_id']}' and au.marketer_id=m.marketer_id) as Class_audtion_num,  
                    (select count(DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_isvisit =1 and au.audition_genre =1  and au.school_id='{$value['school_id']}' and au.marketer_id=m.marketer_id) as Class_audtion_arrivenum, 
                     (SELECT COUNT(r.info_id) from smc_student_registerinfo as r,	smc_student as t,	crm_client_conversionlog as c  where {$registerwhere} and r.school_id = '{$value['school_id']}' and r.info_status =1 	and t.student_id = r.student_id and c.student_branch = t.student_branch and c.school_id = r.school_id and c.marketer_id = '{$value['marketer_id']}') as register_num,
                     (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where {$registerwhere} and r.school_id = '{$value['school_id']}' and r.info_status =1) as register_allnum 
                        from crm_marketer as m where  m.marketer_id ='{$value['marketer_id']}'";
//                exit($sql);
                //整个学校的报名人数 (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where {$registerwhere} and r.school_id = '{$value['school_id']}' and r.info_status =1) as register_num
//这个是之前转正的
//                (select count(cg.client_id) from crm_client_conversionlog as cg left join crm_client as cl ON cg.client_id = cl.client_id where  cl.company_id ='{$request['company_id']}' and cg.marketer_id=m.marketer_id and {$conwhere}  and  cg.school_id ='{$value['school_id']}' and cg.conversionlog_ismajor=1 ) as conversion_num

                $markCout = $this->AnalyzeControl->selectOne($sql);
                $temp['province_name'] = $value['province_name'];
                $temp['school_cnname'] = $value['school_cnname'];
                $temp['school_openclass'] = $value['school_openclass'];
                $temp['school_branch'] = $value['school_branch'];
                $temp['marketer_name'] = $value['marketer_name'];
                $temp['staffer_enname'] = $value['staffer_enname'];
                $temp['post_name'] = $value['post_name'];
                $temp['staffer_branch'] = $value['staffer_branch'];
                $temp['princ_clientnum'] = $markCout['princ_clientnum'];
                $temp['track_client_num'] = $markCout['track_client_num'];
                $temp['client_track_num'] = $markCout['client_track_num'];
                $temp['inv_aud_num'] = $markCout['inv_aud_num'];
                $temp['inv_aud_arrivenum'] = $markCout['inv_aud_arrivenum'];
                $temp['invite_num'] = $markCout['invite_num'];
                $temp['invite_arrivenum'] = $markCout['invite_arrivenum'];
                if ($value['school_openclass'] == 1) {
                    $temp['OH_audtion_num'] = $markCout['OH_audtion_num'];
                    $temp['OH_audtion_arrivenum'] = $markCout['OH_audtion_arrivenum'];
                } else {
                    $temp['OH_audtion_num'] = '--';
                    $temp['OH_audtion_arrivenum'] = '--';
                }

                $temp['Class_audtion_num'] = $markCout['Class_audtion_num'];
                $temp['Class_audtion_arrivenum'] = $markCout['Class_audtion_arrivenum'];
//                $temp['conversion_num'] = $markCout['conversion_num'];
                $temp['register_num'] = $markCout['register_num'];
                $temp['register_allnum'] = $markCout['register_allnum'];
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['princ_clientnum'] = $dateexcelvar['princ_clientnum'];
                    $datearray['track_client_num'] = $dateexcelvar['track_client_num'];
                    $datearray['client_track_num'] = $dateexcelvar['client_track_num'];
                    $datearray['inv_aud_num'] = $dateexcelvar['inv_aud_num'];
                    $datearray['inv_aud_arrivenum'] = $dateexcelvar['inv_aud_arrivenum'];
                    $datearray['invite_num'] = $dateexcelvar['invite_num'];
                    $datearray['invite_arrivenum'] = $dateexcelvar['invite_arrivenum'];
                    if ($dateexcelvar['school_openclass'] == 1) {
                        $datearray['OH_audtion_num'] = $dateexcelvar['OH_audtion_num'];
                        $datearray['OH_audtion_arrivenum'] = $dateexcelvar['OH_audtion_arrivenum'];
                    } else {
                        $datearray['OH_audtion_num'] = '--';
                        $datearray['OH_audtion_arrivenum'] = '--';
                    }
                    $datearray['Class_audtion_num'] = $dateexcelvar['Class_audtion_num'];
                    $datearray['Class_audtion_arrivenum'] = $dateexcelvar['Class_audtion_arrivenum'];
//                    $datearray['conversion_num'] = $dateexcelvar['conversion_num'];
                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '教师中文名', '教师英文名', '职务名称', '教师编号', '新增分配名单', '有效跟踪人数', ' 有效跟踪人次', '邀约名单数', '到访名单数', '柜询邀约名单数', '柜询邀约到访名单数', 'OH邀约名单数', "OH邀约到访名单数", "插班试听邀约数", "插班试听邀约到访数", "报名人数"));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'marketer_name', 'staffer_enname', 'post_name', 'staffer_branch', 'princ_clientnum', 'track_client_num', 'client_track_num', 'inv_aud_num', 'inv_aud_arrivenum', 'invite_num', 'invite_arrivenum', 'OH_audtion_num', 'OH_audtion_arrivenum', 'Class_audtion_num', 'Class_audtion_arrivenum', 'register_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生业绩统计报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        }

        $data['list'] = $mark_data;
        return $data;

    }

    //招生业绩咨询报表
    function companyClientRerport($request)
    {
        $datawhere = "c.company_id = '{$request['company_id']}' AND c.client_id = e.client_id AND e.school_id = s.school_id AND e.is_enterstatus = '1'";
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }
        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}'";
        }

        if ($request['dataequity'] != '1') {
            $postbeOne = $this->AnalyzeControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
            if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
                $datawhere .= " AND e.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            }
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and e.school_id ='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname  like '%{$request['keyword']}%' or c.client_enname  like '%{$request['keyword']}%'  ) ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND (SELECT COUNT(cit.intention_id) FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}'  AND cit.coursecat_id = '{$request['coursecat_id']}' AND cit.client_id = c.client_id) <> 0";
            } else {
                $datawhere .= " AND (SELECT COUNT(1) FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.client_id = c.client_id) <> 0";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND (SELECT COUNT(1) FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}' AND cit.client_id = c.client_id) <> 0";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "c.client_id, s.school_id, s.school_cnname, s.school_branch, s.school_sort
,c.client_cnname,c.client_enname,c.client_createtime,c.client_tracestatus,c.client_age,c.client_sex,c.client_address,c.client_source,c.client_intention_level
,(SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province ) as province_name
,(SELECT COUNT(ct.track_id) FROM crm_client_track AS ct WHERE ct.client_id = c.client_id AND ct.track_isactive = '1' AND ct.school_id = s.school_id) AS track_num
,(SELECT CONCAT(t.marketer_name,'|#|',t.track_note,'|#|',t.track_createtime) FROM crm_client_track AS t WHERE t.school_id = e.school_id AND t.client_id = c.client_id
AND t.track_isactive = 1 ORDER BY t.track_id DESC LIMIT 0,1) AS trackInfo";

        $sql = "SELECT {$fields} FROM crm_client AS c, crm_client_schoolenter AS e, smc_school AS s
                WHERE {$datawhere} ORDER BY c.client_createtime DESC";

        //跟踪状态：0待跟踪1持续跟踪2已柜询3已试听4已转正-1已无意向
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->AnalyzeControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if (count($dateexcelarray) > 3000) {
                $this->error = true;
                $this->errortip = "导出数据量大于3千条,请筛选条件后进行导出";
                return false;
            }
            foreach ($dateexcelarray as &$val) {
                $val['client_createtime'] = date("Y-m-d", $val['client_createtime']);

                $trackArray = explode("|#|", $val['trackInfo']);
                if (is_array($trackArray) && $val['trackInfo']) {
                    $val['marketer_name'] = $trackArray['0'];
                    $val['track_note'] = $trackArray['1'];
                    $val['track_createtime'] = date("Y-m-d", $trackArray['2']);
                } else {
                    $val['marketer_name'] = '--';
                    $val['track_note'] = '--';
                    $val['track_createtime'] = '--';
                }

                $val['client_tracestatus'] = $clientTracestatus[$val['client_tracestatus']];
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '中文名', '英文名', '性别', '创建日期', '跟踪状态', '招生渠道类型', '意向星级', '最后跟踪教师', '最后跟踪内容', '最后跟踪时间', '跟踪人次'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'client_cnname', 'client_enname', 'client_sex', 'client_createtime', 'client_tracestatus', 'client_source', 'client_intention_level', 'marketer_name', 'track_note', 'track_createtime', 'track_num');

            $fielname = $this->LgStringSwitch("招生业绩咨询报表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->AnalyzeControl->selectClear($sql);

            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    $datalist[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    $trackArray = explode("|#|", $value['trackInfo']);
                    if (is_array($trackArray) && $value['trackInfo']) {
                        $datalist[$key]['marketer_name'] = $trackArray['0'];
                        $datalist[$key]['track_note'] = $trackArray['1'];
                        $datalist[$key]['track_createtime'] = date("Y-m-d", $trackArray['2'])?date("Y-m-d", $trackArray['2']):'--';
                    } else {
                        $datalist[$key]['marketer_name'] = '--';
                        $datalist[$key]['track_note'] = '--';
                        $datalist[$key]['track_createtime'] = '--';
                    }
                    $datalist[$key]['client_tracestatus'] = $clientTracestatus[$value['client_tracestatus']];
                }
            } else {
                $datalist = array();
            }
            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $trackNums = $this->AnalyzeControl->selectOne("SELECT COUNT(c.client_id) AS allnums
FROM crm_client AS c, crm_client_schoolenter AS e, smc_school AS s WHERE {$datawhere}");
            if ($trackNums) {
                $data['allnums'] = $trackNums['allnums'];
            } else {
                $data['allnums'] = 0;
            }
        }
        return $data;
    }

    //  集团活动统计报表
    function companyActivity($request)
    {
        $schoolwhere = "sa.company_id='{$request['company_id']}' ";
        $clientwhere = "1";
        $auwhere = '1';
        $invitewhere = '1';
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
            $auwhere .= " and DATE_FORMAT(ct.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $schoolwhere .= " and sa.activity_createtime >='{$stattime}' ";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);

            $clientwhere .= " and c.client_createtime <='{$endime}'";
            $auwhere .= " and DATE_FORMAT(ct.audition_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $schoolwhere .= " and sa.activity_createtime <='{$endime}' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $auwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['activity_id']) && $request['activity_id'] !== "") {
            $schoolwhere .= " and (sa.activity_id = '{$request['activity_id']}') ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (sa.activity_name like '%{$request['keyword']}%') ";
        }
        if (isset($request['channel_id']) && $request['channel_id'] !== "") {
            $schoolwhere .= " and (sa.channel_id = '{$request['channel_id']}') ";
        }
        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== "") {
            $schoolwhere .= " and (sa.frommedia_name = '{$request['frommedia_name']}') ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select sa.activity_id,sa.activity_name,sa.frommedia_name,cc.channel_name,
         (select count(c.client_id)  from  crm_client as  c where c.company_id ='{$request['company_id']}'  and sa.activity_id = c.activity_id and {$clientwhere} ) as  client_maoallnum,
         (select count(c.client_id)  from  crm_client as  c where c.company_id ='{$request['company_id']}'  and sa.activity_id = c.activity_id   ) as  client_leijimaoallnum,
         (select count(c.client_id)  from  crm_client as  c where c.company_id ='{$request['company_id']}'  and sa.activity_id = c.activity_id and c.client_tracestatus <>'-2' and c.client_isgross = '0') as  client_allnum,
         (select count(sl.actschool_id) from crm_sell_activity_school as sl where sl.activity_id = sa.activity_id) as applysch_num,
         ((select count(DISTINCT c.client_id) from crm_client_invite as  iv,crm_client as c where iv.client_id = c.client_id and c.activity_id =sa.activity_id and {$invitewhere}) + (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c where ct.client_id=c.client_id and c.activity_id=sa.activity_id  and {$auwhere} and ct.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id = ct.client_id and {$invitewhere} )) ) as inv_aud_num,
         ((select count(DISTINCT c.client_id) from crm_client_invite as  iv,crm_client as c where iv.client_id = c.client_id and c.activity_id =sa.activity_id and iv.invite_isvisit =1 and {$invitewhere}) + (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c where ct.client_id=c.client_id and c.activity_id=sa.activity_id and ct.audition_isvisit =1 and {$auwhere} and ct.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id = ct.client_id and {$invitewhere} and iv.invite_isvisit =1 )) ) as inv_aud_arrivenum,
         (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c where ct.client_id=c.client_id and c.activity_id=sa.activity_id and ct.audition_genre =0 and {$auwhere}) as OH_client_num,
         (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c where ct.client_id=c.client_id and c.activity_id=sa.activity_id and ct.audition_genre =0 and ct.audition_isvisit =1 and {$auwhere}) as OH_client_arrivenum,
         ((select count(DISTINCT c.client_id) from crm_client_invite as  iv,crm_client as c,crm_client_positivelog as pg where pg.client_id=c.client_id and pg.school_id=iv.school_id  and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') =DATE_FORMAT(pg.positivelog_time,'%Y-%m-%d') and  iv.client_id = c.client_id and c.activity_id =sa.activity_id  and {$invitewhere}) + (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c,crm_client_positivelog as pg where pg.client_id =c.client_id and pg.school_id = ct.school_id and DATE_FORMAT(ct.audition_visittime,'%Y-%m-%d') =DATE_FORMAT(pg.positivelog_time,'%Y-%m-%d') and  ct.client_id=c.client_id and c.activity_id=sa.activity_id and {$auwhere} and ct.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id = ct.client_id and {$invitewhere}  ) ) ) as inv_aud_positivenum,
         (select count(distinct ct.client_id) from crm_client_conversionlog as cl,crm_client as ct
         where ct.client_id = cl.client_id AND ct.activity_id = sa.activity_id) as conversion_num
         FROM  crm_sell_activity as sa
         LEFT JOIN crm_code_channel as cc ON sa.channel_id = cc.channel_id
         where sa.activity_type = '1'  and {$schoolwhere}";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['applysch_num'] = $dateexcelvar['applysch_num'];
                    $datearray['client_maoallnum'] = $dateexcelvar['client_maoallnum'];
                    $datearray['inv_aud_num'] = $dateexcelvar['inv_aud_num'];
                    $datearray['inv_aud_arrivenum'] = $dateexcelvar['inv_aud_arrivenum'];
                    $datearray['OH_client_num'] = $dateexcelvar['OH_client_num'];
                    $datearray['OH_client_arrivenum'] = $dateexcelvar['OH_client_arrivenum'];
                    $datearray['inv_aud_positivenum'] = $dateexcelvar['inv_aud_positivenum'];
                    $datearray['client_leijimaoallnum'] = $dateexcelvar['client_leijimaoallnum'];
                    $datearray['client_allnum'] = $dateexcelvar['client_allnum'];
                    $datearray['conversion_num'] = $dateexcelvar['conversion_num'];
                    $datearray['conversion_rate'] = $dateexcelvar['client_leijimaoallnum'] > 0 ? round($dateexcelvar['conversion_num'] / $dateexcelvar['client_leijimaoallnum'], 4) * 100 . '%' : '0%';
                    $outexceldate[] = $datearray;
                }
            }
//
            $excelheader = $this->LgArraySwitch(array('活动名称', "招生渠道类型", '招生渠道明细', '适配学校数', '新增毛名单', '新增邀约数', '新增邀约到访数', '新增OH邀约名单数', '新增OH邀约到访名单数', '新增邀约报名数', '累计毛名单', '累计有效名单数', '累计报名数', '累计转化率'));
            $excelfileds = array('activity_name', 'frommedia_name', 'channel_name', 'applysch_num', 'client_maoallnum', 'inv_aud_num', 'inv_aud_arrivenum', 'OH_client_num', 'OH_client_arrivenum', 'inv_aud_positivenum', 'client_leijimaoallnum', 'client_allnum', 'conversion_num', 'conversion_rate');

            $fielname = $this->LgStringSwitch("集团活动统计报表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as &$val) {
                    $val['conversion_rate'] = $val['client_leijimaoallnum'] > 0 ? round($val['conversion_num'] / $val['client_leijimaoallnum'], 4) * 100 . '%' : '0%';
                }
            }

            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("
 				        select count(sa.activity_id) as actnums
                    from  crm_sell_activity as sa
                    where sa.activity_type ='1'  and {$schoolwhere}");

            if ($allNum) {
                $data['allnums'] = $allNum['actnums'];

            } else {
                $data['allnums'] = 0;
            }
        }
        return $data;

    }

    //活动业绩统计报表`
    function ActivityEnroll($request)
    {
        $datewhere = "a.company_id = '{$request['company_id']}' AND c.activity_id = a.activity_id AND c.client_id = e.client_id AND e.school_id = s.school_id AND a.activity_type = '0' and s.school_isclose <> '1' and s.school_istest <> '1'";
        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datewhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        $clientwhere = "sr.client_id = ac.client_id and ac.activity_id = a.activity_id and sr.school_id = s.school_id";
        $auwhere = "sr.client_id = ac.client_id and ac.activity_id = a.activity_id and sr.school_id = s.school_id";
        $ivwhere = "sr.client_id = ac.client_id and ac.activity_id = a.activity_id and sr.school_id = s.school_id";
        $positwhere = "sr.client_id = ac.client_id AND ac.activity_id = a.activity_id AND sr.school_id = s.school_id";
        $posittivwhere = "sr.client_id = ac.client_id AND ac.activity_id = a.activity_id AND sr.school_id = s.school_id";
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $datewhere .= " and c.client_createtime >='{$stattime}'";
            $clientwhere .= " and ac.client_createtime >='{$stattime}'";
            $auwhere .= " and DATE_FORMAT(sr.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $ivwhere .= " and DATE_FORMAT(sr.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $positwhere .= " and sr.conversionlog_time >='{$stattime}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datewhere .= " and c.client_createtime <='{$endime}'";
            $clientwhere .= " and ac.client_createtime <='{$endime}'";
            $auwhere .= " and DATE_FORMAT(sr.audition_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $ivwhere .= " and DATE_FORMAT(sr.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $positwhere .= " and sr.conversionlog_time <='{$endime}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $clientwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $auwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $ivwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $positwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $posittivwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $clientwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $ivwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $positwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $posittivwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $clientwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $ivwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $positwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $posittivwhere .= " AND ac.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datewhere .= " and (a.activity_name like '%{$request['keyword']}%') ";
        }
        if (isset($request['activity_id']) && $request['activity_id'] !== "") {
            $datewhere .= " and (a.activity_id = '{$request['activity_id']}') ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datewhere .= " and (s.school_id = '{$request['school_id']}') ";
        }
        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datewhere .= " and s.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_cnname, s.school_branch, a.activity_name, s.school_openclass,
    (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
	COUNT(c.client_id) AS client_maonum,
	COUNT(CASE WHEN c.client_tracestatus <> '-2' THEN 1 ELSE NULL END) AS client_validnum,
	COUNT(CASE WHEN c.client_tracestatus = '-1' THEN 1 ELSE NULL END) AS client_nointentnum,
	COUNT(CASE WHEN c.client_tracestatus = '2' OR c.client_tracestatus = '3' THEN 1 ELSE NULL END) AS client_invitenum,
	COUNT(CASE WHEN c.client_tracestatus = '4' THEN 1 ELSE NULL END) AS client_bookingnum
FROM crm_sell_activity AS a, crm_client AS c, crm_client_schoolenter AS e, smc_school AS s
WHERE {$datewhere} GROUP BY a.activity_id, s.school_id ORDER BY a.activity_id DESC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_maonum'] = $dateexcelvar['client_maonum'];
                    $datearray['client_validnum'] = $dateexcelvar['client_validnum'];
                    $datearray['client_validnum'] = $dateexcelvar['client_nointentnum'];
                    $datearray['client_validnum'] = $dateexcelvar['client_invitenum'];
                    $datearray['client_bookingnum'] = $dateexcelvar['client_bookingnum'];
                    if ($dateexcelvar['client_validnum'] > 0) {
                        $datearray['conv_rate'] = round($dateexcelvar['client_bookingnum'] / $dateexcelvar['client_validnum'], 2) * 100 . "%";
                    } else {
                        $datearray['conv_rate'] = '0%';
                    }
                    $outexceldate[] = $datearray;
                }
            }
//

            $excelheader = array('省份', '校区名称', '校区编号', '活动名称', '新增毛名单', '新增有效名单', '新增无意向名单', '新增到访名单数', '累计学员报名数', '累计转化率');
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'activity_name', 'client_maonum', 'client_validnum', 'client_nointentnum', 'client_invitenum', 'client_bookingnum', 'conv_rate');
            $fielname = $this->LgStringSwitch("活动业绩统计报表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    if ($value['client_validnum'] > 0) {
                        $datalist[$key]['conv_rate'] = round($value['client_bookingnum'] / $value['client_validnum'], 2) * 100 . "%";
                    } else {
                        $datalist[$key]['conv_rate'] = '0.00%';
                    }
                }
            }
            $data['list'] = $datalist;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT COUNT(DISTINCT s.school_id,a.activity_id) AS actnums
FROM crm_sell_activity AS a, crm_client AS c, crm_client_schoolenter AS e, smc_school AS s
WHERE {$datewhere} LIMIT 0,1"
            );
            if ($allNum) {
                $data['allnums'] = $allNum['actnums'];

            } else {
                $data['allnums'] = 0;
            }
        }

        return $data;

    }

    //渠道明细分析报表
    function channelInfoReport($request)
    {
        //$trackwhere = "1";
        $conwhere = "1";
        $auwhere = '1';
        $invitewhere = '1';
        $cwhere = '1';
        $ctwhere = '1';
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            //$trackwhere .= " and clt.track_createtime >='{$stattime}'";
            $conwhere .= " and co.positivelog_time >='{$request['start_time']}'";
            $auwhere .= " and DATE_FORMAT(ca.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $cwhere .= " and c.client_createtime >='{$stattime}'";
            $ctwhere .= " and ct.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $enddate = date('Y-m-d H:i:s', strtotime($request['end_time']));
            $endime = strtotime($enddate) + 60 * 60 * 24 - 1;
            //$trackwhere .= " and clt.track_createtime <='{$endime}'";
            $conwhere .= " and co.positivelog_time <='{$enddate}'";
            $auwhere .= " and DATE_FORMAT(ca.audition_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $cwhere .= " and c.client_createtime <='{$endime}'";
            $ctwhere .= " and ct.client_createtime <='{$endime}'";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] != 0) {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            if ($request['dataequity'] !== '1') {
                //$trackwhere .= " and clt.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}')";
                $conwhere .= " and co.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}')";
                $auwhere .= " and ca.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}')";
                $cwhere .= " and c.client_id in (select cs.client_id from crm_client_schoolenter as cs WHERE cs.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}'))";
            }
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_openclass", "school_id='{$request['school_id']}'");
            //$trackwhere .= " and clt.school_id ='{$request['school_id']}'";
            $conwhere .= " and co.school_id ='{$request['school_id']}'";
            $auwhere .= " and ca.school_id ='{$request['school_id']}'";
            $invitewhere .= " and iv.school_id ='{$request['school_id']}'";
            $cwhere .= " and c.client_id in (select cs.client_id from crm_client_schoolenter as cs WHERE cs.school_id ='{$request['school_id']}')";
            $ctwhere .= " and ct.client_id in (select cs.client_id from crm_client_schoolenter as cs WHERE cs.school_id ='{$request['school_id']}')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $conwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $auwhere .= " AND ce.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invitewhere .= " AND ce.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $cwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $ctwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $conwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auwhere .= " AND ce.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitewhere .= " AND ce.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $cwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $ctwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $conwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auwhere .= " AND ce.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitewhere .= " AND ce.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $cwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $ctwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        $keywhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $keywhere .= " and (ch.channel_name like '%{$request['keyword']}%'  or ch.channel_medianame like '%{$request['keyword']}%' )";
        }


        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== "" && $request['frommedia_name'] !== "[]") {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $keywhere .= " and ch.channel_medianame in ({$commodestr}) ";
            } else {
                $keywhere .= " and ch.channel_medianame='{$request['frommedia_name']}' ";
            }
        }

        if (isset($request['channel_id']) && $request['channel_id'] !== '' && $request['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($request['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $keywhere .= " and ch.channel_id in ({$channelstr}) ";
            } else {
                $keywhere .= " and ch.channel_id='{$request['channel_id']}' ";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ch.channel_id,ch.channel_medianame,ch.channel_name,ch.channel_board,
                (select count(client_id) from crm_client as ct where ct.channel_id = ch.channel_id and ct.company_id = '{$request['company_id']}' and {$ctwhere}) as client_allnum,
                (select count(client_id) from crm_client as ct where ct.channel_id = ch.channel_id and ct.company_id = '{$request['company_id']}'  and {$ctwhere} and ct.client_tracestatus <> '-2' and ct.client_isgross = '0') as client_allvalidnum,
                (select count(client_id) from crm_client as ct where ct.channel_id = ch.channel_id and ct.company_id = '{$request['company_id']}'  and {$ctwhere} and ct.client_tracestatus  = '-1') as client_novalidnum,
                ((select count(DISTINCT iv.client_id) from crm_client_invite as iv,crm_client as ce where ce.client_id =iv.client_id  and ce.channel_id= ch.channel_id and {$invitewhere} ) + 
                (select count(DISTINCT ca.client_id) from crm_client_audition as ca,crm_client as ce where ce.client_id =ca.client_id  and ce.channel_id= ch.channel_id and {$auwhere} and ca.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id=ca.client_id and {$invitewhere} ))
                ) as inv_aud_num,
                ((select count(DISTINCT iv.client_id) from crm_client_invite as iv,crm_client as ce where ce.client_id =iv.client_id  and ce.channel_id= ch.channel_id and {$invitewhere} and iv.invite_isvisit =1 ) + 
                (select count(DISTINCT ca.client_id) from crm_client_audition as ca,crm_client as ce where ce.client_id =ca.client_id  and ce.channel_id= ch.channel_id and {$auwhere} and ca.audition_isvisit =1 and ca.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id=ca.client_id and {$invitewhere} and iv.invite_isvisit =1 ))
                 ) as inv_aud_arrivenum,
                (select count(DISTINCT iv.client_id) from crm_client_invite as iv,crm_client as ce where ce.client_id =iv.client_id  and ce.channel_id= ch.channel_id and {$invitewhere}) as inv_num,
                (select count(DISTINCT iv.client_id) from crm_client_invite as iv,crm_client as ce where ce.client_id =iv.client_id  and ce.channel_id= ch.channel_id and {$invitewhere}  and iv.invite_isvisit =1) as inv_arrivenum,
                (select count(DISTINCT ca.client_id) from crm_client_audition as ca,crm_client as ce where ce.client_id =ca.client_id  and ce.channel_id= ch.channel_id and {$auwhere} and ca.audition_genre =0 ) as OH_audition_num,
                (select count(DISTINCT ca.client_id) from crm_client_audition as ca,crm_client as ce where ce.client_id =ca.client_id  and ce.channel_id= ch.channel_id and {$auwhere} and ca.audition_isvisit =1  and ca.audition_genre =0 ) as OH_audition_arrivenum,
                (select count(DISTINCT ca.client_id) from crm_client_audition as ca,crm_client as ce where ce.client_id =ca.client_id  and ce.channel_id= ch.channel_id and {$auwhere} and ca.audition_genre =1 ) as Class_audition_num,
                (select count(DISTINCT ca.client_id) from crm_client_audition as ca,crm_client as ce where ce.client_id =ca.client_id  and ce.channel_id= ch.channel_id and {$auwhere} and ca.audition_isvisit =1  and ca.audition_genre =1 ) as Class_audition_arrivenum,
                (select count(ct.client_id) from crm_client_positivelog as co left join crm_client as ct ON co.client_id = ct.client_id
                where  ct.company_id ='{$request['company_id']}' AND ct.channel_id = ch.channel_id  and {$conwhere}) as qujian_conversion_num
                from crm_code_channel as ch where ch.company_id = '{$request['company_id']}' and {$keywhere} and exists (select c.client_id from crm_client as c where {$cwhere} and c.channel_id =ch.channel_id )";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];
                    $datearray['section_name'] = $dateexcelvar['channel_board'];

                    $datearray['client_allnum'] = $dateexcelvar['client_allnum'];
                    $datearray['client_allvalidnum'] = $dateexcelvar['client_allvalidnum'];
                    $datearray['client_allvalid_rate'] = $dateexcelvar['client_allnum'] > 0 ? round($dateexcelvar['client_allvalidnum'] / $dateexcelvar['client_allnum'], 4) * 100 . '%' : '0%';
                    $datearray['client_novalidnum'] = $dateexcelvar['client_novalidnum'];
                    $datearray['inv_aud_num'] = $dateexcelvar['inv_aud_num'];
                    $datearray['inv_aud_arrivenum'] = $dateexcelvar['inv_aud_arrivenum'];
                    $datearray['inv_aud_arriverate'] = $dateexcelvar['inv_aud_num'] > 0 ? round($dateexcelvar['inv_aud_arrivenum'] / $dateexcelvar['inv_aud_num'], 4) * 100 . '%' : '0%';
                    $datearray['inv_num'] = $dateexcelvar['inv_num'];
                    $datearray['inv_arrivenum'] = $dateexcelvar['inv_arrivenum'];
                    if (isset($request['school_id']) && $request['school_id'] !== "" && $schoolOne['school_openclass'] == 0) {
                        $datearray['OH_audition_num'] = '--';
                        $datearray['OH_audition_arrivenum'] = '--';
                    } else {
                        $datearray['OH_audition_num'] = $dateexcelvar['OH_audition_num'];
                        $datearray['OH_audition_arrivenum'] = $dateexcelvar['OH_audition_arrivenum'];
                    }
                    $datearray['Class_audition_num'] = $dateexcelvar['Class_audition_num'];
                    $datearray['Class_audition_arrivenum'] = $dateexcelvar['Class_audition_arrivenum'];
                    $datearray['qujian_conversion_num'] = $dateexcelvar['qujian_conversion_num'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('招生渠道明细', '所属渠道类型', '渠道版块', '新增毛名单', "新增有效名单", "名单有效率", "新增无意向名单", "邀约名单数", "到访名单数", "到访率", "柜询邀约名单数", "柜询邀约到访名单数", "OH邀约名单数", "OH邀约到访名单数", "插班试听邀约名单数", "插班试听邀约到访名单数", "报名名单数"));
            $excelfileds = array('channel_name', 'channel_medianame', 'section_name', 'client_allnum', 'client_allvalidnum', 'client_allvalid_rate', 'client_novalidnum', 'inv_aud_num', 'inv_aud_arrivenum', 'inv_aud_arriverate', 'inv_num', 'inv_arrivenum', 'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'qujian_conversion_num');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("渠道明细分析报表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as &$value) {
                    $value['section_name'] = $value['channel_board'];
                    $value['client_allvalid_rate'] = $value['client_allnum'] > 0 ? round($value['client_allvalidnum'] / $value['client_allnum'], 4) * 100 . '%' : '0%';
                    $value['inv_aud_arriverate'] = $value['inv_aud_num'] > 0 ? round($value['inv_aud_arrivenum'] / $value['inv_aud_num'], 4) * 100 . '%' : '0%';
                    if (isset($request['school_id']) && $request['school_id'] !== "" && $schoolOne['school_openclass'] == 0) {
                        $value['OH_audition_num'] = '--';
                        $value['OH_audition_arrivenum'] = '--';
                    }
                }
            }
            $data['list'] = $datalist;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $all_num = $this->DataControl->selectOne("select count(ch.channel_id) as channel_num  from crm_code_channel as ch
                     where ch.company_id='{$request['company_id']}' and {$keywhere} and exists (select c.client_id from crm_client as c where {$cwhere} and c.channel_id =ch.channel_id )");
            if ($all_num) {
                $data['allnum'] = $all_num['channel_num'];
            } else {
                $data['allnum'] = '0';
            }
        } else {
            $data['allnum'] = '0';
        }
        return $data;

    }


    //校园渠道统计分析表
    function schoolChannelReport($request)
    {
//        exit;
        $datawhere = "t.school_id = s.school_id AND t.channel_id = l.channel_id AND s.company_id = '{$request['company_id']}' AND s.school_istest = 0 AND s.school_isclose = 0";
        $cewhere = "c.client_id = e.client_id and e.school_id = s.school_id and c.channel_id = l.channel_id AND e.is_enterstatus = '1'";
        $sewhere = "se.company_id = '{$request['company_id']}'";
        $invwhere = "c.client_id = i.client_id and i.school_id = s.school_id and c.channel_id = l.channel_id";
        $auwhere = "c.client_id = a.client_id and a.school_id = s.school_id and c.channel_id = l.channel_id";
        $register_month_where = "r.student_id = d.student_id AND d.from_client_id = c.client_id and c.channel_id = l.channel_id AND r.school_id = s.school_id and r.info_status = '1'";
        $ivlogwhere = "l.client_id = c.client_id AND c.company_id = '{$request['company_id']}'";
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $cewhere .= " and c.client_createtime >='{$stattime}'";
            $register_month_where .= " and r.pay_successtime >='{$stattime}'";
            $invwhere .= " and i.invite_createtime >='{$stattime}'";
            $auwhere .= " and a.audition_createtime >='{$stattime}'";
            $ivlogwhere .= " AND l.visittime >= '{$request['start_time']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endday = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endtime = strtotime($endday);
            $cewhere .= " and c.client_createtime  <='{$endtime}'";
            $register_month_where .= " and r.pay_successtime <='{$endtime}'";
            $invwhere .= " and i.invite_createtime <='{$endtime}'";
            $auwhere .= " and a.audition_createtime <='{$endtime}'";
            $ivlogwhere .= " AND l.visittime <= '{$endday}'";
        }

//        if (isset($request['start_time']) && $request['start_time'] !== "" && isset($request['end_time']) && $request['end_time'] !== "") {
//            $datawhere .= " and l.channel_id in (SELECT c.channel_id FROM crm_client_schoolenter as t LEFT JOIN crm_client as c ON c.client_id = t.client_id WHERE t.school_id = s.school_id and c.client_createtime>='{$stattime}' and c.client_createtime  <='{$endtime}' )";
//        } 

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] != 0) {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " and s.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}')";
            $ivlogwhere .= " and l.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}')";
            $sewhere .= " and se.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and s.school_id  = '{$request['school_id']}'";
            $ivlogwhere .= " and l.school_id  = '{$request['school_id']}'";
            $sewhere .= " and se.school_id  = '{$request['school_id']}'";
        } else {
//            if (!isset($request['keyword']) || $request['keyword'] == "") {
//                $this->error = true;
//                $this->errortip = "请先选择学校";
//                return false;
//            }
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                //$datawhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $cewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                //$invwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                //$auwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $register_month_where .= " AND r.coursetype_id = '{$request['coursetype_id']}' AND r.coursecat_id = '{$request['coursecat_id']}'";
                $ivlogwhere .= " AND l.coursetype_id = '{$request['coursetype_id']}' AND l.coursecat_id = '{$request['coursecat_id']}'";
            } else {
                //$datawhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $cewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                //$invwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                //$auwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $register_month_where .= " AND r.coursetype_id = '{$request['coursetype_id']}'";
                $ivlogwhere .= " AND l.coursetype_id = '{$request['coursetype_id']}'";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            //$datawhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $cewhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            //$invwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            //$auwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $register_month_where .= " AND r.coursecat_id = '{$request['coursecat_id']}'";
            $ivlogwhere .= " AND l.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== "" && $request['frommedia_name'] !== "[]") {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and l.channel_medianame in ({$commodestr}) ";
            } else {
                $request['frommedia_name'] = str_replace('"', '', stripslashes($request['frommedia_name']));
                $datawhere .= " and l.channel_medianame='{$request['frommedia_name']}' ";
            }
        }

        if (isset($request['channel_id']) && $request['channel_id'] !== '' && $request['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($request['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and l.channel_id in ({$channelstr}) ";
                $ivlogwhere .= " and c.channel_id in ({$channelstr}) ";
                $sewhere .= " and se.channel_id in ({$channelstr}) ";
            } else {
                $request['channel_id'] = str_replace('"', '', stripslashes($request['channel_id']));
                $datawhere .= " and l.channel_id='{$request['channel_id']}' ";
                $ivlogwhere .= " and c.channel_id='{$request['channel_id']}' ";
                $sewhere .= " and se.channel_id='{$request['channel_id']}' ";
            }
        }
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            if ($request['keyword'] == '转介绍' || $request['keyword'] == '园展校' || $request['keyword'] == '空军' || $request['keyword'] == '陆军') {
                $datawhere .= " and l.channel_board = '{$request['keyword']}'";
            } else {
                $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%' or s.school_tagbak ='{$request['keyword']}') ";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_id,s.school_openclass,s.school_cnname,s.school_branch
,(select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name
,(SELECT d.district_cnname FROM gmc_company_district AS  d WHERE d.district_id = s.district_id) AS district_cnname
,l.channel_id, l.channel_medianame, l.channel_name, l.channel_board
,(select count(DISTINCT c.client_id) from crm_client as c,crm_client_schoolenter as e WHERE {$cewhere}) as client_num
,(select count(DISTINCT c.client_id) from crm_client as c,crm_client_schoolenter as e where {$cewhere} and c.client_tracestatus <> '-2' and c.client_isgross = '0' and c.client_intention_maxlevel > 2) as client_validnum
,(select count(DISTINCT c.client_id) from crm_client as c,crm_client_schoolenter as e where {$cewhere} and c.client_tracestatus = '-1') as client_novalidnum
,(SELECT COUNT(r.info_id) from smc_student_registerinfo as r,smc_student AS d,crm_client AS c where {$register_month_where}) as register_num
,(SELECT COUNT(r.info_id) from smc_student_registerinfo as r,smc_student AS d,crm_client AS c where {$register_month_where}
AND NOT EXISTS(SELECT 1 FROM smc_student_registerinfo x, smc_code_coursetype y WHERE x.coursetype_id = y.coursetype_id
AND x.student_id = r.student_id AND x.coursetype_id <> r.coursetype_id AND x.pay_successtime < r.pay_successtime AND x.info_status = 1)) as crm_newnum
FROM ( SELECT se.school_id, se.channel_id FROM crm_client_schoolenter se WHERE {$sewhere} GROUP BY se.school_id, se.channel_id ) t,smc_school s, crm_code_channel l
WHERE {$datawhere}";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无渠道明细分析报表";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                $inviteList = $this->DataControl->selectClear("SELECT COUNT(l.client_id) as inv_aud_clientnum, SUM(IF(isvisit = 1, 1, 0)) AS inv_aud_arriveclientnum,l.school_id,c.channel_id
                                FROM view_crm_invitelog l, crm_client c WHERE {$ivlogwhere} GROUP BY l.school_id, c.channel_id");
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];
                    $datearray['section_name'] = $dateexcelvar['channel_board'];
                    $datearray['client_num'] = $dateexcelvar['client_num'];
                    $datearray['client_validnum'] = $dateexcelvar['client_validnum'];
                    $datearray['client_validrate'] = $dateexcelvar['client_num'] > 0 ? round($dateexcelvar['client_validnum'] / $dateexcelvar['client_num'], 4) * 100 . '%' : '0%';
                    $datearray['client_novalidnum'] = $dateexcelvar['client_novalidnum'];

                    $datearray['inv_aud_clientnum'] = 0;
                    $datearray['inv_aud_arriveclientnum'] = 0;
                    if (is_array($inviteList)) {
                        foreach ($inviteList as $inviteVar) {
                            if ($inviteVar['school_id'] == $dateexcelvar['school_id'] && $inviteVar['channel_id'] == $dateexcelvar['channel_id']) {
                                $datearray['inv_aud_clientnum'] = $inviteVar['inv_aud_clientnum'];
                                $datearray['inv_aud_arriveclientnum'] = $inviteVar['inv_aud_arriveclientnum'] ? $inviteVar['inv_aud_arriveclientnum'] : 0;
                            }
                        }
                    }

                    $datearray['inv_aud_arriverate'] = $datearray['inv_aud_arriveclientnum'] ? round($datearray['inv_aud_arriveclientnum'] / $datearray['inv_aud_clientnum'], 4) * 100 . '%' : '0%';

                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $datearray['crm_newnum'] = $dateexcelvar['crm_newnum'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '招生渠道明细', '所属渠道类型', '渠道板块', '新增毛名单', '新增有效名单', '新增有效率', '新增无意向名单', "邀约名单数", "到访名单数", '到访率', '报名名单数', '累计招新人数'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'channel_name', 'channel_medianame', 'section_name', 'client_num', 'client_validnum', 'client_validrate', 'client_novalidnum', 'inv_aud_clientnum', 'inv_aud_arriveclientnum', 'inv_aud_arriverate', 'register_num', 'crm_newnum');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校园渠道明细统计表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                $inviteList = $this->DataControl->selectClear("SELECT COUNT(l.client_id) as inv_aud_clientnum, SUM(IF(isvisit = 1, 1, 0)) AS inv_aud_arriveclientnum,l.school_id,c.channel_id
                                FROM view_crm_invitelog l, crm_client c WHERE {$ivlogwhere} GROUP BY l.school_id, c.channel_id");
                foreach ($datalist as &$value) {
                    $value['section_name'] = $value['channel_board'];
                    $value['client_validrate'] = $value['client_num'] > 0 ? round($value['client_validnum'] / $value['client_num'], 4) * 100 . '%' : '0%';

                    $value['inv_aud_clientnum'] = 0;
                    $value['inv_aud_arriveclientnum'] = 0;
                    if (is_array($inviteList)) {
                        foreach ($inviteList as $inviteVar) {
                            if ($inviteVar['school_id'] == $value['school_id'] && $inviteVar['channel_id'] == $value['channel_id']) {
                                $value['inv_aud_clientnum'] = $inviteVar['inv_aud_clientnum'];
                                $value['inv_aud_arriveclientnum'] = $inviteVar['inv_aud_arriveclientnum'] ? $inviteVar['inv_aud_arriveclientnum'] : 0;
                            }
                        }
                    }

                    $value['inv_aud_arriverate'] = $value['inv_aud_arriveclientnum'] ? round($value['inv_aud_arriveclientnum'] / $value['inv_aud_clientnum'], 4) * 100 . '%' : '0%';
                    $value['positive_num'] = $value['register_num'];
                }
            }
            $data['list'] = $datalist;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT COUNT(l.channel_id) AS cnums
FROM ( SELECT se.school_id, se.channel_id FROM crm_client_schoolenter se WHERE {$sewhere} GROUP BY se.school_id, se.channel_id ) t,smc_school s, crm_code_channel l WHERE {$datawhere}");
            if ($allNum) {
                $data['allnum'] = $allNum['cnums'];
            } else {
                $data['allnum'] = '0';
            }
        } else {
            $data['allnum'] = '0';
        }
        return $data;
    }

    //校园转介绍渠道统计分析表
    function referralAnalyseReport($request)
    {
        $datawhere = "s.company_id = l.company_id AND l.channel_isreferral = '1' AND s.school_istest <> '1' AND s.school_isclose <> '1' AND s.company_id = '{$request['company_id']}'";
        $clientwhere = "c.client_id = e.client_id AND e.school_id = s.school_id AND e.is_enterstatus = '1' AND c.channel_id = l.channel_id";
        $prewhere = "c.client_id = e.client_id AND e.school_id = s.school_id AND c.channel_id = l.channel_id";
        $invitewhere = "i.company_id = s.company_id";
        $auditionwhere = "a.company_id = s.company_id";

        $today = date("Y-m-d");
        $register_reading_where = " ta.school_id = s.school_id AND ta.beginday <= '{$today}' AND ta.endday >= '{$today}'";
        $register_reading_where .= " and ta.coursetype_id='65'";
        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $stattime = strtotime($request['starttime']);
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
            $prewhere .= " and c.client_createtime >='{$stattime}'";
            $invitewhere .= " AND i.invite_visittime >= '{$request['starttime']}'";
            $auditionwhere .= " AND a.audition_visittime >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $clientwhere .= " and c.client_createtime  <='{$endime}'";
            $prewhere .= " and c.client_createtime  <='{$endime}'";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] != 0) {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " and s.school_id in (SELECT s.school_id FROM gmc_company_organizeschool AS s WHERE s.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and s.school_id  = '{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and s.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (l.channel_name like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%') ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_cnname, s.school_branch,
        (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
        (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname, l.channel_name,
            (SELECT COUNT(1) FROM (
            SELECT A.school_id,A.student_id,C.coursetype_id,MIN(A.study_beginday) AS beginday,MAX(A.study_endday) AS endday
            FROM smc_student_study A
            LEFT JOIN smc_class B ON A.class_id=B.class_id
            LEFT JOIN smc_course C ON B.course_id=C.course_id
            LEFT JOIN smc_code_coursetype D ON C.coursetype_id=D.coursetype_id
            WHERE 1
            AND A.company_id='{$request['company_id']}'
            AND B.class_type='0' AND B.class_status>'-2'
            AND A.study_endday >= DATE_SUB('{$today}', INTERVAL D.coursetype_intervaltime DAY)
            GROUP BY A.school_id,A.student_id,C.coursetype_id ) ta
         WHERE ({$register_reading_where})) as readingnums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere} limit 0,1) as maonums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere} AND c.client_tracestatus <> '-2' limit 0,1) as validnums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND (c.client_id IN ( SELECT i.client_id FROM crm_client_invite i WHERE {$invitewhere} )
OR c.client_id IN ( SELECT a.client_id FROM crm_client_audition a WHERE {$auditionwhere}))) as invitenums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND (c.client_id IN ( SELECT i.client_id FROM crm_client_invite i WHERE {$invitewhere} AND i.invite_isvisit ='1' )
OR c.client_id IN ( SELECT a.client_id FROM crm_client_audition a WHERE {$auditionwhere} AND a.audition_isvisit ='1'))) as visitnums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND c.client_id IN ( SELECT i.client_id FROM crm_client_invite i WHERE {$invitewhere})) as inquirenums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND c.client_id IN ( SELECT i.client_id FROM crm_client_invite i WHERE {$invitewhere} AND i.invite_isvisit ='1')) as inquirevisitnums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND c.client_id IN ( SELECT a.client_id FROM crm_client_audition a WHERE {$auditionwhere} AND a.audition_genre = '1')) as auditionnums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND  c.client_id IN ( SELECT a.client_id FROM crm_client_audition a WHERE {$auditionwhere} AND a.audition_genre = '1' AND a.audition_isvisit ='1')) as auditioninvitenums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND c.client_id IN ( SELECT a.client_id FROM crm_client_audition a WHERE {$auditionwhere} AND a.audition_genre = '0')) as ohauditionnums,
(SELECT count(c.client_id) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$clientwhere}
AND  c.client_id IN ( SELECT a.client_id FROM crm_client_audition a WHERE {$auditionwhere} AND a.audition_genre = '0' AND a.audition_isvisit ='1')) as ohauditionvisitnums,
(select count(e.client_id) from crm_client_positivelog as e,crm_client as c WHERE {$prewhere}) as booknums
FROM smc_school s, crm_code_channel l WHERE {$datawhere}";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();

                foreach ($dateexcelarray as $dataOne) {
                    $datearray = array();
                    $datearray['province_name'] = $dataOne['province_name'];
                    $datearray['school_cnname'] = $dataOne['school_cnname'];
                    $datearray['school_branch'] = $dataOne['school_branch'];
                    $datearray['readingnums'] = $dataOne['readingnums'];
                    $datearray['channel_name'] = $dataOne['channel_name'];
                    $datearray['maonums'] = $dataOne['maonums'];
                    $datearray['validnums'] = $dataOne['validnums'];
                    $datearray['validrate'] = $dataOne['maonums'] > 0 ? round($dataOne['validnums'] / $dataOne['maonums'], 4) * 100 . '%' : '0%';
                    $datearray['invitenums'] = $dataOne['invitenums'];
                    $datearray['visitnums'] = $dataOne['visitnums'];
                    $datearray['invvisitrate'] = $dataOne['invitenums'] > 0 ? round($dataOne['visitnums'] / $dataOne['invitenums'], 4) * 100 . '%' : '0%';
                    $datearray['inquirenums'] = $dataOne['inquirenums'];
                    $datearray['inquirevisitnums'] = $dataOne['inquirevisitnums'];
                    //$datearray['inquireviterate'] = $dataOne['inquirenums'] > 0 ? round($dataOne['inquirevisitnums'] / $dataOne['inquirenums'], 4) * 100 . '%' : '0%';
                    $datearray['ohauditionnums'] = $dataOne['ohauditionnums'];
                    $datearray['ohauditionvisitnums'] = $dataOne['ohauditionvisitnums'];
                    //$datearray['ohauditionvisitrate'] = $dataOne['ohauditionnums'] > 0 ? round($dataOne['ohauditionvisitnums'] / $dataOne['ohauditionnums'], 4) * 100 . '%' : '0%';
                    $datearray['auditionnums'] = $dataOne['auditionnums'];
                    $datearray['auditioninvitenums'] = $dataOne['auditioninvitenums'];
                    //$datearray['auditioninviterate'] = $dataOne['auditionnums'] > 0 ? round($dataOne['auditioninvitenums'] / $dataOne['auditionnums'], 4) * 100 . '%' : '0%';
                    $datearray['booknums'] = $dataOne['booknums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '美语在读人数', '渠道名称', '新增毛名单', '新增有效名单', '新增有效率', "新增邀约名单数", "新增到访名单数", '新增到访率'
            , '柜询邀约数', '柜询邀约到访数', 'OH邀约数', 'OH邀约到访数', '试听邀约数', '试听邀约到访数', '报名名单数'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'readingnums', 'channel_name', 'maonums', 'validnums', 'validrate', 'invitenums', 'visitnums', 'invvisitrate'
            , 'inquirenums', 'inquirevisitnums', 'ohauditionnums', 'ohauditionvisitnums', 'auditionnums', 'auditioninvitenums', 'booknums');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("转介绍业绩统计表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as &$dataOne) {
                    $dataOne['validrate'] = $dataOne['maonums'] > 0 ? round($dataOne['validnums'] / $dataOne['maonums'], 4) * 100 . '%' : '0%';
                    $dataOne['invvisitrate'] = $dataOne['invitenums'] > 0 ? round($dataOne['visitnums'] / $dataOne['invitenums'], 4) * 100 . '%' : '0%';
                    $dataOne['inquireviterate'] = $dataOne['inquirenums'] > 0 ? round($dataOne['inquirevisitnums'] / $dataOne['inquirenums'], 4) * 100 . '%' : '0%';
                    $dataOne['ohauditionvisitrate'] = $dataOne['ohauditionnums'] > 0 ? round($dataOne['ohauditionvisitnums'] / $dataOne['ohauditionnums'], 4) * 100 . '%' : '0%';
                    $dataOne['auditioninviterate'] = $dataOne['auditionnums'] > 0 ? round($dataOne['auditioninvitenums'] / $dataOne['auditionnums'], 4) * 100 . '%' : '0%';
                }
            }
            $data['list'] = $datalist;
        }

        $allNum = $this->DataControl->selectOne("select COUNT(s.school_id) AS snums FROM smc_school AS s, crm_code_channel AS l WHERE {$datawhere} limit 0,1");
        if ($allNum) {
            $data['allnum'] = $allNum['snums'];
        } else {
            $data['allnum'] = '0';
        }
        return $data;
    }

    /**
     * 杨董 需要的招生报表
     * author: ling
     * 对应接口文档 0001
     */
    function getClientHighestReport($request)
    {
        $datawhere = "s.company_id = '{$request['company_id']}' AND s.school_isclose = '0' AND s.school_istest <> '1'";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        if (isset($request['school_type']) && $request['school_type'] !== '') {
            $datawhere .= " and s.school_type='{$request['school_type']}'";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] != '1') {
            $postbeOne = $this->AnalyzeControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $today = date("Y-m-d", strtotime($request['fixedtime']));
            $todaytime = strtotime($request['fixedtime']) + 24 * 60 * 60 - 1;
        } else {
            $today = date("Y-m-d");
            $todaytime = time();
        }

        $thisWeek = GetWeekAll($today);

        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));
        $quarter_startdate = date("Y-m-d", strtotime("-90 days", strtotime($today)));
        $sixmoth_startdate = date("Y-m-d", strtotime("-180 days", strtotime($today)));

        //本月初  本月末
        $monthstart_time = date('Y-m-01', time());
        $mdays = date('t', time());
        $monthend_time = date('Y-m-' . $mdays, time());

        //本周一
        $weekFirst_start_time = date('Y-m-d', (time() - ((date('w') == 0 ? 7 : date('w')) - 1) * 24 * 3600));

        //当前时间
        $nowime = date("Y-m-d");

        $week_starttime = strtotime($week_startdate);
        $month_starttime = strtotime($month_startdate);
        $preyear_starttime = strtotime('2021-01-01');
        $preyear_endtime = strtotime('2021-12-31');
        $nowyear_starttime = strtotime(date("Y-1-1"));
        $quarter_starttime = strtotime($quarter_startdate);
        $threemoth_starttime = strtotime($quarter_startdate);
        $sixmoth_starttime = strtotime($sixmoth_startdate);

        $autumn_starttime = strtotime('2021-09-01');
        $autumn_endtime = strtotime('2022-01-31') + 86399;

        $register_week_where = "r.pay_successtime >= '{$week_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_month_where = "r.pay_successtime >= '{$month_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $pre_year_where = "r.pay_successtime >= '{$preyear_starttime}' and r.pay_successtime <='{$preyear_endtime}' and r.info_status =1";
        $now_year_where = "r.pay_successtime >= '{$nowyear_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $autumn_history_where = "r.pay_successtime >= '{$autumn_starttime}' and r.pay_successtime <='{$autumn_endtime}' and r.info_status =1";
        $register_absentee_where = "si.school_id = s.school_id AND si.studyinfo_status = 1";
        $register_feereading_where = "d.class_id = c.class_id AND c.course_id = r.course_id AND d.school_id = ac.school_id AND d.student_id = ac.student_id
        AND c.course_id = ac.course_id AND d.school_id = s.school_id AND ac.coursebalance_figure = '0' AND d.study_beginday <= '{$today}' AND d.study_endday >= '{$today}'";
        $all_client_where = "cs.client_id = t.client_id and cs.school_id = s.school_id";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $register_week_where .= " and r.coursetype_id='{$request['coursetype_id']}'";
            $register_month_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $pre_year_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $now_year_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $autumn_history_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_absentee_where .= " and si.coursetype_id='{$request['coursetype_id']}'";
            $register_feereading_where .= " and r.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
            $register_week_where .= " and r.coursecat_id='{$request['coursecat_id']}'";
            $register_month_where .= " and r.coursecat_id='{$request['coursecat_id']}' ";
            $pre_year_where .= " and r.coursecat_id='{$request['coursecat_id']}' ";
            $now_year_where .= " and r.coursecat_id='{$request['coursecat_id']}' ";
            $autumn_history_where .= " and r.coursecat_id='{$request['coursecat_id']}' ";
            $register_absentee_where .= " and si.coursecat_id='{$request['coursecat_id']}'";
            $register_feereading_where .= " and r.coursecat_id='{$request['coursecat_id']}'";
        }


        $client_week_where = "t.client_createtime >='{$week_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_month_where = "t.client_createtime >='{$month_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_quarter_where = "t.client_createtime >='{$quarter_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_threemoth_where = "t.client_createtime >='{$threemoth_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_sixmoth_where = "t.client_createtime >='{$sixmoth_starttime}' and t.client_createtime <='{$todaytime}'";

        $track_week_where = "k.track_createtime >='{$week_starttime}' and k.track_createtime <='{$todaytime}' and k.track_isactive =1";
        $track_month_where = "k.track_createtime >='{$month_starttime}' and k.track_createtime <='{$todaytime}' and k.track_isactive =1";

        $invite_week_where = "i.invite_visittime >='{$week_startdate}' and i.invite_visittime <= '{$today}' ";
        $invite_month_where = "i.invite_visittime >='{$month_startdate}' and i.invite_visittime <= '{$today}'";

        $audition_lastweek_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$thisWeek['lastweek_start']}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$thisWeek['lastweek_end']}'";
        $audition_curweek_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$thisWeek['nowweek_start']}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$thisWeek['nowweek_end']}'";
        $audition_week_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$week_startdate}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$today}' ";
        $audition_month_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$month_startdate}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <= '{$today}'";
        $audition_thismonth_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$monthstart_time}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <= '{$monthend_time}'";

        $trading_week_where = "DATE_FORMAT(r.trading_updatatime,'%Y-%m-%d') >='{$monthstart_time}' and DATE_FORMAT(r.trading_updatatime,'%Y-%m-%d') <= '{$nowime}'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $client_week_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $client_month_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $client_quarter_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $client_threemoth_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $client_sixmoth_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";

                $all_client_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";

                $track_week_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $track_month_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";

                $invite_week_where .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $invite_month_where .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";

                $audition_lastweek_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $audition_curweek_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $audition_week_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $audition_month_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $audition_thismonth_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $client_week_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $client_month_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $client_quarter_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $client_threemoth_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $client_sixmoth_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";

                $all_client_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";

                $track_week_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $track_month_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";

                $invite_week_where .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invite_month_where .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";

                $audition_lastweek_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audition_curweek_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audition_week_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audition_month_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audition_thismonth_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
            $client_week_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $client_month_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $client_quarter_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $client_threemoth_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $client_sixmoth_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";

            $all_client_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";

            $track_week_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $track_month_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";

            $invite_week_where .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invite_month_where .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";

            $audition_lastweek_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audition_curweek_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audition_week_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audition_month_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audition_thismonth_where .= " AND a.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }


        $sql = " select s.school_id,s.school_openclass,s.school_shortname,s.school_branch,s.school_foundtime,
         (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province ) as province_name,
         (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS school_tagbak,
         (SELECT count(si.studyinfo_id) FROM temp_smc_student_studyinfo AS si WHERE {$register_absentee_where} AND si.studyinfo_type = 1) AS 'absenteenums',
         (SELECT count(si.studyinfo_id) FROM temp_smc_student_studyinfo AS si  WHERE {$register_absentee_where} AND si.studyinfo_type = 2) AS 'readingnums',
         (SELECT COUNT(DISTINCT d.student_id) FROM smc_student_study as d,smc_class as c,smc_course as r,smc_student_coursebalance as ac
         WHERE ({$register_feereading_where})) as feereadingnums,
         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id  and {$client_month_where} ) as mao_month_client_num,
         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id  and {$client_week_where} ) as mao_week_client_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_week_where} ) as positivelog_week_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where} ) as positivelog_month_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} ) as prelog_year_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} AND r.student_id IN (SELECT p.student_id FROM smc_student_guildpolicy as p) ) as prelog_caseyear_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$now_year_where} ) as nowlog_year_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r,smc_student AS s,crm_client AS t where r.student_id = s.student_id AND s.from_client_id = t.client_id AND t.client_fromtype = '1' AND r.school_id = s.school_id and {$register_month_where} ) as inmonth_num,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_week_where} and h.channel_way =1 and t.client_intention_level>=3) as client_under_weeknum,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_week_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_weeknum,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and h.channel_way =1 and t.client_intention_level>=3) as client_under_monthnum,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_monthnum,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_quarter_where} and t.client_intention_level>=3) as client_up_quarternum,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_tracestatus IN (0,1,2,3,4,'-1') and t.client_isgross = '0') as client_all_num,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_stubranch <> '' and {$client_month_where}) as recomend_clientnum,
         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_threemoth_where} and t.client_tracestatus IN (0,1,2,3,4,'-1') and t.client_isgross = '0' ) as threemoth_clientnum,
         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_sixmoth_where} and t.client_tracestatus IN (0,1,2,3,4,'-1') and t.client_isgross = '0' ) as sixmoth_clientnum,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_distributionstatus = 0  and client_tracestatus = 0  and t.client_isgross = '0') as client_noallot_num,
        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_week_where} ) as track_client_num ,
        (select count(k.track_id) from crm_client_track as  k where k.school_id=s.school_id and  {$track_week_where}) as track_tracknum ,
        (select count( k.track_id) from crm_client_track as  k where k.school_id=s.school_id and  {$track_week_where} and track_linktype ='电话沟通') as track_week_tephonenum ,
        (select count(k.track_id) from crm_client_track as  k where k.school_id=s.school_id  and  {$track_month_where} and track_linktype ='电话沟通') as track_month_tephonenum ,
        (select count(i.invite_id) from  crm_client_invite as i where i.school_id=s.school_id and {$invite_month_where} and i.invite_isvisit=0 ) as invite_no_confirm,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_isvisit=0 and  {$audition_month_where} ) as audition_no_confirm,
        (select count(i.invite_id) from  crm_client_invite as i where i.school_id=s.school_id and {$invite_week_where} ) as invite_week_num,
        (select count(i.invite_id) from  crm_client_invite as i where i.school_id=s.school_id and {$invite_month_where} ) as invite_month_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id  and  {$audition_thismonth_where} ) as audition_thismonth_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and {$audition_curweek_where} ) as ohaudition_curweek_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=1 and {$audition_curweek_where} ) as jmcaudition_curweek_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and {$audition_lastweek_where} ) as ohaudition_lastweek_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and audition_isvisit =1 and {$audition_lastweek_where} ) as ohaudition_lastweek_postnum,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and {$audition_week_where} ) as ohaudition_week_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and  {$audition_month_where} ) as ohaudition_month_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0  and a.audition_isvisit =1 and {$audition_week_where} ) as ohaudition_week_arrnum,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and a.audition_isvisit =1 and  {$audition_month_where} ) as ohaudition_month_arrnum,
        (select count(a.audition_id) from  crm_client_audition as a,crm_client_positivelog as  pg where a.school_id=s.school_id and a.audition_genre=0 and a.audition_isvisit =1 and  pg.client_id = a.client_id and a.school_id = pg.school_id and pg.positivelog_time = DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') and {$audition_month_where} ) as ohpostive_month_arrnum,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and {$audition_week_where} ) as audition_week_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id  and  {$audition_month_where} ) as audition_month_num,
        (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus <> '-2' and client_fromtype ='0' and {$client_month_where}) as client_outnum,
        (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus <> '-2' and client_fromtype ='1' and {$client_month_where}) as client_innernum
        FROM smc_school as s where {$datawhere}
        order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    if ($datearray['school_foundtime'] != '' && !is_null($datearray['school_foundtime'])) {
                        $date11 = explode('-', $datearray['school_foundtime']);
                        $date22 = explode('-', date('Y-m-d'));
                        $date33 = abs($date11[0] - $date22[0]) * 12 - ($date11[1] - $date22[1]);
                        $datearray['schoolage'] = $date33 . '月';
                    } else {
                        $datearray['schoolage'] = '--';
                    }
                    $datearray['absenteenums'] = $dateexcelvar['absenteenums'];
                    $datearray['readingnums'] = $dateexcelvar['readingnums'];
                    $datearray['feereadingnums'] = $dateexcelvar['feereadingnums'];
                    $datearray['prelog_year_num'] = $dateexcelvar['prelog_year_num'];
                    $datearray['prelog_caseyear_num'] = $dateexcelvar['prelog_caseyear_num'];
                    $datearray['nowlog_year_num'] = $dateexcelvar['nowlog_year_num'];

                    $datearray['positivelog_week_num'] = $dateexcelvar['positivelog_week_num'];
                    $datearray['positivelog_month_num'] = $dateexcelvar['positivelog_month_num'];
                    $datearray['inmonth_num'] = $dateexcelvar['inmonth_num'];
                    $datearray['outmonth_num'] = $dateexcelvar['positivelog_month_num'] - $dateexcelvar['inmonth_num'];
                    $datearray['mao_month_client_num'] = $dateexcelvar['mao_month_client_num'];
                    $datearray['mao_week_client_num'] = $dateexcelvar['mao_week_client_num'];
                    $datearray['client_under_weeknum'] = $dateexcelvar['client_under_weeknum'];
                    $datearray['client_under_monthnum'] = $dateexcelvar['client_under_monthnum'];
                    $datearray['client_up_weeknum'] = $dateexcelvar['client_up_weeknum'];
                    $datearray['client_up_monthnum'] = $dateexcelvar['client_up_monthnum'];
                    $datearray['client_up_quarternum'] = $dateexcelvar['client_up_quarternum'];
                    $datearray['recomend_clientnum'] = $dateexcelvar['recomend_clientnum'];
                    $datearray['sixmoth_clientnum'] = $dateexcelvar['sixmoth_clientnum'];
                    $datearray['client_all_num'] = $dateexcelvar['client_all_num'];
                    $datearray['client_noallot_num'] = $dateexcelvar['client_noallot_num'];
                    $datearray['track_client_num'] = $dateexcelvar['track_client_num'];
                    $datearray['track_tracknum'] = $dateexcelvar['track_tracknum'];
                    $datearray['track_week_tephonenum'] = $dateexcelvar['track_week_tephonenum'];
                    $datearray['track_month_tephonenum'] = $dateexcelvar['track_month_tephonenum'];
                    $datearray['no_confirm'] = $dateexcelvar['audition_no_confirm'] + $dateexcelvar['invite_no_confirm'];
                    $datearray['invite_week_num'] = $dateexcelvar['invite_week_num'];
                    $datearray['invite_month_num'] = $dateexcelvar['invite_month_num'];
                    $datearray['audition_thismonth_num'] = $dateexcelvar['audition_thismonth_num'];
                    if ($dateexcelvar['school_openclass'] == 1) {
                        $datearray['ohaudition_curweek_num'] = $dateexcelvar['ohaudition_curweek_num'];
                    } else {
                        $datearray['ohaudition_curweek_num'] = '--';
                    }
                    $datearray['jmcaudition_curweek_num'] = $dateexcelvar['jmcaudition_curweek_num'];
                    if ($dateexcelvar['school_openclass'] == 1) {
                        $datearray['ohaudition_lastweek_num'] = $dateexcelvar['ohaudition_lastweek_num'];
                        $datearray['ohaudition_lastweek_postnum'] = $dateexcelvar['ohaudition_lastweek_postnum'];
                        $datearray['ohaudition_week_num'] = $dateexcelvar['ohaudition_week_num'];
                        $datearray['ohaudition_month_num'] = $dateexcelvar['ohaudition_month_num'];
                        $datearray['ohaudition_week_arrnum'] = $dateexcelvar['ohaudition_week_arrnum'];
                        $datearray['ohaudition_month_arrnum'] = $dateexcelvar['ohaudition_month_arrnum'];
                    } else {
                        $datearray['ohaudition_lastweek_num'] = '--';
                        $datearray['ohaudition_lastweek_postnum'] = '--';
                        $datearray['ohaudition_week_num'] = '--';
                        $datearray['ohaudition_month_num'] = '--';
                        $datearray['ohaudition_week_arrnum'] = '--';
                        $datearray['ohaudition_month_arrnum'] = '--';
                    }
                    $datearray['audition_week_num'] = $dateexcelvar['audition_week_num'];
                    $datearray['audition_month_num'] = $dateexcelvar['audition_month_num'];
                    if ($dateexcelvar['school_openclass'] == 1) {
                        $datearray['ohpostive_month_arrnum'] = $dateexcelvar['ohpostive_month_arrnum'];
                        if ($dateexcelvar['ohaudition_month_arrnum']) {
                            $datearray['ohpostive_month_rate'] = round($dateexcelvar['ohpostive_month_arrnum'] / $dateexcelvar['ohaudition_month_arrnum'], 4) * 100;
                        } else {
                            $datearray['ohpostive_month_rate'] = 0;
                        }
                    } else {
                        $datearray['ohpostive_month_arrnum'] = '--';
                        $datearray['ohpostive_month_rate'] = '--';
                    }
                    $datearray['client_innernum'] = $dateexcelvar['client_innernum'];
                    $datearray['client_outnum'] = $dateexcelvar['client_outnum'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '校龄', '在籍', '在读', '专案免费在读人数', '报名/去年', '专案报名/去年', '报名/当年'
            , '报名/周', '报名/月', '内招/月', '外招/月', '新增毛名单/月', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/季',
                '推荐名单/月', '6个月内有效名单', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪人次/周', "电询/周", "电询/月", '未确认邀约数/月', '柜询/周',
                '柜询/月', 'OH邀约/当周', '插班邀约/当周', 'OH邀约/上周', 'OH邀约到访/上周', 'OH邀约/自然周', 'OH邀约/自然月', "OH到访/自然周", "OH到访/自然月", "试听/周",
                "试听/月", "OH转正数/月", "OH转正率/月"));

            $excelfileds = array('province_name', 'school_shortname', 'school_branch', 'schoolage', 'absenteenums', 'readingnums', 'feereadingnums', 'prelog_year_num', 'prelog_caseyear_num', 'nowlog_year_num'
            , 'positivelog_week_num', 'positivelog_month_num', 'inmonth_num', 'outmonth_num', 'mao_month_client_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum',
                'recomend_clientnum', 'sixmoth_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_tracknum', 'track_week_tephonenum', 'track_month_tephonenum', 'no_confirm', 'invite_week_num',
                'invite_month_num', 'ohaudition_curweek_num', 'jmcaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_lastweek_postnum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num',
                'audition_month_num', 'ohpostive_month_arrnum', 'ohpostive_month_rate');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校区招生大表-截止日期{$today}.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
            $allNum = $this->AnalyzeControl->selectOne("select count(s.school_id)  as  allnum from smc_school as s where {$datawhere}");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            } else {
                foreach ($dataList as $key => $dataOne) {
                    $dataList[$key]['outmonth_num'] = $dataOne['positivelog_month_num'] - $dataOne['inmonth_num'];
                    if ($dataOne['ohaudition_month_arrnum']) {
                        $dataList[$key]['ohpostive_month_rate'] = round($dataOne['ohpostive_month_arrnum'] / $dataOne['ohaudition_month_arrnum'], 4) * 100;
                    } else {
                        $dataList[$key]['ohpostive_month_rate'] = 0;
                    }
                    $dataList[$key]['no_confirm'] = $dataOne['audition_no_confirm'] + $dataOne['invite_no_confirm'];
                    if ($dataOne['school_openclass'] == 0) {
                        $dataList[$key]['ohaudition_curweek_num'] = '--';
                        $dataList[$key]['ohaudition_lastweek_num'] = '--';
                        $dataList[$key]['ohaudition_lastweek_postnum'] = '--';
                        $dataList[$key]['ohaudition_week_num'] = '--';
                        $dataList[$key]['ohaudition_month_num'] = '--';
                        $dataList[$key]['ohaudition_week_arrnum'] = '--';
                        $dataList[$key]['ohaudition_month_arrnum'] = '--';
                        $dataList[$key]['ohpostive_month_arrnum'] = '--';
                        $dataList[$key]['ohpostive_month_rate'] = '--';
                    }

                    if ($dataOne['school_foundtime'] != '' && !is_null($dataOne['school_foundtime'])) {
                        $date11 = explode('-', $dataOne['school_foundtime']);
                        $date22 = explode('-', date('Y-m-d'));
                        $date33 = abs($date11[0] - $date22[0]) * 12 - ($date11[1] - $date22[1]);
                        $dataList[$key]['schoolage'] = $date33 . '月';
                    } else {
                        $dataList[$key]['schoolage'] = '--';
                    }

                }
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = $allNum['allnum'];
            return $result;
        }
    }

    //董 需要的招生报表  -----  学校这边提的修改
    function getClientRecruitReport($request, $isschool = 0)
    {
//        return false;
//        die;
        $datawhere = "s.company_id = '{$request['company_id']}' AND s.school_isclose = '0' AND s.school_istest <> '1'";
        if (isset($request['school_id']) && $request['school_id'] !== '' && $isschool != '1') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        if (isset($request['school_type']) && $request['school_type'] !== '') {
            $datawhere .= " and s.school_type='{$request['school_type']}'";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] != '1') {
            $postbeOne = $this->AnalyzeControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $today = date("Y-m-d", strtotime($request['fixedtime']));
            $todaytime = strtotime($request['fixedtime']) + 24 * 60 * 60 - 1;

            //上月初  上月末
//            $onemonth_begin_time = date('Y-m-01 00:00:00',strtotime('-1 month',strtotime($request['fixedtime'])));
            $onemonth_begin_time = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m", strtotime($request['fixedtime'])) - 1, 1, date("Y", strtotime($request['fixedtime']))));
            $onemonth_end_time = date("Y-m-d 23:59:59", strtotime(-date('d', strtotime($request['fixedtime'])) . 'day', strtotime($request['fixedtime'])));
            //上上月初  上上月末
            $twomonth_begin_time = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m", strtotime($request['fixedtime'])) - 2, 1, date("Y", strtotime($request['fixedtime']))));
            $twomonth_end_time = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m", strtotime($request['fixedtime'])) - 1, 0, date("Y", strtotime($request['fixedtime']))));
            //本月初  本月末
            $monthstart_time = date('Y-m-01', strtotime($request['fixedtime']));
            $mdays = date('t', strtotime($request['fixedtime']));
            $monthend_time = date('Y-m-' . $mdays, strtotime($request['fixedtime']));

            //本周一
            $weekFirst_start_time = date('Y-m-d', (strtotime($request['fixedtime']) - ((date('w', strtotime($request['fixedtime'])) == 0 ? 7 : date('w', strtotime($request['fixedtime']))) - 1) * 24 * 3600));

            //上周一  上周日
            $last_week_start_time = date('Y-m-d', strtotime('-1 monday', strtotime('-6 day', strtotime($request['fixedtime']))));
            $last_week_end_time = date('Y-m-d', strtotime('-1 sunday', strtotime($request['fixedtime'])));

            $month = date("n", strtotime($request['fixedtime']));
        } else {
            $today = date("Y-m-d");
            $todaytime = time();

            //上月初  上月末
//            $onemonth_begin_time = date('Y-m-01 00:00:00',strtotime('-1 month'));
            $onemonth_begin_time = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m") - 1, 1, date("Y")));
            $onemonth_end_time = date("Y-m-d 23:59:59", strtotime(-date('d') . 'day'));
            //上上月初  上上月末
            $twomonth_begin_time = date("Y-m-d H:i:s", mktime(0, 0, 0, date("m") - 2, 1, date("Y")));
            $twomonth_end_time = date("Y-m-d H:i:s", mktime(23, 59, 59, date("m") - 1, 0, date("Y")));

            //本月初  本月末
            $monthstart_time = date('Y-m-01', time());
            $mdays = date('t', time());
            $monthend_time = date('Y-m-' . $mdays, time());

            //本周一
            $weekFirst_start_time = date('Y-m-d', (time() - ((date('w') == 0 ? 7 : date('w')) - 1) * 24 * 3600));

            //上周一  上周日
            $last_week_start_time = date('Y-m-d', strtotime('-1 monday', strtotime('-6 day', time())));
            $last_week_end_time = date('Y-m-d', strtotime('-1 sunday', time()));

            $month = date("n");
        }

        $thisWeek = GetWeekAll($today);
        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));
        $quarter_startdate = date("Y-m-d", strtotime("-90 days", strtotime($today)));
        $sixmoth_startdate = date("Y-m-d", strtotime("-180 days", strtotime($today)));

        //当前时间
        $nowime = date("Y-m-d");

        $week_starttime = strtotime($week_startdate);
        $month_starttime = strtotime($month_startdate);
        $preyear_starttime = strtotime('2021-01-01');
        $preyear_endtime = strtotime('2021-12-31 23:59:59');
        $nowyear_starttime = strtotime(date("Y-1-1"));
        $onemonth_starttime = strtotime($onemonth_begin_time);//上月初
        $twomonth_starttime = strtotime($twomonth_begin_time);//上上月初
        $onemonth_endtime = strtotime($onemonth_end_time);//上月末
        $twomonth_endtime = strtotime($twomonth_end_time);//上上月末

        $this_week_starttime = strtotime($weekFirst_start_time);//上周一
        $last_week_starttime = strtotime($last_week_start_time);//上周一
        $last_week_endtime = strtotime($last_week_end_time);//上周日

        $quarter_starttime = strtotime($quarter_startdate);

        $thisMonth = GetTheMonth($today);
        $maoweek_starttime = strtotime($thisWeek['nowweek_start']);//周一
        $maoweek_endtime = strtotime($thisWeek['nowweek_end']) + 24 * 60 * 60 - 1;//周日
        $maomonth_starttime = strtotime($thisMonth[0]);//月初
        $maomonth_endtime = strtotime($thisMonth[1]) + 24 * 60 * 60 - 1;//月末
        if (strpos("1,2,3", $month) !== false) {//自然季
            $season_starttime = $nowyear_starttime;
            $season_endtime = strtotime("+3 month", $season_starttime) - 1;
        } elseif (strpos("4,5,6", $month) !== false) {
            $season_starttime = strtotime(date("Y-4-1"));
            $season_endtime = strtotime("+3 month", $season_starttime) - 1;
        } elseif (strpos("7,8,9", $month) !== false) {
            $season_starttime = strtotime(date("Y-7-1"));
            $season_endtime = strtotime("+3 month", $season_starttime) - 1;
        } else {
            $season_starttime = strtotime(date("Y-10-1"));
            $season_endtime = strtotime("+3 month", $season_starttime) - 1;
        }
        $nowyear_endtime = strtotime(date("Y-12-31")) + 24 * 60 * 60 - 1;//年末

        $register_absentee_where = "si.school_id = s.school_id AND si.studyinfo_status = 1 AND si.studyinfo_date = '{$today}'";
        $register_feereading_where = "d.class_id = c.class_id AND c.course_id = r.course_id AND d.school_id = ac.school_id AND d.student_id = ac.student_id AND c.course_id = ac.course_id AND d.school_id = s.school_id AND ac.coursebalance_figure = '0' AND d.study_beginday <= '{$nowime}' AND d.study_endday >= '{$nowime}'";
        $pre_year_where = "r.pay_successtime >= '{$preyear_starttime}' and r.pay_successtime <='{$preyear_endtime}' and r.info_status =1";
        $now_year_where = "r.pay_successtime >= '{$nowyear_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_week_where = "r.pay_successtime >= '{$week_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_month_where = "r.pay_successtime >= '{$month_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_one_month_where = "r.pay_successtime >= '{$onemonth_starttime}' and r.pay_successtime <='{$onemonth_endtime}' and r.info_status =1";
        $register_two_month_where = "r.pay_successtime >= '{$twomonth_starttime}' and r.pay_successtime <='{$twomonth_endtime}' and r.info_status =1";
        $mao_week_where = "t.client_createtime >='{$maoweek_starttime}' and t.client_createtime <='{$maoweek_endtime}'";
        $mao_month_where = "t.client_createtime >='{$maomonth_starttime}' and t.client_createtime <='{$maomonth_endtime}'";

        $client_week_where = "t.client_createtime >='{$week_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_month_where = "t.client_createtime >='{$month_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_quarter_where = "t.client_createtime >='{$quarter_starttime}' and t.client_createtime <='{$todaytime}'";

        $client_week_where_aud = "UNIX_TIMESTAMP(a.audition_visittime) >='{$week_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$todaytime}'";//后边 邀约时间
        $client_week_where_invite = "UNIX_TIMESTAMP(a.invite_visittime) >='{$week_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$todaytime}'";//后边 邀约时间
//        $client_week_where_invite_stu = "UNIX_TIMESTAMP(a.visittime) >='{$week_starttime}' and UNIX_TIMESTAMP(a.visittime) <='{$todaytime}'";//后边 邀约时间
        $client_week_where_aud_stu = "UNIX_TIMESTAMP(a.audition_visittime) >='{$week_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$todaytime}'";//后边 邀约时间 ---
        $client_week_where_invite_stu = "UNIX_TIMESTAMP(a.invite_visittime) >='{$week_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$todaytime}'";//后边 邀约时间 ---

        $invitelog_lastweek_where_aud = "UNIX_TIMESTAMP(a.audition_visittime) >='{$last_week_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$last_week_endtime}'";//后边 邀约时间
        $invitelog_lastweek_where_invite = "UNIX_TIMESTAMP(a.invite_visittime) >='{$last_week_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$last_week_endtime}'";//后边 邀约时间
//        $invitelog_lastweek_where_invite_stu = "UNIX_TIMESTAMP(a.visittime) >='{$last_week_starttime}' and UNIX_TIMESTAMP(a.visittime) <='{$last_week_endtime}'";//后边 邀约时间
        $invitelog_lastweek_where_aud_stu = "UNIX_TIMESTAMP(a.audition_visittime) >='{$last_week_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$last_week_endtime}'";//后边 邀约时间 ---
        $invitelog_lastweek_where_invite_stu = "UNIX_TIMESTAMP(a.invite_visittime) >='{$last_week_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$last_week_endtime}'";//后边 邀约时间 ---

        $invitelog_thisweek_where_aud = "UNIX_TIMESTAMP(a.audition_visittime) >='{$this_week_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$todaytime}'";//后边 邀约时间
        $invitelog_thisweek_where_invite = "UNIX_TIMESTAMP(a.invite_visittime) >='{$this_week_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$todaytime}'";//后边 邀约时间
//        $invitelog_thisweek_where_invite_stu = "UNIX_TIMESTAMP(a.visittime) >='{$this_week_starttime}' and UNIX_TIMESTAMP(a.visittime) <='{$todaytime}'";//后边 邀约时间
        $invitelog_thisweek_where_aud_stu = "UNIX_TIMESTAMP(a.audition_visittime) >='{$this_week_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$todaytime}'";//后边 邀约时间 ---
        $invitelog_thisweek_where_invite_stu = "UNIX_TIMESTAMP(a.invite_visittime) >='{$this_week_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$todaytime}'";//后边 邀约时间 ---


        $client_month_where_aud = "UNIX_TIMESTAMP(a.audition_visittime) >='{$month_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$todaytime}'";//后边 邀约时间
        $client_month_where_invite = "UNIX_TIMESTAMP(a.invite_visittime) >='{$month_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$todaytime}'";//后边 邀约时间
//        $client_month_where_invite_stu = "UNIX_TIMESTAMP(a.visittime) >='{$month_starttime}' and UNIX_TIMESTAMP(a.visittime) <='{$todaytime}'";//后边 邀约时间
        $client_month_where_aud_stu = "UNIX_TIMESTAMP(a.audition_visittime) >='{$month_starttime}' and UNIX_TIMESTAMP(a.audition_visittime) <='{$todaytime}'";//后边 邀约时间 ---
        $client_month_where_invite_stu = "UNIX_TIMESTAMP(a.invite_visittime) >='{$month_starttime}' and UNIX_TIMESTAMP(a.invite_visittime) <='{$todaytime}'";//后边 邀约时间 ---


//        $registerplus_month_where = "re.pay_successtime >= '{$maomonth_starttime}' and re.pay_successtime <='{$maomonth_endtime}' and re.info_status =1";//园内招
//        $registerplus_season_where = "re.pay_successtime >= '{$season_starttime}' and re.pay_successtime <='{$season_endtime}' and re.info_status =1";//园内招
//        $registerplus_year_where = "re.pay_successtime >= '{$nowyear_starttime}' and re.pay_successtime <='{$nowyear_endtime}' and re.info_status =1";//园内招
        $registerplus_month_where = "re.pay_successtime >= '{$month_starttime}' and re.pay_successtime <='{$todaytime}' and re.info_status =1";//园内招
        $registerplus_season_where = "re.pay_successtime >= '{$quarter_starttime}' and re.pay_successtime <='{$todaytime}' and re.info_status =1";//园内招
        $registerplus_year_where = "re.pay_successtime >= '{$nowyear_starttime}' and re.pay_successtime <='{$nowyear_endtime}' and re.info_status =1";//园内招

        $all_client_where = "cs.client_id = t.client_id and cs.school_id = s.school_id and t.company_id = s.company_id";
        $invitelog_lastweek_where = "t.client_createtime >='{$last_week_starttime}' and t.client_createtime <='{$last_week_endtime}'";
        $invitelog_thisweek_where = "t.client_createtime >='{$this_week_starttime}' and t.client_createtime <='{$todaytime}'";

        $track_week_where = "k.track_createtime >='{$week_starttime}' and k.track_createtime <='{$todaytime}' and k.track_isactive =1";
        $track_month_where = "k.track_createtime >='{$month_starttime}' and k.track_createtime <='{$todaytime}' and k.track_isactive =1";


        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $register_absentee_where .= " and si.coursetype_id='{$request['coursetype_id']}'";
            $register_feereading_where .= " and r.coursetype_id='{$request['coursetype_id']}'";
            $pre_year_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $now_year_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_week_where .= " and r.coursetype_id='{$request['coursetype_id']}'";
            $register_month_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_one_month_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_two_month_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $mao_week_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $mao_month_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $client_week_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $client_month_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $client_quarter_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $all_client_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $registerplus_month_where .= " and re.coursetype_id='{$request['coursetype_id']}' ";
            $registerplus_season_where .= " and re.coursetype_id='{$request['coursetype_id']}' ";
            $registerplus_year_where .= " and re.coursetype_id='{$request['coursetype_id']}' ";
            $invitelog_lastweek_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $invitelog_thisweek_where .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";

            $client_week_where_aud .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间
            $client_week_where_invite .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间

            $client_week_where_aud_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----
            $client_week_where_invite_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间----

            $invitelog_lastweek_where_aud .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间
            $invitelog_lastweek_where_invite .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间

            $invitelog_lastweek_where_aud_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----
            $invitelog_lastweek_where_invite_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----


            $invitelog_thisweek_where_aud .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间
            $invitelog_thisweek_where_invite .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间

            $invitelog_thisweek_where_aud_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----
            $invitelog_thisweek_where_invite_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----

            $client_month_where_aud .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $client_month_where_invite .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";

            $client_month_where_aud_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----
            $client_month_where_invite_stu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";//后边 邀约时间  ----

//            $track_month_where .= " AND k.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            $track_week_where .= " AND k.coursetype_id = '{$request['coursetype_id']}' ";
            $track_month_where .= " AND k.coursetype_id = '{$request['coursetype_id']}' ";
        }

        //20250723之前注释的
//        (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS school_districtname,
//        (SELECT count(si.studyinfo_id) FROM temp_smc_student_studyinfo AS si WHERE {$register_absentee_where} AND si.studyinfo_type = 1) AS 'absenteenums',
//        (SELECT count(si.studyinfo_id) FROM temp_smc_student_studyinfo AS si  WHERE {$register_absentee_where} AND si.studyinfo_type = 2) AS 'readingnums',

        //20250724 注释的
//        (SELECT sum(si.student_num) FROM temp_smc_school_studyinfo AS si WHERE {$register_absentee_where} AND si.studyinfo_type = 1) AS 'absenteenums',
//        (SELECT sum(si.student_num) FROM temp_smc_school_studyinfo AS si  WHERE {$register_absentee_where} AND si.studyinfo_type = 2) AS 'readingnums',
//        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id  and cs.is_enterstatus = '1' and  {$client_month_where} and ( (h.channel_way =1 and h.channel_isbazaar = 1) or h.channel_way =0)  and t.client_intention_level>=3) as client_upunder_monthnum,
//        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_quarter_where} and t.client_intention_level>=3) as client_up_quarternum,

        $sql = "select s.school_id,s.school_shortname,s.school_branch,s.school_openclass,s.school_foundtime,s.school_tagbak,
         (SELECT d.region_name FROM smc_code_region AS d WHERE d.region_id = s.school_province) AS school_districtname,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} ) as prelog_year_num, 
         
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$now_year_where} ) as nowlog_year_num, 
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_week_where} ) as positivelog_week_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where} ) as positivelog_month_num,   
          
         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id  and {$mao_month_where}  ) as mao_month_client_num,
          
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and cs.is_enterstatus = '1'  and  {$client_month_where} and h.channel_way =1 and h.channel_isbazaar = 1 and t.client_intention_level>=3) as client_under_monthnum, 
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id  and cs.is_enterstatus = '1' and  {$client_month_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_monthnum, 

        (SELECT count(t.client_id) FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_month_where} AND h.channel_isreferral = '1'  ) as client_referral_monthnum,
        (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where}  and  exists (select 1 FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = r.school_id AND h.channel_isreferral = '1' and t.client_id = r.from_client_id  limit 0,1) ) as client_referral_regmonthnum, 
        
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_distributionstatus = 0  and (t.client_tracestatus = 0 or t.client_tracestatus = 1)  and t.client_isgross = '0'  AND cs.is_enterstatus = 1 ) as client_noallot_num
        
        FROM smc_school as s where {$datawhere}
        order by field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";

//        (select count(distinct t.client_id) FROM crm_client AS t, smc_student_registerplus AS re where {$registerplus_month_where} AND t.client_id = re.from_client_id AND re.school_id = s.school_id AND re.info_first_in_company = '1' AND t.client_source = '园展校') as register_client_monthnum,
//        (select count(distinct t.client_id) FROM smc_student_registerinfo as re,smc_student AS s,crm_client AS t where {$registerplus_month_where} AND re.student_id = s.student_id AND s.from_client_id = t.client_id AND re.school_id = s.school_id AND re.info_type = 0 AND t.client_source = '园展校' ) as info_month_num,
//        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_tracestatus IN (0,1,2,3) and t.client_isgross = '0' and t.client_intention_level > 2 and cs.is_enterstatus =1  and t.client_distributionstatus = 1) as client_all_num,
//        (select count(DISTINCT k.student_id) from crm_student_track as  k where k.school_id=s.school_id and {$track_month_where} ) as track_client_num_two
//        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_month_where} ) as track_client_num ,
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$client_month_where_aud} ) +  (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$client_month_where_invite} )) as invitelog_month_num ,
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$client_month_where_aud_stu} ) +  (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$client_month_where_invite_stu} )) as invitelog_month_num_two ,
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$client_month_where_aud} ) + (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$client_month_where_invite} )) as invitelog_month_isvisitnum ,
//        ,
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$client_month_where_aud_stu} ) + (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$client_month_where_invite_stu} )) as invitelog_month_isvisitnum_two ,
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $this->error = true;
            $this->errortip = "暂不支持导出";
            return false;
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_districtname'] = $dateexcelvar['school_districtname'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_tagbak'] = $dateexcelvar['school_tagbak'];
//                    $datearray['absenteenums'] = $dateexcelvar['absenteenums'];
//                    $datearray['readingnums'] = $dateexcelvar['readingnums'];
                    $datearray['prelog_year_num'] = $dateexcelvar['prelog_year_num'];

                    $datearray['nowlog_year_num'] = $dateexcelvar['nowlog_year_num'];
                    $datearray['positivelog_week_num'] = $dateexcelvar['positivelog_week_num'];
                    $datearray['positivelog_month_num'] = $dateexcelvar['positivelog_month_num'];

                    $datearray['mao_month_client_num'] = $dateexcelvar['mao_month_client_num'];

                    $datearray['client_under_monthnum'] = $dateexcelvar['client_under_monthnum'];
                    $datearray['client_up_monthnum'] = $dateexcelvar['client_up_monthnum'];
//                    $datearray['client_upunder_monthnum'] = $dateexcelvar['client_upunder_monthnum'];

//                    $datearray['client_up_quarternum'] = $dateexcelvar['client_up_quarternum'];

                    $datearray['client_referral_monthnum'] = $dateexcelvar['client_referral_monthnum'];
                    $datearray['client_referral_regmonthnum'] = $dateexcelvar['client_referral_regmonthnum'];

//                    $datearray['register_client_monthnum'] = $dateexcelvar['register_client_monthnum'];
//                    $datearray['info_month_num'] = $dateexcelvar['info_month_num'];

//                    $datearray['client_all_num'] = $dateexcelvar['client_all_num'];
                    $datearray['client_noallot_num'] = $dateexcelvar['client_noallot_num'];


//                    $datearray['track_week_client_num'] = $dateexcelvar['track_week_client_num'] + $dateexcelvar['track_week_client_num_two'];//---
//                    $datearray['track_week_tracknum'] = $dateexcelvar['track_week_tracknum'] + $dateexcelvar['track_week_tracknum_two'];//---

//                    $datearray['invitelog_week_num'] = $dateexcelvar['invitelog_week_num']+$dateexcelvar['invitelog_week_num_two'];//---

//                    $datearray['invitelog_week_numonce'] = $dateexcelvar['invitelog_week_numonce']+$dateexcelvar['invitelog_week_numonce_two'];//---
//                    $datearray['track_client_num'] = $dateexcelvar['track_client_num'] + $dateexcelvar['track_client_num_two'];//---
//                    $datearray['track_tracknum'] = $dateexcelvar['track_tracknum'] + $dateexcelvar['track_tracknum_two'];//---
//                    $datearray['invitelog_lastweek_num'] = $dateexcelvar['invitelog_lastweek_num'] + $dateexcelvar['invitelog_lastweek_num_two'];//---
//                    $datearray['invitelog_lastweek_isvisitnum'] = $dateexcelvar['invitelog_lastweek_isvisitnum'] + $dateexcelvar['invitelog_lastweek_isvisitnum_two'];//---

//                    $datearray['invitelog_thisweek_num'] = $dateexcelvar['invitelog_thisweek_num'] + $dateexcelvar['invitelog_thisweek_num_two'];//---
//                    $datearray['invitelog_thisweek_isvisitnum'] = $dateexcelvar['invitelog_thisweek_isvisitnum'] + $dateexcelvar['invitelog_thisweek_isvisitnum_two'];//---
//                    $datearray['invitelog_month_num'] = $dateexcelvar['invitelog_month_num'] + $dateexcelvar['invitelog_month_num_two'];//---
//                    $datearray['invitelog_month_isvisitnum'] = $dateexcelvar['invitelog_month_isvisitnum'] + $dateexcelvar['invitelog_month_isvisitnum_two'];//---

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("地区", "校区名称", "督导区",
                "报名/去年",
                "报名/当年", "报名/周", "报名/月",
                "新增总毛名单/月",
                "新增陆军有效名单/月",  "新增空军有效名单/月",
                "新增转介绍名单/月", "转介绍名单报名/月",
                "待分配名单数" ));

            $excelfileds = array("school_districtname", "school_shortname", "school_tagbak",
                'prelog_year_num',
                "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
                "mao_month_client_num",
                "client_under_monthnum", "client_up_monthnum",
                "client_referral_monthnum", "client_referral_regmonthnum",
                "client_noallot_num" );
//            $excelheader = $this->LgArraySwitch(array("地区", "校区名称", "督导区", "在籍", "在读",
//                "报名/去年",
//                "报名/当年", "报名/周", "报名/月",
//                "新增总毛名单/月",
//                "新增陆军有效名单/月",  "新增空军有效名单/月", "新增有效名单/月（空+陆）", "新增有效名单/季",
//                "新增转介绍名单/月", "转介绍名单报名/月",
//
//                "园内招（名单/月）", "园内招（报名/月）",
//                "可追踪有效名单", "待分配名单数",
//                "追踪人数/月",
//                "邀约诺访/月", "邀约到访/月"));
//
//            $excelfileds = array("school_districtname", "school_shortname", "school_tagbak", "absenteenums", "readingnums",
//                'prelog_year_num',
//                "nowlog_year_num", "positivelog_week_num", "positivelog_month_num",
//                "mao_month_client_num",
//                 "client_under_monthnum", "client_up_monthnum", "client_upunder_monthnum", "client_up_quarternum",
//                "client_referral_monthnum", "client_referral_regmonthnum",
//
//                "register_client_monthnum",  "info_month_num",
//                "client_all_num", "client_noallot_num",
//                "track_client_num",
//                "invitelog_month_num", "invitelog_month_isvisitnum" );

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校区招生追踪大表-截止日期{$today}.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
            $allNum = $this->AnalyzeControl->selectOne("select count(s.school_id)  as  allnum from smc_school as s where {$datawhere}");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            } else {
                foreach ($dataList as $key => $dataOne) {


//                    $dataList[$key]['track_week_client_num'] = $dataOne['track_week_client_num'] + $dataOne['track_week_client_num_two'];//---
//                    $dataList[$key]['track_week_tracknum'] = $dataOne['track_week_tracknum'] + $dataOne['track_week_tracknum_two'];//---
//                    $dataList[$key]['invitelog_week_num'] = $dataOne['invitelog_week_num']+$dataOne['invitelog_week_num_two'];//---
//
//                    $dataList[$key]['invitelog_week_numonce'] = $dataOne['invitelog_week_numonce']+$dataOne['invitelog_week_numonce_two'];//---

//                    $dataList[$key]['track_client_num'] = $dataOne['track_client_num'] + $dataOne['track_client_num_two'];//---
//                    $dataList[$key]['track_tracknum'] = $dataOne['track_tracknum'] + $dataOne['track_tracknum_two'];//---
//                    $dataList[$key]['invitelog_lastweek_num'] = $dataOne['invitelog_lastweek_num'] + $dataOne['invitelog_lastweek_num_two'];//---
//                    $dataList[$key]['invitelog_lastweek_isvisitnum'] = $dataOne['invitelog_lastweek_isvisitnum'] + $dataOne['invitelog_lastweek_isvisitnum_two'];//---

//                    $dataList[$key]['invitelog_thisweek_num'] = $dataOne['invitelog_thisweek_num'] + $dataOne['invitelog_thisweek_num_two'];//---
//                    $dataList[$key]['invitelog_thisweek_isvisitnum'] = $dataOne['invitelog_thisweek_isvisitnum'] + $dataOne['invitelog_thisweek_isvisitnum_two'];//---
//                    $dataList[$key]['invitelog_month_num'] = $dataOne['invitelog_month_num'] + $dataOne['invitelog_month_num_two'];//---
//                    $dataList[$key]['invitelog_month_isvisitnum'] = $dataOne['invitelog_month_isvisitnum'] + $dataOne['invitelog_month_isvisitnum_two'];//---


//                    $dataList[$key]['outmonth_num'] = $dataOne['positivelog_month_num'] - $dataOne['inmonth_num'];
                    if (!$dataOne['absenteenums']) {
                        $dataList[$key]['absenteenums'] = '--';
                    }
                    if (!$dataOne['readingnums']) {
                        $dataList[$key]['readingnums'] = '--';
                    }
                }
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = $allNum['allnum'];
            return $result;
        }
    }
    // sql 备份 20230724 注释数据字段
//$sql = "select s.school_id,s.school_shortname,s.school_branch,s.school_openclass,s.school_foundtime,s.school_tagbak,
//         (SELECT d.region_name FROM smc_code_region AS d WHERE d.region_id = s.school_province) AS school_districtname,
//         (SELECT sum(si.student_num) FROM temp_smc_school_studyinfo AS si WHERE {$register_absentee_where} AND si.studyinfo_type = 1) AS 'absenteenums',
//         (SELECT sum(si.student_num) FROM temp_smc_school_studyinfo AS si  WHERE {$register_absentee_where} AND si.studyinfo_type = 2) AS 'readingnums',
//         (SELECT COUNT(DISTINCT d.student_id) FROM smc_student_study as d,smc_class as c,smc_course as r,smc_student_coursebalance as ac
//         WHERE ({$register_feereading_where})) as feereadingnums,
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} ) as prelog_year_num,
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} AND r.student_id IN (SELECT p.student_id FROM smc_student_guildpolicy as p) ) as prelog_caseyear_num,
//
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$now_year_where} ) as nowlog_year_num,
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_week_where} ) as positivelog_week_num,
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where} ) as positivelog_month_num,
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_one_month_where} ) as positivelog_one_month_num,
//         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_two_month_where} ) as positivelog_two_month_num,
//
//         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id  and {$mao_week_where}) as mao_week_client_num,
//         (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id  and {$mao_month_where}  ) as mao_month_client_num,
//
//        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and cs.is_enterstatus = '1'  and  {$client_week_where} and h.channel_way =1 and h.channel_isbazaar = 1 and t.client_intention_level>=3) as client_under_weeknum,
//        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and cs.is_enterstatus = '1'  and  {$client_month_where} and h.channel_way =1 and h.channel_isbazaar = 1 and t.client_intention_level>=3) as client_under_monthnum,
//        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and cs.is_enterstatus = '1'  and  {$client_week_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_weeknum,
//        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id  and cs.is_enterstatus = '1' and  {$client_month_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_monthnum,
//        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id  and cs.is_enterstatus = '1' and  {$client_month_where} and ( (h.channel_way =1 and h.channel_isbazaar = 1) or h.channel_way =0)  and t.client_intention_level>=3) as client_upunder_monthnum,
//        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_quarter_where} and t.client_intention_level>=3) as client_up_quarternum,
//
//        (SELECT count(t.client_id) FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_week_where} AND h.channel_isreferral = '1'  ) as client_referral_weeknum,
//        (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_week_where}  and  exists (select 1 FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = r.school_id AND h.channel_isreferral = '1' and t.client_id = r.from_client_id  limit 0,1) ) as client_referral_regweeknum,
//        (SELECT count(t.client_id) FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_month_where} AND h.channel_isreferral = '1'  ) as client_referral_monthnum,
//        (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where}  and  exists (select 1 FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = r.school_id AND h.channel_isreferral = '1' and t.client_id = r.from_client_id  limit 0,1) ) as client_referral_regmonthnum,
//        (SELECT count(t.client_id) FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_quarter_where} AND h.channel_isreferral = '1'  ) as client_referral_quarternum,
//
//        (select count(distinct t.client_id) FROM crm_client AS t, smc_student_registerplus AS re where {$registerplus_month_where} AND t.client_id = re.from_client_id AND re.school_id = s.school_id AND re.info_first_in_company = '1' AND t.client_source = '园展校') as register_client_monthnum,
//        (select count(distinct t.client_id) FROM crm_client AS t, smc_student_registerplus AS re where {$registerplus_season_where} AND t.client_id = re.from_client_id AND re.school_id = s.school_id AND re.info_first_in_company = '1' AND t.client_source = '园展校') as register_client_seasonnum,
//        (select count(distinct t.client_id) FROM crm_client AS t, smc_student_registerplus AS re where {$registerplus_year_where} AND t.client_id = re.from_client_id AND re.school_id = s.school_id AND re.info_first_in_company = '1' AND t.client_source = '园展校') as register_client_yearnum,
//        (select count(distinct t.client_id) FROM smc_student_registerinfo as re,smc_student AS s,crm_client AS t where {$registerplus_month_where} AND re.student_id = s.student_id AND s.from_client_id = t.client_id AND re.school_id = s.school_id AND re.info_type = 0 AND t.client_source = '园展校' ) as info_month_num,
//        (select count(distinct t.client_id) FROM smc_student_registerinfo as re,smc_student AS s,crm_client AS t where {$registerplus_season_where} AND re.student_id = s.student_id AND s.from_client_id = t.client_id AND re.school_id = s.school_id AND re.info_type = 0 AND t.client_source = '园展校' ) as info_season_num,
//        (select count(distinct t.client_id) FROM smc_student_registerinfo as re,smc_student AS s,crm_client AS t where {$registerplus_year_where} AND re.student_id = s.student_id AND s.from_client_id = t.client_id AND re.school_id = s.school_id AND re.info_type = 0 AND t.client_source = '园展校' ) as info_year_num,
//
//        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_tracestatus IN (0,1,2,3) and t.client_isgross = '0' and t.client_intention_level > 2 and cs.is_enterstatus =1  and t.client_distributionstatus = 1) as client_all_num,
//        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_distributionstatus = 0  and (t.client_tracestatus = 0 or t.client_tracestatus = 1)  and t.client_isgross = '0'  AND cs.is_enterstatus = 1 ) as client_noallot_num,
//
//
//
//
//
//
//
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$invitelog_lastweek_where_aud} ) +  (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$invitelog_lastweek_where_invite} )) as invitelog_lastweek_num ,
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$invitelog_lastweek_where_aud} ) + (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$invitelog_lastweek_where_invite} )) as invitelog_lastweek_isvisitnum ,
//
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$invitelog_lastweek_where_aud_stu} ) +  (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$invitelog_lastweek_where_invite_stu} )) as invitelog_lastweek_num_two ,
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$invitelog_lastweek_where_aud_stu} ) + (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$invitelog_lastweek_where_invite_stu} )) as invitelog_lastweek_isvisitnum_two ,
//
//
//
//
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$invitelog_thisweek_where_aud} ) +  (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$invitelog_thisweek_where_invite} )) as invitelog_thisweek_num ,
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$invitelog_thisweek_where_aud} ) + (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$invitelog_thisweek_where_invite} )) as invitelog_thisweek_isvisitnum ,
//
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$invitelog_thisweek_where_aud_stu} ) +  (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$invitelog_thisweek_where_invite_stu} )) as invitelog_thisweek_num_two ,
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$invitelog_thisweek_where_aud_stu} ) + (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$invitelog_thisweek_where_invite_stu} )) as invitelog_thisweek_isvisitnum_two ,
//
//
//
//
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$client_month_where_aud} ) +  (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$client_month_where_invite} )) as invitelog_month_num ,
//        ((select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$client_month_where_aud} ) + (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$client_month_where_invite} )) as invitelog_month_isvisitnum ,
//
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and {$client_month_where_aud_stu} ) +  (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3  and {$client_month_where_invite_stu} )) as invitelog_month_num_two ,
//        ((select count(a.student_id) from  crm_student as t,crm_student_audition as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.audition_isvisit = 1 and {$client_month_where_aud_stu} ) + (select count(a.student_id) from  crm_student as t,crm_student_invite as a where t.student_id = a.student_id and a.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre <> 3 and a.invite_isvisit = 1 and {$client_month_where_invite_stu} )) as invitelog_month_isvisitnum_two ,
//
//
//
//
//        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_week_where} ) as track_week_client_num ,
//        (select count(DISTINCT k.student_id) from crm_student_track as  k where k.school_id=s.school_id and {$track_week_where} ) as track_week_client_num_two ,
//        (select count(k.track_id) from crm_client_track as  k where k.school_id=s.school_id and  {$track_week_where}) as track_week_tracknum ,
//        (select count(k.track_id) from crm_student_track as  k where k.school_id=s.school_id and  {$track_week_where}) as track_week_tracknum_two ,
//
//        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_month_where} ) as track_client_num ,
//        (select count(DISTINCT k.student_id) from crm_student_track as  k where k.school_id=s.school_id and {$track_month_where} ) as track_client_num_two ,
//        (select count(k.track_id) from crm_client_track as  k where k.school_id=s.school_id and  {$track_month_where}) as track_tracknum ,
//        (select count(k.track_id) from crm_student_track as  k where k.school_id=s.school_id and  {$track_month_where}) as track_tracknum_two ,
//
//        (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre=3 and a.invite_isvisit = 1 and {$client_week_where_invite} ) as invite_week_num,
//        (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre=3 and a.invite_isvisit = 1 and {$client_month_where_invite} ) as invite_month_num,
//
//        (select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and a.audition_genre=2 and {$client_week_where_aud} ) as audition_week_num,
//        (select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and a.audition_genre=2 and {$client_month_where_aud} ) as audition_month_num
//        FROM smc_school as s where {$datawhere}
//        order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";

    //备份上边之前写的数据 sql
    //        $sql = "select s.school_id,s.school_shortname,s.school_branch,s.school_openclass,s.school_foundtime,s.school_tagbak,
    //         (SELECT d.region_name FROM smc_code_region AS d WHERE d.region_id = s.school_province) AS school_districtname,
    //         (SELECT count(si.studyinfo_id) FROM temp_smc_student_studyinfo AS si WHERE {$register_absentee_where} AND si.studyinfo_type = 1) AS 'absenteenums',
    //         (SELECT count(si.studyinfo_id) FROM temp_smc_student_studyinfo AS si  WHERE {$register_absentee_where} AND si.studyinfo_type = 2) AS 'readingnums',
    //         (SELECT COUNT(DISTINCT d.student_id) FROM smc_student_study as d,smc_class as c,smc_course as r,smc_student_coursebalance as ac
    //         WHERE ({$register_feereading_where})) as feereadingnums,
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} ) as prelog_year_num,
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$pre_year_where} AND r.student_id IN (SELECT p.student_id FROM smc_student_guildpolicy as p) ) as prelog_caseyear_num,
    //
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$now_year_where} ) as nowlog_year_num,
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_week_where} ) as positivelog_week_num,
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where} ) as positivelog_month_num,
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_one_month_where} ) as positivelog_one_month_num,
    //         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_two_month_where} ) as positivelog_two_month_num,
    //        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_week_where} and h.channel_way =1 and t.client_intention_level>=3) as client_under_weeknum,
    //        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and h.channel_way =1 and t.client_intention_level>=3) as client_under_monthnum,
    //        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_week_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_weeknum,
    //        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_monthnum,
    //        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and (h.channel_way =1 or h.channel_way =0)  and t.client_intention_level>=3) as client_upunder_monthnum,
    //        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_quarter_where} and t.client_intention_level>=3) as client_up_quarternum,
    //        (SELECT count(t.client_id) FROM crm_client AS t, crm_client_schoolenter AS cs,crm_code_channel AS h where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id and {$client_month_where} AND h.channel_isreferral = '1'  ) as client_referral_monthnum,
    //        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_tracestatus IN (0,1,2,3,4,'-1') and t.client_isgross = '0') as client_all_num,
    //        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where {$all_client_where} and t.client_distributionstatus = 0  and client_tracestatus = 0  and t.client_isgross = '0') as client_noallot_num,
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 )) and g.isvisit = 1 and {$client_week_where} ) as invitelog_week_num,
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 ))  and g.isvisit = 1 and {$client_week_where} ) as invitelog_week_numonce,
    //
    //        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_month_where} ) as track_client_num ,
    //        (select count(k.track_id) from crm_client_track as  k where k.school_id=s.school_id and  {$track_month_where}) as track_tracknum ,
    //
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 )) and {$invitelog_lastweek_where} ) as invitelog_lastweek_num,
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 ))  and g.isvisit = 1 and {$invitelog_lastweek_where} ) as invitelog_lastweek_isvisitnum,
    //
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 )) and {$invitelog_thisweek_where} ) as invitelog_thisweek_num,
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 ))  and g.isvisit = 1 and {$invitelog_thisweek_where} ) as invitelog_thisweek_isvisitnum,
    //
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 )) and {$client_month_where} ) as invitelog_month_num,
    //        (select count(g.client_id) from  crm_client as t,view_crm_invitelog as g where t.client_id = g.client_id and t.company_id = '{$request['company_id']}' and g.school_id=s.school_id and (g.visitType = 'a' or (g.visitType = 'i' and g.genre<>3 ))  and g.isvisit = 1 and {$client_month_where} ) as invitelog_month_isvisitnum,
    //
    //        (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre=3 and a.invite_isvisit = 1 and {$client_week_where} ) as invite_week_num,
    //        (select count(a.client_id) from  crm_client as t,crm_client_invite as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id and a.invite_genre=3 and a.invite_isvisit = 1 and {$client_month_where} ) as invite_month_num,
    //
    //        (select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and a.audition_genre=2 and {$client_week_where} ) as audition_week_num,
    //        (select count(a.client_id) from  crm_client as t,crm_client_audition as a where t.client_id = a.client_id and t.company_id = '{$request['company_id']}' and a.school_id=s.school_id  and a.audition_genre=2 and {$client_month_where} ) as audition_month_num
    //        FROM smc_school as s where {$datawhere}
    //        order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";

    //教师业绩查询接口
    function getClientMarketerReport($request)
    {
        if (!isset($request['school_branch']) || $request['school_branch'] == "") {
            $this->error = true;
            $this->errortip = "请传入校区信息";
            return false;
        }
        $school = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch='{$request['school_branch']}'");
        $trackwhere = "ct.school_id = '{$school['school_id']}'";
        $auwhere = "au.school_id = '{$school['school_id']}'";
        $conversionlogwhere = "cg.school_id = '{$school['school_id']}'";
        $principalwhere = "cp.school_id = '{$school['school_id']}'";
        $schoolwhere = "m.company_id = '{$request['company_id']}' and exists (select 1 from crm_client_principal as p where p.marketer_id=m.marketer_id and p.school_id='{$school['school_id']}')";

        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $trackwhere .= " and ct.track_createtime >='{$stattime}'";
            $auwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') >='{$request['start_time']}'";
            $principalwhere .= " and cp.principal_createtime >='{$stattime}'";
            $conversionlogwhere .= " and from_unixtime(cg.conversionlog_time,'%Y-%m-%d') >= '{$request['start_time']}'";
        } else {
            $this->error = true;
            $this->errortip = "请传入查询开始日期";
            return false;
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endime = strtotime($request['end_time']) + 60 * 60 * 24 - 1;
            $trackwhere .= " and ct.track_createtime <='{$endime}'";
            $auwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') <='{$request['end_time']}'";
            $principalwhere .= " and cp.principal_createtime <='{$endime}'";
            $conversionlogwhere .= " and from_unixtime(cg.conversionlog_time,'%Y-%m-%d') <= '{$request['end_time']}'";
        } else {
            $this->error = true;
            $this->errortip = "请传入查询结束日期";
            return false;
        }
        if ($endime - $stattime > 2764800) {
            $this->error = true;
            $this->errortip = "查询日期不能超过32天";
            return false;
        }

//        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
//            $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//            $conwhere .= " AND cg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//            $invwhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//            $auwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//            $principalwhere .= " AND cp.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//        }

//        if (isset($request['keyword']) && $request['keyword'] !== "") {
//            $schoolwhere .= " and (m.marketer_name like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%') ";
//        }

        $sql = "select m.marketer_id,m.marketer_name,
                 (SELECT COUNT(cg.conversionlog_id) 
                     FROM crm_client_conversionlog as cg 
                     WHERE {$conversionlogwhere} AND cg.marketer_id = m.marketer_id AND cg.conversionlog_ismajor = 1
                 ) as main_studentnum,
                 (SELECT COUNT(cg.conversionlog_id)
                     FROM crm_client_conversionlog as cg
                     LEFT JOIN smc_student as s ON s.student_branch = cg.student_branch
                     LEFT JOIN smc_student_registerinfo as re ON re.student_id = s.student_id
                     WHERE {$conversionlogwhere} AND cg.marketer_id = m.marketer_id AND cg.conversionlog_ismajor = 1 AND re.coursetype_id = '65'
                 ) as main_readingnum,
                 (SELECT COUNT(cg.conversionlog_id)
                     FROM crm_client_conversionlog as cg
                     LEFT JOIN smc_student as s ON s.student_branch = cg.student_branch
                     LEFT JOIN smc_student_registerinfo as re ON re.student_id = s.student_id
                     WHERE {$conversionlogwhere} AND cg.marketer_id = m.marketer_id AND cg.conversionlog_ismajor = 1 AND re.coursetype_id = '64'
                 ) as main_coachnum,
                 (SELECT COUNT(cg.conversionlog_id) 
                     FROM crm_client_conversionlog as cg 
                     WHERE {$conversionlogwhere} AND cg.marketer_id = m.marketer_id AND cg.conversionlog_ismajor = 0
                 ) as fu_studentnum,
                 (SELECT COUNT(cg.conversionlog_id)
                     FROM crm_client_conversionlog as cg
                     LEFT JOIN smc_student as s ON s.student_branch = cg.student_branch
                     LEFT JOIN smc_student_registerinfo as re ON re.student_id = s.student_id
                     WHERE {$conversionlogwhere} AND cg.marketer_id = m.marketer_id AND cg.conversionlog_ismajor = 0 AND re.coursetype_id = '65'
                 ) as fu_readingnum,
                 (SELECT COUNT(cg.conversionlog_id)
                     FROM crm_client_conversionlog as cg
                     LEFT JOIN smc_student as s ON s.student_branch = cg.student_branch
                     LEFT JOIN smc_student_registerinfo as re ON re.student_id = s.student_id
                     WHERE {$conversionlogwhere} AND cg.marketer_id = m.marketer_id AND cg.conversionlog_ismajor = 0 AND re.coursetype_id = '64'
                 ) as fu_coachnum,
                (select count(cp.client_id) from crm_client_principal as cp,crm_client_allotlog as cl where cl.client_id=cp.client_id and cp.principal_leave = 0 and {$principalwhere} and cp.marketer_id=m.marketer_id) as princ_clientnum,
                (select count( DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_genre = 0 and au.marketer_id=m.marketer_id) as OH_audtion_num,
                (select count( DISTINCT au.client_id) from crm_client_audition as au where {$auwhere} and au.audition_isvisit = 1 and au.audition_genre = 0 and au.marketer_id=m.marketer_id) as OH_audtion_arrivenum,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.marketer_id =m.marketer_id ) as track_client_num,
                (select count(ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.marketer_id =m.marketer_id ) as track_num,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.track_linktype='电话沟通' and ct.marketer_id =m.marketer_id ) as track_counsel_num,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.track_linktype='电话沟通' and ct.marketer_id =m.marketer_id AND ct.coursetype_id = '65') as reading_client_num,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.track_linktype='电话沟通' and ct.marketer_id =m.marketer_id AND ct.coursetype_id = '64') as coach_client_num,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.track_followmode =1 and ct.track_linktype='柜询沟通' and ct.marketer_id =m.marketer_id ) as track_invite_num,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.track_followmode =1 and ct.track_linktype='柜询沟通' and ct.marketer_id =m.marketer_id AND ct.coursetype_id = '65') as reading_invite_num,
                (select count( DISTINCT ct.client_id) from crm_client_track as ct where {$trackwhere} and ct.track_isactive =1 and ct.track_followmode =1 and ct.track_linktype='柜询沟通' and ct.marketer_id =m.marketer_id AND ct.coursetype_id = '64') as coach_invite_num
                from crm_marketer as m
                left join smc_staffer AS sf ON sf.staffer_id = m.staffer_id
                where {$schoolwhere} and sf.staffer_leave = 0";

        $marketList = $this->DataControl->selectClear($sql);

        if (!$marketList) {
            return array();
        } else {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $temp['marketer_id'] = $value['marketer_id'];
                $temp['marketer_name'] = $value['marketer_name'];
                $temp['main_studentnum'] = $value['main_studentnum'];//主招新生数
                $temp['main_readingnum'] = $value['main_readingnum'];//主招美语新生数
                $temp['main_coachnum'] = $value['main_coachnum'];//主招课辅新生数
                $temp['fu_studentnum'] = $value['fu_studentnum'];//辅招新生数
                $temp['fu_readingnum'] = $value['fu_readingnum'];//辅招美语新生数
                $temp['fu_coachnum'] = $value['fu_coachnum'];//辅招课辅新生数
                $temp['princ_clientnum'] = $value['princ_clientnum'];//新增分配名单数
                $temp['OH_audtion_num'] = $value['OH_audtion_num'];//新增OH邀约数
                $temp['OH_audtion_arrivenum'] = $value['OH_audtion_arrivenum'];//新增OH到访数
                $temp['track_client_num'] = $value['track_client_num'];//沟通人数
                $temp['track_num'] = $value['track_num'];//沟通人次
                $temp['track_counsel_num'] = $value['track_counsel_num'];//电话咨询数
                $temp['reading_client_num'] = $value['reading_client_num'];//美语电话咨询数
                $temp['coach_client_num'] = $value['coach_client_num'];//课辅电话咨询数
                $temp['track_invite_num'] = $value['track_invite_num'];//柜询咨询量
                $temp['reading_invite_num'] = $value['reading_invite_num'];//美语柜询咨询量
                $temp['coach_invite_num'] = $value['coach_invite_num'];//课辅柜询咨询量
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['marketer_id'] = $dateexcelvar['marketer_id'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['main_studentnum'] = $dateexcelvar['main_studentnum'];
                    $datearray['main_readingnum'] = $dateexcelvar['main_readingnum'];
                    $datearray['main_coachnum'] = $dateexcelvar['main_coachnum'];
                    $datearray['fu_studentnum'] = $dateexcelvar['fu_studentnum'];
                    $datearray['fu_readingnum'] = $dateexcelvar['fu_readingnum'];
                    $datearray['fu_coachnum'] = $dateexcelvar['fu_coachnum'];
                    $datearray['princ_clientnum'] = $dateexcelvar['princ_clientnum'];
                    $datearray['OH_audtion_num'] = $dateexcelvar['OH_audtion_num'];
                    $datearray['OH_audtion_arrivenum'] = $dateexcelvar['OH_audtion_arrivenum'];
                    $datearray['track_client_num'] = $dateexcelvar['track_client_num'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $datearray['track_counsel_num'] = $dateexcelvar['track_counsel_num'];
                    $datearray['reading_track_num'] = $dateexcelvar['reading_client_num'];
                    $datearray['coach_track_num'] = $dateexcelvar['coach_client_num'];
                    $datearray['track_invite_num'] = $dateexcelvar['track_invite_num'];
                    $datearray['reading_invite_num'] = $dateexcelvar['reading_invite_num'];
                    $datearray['coach_invite_num'] = $dateexcelvar['coach_invite_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('教师ID', '教师名称', '主招新生数', '主招美语新生数', '主招课辅新生数', '辅招新生数', '辅招美语新生数', '辅招课辅新生数', '新增分配名单数', '新增OH邀约数', '新增OH到访数', '沟通人数', '沟通人次', '电话咨询数', '美语电话咨询数', '课辅电话咨询数', '柜询咨询量', '美语柜询咨询量', '课辅柜询咨询量'));
            $excelfileds = array('marketer_id', 'marketer_name', 'main_studentnum', 'main_readingnum', 'main_coachnum', 'fu_studentnum', 'fu_readingnum', 'fu_coachnum', 'princ_clientnum', 'OH_audtion_num', 'OH_audtion_arrivenum', 'track_client_num', 'track_num', 'track_counsel_num', 'reading_client_num', 'coach_client_num', 'track_invite_num', 'reading_invite_num', 'coach_invite_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("教师业绩统计报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        }

        $data['list'] = $mark_data;
        return $data;

    }

    /**
     * 追踪大表的细分-
     *  需要传入对应的字段,根据字段进行对应的查询
     * author: ling
     * 对应接口文档 0001
     */
    function getReportClientList($request)
    {
        if (!isset($request['query_field']) || $request['query_field'] == '') {
            $this->error = 1;
            $this->errortip = '请选择字段';
            return false;
        }
        if (!isset($request['school_id']) || $request['school_id'] == '') {
            $this->error = 1;
            $this->errortip = '请选择学校';
            return false;
        }
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $fixedtime = $request['fixedtime'];
        } else {
            $fixedtime = '';
        }

        $result = array();
        if ($request['query_field'] == 'positivelog_week_num' || $request['query_field'] == 'positivelog_month_num') {
            $result = $this->getClientPositive($request, $request['query_field'], $request['school_id'], $fixedtime);
        }
        if ($request['query_field'] == 'client_under_weeknum' || $request['query_field'] == 'client_under_monthnum' || $request['query_field'] == 'client_up_weeknum' || $request['query_field'] == 'client_up_monthnum' || $request['query_field'] == 'client_up_quarternum') {
            $result = $this->getAddClient($request, $request['query_field'], $request['school_id'], $fixedtime);
        }
        if ($request['query_field'] == 'ohaudition_week_num' || $request['query_field'] == 'ohaudition_month_num' || $request['query_field'] == 'ohaudition_week_arrnum' || $request['query_field'] == 'ohaudition_month_arrnum' || $request['query_field'] == 'audition_week_num' || $request['query_field'] == 'audition_month_num' || $request['query_field'] == 'ohpostive_month_arrnum') {
            $result = $this->getAuditionClient($request, $request['query_field'], $request['school_id'], $fixedtime);
        }
        return $result;
    }

    /**
     * 查询转正的数据
     * author: ling
     * 对应接口文档 0001
     * @param $query_field
     */
    private function getClientPositive($request, $query_field, $school_id, $fixedtime = '')
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if ($fixedtime !== '') {
            $today = date("Y-m-d", strtotime($fixedtime));
            $todaytime = strtotime($request['fixedtime']) + 24 * 60 * 60 - 1;
        } else {
            $today = date("Y-m-d");
            $todaytime = time();
        }

        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));
        $week_starttime = strtotime($week_startdate);
        $month_starttime = strtotime($month_startdate);

        $register_week_where = "r.pay_successtime >= '{$week_starttime}' and r.pay_successtime <='{$todaytime}'";
        $register_month_where = "r.pay_successtime >= '{$month_starttime}' and r.pay_successtime <='{$todaytime}'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $register_week_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_month_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
        }

        $datawhere = "1 and r.school_id ='{$school_id}' and r.info_status=1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%' or r.coursecat_cnname like '%{$request['keyword']}%' or p.coursetype_cnname like '%{$request['keyword']}%')";
        }
        if ($query_field == 'positivelog_week_num') {
            $datawhere .= " and {$register_week_where}";
        } elseif ($query_field == 'positivelog_month_num') {
            $datawhere .= " and {$register_month_where}";
        } else {
            $datawhere .= "t.student_id = 0";
        }

        $sql = "select r.info_id,t.student_cnname,t.student_enname,t.student_branch,r.coursetype_cnname,r.coursecat_cnname,r.course_branch,r.pay_successtime,r.pay_price,y.channel_name
            ,c.client_source,(select h.channel_name from crm_code_channel as h where c.channel_id = h.channel_id ) as channel_name 
             from smc_student_registerinfo as r
             left join smc_student as t ON t.student_id = r.student_id
             left join smc_student_guildpolicy as y ON t.student_id = y.student_id and guildpolicy_enddate>=FROM_UNIXTIME(r.pay_successtime,'%Y-%m-%d')
             left join crm_client as c ON c.client_id = t.from_client_id 
             where {$datawhere}  order by r.pay_successtime ASC limit {$pagestart},{$num}  ";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['pay_successtime'] = date("Y-m-d", $value['pay_successtime']);
                $value['client_source'] = ($value['client_source'] != '') ? $value['client_source'] : '--';
                $value['channel_name'] = is_null($value['channel_name']) != '' ? '--' : $value['channel_name'];
            }
        }
        $allClientNum = $this->DataControl->selectOne("select count(r.info_id) as all_num
             from smc_student_registerinfo as r
             left join smc_student as t ON t.student_id = r.student_id
             where {$datawhere}   ");
        if ($allClientNum) {
            $allnum = $allClientNum['all_num'];
        } else {
            $allnum = 0;
        }

        if (!$dataList) {
            $dataList = array();
        }
        $field = array();
        $i = 0;
        $field[$i]["fieldname"] = 'student_cnname';
        $field[$i]["fieldstring"] = '学员中文名';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'student_enname';
        $field[$i]["fieldstring"] = '学员英文名';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'student_branch';
        $field[$i]["fieldstring"] = '学员编号';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'coursetype_cnname';
        $field[$i]["fieldstring"] = '报名班组';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'coursecat_cnname';
        $field[$i]["fieldstring"] = '报名班种';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'course_branch';
        $field[$i]["fieldstring"] = '报名班别';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_source';
        $field[$i]["fieldstring"] = '渠道类型';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'channel_name';
        $field[$i]["fieldstring"] = '渠道明细';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'channel_name';
        $field[$i]["fieldstring"] = '专案名称';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'pay_price';
        $field[$i]["fieldstring"] = '首次缴费金额';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'pay_successtime';
        $field[$i]["fieldstring"] = '首次缴费时间';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $result['all_num'] = $allnum;
        return $result;
    }

    /**
     * 获取新增的名单线上 线下 与渠道关联
     * author: ling
     * 对应接口文档 0001
     * @param $query_field
     * @param $school_id
     */
    private function getAddClient($request, $query_field, $school_id, $fixedtime = '')
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;
        if ($fixedtime !== '') {
            $today = date("Y-m-d", strtotime($fixedtime));
            $todaytime = strtotime($fixedtime) + 24 * 60 * 60 - 1;
        } else {
            $today = date("Y-m-d");
            $todaytime = time();
        }
        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));
        $quarter_startdate = date("Y-m-d", strtotime("-90 days", strtotime($today)));

        $week_starttime = strtotime($week_startdate);
        $month_starttime = strtotime($month_startdate);
        $quarter_starttime = strtotime($quarter_startdate);

        $client_week_where = "c.client_createtime >='{$week_starttime}' and c.client_createtime <='{$todaytime}' and c.client_intention_level >=3";
        $client_month_where = "c.client_createtime >='{$month_starttime}' and c.client_createtime <='{$todaytime}' and c.client_intention_level >=3";
        $client_quarter_where = "c.client_createtime >='{$quarter_starttime}' and c.client_createtime <='{$todaytime}' and c.client_intention_level >=3";
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_enname like '%{$request['keyword']}%' or c.client_mobile  like '%{$request['keyword']}%' or h.channel_name like '%{$request['keyword']}%')";
        }
        if ($query_field == 'client_under_weeknum') {
            $datawhere .= " and {$client_week_where} and h.channel_way = 1 ";
        } elseif ($query_field == 'client_under_monthnum') {
            $datawhere .= " and {$client_month_where}  and h.channel_way = 1 ";
        } elseif ($query_field == 'client_up_quarternum') {
            $datawhere .= " and {$client_quarter_where}  ";
        } elseif ($query_field == 'client_up_weeknum') {
            $datawhere .= " and {$client_week_where}  and h.channel_way = 0 ";
        } elseif ($query_field == 'client_up_monthnum') {
            $datawhere .= " and {$client_month_where}  and h.channel_way = 0";
        } else {
            $datawhere .= " and  c.client_id = 0";
        }
        $sql = "select c.client_id,client_cnname,c.client_enname,REPLACE(c.client_mobile,SUBSTR(c.client_mobile,4,4),'****') as client_mobile,c.client_createtime,h.channel_name from crm_client as c,crm_client_schoolenter as r,crm_code_channel as h where c.client_id = r.client_id and c.channel_id = h.channel_id and r.school_id='{$school_id}' and {$datawhere} limit {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['client_createdate'] = date("Y-m-d", $value['client_createtime']);
            }
        }
        $allClientNum = $this->DataControl->selectOne("select count(c.client_id) as all_num  from crm_client as c,crm_client_schoolenter as r,crm_code_channel as h where c.client_id = r.client_id and c.channel_id = h.channel_id and r.school_id='{$school_id}' and {$datawhere} ");
        if ($allClientNum) {
            $allnum = $allClientNum['all_num'];
        } else {
            $allnum = 0;
        }

        $field = array();
        $i = 0;
        $field[$i]["fieldname"] = 'client_cnname';
        $field[$i]["fieldstring"] = '客户中文名';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_enname';
        $field[$i]["fieldstring"] = '客户英文名';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_mobile';
        $field[$i]["fieldstring"] = '客户手机号';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'channel_name';
        $field[$i]["fieldstring"] = '所属渠道';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_createdate';
        $field[$i]["fieldstring"] = '创建时间';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $result['all_num'] = $allnum;
        return $result;
    }

    /**
     * 获取试听相关的信息 邀约和到访
     * author: ling
     * 对应接口文档 0001
     */
    private function getAuditionClient($request, $query_field, $school_id, $fixedtime = '')
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if ($fixedtime !== '') {
            $today = date("Y-m-d", strtotime($fixedtime));
        } else {
            $today = date("Y-m-d");
        }
        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));

        $audition_week_where = "a.audition_visittime >='{$week_startdate}'";
        $audition_month_where = "a.audition_visittime >='{$month_startdate}'";

        $datawhere = '1 ';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_enname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%')";
        }
        if ($query_field == 'ohaudition_week_num') {
            $datawhere .= " and {$audition_week_where} and a.audition_genre = 0 ";
        } elseif ($query_field == 'ohaudition_month_num') {
            $datawhere .= " and {$audition_month_where}  and a.audition_genre = 0 ";
        } elseif ($query_field == 'ohaudition_week_arrnum') {
            $datawhere .= " and {$audition_week_where} and a.audition_genre = 0 and audition_isvisit =1 ";
        } elseif ($query_field == 'ohaudition_month_arrnum') {
            $datawhere .= " and {$audition_month_where} and a.audition_genre = 0 and audition_isvisit =1 ";
        } elseif ($query_field == 'audition_week_num') {
            $datawhere .= " and {$audition_week_where} ";
        } elseif ($query_field == 'audition_month_num') {
            $datawhere .= " and {$audition_month_where} ";
        } elseif ($query_field == 'ohpostive_month_arrnum') {
            $datawhere .= " and {$audition_month_where} ";
        } else {
            $datawhere .= " and a.audition_id = 0 ";
        }
        if ($query_field == 'ohpostive_month_arrnum') {
            $sql = "select a.audition_id,c.client_id,c.client_cnname,c.client_enname,REPLACE(c.client_mobile,SUBSTR(c.client_mobile,4,4),'****') as client_mobile,a.audition_visittime,a.audition_isvisit,l.channel_name,c.client_source   
              from crm_client as c , crm_client_audition as a,crm_client_positivelog as  pg,crm_code_channel as l where   a.school_id='{$school_id}' AND c.client_id = a.client_id  and  pg.client_id = a.client_id and a.school_id = pg.school_id and l.channel_id = c.channel_id and pg.positivelog_time =DATE_FORMAT( a.audition_visittime, '%Y-%m-%d' )	AND a.audition_genre = 0  AND a.audition_isvisit = 1   and {$datawhere}  limit {$pagestart},{$num}";
            $allClientNum = $this->DataControl->selectOne("select count(a.audition_id) as all_num  
              from crm_client as c , crm_client_audition as a,crm_client_positivelog as  pg where  a.school_id='{$school_id}' AND c.client_id = a.client_id  and  pg.client_id = a.client_id and a.school_id = pg.school_id and pg.positivelog_time =DATE_FORMAT( a.audition_visittime, '%Y-%m-%d' ) 	AND a.audition_genre = 0  AND a.audition_isvisit = 1  and {$datawhere}  ");
            if ($allClientNum) {
                $allnum = $allClientNum['all_num'];
            } else {
                $allnum = 0;
            }
        } else {
            $sql = "select a.audition_id,c.client_id,c.client_cnname,c.client_enname,REPLACE(c.client_mobile,SUBSTR(c.client_mobile,4,4),'****') as client_mobile,a.audition_visittime,a.audition_isvisit,l.channel_name,c.client_source     
from crm_client_audition as a,crm_client as c,crm_code_channel as l  where c.client_id=a.client_id  and l.channel_id = c.channel_id and a.school_id='{$school_id}' and {$datawhere} limit {$pagestart},{$num} ";
            $allClientNum = $this->DataControl->selectOne("select count(a.audition_id) as all_num  
from crm_client_audition as a,crm_client as c  where  c.client_id=a.client_id and  a.school_id='{$school_id}' and {$datawhere}   ");
            if ($allClientNum) {
                $allnum = $allClientNum['all_num'];
            } else {
                $allnum = 0;
            }
        }

//        var_dump($allClientNum);
//        var_dump($sql);
        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            $isvisitName = $this->LgArraySwitch(array("0" => "待操作", "1" => "已试听", "-1" => "未试听"));
            foreach ($dataList as &$dataOne) {
                $dataOne['audition_isvisit'] = $isvisitName[$dataOne['audition_isvisit']];
                $dataOne['client_source'] = ($dataOne['client_source'] != '') ? $dataOne['client_source'] : '--';
                $dataOne['channel_name'] = ($dataOne['channel_name'] != '') ? $dataOne['channel_name'] : '--';
            }
        }


        $field = array();
        $i = 0;
        $field[$i]["fieldname"] = 'client_cnname';
        $field[$i]["fieldstring"] = '客户中文名';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_enname';
        $field[$i]["fieldstring"] = '客户英文名';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_mobile';
        $field[$i]["fieldstring"] = '客户手机号';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'client_source';
        $field[$i]["fieldstring"] = '渠道类型';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'channel_name';
        $field[$i]["fieldstring"] = '所属渠道';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'audition_visittime';
        $field[$i]["fieldstring"] = '试听时间';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;
        $i++;
        $field[$i]["fieldname"] = 'audition_isvisit';
        $field[$i]["fieldstring"] = '试听状态';
        $field[$i]["custom"] = 1;
        $field[$i]["show"] = 1;

        $result = array();
        $result['field'] = $field;
        $result['list'] = $dataList;
        $result['all_num'] = $allnum;
        return $result;
    }

    /**
     * 转介绍招生明细报表
     * author: qizhugong
     * 对应接口文档 0001
     */
    function referralClientlist($request)
    {
        $datawhere = "s.client_id = c.client_id AND c.channel_id = l.channel_id AND l.channel_isreferral = '1' AND h.school_id = s.school_id AND h.school_istest = '0' AND h.school_isclose = '0' AND c.company_id = '{$request['company_id']}'";
        $regwhere = "rc.student_id = st.student_id AND st.from_client_id = c.client_id";
        if (isset($request['starttime']) && $request['starttime']) {
            $stattime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }

        if (isset($request['visit_starttime']) && $request['visit_starttime'] && isset($request['visit_endtime']) && $request['visit_endtime']) {
            $stattime = date('Y-m-d H:i:s', strtotime($request['visit_starttime']));
            $endime = date('Y-m-d H:i:s', strtotime($request['visit_endtime']) + 60 * 60 * 24 - 1);
            $datawhere .= " and c.client_id IN (SELECT v.client_id FROM view_crm_visitlist AS v WHERE v.company_id = '{$request['company_id']}' AND v.visittime >='{$stattime}' AND v.visittime <= '{$endime}')";
        }

        if (isset($request['bookstarttime']) && $request['bookstarttime'] && isset($request['bookendtime']) && $request['bookendtime']) {
            $stattime = strtotime($request['bookstarttime']);
            $endime = date('Y-m-d H:i:s', strtotime($request['bookendtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_id IN (SELECT st.from_client_id FROM smc_student_registerinfo AS r,smc_student AS st
            WHERE r.student_id = st.student_id AND st.company_id = '{$request['company_id']}' AND st.from_client_id <> '0' and r.info_status = '1' and r.info_type = '0'  
            AND r.pay_successtime >='{$stattime}' AND r.pay_successtime <='{$endime}')";

            $regwhere .= " AND rc.pay_successtime >='{$stattime}' AND rc.pay_successtime <='{$endime}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and s.school_id ='{$request['school_id']}'";
        }
        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and h.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        }

        if (isset($request['class_id']) && $request['class_id'] !== "") {
            $datawhere .= " and c.client_id IN (SELECT st.from_client_id FROM smc_student_study AS ss,smc_student AS st
            WHERE ss.student_id = st.student_id AND st.company_id = '{$request['company_id']}' AND ss.class_id = '{$request['class_id']}' AND ss.study_isreading = '1')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== "") {
            $datawhere .= " and s.school_id in (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname  like '%{$request['keyword']}%' or l.channel_name like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%'  ) ";
        }

        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "h.school_cnname,h.school_branch,
        (select r.region_name from smc_code_region as r where r.region_id = h.school_province ) as province_name,
        (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = h.district_id) AS district_cnname,
        c.client_cnname,c.client_enname,c.client_mobile,c.client_gmcmarket,c.client_tracestatus,l.channel_name,c.client_stubranch,c.client_createtime,c.client_age,c.client_sex,
        (SELECT concat(k.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )
         FROM crm_client_principal as p,crm_marketer as k ,smc_staffer as sf  WHERE  sf.staffer_id=k.staffer_id and k.marketer_id = p.marketer_id and p.client_id =c.client_id and principal_ismajor = 1 and principal_leave =0 order by p.principal_createtime DESC limit 0,1 ) AS zhu_marketer_name,
		(SELECT r.student_branch FROM smc_student as r WHERE r.company_id = c.company_id AND r.from_client_id = c.client_id limit 0,1) as student_branch,c.client_teachername,
		(SELECT CONCAT(rc.pay_price,'-',rc.coursetype_cnname,'-',rc.pay_successtime) FROM smc_student_registerinfo AS rc,smc_student AS st
		WHERE {$regwhere} LIMIT 0,1) as pay_price";
        $sql = "SELECT {$fields} FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h, crm_code_channel AS l
WHERE {$datawhere} and s.is_enterstatus=1 GROUP BY c.client_id order by h.school_sort asc,h.school_id asc";

        //跟踪状态：0待跟踪1持续跟踪2已柜询3已试听4已转正-1已无意向
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if (count($dateexcelarray) > 5000) {
                $this->error = true;
                $this->errortip = "导出数据量大于5千条,请筛选条件后进行导出";
                return false;
            }
            foreach ($dateexcelarray as &$val) {
                $val['client_tracestatus'] = $clientTracestatus[$val['client_tracestatus']];
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'] . ($dateexcelvar['client_enname'] != '' ? '-' . $dateexcelvar['client_enname'] : '');
                    $datearray['student_branch'] = trim($dateexcelvar['student_branch']) ? $dateexcelvar['student_branch'] : '--';
//                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];

                    if ($datearray['student_branch'] !== '--' && $dateexcelvar['pay_price']) {
                        $priceOne = explode("-", $dateexcelvar['pay_price']);
                        $datearray['pay_successtime'] = date("Y-m-d", $priceOne['2']);
                        $datearray['coursetype_cnname'] = trim($priceOne['1']);
                        $datearray['pay_price'] = trim($priceOne['0']);
                    } else {
                        $datearray['pay_successtime'] = '--';
                        $datearray['coursetype_cnname'] = '--';
                        $datearray['pay_price'] = '--';
                    }


                    $datearray['zhu_marketer_name'] = $dateexcelvar['zhu_marketer_name'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['rom_branch'] = '--';
                    $datearray['rom_cnname'] = '--';
                    $datearray['romclass_branch'] = '--';
                    $datearray['romclass_enname'] = '--';
                    $datearray['romstaffer_branch'] = '--';
                    $datearray['romstaffer_cnname'] = '--';

                    if ($datearray['client_stubranch'] !== '') {
                        $romstudentOne = $this->DataControl->selectOne("SELECT CONCAT( t.student_branch, '#',t.student_cnname,'#',t.student_enname, '#', t.class_branch, '#', t.class_enname, '#', t.staffer_branch, '#', t.staffer_cnname,'/',t.staffer_enname) AS stuteach
FROM view_smc_student_teach AS t WHERE t.company_id = '{$request['company_id']}' AND t.student_branch = '{$dateexcelvar['client_stubranch']}'
AND t.study_beginday <= '{$datearray['client_createtime']}' AND t.study_endday >= '{$datearray['client_createtime']}' GROUP BY t.class_branch LIMIT 0, 1");
                        if ($romstudentOne) {
                            $stuteachArray = explode("#", $romstudentOne['stuteach']);
                            $datearray['rom_branch'] = $stuteachArray[0];
                            $datearray['rom_cnname'] = $stuteachArray[1] . ($stuteachArray[2] != '' ? $stuteachArray[2] : '');
                            $datearray['romclass_branch'] = $stuteachArray[3];
                            $datearray['romclass_enname'] = $stuteachArray[4];
                            $datearray['romstaffer_branch'] = $stuteachArray[5];
                            $datearray['romstaffer_cnname'] = $stuteachArray[6];
                        } else {
                            $romstudentOne = $this->DataControl->selectOne("SELECT s.student_branch,s.student_cnname,s.student_enname FROM smc_student AS s
WHERE s.student_branch = '{$dateexcelvar['client_stubranch']}' AND s.company_id = '{$request['company_id']}' LIMIT 0, 1");
                            if ($romstudentOne) {
                                $datearray['rom_branch'] = $romstudentOne['student_branch'];
                                $datearray['rom_cnname'] = $romstudentOne['student_cnname'] . ($romstudentOne['student_enname'] != '' ? '-' . $romstudentOne['student_enname'] : '');
                            }
                        }
                    }

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('名单创建时间', '省份', '校区名称', '校区编号', '被推荐学员姓名', '被推荐学员学员编号', '性别', '年龄', '联系方式', '客户状态', '报名日期', '报名班组', '首缴金额', '被推荐学员主要负责人', '招生渠道明细', '推荐学员编号', '推荐学员姓名', '推荐班级编号', '推荐班级别名', '推荐班级主教编号', '推荐班级主教姓名'));
            $excelfileds = array('client_createtime', 'province_name', 'school_cnname', 'school_branch', 'client_cnname', 'student_branch', 'client_sex', 'client_age', 'client_mobile', 'client_tracestatus', 'pay_successtime', 'coursetype_cnname', 'pay_price', 'zhu_marketer_name', 'channel_name', 'rom_branch', 'rom_cnname', 'romclass_branch', 'romclass_enname', 'romstaffer_branch', 'client_teachername');
            $fielname = $this->LgStringSwitch("转介绍招生明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {

            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as &$dataOne) {
                    $dataOne['client_createtime'] = $dataOne['client_createtime'] == '' ? '--' : date("Y-m-d", $dataOne['client_createtime']);
                    $dataOne['client_cnname'] = $dataOne['client_cnname'] . ($dataOne['client_enname'] != '' ? '-' . $dataOne['client_enname'] : '');
                    $dataOne['student_branch'] = trim($dataOne['student_branch']) ? $dataOne['student_branch'] : '--';
                    if ($dataOne['student_branch'] !== '--' && $dataOne['pay_price']) {
                        $priceOne = explode("-", $dataOne['pay_price']);
                        $dataOne['pay_price'] = trim($priceOne['0']);
                        $dataOne['coursetype_cnname'] = trim($priceOne['1']);
                        $dataOne['pay_successtime'] = date("Y-m-d", $priceOne['2']);
                    } else {
                        $dataOne['pay_price'] = '--';
                        $dataOne['coursetype_cnname'] = '--';
                        $dataOne['pay_successtime'] = '--';
                    }
                    $dataOne['client_tracestatus'] = $clientTracestatus[$dataOne['client_tracestatus']];
                    $dataOne['client_mobile'] = hideNumberString($dataOne['client_mobile']);

                    $dataOne['rom_branch'] = '--';
                    $dataOne['rom_cnname'] = '--';
                    $dataOne['romclass_branch'] = '--';
                    $dataOne['romclass_cnname'] = '--';
                    $dataOne['romclass_enname'] = '--';
                    $dataOne['romstaffer_branch'] = '--';
                    $dataOne['romstaffer_cnname'] = '--';
                    if ($dataOne['client_stubranch'] !== '') {//&& !$dataOne['client_teachername']
                        $romstudentOne = $this->DataControl->selectOne("SELECT CONCAT( t.student_branch, '#',t.student_cnname,'#',t.student_enname, '#', t.class_branch, '#', t.class_cnname, '#', t.class_enname, '#', t.staffer_branch, '#', t.staffer_cnname) AS stuteach
FROM view_smc_student_teach AS t WHERE t.company_id = '{$request['company_id']}' AND t.student_branch = '{$dataOne['client_stubranch']}'
AND t.study_beginday <= '{$dataOne['client_createtime']}' AND t.study_endday >= '{$dataOne['client_createtime']}' GROUP BY t.class_branch LIMIT 0, 1");
                        if ($romstudentOne) {
                            $stuteachArray = explode("#", $romstudentOne['stuteach']);
                            $dataOne['rom_branch'] = $stuteachArray[0];
                            $dataOne['rom_cnname'] = $stuteachArray[1] . ($stuteachArray[2] != '' ? $stuteachArray[2] : '');
                            $dataOne['romclass_branch'] = $stuteachArray[3];
                            $dataOne['romclass_cnname'] = $stuteachArray[4];
                            $dataOne['romclass_enname'] = $stuteachArray[5];
                            $dataOne['romstaffer_branch'] = $stuteachArray[6];
                            $dataOne['client_teachername'] = $dataOne['client_teachername'] ? $dataOne['client_teachername'] : $stuteachArray[7];
                        } else {
                            $romstudentOne = $this->DataControl->selectOne("SELECT s.student_branch,s.student_cnname,s.student_enname FROM smc_student AS s
WHERE s.student_branch = '{$dataOne['client_stubranch']}' AND s.company_id = '{$request['company_id']}' LIMIT 0, 1");
                            if ($romstudentOne) {
                                $dataOne['rom_branch'] = $romstudentOne['student_branch'];
                                $dataOne['rom_cnname'] = $romstudentOne['student_cnname'] . ($romstudentOne['student_enname'] != '' ? '-' . $romstudentOne['student_enname'] : '');
                            }
                        }
                    }
                }
            } else {
                $dataList = array();
            }
            $data['list'] = $dataList;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.client_id) AS allnum FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h, crm_code_channel AS l
WHERE {$datawhere} and s.is_enterstatus=1  limit 0,1");
            if ($allNum) {
                $data['allnum'] = $allNum['allnum'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }


    function referralBooklist($request)
    {
        $datawhere = "r.student_id = s.student_id AND o.school_id = r.school_id AND s.from_client_id = c.client_id AND c.channel_id = l.channel_id
        AND l.channel_isreferral = '1' AND s.company_id = '{$request['company_id']}' and  info_status=1 ";

        if (isset($request['starttime']) && $request['starttime']) {
            $stattime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }

        if (isset($request['bookstarttime']) && $request['bookstarttime']) {
            $stattime = strtotime($request['bookstarttime']);
            $datawhere .= " and r.pay_successtime >='{$stattime}'";
        }
        if (isset($request['bookendtime']) && $request['bookendtime']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['bookendtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and r.pay_successtime <='{$endime}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            $datawhere .= " and r.coursetype_id ='{$request['coursetype_id']}'";
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$request['school_id']}'";
        }
        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and o.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND o.school_id IN (SELECT z.school_id FROM gmc_company_organizeschool AS z WHERE z.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== "") {
            $datawhere .= " and o.school_id in (SELECT z.school_id FROM gmc_company_organizeschool as z WHERE oz.organize_id = '{$request['organize_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname  like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%' or l.channel_name like '%{$request['keyword']}%'  ) ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "o.school_cnname, o.school_branch,
        (select r.region_name from smc_code_region as r where r.region_id = o.school_province ) as province_name,
        (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = o.district_id) AS district_cnname,
         c.client_cnname, c.client_sex, c.client_mobile , l.channel_name, c.client_createtime, s.student_branch,
        s.student_cnname, s.student_enname , r.coursetype_cnname, r.pay_price, r.pay_firsttime, r.pay_successtime";
        $sql = "SELECT {$fields} FROM smc_student_registerinfo r, smc_student s, smc_school o, crm_client c, crm_code_channel l WHERE {$datawhere} ORDER BY r.pay_firsttime DESC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if (count($dateexcelarray) > 5000) {
                $this->error = true;
                $this->errortip = "导出数据量大于5千条,请筛选条件后进行导出";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    $datearray['pay_firsttime'] = date("Y-m-d", $dateexcelvar['pay_firsttime']);
                    $datearray['pay_successtime'] = date("Y-m-d", $dateexcelvar['pay_successtime']);
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', 'CRM中文名', '性别', '手机号码', '招生渠道明细', '名单创建日期', '学员编号', '学员中文名', '学员英文名', '班组', '首缴金额', '首缴日期', '报名缴费日期'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'client_cnname', 'client_sex', 'client_mobile', 'channel_name', 'client_createtime', 'student_branch', 'student_cnname', 'student_enname', 'coursetype_cnname', 'pay_price', 'pay_firsttime', 'pay_successtime');
            $fielname = $this->LgStringSwitch("转介绍报名明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as &$dateOne) {
                    $dateOne['client_mobile'] = hideNumberString($dateOne['client_mobile']);
                    $dateOne['client_createtime'] = date("Y-m-d", $dateOne['client_createtime']);
                    $dateOne['pay_firsttime'] = date("Y-m-d", $dateOne['pay_firsttime']);
                    $dateOne['pay_successtime'] = date("Y-m-d", $dateOne['pay_successtime']);
                }
            } else {
                $dataList = array();
            }
            $data['list'] = $dataList;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT COUNT(DISTINCT r.info_id) AS allnum
FROM smc_student_registerinfo r, smc_student s, smc_school o , crm_client c, crm_code_channel l WHERE {$datawhere} limit 0,1");
            if ($allNum) {
                $data['allnum'] = $allNum['allnum'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    /**
     * 渠道招生明细报表
     * author: qizhugong
     * 对应接口文档 0001
     */
    function detailsByChannel($request)
    {
        $datawhere = "s.client_id = c.client_id AND c.channel_id = l.channel_id AND h.school_id = s.school_id AND c.company_id = '{$request['company_id']}'";//AND h.school_istest = '0' crm 报表 2-2 测试校数据不显示所以去掉了
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and s.school_id ='{$request['school_id']}'";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== "") {
            $datawhere .= " and s.school_id in (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {//产品补充接触点筛选
            $datawhere .= " and (c.client_cnname  like '%{$request['keyword']}%' or c.client_enname  like '%{$request['keyword']}%'  or c.client_mobile  like '%{$request['keyword']}%' or c.client_frompage  like '%{$request['keyword']}%' ) ";
        }

        //接通状态 0未接通 1接通有效 2接通无效
        if (isset($request['client_answerphone']) && $request['client_answerphone'] == 0 && $request['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone = '{$request['client_answerphone']}'";
        } elseif (isset($request['client_answerphone']) && $request['client_answerphone'] == 1) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel >= 3";
        } elseif (isset($request['client_answerphone']) && $request['client_answerphone'] == 2) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel < 3";
        }
//        //是否接通电话
//        if (isset($request['client_answerphone']) && $request['client_answerphone'] !== '') {
//            $datawhere .= " and c.client_answerphone ='{$request['client_answerphone']}'";
//        }

        $having = "1 =1";
        if (isset($request['zhu_marketer_id']) && $request['zhu_marketer_id'] !== "") {
            $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$request['zhu_marketer_id']}'");
            $having .= " and zhu_marketer_name = '{$marketerOne['marketer_name']}' ";
        }
        if (isset($request['fu_marketer_id']) && $request['fu_marketer_id'] !== "") {
            $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$request['fu_marketer_id']}'");
            $having .= " and fu_marketer_name = '{$marketerOne['marketer_name']}' ";
        }
        if (isset($request['track_num']) && $request['track_num'] !== "") {
            $having .= " and track_num <= '{$request['track_num']}'";
        }

        //未柜询，柜询待确认，已柜询
        if (isset($request['ishaveinvite']) && $request['ishaveinvite'] == '0') {//未柜询
            $having .= " and active_invite_id is null and invite_idthree is null ";
        } elseif (isset($request['ishaveinvite']) && $request['ishaveinvite'] == '1') {//柜询待确认
            $having .= " and invite_idthree is not null ";
        } elseif (isset($request['ishaveinvite']) && $request['ishaveinvite'] == '2') {//已柜询
            $having .= " and active_invite_id is not null ";
        }
        //未试听，试听待确认，已试听
        if (isset($request['ishaveaudition']) && $request['ishaveaudition'] == '0') {//未试听
            $having .= " and active_audition_id is null and audition_idthree is null  ";
        } elseif (isset($request['ishaveaudition']) && $request['ishaveaudition'] == '1') {//试听待确认
            $having .= " and audition_idthree is not null ";
        } elseif (isset($request['ishaveaudition']) && $request['ishaveaudition'] == '2') {//已试听
            $having .= " and active_audition_id is not null ";
        }
        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== "" && $request['frommedia_name'] !== "[]") {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }

        if (isset($request['channel_id']) && $request['channel_id'] !== '' && $request['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($request['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }

        if (isset($request['frommedia_name']) && $request['frommedia_name'] != '' && $request['frommedia_name'] != '[]') {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            } else {
                $datawhere .= " and c.client_source ='{$request['frommedia_name']}' ";
            }
        }
        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "h.school_cnname,h.school_branch,c.client_cnname,c.client_enname,c.client_mobile,c.client_patriarchname,c.client_gmcmarket,c.client_tracestatus,c.client_source,l.channel_name,l.channel_board,c.client_createtime,c.client_age,c.client_sex,c.client_intention_level,c.client_intention_maxlevel,c.client_updatetime,c.client_answerphone,c.client_frompage,c.client_lineid,c.client_facebookid,
        (select r.student_branch from smc_student as r where r.from_client_id = c.client_id ) as student_branch,
        (select r.region_name from smc_code_region as r where r.region_id = h.school_province ) as province_name,
        (select p.promotion_jobnumber from crm_ground_promotion AS p where p.promotion_id=c.promotion_id) as promotion_jobnumber,
        (select p.promotion_type from crm_ground_promotion AS p where p.promotion_id=c.promotion_id) as promotion_type,
        (select a.audition_isvisit from crm_client_audition as a where a.client_id = c.client_id and a.school_id = s.school_id order by a.audition_id desc limit 0,1) as last_audition_isvisit,
        (select a.invite_isvisit from crm_client_invite as a where a.client_id = c.client_id and a.school_id = s.school_id order by a.invite_id desc limit 0,1) as last_invite_isvisit,
        (SELECT p.parenter_cnname FROM crm_client_family AS f,smc_parenter as p WHERE f.parenter_id =p.parenter_id  and f.client_id = c.client_id order by f.family_isdefault DESC limit 0,1) AS parenter_cnname,
        (SELECT concat(k.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )
         FROM crm_client_principal as p,crm_marketer as k ,smc_staffer as sf  WHERE  sf.staffer_id=k.staffer_id and k.marketer_id = p.marketer_id and p.client_id =c.client_id and principal_ismajor = 1 and principal_leave =0 order by p.principal_createtime DESC limit 0,1 ) AS zhu_marketer_name,
        (SELECT group_concat(concat(k.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ))
          FROM crm_client_principal as p,crm_marketer as k ,smc_staffer as sf  WHERE  sf.staffer_id=k.staffer_id and k.marketer_id = p.marketer_id and p.client_id =c.client_id and principal_ismajor = 0 and principal_leave =0 order by p.principal_createtime DESC limit 0,1 ) AS fu_marketer_name,
        (select concat(k.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) from crm_client_track as t,crm_marketer as k,smc_staffer as sf WHERE t.client_id =c.client_id and t.track_followmode = '-3' and k.marketer_id = t.marketer_id and sf.staffer_id=k.staffer_id order by t.track_id desc limit 0,1) as last_zhu_marketer_name, 
        (SELECT track_createtime FROM crm_client_track AS t WHERE t.client_id =c.client_id and t.track_isactive =1
        order by t.track_createtime DESC limit 0,1) AS track_createtime,
        (SELECT count(t.track_id) FROM crm_client_track AS t WHERE t.client_id =c.client_id and t.school_id=s.school_id and  t.track_isactive =1) AS track_num,
        (SELECT group_concat(CONCAT(from_unixtime(ct.track_createtime, '%m-%d'),' 跟踪：',ct.track_note,'#'))
		FROM crm_client_track AS ct WHERE ct.client_id = c.client_id AND (ct.track_isactive = '1' or ct.track_isgmcactive = '1') ORDER BY ct.track_id DESC ) AS track_note,
        (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = h.district_id) AS district_cnname,
        (SELECT t.track_intention_level FROM crm_client_track AS t WHERE c.client_id = t.client_id and s.school_id = t.school_id order by track_intention_level desc limit 0,1 ) AS track_intention_level,
        ifnull((select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id),'--') as activity_name,
        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id, 
        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,   
        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree";
        $sql = "SELECT {$fields} FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h, crm_code_channel AS l
WHERE {$datawhere} and s.is_enterstatus=1 GROUP BY c.client_id HAVING {$having}
      order by (case when h.school_istest=0 and h.school_isclose=0 then 1 when h.school_isclose=0 then 2 when h.school_istest=0 then 3 else 4 end),h.school_istest asc,field(h.school_sort,0),h.school_sort asc,h.school_createtime asc";
//var_dump($sql);
        //跟踪状态：0待跟踪1持续跟踪2已柜询3已试听4已转正-1已无意向
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            ini_set("memory_limit", '-1');
            set_time_limit(600);
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($dateexcelarray as &$val) {
                $val['client_tracestatus'] = $clientTracestatus[$val['client_tracestatus']];

                if ($val['client_answerphone'] == 1) {
//                    $val['client_answerphone'] = $this->LgStringSwitch("已接通");
                    if ($val['client_intention_maxlevel'] >= 3) {
                        $val['client_answerphone'] = $this->LgStringSwitch("接通有效");
                    } else {
                        $val['client_answerphone'] = $this->LgStringSwitch("接通无效");
                    }
                } else {
                    $val['client_answerphone'] = $this->LgStringSwitch("未接通");
                }

                if (!$val['promotion_jobnumber']) {
                    $val['promotion_jobnumber'] = '--';
                }
                if (is_null($val['promotion_type'])) {
                    $val['post_name'] = '--';
                } else {
                    if ($val['promotion_type'] == 0) {
                        $val['post_name'] = '市场';
                    } else {
                        $val['post_name'] = '销售';
                    }
                }
                if (!$val['client_frompage']) {
                    $val['client_frompage'] = '--';
                }
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];
                    $datearray['client_answerphone'] = $dateexcelvar['client_answerphone'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['section_name'] = $dateexcelvar['channel_board'];
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];

                    $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];

                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['client_updatetime'] = $dateexcelvar['client_updatetime'] == '' ? '--' : date("Y-m-d", $dateexcelvar['client_updatetime']);
                    $datearray['zhu_marketer_name'] = $dateexcelvar['zhu_marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $datearray['client_intention_maxlevel'] = $dateexcelvar['client_intention_maxlevel'];
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'] == '' ? '--' : date("Y-m-d", $dateexcelvar['track_createtime']);
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch']?$dateexcelvar['student_branch']:'--';
                    $datearray['client_lineid'] = $dateexcelvar['client_lineid'];
                    $datearray['client_facebookid'] = $dateexcelvar['client_facebookid'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 1) {
                $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '中文名', '英文名', '性别', '年龄', '主要联系人', '手机号码', '客户状态', '是否接通', '意向星级', '渠道类型', '渠道明细', '渠道板块', '招生活动', '地推工号', '职务', '接触点', '创建时间', '更新时间', '主要负责人', '协助负责人', '跟踪次数', '最高意向星级', '最后跟踪时间', '最后跟踪内容','学员编号','LineID', 'FacebookID'));
                $excelfileds = array('province_name','school_cnname', 'school_branch', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'parenter_cnname', 'client_mobile', 'client_tracestatus', 'client_answerphone', 'client_intention_level', 'client_source', 'channel_name', 'section_name', 'activity_name', 'promotion_jobnumber', 'post_name', 'client_frompage', 'client_createtime', 'client_updatetime', 'zhu_marketer_name', 'fu_marketer_name', 'track_num', 'client_intention_maxlevel', 'track_createtime', 'track_note','student_branch','client_lineid', 'client_facebookid');
            } else {
                $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '中文名', '英文名', '性别', '年龄', '主要联系人', '手机号码', '客户状态', '是否接通', '意向星级', '渠道类型', '渠道明细', '渠道板块', '招生活动', '地推工号', '职务', '接触点', '创建时间', '更新时间', '主要负责人', '跟踪次数', '最高意向星级', '最后跟踪时间', '最后跟踪内容','学员编号','LineID', 'FacebookID'));
                $excelfileds = array('province_name','school_cnname', 'school_branch', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'parenter_cnname', 'client_mobile', 'client_tracestatus', 'client_answerphone', 'client_intention_level', 'client_source', 'channel_name', 'section_name', 'activity_name', 'promotion_jobnumber', 'post_name', 'client_frompage', 'client_createtime', 'client_updatetime', 'zhu_marketer_name', 'track_num', 'client_intention_maxlevel', 'track_createtime', 'track_note','student_branch','client_lineid', 'client_facebookid');
            }
            $fielname = $this->LgStringSwitch("渠道名单新增明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    $value['section_name'] = $value['channel_board'];
                    if ($value['client_answerphone'] == 1) {
                        if ($value['client_intention_maxlevel'] >= 3) {
                            $datalist[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
                        } else {
                            $datalist[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
                        }
                    } else {
                        $datalist[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                    }
                    $datalist[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    $datalist[$key]['client_updatetime'] = $value['client_updatetime'] == '' ? '--' : date("Y-m-d", $value['client_updatetime']);
                    $datalist[$key]['track_createtime'] = $value['track_createtime'] == '' ? '--' : date("Y-m-d", $value['track_createtime']);
                    $datalist[$key]['client_tracestatus'] = $clientTracestatus[$value['client_tracestatus']];
                    $datalist[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                    $datalist[$key]['track_note'] = $value['track_note'];

                    if (!$value['promotion_jobnumber']) {
                        $datalist[$key]['promotion_jobnumber'] = '--';
                    }
                    if (is_null($value['promotion_type'])) {
                        $datalist[$key]['post_name'] = '--';
                    } else {
                        if ($value['promotion_type'] == 0) {
                            $datalist[$key]['post_name'] = '市场';
                        } else {
                            $datalist[$key]['post_name'] = '销售';
                        }
                    }
                    if (!$value['client_frompage']) {
                        $datalist[$key]['client_frompage'] = '--';
                    }
                }
            } else {
                $datalist = array();
            }
            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectClear("SELECT  c.client_id,
        (SELECT k.marketer_name  FROM crm_client_principal as p,crm_marketer as k  WHERE k.marketer_id = p.marketer_id and p.client_id =c.client_id and principal_ismajor = 1 and principal_leave =0 order by p.principal_createtime DESC limit 0,1 ) AS zhu_marketer_name,
        (SELECT k.marketer_name  FROM crm_client_principal as p,crm_marketer as k  WHERE k.marketer_id = p.marketer_id and p.client_id =c.client_id and principal_ismajor = 0 and principal_leave =0 order by p.principal_createtime DESC limit 0,1 ) AS fu_marketer_name,
        (SELECT count(t.track_id) FROM crm_client_track AS t WHERE t.client_id =c.client_id and t.school_id=s.school_id and  t.track_isactive =1) AS track_num,
        
        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id, 
        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,   
        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h, crm_code_channel AS l WHERE {$datawhere} and s.is_enterstatus=1  GROUP BY c.client_id HAVING {$having}");
            if ($allNum) {
                $data['allnum'] = count($allNum);
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    /**
     * 渠道业绩
     * author: qizhugong
     * 对应接口文档 0001
     */
    function gainsByChannel($request)
    {
        $datawhere = "l.company_id = '{$request['company_id']}'";
        $clientwhere = "c.company_id ='{$request['company_id']}'";
        $positivelogwhere = "s.company_id ='{$request['company_id']}'";
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
            $positivelogwhere .= " and s.positivelog_time >='{$request['start_time']}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $clientwhere .= " and c.client_createtime <='{$endime}'";
            $positivelogwhere .= " and s.positivelog_time <='{$request['end_time']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
                $positivelogwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $positivelogwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $clientwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $positivelogwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== "") {
            $datawhere .= " and l.channel_medianame ='{$request['frommedia_name']}'";
        }
        if (isset($request['channel_id']) && $request['channel_id'] !== "") {
            $datawhere .= " and l.channel_id ='{$request['channel_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "l.channel_id,l.channel_medianame, l.channel_name,l.channel_board";
        $sql = "SELECT {$fields} FROM crm_code_channel AS l WHERE {$datawhere} ORDER BY l.channel_medianame ASC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->AnalyzeControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            if ($dateexcelarray) {
                foreach ($dateexcelarray as $key => $value) {
                    $dateexcelarray[$key]['section_name'] = $value['channel_board'];
                    $clientAllnum = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_allnum
FROM crm_client AS c WHERE {$clientwhere} AND c.channel_id = '{$value['channel_id']}'");
                    $dateexcelarray[$key]['channel_allnum'] = $clientAllnum['channel_allnum'];

                    $clientAllnum = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_effectivenum
FROM crm_client AS c WHERE {$clientwhere} AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus >= '-1' and c.client_isgross = '0'");
                    $dateexcelarray[$key]['channel_effectivenum'] = $clientAllnum['channel_effectivenum'];

                    $tmknumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_tmknum
FROM crm_client AS c WHERE {$clientwhere} AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus >= '0' AND c.outthree_userid <> ''");
                    $dateexcelarray[$key]['channel_tmknum'] = $tmknumOne['channel_tmknum'];

                    $tmkcallnumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_tmkcallnum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus > '1' AND c.outthree_userid <> ''");
                    $dateexcelarray[$key]['channel_tmkcallnum'] = $tmkcallnumOne['channel_tmkcallnum'];

                    if ($tmknumOne['channel_tmknum'] > 0) {
                        $dateexcelarray[$key]['channel_tmkcallrate'] = round(($tmkcallnumOne['channel_tmkcallnum'] / $tmknumOne['channel_tmknum']) * 100, 2) . "%";
                    } else {
                        $dateexcelarray[$key]['channel_tmkcallrate'] = "0.00%";
                    }

                    $schoolnumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_allnum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus >= '0' and c.client_isgross = '0' ");
                    $dateexcelarray[$key]['school_allnum'] = $schoolnumOne['school_allnum'];

                    $schoolvisitnumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_visitnum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}'
 AND c.client_tracestatus > '1' AND (
	c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_audition AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.audition_isvisit = '1'
	)
	OR c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_invite AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.invite_isvisit = '1'
	)
)");
                    $dateexcelarray[$key]['school_visitnum'] = $schoolvisitnumOne['school_visitnum'];

                    if ($schoolnumOne['school_allnum'] > 0) {
                        $dateexcelarray[$key]['school_visitrate'] = round(($schoolvisitnumOne['school_visitnum'] / $schoolnumOne['school_allnum']) * 100, 2) . "%";
                    } else {
                        $dateexcelarray[$key]['school_visitrate'] = "0.00%";
                    }

                    $schoolbookOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_booknum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus = '4' AND (
	c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_audition AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.audition_isvisit = '1'
	)
	OR c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_invite AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.invite_isvisit = '1'
	)
)");
                    $dateexcelarray[$key]['school_booknum'] = $schoolbookOne['school_booknum'];

                    if ($schoolvisitnumOne['school_visitnum'] > 0) {
                        $dateexcelarray[$key]['school_bookrate'] = round(($schoolbookOne['school_booknum'] / $schoolvisitnumOne['school_visitnum']) * 100, 2) . "%";
                    } else {
                        $dateexcelarray[$key]['school_bookrate'] = "0.00%";
                    }

                    $schoolbookOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_bookallnum
FROM crm_client AS c, crm_client_positivelog AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}'");
                    $dateexcelarray[$key]['school_bookallnum'] = $schoolbookOne['school_bookallnum'];

                    $schoolbookOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_totalnum
FROM crm_client AS c, crm_client_positivelog AS s, smc_school AS h
WHERE {$positivelogwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}'");

                    $dateexcelarray[$key]['school_totalnum'] = $schoolbookOne['school_totalnum'];
                }
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];
                    $datearray['section_name'] = $dateexcelvar['channel_board'];
                    $datearray['channel_allnum'] = $dateexcelvar['channel_allnum'];
                    $datearray['channel_effectivenum'] = $dateexcelvar['channel_effectivenum'];
                    $datearray['channel_tmknum'] = $dateexcelvar['channel_tmknum'];
                    $datearray['channel_tmkcallnum'] = $dateexcelvar['channel_tmkcallnum'];
                    $datearray['channel_tmkcallrate'] = $dateexcelvar['channel_tmkcallrate'];
                    $datearray['school_allnum'] = $dateexcelvar['school_allnum'];
                    $datearray['school_visitnum'] = $dateexcelvar['school_visitnum'];
                    $datearray['school_visitrate'] = $dateexcelvar['school_visitrate'];
                    $datearray['school_booknum'] = $dateexcelvar['school_booknum'];
                    $datearray['school_bookrate'] = $dateexcelvar['school_bookrate'];
                    $datearray['school_bookallnum'] = $dateexcelvar['school_bookallnum'];
                    $datearray['school_totalnum'] = $dateexcelvar['school_totalnum'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('渠道明细', '渠道类型', '渠道板块', '渠道总名单', '渠道有效名单', '电销有效名单', '电销诺访', '电销诺访率', '校区有效名单', '实际到访', '校区电转率', '校区签约数', '面转率', '区间报名总数', '历史累计报名数'));
            $excelfileds = array('channel_name', 'channel_medianame', 'section_name', 'channel_allnum', 'channel_effectivenum', 'channel_tmknum', 'channel_tmkcallnum', 'channel_tmkcallrate', 'school_allnum', 'school_visitnum', 'school_visitrate', 'school_booknum', 'school_bookrate', 'school_bookallnum', 'school_totalnum');

            $fielname = $this->LgStringSwitch("渠道业绩报表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $datalist = $this->AnalyzeControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    $datalist[$key]['section_name'] = $value['channel_board'];

                    $clientAllnum = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_allnum
FROM crm_client AS c WHERE {$clientwhere} AND c.channel_id = '{$value['channel_id']}'");
                    $datalist[$key]['channel_allnum'] = $clientAllnum['channel_allnum'];

                    $clientAllnum = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_effectivenum
FROM crm_client AS c WHERE {$clientwhere} AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus >= '-1' and c.client_isgross = '0'");
                    $datalist[$key]['channel_effectivenum'] = $clientAllnum['channel_effectivenum'];

                    $tmknumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_tmknum
FROM crm_client AS c WHERE {$clientwhere} AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus >= '0' AND c.outthree_userid <> ''");
                    $datalist[$key]['channel_tmknum'] = $tmknumOne['channel_tmknum'];

                    $tmkcallnumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as channel_tmkcallnum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus > '1' AND c.outthree_userid <> ''");
                    $datalist[$key]['channel_tmkcallnum'] = $tmkcallnumOne['channel_tmkcallnum'];

                    if ($tmknumOne['channel_tmknum'] > 0) {
                        $datalist[$key]['channel_tmkcallrate'] = round(($tmkcallnumOne['channel_tmkcallnum'] / $tmknumOne['channel_tmknum']) * 100, 2) . "%";
                    } else {
                        $datalist[$key]['channel_tmkcallrate'] = "0.00%";
                    }

                    $schoolnumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_allnum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus >= '0' and c.client_isgross = '0'");
                    $datalist[$key]['school_allnum'] = $schoolnumOne['school_allnum'];

                    $schoolvisitnumOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_visitnum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}'
 AND c.client_tracestatus > '1' AND (
	c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_audition AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.audition_isvisit = '1'
	)
	OR c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_invite AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.invite_isvisit = '1'
	)
)");
                    $datalist[$key]['school_visitnum'] = $schoolvisitnumOne['school_visitnum'];

                    if ($schoolnumOne['school_allnum'] > 0) {
                        $datalist[$key]['school_visitrate'] = round(($schoolvisitnumOne['school_visitnum'] / $schoolnumOne['school_allnum']) * 100, 2) . "%";
                    } else {
                        $datalist[$key]['school_visitrate'] = "0.00%";
                    }

                    $schoolbookOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_booknum
FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}' AND c.client_tracestatus = '4' AND (
	c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_audition AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.audition_isvisit = '1'
	)
	OR c.client_id IN (
		SELECT
			a.client_id
		FROM
			crm_client_invite AS a
		WHERE
			a.company_id = '{$request['company_id']}'
		AND a.invite_isvisit = '1'
	)
)");
                    $datalist[$key]['school_booknum'] = $schoolbookOne['school_booknum'];

                    if ($schoolvisitnumOne['school_visitnum'] > 0) {
                        $datalist[$key]['school_bookrate'] = round(($schoolbookOne['school_booknum'] / $schoolvisitnumOne['school_visitnum']) * 100, 2) . "%";
                    } else {
                        $datalist[$key]['school_bookrate'] = "0.00%";
                    }

                    $schoolbookOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_bookallnum
FROM crm_client AS c, crm_client_positivelog AS s, smc_school AS h
WHERE {$clientwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}'");
                    $datalist[$key]['school_bookallnum'] = $schoolbookOne['school_bookallnum'];

                    $schoolbookOne = $this->AnalyzeControl->selectOne("SELECT count(c.client_id) as school_totalnum
FROM crm_client AS c, crm_client_positivelog AS s, smc_school AS h
WHERE {$positivelogwhere} AND s.client_id = c.client_id AND h.school_id = s.school_id AND h.school_istest = '0' AND c.channel_id = '{$value['channel_id']}'");
                    $datalist[$key]['school_totalnum'] = $schoolbookOne['school_totalnum'];
                }
            } else {
                $datalist = array();
            }
            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->AnalyzeControl->selectOne("SELECT COUNT(l.channel_id) AS coutnums FROM crm_code_channel as l WHERE {$datawhere}");
            if ($allNum) {
                $data['allnum'] = $allNum['coutnums'];

            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }


    /**
     * TMK专用跟踪渠道
     * author: qizhugong
     * 对应接口文档 0001
     */
    function getTmkConsul($request)
    {
        $datawhere = "c.company_id = '{$request['company_id']}' AND ( a.outthree_bookid <> '' OR i.outthree_bookid <> '' )";
        if (isset($request['start_time']) && $request['start_time']) {
            $datawhere .= " and p.positivelog_time >='{$request['start_time']}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $datawhere .= " and p.positivelog_time <='{$request['end_time']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and s.school_id ='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            $datawhere .= " AND it.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
            $datawhere .= " AND it.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' OR c.client_mobile like '%{$request['keyword']}%') ";
        }
        //请勿改，改之前问
        if (isset($request['channel_id']) && $request['channel_id'] !== '' && $request['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($request['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== '' && $request['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_branch, s.school_cnname,
       (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
         (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname,
          c.client_cnname, p.student_branch, c.client_mobile, c.client_source,
	(SELECT l.channel_name FROM crm_code_channel AS l WHERE l.channel_id = c.channel_id) AS channel_name,
	p.positivelog_time, c.client_gmcmarket, c.client_soursename, c.client_remark, a.audition_visittime, i.invite_visittime, c.client_updatetime, c.client_createtime
	FROM crm_client AS c
LEFT JOIN crm_client_intention AS it ON it.client_id = c.client_id
LEFT JOIN crm_client_positivelog AS p ON p.client_id = c.client_id
LEFT JOIN smc_school AS s ON p.school_id = s.school_id
LEFT JOIN crm_client_audition AS a ON a.client_id = c.client_id
LEFT JOIN crm_client_invite AS i ON i.client_id = c.client_id
WHERE {$datawhere} GROUP BY c.client_id ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['positivelog_time'] = $dateexcelvar['positivelog_time'];
                    $datearray['client_gmcmarket'] = $dateexcelvar['client_gmcmarket'];
                    $datearray['client_soursename'] = $dateexcelvar['client_soursename'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];
                    $datearray['invite_visittime'] = $dateexcelvar['invite_visittime'];
                    $datearray['client_updatetime'] = date("Y-m-d H:i:s", $dateexcelvar['client_updatetime']);
                    $datearray['client_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['client_createtime']);
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('省份', '校区编号', '校区名称', '学员姓名', '学员编号', '手机号码',
                '渠道类型', '渠道明细', '报名登记日期', '录入人', '渠道备注', '学员备注', '试听时间', '柜询时间', '名单更新日期', '名单创建日期'));
            $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'client_cnname', 'student_branch',
                'client_mobile', 'client_source', 'channel_name', 'positivelog_time',
                'client_gmcmarket', 'client_soursename', 'client_remark', 'audition_visittime',
                'invite_visittime', 'client_updatetime', 'client_createtime');

            $fielname = $this->LgStringSwitch("TMK业绩明细表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            } else {
                foreach ($dataList as &$dataOne) {
                    $dataOne['client_updatetime'] = date("Y-m-d H:i:s", $dataOne['client_updatetime']);
                    $dataOne['client_createtime'] = date("Y-m-d H:i:s", $dataOne['client_createtime']);
                }
            }
            $data['list'] = $dataList;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->AnalyzeControl->selectOne("SELECT COUNT(DISTINCT c.client_id) as a FROM crm_client AS c
LEFT JOIN crm_client_intention AS it ON it.client_id = c.client_id
LEFT JOIN crm_client_positivelog AS p ON p.client_id = c.client_id LEFT JOIN smc_school AS s ON p.school_id = s.school_id
LEFT JOIN crm_client_audition AS a ON a.client_id = c.client_id LEFT JOIN crm_client_invite AS i ON i.client_id = c.client_id
WHERE {$datawhere}");
            if ($allNum) {
                $data['allnum'] = $allNum['a'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    /**
     * TMK坐席业绩统计表
     * author: lijiuqi
     */
    function getTmkSeatsAchievement($request)
    {
        $datawhere = " p.staffer_id = s.staffer_id and s.staffer_id = m.staffer_id and p.postbe_isgmccrm = '1' and s.company_id = '{$request['company_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.staffer_branch like '%{$request['keyword']}%' OR s.staffer_cnname like '%{$request['keyword']}%' OR s.staffer_enname like '%{$request['keyword']}%') ";
        }
        $sonwhere = '';
        $addnewwhere = '';//新增分配名单
        $toschwhere = '';//邀约至校区名单数
        $istoschwhere = '';//邀约到访名单数
        $nointenwhere = '';//新增无意向名单
        $invalidwhere = '';//新增无效名单
        $signupwhere = '';//报名名单
        if (isset($request['start_time']) && $request['start_time']) {
            $start_time = strtotime($request['start_time']);
            $addnewwhere = " and p.principal_createtime >='{$start_time}'";
            $toschwhere = " and t.track_createtime >='{$start_time}'";
            $istoschwhere = " and t.track_createtime >='{$start_time}'";
            $nointenwhere = " and (SELECT t.track_id from crm_client_track as t WHERE t.client_id = c.client_id and (t.track_followmode ='-1' or t.track_followmode ='-2')  and t.track_createtime >= '{$start_time}' limit 0,1) is not null ";
            $invalidwhere = " and (SELECT t.track_id from crm_client_track as t WHERE t.client_id = c.client_id and t.track_followmode ='-3'  and t.track_createtime >= '{$start_time}'  limit 0,1) is not null ";
            $signupwhere = " and (select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where d.from_client_id = c.client_id and f.pay_successtime >= '{$start_time}'  limit 0,1 ) is not null ";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $end_time = strtotime($request['end_time']) + 86399;
            $addnewwhere .= " and p.principal_createtime <='{$end_time}'";
            $toschwhere .= " and t.track_createtime <='{$end_time}'";
            $istoschwhere .= " and t.track_createtime <='{$end_time}'";
            $nointenwhere .= " and (SELECT t.track_id from crm_client_track as t WHERE t.client_id = c.client_id and (t.track_followmode ='-1' or t.track_followmode ='-2')  and t.track_createtime <= '{$end_time}'  limit 0,1) is not null ";
            $invalidwhere .= " and (SELECT t.track_id from crm_client_track as t WHERE t.client_id = c.client_id and t.track_followmode ='-3'  and t.track_createtime <= '{$end_time}'  limit 0,1) is not null";
            $signupwhere .= " and (select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where d.from_client_id = c.client_id and f.pay_successtime <= '{$end_time}'  limit 0,1 ) is not null ";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT p.staffer_id,s.staffer_branch,s.staffer_cnname,s.staffer_enname,m.marketer_id
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' AND p.principal_leave = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' ) ) as addnum
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' 	OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) {$addnewwhere}) as addnewnum 
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p ,crm_client_track t WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' 	OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) AND c.client_id = t.client_id AND t.track_type = '1' AND ( t.track_followmode = '1' OR t.track_followmode = '2' ) AND t.school_id > 0  {$toschwhere}) as toschnum   
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p ,crm_client_track t WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' 	OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) AND c.client_id = t.client_id AND t.track_type = '1' AND ( t.track_followmode = '1' OR t.track_followmode = '2' ) AND t.school_id > 0 and (  (SELECT i.invite_id from crm_client_invite as i WHERE i.track_id = t.track_id and i.invite_isvisit = '1') > 0 or (SELECT a.audition_id from crm_client_audition as a WHERE a.track_id = t.track_id and a.audition_isvisit = '1') > 0 )  {$istoschwhere}) as istoschnum 
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' 	OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) and c.client_tracestatus = '-1' {$nointenwhere}) as nointennum  
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' 	OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) and c.client_tracestatus = '-2' {$invalidwhere}) as invalidnum  
            ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c, crm_client_principal p WHERE c.company_id = '{$request['company_id']}' AND c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = m.marketer_id  and (( (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1'  AND st.is_gmcdirectschool = '0' order by st.schoolenter_id desc limit 0,1 ) is not null) or c.client_gmcdistributionstatus = '1' 	OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) and c.client_tracestatus = '4' {$signupwhere}) as signupnum  
                FROM gmc_staffer_postbe p,smc_staffer s,crm_marketer m  
                WHERE {$datawhere} 
                group by p.staffer_id";
        // AND p.principal_leave = '0' 删除了
        // 添加了 OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    if ($dateexcelvar['staffer_enname']) {
                        $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'] . "(" . $dateexcelvar['staffer_enname'] . ")";
                    } else {
                        $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    }
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['addnum'] = $dateexcelvar['addnum'];
                    $datearray['addnewnum'] = $dateexcelvar['addnewnum'];
                    $datearray['toschnum'] = $dateexcelvar['toschnum'];
                    $datearray['istoschnum'] = $dateexcelvar['istoschnum'];
                    $datearray['nointennum'] = $dateexcelvar['nointennum'];
                    $datearray['invalidnum'] = $dateexcelvar['invalidnum'];
                    $datearray['signupnum'] = $dateexcelvar['signupnum'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('职工姓名', '职工编号', '当前名单数量', '新增分配名单', '邀约至校区名单数', '邀约到访名单数', '新增无意向名单', '新增无效名单', '报名名单'));
            $excelfileds = array('staffer_cnname', 'staffer_branch', 'addnum', 'addnewnum', 'toschnum', 'istoschnum', 'nointennum', 'invalidnum', 'signupnum');

            $fielname = $this->LgStringSwitch("TMK坐席业绩统计表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            } else {
                foreach ($dataList as &$dataOne) {
                    if ($dataOne['staffer_enname']) {
                        $dataOne['staffer_cnname'] = $dataOne['staffer_cnname'] . "(" . $dataOne['staffer_enname'] . ")";
                    }
                }
            }
            $data['list'] = $dataList;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectClear("SELECT p.staffer_id FROM gmc_staffer_postbe p,smc_staffer s,crm_marketer m WHERE {$datawhere} group by  p.staffer_id");
            if (is_array($allNum)) {
                $data['allnum'] = count($allNum);
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    /**
     * TMK渠道业绩转化表
     * author: lijiuqi
     */
    function getTmkChannelAchievement($request)
    {
        //有权限的人
        $postbesql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id 
            left join smc_staffer as s ON s.staffer_id = p.staffer_id 
            WHERE p.postbe_isgmccrm = 1  and  m.marketer_id <> '' and s.staffer_istest = '0' ";
        $markertList = $this->DataControl->selectClear($postbesql);
        $arr_marketer_id = array_column($markertList, "marketer_id");
        $str_marketer_id = implode(',', $arr_marketer_id);
        //包含管理员
        $accountmarker = $this->DataControl->selectClear("select m.marketer_id from smc_staffer as s 
                            left join crm_marketer as m ON s.staffer_id = m.staffer_id 
                            where s.account_class = '1' and s.company_id = '{$request['company_id']}' and s.staffer_leave = '0' ");
        if (is_array($accountmarker)) {
            $arr_accountmarketer_id = array_column($accountmarker, "marketer_id");
            $str_accountmarketer_id = implode(',', $arr_accountmarketer_id);
            $str_marketer_id .= "," . $str_accountmarketer_id;
        }

        $datawhere = " l.channel_medianame = f.frommedia_name and l.company_id = '{$request['company_id']}' and f.company_id = '{$request['company_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.staffer_branch like '%{$request['keyword']}%' OR s.staffer_cnname like '%{$request['keyword']}%' OR s.staffer_enname like '%{$request['keyword']}%') ";
        }
        $allwhere = '';//新增毛名单数
        $allvalidwhere = '';//新增有效名单
        $addallwhere = '';//新增分配TMK派发量
        $addvalidwhere = '';//新增分配TMK有效量
        $toschwhere = '';//TMK邀约名单数
        $istoschwhere = '';//TMK到访名单数
        $signupwhere = '';//TMK报名名单数
        if (isset($request['start_time']) && $request['start_time']) {
            $start_time = strtotime($request['start_time']);
            $allwhere = " and c.client_createtime >='{$start_time}'";
            $allvalidwhere = " and c.client_createtime >='{$start_time}'";
            $addallwhere = " and p.principal_createtime >='{$start_time}'";
            $addvalidwhere = " and p.principal_createtime >='{$start_time}'";
            $toschwhere = " and t.track_createtime >='{$start_time}'";
            $istoschwhere = " and t.track_createtime >='{$start_time}'";
            $signupwhere = " and (select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where d.from_client_id = c.client_id and f.pay_successtime >= '{$start_time}'  limit 0,1 ) is not null ";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $end_time = strtotime($request['end_time']) + 86399;
            $allwhere .= " and c.client_createtime <='{$end_time}'";
            $allvalidwhere .= " and c.client_createtime <='{$end_time}'";
            $addallwhere .= " and p.principal_createtime <='{$end_time}'";
            $addvalidwhere .= " and p.principal_createtime <='{$end_time}'";
            $toschwhere .= " and t.track_createtime <='{$end_time}'";
            $istoschwhere .= " and t.track_createtime <='{$end_time}'";
            $signupwhere .= " and (select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where d.from_client_id = c.client_id and f.pay_successtime <= '{$end_time}'  limit 0,1 ) is not null ";
        }
        if (isset($request['channel_id']) && $request['channel_id'] !== '' && $request['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($request['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and l.channel_id in ({$channelstr}) ";
            }
        }
        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== '' && $request['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and l.channel_medianame in ({$commodestr}) ";
            }
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        //,(select count(c.client_id) from crm_client as c where c.channel_id = l.channel_id {$allwhere}) as allnum
        //,(select count(c.client_id) from crm_client as c where c.channel_id = l.channel_id and c.client_tracestatus >= '0' {$allvalidwhere}) as allvalidnum
        $sql = "SELECT l.channel_name,l.channel_medianame
                ,(SELECT COUNT(DISTINCT(c.client_id)) FROM crm_client as c  WHERE c.channel_id = l.channel_id  {$allwhere}
                    and not exists (select 1 from crm_client_schoolenter as a where a.client_id = c.client_id and a.is_enterstatus = '1' and a.school_id > 0 ) )as allone
                ,(SELECT COUNT(DISTINCT(c.client_id)) FROM crm_client as c  WHERE c.channel_id = l.channel_id  {$allwhere}
                    and exists (select 1 from crm_client_schoolenter as a where a.client_id = c.client_id and a.is_enterstatus = '1' and a.school_id > 0 )
                    and exists (select 1 from crm_client_principal as a where a.client_id = c.client_id and a.marketer_id in ('3268','4404','4500','201')) )as alltwo
                ,(SELECT COUNT(DISTINCT(c.client_id)) FROM crm_client as c  WHERE c.channel_id = l.channel_id and c.client_intention_maxlevel >= '3' {$allvalidwhere}
                    and not exists (select 1 from crm_client_schoolenter as a where a.client_id = c.client_id and a.is_enterstatus = '1' and a.school_id > 0 ) )as allvalidone
                ,(SELECT COUNT(DISTINCT(c.client_id)) FROM crm_client as c  WHERE c.channel_id = l.channel_id and c.client_intention_maxlevel >= '3' {$allvalidwhere}
                    and exists (select 1 from crm_client_schoolenter as a where a.client_id = c.client_id and a.is_enterstatus = '1' and a.school_id > 0 )
                    and exists (select 1 from crm_client_principal as a where a.client_id = c.client_id and a.marketer_id in ('3268','4404','4500','201')) )as allvalidtwo
                
                ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c,crm_client_principal p WHERE c.channel_id = l.channel_id and c.client_id = p.client_id AND p.school_id = '0' AND c.company_id = '{$request['company_id']}' AND p.marketer_id in ({$str_marketer_id}) AND ( c.client_gmcdistributionstatus = '1' OR (SELECT st.schoolenter_id FROM crm_client_schoolenter AS st WHERE st.client_id = c.client_id AND st.company_id = c.company_id AND st.is_enterstatus = '1' AND st.is_gmctocrmschool = '1' AND st.is_gmcdirectschool = '0'  ORDER BY st.schoolenter_id DESC LIMIT 0, 1 ) IS NOT NULL OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) {$addallwhere}) as addallnum 
                ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c,crm_client_principal p WHERE c.channel_id = l.channel_id and c.client_id = p.client_id  AND p.school_id = '0' AND c.company_id = '{$request['company_id']}' AND p.marketer_id in ({$str_marketer_id}) AND ( c.client_gmcdistributionstatus = '1' OR (SELECT st.schoolenter_id FROM crm_client_schoolenter AS st WHERE st.client_id = c.client_id AND st.company_id = c.company_id AND st.is_enterstatus = '1' AND st.is_gmctocrmschool = '1' AND st.is_gmcdirectschool = '0' ORDER BY st.schoolenter_id DESC LIMIT 0, 1 ) IS NOT NULL OR (SELECT t.track_id FROM crm_client_track as t WHERE c.client_id = t.client_id  and (t.track_followmode = '-1' or t.track_followmode = '-2' or t.track_followmode = '-3') LIMIT 0,1) IS NOT NULL) {$addvalidwhere} and c.client_tracestatus <> '-2') as addvalidnum 
                ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c,crm_client_principal p,crm_client_track t WHERE c.channel_id = l.channel_id and c.client_id = p.client_id AND p.school_id = '0' AND c.company_id = '{$request['company_id']}' AND p.marketer_id in ({$str_marketer_id}) AND ( c.client_gmcdistributionstatus = '1' OR (SELECT st.schoolenter_id FROM crm_client_schoolenter AS st WHERE st.client_id = c.client_id AND st.company_id = c.company_id AND st.is_enterstatus = '1' AND st.is_gmctocrmschool = '1' AND st.is_gmcdirectschool = '0'  ORDER BY st.schoolenter_id DESC LIMIT 0, 1 ) IS NOT NULL ) AND c.client_id = t.client_id AND t.track_type = '1' AND ( t.track_followmode = '1' OR t.track_followmode = '2' ) AND t.school_id > 0 {$toschwhere}) as toschnum 
                ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c,crm_client_principal p,crm_client_track t WHERE c.channel_id = l.channel_id and c.client_id = p.client_id AND p.school_id = '0' AND c.company_id = '{$request['company_id']}' AND p.marketer_id in ({$str_marketer_id}) AND ( c.client_gmcdistributionstatus = '1' OR (SELECT st.schoolenter_id FROM crm_client_schoolenter AS st WHERE st.client_id = c.client_id AND st.company_id = c.company_id AND st.is_enterstatus = '1' AND st.is_gmctocrmschool = '1' AND st.is_gmcdirectschool = '0'  ORDER BY st.schoolenter_id DESC LIMIT 0, 1 ) IS NOT NULL ) AND c.client_id = t.client_id AND t.track_type = '1' AND ( t.track_followmode = '1' OR t.track_followmode = '2' ) AND t.school_id > 0 and (  (SELECT i.invite_id from crm_client_invite as i WHERE i.track_id = t.track_id and i.invite_isvisit = '1') > 0 or (SELECT a.audition_id from crm_client_audition as a WHERE a.track_id = t.track_id and a.audition_isvisit = '1') > 0 ){$istoschwhere}) as istoschnum 
                ,(SELECT COUNT(DISTINCT(c.client_id)) as a FROM crm_client c,crm_client_principal p WHERE c.channel_id = l.channel_id and c.client_id = p.client_id AND p.school_id = '0' AND c.company_id = '{$request['company_id']}' AND p.marketer_id in ({$str_marketer_id}) AND ( c.client_gmcdistributionstatus = '1' OR (SELECT st.schoolenter_id FROM crm_client_schoolenter AS st WHERE st.client_id = c.client_id AND st.company_id = c.company_id AND st.is_enterstatus = '1' AND st.is_gmctocrmschool = '1' AND st.is_gmcdirectschool = '0'  ORDER BY st.schoolenter_id DESC LIMIT 0, 1 ) IS NOT NULL ) and c.client_tracestatus = '4' {$signupwhere}) as signupnum 
                FROM crm_code_channel l,crm_code_frommedia f 
                WHERE {$datawhere}";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {

                    $dateexcelvar['allnum'] = $dateexcelvar['allone']+$dateexcelvar['alltwo'];
                    $dateexcelvar['allvalidnum'] = $dateexcelvar['allvalidone']+$dateexcelvar['allvalidtwo'];

                    $datearray = array();
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道明细
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];//所属渠道类型
                    $datearray['allnum'] = $dateexcelvar['allnum'];//新增毛名单数
                    $datearray['allvalidnum'] = $dateexcelvar['allvalidnum'];//新增有效名单
                    $datearray['addallnum'] = $dateexcelvar['addallnum'];//新增分配TMK派发量
                    $datearray['addvalidnum'] = $dateexcelvar['addvalidnum'];//新增分配TMK有效量
                    $datearray['toschnum'] = $dateexcelvar['toschnum'];//TMK邀约名单数
                    $datearray['istoschnum'] = $dateexcelvar['istoschnum'];//TMK到访名单数
                    $datearray['signupnum'] = $dateexcelvar['signupnum'];//TMK报名名单数
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('渠道明细', '所属渠道类型', '新增毛名单数', '新增有效名单', '新增分配TMK派发量', '新增分配TMK有效量', 'TMK邀约名单数', 'TMK到访名单数', 'TMK报名名单数'));
            $excelfileds = array('channel_name', 'channel_medianame', 'allnum', 'allvalidnum', 'addallnum', 'addvalidnum', 'toschnum', 'istoschnum', 'signupnum');

            $fielname = $this->LgStringSwitch("TMK渠道业绩转化表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            } else {
                foreach ($dataList as &$dataOne) {
                    $dataOne['allnum'] = $dataOne['allone']+$dataOne['alltwo'];
                    $dataOne['allvalidnum'] = $dataOne['allvalidone']+$dataOne['allvalidtwo'];
                }
            }
            $data['list'] = $dataList;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->AnalyzeControl->selectOne("SELECT COUNT(l.channel_id) as a FROM crm_code_channel l,crm_code_frommedia f WHERE {$datawhere}");
            if ($allNum) {
                $data['allnum'] = $allNum['a'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    function trackDetailByLinkType($request)
    {
        $datawhere = "a.track_isactive = '1' AND c.company_id = '{$request['company_id']}' ";

        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $datawhere .= " and a.track_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and a.track_createtime <='{$endime}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and b.school_id ='{$request['school_id']}'";
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $filenamehead = $schoolOne['school_cnname'];
        } else {
            $datawhere .= " and b.school_istest ='0' and b.school_isclose='0' ";
            $filenamehead = '';
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' 
            or c.client_enname like '%{$request['keyword']}%'  
            or c.client_mobile like '%{$request['keyword']}%' ) ";
        }

        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus = '{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['track_linktype']) && $request['track_linktype'] !== "") {
            $datawhere .= " and e.commode_id ='{$request['track_linktype']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select b.school_branch,b.school_shortname as school_cnname,
           (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = b.school_province ) as province_name,
           (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = b.district_id) AS district_cnname,
            b.school_id,c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_createtime,
            c.client_source,d.channel_name,
            FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') as client_createtime,
            (SELECT concat(y.marketer_name,(CASE WHEN ifnull( z.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', z.staffer_enname)END)) 
            FROM crm_client_principal as x,crm_marketer as y ,smc_staffer as z 
            WHERE z.staffer_id=y.staffer_id and y.marketer_id = x.marketer_id and x.client_id =c.client_id and principal_ismajor = 1 and principal_leave =0 
            order by x.principal_createtime DESC limit 0,1 ) AS main_marketer_name,
            (SELECT  group_concat(concat(y.marketer_name,(CASE WHEN ifnull( z.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', z.staffer_enname)END))) 
            FROM crm_client_principal as x,crm_marketer as y ,smc_staffer as z 
            WHERE z.staffer_id=y.staffer_id and y.marketer_id = x.marketer_id and x.client_id =c.client_id and principal_ismajor = 0 and principal_leave =0 
            order by x.principal_createtime DESC limit 0,1 ) AS sub_marketer_name,
            c.client_tracestatus,
            a.track_linktype,
            FROM_UNIXTIME(a.track_createtime) as track_createtime,
            a.track_note,
            (SELECT group_concat(CONCAT(from_unixtime(ct.track_createtime, '%m-%d'),' 跟踪：',ct.track_note,'#') ORDER BY ct.track_id DESC) 
            FROM crm_client_track AS ct WHERE ct.client_id = c.client_id AND ct.track_validinc=1 and ct.track_isactive = '1') AS track_detail,
            c.client_remark
            from crm_client_track a 
            left join smc_school b on a.school_id=b.school_id 
            left join crm_client c on a.client_id=c.client_id
            left join crm_code_channel d on c.channel_id=d.channel_id
            left join crm_code_commode e on c.company_id=e.company_id and a.track_linktype=e.commode_name
            where {$datawhere} 
            and a.track_followmode in('0','1','2','-1','-3')
            order by b.school_branch,a.track_createtime DESC ";

        //跟踪状态：0待跟踪1持续跟踪2已柜询3已试听4已转正-1已无意向
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($request['is_export']) && $request['is_export'] == 1) {

            ini_set("memory_limit", '-1');
            set_time_limit(600);

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无沟通类型明细数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {

                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    if ($filenamehead == '') {
                        $datearray['province_name'] = $dateexcelvar['province_name'];
                        $datearray['school_branch'] = $dateexcelvar['school_branch'];
                        $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                        $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                        $datearray['client_enname'] = $dateexcelvar['client_enname'];
                        $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                        $datearray['channel_name'] = $dateexcelvar['channel_name'];

                        $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                        $datearray['main_marketer_name'] = $dateexcelvar['main_marketer_name'];
                        $datearray['sub_marketer_name'] = $dateexcelvar['sub_marketer_name'];
                        $datearray['client_tracestatus'] = $clientTracestatus[$dateexcelvar['client_tracestatus']];
                        $datearray['track_linktype'] = $dateexcelvar['track_linktype'];
                        $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                        $datearray['track_note'] = $dateexcelvar['track_note'];
                        $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    } else {
                        $datearray['client_id'] = $dateexcelvar['client_id'];
                        $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                        $datearray['client_enname'] = $dateexcelvar['client_enname'];
                        $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);

                        $datearray['client_source'] = $dateexcelvar['client_source'];
                        $datearray['channel_name'] = $dateexcelvar['channel_name'];
                        $datearray['main_marketer_name'] = $dateexcelvar['main_marketer_name'];
                        if ($this->company_isassist == 1) {
                            $datearray['sub_marketer_name'] = $dateexcelvar['sub_marketer_name'];
                        }
                        $datearray['client_tracestatus'] = $clientTracestatus[$dateexcelvar['client_tracestatus']];
                        $datearray['track_linktype'] = $dateexcelvar['track_linktype'];
                        $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                        $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                        $datearray['track_detail'] = $dateexcelvar['track_detail'];
                        $datearray['track_note'] = $dateexcelvar['track_note'];
                        $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    }

                    $outexceldate[] = $datearray;
                }
            }

            if ($this->company_isassist == 1) {
                if ($filenamehead == '') {
                    $excelheader = $this->LgArraySwitch(array('省份', '校区编号', '校区名称', '中文名', '英文名', '联系手机', '招生渠道明细', '创建时间', '主要负责人', '协助负责人', '客户状态', '沟通类型', '跟踪时间', '跟踪明细', '学员备注'));
                    $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'client_cnname', 'client_enname', 'client_mobile', 'channel_name', 'client_createtime', 'main_marketer_name', 'sub_marketer_name', 'client_tracestatus', 'track_linktype', 'track_createtime', 'track_note', 'client_remark');
                } else {
                    $excelheader = $this->LgArraySwitch(array('会员序号', '中文名', '英文名', '联系手机', '招生渠道类型', '招生渠道明细', '主要负责人', '协助负责人', '客户状态', '沟通类型', '跟踪时间', '创建时间', '有效跟踪明细', '跟踪备注', '学员备注'));
                    $excelfileds = array('client_id', 'client_cnname', 'client_enname', 'client_mobile', 'client_source', 'channel_name', 'main_marketer_name', 'sub_marketer_name', 'client_tracestatus', 'track_linktype', 'track_createtime', 'client_createtime', 'track_detail', 'track_note', 'client_remark');
                }
            } else {
                if ($filenamehead == '') {
                    $excelheader = $this->LgArraySwitch(array('省份', '校区编号', '校区名称', '中文名', '英文名', '联系手机', '招生渠道明细', '创建时间', '主要负责人', '客户状态', '沟通类型', '跟踪时间', '跟踪明细', '学员备注'));
                    $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'client_cnname', 'client_enname', 'client_mobile', 'channel_name', 'client_createtime', 'main_marketer_name', 'client_tracestatus', 'track_linktype', 'track_createtime', 'track_note', 'client_remark');
                } else {
                    $excelheader = $this->LgArraySwitch(array('会员序号', '中文名', '英文名', '联系手机', '招生渠道类型', '招生渠道明细', '主要负责人', '客户状态', '沟通类型', '跟踪时间', '创建时间', '有效跟踪明细', '跟踪备注', '学员备注'));
                    $excelfileds = array('client_id', 'client_cnname', 'client_enname', 'client_mobile', 'client_source', 'channel_name', 'main_marketer_name', 'client_tracestatus', 'track_linktype', 'track_createtime', 'client_createtime', 'track_detail', 'track_note', 'client_remark');
                }
            }

            $fielname = $this->LgStringSwitch($filenamehead . "沟通类型明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}~{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";

            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {

                foreach ($datalist as $key => $value) {
                    $datalist[$key]['client_tracestatus'] = $clientTracestatus[$value['client_tracestatus']];
                    $datalist[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                }
            } else {
                $datalist = array();
            }

            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectClear("select a.track_id
                from crm_client_track a 
                left join smc_school b on a.school_id=b.school_id 
                left join crm_client c on a.client_id=c.client_id 
                left join crm_code_channel d on c.channel_id=d.channel_id 
                left join crm_code_commode e on c.company_id=e.company_id and a.track_linktype=e.commode_name 
                where  {$datawhere} 
                and a.track_followmode in('0','1','2','-1','-3')");
            if ($allNum) {
                $data['allnum'] = count($allNum);
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    function getStuSponsorPositive($request)
    {
        $datawhere = "c.company_id = '{$request['company_id']}'";

        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $datawhere = "i.pay_successtime > '{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $datawhere = "i.pay_successtime <= '{$endime}'";

        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and i.school_id ='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or t.student_enname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%' ) ";
        }

        $sql = "select t.student_cnname,t.student_id,t.student_enname,t.student_branch,c.client_mobile,l.channel_name,l.channel_medianame,i.pay_successtime,info_createtime,sl.school_cnname,sl.school_branch
            from smc_student_registerinfo as i
            left join smc_student as t ON i.student_id=t.student_id
            left join crm_client as c ON c.client_stubranch=t.student_branch 
            left join crm_code_channel as l ON l.channel_id=c.channel_id
            left join smc_school as sl ON sl.school_id=i.school_id
            where {$datawhere} ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['is_export']) && $request['is_export'] == 1) {

            ini_set("memory_limit", '-1');
            set_time_limit(600);

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {

                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['pay_successtime'] = date("Y-m-d", $dateexcelvar['pay_successtime']);
                    $datearray['info_createtime'] = date("Y-m-d", $dateexcelvar['pay_successtime']);
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("中文名", "英文名", "学生编号", "报名校区编号", "报名校区名称", "联系手机", "渠道类型", "招生渠道明细", "报名时间", "添加时间"));
            $excelfileds = array("student_cnname", "student_enname", "student_branch", "school_branch", "school_cnname", "client_mobile", "channel_medianame", "channel_name", "pay_successtime", "info_createtime");

            $fielname = $this->LgStringSwitch("学员转介绍明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}~{$request['end_time']}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
        }

        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as $key => $val) {
                $dataList[$key]['pay_successtime'] = date("Y-m-d", $val['pay_successtime']);
                $dataList[$key]['info_createtime'] = date("Y-m-d", $val['info_createtime']);
            }
        }


        $all_num = $this->DataControl->selectOne("select count(i.student_id) as num
            from smc_student_registerinfo as i
            left join smc_student as t ON i.student_id=t.student_id
            left join crm_client as c ON c.client_stubranch=t.student_branch 
            left join crm_code_channel as l ON l.channel_id=c.channel_id
            where {$datawhere}");
        $all_num = $all_num['num'] + 0;
        $result = array();
        $result['allnum'] = $all_num;
        $result['list'] = $dataList;
        return $result;
    }

    //TMK邀约跟踪状况表
    function getTmkInviteTrack($request)
    {
        ////20220810 注释负责人有问题
//        $datawhere = " c.company_id = '{$request['company_id']}' and c.client_id = e.client_id and c.company_id = e.company_id  and e.is_enterstatus = '1' and e.is_gmctocrmschool = '1' and e.is_gmcdirectschool = '0' and e.schoolenter_id > 0 and e.schoolenter_id is not null and c.client_id = p.client_id and p.school_id = '0'  and p.marketer_id = m.marketer_id and m.staffer_id = s.staffer_id and c.channel_id = l.channel_id ";//and e.school_id = '{$request['school_id']}'    ---and p.principal_leave = '0'

        $datawhere = " c.company_id = '{$request['company_id']}' and c.client_id = e.client_id and c.company_id = e.company_id  and e.is_enterstatus = '1' and e.is_gmctocrmschool = '1' and e.is_gmcdirectschool = '0' and e.schoolenter_id > 0 and e.schoolenter_id is not null  and c.channel_id = l.channel_id ";//and c.client_id = p.client_id and p.school_id = '0'  and p.marketer_id = m.marketer_id and m.staffer_id = s.staffer_id

        if (isset($request['start_time']) && $request['start_time']) {
            $start_time = strtotime($request['start_time']);
            $datawhere .= " and c.client_createtime >='{$start_time}'";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $end_time = strtotime($request['end_time']) + 86399;
            $datawhere .= " and c.client_createtime <='{$end_time}'";
        }
        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}' ";
        }
        if (isset($request['channel_id']) && $request['channel_id'] !== '' && $request['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($request['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and l.channel_id in ({$channelstr}) ";
            }
        }
        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== '' && $request['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($request['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and l.channel_medianame in ({$commodestr}) ";
            }
        }
        $having = "1 =1";
        if (isset($request['invite_start_time']) && $request['invite_start_time']) {
            $invite_start_time = strtotime($request['invite_start_time']);
            $having .= " and first_track_createtime >= '{$invite_start_time}' ";
        }
        if (isset($request['invite_end_time']) && $request['invite_end_time']) {
            $invite_end_time = strtotime($request['invite_end_time']) + 86399;
            $having .= " and first_track_createtime <= '{$invite_end_time}' ";
        }
        if (isset($request['isinvite']) && $request['isinvite'] == '1') {
            $having .= " and invite_id > '0' ";
        } elseif (isset($request['isinvite']) && $request['isinvite'] == '0') {
            $having .= " and invite_id is null ";
        }
        if (isset($request['isaudition']) && $request['isaudition'] == '1') {
            $having .= " and audition_id > '0' ";
        } elseif (isset($request['isaudition']) && $request['isaudition'] == '0') {
            $having .= " and audition_id is null ";
        }
        if (isset($request['start_visittime']) && $request['start_visittime'] != '') {
            $start_visittime = date("Y-m-d H:i:s", strtotime($request['start_visittime']));
            $having .= " and visittime >= '{$start_visittime}' ";
        }
        if (isset($request['end_visittime']) && $request['end_visittime'] != '') {
            $end_visittime = date("Y-m-d H:i:s", strtotime($request['end_visittime']) + 86399);
            $having .= " and visittime <= '{$end_visittime}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname like '%{$request['keyword']}%' or c.client_enname like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%' ) ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_tracestatus,l.channel_medianame,l.channel_name,c.client_createtime,
        (select s.staffer_cnname from crm_client_principal as p,crm_marketer as m,smc_staffer as s where c.client_id = p.client_id AND p.school_id = '0' AND p.marketer_id = m.marketer_id AND m.staffer_id = s.staffer_id order by p.principal_leave ASC limit 0,1) as staffer_cnname,
        (SELECT t.track_createtime FROM crm_client_track as t WHERE t.client_id = c.client_id  and track_followmode in ('1','2') ORDER BY track_createtime ASC LIMIT 0,1) as first_track_createtime,
        (SELECT h.school_cnname FROM smc_school as h WHERE h.school_id = e.school_id ) as school_cnname,
        (SELECT t.track_createtime FROM crm_client_track as t WHERE t.client_id = c.client_id ORDER BY track_id desc LIMIT 0,1) as last_track_createtime,
        (SELECT t.track_note FROM crm_client_track as t WHERE t.client_id = c.client_id ORDER BY track_id desc LIMIT 0,1) as last_track_note,
        (SELECT if(f.staffer_enname<>'',concat(f.staffer_cnname,'/',f.staffer_enname),f.staffer_cnname) FROM crm_client_track as t,crm_marketer as k,smc_staffer as f WHERE t.client_id = c.client_id and t.marketer_id = k.marketer_id and k.staffer_id =f.staffer_id ORDER BY track_id desc LIMIT 0,1) as last_staffer_cnname,
        (SELECT i.invite_id FROM crm_client_invite as i WHERE i.client_id = c.client_id and i.invite_isvisit = '1' LIMIT 0,1) as invite_id,
        (SELECT i.audition_id FROM crm_client_audition as i WHERE i.client_id = c.client_id  and i.audition_isvisit = '1' LIMIT 0,1) as audition_id,
        (SELECT i.conversionlog_id FROM crm_client_conversionlog as i WHERE i.client_id = c.client_id and i.school_id = e.school_id and i.student_branch <> '' LIMIT 0,1) as conversionlog_id,
        (SELECT sf.info_id FROM smc_student AS st,smc_student_registerinfo as sf WHERE st.from_client_id = c.client_id AND st.company_id = c.company_id	AND st.student_id = sf.student_id and sf.info_status = '1' LIMIT 0,1) as pay_order_id,
        (select g.visittime from view_crm_invitelog as g where g.client_id=c.client_id and g.school_id = e.school_id and g.isvisit = '1' order by g.visittime desc limit 0,1 ) as visittime 
        FROM crm_client as c,crm_client_schoolenter as e,crm_code_channel as l 
        WHERE {$datawhere} group by e.school_id,c.client_id HAVING {$having} ";//s.staffer_cnname,s.staffer_enname,

//        //20220810 注释负责人有问题
//        $sql = "SELECT c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_tracestatus,l.channel_medianame,l.channel_name,c.client_createtime,s.staffer_cnname,s.staffer_enname,
//        (SELECT t.track_createtime FROM crm_client_track as t WHERE t.client_id = c.client_id  and track_followmode in ('1','2') ORDER BY track_createtime ASC LIMIT 0,1) as first_track_createtime,
//        (SELECT h.school_cnname FROM smc_school as h WHERE h.school_id = e.school_id ) as school_cnname,
//        (SELECT t.track_createtime FROM crm_client_track as t WHERE t.client_id = c.client_id ORDER BY track_id desc LIMIT 0,1) as last_track_createtime,
//        (SELECT t.track_note FROM crm_client_track as t WHERE t.client_id = c.client_id ORDER BY track_id desc LIMIT 0,1) as last_track_note,
//        (SELECT if(f.staffer_enname<>'',concat(f.staffer_cnname,'/',f.staffer_enname),f.staffer_cnname) FROM crm_client_track as t,crm_marketer as k,smc_staffer as f WHERE t.client_id = c.client_id and t.marketer_id = k.marketer_id and k.staffer_id =f.staffer_id ORDER BY track_id desc LIMIT 0,1) as last_staffer_cnname,
//        (SELECT i.invite_id FROM crm_client_invite as i WHERE i.client_id = c.client_id and i.invite_isvisit = '1' LIMIT 0,1) as invite_id,
//        (SELECT i.audition_id FROM crm_client_audition as i WHERE i.client_id = c.client_id  and i.audition_isvisit = '1' LIMIT 0,1) as audition_id,
//        (SELECT i.conversionlog_id FROM crm_client_conversionlog as i WHERE i.client_id = c.client_id and i.school_id = e.school_id and i.student_branch <> '' LIMIT 0,1) as conversionlog_id,
//        (SELECT sf.info_id FROM smc_student AS st,smc_student_registerinfo as sf WHERE st.from_client_id = c.client_id AND st.company_id = c.company_id	AND st.student_id = sf.student_id and sf.info_status = '1' LIMIT 0,1) as pay_order_id,
//        (select g.visittime from view_crm_invitelog as g where g.client_id=c.client_id and g.school_id = e.school_id and g.isvisit = '1' order by g.visittime desc limit 0,1 ) as visittime
//        FROM crm_client as c,crm_client_schoolenter as e,crm_client_principal as p,crm_marketer as m,smc_staffer as s,crm_code_channel as l
//        WHERE {$datawhere} group by e.school_id,c.client_id HAVING {$having} ";

        // (SELECT t.track_createtime FROM crm_client_track as t WHERE t.client_id = c.client_id and t.marketer_id = p.marketer_id and track_followmode in ('1','2') ORDER BY track_createtime ASC LIMIT 0,1) as first_track_createtime,
//and i.school_id = e.school_id  audition_id   // and i.school_id = e.school_id    invite_id
//        (SELECT o.order_id FROM smc_student as st,smc_payfee_order as o,smc_payfee_order_pay as pa,smc_code_paytype as t WHERE st.from_client_id = c.client_id and st.company_id = c.company_id and st.student_id = o.student_id and o.order_pid = pa.order_pid and pa.pay_issuccess = '1' and pa.paytype_code = t.paytype_code and t.paytype_ischarge = '1' limit 0,1) as pay_order_id

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
                foreach ($dateexcelarray as &$dataOne) {
                    if ($dataOne['client_enname']) {
                        $dataOne['client_cnname'] = $dataOne['client_cnname'] . '/' . $dataOne['client_enname'];
                    }
                    if ($dataOne['staffer_enname']) {
                        $dataOne['staffer_cnname'] = $dataOne['staffer_cnname'] . '/' . $dataOne['staffer_enname'];
                    }
                    $dataOne['client_tracestatusname'] = $clientTracestatus[$dataOne['client_tracestatus']];
                    if ($dataOne['client_createtime'] > '0') {
                        $dataOne['client_createtime'] = date("Y-m-d", $dataOne['client_createtime']);
                    } else {
                        $dataOne['client_createtime'] = '--';
                    }
                    if ($dataOne['first_track_createtime'] > '0') {
                        $dataOne['first_track_createtime'] = date("Y-m-d", $dataOne['first_track_createtime']);
                    } else {
                        $dataOne['first_track_createtime'] = '--';
                    }
                    if ($dataOne['last_track_createtime'] > '0') {
                        $dataOne['last_track_createtime'] = date("Y-m-d", $dataOne['last_track_createtime']);
                    } else {
                        $dataOne['last_track_createtime'] = '--';
                    }
                    $dataOne['isinvite'] = $dataOne['invite_id'] > 1 ? '是' : '否';
                    $dataOne['isaudition'] = $dataOne['audition_id'] > 1 ? '是' : '否';
                    $dataOne['isconversionlog'] = $dataOne['conversionlog_id'] > 1 ? '是' : '否';
                    $dataOne['ispay'] = $dataOne['pay_order_id'] > 1 ? '是' : '否';
                }

                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['client_cnname'] = $val['client_cnname'];
                    $datearray['client_mobile'] = $val['client_mobile'];
                    $datearray['client_tracestatusname'] = $val['client_tracestatusname'];
                    $datearray['channel_medianame'] = $val['channel_medianame'];
                    $datearray['channel_name'] = $val['channel_name'];
                    $datearray['client_createtime'] = $val['client_createtime'];
                    $datearray['staffer_cnname'] = $val['staffer_cnname'];
                    $datearray['first_track_createtime'] = $val['first_track_createtime'];
                    $datearray['school_cnname'] = $val['school_cnname'];
                    $datearray['visittime'] = $val['visittime'];
                    $datearray['last_track_createtime'] = $val['last_track_createtime'];
                    $datearray['last_track_note'] = $val['last_track_note'];
                    $datearray['last_staffer_cnname'] = $val['last_staffer_cnname'];
                    $datearray['isinvite'] = $val['isinvite'];
                    $datearray['isaudition'] = $val['isaudition'];
                    $datearray['isconversionlog'] = $val['isconversionlog'];
                    $datearray['ispay'] = $val['ispay'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('姓名', '手机号', '客户状态', '渠道类型', '渠道明细', '创建日期', 'TMK负责人', 'TMK首次邀约日期', 'TMK邀约校区', '到访时间', '最后跟踪时间', '最后跟踪内容', '最后跟踪人', '是否已柜询', '是否已试听', '是否转正', '是否缴费'));
            $excelfileds = array('client_cnname', 'client_mobile', 'client_tracestatusname', 'channel_medianame', 'channel_name', 'client_createtime', 'staffer_cnname', 'first_track_createtime', 'school_cnname', 'visittime', 'last_track_createtime', 'last_track_note', 'last_staffer_cnname', 'isinvite', 'isaudition', 'isconversionlog', 'ispay');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校内扩科招生大表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            } else {
                $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
                foreach ($dataList as &$dataOne) {
                    if ($dataOne['client_enname']) {
                        $dataOne['client_cnname'] = $dataOne['client_cnname'] . '/' . $dataOne['client_enname'];
                    }
//                    if ($dataOne['staffer_enname']) {
//                        $dataOne['staffer_cnname'] = $dataOne['staffer_cnname'] . '/' . $dataOne['staffer_enname'];
//                    }
                    $dataOne['client_tracestatusname'] = $clientTracestatus[$dataOne['client_tracestatus']];
                    if ($dataOne['client_createtime'] > '0') {
                        $dataOne['client_createtime'] = date("Y-m-d", $dataOne['client_createtime']);
                    } else {
                        $dataOne['client_createtime'] = '--';
                    }
                    if ($dataOne['first_track_createtime'] > '0') {
                        $dataOne['first_track_createtime'] = date("Y-m-d", $dataOne['first_track_createtime']);
                    } else {
                        $dataOne['first_track_createtime'] = '--';
                    }
                    if ($dataOne['last_track_createtime'] > '0') {
                        $dataOne['last_track_createtime'] = date("Y-m-d", $dataOne['last_track_createtime']);
                    } else {
                        $dataOne['last_track_createtime'] = '--';
                    }
                    $dataOne['isinvite'] = $dataOne['invite_id'] > 1 ? '是' : '否';
                    $dataOne['isaudition'] = $dataOne['audition_id'] > 1 ? '是' : '否';
                    $dataOne['isconversionlog'] = $dataOne['conversionlog_id'] > 1 ? '是' : '否';
                    $dataOne['ispay'] = $dataOne['pay_order_id'] > 1 ? '是' : '否';
                }
            }
        }
        $data['list'] = $dataList;
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectClear("SELECT c.client_id,
        (SELECT t.track_createtime FROM crm_client_track as t WHERE t.client_id = c.client_id  and track_followmode in ('1','2') ORDER BY track_createtime ASC LIMIT 0,1) as first_track_createtime,
        (SELECT i.invite_id FROM crm_client_invite as i WHERE i.client_id = c.client_id and i.invite_isvisit = '1' LIMIT 0,1) as invite_id,
        (SELECT i.audition_id FROM crm_client_audition as i WHERE i.client_id = c.client_id  and i.audition_isvisit = '1' LIMIT 0,1) as audition_id,
        (SELECT i.conversionlog_id FROM crm_client_conversionlog as i WHERE i.client_id = c.client_id and i.school_id = e.school_id and i.student_branch <> '' LIMIT 0,1) as conversionlog_id,
        (SELECT sf.info_id FROM smc_student AS st,smc_student_registerinfo as sf WHERE st.from_client_id = c.client_id AND st.company_id = c.company_id	AND st.student_id = sf.student_id and sf.info_status = '1' LIMIT 0,1) as pay_order_id,
        (select g.visittime from view_crm_invitelog as g where g.client_id=c.client_id and g.school_id = e.school_id and g.isvisit = '1' order by g.visittime desc limit 0,1 ) as visittime 
        FROM crm_client as c,crm_client_schoolenter as e,crm_code_channel as l 
        WHERE {$datawhere} group by e.school_id,c.client_id HAVING {$having}");
            if ($allNum) {
                $data['allnum'] = count($allNum);
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;


    }

    //校内扩科招生大表
    function getStuClientEnroll($request)
    {
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $today = date("Y-m-d", strtotime($request['end_time']));
            $todaytime = strtotime($request['end_time']) + 24 * 60 * 60 - 1;
        } else {
            $today = date("Y-m-d");
            $todaytime = time();
        }
        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));
        $seven_time = strtotime($week_startdate);
        $month_time = strtotime($month_startdate);

        $datawhere = " u.company_id = '{$request['company_id']}' and u.coursetype_isrecruit = '1' AND u.coursetype_isopenclass = '0' AND s.school_istest = '0' AND s.school_isclose = '0' and s.company_id = u.company_id and s.district_id = d.district_id and u.coursetype_id = c.coursetype_id";

        $register_absentee_where = " studyinfo_status = 1 and school_id = s.school_id and coursetype_id = u.coursetype_id and coursecat_id = c.coursecat_id ";//在籍/在读人数

        $register_week = " re.info_status = 1 and re.school_id = s.school_id and re.coursetype_id = u.coursetype_id and re.coursecat_id = c.coursecat_id
AND re.pay_successtime > '{$seven_time}' ";
        $register_month = " re.info_status = 1 and re.school_id = s.school_id and re.coursetype_id = u.coursetype_id and re.coursecat_id = c.coursecat_id
AND re.pay_successtime > '{$month_time}' ";

        $principalnum_where = " p.principal_leave = '0' and p.school_id = s.school_id and p.principal_createtime > '{$month_time}' ";
        $tracknum_where = " t.coursetype_id = u.coursetype_id and t.track_isactive = '1' and t.track_createtime > '{$month_time}' and t.school_id = s.school_id ";
        $trackallnum_where = " t.coursetype_id = u.coursetype_id and t.track_isactive = '1' and t.track_createtime > '{$month_time}' and t.school_id = s.school_id ";
        $auditionnum_where = " a.coursetype_id = u.coursetype_id and a.audition_visittime > '{$month_time}' and a.school_id = s.school_id ";
        $auditionvisitnum_where = " a.coursetype_id = u.coursetype_id and a.audition_isvisit = '1' and a.audition_visittime > '{$month_time}'  and a.school_id = s.school_id  ";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and u.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and c.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_cnname,s.school_enname,s.school_shortname,s.school_branch,d.district_cnname,d.district_branch,u.coursetype_cnname,c.coursecat_id,c.coursecat_cnname,c.coursecat_branch
,(SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province ) as province_name
,(SELECT count(studyinfo_id) FROM temp_smc_student_studyinfo WHERE {$register_absentee_where}  and studyinfo_type = 1) as absenteenums
,(SELECT count(studyinfo_id) FROM temp_smc_student_studyinfo WHERE {$register_absentee_where}  and studyinfo_type = 2) as readingnums
,(SELECT count(info_id) FROM smc_student_registerplus as re WHERE {$register_week}) as  register_week
,(SELECT count(info_id) FROM smc_student_registerplus as re WHERE {$register_month}) as  register_month
,(SELECT count(DISTINCT(p.student_id)) FROM crm_student_principal as p WHERE {$principalnum_where}) as principalnum
,(SELECT count(DISTINCT(t.student_id)) FROM crm_student_track as t WHERE {$tracknum_where}) as tracknum
,(SELECT count(t.student_id) FROM crm_student_track as t WHERE {$trackallnum_where}) as trackallnum
,(SELECT count(DISTINCT(a.student_id)) FROM crm_student_audition as a WHERE {$auditionnum_where}) as auditionnum
,(SELECT count(DISTINCT(a.student_id)) FROM crm_student_audition as a WHERE {$auditionvisitnum_where}) as auditionvisitnum
from smc_code_coursetype as u,smc_school as s,gmc_company_district as d,smc_code_coursecat as c 
WHERE {$datawhere}
order by s.school_sort ASC,u.coursetype_branch ASC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['province_name'] = $val['province_name'];
                    $datearray['school_cnname'] = $val['school_cnname'];
                    $datearray['school_branch'] = $val['school_branch'];
                    $datearray['district_cnname'] = $val['district_cnname'];
                    $datearray['coursetype_cnname'] = $val['coursetype_cnname'];
                    $datearray['absenteenums'] = $val['absenteenums'];
                    $datearray['readingnums'] = $val['readingnums'];
                    $datearray['register_week'] = $val['register_week'];
                    $datearray['register_month'] = $val['register_month'];
                    $datearray['principalnum'] = $val['principalnum'];
                    $datearray['tracknum'] = $val['tracknum'];
                    $datearray['trackallnum'] = $val['trackallnum'];
                    $datearray['auditionnum'] = $val['auditionnum'];
                    $datearray['auditionvisitnum'] = $val['auditionvisitnum'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '学校名称', '学校编号', '所属区域', '班组名称', '在籍人数', '在读人数', '报名/周', '报名/月', '新增意向学员/月', '追踪名单/月', '追踪名单次/月', '试听邀约人数/月', '试听邀约到访/月'));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'district_cnname', 'coursetype_cnname', 'absenteenums', 'readingnums', "register_week", 'register_month', 'principalnum', 'tracknum', 'trackallnum', 'auditionnum', 'auditionvisitnum');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校内扩科招生大表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if ($dataList) {
                foreach ($dataList as &$val) {
                    $val['absenteenums'] = is_null($val['absenteenums']) ? 0 : $val['absenteenums'];
                }
            }
            $data['list'] = $dataList;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT count(s.school_id) as allnum
        from smc_code_coursetype as u,smc_school as s,gmc_company_district as d ,smc_code_coursecat as c 
        WHERE {$datawhere} order by s.school_id ASC ");
            if ($allNum) {
                $data['allnum'] = $allNum['allnum'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    //校区招生总览报表
    function getCoursecatRecruit($request)
    {
        $datawhere = " s.company_id = '{$request['company_id']}' and s.school_isclose = '0' and s.school_istest = '0' and s.company_id = p.company_id and p.coursetype_isrecruit = '1' AND p.coursetype_isopenclass = '0' and p.coursetype_id = c.coursetype_id and c.coursecat_iscrmadded = '1'  ";
        $register_absentee_where = " studyinfo_status = 1 and school_id = s.school_id and coursetype_id = p.coursetype_id and coursecat_id = c.coursecat_id ";

        $clienttostudent = " re.info_status = 1 and re.school_id = s.school_id and re.coursetype_id = p.coursetype_id and re.coursecat_id = c.coursecat_id
AND NOT exists( SELECT  * FROM smc_student_registerinfo WHERE info_status = 1 and pay_successtime <  re.pay_successtime and student_id = re.student_id ) ";
        $extendcoursereg = " re.info_status = 1  and re.school_id = s.school_id and re.coursetype_id = p.coursetype_id and re.coursecat_id = c.coursecat_id
AND exists( SELECT  * FROM smc_student_registerinfo WHERE info_status = 1 and pay_successtime <  re.pay_successtime and student_id = re.student_id )  ";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (p.coursetype_cnname like '%{$request['keyword']}%' and p.coursetype_branch like '%{$request['keyword']}%' ) ";
        }
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);

            $clienttostudent .= " and re.pay_successtime >= '{$stattime}'  ";
            $extendcoursereg .= " and re.pay_successtime >= '{$stattime}'  ";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $clienttostudent .= " and re.pay_successtime <= '{$endime}'  ";
            $extendcoursereg .= " and re.pay_successtime <= '{$endime}'  ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and s.district_id='{$request['district_id']}'";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and p.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and c.coursecat_id='{$request['coursecat_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_id,s.district_id,s.school_shortname,s.school_branch
, (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name
, (select district_cnname from gmc_company_district WHERE district_id = s.district_id) as district_cnname
, p.coursetype_id,p.coursetype_cnname,p.coursetype_branch, c.coursecat_id,c.coursecat_cnname,c.coursecat_branch
        ,(SELECT count(studyinfo_id) FROM temp_smc_student_studyinfo WHERE {$register_absentee_where}  and studyinfo_type = 1) as absenteenums
        ,(SELECT count(studyinfo_id) FROM temp_smc_student_studyinfo WHERE {$register_absentee_where}  and studyinfo_type = 2) as readingnums
        ,(SELECT count(info_id) FROM smc_student_registerinfo as re WHERE {$clienttostudent}) as clienttostudent
        ,(SELECT count(info_id) FROM smc_student_registerinfo as re WHERE {$extendcoursereg}) as extendcoursereg 
                FROM smc_school as s,smc_code_coursetype as p,smc_code_coursecat as c 
                where {$datawhere}
                ORDER BY s.school_sort ASC,p.coursetype_branch DESC,c.coursecat_branch DESC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['province_name'] = $val['province_name'];
                    $datearray['school_shortname'] = $val['school_shortname'];
                    $datearray['school_branch'] = $val['school_branch'];
                    $datearray['district_cnname'] = $val['district_cnname'];
                    $datearray['coursetype_cnname'] = $val['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $val['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $val['coursecat_branch'];
                    $datearray['absenteenums'] = $val['absenteenums'];
                    $datearray['readingnums'] = $val['readingnums'];
                    $datearray['allclientstudent'] = $val['clienttostudent'] + $val['extendcoursereg'];
                    $datearray['clienttostudent'] = $val['clienttostudent'];
                    $datearray['extendcoursereg'] = $val['extendcoursereg'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '所属区域', '班组名称', '班种名称', '班种编号', '当前在籍人数', '当前在读人数', '总报名人数', '新学员报名人数', '扩科报名人数'));
            $excelfileds = array('province_name', 'school_shortname', 'school_branch', 'district_cnname', 'coursetype_cnname', 'coursecat_cnname', 'coursecat_branch', 'absenteenums', 'readingnums', 'allclientstudent', 'clienttostudent', 'extendcoursereg');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校区招生总览报表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if ($dataList) {
                foreach ($dataList as &$val) {
                    $val['allclientstudent'] = $val['clienttostudent'] + $val['extendcoursereg'];
                }
            }
            $data['list'] = $dataList;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT count(s.school_id) as allnum
                FROM smc_school as s,smc_code_coursetype as p,smc_code_coursecat as c 
                where {$datawhere}
                ORDER BY s.school_id ASC");
            if ($allNum) {
                $data['allnum'] = $allNum['allnum'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    //校区招生分析报表
    function getCenterEnrollAnalysis($request)
    {
        $datawhere = " s.company_id = '{$request['company_id']}' AND s.school_istest = '0' AND s.school_isclose = '0'";

        //在籍在读查询
        $studywhere = "sd.school_id = s.school_id and sd.studyinfo_status=1";

        //报名查询
        $registerpluswhere = "re.info_status = 1 AND re.school_id = s.school_id ";

        //推荐报名查询
        $rombookingwhere = " c.client_id = e.client_id AND e.school_id = s.school_id AND e.is_enterstatus = '1' AND c.client_id = re.from_client_id AND re.info_status = 1 AND re.info_first_in_company = '1' AND c.client_stubranch <> ''";

        //校区入校名单
        $schoolenterwhere = " c.client_id = e.client_id AND e.school_id = s.school_id AND e.is_enterstatus = '1'";
        //当前待分配名单
        $nothaveprincipal = $schoolenterwhere . " AND c.client_tracestatus = '0'  AND c.client_distributionstatus = '0'";

        //追踪名单数
        $trackswhere = "c.client_id = e.client_id  AND e.school_id = s.school_id AND e.is_enterstatus = '1' AND c.client_id = t.client_id  AND t.track_isactive = '1'  AND t.school_id = s.school_id";

        //邀约人数查询
        $invitelogwhere = $schoolenterwhere . " AND c.client_id = t.client_id AND t.school_id = s.school_id";

        //柜询查询
        $invitewhere = $schoolenterwhere . " AND c.client_id = t.client_id  AND t.school_id = s.school_id";

        //试听课查询
        $audiwhere = $schoolenterwhere . " AND c.client_id = t.client_id  AND t.school_id = s.school_id";

        $bookwhere = "";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $studywhere .= " and sd.coursetype_id = '{$request['coursetype_id']}' ";

            $registerpluswhere .= " and re.coursetype_id = '{$request['coursetype_id']}' ";

            $rombookingwhere .= " and re.coursetype_id = '{$request['coursetype_id']}' ";

            $schoolenterwhere .= " AND (SELECT it.intention_id FROM crm_client_intention AS it WHERE it.client_id = c.client_id AND it.coursetype_id = '{$request['coursetype_id']}' limit 0,1) > 1 ";

            $trackswhere .= " AND t.coursetype_id = '{$request['coursetype_id']}'";

            $invitelogwhere .= " AND t.coursetype_id = '{$request['coursetype_id']}'";

            $invitewhere .= " AND t.coursetype_id = '{$request['coursetype_id']}'";

            $audiwhere .= " AND t.coursetype_id = '{$request['coursetype_id']}'";

            $bookwhere .= " and re.coursetype_id = '{$request['coursetype_id']}' ";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $studywhere .= " and sd.coursecat_id = '{$request['coursecat_id']}'";

            $registerpluswhere .= " and re.coursecat_id = '{$request['coursecat_id']}' ";

            $rombookingwhere .= " and re.coursecat_id = '{$request['coursecat_id']}' ";

            $schoolenterwhere .= " AND (SELECT it.intention_id FROM crm_client_intention AS it WHERE it.client_id = c.client_id AND it.coursecat_id = '{$request['coursecat_id']}' limit 0,1) > 1 ";

            $trackswhere .= " AND t.coursecat_id = '{$request['coursecat_id']}'";

            $invitelogwhere .= " AND t.coursecat_id = '{$request['coursecat_id']}'";

            $invitewhere .= " AND t.coursecat_id = '{$request['coursecat_id']}'";

            $audiwhere .= " AND t.coursecat_id = '{$request['coursecat_id']}'";

            $bookwhere .= " and re.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['start_time']) && $request['start_time'] != '') {
            $stattime = strtotime($request['start_time']);
            $statday = date('Y-m-d H:i:s', $stattime);

            $registerpluswhere .= " and re.pay_successtime > '{$stattime}'";//

            $rombookingwhere .= " and re.pay_successtime > '{$stattime}'";//

            $schoolenterwhere .= " and c.client_createtime > '{$stattime}'";

            $trackswhere .= " and t.track_createtime > '{$stattime}'";

            $invitelogwhere .= " and t.visittime > '{$statday}'";

            $invitewhere .= " and t.invite_visittime > '{$statday}'";

            $audiwhere .= " and t.audition_visittime > '{$statday}'";
        } else {
            $stattime = strtotime(date('Y-m-01', time()));
            $statday = date('Y-m-d H:i:s', $stattime);

            $registerpluswhere .= " and re.pay_successtime > '{$stattime}'";//

            $rombookingwhere .= " and re.pay_successtime > '{$stattime}'";

            $schoolenterwhere .= " and c.client_createtime > '{$stattime}'  ";

            $trackswhere .= " and t.track_createtime > '{$stattime}'  ";

            $invitelogwhere .= " and t.visittime > '{$statday}'";

            $invitewhere .= " and t.invite_visittime > '{$statday}'  ";

            $audiwhere .= " and t.audition_visittime > '{$statday}'";

        }

        if (isset($request['end_time']) && $request['end_time'] != '') {
            $endtime = strtotime($request['end_time']) + 86399;
            $endday = date('Y-m-d H:i:s', $endtime);

            $registerpluswhere .= " and re.pay_successtime <= '{$endtime}'";//and r.pay_successtime <= '{$endtime}''

            $rombookingwhere .= " and re.pay_successtime <= '{$endtime}'";//

            $schoolenterwhere .= " and c.client_createtime <= '{$endtime}'";

            $trackswhere .= " and t.track_createtime <= '{$endtime}'  ";

            $invitelogwhere .= " and t.visittime <= '{$endday}'";

            $invitewhere .= " and t.invite_visittime <= '{$endday}'";

            $audiwhere .= " and t.audition_visittime <= '{$endday}'";

        } else {
            $endtimestr = date('Y-m-d', time());//当前时间
            $endtime = strtotime($endtimestr) + 86399;
            $endday = date('Y-m-d H:i:s', $endtime);

            $registerpluswhere .= " and re.pay_successtime <= '{$endtime}'";//and r.pay_successtime <= '{$endtime}'

            $rombookingwhere .= " and re.pay_successtime <= '{$endtime}'";

            $schoolenterwhere .= " and c.client_createtime <= '{$endtime}'  ";

            $trackswhere .= " and t.track_createtime <= '{$endtime}'";

            $invitelogwhere .= " and t.visittime <= '{$endday}'";

            $invitewhere .= " and t.invite_visittime <= '{$endday}'";

            $audiwhere .= " and t.audition_visittime <= '{$endday}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and s.district_id='{$request['district_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_id,s.district_id,s.school_shortname,s.school_branch,
                (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
                (select district_cnname from gmc_company_district WHERE district_id = s.district_id) as district_cnname,
                (select count(sd.studyinfo_id) from temp_smc_student_studyinfo AS sd where {$studywhere} and sd.studyinfo_type = 1) as 'absenteenums',
                (select count(sd.studyinfo_id) from temp_smc_student_studyinfo AS sd where {$studywhere} and sd.studyinfo_type = 2) as 'readingnums',

                (select count(re.info_id) FROM smc_student_registerplus AS re where {$registerpluswhere} AND re.info_first_in_company = '1') as 'clienttostudent',
                (select count(re.info_id) FROM smc_student_registerplus AS re where {$registerpluswhere} AND re.info_first_in_company = '0') as 'extendcoursereg',
                (select count(re.info_id) FROM crm_client AS c, smc_student_registerplus AS re where {$registerpluswhere} AND c.client_id = re.from_client_id  AND re.info_first_in_company = '1' AND c.client_fromtype = '1') as 'recruitclientreg',

                (SELECT COUNT( c.client_id ) FROM crm_client AS c,crm_client_schoolenter AS e WHERE {$schoolenterwhere} ) as 'newaddgrossclient',
                (SELECT COUNT( c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_code_channel AS h WHERE {$schoolenterwhere} AND c.channel_id = h.channel_id AND h.channel_push = '1') as 'newaddpushchannelclient',
                (SELECT COUNT( c.client_id )  FROM crm_client AS c, crm_client_schoolenter AS e, crm_code_channel AS h  WHERE {$schoolenterwhere} AND c.channel_id = h.channel_id AND h.channel_way = '0') as 'newaddwaychannelclient',

                (SELECT COUNT( c.client_id )  FROM crm_client AS c, crm_client_schoolenter AS e  WHERE {$schoolenterwhere} AND c.client_stubranch <> '')  as 'addstubranchclient',
                (SELECT COUNT( c.client_id )  FROM crm_client AS c, crm_client_schoolenter AS e, smc_student_registerplus AS re WHERE {$rombookingwhere}) as 'addstubranchclientreg',

                (SELECT COUNT( c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e WHERE {$nothaveprincipal}) as 'nothaveprincipal',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_client_track AS t  WHERE {$trackswhere}) as 'haveprincipaltrackclient',
                (SELECT COUNT( c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_client_track AS t WHERE {$trackswhere}) as 'haveprincipaltrackclientall',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, view_crm_invitelog AS t WHERE {$invitelogwhere} ) as 'inviteclient',
                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, view_crm_invitelog AS t WHERE {$invitelogwhere}  AND t.isvisit = '1' AND t.genre IN (0, 1)) as 'inviteclientreach',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_client_invite AS t WHERE {$invitewhere} AND t.invite_isvisit = '1' AND t.invite_genre = '2') as 'pushinviteclientreach',
                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_client_invite AS t  WHERE {$invitewhere} AND t.invite_isvisit = '1' AND t.invite_genre = '3')  as 'activeinviteclientreach',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, view_crm_invitelog AS t WHERE {$invitelogwhere} AND t.isvisit = '1' ) as 'allinviteclientreach',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, view_crm_invitelog AS t, smc_student_registerplus AS re WHERE {$invitelogwhere} AND t.isvisit = '1' AND c.client_id = re.from_client_id  {$bookwhere}) as 'allinviteclientreachbook',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_client_audition AS t WHERE {$audiwhere} AND t.audition_isvisit = '1') as 'ohauditionclient',

                (SELECT COUNT( DISTINCT c.client_id ) FROM crm_client AS c, crm_client_schoolenter AS e, crm_client_audition AS t, smc_student_registerplus AS re WHERE {$audiwhere}  AND c.client_id = re.from_client_id AND t.audition_isvisit = '1' {$bookwhere}) as 'ohauditionclientreach'
                FROM smc_school as s where {$datawhere} ORDER BY s.school_sort ASC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['province_name'] = $val['province_name'];
                    $datearray['school_shortname'] = $val['school_shortname'];
                    $datearray['school_branch'] = $val['school_branch'];
                    $datearray['district_cnname'] = $val['district_cnname'];

                    $datearray['absenteenums'] = $val['absenteenums'];
                    $datearray['readingnums'] = $val['readingnums'];
                    $datearray['allregstu'] = $val['clienttostudent'] + $val['extendcoursereg'];
                    $datearray['clienttostudent'] = $val['clienttostudent'];
                    $datearray['extendcoursereg'] = $val['extendcoursereg'];

                    $datearray['recruitclientreg'] = $val['recruitclientreg'];
                    $datearray['newaddgrossclient'] = $val['newaddgrossclient'];
                    $datearray['newaddpushchannelclient'] = $val['newaddpushchannelclient'];
                    $datearray['newaddwaychannelclient'] = $val['newaddwaychannelclient'];
                    $datearray['addstubranchclient'] = $val['addstubranchclient'];

                    $datearray['addstubranchclientreg'] = $val['addstubranchclientreg'];
                    $datearray['nothaveprincipal'] = $val['nothaveprincipal'];
                    $datearray['haveprincipaltrackclient'] = $val['haveprincipaltrackclient'];
                    $datearray['haveprincipaltrackclientall'] = $val['haveprincipaltrackclientall'];
                    $datearray['inviteclient'] = $val['inviteclient'];

                    $datearray['inviteclientreach'] = $val['inviteclientreach'];
                    $datearray['conversion'] = ($val['inviteclient'] == 0 ? '--' : (sprintf("%.2f", $val['inviteclientreach'] / $val['inviteclient']) * 100)) . "%";
                    $datearray['pushinviteclientreach'] = $val['pushinviteclientreach'];
                    $datearray['activeinviteclientreach'] = $val['activeinviteclientreach'];
                    $datearray['allinviteclientreach'] = $val['allinviteclientreach'];
                    $datearray['allinviteclientreachbook'] = $val['allinviteclientreachbook'];

                    $datearray['signrate'] = ($val['allinviteclientreachbook'] == 0 ? '--' : (sprintf("%.2f", $val['allinviteclientreachbook'] / $val['allinviteclientreach']) * 100)) . "%";

                    $datearray['ohauditionclient'] = $val['ohauditionclient'];
                    $datearray['ohauditionclientreach'] = $val['ohauditionclientreach'];
                    $datearray['ohformalrate'] = ($val['ohauditionclient'] == 0 ? '--' : (sprintf("%.2f", $val['ohauditionclientreach'] / $val['ohauditionclient']) * 100)) . "%";
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('省份', '校区名称', '校区编号', '所属区域',
                '当前在籍人数', '当前在读人数', '总报名人数', '新学员报名人数', '扩科报名人数',
                '园内内招报名人数', '新增毛名单', '新增地推有效名单', '新增线上有效名单', '推荐名单',
                '推荐报名人数', '当前待分配名单', '追踪名单数', '追踪人次', '邀约人数',
                '邀约到访人数', '电转率', '推带到访人数', '主动到访人数', '总到访人数', '总到访报名人数',
                '到访签约率', 'OH试听人数', 'OH转正人数', 'OH转正率'));
            $excelfileds = array('province_name', 'school_shortname', 'school_branch', 'district_cnname',
                'absenteenums', 'readingnums', 'allregstu', 'clienttostudent', 'extendcoursereg',
                'recruitclientreg', 'newaddgrossclient', 'newaddpushchannelclient', 'newaddwaychannelclient', 'addstubranchclient',
                'addstubranchclientreg', 'nothaveprincipal', 'haveprincipaltrackclient', 'haveprincipaltrackclientall', 'inviteclient',
                'inviteclientreach', 'conversion', 'pushinviteclientreach', 'activeinviteclientreach', 'allinviteclientreach', 'allinviteclientreachbook',
                'signrate', 'ohauditionclient', 'ohauditionclientreach', 'ohformalrate');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校区招生分析报表.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if ($dataList) {
                foreach ($dataList as &$val) {////sprintf("%.2f",$num)
                    $val['allregstu'] = $val['clienttostudent'] + $val['extendcoursereg'];//allregstu

                    $val['conversion'] = ($val['inviteclient'] == 0 ? '--' : (sprintf("%.2f", $val['inviteclientreach'] / $val['inviteclient']) * 100)) . "%";////conversion
                    $val['signrate'] = ($val['allinviteclientreachbook'] == 0 ? '--' : (sprintf("%.2f", $val['allinviteclientreachbook'] / $val['allinviteclientreach']) * 100)) . "%";//signrate
                    $val['ohformalrate'] = ($val['ohauditionclient'] == 0 ? '--' : (sprintf("%.2f", $val['ohauditionclientreach'] / $val['ohauditionclient']) * 100)) . "%";//ohformalrate
                }
            }
            $data['list'] = $dataList;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT count(s.school_id) as allnum
                FROM smc_school as s
                where {$datawhere}
                ORDER BY s.school_id ASC");
            if ($allNum) {
                $data['allnum'] = $allNum['allnum'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

}