<?php


namespace Model\Report\Gmc;

class EducateReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->staffer_id = $publicarray['staffer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }


    function teacherClassStatistic($request)
    {
        $datawhere = " and A.school_istest = '0' and A.company_id='{$this->company_id}' ";

        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and A.hour_day >= '{$request['start_time']}' ";
        }
        //结束时间
        $endtime = strtotime(date("Y-m-d"));
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and A.hour_day <= '{$request['end_time']}' ";
            $endtime = strtotime($request['end_time']);
        }
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (A.staffer_branch like '%{$request['keyword']}%' 
            or A.staffer_cnname like '%{$request['keyword']}%' 
            or A.staffer_enname like '%{$request['keyword']}%' 
            or A.class_cnname like '%{$request['keyword']}%' 
            or A.class_enname like '%{$request['keyword']}%' 
            or A.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '' && $request['school_id'] != '[]') {
            $schoolArray = json_decode(stripslashes($request['school_id']), 1);
            if (is_array($schoolArray) && count($schoolArray) > 0) {
                $schoolstr = '';
                foreach ($schoolArray as $schoolvar) {
                    $schoolstr .= "'" . $schoolvar . "'" . ',';
                }
                $schoolstr = substr($schoolstr, 0, -1);
                $datawhere .= " and A.school_id in ({$schoolstr}) ";
            } else {
                if (isset($request['school_id']) && $request['school_id'] !== '') {
                    $datawhere .= " and A.school_id ='{$request['school_id']}' ";
                } else {
                    $datawhere .= " AND c.school_istest <> '1' AND c.school_isclose<> '1' ";
                }
            }
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and c.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND c.school_istest <> '1' AND c.school_isclose<> '1' ";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '' && !is_array($request['coursetype_id'])) {
            $datawhere .= " and A.coursetype_id in('{$request['coursetype_id']}')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '' && !is_array($request['coursecat_id'])) {
            $datawhere .= " and A.coursecat_id in('{$request['coursecat_id']}')";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '' && !is_array($request['course_id'])) {
            $datawhere .= " and A.course_id in('{$request['course_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT A.school_id,A.school_branch,A.school_cnname,
                    A.staffer_id,A.staffer_cnname,A.staffer_enname,A.staffer_branch,A.staffer_native,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = c.school_province ) as province_name,
                    (SELECT Y.post_name FROM gmc_staffer_postbe X,gmc_company_post Y WHERE X.post_id = Y.post_id AND X.company_id=Y.company_id AND X.staffer_id=A.staffer_id 
                    AND X.company_id=A.company_id and X.school_id=A.school_id AND Y.post_type=1 AND X.postbe_createtime<='{$endtime}' ORDER BY X.postbe_createtime DESC LIMIT 1) AS post_name,
                    A.class_id,A.class_cnname,A.class_enname,A.class_branch,A.course_branch,A.course_cnname, 
                    A.coursecat_branch,A.coursecat_cnname,A.coursetype_branch,A.coursetype_cnname, 
                    
                    COUNT(if(A.hour_way=1 and A.teaching_type=0,true,null)) AS main_hour_num_online,
                    ROUND( sum(if(A.hour_way=1 and A.teaching_type=0,A.hour_classtimes,0))/3600,2) AS main_hour_times_online,
                    
                    COUNT(if(A.hour_way=0 and A.teaching_type=0,true,null)) AS main_hour_num_offline,
                    ROUND( sum(if(A.hour_way=0 and A.teaching_type=0,A.hour_classtimes,0))/3600,2) AS main_hour_times_offline,
                    
                    COUNT(if(A.hour_way=1 and A.teaching_type=1,true,null)) AS sub_hour_num_online,
                    ROUND( sum(if(A.hour_way=1 and A.teaching_type=1,A.hour_classtimes,0))/3600,2) AS sub_hour_times_online,
                    
                    COUNT(if(A.hour_way=0 and A.teaching_type=1,true,null)) AS sub_hour_num_offline,
                    ROUND( sum(if(A.hour_way=0 and A.teaching_type=1,A.hour_classtimes,0))/3600,2) AS sub_hour_times_offline,
                    
                    (select count(1) from smc_student_study where study_beginday<=a.hour_day and study_endday>=a.hour_day and class_id=a.class_id) as student_num,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = c.district_id) AS district_cnname
                    FROM view_smc_class_hour_teaching A 
                    left join smc_code_teachtype b on A.COMPANY_ID=B.COMPANY_ID AND A.TEACHTYPE_CODE=B.TEACHTYPE_CODE
                    left join smc_school c on a.company_id=c.company_id and a.school_id=c.school_id 
                    WHERE 1 {$datawhere}
                    GROUP BY A.school_id,A.staffer_id,A.class_id
                    ORDER BY (case when c.school_istest=0 and c.school_isclose=0 then 1 when c.school_isclose=0 then 2 when c.school_istest=0 then 3 else 4 end),c.school_istest asc,field(c.school_sort,0),c.school_sort asc,c.school_createtime asc,A.school_id,A.staffer_id,A.class_id ";

        $native = $this->LgArraySwitch(array('0' => '陆籍', '1' => '外籍', '2' => '港澳籍', '3' => '台籍'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无带课数据";
                return false;
            }

            $sql_export = "SELECT A.school_id,A.staffer_id,A.class_id 
                        FROM view_smc_class_hour_teaching A 
                        left join smc_school c on A.company_id=c.company_id and A.school_id=c.school_id 
                        WHERE 1 {$datawhere} 
                        GROUP BY A.school_id,A.staffer_id,A.class_id 
                        ORDER BY A.school_id,A.staffer_id,A.class_id";

            $list_export = $this->DataControl->selectClear($sql_export);

            $outexceldate = array();
            if ($list_export) {
                foreach ($list_export as &$one_show) {

                    $datearray = array();
                    $datearray['school_id'] = $one_show['school_id'];
                    $datearray['staffer_id'] = $one_show['staffer_id'];
                    $datearray['class_id'] = $one_show['class_id'];

                    $cursor = 0;
                    foreach ($dateexcelarray as &$one) {
                        if ($one['school_id'] == $one_show['school_id'] && $one['staffer_id'] == $one_show['staffer_id'] && $one['class_id'] == $one_show['class_id']) {
                            $cursor++;
                            $datearray['province_name'] = $one['province_name'];
                            $datearray['school_branch'] = $one['school_branch'];
                            $datearray['school_cnname'] = $one['school_cnname'];
                            $datearray['district_cnname'] = $one['district_cnname'];
                            $datearray['staffer_cnname'] = $one['staffer_cnname'];
                            $datearray['staffer_enname'] = $one['staffer_enname'];
                            $datearray['staffer_branch'] = $one['staffer_branch'];
                            $datearray['staffer_native'] = $native[$one['staffer_native']];
                            $datearray['post_name'] = $one['post_name'];
                            $datearray['teachtype_name'] = $one['teachtype_name'];
                            $datearray['class_cnname'] = $one['class_cnname'];
                            $datearray['class_enname'] = $one['class_enname'];
                            $datearray['class_branch'] = $one['class_branch'];
                            $datearray['course_branch'] = $one['course_branch'];
                            $datearray['course_cnname'] = $one['course_cnname'];
                            $datearray['coursecat_branch'] = $one['coursecat_branch'];
                            $datearray['coursecat_cnname'] = $one['coursecat_cnname'];
                            $datearray['coursetype_branch'] = $one['coursetype_branch'];
                            $datearray['coursetype_cnname'] = $one['coursetype_cnname'];

                            $datearray['main_hour_num_online'] = $one['main_hour_num_online'];
                            $datearray['main_hour_times_online'] = $one['main_hour_times_online'];
                            $datearray['main_hour_num_offline'] = $one['main_hour_num_offline'];
                            $datearray['main_hour_times_offline'] = $one['main_hour_times_offline'];
                            $datearray['sub_hour_num_online'] = $one['sub_hour_num_online'];
                            $datearray['sub_hour_times_online'] = $one['sub_hour_times_online'];
                            $datearray['sub_hour_num_offline'] = $one['sub_hour_num_offline'];
                            $datearray['sub_hour_times_offline'] = $one['sub_hour_times_offline'];
                            $datearray['student_num'] = $one['student_num'];
                        }
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "所属区域", "教师中文名", "教师英文名", "教师编号", "籍贯", "职位", "教师类型", "班级名称", "班级别名", "班级编号", "课程别编号", "课程别名称", "班种编号", "班种名称", "班组编号", "班组名称", "主教线上课次数", "主教线上课时数", "主教线下课次数", "主教线下课时数", "助教线上课次数", "助教线上课时数", "助教线下课次数", "助教线下课时数", "学生人数"));
            $excelfileds = array("province_name", "school_branch", "school_cnname", "district_cnname", 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'staffer_native', 'post_name', 'teachtype_name', 'class_cnname', 'class_enname', 'class_branch', 'course_branch', 'course_cnname', 'coursecat_branch', 'coursecat_cnname', 'coursetype_branch', 'coursetype_cnname', 'main_hour_num_online', 'main_hour_times_online', 'main_hour_num_offline', 'main_hour_times_offline', 'sub_hour_num_online', 'sub_hour_times_online', 'sub_hour_num_offline', 'sub_hour_times_offline', 'student_num');
            $tem_name = $this->LgStringSwitch("教师带班课时统计表（{$request['start_time']}-{$request['end_time']}）.xls");
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql_show = "SELECT A.school_id,A.staffer_id,A.class_id 
                        FROM view_smc_class_hour_teaching A 
                        left join smc_school c on A.company_id=c.company_id and A.school_id=c.school_id 
                        WHERE 1 {$datawhere}
                        GROUP BY A.school_id,A.staffer_id,A.class_id 
                        ORDER BY A.school_id,A.staffer_id,A.class_id";
            $sql_show .= ' limit ' . $pagestart . ',' . $num;

            $list_show = $this->DataControl->selectClear($sql_show);
            $list_all = $this->DataControl->selectClear($sql);

            $show_list = array();
            if ($list_show) {
                foreach ($list_show as &$one_show) {

                    $show_array = array();
                    $show_array['school_id'] = $one_show['school_id'];
                    $show_array['staffer_id'] = $one_show['staffer_id'];
                    $show_array['class_id'] = $one_show['class_id'];

                    $cursor = 0;
                    foreach ($list_all as &$one) {
                        if ($one['school_id'] == $one_show['school_id'] && $one['staffer_id'] == $one_show['staffer_id'] && $one['class_id'] == $one_show['class_id']) {
                            $cursor++;
                            $show_array['district_cnname'] = $one['district_cnname'];
                            $show_array['province_name'] = $one['province_name'];
                            $show_array['school_branch'] = $one['school_branch'];
                            $show_array['school_cnname'] = $one['school_cnname'];
                            $show_array['staffer_cnname'] = $one['staffer_cnname'];
                            $show_array['staffer_enname'] = $one['staffer_enname'];
                            $show_array['staffer_branch'] = $one['staffer_branch'];
                            $show_array['staffer_native'] = $native[$one['staffer_native']];
                            $show_array['post_name'] = $one['post_name'];
                            $show_array['teachtype_name'] = $one['teachtype_name'];
                            $show_array['class_cnname'] = $one['class_cnname'];
                            $show_array['class_enname'] = $one['class_enname'];
                            $show_array['class_branch'] = $one['class_branch'];
                            $show_array['course_branch'] = $one['course_branch'];
                            $show_array['course_cnname'] = $one['course_cnname'];
                            $show_array['coursecat_branch'] = $one['coursecat_branch'];
                            $show_array['coursecat_cnname'] = $one['coursecat_cnname'];
                            $show_array['coursetype_branch'] = $one['coursetype_branch'];
                            $show_array['coursetype_cnname'] = $one['coursetype_cnname'];

                            $show_array['main_hour_num_online'] = $one['main_hour_num_online'];
                            $show_array['main_hour_times_online'] = $one['main_hour_times_online'];
                            $show_array['main_hour_num_offline'] = $one['main_hour_num_offline'];
                            $show_array['main_hour_times_offline'] = $one['main_hour_times_offline'];
                            $show_array['sub_hour_num_online'] = $one['sub_hour_num_online'];
                            $show_array['sub_hour_times_online'] = $one['sub_hour_times_online'];
                            $show_array['sub_hour_num_offline'] = $one['sub_hour_num_offline'];
                            $show_array['sub_hour_times_offline'] = $one['sub_hour_times_offline'];

                            $show_array['student_num'] = $one['student_num'];
//                            $show_array['hour_num'] = $one['hour_num'];
                        }
                    }
                    $show_list[] = $show_array;
                }
            }
            $data = array();
            $count_sql = "SELECT A.school_id,A.staffer_id,A.class_id 
                        FROM view_smc_class_hour_teaching A 
                        left join smc_school c on A.company_id=c.company_id and A.school_id=c.school_id 
                        WHERE 1 {$datawhere}
                        GROUP BY A.school_id,A.staffer_id,A.class_id ";
            $dbNums = $this->DataControl->selectClear($count_sql);
            if ($dbNums) {
                $allnum = count($dbNums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $show_list;
            return $data;
        }
    }

    //网课排课进度表
    function classHourLineReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " and s.school_istest<>'1' and s.school_isclose<>'1'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and s.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND s.school_istest <> '1' AND s.school_isclose<> '1' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and h.hour_day <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and h.hour_day >= '{$request['start_time']}'";
        }

        if (isset($request['hour_ischecking']) && $request['hour_ischecking'] !== '') {
            $datawhere .= " and h.hour_ischecking = '{$request['hour_ischecking']}'";
        }

        $file_name_add = '';
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}'";
            $coursetypeOne = $this->DataControl->getFieldOne('smc_code_coursetype', "concat(coursetype_cnname,'(',coursetype_branch,')') as coursetype", " coursetype_id= '{$request['coursetype_id']}'");
            $file_name_add .= $coursetypeOne['coursetype'] . '_';
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}'";
            $coursecatOne = $this->DataControl->getFieldOne('smc_code_coursecat', "concat(coursecat_cnname,'(',coursecat_branch,')') as coursecat", " coursecat_id= '{$request['coursecat_id']}'");
            $file_name_add .= $coursecatOne['coursecat'] . '_';
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
                SELECT
                 s.school_id,
                 s.school_branch,
                 s.school_shortname as school_cnname,
                 (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
                 cct.coursetype_branch,
                 cct.coursetype_cnname,
                 ccc.coursecat_branch,
                 ccc.coursecat_cnname,
                 co.course_branch,
                 co.course_cnname,
                 c.class_branch,
                 c.class_cnname,
                 h.hour_id,
                 h.hour_day,
                 h.hour_ischecking,
                 CONCAT(
                  h.hour_starttime,
                  '-',
                  h.hour_endtime
                 ) AS hour_times,
                 h.hour_number,
                 (
                  SELECT
                   concat(staffer_cnname,(CASE WHEN ifnull( f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', f.staffer_enname ) END ) )
                  FROM
                   smc_class_hour_teaching AS t,
                   smc_staffer AS f
                  WHERE
                   t.staffer_id = f.staffer_id AND t.hour_id = h.hour_id
                  AND t.teaching_type = '0'
                  LIMIT 0,
                  1
                 ) AS bishoptename,
                 (
                  SELECT
                   concat(staffer_cnname,(CASE WHEN ifnull( f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', f.staffer_enname ) END ) )
                  FROM
                   smc_class_hour_teaching AS t,
                   smc_staffer AS f
                  WHERE
                   t.staffer_id = f.staffer_id AND t.hour_id = h.hour_id
                  AND t.teaching_type = '1'
                  LIMIT 0,
                  1
                 ) AS assistanttename
                FROM
                 smc_class_hour AS h,
                 smc_class AS c,
                 smc_school AS s,
                 smc_course as co,
                 smc_code_coursecat as ccc,
                 smc_code_coursetype as cct 
                WHERE
                {$datawhere}
                AND h.class_id = c.class_id
                AND c.school_id = s.school_id
                AND co.course_id = c.course_id
                AND co.coursecat_id=ccc.coursecat_id
                AND co.coursetype_id=cct.coursetype_id
                AND h.hour_way = '1'
                AND c.company_id = '{$request['company_id']}'
                order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
              ";

        $status = $this->LgArraySwitch(array("0" => "待考勤", "1" => "已考勤", "-1" => "已取消"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['hour_times'] = $dateexcelvar['hour_times'];
                    $datearray['hour_number'] = $dateexcelvar['hour_number'];
                    $datearray['bishoptename'] = $dateexcelvar['bishoptename'];
                    $datearray['assistanttename'] = $dateexcelvar['assistanttename'];
                    $datearray['hour_ischecking'] = $status[$dateexcelvar['hour_ischecking']];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "班组编号", "班组名称", "班种编号", "班种编号", "班别编号", "班别名称", "班级编号", "班级名称", '上课日期', "上课时间", "云教室编号", "主教教师", "助教教师", "考勤状态"));
            $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'coursetype_branch', 'coursetype_cnname', 'coursecat_branch', 'coursecat_cnname', 'course_branch', 'course_cnname', 'class_branch', 'class_cnname', 'hour_day', 'hour_times', 'hour_number', 'bishoptename', 'assistanttename', 'hour_ischecking');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($file_name_add . "网课排课进度报表" . ".xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            if ($refundList) {
                foreach ($refundList as &$val) {
                    $val['hour_ischecking'] = $status[$val['hour_ischecking']];
                }

            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT
                 s.school_id,
                 s.school_branch,
                 s.school_cnname,
                 c.class_branch,
                 c.class_cnname,
                 h.hour_id,
                 h.hour_day,
                 CONCAT(
                  h.hour_starttime,
                  '-',
                  h.hour_endtime
                 ) AS hour_times,
                 h.hour_number,
                 (
                  SELECT
                   f.staffer_cnname
                  FROM
                   smc_class_hour_teaching AS t,
                   smc_staffer AS f
                  WHERE
                   t.staffer_id = f.staffer_id AND t.hour_id = h.hour_id
                  AND t.teaching_type = '0'
                  LIMIT 0,
                  1
                 ) AS bishoptename,
                 (
                  SELECT
                   f.staffer_cnname
                  FROM
                   smc_class_hour_teaching AS t,
                   smc_staffer AS f
                  WHERE
                   t.staffer_id = f.staffer_id AND t.hour_id = h.hour_id
                  AND t.teaching_type = '1'
                  LIMIT 0,
                  1
                 ) AS assistanttename
                FROM
                 smc_class_hour AS h,
                 smc_class AS c,
                 smc_school AS s,
                 smc_course as co
                WHERE
                {$datawhere}
                AND h.class_id = c.class_id
                AND c.school_id = s.school_id
                AND co.course_id = c.course_id
                AND h.hour_way = '1'
                AND c.company_id = '{$request['company_id']}'";

                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }
    }

    //教师代课课时统计表
    function teaTeachReport($request)
    {
        $datawhere = " 1 ";
        $outwhere = " 1 ";
        $subwhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND l.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and l.school_id = '{$request['school_id']}' ";
        } else {
            $datawhere .= " and l.school_istest<>'1' and l.school_isclose<>'1' ";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and l.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND l.school_istest <> '1' AND l.school_isclose<> '1' ";
        }

        $file_name_add = '';
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " AND cs.coursetype_id='{$request['coursetype_id']}'";
            $subwhere .= " AND w.coursetype_id='{$request['coursetype_id']}'";
            $coursetypeOne = $this->DataControl->getFieldOne('smc_code_coursetype', "concat(coursetype_cnname,'(',coursetype_branch,')') as coursetype", " coursetype_id= '{$request['coursetype_id']}'");
            $file_name_add .= $coursetypeOne['coursetype'] . '_';
        }

        if (isset($request['coursetype_array_id']) && $request['coursetype_array_id'] !== '') {
            $coursetype_array = json_decode(stripslashes($request['coursetype_array_id']), 1);
            if (is_array($coursetype_array) && count($coursetype_array) > 0) {
                $string = implode($coursetype_array, ',');
                $datawhere .= " and cs.coursetype_id in ({$string})";
                $subwhere .= " and w.coursetype_id in ({$string})";
                $coursetypeOne = $this->DataControl->selectClear("select group_concat(concat(coursetype_cnname,'(',coursetype_branch,')')) as coursetype from smc_code_coursetype where coursetype_id in ({$string}) ");
                $file_name_add .= $coursetypeOne['coursetype'] . '_';
            }
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " AND cs.coursecat_id='{$request['coursecat_id']}'";
            $subwhere .= " AND w.coursecat_id='{$request['coursecat_id']}'";
            $coursecatOne = $this->DataControl->getFieldOne('smc_code_coursecat', "concat(coursecat_cnname,'(',coursecat_branch,')') as coursecat", " coursecat_id= '{$request['coursecat_id']}'");
            $file_name_add .= $coursecatOne['coursecat'] . '_';
        }

        if (isset($request['teaching_ischecking']) && $request['teaching_ischecking'] !== "") {
            $datawhere .= " and h.hour_ischecking ='{$request['teaching_ischecking']}'";
            $subwhere .= " and y.hour_ischecking ='{$request['teaching_ischecking']}'";
        }

        if (isset($request['teachtype_code']) && $request['teachtype_code'] !== '') {
            $datawhere .= " and t.teachtype_code='{$request['teachtype_code']}'";
            $subwhere .= " and x.teachtype_code='{$request['teachtype_code']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and h.hour_day <= '{$request['end_time']}'";
            $subwhere .= " and y.hour_day <= '{$request['end_time']}'";
            $outwhere .= " and ho.hour_day <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and h.hour_day >= '{$request['start_time']}'";
            $subwhere .= " and y.hour_day >= '{$request['start_time']}'";
            $outwhere .= " and ho.hour_day >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    l.school_id,
                    l.school_branch,
                    l.school_cnname,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = l.school_province ) as province_name,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = l.district_id) AS district_cnname,
                    s.staffer_id,
                    s.staffer_cnname,
                    s.staffer_enname,
                    s.staffer_branch,
                    s.staffer_employeepid,
                    s.staffer_native,
                    s.staffer_isparttime,
                    q.post_name,q.post_sort,
                    s.staffer_leave,
                    s.staffer_mobile,
                    IFNULL(p.postbe_ismianjob,0) AS ismianjob,
                    count(distinct h.class_id,if(h.hour_way='0' and t.teaching_type='0',true,null)) AS offline_classnums,
                    count(distinct h.class_id,if(h.hour_way='1' and t.teaching_type='0',true,null)) AS online_classnums,
                    sum(if(h.hour_way='0' and t.teaching_type='0',h.hour_classtimes,0) )  AS offline_hours_main,
                    sum(if(h.hour_way='0' and t.teaching_type='1',h.hour_classtimes,0) )  AS offline_hours_sub,
                    sum(if(h.hour_way='1' and t.teaching_type='0',h.hour_classtimes,0) )  AS online_hours_main,
                    sum(if(h.hour_way='1' and t.teaching_type='1',h.hour_classtimes,0) )  AS online_hours_sub,
                    sum(if(h.hour_way='0' and t.teaching_type='0',h.hour_classtimes*cs.course_offline_main_percentage,0) )  AS offline_hours_main_calc,
                    sum(if(h.hour_way='0' and t.teaching_type='1',h.hour_classtimes*cs.course_offline_sub_percentage,0) )  AS offline_hours_sub_calc,
                    sum(if(h.hour_way='1' and t.teaching_type='0',h.hour_classtimes*cs.course_online_main_percentage,0) )  AS online_hours_main_calc,
                    sum(if(h.hour_way='1' and t.teaching_type='1',h.hour_classtimes*cs.course_online_sub_percentage,0) )  AS online_hours_sub_calc,
                    ifnull((select sum(ho.hour_classtimes) from smc_outclass_hour as ho,smc_code_outclasstype as co where {$outwhere} and ho.outclasstype_id=co.outclasstype_id and ho.staffer_id = s.staffer_id and ho.hour_iscancel = '0' and co.outclasstype_code=0),0) as outclasstimes_class,
                    ifnull((select sum(ho.hour_classtimes) from smc_outclass_hour as ho,smc_code_outclasstype as co where {$outwhere} and ho.outclasstype_id=co.outclasstype_id and ho.staffer_id = s.staffer_id and ho.hour_iscancel = '0' and co.outclasstype_code=1),0) as outclasstimes_other,
                    sum(h.hour_classtimes )AS total_hours_in,
                    (SELECT sum(y.hour_classtimes*(CASE 
                    when y.hour_way='0' and x.teaching_type='0' then w.course_offline_main_percentage 
                    when y.hour_way='0' and x.teaching_type='1' then w.course_offline_sub_percentage 
                    when y.hour_way='1' and x.teaching_type='0' then w.course_online_main_percentage 
                    when y.hour_way='1' and x.teaching_type='1' then w.course_online_sub_percentage 
                    end))
                    FROM smc_class_hour_teaching x
                    LEFT JOIN smc_class_hour y ON x.class_id = y.class_id AND x.hour_id = y.hour_id
                    LEFT JOIN smc_class z ON y.class_id = z.class_id 
                    LEFT JOIN smc_course w ON y.course_id=w.course_id
                    WHERE {$subwhere}
                    AND x.staffer_id = s.staffer_id 
                    AND z.school_id <> l.school_id 
                    AND y.hour_ischecking >- 1 
                    and x.teaching_isdel=0
                    AND z.class_status >- 2 
                    ) AS total_hours_out 
                FROM smc_class_hour_teaching AS t 
                left join smc_class_hour AS h on t.hour_id=h.hour_id 
                left join smc_class AS c on h.class_id=c.class_id 
                left join smc_staffer AS s on s.staffer_id = t.staffer_id  
                left join gmc_staffer_postbe p on p.staffer_id=s.staffer_id and p.school_id=c.school_id 
                left join gmc_company_post q on p.company_id=q.company_id and p.post_id=q.post_id 
                left join smc_school AS l on c.school_id=l.school_id
                left join smc_course AS cs on c.course_id=cs.course_id and c.company_id=cs.company_id
                WHERE 
                {$datawhere}
                AND h.hour_ischecking >- 1 
                and t.teaching_isdel=0
                AND c.class_status >- 2 
                AND t.staffer_id <> '0' 	
                AND s.company_id = '{$request['company_id']}' 
                GROUP BY l.school_id,s.staffer_id  
                ORDER BY (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end),l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc,l.school_id,q.post_sort
              ";

        $statuss = $this->LgArraySwitch(array('0' => '全职', '1' => '兼职'));
        $jobstatuss = $this->LgArraySwitch(array('0' => '在职', '1' => '离职'));
        $ismainjob = $this->LgArraySwitch(array('0' => '跨校', '1' => '主职'));
        $nativecode = $this->LgArraySwitch(array("0" => "陆籍", "1" => "外籍", "2" => "港澳籍", "3" => "台籍"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];

                    $datearray['staffer_employeepid'] = $dateexcelvar['staffer_employeepid'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];

                    $datearray['staffer_leave'] = $jobstatuss[$dateexcelvar['staffer_leave']];
                    $datearray['staffer_isparttime'] = $statuss[$dateexcelvar['staffer_isparttime']];
                    $datearray['ismianjob'] = $ismainjob[$dateexcelvar['ismianjob']];
                    $datearray['staffer_native'] = $nativecode[$dateexcelvar['staffer_native']];
                    $datearray['post_name'] = $dateexcelvar['post_name'];

                    $datearray['offline_classnums'] = $dateexcelvar['offline_classnums'];
                    $datearray['online_classnums'] = $dateexcelvar['online_classnums'];
                    $datearray['offline_hours_main'] = ceil($dateexcelvar['offline_hours_main'] / 36) / 100;
                    $datearray['offline_hours_sub'] = ceil($dateexcelvar['offline_hours_sub'] / 36) / 100;
                    $datearray['online_hours_main'] = ceil($dateexcelvar['online_hours_main'] / 36) / 100;
                    $datearray['online_hours_sub'] = ceil($dateexcelvar['online_hours_sub'] / 36) / 100;

                    $datearray['outclasstimes_class'] = $dateexcelvar['outclasstimes_class'];
                    $datearray['outclasstimes_other'] = $dateexcelvar['outclasstimes_other'];
                    $datearray['outclasstimes_all'] = $datearray['outclasstimes_class'] + $datearray['outclasstimes_other'];

                    $datearray['total_hours_in'] = ceil(($dateexcelvar['offline_hours_main_calc'] + $dateexcelvar['offline_hours_sub_calc'] + $dateexcelvar['online_hours_main_calc'] + $dateexcelvar['online_hours_sub_calc']) / 36) / 100;
                    $datearray['total_hours_out'] = ceil($dateexcelvar['total_hours_out'] / 36) / 100;
                    $datearray['total_hours'] = $datearray['total_hours_in'] + $datearray['total_hours_out'] + $datearray['outclasstimes_all'];

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("所属区域", "省份", "校区编号", "分校名称", "员工编码", "教师中文名", "教师英文名", "教师编号", "在职状态", "职务类型", "是否主职", "籍贯", "职位", "实体课带班数量（主教）", "线上课带班数量（主教）", "实体课主教小时数", "实体课助教小时数", "线上课主教小时数", "线上课助教小时数", "班外教学课时数", "班外其他课时数", "班外总课时数", "本校总小时数", "跨校总小时数", "总教学小时数"));
            $excelfileds = array('district_cnname', 'province_name', 'school_branch', 'school_cnname', 'staffer_employeepid', 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'staffer_leave', 'staffer_isparttime', 'ismianjob', 'staffer_native', 'post_name', 'offline_classnums', 'online_classnums', 'offline_hours_main', 'offline_hours_sub', 'online_hours_main', 'online_hours_sub', 'outclasstimes_class', 'outclasstimes_other', 'outclasstimes_all', 'total_hours_in', 'total_hours_out', 'total_hours');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $file_name_add . "教师排课统计表" . ".xlsx");
            exit;
        } else {
            $sql = "SELECT
                    l.school_id,
                    l.school_branch,
                    l.school_cnname,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = l.school_province ) as province_name,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = l.district_id) AS district_cnname,
                    s.staffer_id,
                    s.staffer_cnname,
                    s.staffer_enname,
                    s.staffer_branch,
                    s.staffer_native,
                    s.staffer_isparttime,
                    q.post_name,q.post_sort,
                    s.staffer_leave,
                    s.staffer_mobile,
                    IFNULL(p.postbe_ismianjob,0) AS ismianjob,
                    count(distinct h.class_id,if(h.hour_way='0' and t.teaching_type='0',true,null)) AS offline_classnums,
                    count(distinct h.class_id,if(h.hour_way='1' and t.teaching_type='0',true,null)) AS online_classnums,
                    sum(if(h.hour_way='0' and t.teaching_type='0',h.hour_classtimes,0) )  AS offline_hours_main,
                    sum(if(h.hour_way='0' and t.teaching_type='1',h.hour_classtimes,0) )  AS offline_hours_sub,
                    sum(if(h.hour_way='1' and t.teaching_type='0',h.hour_classtimes,0) )  AS online_hours_main,
                    sum(if(h.hour_way='1' and t.teaching_type='1',h.hour_classtimes,0) )  AS online_hours_sub,
                    sum(if(h.hour_way='0' and t.teaching_type='0',h.hour_classtimes*cs.course_offline_main_percentage,0) )  AS offline_hours_main_calc,
                    sum(if(h.hour_way='0' and t.teaching_type='1',h.hour_classtimes*cs.course_offline_sub_percentage,0) )  AS offline_hours_sub_calc,
                    sum(if(h.hour_way='1' and t.teaching_type='0',h.hour_classtimes*cs.course_online_main_percentage,0) )  AS online_hours_main_calc,
                    sum(if(h.hour_way='1' and t.teaching_type='1',h.hour_classtimes*cs.course_online_sub_percentage,0) )  AS online_hours_sub_calc,
                    ifnull((select sum(ho.hour_classtimes) from smc_outclass_hour as ho,smc_code_outclasstype as co where {$outwhere} and ho.outclasstype_id=co.outclasstype_id and ho.staffer_id = s.staffer_id and ho.hour_iscancel = '0' and co.outclasstype_code=0),0) as outclasstimes_class,
                    ifnull((select sum(ho.hour_classtimes) from smc_outclass_hour as ho,smc_code_outclasstype as co where {$outwhere} and ho.outclasstype_id=co.outclasstype_id and ho.staffer_id = s.staffer_id and ho.hour_iscancel = '0' and co.outclasstype_code=1),0) as outclasstimes_other,
                    sum(h.hour_classtimes )AS total_hours_in
                FROM smc_class_hour_teaching AS t 
                left join smc_class_hour AS h on t.hour_id=h.hour_id 
                left join smc_class AS c on h.class_id=c.class_id 
                left join smc_staffer AS s on s.staffer_id = t.staffer_id  
                left join gmc_staffer_postbe p on p.staffer_id=s.staffer_id and p.school_id=c.school_id 
                left join gmc_company_post q on p.company_id=q.company_id and p.post_id=q.post_id 
                left join smc_school AS l on c.school_id=l.school_id
                left join smc_course AS cs on c.course_id=cs.course_id and c.company_id=cs.company_id
                WHERE 
                {$datawhere}
                AND h.hour_ischecking >- 1 
                AND c.class_status >- 2 
                and t.teaching_isdel=0
                AND t.staffer_id <> '0' 
                AND s.company_id = '{$request['company_id']}' 
                GROUP BY l.school_id,s.staffer_id  
                ORDER BY (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end),l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc,l.school_id,q.post_sort
              ";
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($refundList as &$one) {
                $one['staffer_isparttime'] = $statuss[$one['staffer_isparttime']];
                $one['staffer_leave'] = $jobstatuss[$one['staffer_leave']];
                $one['ismianjob'] = $ismainjob[$one['ismianjob']];
                $one['staffer_native'] = $nativecode[$one['staffer_native']];

                $one['offline_hours_main'] = ceil($one['offline_hours_main'] / 36) / 100;
                $one['offline_hours_sub'] = ceil($one['offline_hours_sub'] / 36) / 100;
                $one['online_hours_main'] = ceil($one['online_hours_main'] / 36) / 100;
                $one['online_hours_sub'] = ceil($one['online_hours_sub'] / 36) / 100;

                $one['total_hours_in'] = ceil(($one['offline_hours_main_calc'] + $one['offline_hours_sub_calc'] + $one['online_hours_main_calc'] + $one['online_hours_sub_calc']) / 36) / 100;

                $subsql = " SELECT sum(y.hour_classtimes*(CASE 
                    when y.hour_way='0' and x.teaching_type='0' then w.course_offline_main_percentage 
                    when y.hour_way='0' and x.teaching_type='1' then w.course_offline_sub_percentage 
                    when y.hour_way='1' and x.teaching_type='0' then w.course_online_main_percentage 
                    when y.hour_way='1' and x.teaching_type='1' then w.course_online_sub_percentage 
                    end)) as total_hours_out
                    FROM smc_class_hour_teaching x
                    LEFT JOIN smc_class_hour y ON x.class_id = y.class_id AND x.hour_id = y.hour_id
                    LEFT JOIN smc_class z ON y.class_id = z.class_id 
                    LEFT JOIN smc_course w ON y.course_id=w.course_id
                    WHERE {$subwhere}
                    AND x.staffer_id = '{$one['staffer_id']}'
                    AND z.school_id <> '{$one['school_id']}'
                    AND y.hour_ischecking >- 1
                    and x.teaching_isdel=0
                    AND z.class_status >- 2 ";
                $total_hours_out_One = $this->AnalyzeControl->selectOne($subsql);
                if ($total_hours_out_One) {
                    $one['total_hours_out'] = ceil($total_hours_out_One['total_hours_out'] / 36) / 100;
                } else {
                    $one['total_hours_out'] = 0;
                }
                $one['outclasstimes_class'] = round($one['outclasstimes_class'], 2);
                $one['outclasstimes_other'] = round($one['outclasstimes_other'], 2);
                $one['outclasstimes_all'] = $one['outclasstimes_class'] + $one['outclasstimes_other'];
                $one['total_hours'] = $one['total_hours_in'] + $one['total_hours_out'] + $one['outclasstimes_all'];

            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                    SELECT  l.school_id,
                    s.staffer_id
                    FROM
                    smc_class_hour_teaching AS t 
                    left join smc_class_hour AS h on t.hour_id=h.hour_id 
                    left join smc_class AS c on h.class_id=c.class_id 
                    left join smc_staffer AS s on s.staffer_id = t.staffer_id 
                    left join gmc_staffer_postbe p on p.staffer_id=s.staffer_id and p.school_id=c.school_id 
                    left join smc_school AS l on l.school_id=c.school_id 
                    left join smc_course AS cs on c.course_id=cs.course_id and c.company_id=cs.company_id
                WHERE
                {$datawhere}
                AND h.hour_ischecking >- 1 
                AND c.class_status >- 2
                and t.teaching_isdel=0
                AND t.staffer_id <> '0' 	
                AND s.company_id = '{$request['company_id']}'
                GROUP BY l.school_id,s.staffer_id  ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['teachtype'] = $this->DataControl->selectClear("select teachtype_code,teachtype_name from smc_code_teachtype where company_id = '{$request['company_id']}'");


            $data['list'] = $refundList;

            return $data;
        }
    }

    //教师代课课时统计表8888-65
    function teaTeachReport_8888($request)
    {
        $file_name_add = '美语类_';
        $datawhere = " 1 ";
        $outwhere = " 1 ";
        $subwhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND l.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and l.school_id = '{$request['school_id']}' ";
        } else {
            $datawhere .= " and l.school_istest<>'1' and l.school_isclose<>'1' ";
        }

        $datawhere .= " and (cs.course_id in(515,70987) or cs.coursecat_id=11802 or cs.coursetype_id in(65,79660,79661,101))";
        $subwhere .= " and (w.course_id in(515,70987) or w.coursecat_id=11802 or w.coursetype_id in(65,79660,79661,101))";

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " AND cs.coursecat_id='{$request['coursecat_id']}'";
            $subwhere .= " AND w.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['teaching_ischecking']) && $request['teaching_ischecking'] !== "") {
            $datawhere .= " and h.hour_ischecking ='{$request['teaching_ischecking']}'";
            $subwhere .= " and y.hour_ischecking ='{$request['teaching_ischecking']}'";
        }

        if (isset($request['teachtype_code']) && $request['teachtype_code'] !== '') {
            $datawhere .= " and t.teachtype_code='{$request['teachtype_code']}'";
            $subwhere .= " and x.teachtype_code='{$request['teachtype_code']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and h.hour_day <= '{$request['end_time']}'";
            $subwhere .= " and y.hour_day <= '{$request['end_time']}'";
            $outwhere .= " and a.hour_day <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and h.hour_day >= '{$request['start_time']}'";
            $subwhere .= " and y.hour_day >= '{$request['start_time']}'";
            $outwhere .= " and a.hour_day >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    l.school_id,
                    l.school_branch,
                    l.school_cnname,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = l.school_province ) as province_name,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = l.district_id) AS district_cnname,
                    s.staffer_id,
                    s.staffer_cnname,
                    s.staffer_enname,
                    s.staffer_branch,
                    s.staffer_employeepid,
                    s.staffer_native,
                    s.staffer_isparttime,
                    q.post_name,q.post_sort,
                    s.staffer_leave,
                    s.staffer_mobile,
                    IFNULL(p.postbe_ismianjob,0) AS ismianjob,
                    count(distinct h.class_id,if(t.teaching_type='0' and cs.coursetype_id<>101,true,null)) AS inner_classnums,
                    sum(if(t.teaching_type='0',h.hour_classtimes,0))  AS inner_hour_main, -- *cs.course_offline_main_percentage
                    sum(if(t.teaching_type='1',h.hour_classtimes,0))  AS inner_hour_sub -- *cs.course_offline_sub_percentage
                FROM smc_class_hour_teaching AS t 
                left join smc_class_hour AS h on t.hour_id=h.hour_id 
                left join smc_class AS c on h.class_id=c.class_id 
                left join smc_staffer AS s on s.staffer_id = t.staffer_id  
                left join gmc_staffer_postbe p on p.staffer_id=s.staffer_id and p.school_id=c.school_id 
                left join gmc_company_post q on p.company_id=q.company_id and p.post_id=q.post_id 
                left join smc_school AS l on c.school_id=l.school_id
                left join smc_course AS cs on c.course_id=cs.course_id and c.company_id=cs.company_id
                WHERE 
                {$datawhere}
                AND h.hour_ischecking>-1 
                and h.hour_way='0'
                AND c.class_status >- 2 
                and t.teaching_isdel=0
                AND t.staffer_id <> '0' 	
                AND s.company_id = '{$request['company_id']}' 
                GROUP BY l.school_id,s.staffer_id  
                ORDER BY (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end),l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc,l.school_id,q.post_sort
              ";

        $outsql = "select a.school_id,a.staffer_id,sum(a.hour_classtimes*c.outclasstype_rate) as outclass_hour
                from smc_outclass_hour a
                left join smc_staffer b on a.staffer_id=b.staffer_id
                left join smc_code_outclasstype c on c.outclasstype_id=a.outclasstype_id
                where {$outwhere}
                and b.company_id= '{$request['company_id']}' 
                and a.hour_iscancel=0
                group by a.school_id,a.staffer_id
                order by a.school_id,a.staffer_id";
        $outclasshourarray = $this->DataControl->selectClear($outsql);

        $statuss = $this->LgArraySwitch(array('0' => '全职', '1' => '兼职'));
        $jobstatuss = $this->LgArraySwitch(array('0' => '在职', '1' => '离职'));
        $ismainjob = $this->LgArraySwitch(array('0' => '跨校', '1' => '主职'));
        $nativecode = $this->LgArraySwitch(array("0" => "陆籍", "1" => "外籍", "2" => "港澳籍", "3" => "台籍"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            $headersarray = array("所属区域", "省份", "校区编号", "分校名称", "员工编码", "教师中文名", "教师英文名", "教师编号", "在职状态", "职务类型", "是否主职", "籍贯", "职位"
            , "本校班级数", "跨校班级数", "本校主教时数", "本校助教时数", "本校班外时数", "本校总时数", "跨校总时数", "总时数");
            $fieldsarray = array('district_cnname', 'province_name', 'school_branch', 'school_cnname', 'staffer_employeepid', 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'staffer_leave', 'staffer_isparttime', 'ismianjob', 'staffer_native', 'post_name'
            , 'inner_classnums', 'outer_classnums', 'inner_hour_main', 'inner_hour_sub', 'inner_outclass_hour', 'inner_total', 'outer_total', 'all_total');
            if ($dateexcelarray) {
                foreach ($dateexcelarray as &$one) {
                    $one['staffer_isparttime'] = $statuss[$one['staffer_isparttime']];
                    $one['staffer_leave'] = $jobstatuss[$one['staffer_leave']];
                    $one['ismianjob'] = $ismainjob[$one['ismianjob']];
                    $one['staffer_native'] = $nativecode[$one['staffer_native']];

                    $subsql = " SELECT count(distinct y.class_id) as outer_classnums,
                    sum(y.hour_classtimes) as outer_hour_total -- *(CASE when x.teaching_type='0' then w.course_offline_main_percentage when x.teaching_type='1' then w.course_offline_sub_percentage end)
                    FROM smc_class_hour_teaching x
                    LEFT JOIN smc_class_hour y ON x.class_id = y.class_id AND x.hour_id = y.hour_id
                    LEFT JOIN smc_class z ON y.class_id = z.class_id 
                    LEFT JOIN smc_course w ON y.course_id=w.course_id 
                    WHERE {$subwhere}
                    and x.teaching_isdel=0
                    AND x.staffer_id='{$one['staffer_id']}'
                    AND z.school_id<>'{$one['school_id']}'
                    AND y.hour_ischecking>-1
                    AND y.hour_way='0'
                    AND z.class_status>-2 ";
                    $outer_hour_one = $this->AnalyzeControl->selectOne($subsql);
                    if ($outer_hour_one) {
                        $one['outer_classnums'] = $outer_hour_one['outer_classnums'];
                        $one['outer_hour_total'] = ceil($outer_hour_one['outer_hour_total'] / 36) / 100;
                    } else {
                        $one['outer_classnums'] = 0;
                        $one['outer_hour_total'] = 0;
                    }

                    $inner_outclass_hour = 0;
                    $outer_outclass_hour = 0;
                    foreach ($outclasshourarray as $outclassone) {
                        $this_one_hour = 0;
                        if ($outclassone['staffer_id'] == $one['staffer_id']) {
                            $this_one_hour = round($outclassone['outclass_hour'], 2);
                        }
                        if ($outclassone['school_id'] == $one['school_id']) {
                            $inner_outclass_hour += $this_one_hour;
                        } else {
                            $outer_outclass_hour += $this_one_hour;
                        }
                    }

                    $one['inner_hour_main'] = ceil($one['inner_hour_main'] / 36) / 100;
                    $one['inner_hour_sub'] = ceil($one['inner_hour_sub'] / 36) / 100;
                    $one['inner_outclass_hour'] = $inner_outclass_hour;

                    $one['inner_total'] = $one['inner_hour_main'] + $one['inner_hour_sub'] + $inner_outclass_hour;
                    $one['outer_total'] = $one['outer_hour_total'] + $outer_outclass_hour;
                    $one['all_total'] = $one['inner_total'] + $one['outer_total'];
                }
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['staffer_employeepid'] = $dateexcelvar['staffer_employeepid'];

                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['staffer_leave'] = $dateexcelvar['staffer_leave'];

                    $datearray['staffer_isparttime'] = $dateexcelvar['staffer_isparttime'];
                    $datearray['ismianjob'] = $dateexcelvar['ismianjob'];
                    $datearray['staffer_native'] = $dateexcelvar['staffer_native'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];

                    $datearray['inner_classnums'] = $dateexcelvar['inner_classnums'];
                    $datearray['outer_classnums'] = $dateexcelvar['outer_classnums'];
                    $datearray['inner_hour_main'] = $dateexcelvar['inner_hour_main'];
                    $datearray['inner_hour_sub'] = $dateexcelvar['inner_hour_sub'];

                    $datearray['inner_outclass_hour'] = $dateexcelvar['inner_outclass_hour'];
                    $datearray['inner_total'] = $dateexcelvar['inner_total'];
                    $datearray['outer_total'] = $dateexcelvar['outer_total'];
                    $datearray['all_total'] = $dateexcelvar['all_total'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch($headersarray);
            $excelfileds = $fieldsarray;

            query_to_excel($excelheader, $outexceldate, $excelfileds, $file_name_add . "教师排课统计表" . ".xlsx");
            exit;
        } else {
            $sql = "SELECT
                    l.school_id,
                    l.school_branch,
                    l.school_cnname,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = l.school_province ) as province_name,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = l.district_id) AS district_cnname,
                    s.staffer_id,
                    s.staffer_cnname,
                    s.staffer_enname,
                    s.staffer_branch,
                    s.staffer_employeepid,
                    s.staffer_native,
                    s.staffer_isparttime,
                    q.post_name,
                    q.post_sort,
                    s.staffer_leave,
                    s.staffer_mobile,
                    IFNULL(p.postbe_ismianjob,0) AS ismianjob,
                    count(distinct h.class_id,if(t.teaching_type='0' and cs.coursetype_id<>101,true,null)) AS inner_classnums,
                    sum(if(t.teaching_type='0',h.hour_classtimes,0))  AS inner_hour_main, -- *cs.course_offline_main_percentage
                    sum(if(t.teaching_type='1',h.hour_classtimes,0))  AS inner_hour_sub -- *cs.course_offline_sub_percentage
                FROM smc_class_hour_teaching AS t 
                left join smc_class_hour AS h on t.hour_id=h.hour_id 
                left join smc_class AS c on h.class_id=c.class_id 
                left join smc_staffer AS s on s.staffer_id = t.staffer_id  
                left join gmc_staffer_postbe p on p.staffer_id=s.staffer_id and p.school_id=c.school_id 
                left join gmc_company_post q on p.company_id=q.company_id and p.post_id=q.post_id 
                left join smc_school AS l on c.school_id=l.school_id
                left join smc_course AS cs on c.course_id=cs.course_id and c.company_id=cs.company_id
                WHERE 
                {$datawhere}
                AND h.hour_ischecking>-1 
                and h.hour_way='0'
                AND c.class_status>-2 
                and t.teaching_isdel=0
                AND t.staffer_id<>'0' 
                AND s.company_id='{$request['company_id']}' 
                GROUP BY l.school_id,s.staffer_id  
                ORDER BY (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end),l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc,l.school_id,q.post_sort
              ";
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $hourCalcList = $this->DataControl->selectClear($sql);
            if (!$hourCalcList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($hourCalcList as &$one) {
                $one['staffer_isparttime'] = $statuss[$one['staffer_isparttime']];
                $one['staffer_leave'] = $jobstatuss[$one['staffer_leave']];
                $one['ismianjob'] = $ismainjob[$one['ismianjob']];
                $one['staffer_native'] = $nativecode[$one['staffer_native']];

                $subsql = " SELECT count(distinct y.class_id) as outer_classnums,
                    sum(y.hour_classtimes) as outer_hour_total -- *(CASE when x.teaching_type='0' then w.course_offline_main_percentage when x.teaching_type='1' then w.course_offline_sub_percentage end)
                    FROM smc_class_hour_teaching x
                    LEFT JOIN smc_class_hour y ON x.class_id = y.class_id AND x.hour_id = y.hour_id
                    LEFT JOIN smc_class z ON y.class_id = z.class_id 
                    LEFT JOIN smc_course w ON y.course_id=w.course_id 
                    WHERE {$subwhere}
                    and x.teaching_isdel=0
                    AND x.staffer_id='{$one['staffer_id']}'
                    AND z.school_id<>'{$one['school_id']}'
                    AND y.hour_ischecking>-1
                    AND y.hour_way='0'
                    AND z.class_status>-2 ";
                $outer_hour_one = $this->AnalyzeControl->selectOne($subsql);
                if ($outer_hour_one) {
                    $one['outer_classnums'] = $outer_hour_one['outer_classnums'];
                    $one['outer_hour_total'] = ceil($outer_hour_one['outer_hour_total'] / 36) / 100;
                } else {
                    $one['outer_classnums'] = 0;
                    $one['outer_hour_total'] = 0;
                }

                $inner_outclass_hour = 0;
                $outer_outclass_hour = 0;
                foreach ($outclasshourarray as $outclassone) {
                    $this_one_hour = 0;
                    if ($outclassone['staffer_id'] == $one['staffer_id']) {
                        $this_one_hour = round($outclassone['outclass_hour'], 2);
                    }
                    if ($outclassone['school_id'] == $one['school_id']) {
                        $inner_outclass_hour += $this_one_hour;
                    } else {
                        $outer_outclass_hour += $this_one_hour;
                    }
                }

                $one['inner_hour_main'] = ceil($one['inner_hour_main'] / 36) / 100;
                $one['inner_hour_sub'] = ceil($one['inner_hour_sub'] / 36) / 100;
                $one['inner_outclass_hour'] = $inner_outclass_hour;

                $one['inner_total'] = $one['inner_hour_main'] + $one['inner_hour_sub'] + $inner_outclass_hour;
                $one['outer_total'] = $one['outer_hour_total'] + $outer_outclass_hour;
                $one['all_total'] = $one['inner_total'] + $one['outer_total'];
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT l.school_id,s.staffer_id 
                FROM smc_class_hour_teaching AS t 
                left join smc_class_hour AS h on t.hour_id=h.hour_id 
                left join smc_class AS c on h.class_id=c.class_id 
                left join smc_staffer AS s on s.staffer_id = t.staffer_id 
                left join gmc_staffer_postbe p on p.staffer_id=s.staffer_id and p.school_id=c.school_id 
                left join smc_school AS l on l.school_id=c.school_id 
                left join smc_course AS cs on c.course_id=cs.course_id and c.company_id=cs.company_id 
                WHERE {$datawhere} 
                AND h.hour_ischecking>-1 
                and h.hour_way='0'
                AND c.class_status >- 2 
                and t.teaching_isdel=0 
                AND t.staffer_id <> '0' 
                AND s.company_id = '{$request['company_id']}'
                GROUP BY l.school_id,s.staffer_id ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['teachtype'] = $this->DataControl->selectClear("select teachtype_code,teachtype_name from smc_code_teachtype where company_id = '{$request['company_id']}'");

            $k = 0;
            $field = array();

            $field[$k]["fieldstring"] = "district_cnname";
            $field[$k]["fieldname"] = "所属区域";
            $field[$k]["show"] = (isset($request["company_id"]) && $request["company_id"] == '8888') ? 1 : 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = "省份";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "分校名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_employeepid";
            $field[$k]["fieldname"] = "HR编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_cnname";
            $field[$k]["fieldname"] = "教师中文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_enname";
            $field[$k]["fieldname"] = "教师英文名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_branch";
            $field[$k]["fieldname"] = "教师编号";
            $field[$k]["show"] = 0;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_leave";
            $field[$k]["fieldname"] = "在职状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_isparttime";
            $field[$k]["fieldname"] = "职务类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "ismianjob";
            $field[$k]["fieldname"] = "是否主职";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "staffer_native";
            $field[$k]["fieldname"] = "籍贯";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "post_name";
            $field[$k]["fieldname"] = "职位";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "inner_classnums";
            $field[$k]["fieldname"] = "本校带班数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outer_classnums";
            $field[$k]["fieldname"] = "跨校带班数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "inner_hour_main";
            $field[$k]["fieldname"] = "本校主教时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "inner_hour_sub";
            $field[$k]["fieldname"] = "本校助教时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
//            $field[$k]["ismethod"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "inner_outclass_hour";
            $field[$k]["fieldname"] = "本校班外时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "inner_total";
            $field[$k]["fieldname"] = "本校总时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "outer_total";
            $field[$k]["fieldname"] = "跨校总时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "all_total";
            $field[$k]["fieldname"] = "总时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $data['field'] = $field;
            $data['list'] = $hourCalcList;

            return $data;
        }
    }

    function stuConsumptionReport($request)
    {
        $datawhere = " A.hour_isfree=0 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (A.student_cnname like '%{$request['keyword']}%' or A.student_enname like '%{$request['keyword']}%' or A.student_branch like '%{$request['keyword']}%'
            or A.course_branch like '%{$request['keyword']}%' or A.course_cnname like '%{$request['keyword']}%'
            or A.class_branch like '%{$request['keyword']}%' or A.class_enname like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = $request['start_time'];
        } else {
            $starttime = date('Y-m-01');
        }
        $datawhere .= " and A.hour_day >='{$starttime}'";
        $datawhere .= " and A.study_endday >='{$starttime}'";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = $request['end_time'];
        } else {
            $endtime = date('Y-m-d');
        }
        $datawhere .= " and A.hour_day <='{$endtime}'";
        $datawhere .= " and A.study_beginday <='{$endtime}'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " and sch.school_istest<>'1' and sch.school_isclose<>'1' ";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and sch.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND sch.school_istest <> '1' AND sch.school_isclose<> '1' ";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] != '') {
            $postbeOne = $this->AnalyzeControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
            if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
                $datawhere .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
            }
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and A.coursecat_id='{$request['coursecat_id']}'";
        }

//        if (!isset($request['school_id']) || $request['school_id'] == '') {
//            $this->error = true;
//            $this->errortip = "请先选择学校";
//            return false;
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select  sch.school_id,sch.school_cnname,sch.school_branch,
                        a.student_id,a.student_cnname,a.student_enname,a.student_branch,c.channel_name,
						a.coursecat_branch,a.coursecat_cnname,a.course_branch,a.course_cnname, 
						a.class_id,a.class_branch,a.class_enname,
						count(hour_isfree) as shouldcheck_times,
						sum(clockinginlog_price) as deduct_price, 
						sum(log_playamount) AS log_playamount,
						count(hourstudy_checkin) as deduct_times,
						sum(hourstudy_checkin) as atte_times,
						(select r.region_name from smc_code_region as r where r.region_id = sch.school_province ) as province_name,
						(case when a.study_endday>='{$endtime}' then IFNULL((SELECT Y.stustatus_inclass FROM smc_student_changelog AS X, smc_code_stuchange AS Y WHERE X.stuchange_code = Y.stuchange_code 
						AND Y.stuchange_type = 0 AND X.company_id = A.company_id AND X.school_id = A.school_id AND X.student_id = A.student_id 
						AND X.class_id = A.class_id AND X.changelog_day<='{$endtime}' ORDER BY changelog_id DESC  LIMIT 0,1 ),1) else a.study_isreading end) as reading_status
						,sum(ifnull(d.income_price,0)-ifnull(e.reduce_price,0)) as supervise_price
						from view_smc_student_study_spend  A
						left join smc_school as sch on sch.school_id=A.school_id
						left join smc_student_guildpolicy C on c.student_id=a.student_id and guildpolicy_enddate>='{$endtime}'
						left join cmb_trans_transfer d on a.hourstudy_id=d.hourstudy_id and d.hourstudy_id>0 and d.transfer_status<>'-3' and d.is_confirm=1
						left join cmb_trans_transfer_reduce e on a.student_id=e.student_id and a.class_id=e.class_id and a.hour_lessontimes=e.hour_lessontimes
						where {$datawhere}
						and A.company_id='{$request['company_id']}'
						group by A.school_id,A.student_id,A.class_id 
						order by (case when sch.school_istest=0 and sch.school_isclose=0 then 1 when sch.school_isclose=0 then 2 when sch.school_istest=0 then 3 else 4 end),sch.school_istest asc,field(sch.school_sort,0),sch.school_sort asc,sch.school_createtime asc,student_branch,course_branch,hour_day
              ";

        $status_type = $this->LgArraySwitch(array("-1" => "已结束", "0" => "不在班", "1" => "在班"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
//            $sql.=" limit 20000,20000";
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['reading_status'] = $status_type[$dateexcelvar['reading_status']];
                    $datearray['supervise_price'] = $dateexcelvar['supervise_price'];
                    $datearray['shouldcheck_times'] = $dateexcelvar['shouldcheck_times'];
                    $datearray['uncheck_times'] = $dateexcelvar['shouldcheck_times'] - $dateexcelvar['deduct_times'];
                    $datearray['deduct_times'] = $dateexcelvar['deduct_times'];
                    $datearray['log_playamount'] = $dateexcelvar['log_playamount'] ? $dateexcelvar['log_playamount'] : 0;
                    $datearray['deduct_price'] = $dateexcelvar['deduct_price'] ? ($dateexcelvar['deduct_price'] - $dateexcelvar['log_playamount']) : '';
                    $datearray['atte_times'] = $dateexcelvar['atte_times'];
                    $datearray['unatte_times'] = $dateexcelvar['deduct_times'] - $dateexcelvar['atte_times'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("省份", "校区名称", "校区编号", "中文名", "英文名", "学员编号", "专案名称", "班种名称", "班种编号", "课程别名称", "课程别编号", "班级编号", "班级名称", "在班状态", "监管金额", "应考勤课次", "未考勤课次", "消耗课次", "消耗金额", "优惠金额", "出勤课次", "缺勤课次"));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'channel_name', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', "class_branch", 'class_enname', 'reading_status', 'supervise_price', 'shouldcheck_times', 'uncheck_times', 'deduct_times', 'log_playamount', 'deduct_price', 'atte_times', 'unatte_times');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("学员课次消费报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $spendList = $this->DataControl->selectClear($sql);
            if (!$spendList) {
                $this->error = true;
                $this->errortip = "无学员课次消费数据";
                return false;
            }
            $data = array();
            foreach ($spendList as &$val) {
                $val['log_playamount'] = $val['log_playamount'] ? $val['log_playamount'] : 0;
                $val['deduct_price'] = $val['deduct_price'] ? ($val['deduct_price'] - $val['log_playamount']) : '';
                $val['uncheck_times'] = $val['shouldcheck_times'] - $val['deduct_times'];
                $val['unatte_times'] = $val['deduct_times'] - $val['atte_times'];
                $val['reading_status'] = $status_type[$val['reading_status']];
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                    select A.school_id,A.student_id,A.class_id 
						from view_smc_student_study_spend  A
						left join smc_school as sch on sch.school_id=A.school_id 
						where {$datawhere}
						and A.company_id='{$request['company_id']}'
						group by A.school_id,A.student_id,A.class_id 
						order by student_branch,course_branch,hour_day 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $spendList;
            return $data;
        }
    }

    //课时数据明细报表
    function HourDataDetail($request)
    {
        $datawhere = "cs.company_id='{$request['company_id']}'";
        $hourwhere = "1";
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $startqueryday = $request['start_time'];
        } else {
            $startqueryday = date("Y-m-01");
        }
        $hourwhere .= " AND h.hour_day >= '{$startqueryday}'";
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $endqueryday = $request['end_time'];
        } else {
            $endqueryday = date("Y-m-t");
        }
        $hourwhere .= " AND h.hour_day <= '{$endqueryday}'";

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        } else {
            $datawhere .= " and s.school_istest<>'1'";
            $datawhere .= " and s.school_isclose<>'1'";
        }

        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['type']) && $request['type'] == '1') {

            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (cs.coursetype_cnname like '%{$request['keyword']}%' or cs.coursetype_branch like '%{$request['keyword']}%')";
            }

            $sql = "SELECT s.school_cnname,s.school_branch,cs.coursetype_cnname,cs.coursetype_branch,
                        (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND {$hourwhere})) as attendance_all_num,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND sh.hourstudy_checkin = '0' AND {$hourwhere})) as attendance_not_num,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND sh.hourstudy_checkin = '1' AND {$hourwhere})) as attendance_num,
                        SUM((SELECT COUNT(h.hour_id) FROM smc_class_hour AS h WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND {$hourwhere})) as classhour_num,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND {$hourwhere})) as consumehour_num,
                        SUM((SELECT SUM(ifnull(cb.income_price,0))
                          FROM smc_class_hour AS h
                          LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id
                          LEFT JOIN smc_school_income AS cb ON cb.course_id = h.course_id AND cb.student_id = sh.student_id 
                          AND cb.hourstudy_id = sh.hourstudy_id AND cb.income_type = '0' 
                          WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND cb.school_id = c.school_id AND h.hour_ischecking = '1' AND h.hour_isfree = '0' AND {$hourwhere})) as consumehour_price
                FROM smc_code_coursetype as cs
                LEFT JOIN smc_course as co ON co.coursetype_id = cs.coursetype_id and co.company_id=cs.company_id
                LEFT JOIN smc_class as c ON c.course_id = co.course_id and c.company_id=cs.company_id
                LEFT JOIN smc_school as s ON s.school_id = c.school_id and s.company_id=cs.company_id
                WHERE {$datawhere}
                GROUP BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc,s.school_id,cs.coursetype_id";

        } elseif (isset($request['type']) && $request['type'] == '2') {

            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (cs.coursecat_cnname like '%{$request['keyword']}%' or cs.coursecat_branch like '%{$request['keyword']}%')";
            }

            $sql = "SELECT s.school_cnname,s.school_branch,cs.coursecat_cnname,cs.coursecat_branch,
                        (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND {$hourwhere})) as attendance_all_num,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND sh.hourstudy_checkin = '0' AND {$hourwhere})) as attendance_not_num,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND sh.hourstudy_checkin = '1' AND {$hourwhere})) as attendance_num,
                        SUM((SELECT COUNT(h.hour_id) FROM smc_class_hour AS h WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND {$hourwhere})) as classhour_num,
                        SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND {$hourwhere})) as consumehour_num,
                        SUM((SELECT SUM(ifnull(cb.income_price,0))
                          FROM smc_class_hour AS h
                          LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id
                          LEFT JOIN smc_school_income AS cb ON cb.course_id = h.course_id AND cb.student_id = sh.student_id 
                          AND cb.hourstudy_id = sh.hourstudy_id AND cb.income_type = '0' 
                          WHERE h.course_id = co.course_id AND h.class_id = c.class_id AND cb.school_id = c.school_id AND h.hour_ischecking = '1' AND h.hour_isfree = '0' AND {$hourwhere})) as consumehour_price
                FROM smc_code_coursecat as cs
                LEFT JOIN smc_course as co ON co.coursecat_id = cs.coursecat_id and co.company_id=cs.company_id
                LEFT JOIN smc_class as c ON c.course_id = co.course_id and c.company_id=cs.company_id
                LEFT JOIN smc_school as s ON s.school_id = c.school_id and s.company_id=cs.company_id
                WHERE {$datawhere}
                GROUP BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc,s.school_id,cs.coursecat_id";

        } elseif (isset($request['type']) && $request['type'] == '3') {

            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (cs.course_cnname like '%{$request['keyword']}%' or cs.course_branch like '%{$request['keyword']}%')";
            }

            $sql = "SELECT s.school_cnname,s.school_branch,cs.course_cnname,cs.course_branch,
                    (select r.region_name from smc_code_region as r where r.region_id = s.school_province ) as province_name,
                    SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = cs.course_id AND h.class_id = c.class_id AND {$hourwhere})) as attendance_all_num,
                    SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = cs.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND sh.hourstudy_checkin = '0' AND {$hourwhere})) as attendance_not_num,
                    SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = cs.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND sh.hourstudy_checkin = '1' AND {$hourwhere})) as attendance_num,
                    SUM((SELECT COUNT(h.hour_id) FROM smc_class_hour AS h WHERE h.course_id = cs.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND {$hourwhere})) as classhour_num,
                    SUM((SELECT COUNT(sh.student_id) FROM smc_class_hour AS h LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id WHERE h.course_id = cs.course_id AND h.class_id = c.class_id AND h.hour_ischecking = '1' AND {$hourwhere})) as consumehour_num,
                    SUM((SELECT SUM(ifnull(cb.income_price,0))
                          FROM smc_class_hour AS h
                          LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id
                          LEFT JOIN smc_school_income AS cb ON cb.course_id = h.course_id AND cb.student_id = sh.student_id 
                          AND cb.hourstudy_id = sh.hourstudy_id AND cb.income_type = '0' 
                      WHERE h.course_id = cs.course_id AND h.class_id = c.class_id AND cb.school_id = c.school_id AND h.hour_ischecking = '1' AND h.hour_isfree = '0' AND {$hourwhere})) as consumehour_price
                FROM smc_course as cs
                LEFT JOIN smc_class as c ON c.course_id = cs.course_id and c.company_id=cs.company_id
                LEFT JOIN smc_school as s ON s.school_id = c.school_id and s.company_id=cs.company_id
                WHERE {$datawhere}
                GROUP BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc,s.school_id,cs.course_id";

        } else {
            $this->error = true;
            $this->errortip = "暂无数据";
            return false;
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    if (isset($request['type']) && $request['type'] == '1') {
                        $datearray['cnname'] = $dateexcelvar['coursetype_cnname'];
                        $datearray['branch'] = $dateexcelvar['coursetype_branch'];
                    } elseif (isset($request['type']) && $request['type'] == '2') {
                        $datearray['cnname'] = $dateexcelvar['coursecat_cnname'];
                        $datearray['branch'] = $dateexcelvar['coursecat_branch'];
                    } elseif (isset($request['type']) && $request['type'] == '3') {
                        $datearray['cnname'] = $dateexcelvar['course_cnname'];
                        $datearray['branch'] = $dateexcelvar['course_branch'];
                    }
                    $datearray['attendance_all_num'] = $dateexcelvar['attendance_all_num'];//应到人数
                    $datearray['attendance_not_num'] = $dateexcelvar['attendance_not_num'];//缺勤人数
                    $datearray['attendance_num'] = $dateexcelvar['attendance_num'];//实到人数
                    if ($dateexcelvar['attendance_num']) {
                        $datearray['attendance_rate'] = sprintf("%.4f", ($dateexcelvar['attendance_num'] / $dateexcelvar['attendance_all_num'])) * 100 . '%';//出勤率
                    } else {
                        $datearray['attendance_rate'] = '0%';
                    }
                    $datearray['classhour_num'] = $dateexcelvar['classhour_num'];//上课课时数
                    $datearray['consumehour_num'] = $dateexcelvar['consumehour_num'];//学员累计耗课数
                    $datearray['consumehour_price'] = $dateexcelvar['consumehour_price'];//累计耗课收入
                    if ($dateexcelvar['consumehour_price']) {
                        $datearray['consumehour_avgprice'] = sprintf("%.2f", $dateexcelvar['consumehour_price'] / $dateexcelvar['consumehour_num']);//耗课课时均价
                    } else {
                        $datearray['consumehour_avgprice'] = '0.00';
                    }
                    $outexceldate[] = $datearray;
                }
            }
            if (isset($request['type']) && $request['type'] == '1') {
                $cnname = '班组名称';
                $branch = '班组编号';
            } elseif (isset($request['type']) && $request['type'] == '2') {
                $cnname = '班种名称';
                $branch = '班种编号';
            } elseif (isset($request['type']) && $request['type'] == '3') {
                $cnname = '课程别名称';
                $branch = '课程别编号';
            }

            $excelheader = $this->LgArraySwitch(array("省份", "校区名称", "校区编号", $cnname, $branch, "应到人数", "缺勤人数", '实到人数', "出勤率", "上课课时数", "学员累计耗课数", "累计耗课收入", "耗课课时均价"));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'cnname', 'branch', 'attendance_all_num', 'attendance_not_num', 'attendance_num', 'attendance_rate', 'classhour_num', 'consumehour_num', 'consumehour_price', 'consumehour_avgprice');
            if (isset($request['type']) && $request['type'] == '1') {
                $tip = '班组';
            } elseif (isset($request['type']) && $request['type'] == '2') {
                $tip = '班种';
            } elseif (isset($request['type']) && $request['type'] == '3') {
                $tip = '课程别';
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}{$tip}课时数据明细表{$startqueryday}-{$endqueryday}.xlsx"));
            exit;

        } else {

            $sql .= ' limit ' . $pagestart . ',' . $num;
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }
            foreach ($dataList as &$v) {
                if ($v['attendance_num']) {
                    $v['attendance_rate'] = sprintf("%.4f", ($v['attendance_num'] / $v['attendance_all_num'])) * 100 . '%';
                } else {
                    $v['attendance_rate'] = '0%';
                }
                if ($v['consumehour_price']) {
                    $v['consumehour_avgprice'] = sprintf("%.2f", $v['consumehour_price'] / $v['consumehour_num']);
                } else {
                    $v['consumehour_price'] = '0.00';
                    $v['consumehour_avgprice'] = '0.00';
                }
            }
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {

                if (isset($request['type']) && $request['type'] == '1') {
                    $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.coursetype_id) as num FROM
                                                                (SELECT cs.coursetype_id
                                                                FROM smc_code_coursetype as cs
                                                                LEFT JOIN smc_course as co ON co.coursetype_id = cs.coursetype_id
                                                                LEFT JOIN smc_class as c ON c.course_id = co.course_id
                LEFT JOIN smc_school as s ON s.school_id = c.school_id and s.company_id=cs.company_id
                                                                WHERE {$datawhere} GROUP BY c.school_id,cs.coursetype_id) as q");
                } elseif (isset($request['type']) && $request['type'] == '2') {
                    $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.coursecat_id) as num FROM
                                                                (SELECT cs.coursecat_id
                                                                FROM smc_code_coursecat as cs
                                                                LEFT JOIN smc_course as co ON co.coursecat_id = cs.coursecat_id
                                                                LEFT JOIN smc_class as c ON c.course_id = co.course_id
                LEFT JOIN smc_school as s ON s.school_id = c.school_id and s.company_id=cs.company_id
                                                                WHERE {$datawhere} GROUP BY c.school_id,cs.coursecat_id) as q");
                } elseif (isset($request['type']) && $request['type'] == '3') {
                    $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.course_id) as num FROM
                                                                (SELECT cs.course_id
                                                                FROM smc_course as cs
                                                                LEFT JOIN smc_class as c ON c.course_id = cs.course_id
                LEFT JOIN smc_school as s ON s.school_id = c.school_id and s.company_id=cs.company_id
                                                                WHERE {$datawhere} GROUP BY c.school_id,cs.course_id) as q");
                }
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $dataList;
            return $data;
        }
    }

    function teacherClass($request)
    {
        $datawhere = " 1 ";
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
        } else {
            $request['start_time'] = date('Y-m-01');
        }
        $datawhere .= " and C.class_enddate >= '{$request['start_time']}' ";
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
        } else {
            $request['end_time'] = date('Y-m-d');
        }
        $datawhere .= " and C.class_stdate <= '{$request['end_time']}' ";

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (B.staffer_branch like '%{$request['keyword']}%' or B.staffer_cnname like '%{$request['keyword']}%' or B.staffer_enname like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND E.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and E.school_id = '{$request['school_id']}'";
        } else {
            $datawhere .= " and E.school_istest <> '1' AND E.school_isclose<> '1' ";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and E.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND E.school_istest <> '1' AND E.school_isclose<> '1' ";
        }

        $file_name_add = '';
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and D.coursetype_id = '{$request['coursetype_id']}'";
            $coursetypeOne = $this->DataControl->getFieldOne('smc_code_coursetype', "concat(coursetype_cnname,'(',coursetype_branch,')') as coursetype", " coursetype_id= '{$request['coursetype_id']}'");
            $file_name_add .= $coursetypeOne['coursetype'] . '_';
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and D.coursecat_id = '{$request['coursecat_id']}'";
            $coursecatOne = $this->DataControl->getFieldOne('smc_code_coursecat', "concat(coursecat_cnname,'(',coursecat_branch,')') as coursecat", " coursecat_id= '{$request['coursecat_id']}'");
            $file_name_add .= $coursecatOne['coursecat'] . '_';
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $to = "'A07','B03','B04','B06','E01','B07'";
        $leave = "'B01','C02'";

        $sql = "
            select TA.school_id,TA.school_branch,TA.school_cnname,TA.school_shortname,
            TA.staffer_id,TA.staffer_branch,TA.staffer_cnname,TA.staffer_enname,
            (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = TA.school_province ) as province_name,
            (SELECT d.district_cnname FROM gmc_company_district as d WHERE d.district_id = TA.district_id) AS district_cnname,
            count(TA.class_id) AS teaching_class_num,
            SUM(student_all_num) AS student_all_num,
            SUM(student_all_arrive_num) AS student_all_arrive_num,
            SUM(student_is_checking_num) AS student_is_checking_num,
            SUM(to_class_num) AS to_class_num,
            SUM(out_class_num) AS out_class_num,
            SUM(up_class_num) AS up_class_num,
            SUM(leave_class_num) AS leave_class_num,
            SUM(student_insert_num) AS student_insert_num
            from(
            SELECT E.school_id,E.school_branch,E.school_cnname,E.school_shortname,E.school_province,E.district_id,
            B.staffer_id,B.staffer_branch,B.staffer_cnname,B.staffer_enname,
            C.class_id,
            (select count(x.student_id) from smc_student_study x where x.class_id=A.class_id and x.study_endday >= '{$request['start_time']}' and x.study_beginday <= '{$request['end_time']}' ) as student_all_num,
            (select count(z.hourstudy_id) from smc_class_hour x 
                left join smc_student_study y on x.class_id=y.class_id and y.study_endday>=x.hour_day and y.study_beginday<=x.hour_day 
                left join smc_student_hourstudy z on z.student_id=y.student_id and z.hour_id=x.hour_id
                where x.class_id=C.class_id and x.hour_day >= '{$request['start_time']}'  and x.hour_day <= '{$request['end_time']}' and x.hour_ischecking>-1
            ) as student_all_arrive_num,
            (select count(z.hourstudy_id) from smc_class_hour x 
                left join smc_student_study y on x.class_id=y.class_id and y.study_endday>=x.hour_day and y.study_beginday<=x.hour_day 
                left join smc_student_hourstudy z on z.student_id=y.student_id and z.hour_id=x.hour_id
                where x.class_id=C.class_id and x.hour_day >= '{$request['start_time']}' and x.hour_day <= '{$request['end_time']}' and x.hour_ischecking>-1 and z.hourstudy_checkin =1
            ) as student_is_checking_num,
            (select count(DISTINCT x.student_id) from smc_student_changelog x where x.class_id=C.class_id and changelog_type =0 and x.stuchange_code = 'A05' 
            and x.changelog_day >= '{$request['start_time']}' and x.changelog_day <= '{$request['end_time']}') as to_class_num,
            (select count(DISTINCT x.student_id) from smc_student_changelog x where x.class_id=c.class_id and changelog_type =1 and x.stuchange_code in ('A07','B03','B04','B06','E01','B07') 
            and 1 and x.changelog_day >= '{$request['start_time']}' and x.changelog_day <= '{$request['end_time']}') as out_class_num,
            (select count(DISTINCT x.student_id) from smc_student_changelog x where x.class_id=C.class_id and changelog_type =0 and x.stuchange_code = 'B02' 
            and x.changelog_day >= '{$request['start_time']}' and x.changelog_day <= '{$request['end_time']}') as up_class_num,
            (select count(DISTINCT x.student_id) from smc_student_changelog x where x.class_id=c.class_id and changelog_type =1 and x.stuchange_code in ('B01','C02') 
            and 1 and x.changelog_day >= '{$request['start_time']}' and x.changelog_day <= '{$request['end_time']}') as leave_class_num,
            (select count(x.student_id) from smc_student_study x where x.class_id=C.class_id and x.study_beginday > (select sch.hour_day from smc_class_hour as sch where sch.class_id=x.class_id  and sch.hour_isfree = 0 and sch.hour_iswarming =0 order by sch.hour_day ASC limit 0,1)  
            and x.study_endday >= '{$request['start_time']}' and x.study_beginday <= '{$request['end_time']}') as  student_insert_num
            FROM smc_class_teach A 
            LEFT JOIN smc_staffer B ON A.staffer_id=B.staffer_id
            LEFT JOIN smc_class C ON A.class_id=C.class_id
            LEFT JOIN smc_course D ON C.course_id=D.course_id
            LEFT JOIN smc_school E ON C.school_id=E.school_id
            WHERE A.teach_type = 0
            AND A.teach_status=0
            AND E.company_id='{$request['company_id']}'
            AND {$datawhere}
            )TA
            group by TA.school_id,TA.staffer_id 
            order by TA.school_id,TA.staffer_id";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无带课数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['teaching_class_num'] = $dateexcelvar['teaching_class_num'];
                    $datearray['student_all_num'] = $dateexcelvar['student_all_num'];
                    $datearray['student_all_arrive_num'] = $dateexcelvar['student_all_arrive_num'];
                    $datearray['student_no_checking_num'] = $dateexcelvar['student_all_arrive_num'] - $dateexcelvar['student_is_checking_num'];
                    $datearray['student_is_checking_num'] = $dateexcelvar['student_is_checking_num'];
                    $datearray['student_all_arrive_rate'] = $dateexcelvar['student_all_arrive_num'] > 0 ? round($dateexcelvar['student_is_checking_num'] / $dateexcelvar['student_all_arrive_num'], 4) * 100 . '%' : '0%';
                    $datearray['to_class_num'] = $dateexcelvar['to_class_num'];
                    $datearray['out_class_num'] = $dateexcelvar['out_class_num'];
                    $datearray['up_class_num'] = $dateexcelvar['up_class_num'];
                    $datearray['leave_class_num'] = $dateexcelvar['leave_class_num'];
                    $datearray['student_insert_num'] = $dateexcelvar['student_insert_num'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "教师中文名", "教师英文名", "教师编号", "带班数", "带班总人数", "应到人次", "未考勤人次", "实到人次", "出勤率", "转入人数", "转出人数", "升班人数", "班内流失人数", "插班人数"));
            $excelfileds = array("province_name", "school_branch", "school_cnname", 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'teaching_class_num', 'student_all_num', 'student_all_arrive_num', 'student_no_checking_num', 'student_is_checking_num', 'student_all_arrive_rate', 'to_class_num', 'out_class_num', 'up_class_num', 'leave_class_num', 'student_insert_num');
            $tem_name = $this->LgStringSwitch($file_name_add . "教师带课明细表" . ".xlsx");
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if ($list) {
                foreach ($list as &$one) {
                    $one['student_no_checking_num'] = $one['student_all_arrive_num'] - $one['student_is_checking_num'];
                    $one['student_all_arrive_rate'] = $one['student_all_arrive_num'] > 0 ? round($one['student_is_checking_num'] / $one['student_all_arrive_num'], 4) * 100 . '%' : '0%';
                }
            }
            $data = array();
            $count_sql = "select TA.school_id,TA.school_branch,TA.school_cnname,TA.school_shortname,
                TA.staffer_id,TA.staffer_branch,TA.staffer_cnname,TA.staffer_enname,
                count(TA.class_id) AS teaching_class_num
                from(
                SELECT E.school_id,E.school_branch,E.school_cnname,E.school_shortname,
                B.staffer_id,B.staffer_branch,B.staffer_cnname,B.staffer_enname,
                C.class_id
                FROM smc_class_teach A 
                LEFT JOIN smc_staffer B ON A.staffer_id=B.staffer_id
                LEFT JOIN smc_class C ON A.class_id=C.class_id
                LEFT JOIN smc_course D ON C.course_id=D.course_id
                LEFT JOIN smc_school E ON C.school_id=E.school_id
                WHERE A.teach_type = 0
                AND A.teach_status=0
                AND E.company_id='{$request['company_id']}'
                AND {$datawhere}
                )TA
            group by TA.school_id,TA.staffer_id ";
            $dbNums = $this->DataControl->selectClear($count_sql);
            if ($dbNums) {
                $allnum = count($dbNums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;
            return $data;
        }
    }

    //主教老师留班统计表
    function bishopClassrenew($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND b.school_istest <> '1' AND b.school_isclose <> '1'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (g.staffer_branch like '%{$request['keyword']}%' 
            or g.staffer_cnname like '%{$request['keyword']}%' 
            or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND b.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and c.class_enddate<= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and c.class_enddate>= '{$request['start_time']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and d.course_id= '{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and e.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and f.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['course_isrenew']) && $request['course_isrenew'] !== '') {
            $datawhere .= " and d.course_isrenew= '{$request['course_isrenew']}'";
            $datawhere .= " and c.class_isnotrenew <>'{$request['course_isrenew']}'";
        }

        if (isset($request['is_containsbreakoff']) && $request['is_containsbreakoff'] == '0') {
            $datawhere .= " and not exists(select 1 from smc_class_breakoff where class_id=a.class_id and breakoff_status>=2 and breakoff_type=0) ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select b.school_tagbak
                ,b.school_branch
                ,b.school_shortname as school_name
                ,(SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = b.school_province ) as province_name
                ,(SELECT y.post_name FROM gmc_staffer_postbe x,gmc_company_post y where x.post_id = y.post_id and x.staffer_id = g.staffer_id 
                    and y.post_type = 1 and x.school_id =a.school_id ORDER BY x.postbe_createtime DESC LIMIT 1 ) AS job_name
                ,g.staffer_id
                ,g.staffer_branch
                ,g.staffer_cnname
                ,g.staffer_enname
                ,f.coursetype_id
                ,f.coursetype_branch
                ,f.coursetype_cnname
                ,e.coursecat_id
                ,e.coursecat_branch
                ,e.coursecat_cnname
                ,d.course_id
                ,d.course_branch
                ,d.course_cnname
                ,c.class_id
                ,c.class_branch
                ,c.class_cnname
                ,c.class_enname
                ,DATE_FORMAT(c.class_enddate,'%Y-%m') as class_endmonth
                ,DATE_FORMAT( c.class_enddate, '%Y' ) AS class_endyear
                ,(select count(1) from smc_class_endcalc_study w where study_iscalculate>=0 and  study_outtype in('0','1','3','4') and endcalc_id=a.endcalc_id
                and not exists(select 1 from smc_student_guildpolicy where student_id=w.student_id and guildpolicy_enddate>=w.study_outdate)) as all_num
                ,(select count(1) from smc_class_endcalc_study w where study_iscalculate>=0 and  study_outtype in('0','1','3','4') and endcalc_id=a.endcalc_id and study_nexttimes>0
                and not exists(select 1 from smc_student_guildpolicy where student_id=w.student_id and guildpolicy_enddate>=w.study_outdate)) as next_num
                ,(select count(1) from smc_class_endcalc_study w where study_iscalculate>=0 and  study_outtype in('0','1','3','4') and endcalc_id=a.endcalc_id 
                and study_upgraderate>=100
                and not exists(select 1 from smc_student_guildpolicy where student_id=w.student_id and guildpolicy_enddate>=w.study_outdate)) as renew_num
                ,a.endcalc_chn_times as class_times
                ,a.endcalc_staffer_times
                from smc_class_endcalc a
                left join smc_school b on a.school_id=b.school_id
                left join smc_class c on a.class_id=c.class_id
                left join smc_course d on c.course_id=d.course_id
                left join smc_code_coursecat e on d.coursecat_id=e.coursecat_id
                left join smc_code_coursetype f on e.coursetype_id=f.coursetype_id
                left join smc_staffer g on a.endcalc_staffer_id=g.staffer_id
                where c.class_enddate<=curdate()
                and {$datawhere}
                order by b.school_tagbak,b.school_branch,class_endmonth
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无教师留班信息";
                return false;
            }

            $sql = "SELECT
                        ta.school_tagbak,
                        ta.school_branch,
                        ta.school_name,
                        ta.province_name,
                        ta.staffer_id,
                        ta.staffer_branch,
                        ta.staffer_cnname,
                        ta.staffer_enname,
                        ta.job_name,
                        ta.coursecat_id,
                        ta.coursecat_branch,
                        ta.class_endmonth,
                        ta.class_endyear,
                        sum( ta.all_num ) AS all_num_calc,
                        sum( ta.renew_num ) AS renew_num_calc 
                    FROM
                        (
                    SELECT
                        b.school_tagbak,
                        b.school_branch,
                        b.school_shortname AS school_name,
                        (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = b.school_province ) as province_name,
                        (
                    SELECT
                        y.post_name 
                    FROM
                        gmc_staffer_postbe x,
                        gmc_company_post y 
                    WHERE
                        x.post_id = y.post_id 
                        AND x.staffer_id = g.staffer_id 
                        AND y.post_type = 1 
                        AND x.school_id = a.school_id 
                    ORDER BY
                        x.postbe_createtime DESC 
                        LIMIT 1 
                        ) AS job_name,
                        g.staffer_id,
                        g.staffer_branch,
                        g.staffer_cnname,
                        g.staffer_enname,
                        e.coursecat_id,
                        e.coursecat_branch,
                        c.class_id,
                        DATE_FORMAT( c.class_enddate, '%Y-%m' ) AS class_endmonth,
                        DATE_FORMAT( c.class_enddate, '%Y' ) AS class_endyear,
                        (SELECT count(1) FROM smc_class_endcalc_study w WHERE study_iscalculate>=0 and  study_outtype IN ( '0', '1', '3', '4' ) AND endcalc_id = a.endcalc_id 
                        and not exists(select 1 from smc_student_guildpolicy where student_id=w.student_id and guildpolicy_enddate>=w.study_outdate)) AS all_num,
                        (SELECT count(1) FROM smc_class_endcalc_study w WHERE study_iscalculate>=0 and  study_outtype IN ( '0', '1', '3', '4' ) AND endcalc_id = a.endcalc_id 
                        and study_upgraderate>=100
                        and not exists(select 1 from smc_student_guildpolicy where student_id=w.student_id and guildpolicy_enddate>=w.study_outdate)) AS renew_num 
                    FROM 
                        smc_class_endcalc a
                        LEFT JOIN smc_school b ON a.school_id = b.school_id
                        LEFT JOIN smc_class c ON a.class_id = c.class_id
                        LEFT JOIN smc_course d ON c.course_id = d.course_id
                        LEFT JOIN smc_code_coursecat e ON d.coursecat_id = e.coursecat_id
                        LEFT JOIN smc_code_coursetype f ON e.coursetype_id = f.coursetype_id
                        LEFT JOIN smc_staffer g ON a.endcalc_staffer_id = g.staffer_id 
                    WHERE
                        c.class_enddate <= curdate( ) 
                        and {$datawhere} 
                        ) ta 
                    GROUP BY
                        ta.school_branch,
                        ta.staffer_id,
                        ta.coursecat_id,
                        ta.class_endmonth 
                    ORDER BY
                        ta.school_tagbak,
                        ta.school_branch,
                        ta.staffer_id,
                        ta.coursecat_id,
                        ta.class_endmonth";

            $calcdataarray = $this->DataControl->selectClear($sql);

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $dateexcelvar['renew_rate'] = $dateexcelvar['all_num'] > 0 ? round($dateexcelvar['renew_num'] / $dateexcelvar['all_num'], 4) * 100 . '%' : '--';
                    $dateexcelvar['teach_rate'] = $dateexcelvar['class_times'] > 0 ? round($dateexcelvar['endcalc_staffer_times'] / $dateexcelvar['class_times'], 4) * 100 . '%' : '--';
                }
            }
            $fileType = ".xlsx";
            $excelheader = $this->LgArraySwitch(array("督导", "省份", "校区编号", "校区名称", "教师中文名", "教师英文名", "教师编号", "职称", "班种代码", "班种名称", "课程别代码", "课程别名称", "班级编号", "班级别名", "结班月份", "总人数", "续费人数", "留班人数", "留班率", "带课比例"));
            $excelfileds = array("school_tagbak", "province_name", "school_branch", "school_name", 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'job_name', 'coursecat_branch', 'coursecat_cnname', 'course_branch', 'course_cnname', 'class_branch', 'class_enname', 'class_endmonth', 'all_num', 'next_num', 'renew_num', 'renew_rate', 'teach_rate');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_shortname'] . "主教中师留班统计表");
            excelcustom_bishopClassrenew($excelheader, $dateexcelarray, $calcdataarray, $tem_name, $fileType);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['renew_rate'] = $var['all_num'] > 0 ? round($var['renew_num'] / $var['all_num'], 4) * 100 . '%' : '--';
                $var['teach_rate'] = $var['class_times'] > 0 ? round($var['endcalc_staffer_times'] / $var['class_times'], 4) * 100 . '%' : '--';
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.endcalc_id
                from smc_class_endcalc a
                left join smc_school b on a.school_id=b.school_id
                left join smc_class c on a.class_id=c.class_id
                left join smc_course d on c.course_id=d.course_id
                left join smc_code_coursecat e on d.coursecat_id=e.coursecat_id
                left join smc_code_coursetype f on e.coursetype_id=f.coursetype_id
                left join smc_staffer g on a.endcalc_staffer_id=g.staffer_id
                where c.class_enddate<=curdate()
                and {$datawhere}
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $ClassCalc;

            return $data;
        }
    }

    function coursepackRenewDetl($request)
    {
        $datawhere = " and a.company_id='{$request['company_id']}'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND f.school_istest <> '1' AND f.school_isclose <> '1'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.student_branch like '%{$request['keyword']}%' 
            or b.student_cnname like '%{$request['keyword']}%' 
            or c.class_cnname like '%{$request['keyword']}%' 
            or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " AND a.hour_day>= '{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " AND a.hour_day<= '{$request['end_time']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and d.course_id= '{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and d.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and d.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT f.school_id,f.school_tagbak,
        f.school_branch,
        f.school_shortname as school_name,
        (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = f.school_province ) as province_name,
        b.student_branch,
        b.student_cnname,
        b.student_enname,
        c.class_branch,
        c.class_enname,
        (select concat(coursetype_branch,'-',coursetype_cnname) from smc_code_coursetype where coursetype_id=d.coursetype_id) as coursetype,
        (select concat(coursecat_branch,'-',coursecat_cnname) from smc_code_coursecat where coursecat_id=d.coursecat_id) as coursecat,
        d.course_branch,
        d.course_cnname,
        a.hour_day,
        (select sum(coursebalance_figure) from smc_student_coursebalance x,smc_course y where x.course_id=y.course_id and y.course_sellclass = 1
        and x.school_id=a.school_id and x.student_id=a.student_id and y.coursetype_id=d.coursetype_id) as coursebalance_figure,
        (select sum(coursebalance_time) from smc_student_coursebalance x,smc_course y where x.course_id=y.course_id and y.course_sellclass = 1
        and x.school_id=a.school_id and x.student_id=a.student_id and y.coursetype_id=d.coursetype_id) as coursebalance_time,
        (SELECT p.hourplan_id
        FROM temp_smc_hourplan AS p
        WHERE p.student_id = a.student_id
        AND p.school_id = a.school_id
        AND p.coursetype_id = a.coursetype_id
        AND p.hour_isbuy = '1'
        AND p.hourplan_rank > a.hourplan_rank
        LIMIT 0,1) AS next_id
        FROM
        temp_smc_hourplan AS a,
        smc_student AS b,
        smc_class AS c,
        smc_course AS d,
        smc_school as f
        WHERE a.school_id = f.school_id
        AND a.student_id = b.student_id
        AND a.class_id = c.class_id
        AND a.course_id = d.course_id
        AND a.hour_isnode = '1'
        {$datawhere}
        order BY f.school_branch,a.hour_day
              ";//(select count(1) from smc_class_hour where hour_ischecking>=0 and class_id=a.class_id)

        $status = $this->LgArraySwitch(array("0" => "未续包", "1" => "已续包"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无续费数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];

                    $datearray['coursetype'] = $dateexcelvar['coursetype'];
                    $datearray['coursecat'] = $dateexcelvar['coursecat'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];

                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['next_id'] = $dateexcelvar['next_id'] > 0 ? $status[1] : $status[0];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "学员中文名", "学员英文名", "学员编号", "班组", "班种", "课程别编号", "课程别名称", "应续包日期", "班组剩余金额", "班组剩余次数", "是否续包"));
            $excelfileds = array('province_name', 'school_branch', 'school_name', 'student_cnname', 'student_enname', 'student_branch', "coursetype", "coursecat", 'course_branch', 'course_cnname', 'hour_day', 'coursebalance_figure', 'coursebalance_time', 'next_id');

            $tem_name = '学员续课包明细表' . $request['start_time'] . '~' . $request['end_time'] . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无续费信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['next_id'] = $var['next_id'] > 0 ? $status[1] : $status[0];
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT a.hourplan_id
                FROM
                temp_smc_hourplan AS a,
                smc_student AS b,
                smc_class AS c,
                smc_course AS d,
                smc_school as f
                WHERE a.school_id = f.school_id
                AND a.student_id = b.student_id
                AND a.class_id = c.class_id
                AND a.course_id = d.course_id
                AND a.hour_isnode = '1'
                {$datawhere}
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $ClassCalc;

            return $data;
        }
    }

    function coursepackRenewTeach($request)
    {
        $datawherein = " and a.company_id='{$request['company_id']}'";
        $datawhereout = " ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhereout .= " and (te.staffer_branch like '%{$request['keyword']}%'
            or te.staffer_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawherein .= " and a.school_id='{$request['school_id']}' ";
        } else {
            $datawhereout .= " AND tb.school_istest <> '1' AND tb.school_isclose <> '1'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawherein .= " AND a.hour_day>= '{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawherein .= " AND a.hour_day<= '{$request['end_time']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawherein .= " and c.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tb.school_id
        ,tb.school_branch
        ,tb.school_cnname as school_name
        ,(SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = tb.school_province ) as province_name
        ,te.staffer_id
        ,te.staffer_branch
        ,te.staffer_cnname
        ,tc.coursetype_id
        ,tc.coursetype_branch
        ,tc.coursetype_cnname
        ,count(distinct ta.class_id) as class_num
        ,count(ta.student_id) as student_num
        ,count(if(buy_num>0,true,null)) as buying_num
        from (
        select a.school_id
        ,c.coursetype_id
        ,a.class_id
        ,a.student_id
        ,(select count(1) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id 
            AND coursetype_id = a.coursetype_id and hourplan_rank>a.hourplan_rank and hour_isbuy=1) as buy_num
        from temp_smc_hourplan a
        left join smc_class b on a.class_id=b.class_id
        left join smc_course c on b.course_id=c.course_id
        where 1 {$datawherein}
        and a.hour_isnode = '1'
        and b.class_status>-2
        and c.course_sellclass=1
        )ta
        left join smc_school tb on ta.school_id=tb.school_id
        left join smc_code_coursetype tc on ta.coursetype_id=tc.coursetype_id
        left join smc_class_teach td on ta.class_id=td.class_id and td.teach_status=0 and td.teach_type=0
        left join smc_staffer te on te.staffer_id=td.staffer_id
        where 1 {$datawhereout}
        group by tb.school_id,te.staffer_id,tc.coursetype_id
        order by tb.school_id,te.staffer_id,tc.coursetype_id";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无续费数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['student_num'] = $dateexcelvar['student_num'];
                    $datearray['buying_num'] = $dateexcelvar['buying_num'];
                    $datearray['buying_rate'] = $dateexcelvar['student_num'] > 0 ? round($dateexcelvar['buying_num'] / $dateexcelvar['student_num'], 4) * 100 . '%' : '--';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "教师编号", "教师中文名", "班组编号", "班组名称", "带班数", "应付费人数", "已付费人数", "续包率"));
            $excelfileds = array('province_name', 'school_branch', 'school_name', 'staffer_branch', 'staffer_cnname', 'coursetype_branch', "coursetype_cnname", "class_num", 'student_num', 'buying_num', 'buying_rate');

            $tem_name = '教师带班续课包统计表' . $request['start_time'] . '~' . $request['end_time'] . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无续费信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['buying_rate'] = $var['student_num'] > 0 ? round($var['buying_num'] / $var['student_num'], 4) * 100 . '%' : '--';
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select tb.school_id
                ,te.staffer_id
                ,tc.coursetype_id
                from (
                select a.school_id
                ,c.coursetype_id
                ,a.class_id
                ,a.student_id
                ,(select count(1) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id 
                    AND coursetype_id = a.coursetype_id and hourplan_rank>a.hourplan_rank and hour_isbuy=1) as buy_num
                from temp_smc_hourplan a
                left join smc_class b on a.class_id=b.class_id
                left join smc_course c on b.course_id=c.course_id
                where 1 {$datawherein}
                and a.hour_isnode = '1'
                and b.class_status>-2
                and c.course_sellclass=1
                )ta
                left join smc_school tb on ta.school_id=tb.school_id
                left join smc_code_coursetype tc on ta.coursetype_id=tc.coursetype_id
                left join smc_class_teach td on ta.class_id=td.class_id and td.teach_status=0 and td.teach_type=0
                left join smc_staffer te on te.staffer_id=td.staffer_id
                where 1 {$datawhereout}
                group by tb.school_id,te.staffer_id,tc.coursetype_id
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $ClassCalc;

            return $data;
        }
    }

}