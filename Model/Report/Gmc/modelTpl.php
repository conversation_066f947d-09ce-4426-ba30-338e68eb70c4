<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Report\Gmc;


class modelTpl extends \Model\modelTpl{
//    public $DataControl;
//    public $AnalyzeControl;

    public function __construct(){
        parent::__construct();
        //数据库操作
//        $this->AnalyzeControl = new \Analyzesql();
    }

    function ChangeTime($time){
        $time = time() - $time;
        if(is_numeric($time)){
            $value = array(
                "years" => 0, "days" => 0, "hours" => 0,
                "minutes" => 0, "seconds" => 0,
            );
            if($time >= 31556926){
                $value["years"] = floor($time/31556926);
                $time = ($time%31556926);
                $t = $value["years"].'年前';
            }
            elseif(31556926 >$time && $time >= 86400){
                $value["days"] = floor($time/86400);
                $time = ($time%86400);
                $t = $value["days"].'天前';
            }
            elseif(86400 > $time && $time >= 3600){
                $value["hours"] = floor($time/3600);
                $time = ($time%3600);
                $t = $value["hours"].'小时前';
            }
            elseif(3600 > $time && $time >= 60){
                $value["minutes"] = floor($time/60);
                $time = ($time%60);
                $t = $value["minutes"].'分钟前';
            }else{
                $t = $time.'秒前';
            }
            return $t;
        }else{
            return date('Y-m-d H:i:s',time());
        }
    }

    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    public function addGmcWorkLog($company_id,$staffer_id,$module,$type,$content)
    {
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('gmc_staffer_worklog', $logData);
    }

    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
