<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Report\Smc;


class modelTpl extends \Model\modelTpl{
//    public $AnalyzeControl;
//    public $DataControl;

    public function __construct(){
        parent::__construct();
        //数据库操作
//        $this->AnalyzeControl = new \Analyzesql();
        $this->DataControl = new \Dbmysql();
    }
    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }

    function get_arr($arr)
    {
        foreach ($arr as $k => $v) {
            if (is_array($arr[$k])) {
                $arr[$k] = $this->get_arr($arr[$k]);
            } else {
                if ($k==-1) {
                    unset($arr[$k]);
                }
            }
        }
        return $arr;
    }

    public static $WORK_DAY = [
        1 => ['en' => 'Monday','cn'=>'周一'],
        2 => ['en' => 'Tuesday','cn'=>'周二'],
        3 => ['en' => 'Wednesday','cn'=>'周三'],
        4 => ['en' => 'Thursday','cn'=>'周四'],
        5 => ['en' => 'Friday','cn'=>'周五'],
        6 => ['en' => 'Saturday','cn'=>'周六'],
        7 => ['en' => 'Sunday','cn'=>'周日']
    ];

    function ChangeTime($time){
        $time = time() - $time;
        if(is_numeric($time)){
            $value = array(
                "years" => 0, "days" => 0, "hours" => 0,
                "minutes" => 0, "seconds" => 0,
            );
            if($time >= 31556926){
                $value["years"] = floor($time/31556926);
                $time = ($time%31556926);
                $t = $value["years"].'年前';
            }
            elseif(31556926 >$time && $time >= 86400){
                $value["days"] = floor($time/86400);
                $time = ($time%86400);
                $t = $value["days"].'天前';
            }
            elseif(86400 > $time && $time >= 3600){
                $value["hours"] = floor($time/3600);
                $time = ($time%3600);
                $t = $value["hours"].'小时前';
            }
            elseif(3600 > $time && $time >= 60){
                $value["minutes"] = floor($time/60);
                $time = ($time%60);
                $t = $value["minutes"].'分钟前';
            }else{
                $t = $time.'秒前';
            }
            return $t;
        }else{
            return date('Y-m-d H:i:s',time());
        }
    }

}
