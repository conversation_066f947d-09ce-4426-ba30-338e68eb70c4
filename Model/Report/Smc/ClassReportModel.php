<?php


namespace Model\Report\Smc;

class ClassReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->staffer_id = $publicarray['staffer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //班级信息报表
    function classInfo($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or class_branch like '%{$request['keyword']}%') ";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and c.course_id='{$request['course_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id='{$request['school_id']} ' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and D.coursetype_id='{$request['coursetype_id']}' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $today = $request['end_time'];
            $datawhere .= " and c.class_stdate <= '{$request['end_time']}'  and c.class_enddate >= '{$request['end_time']}' ";
        } else {
            $today = date("Y-m-d");
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $in = "'A02','A03','A04','A05'";
        $to = "'A07','B01','B02','B03','B04','B06','E01','B07'";
        $sql = "
            select ss.school_cnname,ss.school_branch,c.class_cnname,c.class_enname,c.class_branch,c.class_fullnums,
            (select count(DISTINCT staffer_id) from  smc_class_hour_teaching as t,smc_class_hour as ch  where t.hour_id=ch.hour_id and t.class_id = c.class_id and ch.hour_day <='{$today}') as all_teacher_num,
            (select count(DISTINCT staffer_id) from  smc_class_teach as te where te.class_id = c.class_id  ) as teaching_num,
            (select count(student_id) from smc_student_study as s where s.class_id=c.class_id and s.study_beginday<='{$today}' and s.study_endday >='{$today}') as study_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =1 and stuchange_code in ({$in}) and changelog_day<='{$today}' ) as to_class_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =0 and stuchange_code in ({$to}) and changelog_day<='{$today}' ) as out_class_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =0 and stuchange_code in ({$to}) and changelog_day<='{$today}' ) as leave_class_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =0 and stuchange_code ='A07'  and changelog_day<='{$today}' ) as wait_class_num,
            (select count(h.hourstudy_id) from smc_student_hourstudy as h,smc_class_hour as ch  where h.hour_id=ch.hour_id and  h.class_id =c.class_id and ch.hour_day <='{$today}' ) as stu_all_checking_num,
            (select count(h.hourstudy_id) from smc_student_hourstudy as h,smc_class_hour as ch  where h.hour_id=ch.hour_id and h.class_id =c.class_id and h.hourstudy_checkin =1 and ch.hour_day <='{$today}') as stu_right_checking_num
            from smc_class as c 
            left join smc_school as ss on c.school_id = ss.school_id
            left join smc_course AS D on c.course_id=d.course_id 
            where   c.company_id='{$this->company_id}' and c.class_status <> -2  and  {$datawhere} order by class_createtime DESC ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无班级信息";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['all_teacher_num'] = $dateexcelvar['all_teacher_num'];
                    $datearray['teaching_num'] = $dateexcelvar['teaching_num'];
                    $datearray['class_fullnums'] = $dateexcelvar['class_fullnums'];
                    $datearray['study_num'] = $dateexcelvar['study_num'];
                    $datearray['fill_class_rate'] = $dateexcelvar['class_fullnums'] ? round($dateexcelvar['study_num'] / $dateexcelvar['class_fullnums'], 4) * 100 . '%' : '0%';
                    $datearray['to_class_num'] = $dateexcelvar['to_class_num'];
                    $datearray['out_class_num'] = $dateexcelvar['out_class_num'];
                    $datearray['leave_class_num'] = $dateexcelvar['leave_class_num'];
                    $datearray['wait_class_num'] = $dateexcelvar['wait_class_num'];
                    $datearray['stu_all_checking_num'] = $dateexcelvar['stu_all_checking_num'];
                    $datearray['stu_right_checking_num'] = $dateexcelvar['stu_right_checking_num'];
                    $datearray['stu_checking_rate'] = $dateexcelvar['stu_all_checking_num'] ? round($dateexcelvar['stu_right_checking_num'] / $dateexcelvar['stu_all_checking_num'], 4) * 100 . '%' : '0%';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "班级别名", "班级编号", "班级教师累计数", "教师人数", "满班人数", "在读人数", "满班率", "转入人数", "转出人数", "班级流失人数", "延班人数", "应到人次", "实到人次", "出勤率"));
            $excelfileds = array('school_cnname', 'school_branch', 'class_cnname', 'class_enname', 'class_branch', 'all_teacher_num', 'teaching_num', 'class_fullnums', "study_num", 'fill_class_rate', 'to_class_num', "out_class_num", "leave_class_num", "wait_class_num", "stu_all_checking_num", "stu_right_checking_num", "stu_checking_rate");
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '班级信息统计表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "班级信息信息";
                return false;
            }
            foreach ($list as &$one) {
                $one['fill_class_rate'] = $one['class_fullnums'] ? round($one['study_num'] / $one['class_fullnums'], 4) * 100 . '%' : '0%';
                $one['stu_checking_rate'] = $one['stu_all_checking_num'] ? round($one['stu_right_checking_num'] / $one['stu_all_checking_num'], 4) * 100 . '%' : '0%';
            }
            $data = array();
            $count_sql = "select c.class_id
            from smc_class as c
            left join smc_course AS D on c.course_id=d.course_id 
            where c.company_id='{$this->company_id}' 
            and c.class_status <> -2  
            and  {$datawhere}
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;
            return $data;
        }
    }

    function classInfoList($request)
    {
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $week_start_day = date("Y-m-d", strtotime($request['start_time']));
        } else {
            $week_start_day = date("Y-m-01");
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $week_end_day = date("Y-m-d", strtotime($request['end_time']));
        } else {
            $week_end_day = date();
        }
        $datawhere = "1";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or s.student_enname like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%'
            or c.class_cnname like '%{$request['keyword']}%' 
            or c.class_enname like '%{$request['keyword']}%' 
            or c.class_branch like '%{$request['keyword']}%'
             )";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id='{$request['class_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and sc.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and cc.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and ss.school_id = '{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select so.school_cnname,so.school_branch,s.student_cnname,s.student_enname,s.student_branch,sf.family_mobile,c.class_cnname,c.class_enname,c.class_branch,cc.coursecat_cnname
                ,cc.coursecat_branch,sc.course_cnname,sc.course_branch,ss.study_beginday,ss.study_endday,ss.study_isreading
                ,cb.coursebalance_figure
                ,cb.coursebalance_time
                ,(select count(income_id) from smc_school_income where income_type=0 and class_id=ss.class_id and student_id=ss.student_id) as spend_times
                ,(select sum(income_price) from smc_school_income where income_type=0 and class_id=ss.class_id and student_id=ss.student_id) as income_price
                from smc_student_study as ss
                left join smc_class as c on c.class_id=ss.class_id
                left join smc_student as s on s.student_id=ss.student_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                left join smc_school as so on so.school_id=ss.school_id
                left join smc_student_coursebalance as cb on c.course_id=cb.course_id and cb.student_id=ss.student_id and cb.school_id=ss.school_id
                where {$datawhere} and ss.company_id='{$request['company_id']}'
                and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' 
                order by ss.class_id desc
        ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                $isreading = $this->LgArraySwitch(array("0" => "不在读", "1" => "在读", "-1" => "已结束"));
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
//                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['family_mobile'] = $dateexcelvar['family_mobile'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    $datearray['study_endday'] = $dateexcelvar['study_endday'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['spend_times'] = $dateexcelvar['spend_times'];
                    $datearray['income_price'] = $dateexcelvar['income_price'];
                    $datearray['study_isreading_name'] = $isreading[$dateexcelvar['study_isreading']];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "联系电话", "班级名称", '班级别名', "班级编号", "班种名称", "班种编号", "课程别名称", "课程别编号", "入班日期", "出班日期", "剩余课次", "课程余额", "已耗课次", "课程收入", "在班状态"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', "family_mobile", 'class_cnname', 'class_enname', 'class_branch', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'study_beginday', 'study_endday', 'coursebalance_time', 'coursebalance_figure', 'spend_times', 'income_price', 'study_isreading_name');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}班级学生信息报表{$week_start_day}-{$week_end_day}.xlsx"));
            exit;


        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $isreading = $this->LgArraySwitch(array("0" => "不在读", "1" => "在读", "-1" => "已结束"));
            foreach ($classList as &$classOne) {
                $classOne['study_isreading_name'] = $isreading[$classOne['study_isreading']];
            }
            if (isset($request['is_count']) && $request['is_count'] == 1) {

                $count_sql = "select c.class_cnname,c.class_enname,c.class_branch,s.student_cnname
                              from smc_student_study as ss
                              left join smc_class as c on c.class_id=ss.class_id
                              left join smc_student as s on s.student_id=ss.student_id
                              left join smc_course as sc on sc.course_id=c.course_id
                              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
                              where {$datawhere} and ss.company_id='{$request['company_id']}'
                              and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;

            }
            $data['list'] = $classList;

            return $data;
        }

    }

    //班级升班报表
    function promotion($request)
    {
        $datawhere = "cu.company_id='{$request['company_id']}' and cu.upgradeorder_status='1'";
        $endqueryday = date("Y-m-d");
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $startqueryday = $request['start_time'];
            $startquerytime = strtotime($startqueryday);
            $datawhere .= " and cu.upgradeorder_createtime>='{$startquerytime}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
            $endquerytime = strtotime($endqueryday) + (3600 * 24);
            $datawhere .= " and cu.upgradeorder_createtime<='{$endquerytime}'";
        }

        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND l.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " AND (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            $datawhere .= " AND sc.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
            $datawhere .= " AND sc.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " AND cu.school_id='{$request['school_id']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and l.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND l.school_istest <> '1' AND l.school_isclose<> '1' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select l.school_id,l.school_branch,l.school_cnname,c.class_cnname as from_class_cnname,c.class_enname as from_class_enname,c.class_branch as from_class_branch,sc.course_cnname as from_course_cnname,sc.course_branch as from_course_branch,cu.upgrade_stayeffective,cu.upgrade_staynums,cu.upgrade_intonums,cu.upgrade_intostaynums
              ,(select r.region_name from smc_code_region as r where r.region_id = l.school_province ) as province_name
              ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ))) from smc_class_lessonplan as cl left join smc_staffer as s on s.staffer_id=cl.staffer_id where cl.class_id=cu.from_class_id and cl.staffer_id>0) as from_staffer_cnname
              ,FROM_UNIXTIME( cu.upgradeorder_createtime, '%Y-%m-%d %H:%i:%s' ) as upgradeorder_createtime
              ,cl.class_cnname as to_class_cnname,cl.class_enname as to_class_enname,cl.class_branch as to_class_branch,sce.course_cnname as to_course_cnname,sce.course_branch as to_course_branch
              ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ))) from smc_class_lessonplan as cl left join smc_staffer as s on s.staffer_id=cl.staffer_id where cl.class_id=cu.to_class_id and cl.staffer_id>0) as to_staffer_cnname
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=cu.from_class_id and ss.study_endday>='{$endqueryday}' ) as from_num
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=cu.to_class_id and ss.study_endday>='{$endqueryday}') as to_num
              from smc_class_upgradeorder as cu
              left join smc_class as c on c.class_id=cu.from_class_id
              left join smc_class as cl on cl.class_id=cu.to_class_id
              left join smc_course as sc on sc.course_id=cu.from_course_id
              left join smc_course as sce on sce.course_id=cu.to_course_id
              left join smc_school as l on cu.school_id=l.school_id 
              where {$datawhere}
              order by (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end),l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['from_class_cnname'] = $dateexcelvar['from_class_cnname'];
                    $datearray['from_class_enname'] = $dateexcelvar['from_class_enname'];
                    $datearray['from_class_branch'] = $dateexcelvar['from_class_branch'];
                    $datearray['from_course_cnname'] = $dateexcelvar['from_course_cnname'];
                    $datearray['from_course_branch'] = $dateexcelvar['from_course_branch'];
                    $datearray['from_staffer_cnname'] = $dateexcelvar['from_staffer_cnname'];
                    $datearray['from_num'] = $dateexcelvar['from_num'];
                    $datearray['upgradeorder_createtime'] = $dateexcelvar['upgradeorder_createtime'];
                    $datearray['to_class_cnname'] = $dateexcelvar['to_class_cnname'];
                    $datearray['to_class_enname'] = $dateexcelvar['to_class_enname'];
                    $datearray['to_class_branch'] = $dateexcelvar['to_class_branch'];
                    $datearray['to_course_cnname'] = $dateexcelvar['to_course_cnname'];
                    $datearray['to_course_branch'] = $dateexcelvar['to_course_branch'];
                    $datearray['to_staffer_cnname'] = $dateexcelvar['to_staffer_cnname'];
                    $datearray['to_num'] = $dateexcelvar['to_num'];
                    $datearray['promotion_rate'] = round($dateexcelvar['upgrade_staynums'] * 100 / $dateexcelvar['upgrade_stayeffective'], 2) . '%';

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("省份", "校区编号", "校区名称", "班级名称", "班级英文名", "班级编号", "课程别名称", "课程别编号", "班级教师", "班级人数", "升班时间", "升班班级名称", "升班班级英文名", "升班班级编号", "升班班级教师", "升班人数", "升班率");
            $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'from_class_cnname', 'from_class_enname', 'from_class_branch', 'from_course_cnname', 'from_course_branch', 'from_staffer_cnname', 'from_num', 'upgradeorder_createtime', 'to_class_cnname', 'to_class_enname', 'to_class_branch', 'to_staffer_cnname', 'to_num', 'promotion_rate');
            $tem_name = $this->schoolOne['school_cnname'] . '班级信息统计报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }

            foreach ($list as &$val) {
                if (!$val['class_fullnums']) {
                    $val['full_class_rate'] = '0%';
                } else {
                    $val['full_class_rate'] = round(($val['reading_num'] * 100) / $val['class_fullnums'], 2) . '%';
                }

                if (!$val['arrive_num']) {
                    $val['attendance_rate'] = '0%';
                } else {
                    $val['attendance_rate'] = round(($val['attendance_num'] * 100) / $val['arrive_num'], 2) . '%';
                }
                if ($val['upgrade_stayeffective'] == 0) {
                    $val['promotion_rate'] = "0.00%";
                } else {
                    $val['promotion_rate'] = round($val['upgrade_staynums'] * 100 / $val['upgrade_stayeffective'], 2) . '%';
                }

                $val['not_arrived_num'] = $val['arrive_num'] - $val['attendance_num'];
            }

            $data = array();
            $count_sql = "select c.class_cnname as from_class_cnname,c.class_enname as from_class_enname,c.class_branch as from_class_branch,sc.course_cnname as from_course_cnname,sc.course_branch as from_course_branch,cu.upgrade_stayeffective,cu.upgrade_staynums,cu.upgrade_intonums,cu.upgrade_intostaynums
              ,(select group_concat(s.staffer_cnname) from smc_class_lessonplan as cl left join smc_staffer as s on s.staffer_id=cl.staffer_id where cl.class_id=cu.from_class_id and cl.staffer_id>0) as from_staffer_cnname
              ,FROM_UNIXTIME( cu.upgradeorder_createtime, '%Y-%m-%d %H:%i:%s' ) as upgradeorder_createtime
              ,cl.class_cnname as to_class_cnname,cl.class_enname as to_class_enname,cl.class_branch as to_class_branch,sce.course_cnname as to_course_cnname,sce.course_branch as to_course_branch
              ,(select group_concat(s.staffer_cnname) from smc_class_lessonplan as cl left join smc_staffer as s on s.staffer_id=cl.staffer_id where cl.class_id=cu.to_class_id and cl.staffer_id>0) as to_staffer_cnname
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=cu.from_class_id and ss.study_endday>='{$endqueryday}' ) as from_num
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=cu.to_class_id and ss.study_endday>='{$endqueryday}') as to_num
              from smc_class_upgradeorder as cu
              left join smc_class as c on c.class_id=cu.from_class_id
              left join smc_class as cl on cl.class_id=cu.to_class_id
              left join smc_course as sc on sc.course_id=cu.from_course_id
              left join smc_course as sce on sce.course_id=cu.to_course_id
              left join smc_school as l on cu.school_id=l.school_id 
              where {$datawhere}";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //班级转班明细报表
    function transferClass($request)
    {
        $datawhere = " 1 ";
        $endquerytime = time();
        $endqueryday = date("Y-m-d");
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $startqueryday = $request['starttime'];
            $startquerytime = strtotime($startqueryday);
            $datawhere .= " and scg.change_createtime>='{$startqueryday}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
            $endquerytime = strtotime($endqueryday) + (3600 * 24);
            $datawhere .= " and scg.change_createtime<='{$endqueryday}'";
        }


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_cnname,s.student_enname,s.student_branch,scg.change_reason,scg.change_day,c.class_cnname as from_class_cnname,c.class_enname as from_class_enname,c.class_branch as from_class_branch,sc.class_cnname as to_class_cnname,sc.class_enname as to_class_enname,sc.class_branch as to_class_branch
,(select group_concat(s.staffer_cnname) from smc_class_lessonplan as cl left join smc_staffer as s on s.staffer_id=cl.staffer_id where cl.class_id=scg.from_class_id and cl.staffer_id>0) as from_staffer_cnname
,(select group_concat(s.staffer_cnname) from smc_class_lessonplan as cl left join smc_staffer as s on s.staffer_id=cl.staffer_id where cl.class_id=scg.to_class_id and cl.staffer_id>0) as to_staffer_cnname
              from smc_student_change as scg
              left join smc_student as s on s.student_id=scg.student_id
              left join smc_class as c on c.class_id=scg.from_class_id
              left join smc_class as sc on sc.class_id=scg.to_class_id
              where {$datawhere} and scg.company_id='{$this->company_id}' and scg.from_stuchange_code='B04' and scg.to_stuchange_code='A05'
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['from_class_cnname'] = $dateexcelvar['from_class_cnname'];
                    $datearray['from_class_enname'] = $dateexcelvar['from_class_enname'];
                    $datearray['from_class_branch'] = $dateexcelvar['from_class_branch'];
                    $datearray['from_staffer_cnname'] = $dateexcelvar['from_staffer_cnname'];
                    $datearray['to_class_cnname'] = $dateexcelvar['to_class_cnname'];
                    $datearray['to_class_enname'] = $dateexcelvar['to_class_enname'];
                    $datearray['to_class_branch'] = $dateexcelvar['to_class_branch'];
                    $datearray['to_staffer_cnname'] = $dateexcelvar['to_staffer_cnname'];
                    $datearray['change_day'] = $dateexcelvar['change_day'];
                    $datearray['change_reason'] = $dateexcelvar['change_reason'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "班级名称", "班级英文名", "班级编号", "班级教师", "转班班级名称", "转班班级英文名", "转班班级编号", "转班班级教师", "转班时间", "异动原因");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'from_class_cnname', 'from_class_enname', 'from_class_branch', 'from_staffer_cnname', 'to_class_cnname', 'to_class_enname', 'to_class_branch', 'to_staffer_cnname', 'change_day', 'change_reason');
            $tem_name = $this->schoolOne['school_cnname'] . '班级信息统计报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }


            $data = array();
            $count_sql = "select c.class_id
              from smc_student_change as scg
              where {$datawhere} and scg.company_id='{$this->company_id}' and scg.from_stuchange_code='B04' and scg.to_stuchange_code='A05'
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    function changeClassDetail($request)
    {
        $datawhere = "1";

        //关键词
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or t.student_enname like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or a.class_cnname like '%{$request['keyword']}%' or a.class_enname like '%{$request['keyword']}%' or a.class_branch like '%{$request['keyword']}%')";
        }
        //默认本月
        $time = GetTheMonth(date("Y-m-d"));
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and sc.change_day >= '{$request['start_time']}' ";
            $week_start_day = $request['start_time'];
        } else {
            $datawhere .= " and sc.change_day >= '{$time[0]}' ";
            $week_start_day = $time[0];
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and sc.change_day <= '{$request['end_time']}'";
            $week_end_day = $request['end_time'];
        } else {
            $datawhere .= " and sc.change_day <= '{$time[1]}' ";
            $week_end_day = $time[1];
        }

        //班组筛选
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and cc.coursetype_id='{$request['coursetype_id']}'";
        }
        //班种筛选
        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and cc.coursecat_id='{$request['coursecat_id']}'";
        }
        //班别筛选
        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and co.course_id='{$request['course_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.to_school_id = '{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_cnname,s.school_branch,t.student_id,t.student_cnname,t.student_enname,t.student_branch,sc.change_day,
                c.class_cnname as from_class_cnname,
                c.class_enname as from_class_enname,
                c.class_branch as from_class_branch,
                a.class_cnname as to_class_cnname,
                a.class_enname as to_class_enname,
                a.class_branch as to_class_branch,
                (SELECT group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', f.staffer_enname ) END ) ) ) FROM smc_class_teach as ct LEFT JOIN smc_staffer as f ON f.staffer_id = ct.staffer_id WHERE ct.class_id = c.class_id AND ct.teach_status='0') as from_staffer_cnname,
                (SELECT r.reason_note FROM smc_code_stuchange_reason as r WHERE r.company_id = sc.company_id AND r.reason_code = sc.reason_code) as reason_note,
                (SELECT group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', f.staffer_enname ) END ) ) ) FROM smc_class_teach as ct LEFT JOIN smc_staffer as f ON f.staffer_id = ct.staffer_id WHERE ct.class_id = a.class_id AND ct.teach_status='0') as to_staffer_cnname
            FROM smc_student_change as sc
            LEFT JOIN smc_class as c ON c.class_id = sc.from_class_id
            LEFT JOIN smc_class as a ON a.class_id = sc.to_class_id
            LEFT JOIN smc_student as t ON t.student_id = sc.student_id
            LEFT JOIN smc_course AS co ON co.course_id = c.course_id
            LEFT JOIN smc_code_coursecat AS cc ON cc.coursecat_id = co.coursecat_id
            LEFT JOIN smc_school AS s ON s.school_id = c.school_id
            WHERE {$datawhere}
            AND sc.company_id = '{$request['company_id']}'
            AND sc.to_stuchange_code = 'A05' AND t.student_id > 0
            order by s.school_branch
            ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
//                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['from_class_cnname'] = $dateexcelvar['from_class_cnname'];
                    $datearray['from_class_enname'] = $dateexcelvar['from_class_enname'];
                    $datearray['from_class_branch'] = $dateexcelvar['from_class_branch'];
                    $datearray['from_staffer_cnname'] = $dateexcelvar['from_staffer_cnname'];
                    $datearray['to_class_cnname'] = $dateexcelvar['to_class_cnname'];
                    $datearray['to_class_enname'] = $dateexcelvar['to_class_enname'];
                    $datearray['to_class_branch'] = $dateexcelvar['to_class_branch'];
                    $datearray['to_staffer_cnname'] = $dateexcelvar['to_staffer_cnname'];
                    $datearray['change_day'] = $dateexcelvar['change_day'];
                    $datearray['reason_note'] = $dateexcelvar['reason_note'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "转出班级名称", '转出班级别名', "转出班级编号", "转出班级教师", "转入班级名称", "转入班级别名", "转入班级编号", "转入班级教师", "异动时间", "异动原因"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'from_class_cnname', 'from_class_enname', 'from_class_branch', 'from_staffer_cnname', 'to_class_cnname', 'to_class_enname', 'to_class_branch', 'to_staffer_cnname', 'change_day', 'reason_note');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员转班明细报表{$week_start_day}-{$week_end_day}.xlsx"));
            exit;

        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT t.student_id
                              FROM smc_student_change as sc
                              LEFT JOIN smc_class as c ON c.class_id = sc.from_class_id
                              LEFT JOIN smc_class as a ON a.class_id = sc.to_class_id
                              LEFT JOIN smc_student as t ON t.student_id = sc.student_id
                              LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                              LEFT JOIN smc_code_coursecat AS cc ON cc.coursecat_id = co.coursecat_id
                              LEFT JOIN smc_school AS s ON s.school_id = c.school_id
                              WHERE {$datawhere}
                              AND sc.company_id = '{$request['company_id']}'
                              AND sc.to_stuchange_code = 'A05' AND t.student_id > 0";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;

            }
            $data['list'] = $classList;

            return $data;
        }
    }

    //学员升班明细报表
    function studentClassUp($request)
    {
        $datawhere = "  ";
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or t.student_enname like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or a.class_cnname like '%{$request['keyword']}%' or a.class_enname like '%{$request['keyword']}%' or a.class_branch like '%{$request['keyword']}%')";
        }
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $start_time = strtotime($request['start_time']);
            $datawhere .= " and s.upgradelog_createtime >= '{$start_time}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $end_time = strtotime($request['end_time']) + 86399;
            $datawhere .= " and s.upgradelog_createtime <= '{$end_time}'";
        }

        //学校筛选
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        //班种筛选
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and u.coursecat_id='{$request['coursecat_id']}'";
        }

        //班组筛选
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and u.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT l.school_id,l.school_cnname,l.school_branch,s.student_id,t.student_cnname,t.student_enname,t.student_branch,
                c.class_cnname as from_class_cnname,
                c.class_enname as from_class_enname,
                c.class_branch as from_class_branch,
                group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', f.staffer_enname ) END ) ) ) as staffer_cnname,
                a.class_cnname as to_class_cnname,
                a.class_enname as to_class_enname,
                a.class_branch as to_class_branch,
                s.upgradelog_createtime
            FROM smc_class_upgradelog as s 
            LEFT JOIN smc_class as c ON c.class_id = s.from_class_id
            LEFT JOIN smc_class as a ON a.class_id = s.to_class_id
            LEFT JOIN smc_student as t ON t.student_id = s.student_id
            LEFT JOIN smc_staffer as f ON f.staffer_id = s.staffer_id 
            LEFT JOIN smc_course as u ON u.course_id = c.course_id
            left join smc_school as l on s.school_id=l.school_id
            WHERE s.company_id = '{$request['company_id']}'
            and s.upgradelog_status <> '0' 
            {$datawhere}
            group by s.student_id,s.from_class_id
            order by upgradelog_createtime desc
            ";
//        LEFT JOIN smc_code_coursecat as ct ON ct.coursecat_id = u.coursecat_id
//        and s.upgradelog_class = '1'

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无升班数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                $one['upgradelog_createtime'] = date("Y-m-d H:i:s", $one['upgradelog_createtime']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['from_class_cnname'] = $dateexcelvar['from_class_cnname'];
                    $datearray['from_class_enname'] = $dateexcelvar['from_class_enname'];
                    $datearray['from_class_branch'] = $dateexcelvar['from_class_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['to_class_cnname'] = $dateexcelvar['to_class_cnname'];
                    $datearray['to_class_enname'] = $dateexcelvar['to_class_enname'];
                    $datearray['to_class_branch'] = $dateexcelvar['to_class_branch'];
                    $datearray['upgradelog_createtime'] = $dateexcelvar['upgradelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "学员中文名", "学员英文名", "学员编号", "来源班级名称", "来源班级别名", "来源班级编号", "班级教师", "升班班级名称", "升班班级别名", "升班班级编号", "升班时间"));
            $excelfileds = array("school_cnname", "school_branch", 'student_cnname', 'student_enname', 'student_branch', 'from_class_cnname', 'from_class_enname', 'from_class_branch', 'staffer_cnname', 'to_class_cnname', 'to_class_enname', 'to_class_branch', 'upgradelog_createtime');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '学员升班明细报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无异动数据";
            return false;
        }
        foreach ($list as &$one) {
            $one['upgradelog_createtime'] = date("Y-m-d H:i:s", $one['upgradelog_createtime']);
        }
        $data = array();
        $count_sql = "SELECT  s.upgradelog_id
                FROM smc_class_upgradelog as s 
            LEFT JOIN smc_class as c ON c.class_id = s.from_class_id
            LEFT JOIN smc_class as a ON a.class_id = s.to_class_id
            LEFT JOIN smc_student as t ON t.student_id = s.student_id
            LEFT JOIN smc_staffer as f ON f.staffer_id = s.staffer_id 
            LEFT JOIN smc_course as u ON u.course_id = c.course_id
            WHERE s.company_id = '{$request['company_id']}'
            and s.upgradelog_status <> '0' 
            {$datawhere}
            group by s.student_id
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $list;

        return $data;
    }

    //待开班
    function classToStartList($request)
    {
        $time = date("Y-m-d", time());
        $datawhere = "T.coursetype_isopenclass = '0' 
            and A.company_id='{$request['company_id']}' 
            and A.school_id='{$request['school_id']}' 
            and A.class_stdate >= '{$time}' 
            and A.class_status > '-2'  
            and A.class_type='0' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (A.class_cnname like '%{$request['keyword']}%' 
            or A.class_enname like '%{$request['keyword']}%' 
            or A.class_branch like '%{$request['keyword']}%' 
            or B.course_branch like '%{$request['keyword']}%' 
            or B.course_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '' && is_array($request['coursecat_id'])) {
            $coursecat_ids = implode("','", $request['coursecat_id']);
            $datawhere .= " and C.coursecat_id in('{$coursecat_ids}')";
        }

        //班组筛选
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and B.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select F.school_id,F.school_branch,F.school_cnname
                 ,A.class_id
                 ,C.coursecat_cnname 
                 ,C.coursecat_branch
                 ,A.class_cnname
                 ,A.class_branch
                 ,A.class_enname
                 ,A.class_stdate
                 ,A.class_enddate
                 ,A.class_timestr
                 ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_isfree=0 and hour_ischecking>-1) as class_feenum
                 ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_isfree=1 and hour_ischecking>-1) as class_freenum
                 ,group_concat(distinct(E.staffer_cnname)) as cnteacher
                 from smc_class A 
                 left join smc_course B on B.course_id=A.course_id 
                 left join smc_code_coursecat C on C.coursecat_id=B.coursecat_id
                 left join smc_code_coursetype T on T.coursetype_id = B.coursetype_id
                 left join smc_class_hour_teaching D on D.class_id=A.class_id 
                 left join smc_staffer E on E.staffer_id=D.staffer_id 
                 left join smc_school F on A.school_id=F.school_id and A.company_id=F.company_id
                 where {$datawhere}
                 group by A.class_id 
                 order by F.school_branch,C.coursecat_id desc,A.class_enname asc 
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无待开班数据";
                return false;
            }
            foreach ($dateexcelarray as &$val) {
                $val['classTime'] = $val['class_stdate'] . '-' . $val['class_enddate'];

                $val['class_totalnum'] = $val['class_feenum'] + $val['class_freenum'];
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['classTime'] = $dateexcelvar['classTime'];
                    $datearray['class_timestr'] = $dateexcelvar['class_timestr'];
                    $datearray['cnteacher'] = $dateexcelvar['cnteacher'];
                    $datearray['class_totalnum'] = $dateexcelvar['class_totalnum'];
                    $datearray['class_feenum'] = $dateexcelvar['class_feenum'];
                    $datearray['class_freenum'] = $dateexcelvar['class_freenum'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "班种", "班种编号", "班级名称", "英文名称", "班级编号", "起止日期", "上课时段", "教师姓名", '总课次数', '收费课次数', '赠送课次数'));
            $excelfileds = array('school_branch', 'school_cnname', 'coursecat_cnname', 'coursecat_branch', 'class_cnname', 'class_enname', 'class_branch', 'classTime', 'class_timestr', 'cnteacher', 'class_totalnum', 'class_feenum', 'class_freenum');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}班级待开班报表{$time}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);

            if (!$classList) {
                $this->error = true;
                $this->errortip = "无待开班数据";
                return false;
            }

            foreach ($classList as &$val) {
                $val['classTime'] = $val['class_stdate'] . '-' . $val['class_enddate'];

                $val['class_totalnum'] = $val['class_feenum'] + $val['class_freenum'];
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.class_id
                              from smc_class A
                              left join smc_course B on B.course_id=A.course_id
                              left join smc_code_coursecat C on C.coursecat_id=B.coursecat_id
                              left join smc_code_coursetype T on T.coursetype_id = B.coursetype_id
                              left join smc_class_hour_teaching D on D.class_id=A.class_id
                              left join smc_staffer E on E.staffer_id=D.staffer_id
                              where {$datawhere}
                              group by A.class_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;

            }
            $data['list'] = $classList;

            return $data;
        }
    }

    /**
     * 周人数报表
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return mixed
     */
    function weeklyNumberList($request)
    {
        $datawhere = " G.coursetype_isopenclass = '0' ";
        $studywhere = " 1  ";

        $time = date("Y-m-d", time());
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (A.class_cnname like '%{$request['keyword']}%' 
            or A.class_enname like '%{$request['keyword']}%' 
            or A.class_branch like '%{$request['keyword']}%' 
            or B.course_branch like '%{$request['keyword']}%' 
            or B.course_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $time = $request['start_time'];
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
            $studywhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and G.coursetype_id='{$request['coursetype_id']}'";
            $studywhere .= " and E.coursetype_id='{$request['coursetype_id']}'";
        }

        $week_start_day = date("Y-m-d", strtotime($time));
        $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select F.school_id 
            ,F.school_branch 
            ,F.school_cnname 
            ,A.class_id 
            ,G.coursetype_cnname 
            ,G.coursetype_branch 
            ,C.coursecat_cnname 
            ,C.coursecat_branch 
            ,B.course_branch 
            ,A.class_cnname 
            ,A.class_branch 
            ,A.class_isupgrade 
            ,A.class_isfictitious 
            ,A.class_enname 
            ,A.class_stdate 
            ,A.class_enddate 
            ,(select group_concat(bb.week_cnname,' ',aa.hour_starttime,'-',aa.hour_endtime) from smc_class_hour aa,smc_code_week bb 
                where weekday(hour_day)=bb.week_id and class_id=A.class_id and hour_ischecking>-1 and hour_day>='{$week_start_day}' and hour_day<='{$week_end_day}') as class_timestr 
            ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_ischecking=1 and hour_day>='{$week_start_day}' and hour_day<='{$week_end_day}') as deduct_num 
            ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_isfree=0 and hour_ischecking>-1) as class_feenum 
            ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_isfree=1 and hour_ischecking>-1) as class_freenum 
            ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_isfree=0 and hour_ischecking=1 and hour_day<='{$week_end_day}') as feenum 
            ,(select count(hour_id) from smc_class_hour where class_id=A.class_id and hour_isfree=1 and hour_ischecking=1 and hour_day<='{$week_end_day}') as freenum 
            ,group_concat(distinct(E.staffer_cnname)) as cnteacher 
            ,B.course_inclasstype 
            ,B.course_weekstandardnum 
            ,(select class_branch from smc_class where from_class_id=a.class_id) as next_class_branch		
            ,(select count(study_id) from smc_student_study q,smc_class p,smc_course o where q.class_id=p.class_id and p.course_id=o.course_id
                and q.study_endday>='{$week_start_day}' and q.study_beginday<='{$week_start_day}' and q.class_id=A.class_id 
                and NOT exists(select 1 from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id 
                and x.study_endday>='{$week_start_day}' and x.study_beginday<='{$week_start_day}' and x.student_id=q.student_id and x.class_id<>q.class_id and y.school_id=p.school_id 
                and x.study_beginday>q.study_beginday and z.coursetype_id=o.coursetype_id and y.class_type=0 and y.class_status>-2)) as oldweeknum 
            ,(select count(1) from smc_student_changelog where class_id=A.class_id and stuchange_code in('A03','A04','A05') 
                and changelog_day<='{$week_end_day}' and changelog_day>='{$week_start_day}') as innum 
            ,(select count(1) from smc_student_changelog where class_id=A.class_id and stuchange_code in('B02','B03','B04','B07') 
                and changelog_day<='{$week_end_day}' and changelog_day>='{$week_start_day}') as outnum 
            ,(select count(1) from smc_student_changelog where class_id=A.class_id and stuchange_code in('A07') 
                and changelog_day<='{$week_end_day}' and changelog_day>='{$week_start_day}') as staynum 
            ,(select count(1) from smc_student_changelog where class_id=A.class_id and stuchange_code in('B01','C03') 
                and changelog_day<='{$week_end_day}' and changelog_day>='{$week_start_day}') as lostnum 
            ,(select count(1) from smc_student_changelog where class_id=A.class_id and stuchange_code in('A02') 
                and changelog_day<='{$week_end_day}' and changelog_day>='{$week_start_day}') as freshnum 
            ,(select count(study_id) from smc_student_study q,smc_class p,smc_course o where q.class_id=p.class_id and p.course_id=o.course_id
                and q.study_endday>='{$week_start_day}' and q.study_beginday<='{$week_end_day}' and q.class_id=A.class_id 
                and NOT exists(select 1 from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id 
                and x.study_endday>='{$week_start_day}' and x.study_beginday<='{$week_end_day}' 
                and y.class_enddate>='{$week_start_day}' and y.class_stdate<='{$week_end_day}' 
                and x.student_id=q.student_id and x.class_id<>q.class_id and y.school_id=p.school_id 
                and x.study_beginday>q.study_beginday and z.coursetype_id=o.coursetype_id and y.class_type=0 and y.class_status>-2)) as newweeknum 
            from smc_class A 
            left join smc_course B on B.course_id=A.course_id 
            left join smc_code_coursecat C on C.coursecat_id=B.coursecat_id 
            left join smc_class_hour_teaching D on D.class_id=A.class_id 
            left join smc_staffer E on E.staffer_id=D.staffer_id 
            left join smc_school F on A.school_id=F.school_id 
            left join smc_code_coursetype G on G.coursetype_id = C.coursetype_id 
            where {$datawhere}
            and A.company_id='{$request['company_id']}' 
            and A.class_type=0 
            and A.class_status>'-2' 
            and A.class_enddate>='{$week_start_day}' 
            and A.class_stdate<='{$week_end_day}' 
            and G.coursetype_isopenclass=0 
            group by A.class_id 
            order by  A.class_stdate,C.coursecat_id desc,A.class_enname asc 
              ";

        $sqlall = "SELECT A.class_id,C.class_branch,B.student_branch,B.student_cnname 
            FROM smc_student_study A 
            LEFT JOIN smc_student B ON A.student_id=B.student_id AND A.company_id=B.company_id 
            LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id 
            LEFT JOIN smc_course D ON C.course_id=D.course_id 
            LEFT JOIN smc_code_coursetype E ON E.coursetype_id = D.coursetype_id 
            LEFT JOIN smc_school F ON A.school_id=F.school_id 
            WHERE {$studywhere}
            AND A.company_id='{$request['company_id']}' 
            AND C.class_type =0  
            AND C.class_status>'-2' 
            AND C.class_enddate>='{$week_start_day}' 
            AND C.class_stdate<='{$week_end_day}' 
            AND A.study_endday>='{$week_start_day}' 
            AND A.study_beginday<='{$week_end_day}' 
            AND E.coursetype_isopenclass=0 
            and (d.course_inclasstype<>2 
                or exists(select 1 from smc_class_booking x left join smc_class_hour y on x.hour_id=y.hour_id 
                    where x.class_id=a.class_id and x.student_id=a.student_id and y.hour_day>='{$week_start_day}' and y.hour_day<='{$week_end_day}' and x.booking_status=0))
            and NOT exists(select 1 from smc_student_study x,smc_class y,smc_course z 
                where x.class_id=y.class_id and y.course_id=z.course_id 
                and x.study_endday>='{$week_start_day}' and x.study_beginday<='{$week_end_day}' 
                and y.class_enddate>='{$week_start_day}' and y.class_stdate<='{$week_end_day}' 
                and x.student_id=A.student_id and x.class_id<>A.class_id 
                and y.school_id=C.school_id and x.study_beginday>A.study_beginday 
                and z.coursetype_id=E.coursetype_id and y.class_type=0 and y.class_status>-2)";

        $class_isfictitious = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        $class_isupgrade = $this->LgArraySwitch(array("0" => "新建班级", "1" => "升级班级"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_shortname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            $classstudentarray = $this->DataControl->selectClear($sqlall);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            foreach ($dateexcelarray as &$val) {
                $val['classTime'] = $val['class_stdate'] . '-' . $val['class_enddate'];
                if ($val['class_freenum'] > 0) {
                    $val['classInfo'] = $val['feenum'] . '(' . $val['freenum'] . ')/' . $val['class_feenum'] . '(' . $val['class_freenum'] . ')';
                } else {
                    $val['classInfo'] = $val['feenum'] . '/' . $val['class_feenum'];
                }

                $val['class_totalnum'] = $val['class_feenum'] + $val['class_freenum'];
                $val['left_totalnum'] = $val['class_feenum'] + $val['class_freenum'] - $val['feenum'] - $val['freenum'];
                $val['left_feenum'] = $val['class_feenum'] - $val['feenum'];
                $val['left_freenum'] = $val['class_freenum'] - $val['freenum'];
                $val['standard_num'] = $val['course_inclasstype'] == '0' ? $val['course_weekstandardnum'] : '--';
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();

                $oldweeknum = 0;
                $innum = 0;
                $outnum = 0;
                $staynum = 0;
                $lostnum = 0;
                $freshnum = 0;
                $newweeknum = 0;
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_isupgrade'] = $class_isupgrade[$dateexcelvar['class_isupgrade']];
                    $datearray['class_isfictitious'] = $class_isfictitious[$dateexcelvar['class_isfictitious']];
                    $datearray['classTime'] = $dateexcelvar['classTime'];
                    $datearray['class_timestr'] = $dateexcelvar['class_timestr'];
                    $datearray['cnteacher'] = $dateexcelvar['cnteacher'];
                    $datearray['classInfo'] = $dateexcelvar['classInfo'];
                    $datearray['deduct_num'] = $dateexcelvar['deduct_num'];
                    $datearray['standard_num'] = $dateexcelvar['standard_num'];
                    $datearray['class_totalnum'] = $dateexcelvar['class_totalnum'];
                    $datearray['class_feenum'] = $dateexcelvar['class_feenum'];
                    $datearray['class_freenum'] = $dateexcelvar['class_freenum'];
                    $datearray['left_totalnum'] = $dateexcelvar['left_totalnum'];
                    $datearray['left_feenum'] = $dateexcelvar['left_feenum'];
                    $datearray['left_freenum'] = $dateexcelvar['left_freenum'];
                    $datearray['oldweeknum'] = $dateexcelvar['oldweeknum'];
                    $datearray['innum'] = $dateexcelvar['innum'];
                    $datearray['outnum'] = $dateexcelvar['outnum'];
                    $datearray['staynum'] = $dateexcelvar['staynum'];
                    $datearray['lostnum'] = $dateexcelvar['lostnum'];
                    $datearray['freshnum'] = $dateexcelvar['freshnum'];

                    if ($dateexcelvar['course_inclasstype'] == '2') {
                        $sql = "select a.student_id 
                    from smc_class_booking a 
                    left join smc_class_hour b on a.hour_id=b.hour_id
                    where a.class_id='{$dateexcelvar['class_id']}'
                    and b.hour_day>='{$week_start_day}'
                    and b.hour_day<='{$week_end_day}'
                    and a.booking_status=0
                    group by a.student_id 
                    ";
                        $bookList = $this->DataControl->selectClear($sql);
                        if ($bookList) {
                            $datearray['newweeknum'] = count($bookList);
                        } else {
                            $datearray['newweeknum'] = 0;
                        }
                    } else {
                        $datearray['newweeknum'] = $dateexcelvar['newweeknum'];
                    }

                    $outexceldate[] = $datearray;

                    $oldweeknum += $dateexcelvar['oldweeknum'];
                    $innum += $dateexcelvar['innum'];
                    $outnum += $dateexcelvar['outnum'];
                    $staynum += $dateexcelvar['staynum'];
                    $lostnum += $dateexcelvar['lostnum'];
                    $freshnum += $dateexcelvar['freshnum'];
                    $newweeknum += $dateexcelvar['newweeknum'];
                }

                $datearray['coursecat_cnname'] = "合计";
                $datearray['coursecat_branch'] = "";
                $datearray['class_cnname'] = "";
                $datearray['class_enname'] = "";
                $datearray['class_branch'] = "";
                $datearray['class_isupgrade'] = "";
                $datearray['class_isfictitious'] = "";
                $datearray['classTime'] = "";
                $datearray['class_timestr'] = "";
                $datearray['cnteacher'] = "";
                $datearray['classInfo'] = "";
                $datearray['class_totalnum'] = "";
                $datearray['class_feenum'] = "";
                $datearray['class_freenum'] = "";
                $datearray['left_totalnum'] = "";
                $datearray['left_feenum'] = "";
                $datearray['left_freenum'] = "";
                $datearray['oldweeknum'] = $oldweeknum;
                $datearray['innum'] = $innum;
                $datearray['outnum'] = $outnum;
                $datearray['staynum'] = $staynum;
                $datearray['lostnum'] = $lostnum;
                $datearray['freshnum'] = $freshnum;
                $datearray['newweeknum'] = $newweeknum;
                $datearray['next_class_branch'] = $dateexcelvar['next_class_branch'];
                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("班组", "班组编号", "班种", "班种编号", "班级名称", "英文名称", "班级编号", "新建/升级", "是否虚拟班级", "起止日期", "上课时段", "教师姓名", "已上课次数", '本周考勤次数', '周排课标准课次', '总课次数', '收费课次数', '赠送课次数', '剩余总课次', '收费剩余课次', '赠送剩余课次', "上周人数", "转入", "转出", "延班", "流失", "新生", "本周人数", "升班编号"));
            $excelfileds = array('coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'class_cnname', 'class_enname', 'class_branch', 'class_isupgrade', 'class_isfictitious', 'classTime', 'class_timestr', 'cnteacher', 'classInfo', 'deduct_num', 'standard_num', 'class_totalnum', 'class_feenum', 'class_freenum', 'left_totalnum', 'left_feenum', 'left_freenum', 'oldweeknum', 'innum', 'outnum', 'staynum', 'lostnum', 'freshnum', 'newweeknum', 'next_class_branch');
            excelcustom_weeklyNumber($classstudentarray, $excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_shortname']}班级周人数报表{$week_start_day}-{$week_end_day}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);

            if (!$classList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            foreach ($classList as &$val) {
                $val['classTime'] = $val['class_stdate'] . '-' . $val['class_enddate'];
                if ($val['class_freenum'] > 0) {
                    $val['classInfo'] = $val['feenum'] . '(' . $val['freenum'] . ')/' . $val['class_feenum'] . '(' . $val['class_freenum'] . ')';
                } else {
                    $val['classInfo'] = $val['feenum'] . '/' . $val['class_feenum'];
                }

                $val['class_totalnum'] = $val['class_feenum'] + $val['class_freenum'];
                $val['left_totalnum'] = $val['class_feenum'] + $val['class_freenum'] - $val['feenum'] - $val['freenum'];
                $val['left_feenum'] = $val['class_feenum'] - $val['feenum'];
                $val['left_freenum'] = $val['class_freenum'] - $val['freenum'];
                $val['class_isupgrade'] = $class_isupgrade[$val['class_isupgrade']];
                $val['class_isfictitious'] = $class_isfictitious[$val['class_isfictitious']];
                $val['standard_num'] = $val['course_inclasstype'] == '0' ? $val['course_weekstandardnum'] : '--';

                if ($val['course_inclasstype'] == '2') {
                    $sql = "select a.student_id
                    from smc_class_booking a 
                    left join smc_class_hour b on a.hour_id=b.hour_id
                    where a.class_id='{$val['class_id']}'
                    and b.hour_day>='{$week_start_day}'
                    and b.hour_day<='{$week_end_day}'
                    and a.booking_status=0
                    group by a.student_id 
                    ";
                    $bookList = $this->DataControl->selectClear($sql);
                    if ($bookList) {
                        $val['newweeknum'] = count($bookList);
                    } else {
                        $val['newweeknum'] = 0;
                    }
                }
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.class_id
                    from smc_class A
                    left join smc_course B on B.course_id=A.course_id
                    left join smc_code_coursecat C on C.coursecat_id=B.coursecat_id
                    left join smc_class_hour_teaching D on D.class_id=A.class_id
                    left join smc_staffer E on E.staffer_id=D.staffer_id
                    left join smc_school F on A.school_id=F.school_id 
                    left join smc_code_coursetype G on G.coursetype_id = C.coursetype_id
                    where {$datawhere} 
                    and A.company_id='{$request['company_id']}' 
                    and A.class_type=0 
                    and A.class_status>'-2' 
                    and A.class_enddate>='{$week_start_day}' 
                    and A.class_stdate<='{$week_end_day}' 
                    and G.coursetype_isopenclass=0 
                    and A.class_type =0 
                    group by A.class_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $classList;
            return $data;
        }
    }

    //学员拆班表
    function classBreakoffSummary($request)
    {
        $datawhere = " A.company_id='{$request['company_id']}' ";
        $havingwhere = " having 1 ";
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " 
            and (D.student_cnname like '%{$request['keyword']}%' 
            or D.student_enname like '%{$request['keyword']}%' 
            or D.student_branch like '%{$request['keyword']}%' 
            or B.class_cnname like '%{$request['keyword']}%' 
            or B.class_enname like '%{$request['keyword']}%' 
            or B.class_branch like '%{$request['keyword']}%')";
        }
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $query_stdate = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and B.class_enddate >= '{$query_stdate}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $query_enddate = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and B.class_enddate <= '{$query_enddate}'";
        }
        //学校筛选
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }
        //班组筛选
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and F.coursetype_id='{$request['coursetype_id']}'";
        }
        //班种筛选
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and F.coursecat_id='{$request['coursecat_id']}'";
        }
        //班别筛选
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and F.course_id='{$request['course_id']}'";
        }
        //拆班类别
        if (isset($request['breakoff_type']) && $request['breakoff_type'] !== '') {
            $datawhere .= " and A.breakoff_type='{$request['breakoff_type']}'";
        }
        //学员状态
        if (isset($request['student_status']) && $request['student_status'] !== '') {
            $datawhere .= " and J.enrolled_status='{$request['student_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT E.school_id,E.school_branch,E.school_shortname AS school_cnname,
            D.student_id,D.student_branch,D.student_cnname,D.student_enname,J.enrolled_status,
            (SELECT changelog_note FROM smc_student_changelog WHERE changelog_day>=C.study_endday 
            AND student_id=C.student_id AND school_id=C.school_id AND stuchange_code='C02' 
            ORDER BY changelog_day DESC,changelog_id DESC LIMIT 0,1) AS lost_reason,
            (SELECT changelog_day FROM smc_student_changelog WHERE changelog_day>=C.study_endday 
            AND student_id=C.student_id AND school_id=C.school_id AND stuchange_code='C02' 
            ORDER BY changelog_day DESC,changelog_id DESC LIMIT 0,1) AS lost_date,
            get_reading_status(C.student_id,C.class_id,B.class_enddate) AS is_endreading,
            (SELECT max(hour_day) FROM smc_class_hour WHERE class_id=A.class_id AND hour_ischecking>=0 AND hour_iswarming=0) as hour_enddate,C.study_endday,
            (SELECT max(hour_lessontimes) FROM smc_class_hour WHERE class_id=A.class_id AND hour_ischecking>=0 AND hour_iswarming=0) as class_times,
            B.class_id,B.class_branch,B.class_cnname,B.class_enname,
            B.class_enddate,A.breakoff_type,
            H.coursetype_id,H.coursetype_branch,H.coursetype_cnname,
            G.coursecat_id,G.coursecat_branch,G.coursecat_cnname,
            F.course_id,F.course_branch,F.course_cnname,
            (
            SELECT Y.class_branch
            FROM smc_student_study X,smc_class Y,smc_course Z,smc_student_hourstudy W
            WHERE X.class_id=Y.class_id AND Y.course_id=Z.course_id
            AND X.student_id=W.student_id AND X.class_id=W.class_id
            AND X.student_id=C.student_id AND X.school_id=C.school_id 
            AND Z.coursetype_id=H.coursetype_id AND X.class_id<>C.class_id
            AND X.study_beginday>=B.class_enddate AND Y.class_type='0' 
            GROUP BY X.class_id 
            ORDER BY X.study_beginday
            LIMIT 0,1
            ) AS after_class_branch,
            (
            SELECT Y.class_cnname
            FROM smc_student_study X,smc_class Y,smc_course Z,smc_student_hourstudy W
            WHERE X.class_id=Y.class_id AND Y.course_id=Z.course_id
            AND X.student_id=W.student_id AND X.class_id=W.class_id
            AND X.student_id=C.student_id AND X.school_id=C.school_id 
            AND Z.coursetype_id=H.coursetype_id AND X.class_id<>C.class_id
            AND X.study_beginday>=B.class_enddate AND Y.class_type='0' 
            GROUP BY X.class_id 
            ORDER BY X.study_beginday
            LIMIT 0,1
            ) AS after_class_cnname,
            (
            SELECT Y.class_enname
            FROM smc_student_study X,smc_class Y,smc_course Z,smc_student_hourstudy W
            WHERE X.class_id=Y.class_id AND Y.course_id=Z.course_id
            AND X.student_id=W.student_id AND X.class_id=W.class_id
            AND X.student_id=C.student_id AND X.school_id=C.school_id 
            AND Z.coursetype_id=H.coursetype_id AND X.class_id<>C.class_id
            AND X.study_beginday>=B.class_enddate AND Y.class_type='0' 
            GROUP BY X.class_id 
            ORDER BY X.study_beginday
            LIMIT 0,1
            ) AS after_class_enname,
            (
            SELECT count(W.hourstudy_id)
            FROM smc_student_study X,smc_class Y,smc_course Z,smc_student_hourstudy W
            WHERE X.class_id=Y.class_id AND Y.course_id=Z.course_id
            AND X.student_id=W.student_id AND X.class_id=W.class_id
            AND X.student_id=C.student_id AND X.school_id=C.school_id 
            AND Z.coursetype_id=H.coursetype_id AND X.class_id<>C.class_id
            AND X.study_beginday>=B.class_enddate AND Y.class_type='0' 
            GROUP BY X.class_id 
            ORDER BY X.study_beginday
            LIMIT 0,1
            ) AS after_class_times
            FROM smc_class_breakoff A 
            LEFT JOIN smc_class B ON A.class_id=B.class_id AND A.school_id=B.school_id AND A.company_id=B.company_id 
            INNER JOIN smc_student_study C ON A.class_id=C.class_id AND B.school_id=C.school_id AND B.company_id=C.company_id 
            LEFT JOIN smc_student D ON C.student_id=D.student_id AND C.company_id=D.company_id 
            LEFT JOIN smc_school E ON B.school_id=E.school_id AND B.company_id=E.company_id 
            LEFT JOIN smc_course F ON B.company_id=F.company_id AND B.course_id=F.course_id
            LEFT JOIN smc_code_coursecat G ON F.company_id=G.company_id AND F.coursecat_id=G.coursecat_id
            LEFT JOIN smc_code_coursetype H ON G.company_id=H.company_id AND G.coursetype_id=H.coursetype_id
            LEFT JOIN smc_student_enrolled J ON C.school_id=J.school_id AND C.student_id=J.student_id
            WHERE {$datawhere} 
            AND A.breakoff_status>=2 
            having C.study_endday>=hour_enddate 
            ORDER BY E.school_branch,B.class_enddate 
            ";

        $enrolled_status = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "新生", "-1" => "已离校"));
        $breakoff_type = $this->LgArraySwitch(array("0" => "中途拆班", "1" => "期末拆班"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无升班数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];

                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];

                    $datearray['enrolled_status'] = $enrolled_status[$dateexcelvar['enrolled_status']];
                    $datearray['lost_reason'] = $dateexcelvar['lost_reason'];
                    $datearray['lost_date'] = $dateexcelvar['lost_date'];
                    $datearray['class_enddate'] = $dateexcelvar['class_enddate'];
                    $datearray['breakoff_type'] = $breakoff_type[$dateexcelvar['breakoff_type']];

                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_times'] = $dateexcelvar['class_times'];

                    $datearray['after_class_cnname'] = $dateexcelvar['after_class_cnname'];
                    $datearray['after_class_enname'] = $dateexcelvar['after_class_enname'];
                    $datearray['after_class_branch'] = $dateexcelvar['after_class_branch'];
                    $datearray['after_class_times'] = $dateexcelvar['after_class_times'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "学员中文名", "学员英文名", "学员编号", "学员状态", "流失原因", "流失日期", "班级结束日期", "拆班类型", "中途拆班班级名称", "中途拆班班级别名", "中途拆班班级编号", "中途拆班班级上课课次", "下次就读班级名称", "下次就读班级别名", "下次就读班级编号", "下次就读班级上课课次"));
            $excelfileds = array("school_cnname", "school_branch", 'student_cnname', 'student_enname', 'student_branch', 'enrolled_status', 'lost_reason', 'lost_date', 'class_enddate', 'breakoff_type', 'class_cnname', 'class_enname', 'class_branch', 'class_times', 'after_class_cnname', 'after_class_enname', 'after_class_branch', 'after_class_times');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '中途拆班学员去向分析表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无拆班数据";
            return false;
        }
        foreach ($list as &$one) {
            $one['enrolled_status'] = $enrolled_status[$one['enrolled_status']];
            $one['breakoff_type'] = $breakoff_type[$one['breakoff_type']];
        }
        $data = array();
        $count_sql = "SELECT E.school_id,
            D.student_id,
            J.enrolled_status,
            B.class_id,
            B.class_enddate,
            A.breakoff_type,
            (SELECT max(hour_day) FROM smc_class_hour WHERE class_id=A.class_id AND hour_ischecking>=0 AND hour_iswarming=0) as hour_enddate,
            C.study_endday
            FROM smc_class_breakoff A 
            LEFT JOIN smc_class B ON A.class_id=B.class_id AND A.school_id=B.school_id AND A.company_id=B.company_id 
            INNER JOIN smc_student_study C ON A.class_id=C.class_id AND B.school_id=C.school_id AND B.company_id=C.company_id 
            LEFT JOIN smc_student D ON C.student_id=D.student_id AND C.company_id=D.company_id 
            LEFT JOIN smc_school E ON B.school_id=E.school_id AND B.company_id=E.company_id 
            LEFT JOIN smc_course F ON B.company_id=F.company_id AND B.course_id=F.course_id
            LEFT JOIN smc_student_enrolled J ON C.school_id=J.school_id AND C.student_id=J.student_id
            WHERE {$datawhere} 
            AND A.breakoff_status>=2 
            having C.study_endday>=hour_enddate 
            ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $list;

        return $data;
    }


    //班级学员流失率
    function classYearlossrate($request)
    {
        $datawhere = "c.class_id = e.class_id AND u.course_id = c.course_id AND c.company_id='{$request['company_id']}' ";
        $nowDay = date("Y-m-d");
        $datawhere .= " and e.endcalc_enddate <= '{$nowDay}'";
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= "and (c.class_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%')";
        }
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $query_stdate = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and e.endcalc_enddate >= '{$query_stdate}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $query_enddate = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and e.endcalc_enddate <= '{$query_enddate}'";
        }
        //学校筛选
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id='{$request['school_id']}'";
        }
        //班组筛选
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and e.coursetype_id='{$request['coursetype_id']}'";
        }
        //是否拆并班
        if (isset($request['is_breakoff']) && $request['is_breakoff'] !== '') {
            if ($request['is_breakoff'] == '1') {
                $datawhere .= " and c.class_id IN (SELECT b.class_id FROM smc_class_breakoff AS b WHERE b.company_id = '{$request['company_id']}' AND b.breakoff_status = '2')";
            } elseif ($request['is_breakoff'] == '0') {
                $datawhere .= " and c.class_id NOT IN (SELECT b.class_id FROM smc_class_breakoff AS b WHERE b.company_id = '{$request['company_id']}' AND b.breakoff_status = '2')";
            }
        }
        //是否留续班级
        if (isset($request['course_isrenew']) && $request['course_isrenew'] !== '') {
            $datawhere .= " and u.course_isrenew= '{$request['course_isrenew']}'";
            $datawhere .= " and c.class_isnotrenew <>'{$request['course_isrenew']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT e.endcalc_id,c.class_branch, c.class_cnname, c.class_enname, e.endcalc_startdate, e.endcalc_enddate
, (SELECT COUNT(d.student_id) FROM smc_class_endcalc_study d WHERE study_iscalculate>=0 and  d.endcalc_id = e.endcalc_id AND d.study_outtype <> '-1' ) AS beginnums
, (SELECT COUNT(d.student_id) FROM smc_class_endcalc_study d WHERE study_iscalculate>=0 and  d.endcalc_id = e.endcalc_id AND d.study_outtype = '2' ) AS transoutnums
, (SELECT COUNT(d.student_id) FROM smc_class_endcalc_study d WHERE study_iscalculate>=0 and  d.endcalc_id = e.endcalc_id AND d.study_outtype = '3' AND d.study_upgraderate>=100) AS deferrednums
, (SELECT COUNT(d.student_id) FROM smc_class_endcalc_study d WHERE study_iscalculate>=0 and  d.endcalc_id = e.endcalc_id AND d.study_outtype IN ('0', '1') AND d.study_upgraderate>=100) AS upgradenums
, (SELECT GROUP_CONCAT(ss.class_enname) FROM smc_class ss WHERE FIND_IN_SET(ss.class_id, e.endcalc_fromlocus)) AS yearlocus
FROM smc_class_endcalc e, smc_class c,smc_course u WHERE {$datawhere}";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无升班数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['endcalc_startdate'] = $dateexcelvar['endcalc_startdate'];
                    $datearray['endcalc_enddate'] = $dateexcelvar['endcalc_enddate'];
                    $datearray['beginnums'] = $dateexcelvar['beginnums'];
                    $datearray['transoutnums'] = $dateexcelvar['transoutnums'];
                    $datearray['deferrednums'] = $dateexcelvar['deferrednums'];
                    $datearray['upgradenums'] = $dateexcelvar['upgradenums'];
                    $datearray['loss_rate'] = round((($dateexcelvar['beginnums'] - $dateexcelvar['transoutnums'] - $dateexcelvar['deferrednums'] - $dateexcelvar['upgradenums']) / ($dateexcelvar['beginnums'] - $dateexcelvar['transoutnums'])) * 100, 2) . "%";
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("班级名称", "班级别名", "班级编号", "学年开班日期", "学年结束日期", "入班累计学员数", "转班转出累计学员", "延班学员数", "续读人数", "班级流失预估率"));
            $excelfileds = array("class_cnname", "class_enname", 'class_branch', 'endcalc_startdate', 'endcalc_enddate', 'beginnums', 'transoutnums', 'deferrednums', 'upgradenums', 'loss_rate');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '班级学年流失率表.xlsx');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无拆班数据";
            return false;
        }
        foreach ($list as &$one) {
            $one['loss_rate'] = round((($one['beginnums'] - $one['transoutnums'] - $one['deferrednums'] - $one['upgradenums']) / ($one['beginnums'] - $one['transoutnums'])) * 100, 2) . "%";

        }
        $data = array();
        $count_sql = "SELECT COUNT(e.endcalc_id) AS enums FROM smc_class_endcalc e, smc_class c,smc_course u WHERE {$datawhere}";
        $dbNums = $this->DataControl->selectOne($count_sql);
        if ($dbNums) {
            $allnum = $dbNums['enums'];
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $list;

        return $data;
    }

    //班级学员流失率
    function classUpgradeSummary($request)
    {
        $datawhere = "a.company_id='{$request['company_id']}' ";

        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= "and (a.class_branch like '%{$request['keyword']}%' 
            or a.class_cnname like '%{$request['keyword']}%' 
            or a.class_enname like '%{$request['keyword']}%')";
        }
        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND a.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $query_stdate = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and a.class_enddate >= '{$query_stdate}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $query_enddate = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and a.class_enddate <= '{$query_enddate}'";
        }
        //学校筛选
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " and b.school_istest<>'1' and b.school_isclose<>'1' ";
        }
        //班级状态筛选
        if (isset($request['class_status']) && $request['class_status'] !== '') {
            $datawhere .= " and a.class_status='{$request['class_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT a.school_id
        ,b.school_branch
        ,b.school_cnname
        ,a.class_id
        ,a.class_branch
        ,a.class_cnname
        ,a.class_enname
        ,a.class_stdate
        ,a.class_enddate
        ,a.class_status
        ,(select count(1) from smc_student_study where class_id=a.class_id 
            and study_isreading=1) as student_now
        ,(select count(1) from smc_student_study where class_id=a.class_id 
            and study_beginday<=DATE_SUB(a.class_enddate,interval 42 day) 
            and study_endday>=DATE_SUB(a.class_enddate,interval 42 day)) as student_num
        ,c.course_nextid
        FROM smc_class a
        LEFT JOIN smc_school b on a.school_id=b.school_id
        left join smc_course c on a.course_id=c.course_id
        where {$datawhere}
        and a.class_status=1
        and c.course_isrenew=1
        and a.class_isnotrenew=0
        and a.class_type=0
        order by (case when b.school_istest=0 and b.school_isclose=0 then 1 when b.school_isclose=0 then 2 when b.school_istest=0 then 3 else 4 end)
        ,b.school_istest asc,field(b.school_sort,0),b.school_sort asc,b.school_createtime asc,b.school_branch,a.class_enddate";

        $class_status = $this->LgArraySwitch(array("-1" => "已结束", "1" => "进行中"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预制升班数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_stdate'] = $dateexcelvar['class_stdate'];
                    $datearray['class_enddate'] = $dateexcelvar['class_enddate'];
                    $datearray['class_status'] = $class_status[$dateexcelvar['class_status']];
                    $datearray['student_now'] = $dateexcelvar['student_now'];
                    $datearray['student_num'] = $dateexcelvar['student_num'];

                    if (!$dateexcelvar['course_nextid'] || $dateexcelvar['course_nextid'] == '') {
                        $dateexcelvar['course_nextid'] = '0';
                    }

                    $sql = "select count(ta.student_id) as order_num
                        ,count(if(ta.order_status=4 and buy_price>=order_price,true,null)) as pay_num
                        from (
                        select x.student_id,min(z.order_status) as order_status,min(w.coursebalance_figure) as buy_price,min(pricinglog_buyprice) as order_price
                        from smc_student_study x
                        left join smc_student_coursebalance_pricinglog y on x.student_id=y.student_id and y.school_id='{$dateexcelvar['school_id']}'
                        left join smc_payfee_order z on y.order_pid=z.order_pid
                        left join smc_student_coursebalance w on w.school_id=y.school_id and w.student_id=y.student_id and w.course_id=y.course_id
                        where 1
                        and x.class_id='{$dateexcelvar['class_id']}'
                        and z.order_status>=0
                        and x.study_isreading=1
                        and y.course_id in({$dateexcelvar['course_nextid']})
                        group by x.student_id
                        ) as ta
                        where 1  ";
                    $orderNums = $this->DataControl->selectOne($sql);
                    if ($orderNums) {
                        $datearray['order_num'] = $orderNums['order_num'];
                        $datearray['pay_num'] = $orderNums['pay_num'];
                    } else {
                        $datearray['order_num'] = 0;
                        $datearray['pay_num'] = 0;
                    }


                    $datearray['order_num'] = $dateexcelvar['order_num'];
                    $datearray['pay_num'] = $dateexcelvar['pay_num'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "班级别名", "班级编号", "开班日期", "结班日期", "班级状态", "当前在班学员", "结班前42天在班学员", "已下单下级别课程学员", "已付款下级别课程学员"));
            $excelfileds = array("school_cnname", "school_branch", "class_cnname", "class_enname", 'class_branch', 'class_stdate', 'class_enddate', 'class_status', 'student_now', 'student_num', 'order_num', 'pay_num');
            $tem_name = $this->LgStringSwitch('预制升班统计表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无预制升班数据";
            return false;
        }
        foreach ($list as &$one) {
            if (!$one['course_nextid'] || $one['course_nextid'] == '') {
                $one['course_nextid'] = '0';
            }
            $sql = "select count(ta.student_id) as order_num
                ,count(if(ta.order_status=4 and buy_price>=order_price,true,null)) as pay_num
                from (
                select x.student_id,min(z.order_status) as order_status,min(w.coursebalance_figure) as buy_price,min(pricinglog_buyprice) as order_price
                from smc_student_study x
                left join smc_student_coursebalance_pricinglog y on x.student_id=y.student_id and y.school_id='{$one['school_id']}'
                left join smc_payfee_order z on y.order_pid=z.order_pid
                left join smc_student_coursebalance w on w.school_id=y.school_id and w.student_id=y.student_id and w.course_id=y.course_id
                where 1
                and x.class_id='{$one['class_id']}'
                and z.order_status>=0
                and x.study_isreading=1
                and y.course_id in({$one['course_nextid']})
                group by x.student_id
                ) as ta
                where 1  ";

            $orderNums = $this->DataControl->selectOne($sql);
            if ($orderNums) {
                $one['order_num'] = $orderNums['order_num'];
                $one['pay_num'] = $orderNums['pay_num'];
            } else {
                $one['order_num'] = 0;
                $one['pay_num'] = 0;
            }

            $one['class_status'] = $class_status[$one['class_status']];
        }
        $data = array();
        $count_sql = "SELECT count(a.class_id) as enums
        FROM smc_class a
        LEFT JOIN smc_school b on a.school_id=b.school_id
        left join smc_course c on a.course_id=c.course_id
        where {$datawhere}
        and a.class_status=1
        and c.course_isrenew=1
        and a.class_isnotrenew=0
        and a.class_type=0";
        $dbNums = $this->DataControl->selectOne($count_sql);
        if ($dbNums) {
            $allnum = $dbNums['enums'];
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $list;

        return $data;
    }

    //班级考勤统计表
    function classMachineAbout($request)
    {
        $datawhere = " c.company_id='{$this->company_id}' and c.class_status <> -2 and h.school_id = '{$request['school_id']}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%') ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and u.coursetype_id='{$request['coursetype_id']}' ";
        }
        if (isset($request['start_time']) && $request['start_time'] !== '' && isset($request['end_time']) && $request['end_time'] !== '') {
            $startday = $request['start_time'];
            $endday = $request['end_time'];
            $datawhere .= " and ((c.class_stdate <= '{$request['start_time']}' and c.class_enddate >= '{$request['start_time']}') or (c.class_stdate <= '{$request['end_time']}' and c.class_enddate >= '{$request['end_time']}')) ";
        }else{
            if (isset($request['start_time']) && $request['start_time'] !== '') {
                $startday = $request['start_time'];
                $datawhere .= " and c.class_stdate <= '{$request['start_time']}' and c.class_enddate >= '{$request['start_time']}' ";
            }else{
                $startday = date("Y-m-d");
            }
            if (isset($request['end_time']) && $request['end_time'] !== '') {
                $endday = $request['start_time'];
                $datawhere .= " and c.class_stdate <= '{$request['end_time']}' and c.class_enddate >= '{$request['end_time']}' ";
            }else{
                $endday = date("Y-m-d");
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " select h.school_branch,h.school_cnname,
            c.class_id,c.class_cnname,c.class_enname,c.class_branch,c.class_fullnums
             -- 应出勤最大人数  ID
            ,(  
                select GROUP_CONCAT(DISTINCT(ss.student_id))
                from smc_class_hour as ch
                left join smc_class as cc on cc.class_id=ch.class_id
                left join smc_student_study as ss on ss.class_id=ch.class_id
                where ss.school_id=h.school_id 
                and ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' 
                and cc.class_id = c.class_id
                and ((ss.study_endday>='{$startday}' and ss.study_beginday<='{$startday}') 
                or (ss.study_endday>='{$endday}' and ss.study_beginday<='{$endday}' )) 
                and ch.hour_iswarming='0' and cc.class_type=0 
            ) as MaxStuIdStr
            from smc_class as c  
            left join smc_school as h on c.school_id = h.school_id
            left join smc_course AS u on c.course_id = u.course_id 
            where  {$datawhere} 
            having MaxStuIdStr is not null 
            order by class_createtime DESC ";
//        echo $sql;die;
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无班级信息";
                return false;
            }

            foreach ($dateexcelarray as &$one) {
                $MaxStuIdArray = array();
                if($one['MaxStuIdStr']){
                    $MaxStuIdArray = explode(',',$one['MaxStuIdStr']);
                    $one['MaxStuNum'] = $MaxStuNum = count($MaxStuIdArray);

                    //人脸采集人数最大人数
                    $MaxStuportrait = $this->DataControl->selectOne(" select count(DISTINCT(student_id)) as num from gmc_machine_stuportrait where student_id in ({$one['MaxStuIdStr']})  ");
                    $one['MaxStuportraitNum'] = $MaxStuportrait['num']?$MaxStuportrait['num']:'--';

                }else{
                    $one['MaxStuNum'] = '--';
                    $one['MaxStuportraitNum'] = '--';
                }

                $one['DiffStucardNum_name'] = '--';
                //实际考勤人数
                $RealityStu = $this->DataControl->selectClear("select sh.student_id,
                    IF((select 1 from gmc_machine_stucardlog as g where g.school_id = c.school_id and FROM_UNIXTIME(g.cardlog_clocktime,'%Y-%m-%d') = ch.hour_day and g.student_id = sh.student_id limit 0,1),1,0) as stucardlog 
                    from smc_student_hourstudy as sh
                    left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                    left join smc_class as c on c.class_id=ch.class_id
                    where ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' 
                    and c.school_id='{$request['school_id']}' 
                    and ch.class_id = '{$one['class_id']}'
                    and sh.hourstudy_checkin='1'  and ch.hour_iswarming='0'  and c.class_type=0 
                    group by ch.hour_day,sh.student_id ");
                if($RealityStu){
                    //实际考勤人数
                    $one['RealityStuNum'] = count($RealityStu);
                    //扫脸出勤人数
                    $stucardlogArray = array_column($RealityStu, 'stucardlog');
                    $one['RealitystucardlogNum'] = array_sum($stucardlogArray);
                    //差异人数
                    $one['DiffStucardNum'] = $one['RealityStuNum'] - $one['RealitystucardlogNum'];
                    //差异人明细
                    if($one['DiffStucardNum'] > 0){
                        $logstudata = array();
                        foreach ($RealityStu as $RealityStuVar){
                            if($RealityStuVar['stucardlog'] == '0'){
                                $logstudata[] = $RealityStuVar['student_id'];
                            }
                        }
                        if($logstudata){
                            $logstustr = implode(',',$logstudata);
                            $stunamedata = $this->DataControl->selectOne(" select GROUP_CONCAT(student_cnname) as stunamestr from smc_student where student_id in ($logstustr) limit 0,1 ");
                            $one['DiffStucardNum_name'] = $stunamedata['stunamestr'];
                        }
                    }
                }else{
                    //实际考勤人数
                    $one['RealityStuNum'] = '--';
                    //扫脸出勤人数
                    $one['RealitystucardlogNum'] = '--';
                    //差异人数
                    $one['DiffStucardNum'] = '--';
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['MaxStuNum'] = $dateexcelvar['MaxStuNum'];
                    $datearray['MaxStuportraitNum'] = $dateexcelvar['MaxStuportraitNum'];
                    $datearray['RealityStuNum'] = $dateexcelvar['RealityStuNum'];
                    $datearray['RealitystucardlogNum'] = $dateexcelvar['RealitystucardlogNum'];
                    $datearray['DiffStucardNum'] = $dateexcelvar['DiffStucardNum'];
                    $datearray['DiffStucardNum_name'] = $dateexcelvar['DiffStucardNum_name'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区编号名称", "班级编号", "班级名称", "班级别名","应考勤人数", "人脸采集人数", "实际考勤人数", "扫脸出勤人数", "差异人数", "差异明细" ));
            $excelfileds = array('school_branch','school_cnname','class_branch', 'class_cnname', 'class_enname','MaxStuNum','MaxStuportraitNum','RealityStuNum', 'RealitystucardlogNum', 'DiffStucardNum', 'DiffStucardNum_name' );
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '班级考勤统计表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "暂无班级信息数据";
                return false;
            }
            foreach ($list as &$one) {
                $MaxStuIdArray = array();
                if($one['MaxStuIdStr']){
                    $MaxStuIdArray = explode(',',$one['MaxStuIdStr']);
                    $one['MaxStuNum'] = $MaxStuNum = count($MaxStuIdArray);

                    //人脸采集人数最大人数
                    $MaxStuportrait = $this->DataControl->selectOne(" select count(DISTINCT(student_id)) as num from gmc_machine_stuportrait where student_id in ({$one['MaxStuIdStr']})  ");
                    $one['MaxStuportraitNum'] = $MaxStuportrait['num']?$MaxStuportrait['num']:'--';

                }else{
                    $one['MaxStuNum'] = '--';
                    $one['MaxStuportraitNum'] = '--';
                }

                $one['DiffStucardNum_name'] = '--';
                //实际考勤人数
                $RealityStu = $this->DataControl->selectClear("select sh.student_id,
                    IF((select 1 from gmc_machine_stucardlog as g where g.school_id = c.school_id and FROM_UNIXTIME(g.cardlog_clocktime,'%Y-%m-%d') = ch.hour_day and g.student_id = sh.student_id limit 0,1),1,0) as stucardlog 
                    from smc_student_hourstudy as sh
                    left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                    left join smc_class as c on c.class_id=ch.class_id
                    where ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' 
                    and c.school_id='{$request['school_id']}' 
                    and ch.class_id = '{$one['class_id']}'
                    and sh.hourstudy_checkin='1'  and ch.hour_iswarming='0'  and c.class_type=0 
                    group by ch.hour_day,sh.student_id ");
                if($RealityStu){
                    //实际考勤人数
                    $one['RealityStuNum'] = count($RealityStu);
                    //扫脸出勤人数
                    $stucardlogArray = array_column($RealityStu, 'stucardlog');
                    $one['RealitystucardlogNum'] = array_sum($stucardlogArray);
                    //差异人数
                    $one['DiffStucardNum'] = $one['RealityStuNum'] - $one['RealitystucardlogNum'];

                    //差异人明细
                    if($one['DiffStucardNum'] > 0){
                        $logstudata = array();
                        foreach ($RealityStu as $RealityStuVar){
                            if($RealityStuVar['stucardlog'] == '0'){
                                $logstudata[] = $RealityStuVar['student_id'];
                            }
                        }
                        if($logstudata){
                            $logstustr = implode(',',$logstudata);
                            $stunamedata = $this->DataControl->selectOne(" select GROUP_CONCAT(student_cnname) as stunamestr from smc_student where student_id in ($logstustr) limit 0,1 ");
                            $one['DiffStucardNum_name'] = $stunamedata['stunamestr'];
                        }
                    }
                }else{
                    //实际考勤人数
                    $one['RealityStuNum'] = '--';
                    //扫脸出勤人数
                    $one['RealitystucardlogNum'] = '--';
                    //差异人数
                    $one['DiffStucardNum'] = '--';
                }
            }

            $count_sql = "select c.class_id
                     -- 应出勤最大人数  ID
                    ,(  
                        select GROUP_CONCAT(DISTINCT(ss.student_id))
                        from smc_class_hour as ch
                        left join smc_class as cc on cc.class_id=ch.class_id
                        left join smc_student_study as ss on ss.class_id=ch.class_id
                        where ss.school_id=h.school_id 
                        and ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' 
                        and cc.class_id = c.class_id
                        and ((ss.study_endday>='{$startday}' and ss.study_beginday<='{$startday}') 
                        or (ss.study_endday>='{$endday}' and ss.study_beginday<='{$endday}' )) 
                        and ch.hour_iswarming='0' and cc.class_type=0 
                    ) as MaxStuIdStr
                from smc_class as c  
                left join smc_school as h on c.school_id = h.school_id
                left join smc_course AS u on c.course_id = u.course_id 
                where  {$datawhere}  
                having MaxStuIdStr is not null ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }

            $data = array();
            $data['allnum'] = $allnum;
            $data['list'] = $list;
            return $data;
        }
    }

}