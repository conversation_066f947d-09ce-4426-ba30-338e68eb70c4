<?php


namespace Model\Report\Smc;

class FinanceReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->staffer_id = $publicarray['staffer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //校园收入统计报表
    function schoolIncome($request)
    {

        $datawhere = " 1 ";

        $datawhere .= " and B.company_id='{$request['company_id']}'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " AND B.school_istest <> '1' AND B.school_isclose<> '1' ";
        }

        if (isset($request['income_type']) && $request['income_type'] !== '') {
            $datawhere .= " and A.income_type='{$request['income_type']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
        } else {
            $endtime = strtotime(date("Y-m-d") . ' 23:59:59');
        }
        $datawhere .= " AND A.income_confirmtime <= '{$endtime}'";

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . '0:00:00');
        } else {
            $starttime = strtotime(date('Y-m-01', strtotime(date("Y-m-d"))) . '0:00:00');
        }
        $datawhere .= " AND  A.income_confirmtime >= '{$starttime}'";


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.school_id,B.school_branch,B.school_cnname,B.school_shortname
        ,DATE_FORMAT(FROM_UNIXTIME(A.income_confirmtime),'%Y-%m') AS income_month
        ,A.income_type
        ,count(A.income_id) AS income_total_count
        ,count(distinct student_id) AS income_student_count
        ,sum(A.income_price) AS income_total_price
        FROM smc_school_income A 
        LEFT JOIN smc_school B ON A.school_id=B.school_id AND A.company_id=B.company_id
        WHERE {$datawhere}
		GROUP BY A.school_id,B.school_branch,B.school_cnname,B.school_shortname,A.income_type,income_month
		order BY B.school_branch,income_month,A.income_type
		";


        $income_type = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "收入红冲"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_shortname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                $total_income = 0;
                $student_count = 0;
                $income_count = 0;
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['income_month'] = $dateexcelvar['income_month'];
                    $datearray['income_type'] = $income_type[$dateexcelvar['income_type']];
                    $datearray['income_total_count'] = $dateexcelvar['income_total_count'];
                    $datearray['income_student_count'] = $dateexcelvar['income_student_count'];
                    $datearray['income_total_price'] = $dateexcelvar['income_total_price'];
                    $outexceldate[] = $datearray;
                    $income_count += $dateexcelvar['income_total_count'];
                    $student_count += $dateexcelvar['income_student_count'];
                    $total_income += $dateexcelvar['income_total_price'];
                }
                $datearray = array();
                $datearray['school_id'] = "合计";
                $datearray['school_branch'] = '';
                $datearray['school_cnname'] = '';
                $datearray['income_month'] = '';
                $datearray['income_type'] = '';
                $datearray['income_total_count'] = '';
                $datearray['income_student_count'] = '';
                $datearray['income_total_price'] = $total_income;
                $outexceldate[] = $datearray;
            }
            $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", "收入月份", "收入类型", "收入单数", "涉及学员人数", "收入总计"));
            $excelfileds = array('school_id', 'school_branch', 'school_cnname', "income_month", 'income_type', "income_total_count", "income_student_count", 'income_total_price');

            $tem_name = $schoolOne['school_shortname'] . '校园收入统计表' . $request['start_time'] . '~' . $request['end_time'] . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $IncomeList = $this->DataControl->selectClear($sql);

            if (!$IncomeList) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            foreach ($IncomeList as &$var) {
                $var['income_type'] = $income_type[$var['income_type']];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select A.school_id,A.income_type
        ,DATE_FORMAT(FROM_UNIXTIME(A.income_confirmtime),'%Y-%m') AS income_month
        FROM smc_school_income A 
        LEFT JOIN smc_school B ON A.school_id=B.school_id AND A.company_id=B.company_id
        WHERE {$datawhere}
		GROUP BY A.school_id,B.school_branch,B.school_cnname,A.income_type,income_month
		");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $IncomeList;

            return $data;
        }
    }

    //班级收入统计报表
    function classIncome($request)
    {
        $datawhere = " 1 ";

        $datawhere .= " and A.company_id='{$request['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (C.class_cnname like '%{$request['keyword']}%' 
            or C.class_enname like '%{$request['keyword']}%' 
            or C.class_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['income_type']) && $request['income_type'] !== '') {
            $datawhere .= " and A.income_type='{$request['income_type']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = $request['end_time'];
        } else {
            $endtime = date("Y-m-d");
        }
        $datawhere .= " AND C.class_enddate <= '{$endtime}'";

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = $request['start_time'];
        } else {
            $starttime = date('Y-m-01', strtotime(date("Y-m-d")));
        }
        $datawhere .= " AND C.class_enddate>= '{$starttime}'";


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.school_id,B.school_branch,B.school_cnname,B.school_shortname
        ,D.course_branch,C.class_branch,C.class_cnname,C.class_enname
        ,A.income_type
        ,count(A.income_id) AS income_total_count
        ,count(distinct student_id) AS income_student_count
        ,sum(A.income_price) AS income_total_price
        FROM smc_school_income A 
        LEFT JOIN smc_school B ON A.school_id=B.school_id AND A.company_id=B.company_id
        LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id
        LEFT JOIN smc_course D ON C.course_id=D.course_id AND A.company_id=D.company_id
        WHERE {$datawhere}
        GROUP BY A.school_id,B.school_branch,B.school_cnname,D.course_branch,C.class_branch,C.class_cnname,C.class_enname,A.income_type
        ORDER BY B.school_branch,C.class_enddate,A.income_type
		";


        $income_type = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "收入红冲"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_shortname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                $total_income = 0;
                $student_count = 0;
                $income_count = 0;
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['income_type'] = $income_type[$dateexcelvar['income_type']];
                    $datearray['income_total_count'] = $dateexcelvar['income_total_count'];
                    $datearray['income_student_count'] = $dateexcelvar['income_student_count'];
                    $datearray['income_total_price'] = $dateexcelvar['income_total_price'];
                    $outexceldate[] = $datearray;
                    $total_income += $dateexcelvar['income_total_price'];
                }
                $datearray = array();
                $datearray['school_branch'] = "合计";
                $datearray['school_cnname'] = '';
                $datearray['course_branch'] = '';
                $datearray['class_branch'] = '';
                $datearray['class_cnname'] = '';
                $datearray['class_enname'] = '';
                $datearray['income_type'] = '';
                $datearray['income_total_count'] = '';
                $datearray['income_student_count'] = '';
                $datearray['income_total_price'] = $total_income;
                $outexceldate[] = $datearray;
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "课程别", "班级编号", "班级名称", "班级别名", "收入类型", "收入单数", "涉及学员人数", "收入总计"));
            $excelfileds = array('school_branch', 'school_cnname', 'course_branch', 'class_branch', 'class_cnname', 'class_enname', 'income_type', "income_total_count", "income_student_count", 'income_total_price');

            $tem_name = $this->LgStringSwitch($schoolOne['school_shortname'] . '班级结束收入统计表' . $request['start_time'] . '~' . $request['end_time'] . '.xlsx');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $IncomeList = $this->DataControl->selectClear($sql);

            if (!$IncomeList) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            foreach ($IncomeList as &$var) {
                $var['income_type'] = $income_type[$var['income_type']];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select A.school_id,C.class_id,A.income_type
        FROM smc_school_income A 
        LEFT JOIN smc_school B ON A.school_id=B.school_id AND A.company_id=B.company_id
        LEFT JOIN smc_class C ON A.class_id=C.class_id AND A.company_id=C.company_id
        LEFT JOIN smc_course D ON C.course_id=D.course_id AND A.company_id=D.company_id
        WHERE {$datawhere}
        GROUP BY A.school_id,C.class_id,A.income_type
		");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $IncomeList;

            return $data;
        }
    }

    //续费预估
    function studCourseEstimateSummary($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' AND d.school_istest <> '1' AND d.school_isclose<> '1' AND e.course_classnum > 10";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id='{$request['school_id']}' ";
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
        } else {
            $datawhere .= " AND d.school_istest <> '1' AND d.school_isclose <> '1'";
        }

//        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
//            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
//            $datawhere .= " AND b.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
//        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.student_cnname like '%{$request['keyword']}%' 
            or c.student_enname like '%{$request['keyword']}%' 
            or c.student_idcard like '%{$request['keyword']}%' 
            or c.student_branch like '%{$request['keyword']}%' 
            or b.class_enname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and b.class_enddate<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and b.class_enddate>= '{$firstday}'";
//            $datawhere .= " and a.study_endday>= '{$firstday}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and e.course_id= '{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and e.coursecat_id= '{$request['coursecat_id']}'";
        }

        $mainTable = ' smc_student_study ';
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and e.coursetype_id= '{$request['coursetype_id']}'";
            $datawhere .= " and a.study_endday>=b.class_enddate";
        } else {
            $datawhere .= " and a.study_endday>=b.class_enddate";
        }


        if (isset($request['course_isrenew']) && $request['course_isrenew'] !== '') {
            $datawhere .= " and e.course_isrenew= '{$request['course_isrenew']}'";
            $datawhere .= " and b.class_isnotrenew <>'{$request['course_isrenew']}'";
        }

        if (isset($request['course_issesson']) && $request['course_issesson'] !== '') {
            $datawhere .= " and e.course_issesson= '{$request['course_issesson']}'";
        }

        $havingwhere = " having 1 ";//and classnum = course_classnum
        if (isset($request['is_renewal']) && $request['is_renewal'] !== '') {
            if ($request['is_renewal'] == "1") {
//                $havingwhere .= " and ((renewal_amount-unpaid_price+spend_price)>=2500 or ifnull(connect_times,'')='续费') ";//旧规则
//                $havingwhere .= " and ((renewal_times+spend_times-unpaid_times)>=coursetype_renewtimes or ifnull(connect_times,'')='续费') ";
                $havingwhere .= " and ((renewal_rates+spend_rates-unpaid_rates)>=coursetype_renewtimes or ifnull(connect_times,'')='续费') ";
            } else {
//                $havingwhere .= " and ((renewal_amount-unpaid_price+spend_price)<2500 and ifnull(connect_times,'')<>'续费')";//旧规则
//                $havingwhere .= " and ((renewal_times+spend_times-unpaid_times)<coursetype_renewtimes and ifnull(connect_times,'')<>'续费')";
                $havingwhere .= " and ((renewal_rates+spend_rates-unpaid_rates) < coursetype_renewtimes and ifnull(connect_times,'') <> '续费')";
            }
        }

        if (isset($request['is_standard']) && $request['is_standard'] !== '') {
            if ($request['is_standard'] == "1") {
                $havingwhere .= " and class_num = course_class_num ";
            } else {
                $havingwhere .= " and class_num <> course_class_num ";
            }
        }
        if (isset($request['is_containsbreakoff']) && $request['is_containsbreakoff'] !== '') {
            if ($request['is_containsbreakoff'] == '0') {
                $datawhere .= " and not exists(select 1 from smc_class_breakoff where class_id=b.class_id and breakoff_status>=2 and breakoff_type=0) ";
                $is_containsbreakoff = 0;
            } else {
                $is_containsbreakoff = 1;
            }
        } else {
            $is_containsbreakoff = 1;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select d.school_id,d.school_branch,d.school_cnname,
                c.student_id,c.student_branch,c.student_cnname,c.student_enname,j.channel_name,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id AND st.student_id = c.student_id   and st.school_id=d.school_id and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0
                AND st.track_classname = '续费电访' AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id= st.result_id AND st.student_id = c.student_id   and st.school_id=d.school_id and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0
                AND st.track_classname = '主管电访' AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_main,
                ifnull((SELECT st.track_note FROM smc_student_track AS st WHERE st.student_id = c.student_id   and st.school_id=d.school_id and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0
                AND st.track_classname = '续费电访' AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS track_note,
                b.class_id,b.class_branch,b.class_cnname,b.class_enname,b.class_enddate,
                (select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=0) as main_teacher,
                e.course_branch,e.course_cnname,f.coursecat_id,f.coursecat_branch,f.coursecat_cnname,g.coursetype_id,g.coursetype_branch,g.coursetype_cnname
                ,100 as coursetype_renewtimes
                ,ifnull((select sum(X.coursebalance_figure) from smc_student_coursebalance X,smc_course Z 
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id and z.course_isfollow=0
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_amount
                ,ifnull((select sum(coursebalance_time) from smc_student_coursebalance X,smc_course Z 
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id and z.course_isfollow=0
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_times 
                ,ifnull((select sum(coursebalance_time*z.course_classtimerates) from smc_student_coursebalance X,smc_course Z 
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id and z.course_isfollow=0
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_rates 
                
                ,ifnull((select sum(income_price) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.company_id=a.company_id and x.student_id=a.student_id and Y.course_isfollow=0
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_price 
                ,ifnull((select count(x.income_id) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.student_id=a.student_id and x.company_id=a.company_id and Y.course_isfollow=0
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_times 
                ,ifnull((select sum(y.course_classtimerates) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.student_id=a.student_id and x.company_id=a.company_id and Y.course_isfollow=0
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_rates 
                
                ,ifnull((select sum(all_price-pay_price) from smc_student_unpaid where school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_price
                ,ifnull((select sum(unpaid_times) from smc_student_unpaid where school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_times
                ,ifnull((select sum(unpaid_timesrate) from smc_student_unpaid where school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_rates
                ,(SELECT count(1) FROM smc_class_hour WHERE class_id = b.class_id AND hour_ischecking >= 0 AND hour_iswarming = 0) as class_num
                ,e.course_classnum as course_class_num,k.coursebalance_figure,k.coursebalance_time
                ,(select count(1) from smc_student_free_coursetimes x where x.class_id=a.class_id and x.student_id=a.student_id and x.is_use=0) AS left_free_times
                from 
                {$mainTable} a
                left join smc_class b on a.class_id=b.class_id 
                left join smc_student c on a.student_id=c.student_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_course e on b.course_id=e.course_id and a.company_id=e.company_id 
                LEFT JOIN smc_code_coursecat f ON e.coursecat_id = f.coursecat_id  AND a.company_id = f.company_id 
                LEFT JOIN smc_code_coursetype g ON f.coursetype_id = g.coursetype_id  AND a.company_id = g.company_id 
                left join smc_student_guildpolicy j on c.student_id=j.student_id and j.guildpolicy_enddate>=b.class_enddate
                left join smc_student_coursebalance k on k.school_id=a.school_id and k.student_id=a.student_id and k.course_id=b.course_id
                where b.class_status<>'-2'  and b.class_type='0' and e.course_sellclass=0
                and {$datawhere} 
                {$havingwhere} 
                order by d.school_id,g.coursetype_id,f.coursecat_id,b.class_id 
              ";
//        var_dump($sql);die;

//        ,ifnull((select sum(order_arrearageprice) from smc_payfee_order x where x.order_status>0 and x.company_id=a.company_id and x.student_id=a.student_id
//    and exists(select 1 from smc_payfee_order_course y,smc_course z where y.course_id=z.course_id and y.order_pid=x.order_pid and z.company_id=x.company_id
//    and z.coursetype_id=e.coursetype_id) ),0) as unpaid_price
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            $mainData = array();
            if ($dateexcelarray) {
                $mainData = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['class_id'] = $dateexcelvar['class_id'];
                    $datearray['coursecat_id'] = $dateexcelvar['coursecat_id'];
                    $datearray['coursetype_id'] = $dateexcelvar['coursetype_id'];

                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];

//                    $datearray['is_renewal'] = (($dateexcelvar['renewal_times'] + $dateexcelvar['spend_times'] - $dateexcelvar['unpaid_times'] >= $dateexcelvar['coursetype_renewtimes']) || $dateexcelvar['connect_times'] == '续费') ? "Y" : "N";
//                    $datearray['is_renewal_real'] = ($dateexcelvar['renewal_times'] + $dateexcelvar['spend_times'] - $dateexcelvar['unpaid_times'] >= $dateexcelvar['coursetype_renewtimes']) ? "Y" : "N";

                    $datearray['is_renewal'] = (($dateexcelvar['renewal_rates'] + $dateexcelvar['spend_rates'] - $dateexcelvar['unpaid_rates'] >= $dateexcelvar['coursetype_renewtimes']) || $dateexcelvar['connect_times'] == '续费') ? "Y" : "N";
                    $datearray['is_renewal_real'] = ($dateexcelvar['renewal_rates'] + $dateexcelvar['spend_rates'] - $dateexcelvar['unpaid_rates'] >= $dateexcelvar['coursetype_renewtimes']) ? "Y" : "N";

                    $datearray['renewal_times'] = $dateexcelvar['renewal_times'] + $dateexcelvar['spend_times'] - $dateexcelvar['unpaid_times'];
                    $datearray['renewal_amount'] = $dateexcelvar['renewal_amount'];
                    $datearray['unpaid_price'] = $dateexcelvar['unpaid_price'];
                    $datearray['spend_price'] = $dateexcelvar['spend_price'];
                    $datearray['estimate_price'] = ($dateexcelvar['renewal_amount'] ? $dateexcelvar['renewal_amount'] : 0) - ($dateexcelvar['unpaid_price'] ? $dateexcelvar['unpaid_price'] : 0) + ($dateexcelvar['spend_price'] ? $dateexcelvar['spend_price'] : 0);

                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['left_free_times'] = $dateexcelvar['left_free_times'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];

                    $datearray['connect_times'] = trim($dateexcelvar['connect_times']) == '' ? '---' : $dateexcelvar['connect_times'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_enddate'] = $dateexcelvar['class_enddate'];
                    $datearray['main_teacher'] = $dateexcelvar['main_teacher'];

                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];

                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['course_class_num'] = $dateexcelvar['course_class_num'];

                    $mainData[] = $datearray;
                }
            }
            if (isset($request['school_id']) && $request['school_id'] !== '' && !is_array($request['school_id'])) {
                $excelName = $this->LgStringSwitch("{$schoolOne['school_cnname']}学员续费预估统计表");
            } else {
                $excelName = $this->LgStringSwitch("学员续费预估统计表");
            }

            $monthrange = array();
            $end = date('Ym', strtotime($lastday)); // 转换为月
            $i = 0;
            do {
                $month = date('Ym', strtotime($firstday . ' + ' . $i . ' month'));
                $monthrange[] = $month;
                $i++;
            } while ($month < $end);

            $exceldetailheader = $this->LgArraySwitch(array("校区编号", "校区名称", "学生编号", "学员中文名", '学员英文名', "是否续费", "续费评估课次", "班组剩余金额", "班组欠费金额", "后续耗课金额", "续费评估金额", "剩余课次", "剩余课时金额", "剩余免费课次", "专案名称", "电访结果", "沟通内容", "班级编号", "班级名称", "班级别名", "班级结束日期", "主教教师", "课程别编号", "课程别名称", "班种编号", "班种名称", "班组编号", "班组名称", "班级课次", "课程别设定课次"));
            $excelclassheader = $this->LgArraySwitch(array("校区编号", "校区名称", "班级编号", "班种编号", "课程别编号", "班级别名", "班级结束日期", "结班月份", "主教教师", "总人数", "公益人数", "总续费人数", '公益续费人数', '人数', '实际续费', '预估续费', '实际续费率', '预估续费率'));

            $excelschoolheader = [];
            $schoolname = $this->LgStringSwitch("校区编号");
            $excelschoolheader[] = $schoolname;
            $schoolname = $this->LgStringSwitch("校区名称");
            $excelschoolheader[] = $schoolname;

            $fileName = $excelName;
            $fileType = ".xlsx";

            excelcustom_studCourseEstimate($is_containsbreakoff, $exceldetailheader, $excelclassheader, $excelschoolheader, $mainData, $monthrange, $fileName, $fileType);

            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['connect_main'] = trim($var['connect_main']) == '' ? '---' : $var['connect_main'];
                $var['connect_times'] = trim($var['connect_times']) == '' ? '---' : $var['connect_times'];
                $var['estimate_price'] = $var['renewal_amount'] - $var['unpaid_price'] + $var['spend_price'];
                $var['renewal_times'] = $var['renewal_times'] + $var['spend_times'] - $var['unpaid_times'];
                $var['renewal_rates'] = $var['renewal_rates'] + $var['spend_rates'] - $var['unpaid_rates'];
//                $var['is_renewal'] = $this->LgArraySwitch(($var['renewal_times'] >= $var['coursetype_renewtimes'] || $var['connect_times'] == '续费') ? "是" : "否");
                $var['is_renewal'] = $this->LgArraySwitch(($var['renewal_rates'] >= $var['coursetype_renewtimes'] || $var['connect_times'] == '续费') ? "是" : "否");
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select d.school_id,
                c.student_id,
                b.class_id,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id AND st.student_id = c.student_id and st.school_id=d.school_id and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0 AND st.track_classname = '续费电访'
                AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times
                ,(SELECT count(1) FROM smc_class_hour WHERE class_id = b.class_id AND hour_ischecking >= 0 AND hour_iswarming = 0) as class_num
                ,e.course_classnum as course_class_num
                ,100 as coursetype_renewtimes
                ,ifnull((select sum(X.coursebalance_figure) from smc_student_coursebalance X,smc_course Z 
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id  and Z.course_isfollow=0
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_amount
                ,ifnull((select sum(coursebalance_time) from smc_student_coursebalance X,smc_course Z 
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id  and Z.course_isfollow=0
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_times 
                ,ifnull((select sum(coursebalance_time*z.course_classtimerates) from smc_student_coursebalance X,smc_course Z 
                where X.company_id=a.company_id AND X.student_id=a.student_id AND X.course_id=Z.course_id  and Z.course_isfollow=0
                AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_rates 
                
                ,ifnull((select sum(income_price) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.company_id=a.company_id and x.student_id=a.student_id and Y.course_isfollow=0
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_price 
                ,ifnull((select count(x.income_id) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.student_id=a.student_id and x.company_id=a.company_id  and Y.course_isfollow=0
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_times 
                ,ifnull((select sum(y.course_classtimerates) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.student_id=a.student_id and x.company_id=a.company_id  and Y.course_isfollow=0
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_rates 
                
                ,ifnull((select sum(all_price-pay_price) from smc_student_unpaid where school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_price
                ,ifnull((select sum(unpaid_times) from smc_student_unpaid where school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_times
                ,ifnull((select sum(unpaid_timesrate) from smc_student_unpaid where school_id=a.school_id and student_id=a.student_id and coursetype_id=e.coursetype_id),0) as unpaid_rates
                from 
                {$mainTable} a
                left join smc_class b on a.class_id=b.class_id
                left join smc_student c on a.student_id=c.student_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_course e on b.course_id=e.course_id and a.company_id=e.company_id 
                LEFT JOIN smc_code_coursecat f ON e.coursecat_id = f.coursecat_id  AND a.company_id = f.company_id 
                LEFT JOIN smc_code_coursetype g ON f.coursetype_id = g.coursetype_id  AND a.company_id = g.company_id 
                where b.class_status<>'-2'   and b.class_type='0' and e.course_sellclass=0
                and {$datawhere} 
                {$havingwhere} 
              ";

                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $ClassCalc;
            return $data;
        }
    }

    //缴费明细
    function stuPayReport($request)
    {
        $datawhere = "B.company_id='{$request['company_id']}' and A.pay_issuccess='1' and B.order_status > '-2'and A.pay_price>0
                      and not EXISTS (select 1 from smc_refund_order where from_order_pid=a.order_pid and refund_status='4')";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (C.student_cnname like '%{$request['keyword']}%' 
            or C.student_enname like '%{$request['keyword']}%' 
            or C.student_idcard like '%{$request['keyword']}%' 
            or C.student_branch like '%{$request['keyword']}%' 
            or B.order_pid like '%{$request['keyword']}%' 
            or B.trading_pid like '%{$request['keyword']}%' 
            OR A.pay_pid like '%{$request['keyword']}%')";
        }

        $datawhere .= " and B.order_status>'-2'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and B.school_id='{$request['school_id']}' ";
        }

//        if (isset($request['end_time']) && $request['end_time'] !== '') {
//            $endtime = strtotime($request['end_time'] . ' 23:59:59');
//            $datawhere .= " and A.pay_successtime <= '{$endtime}'";
//        }
//
//        if (isset($request['start_time']) && $request['start_time'] !== '') {
//            $starttime = strtotime($request['start_time']);
//            $datawhere .= " and A.pay_successtime >= '{$starttime}'";
//        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and B.order_createtime <= '{$endtime}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time']);
            $datawhere .= " and B.order_createtime >= '{$starttime}'";
        }

        if (isset($request['pay_endtime']) && $request['pay_endtime'] !== '') {
            $endtime = strtotime($request['pay_endtime'] . ' 23:59:59');
            $datawhere .= " and A.pay_successtime <= '{$endtime}'";
        }

        if (isset($request['pay_starttime']) && $request['pay_starttime'] !== '') {
            $starttime = strtotime($request['pay_starttime']);
            $datawhere .= " and A.pay_successtime >= '{$starttime}'";
        }

        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
            $datawhere .= " and A.pay_type= '{$request['pay_type']}'";
        }

//        if (isset($request['paytype_code']) && $request['paytype_code'] !== '') {
//            $datawhere .= " and A.paytype_code= '{$request['paytype_code']}'";
//        }

        if (isset($request['paytype_code']) && $request['paytype_code'] !== '' && $request['paytype_code'] != '[]') {
            $paytypeArray = json_decode(stripslashes($request['paytype_code']), 1);
            if (is_array($paytypeArray) && count($paytypeArray) > 0) {
                $paytypestr = '';
                foreach ($paytypeArray as $paytypevar) {
                    $paytypestr .= "'" . $paytypevar . "'" . ',';
                }
                $paytypestr = substr($paytypestr, 0, -1);
                $datawhere .= " and A.paytype_code in ({$paytypestr}) ";
            }
        }

        if (isset($request['paychannel_code']) && $request['paychannel_code'] !== '') {
            $datawhere .= " and A.paychannel_code= '{$request['paychannel_code']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and B.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and A.companies_id= '{$request['companies_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.order_pid,A.pay_id,A.pay_pid,
                (select companies_cnname from gmc_code_companies where companies_id=A.companies_id)as companies_cnname,
                C.student_branch,C.student_cnname,C.student_enname,
                B.order_from,B.order_type,B.order_createtime,B.order_status,B.order_paymentprice,
                A.pay_price,A.pay_successtime,A.pay_type,E.paychannel_name,A.pay_typename,A.pay_issuccess,A.pay_outnumber,
                D.paytype_name,D.paytype_ischarge,
                (SELECT GROUP_CONCAT(H.coupons_name) FROM smc_student_coupons AS H WHERE H.order_pid = B.order_pid AND H.student_id = B.student_id)  AS coupons_name,
                B.order_coupon_price as ordercoupons_price,
                B.trading_pid,
                (select group_concat(distinct z.coursecat_branch) from smc_payfee_order_course x,smc_course y,smc_code_coursecat z where x.course_id=y.course_id and y.coursecat_id=z.coursecat_id and x.order_pid=b.order_pid) as coursecat_branch,
                A.pay_note,
                B.order_note
                from smc_payfee_order_pay A 
                left join smc_payfee_order B on A.order_pid=b.order_pid 
                left join smc_student C on B.student_id=C.student_id and B.company_id=c.company_id
                left join smc_code_paytype D on A.paytype_code=D.paytype_code 
                left join smc_code_paychannel E on A.paychannel_code=E.paychannel_code 
                LEFT JOIN smc_code_coursecat J ON J.coursecat_id = B.coursecat_id
                where {$datawhere}
                group by A.pay_id 
                order by C.student_branch, A.order_pid,A.pay_successtime";

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统导入"));
        $issuccess = $this->LgArraySwitch(array("0" => "未支付", "1" => "已支付", "-1" => "已失效"));
        $order_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
        $paytype_ischarge = $this->LgArraySwitch(array("0" => "抵扣类型", "1" => "现金类型"));

        $pay_type = $this->LgArraySwitch(array("0" => "课程收费", "1" => "教材收费", "2" => "杂费收费", "3" => "账户充值"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($dateexcelarray as &$val) {
                $val['order_status'] = $order_status[$val['order_status']];
                $val['order_from'] = $order_from[$val['order_from']];
                $val['order_type'] = $order_type[$val['order_type']];
                if ($val['pay_type'] != '0') {
                    $val['coursecat_branch'] = '--';
                }
                $val['pay_type'] = $pay_type[$val['pay_type']];
                $val['pay_issuccess'] = $issuccess[$val['pay_issuccess']];
                $val['paytype_ischarge'] = $paytype_ischarge[$val['paytype_ischarge']];
                $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
                if ($val['pay_successtime']) {
                    $val['pay_successtime'] = date("Y-m-d H:i:s", $val['pay_successtime']);
                }
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['pay_pid'] = $dateexcelvar['pay_pid'];
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    $datearray['pay_issuccess'] = $dateexcelvar['pay_issuccess'];
                    $datearray['order_from'] = $dateexcelvar['order_from'];
                    $datearray['order_type'] = $dateexcelvar['order_type'];
                    $datearray['pay_type'] = $dateexcelvar['pay_type'];
                    $datearray['paychannel_name'] = $dateexcelvar['paychannel_name'] ? $dateexcelvar['paychannel_name'] : '--';
                    $datearray['pay_typename'] = $dateexcelvar['pay_typename'] ? $dateexcelvar['pay_typename'] : '--';
                    $datearray['pay_outnumber'] = $dateexcelvar['pay_outnumber'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];

//                    $datearray['paytype_name'] = $dateexcelvar['paytype_name'];
                    $datearray['paytype_ischarge'] = $dateexcelvar['paytype_ischarge'];
                    $datearray['coupons_name'] = $dateexcelvar['coupons_name'];
                    $datearray['ordercoupons_price'] = $dateexcelvar['ordercoupons_price'];

                    $datearray['order_createtime'] = $dateexcelvar['order_createtime'];
                    $datearray['pay_successtime'] = $dateexcelvar['pay_successtime'] ? $dateexcelvar['pay_successtime'] : '--';
                    $datearray['order_note'] = $dateexcelvar['order_note'];
                    $datearray['pay_note'] = $dateexcelvar['pay_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->AnalyzeControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("交易编号", "订单编号", "支付编号", "支付企业主体", "学员编号", "学员中文名", "学员英文名", "支付金额", "支付状态", "订单来源", "订单类型", "收费项目类别", "支付渠道", "支付方式", '关联外部单号', '收费班种', '收费类型', '优惠券名称', '优惠券金额', "下单日期", "支付成功日期", "订单备注", "支付备注"));
            $excelfileds = array('trading_pid', 'order_pid', 'pay_pid', 'companies_cnname', 'student_branch', 'student_cnname', 'student_enname', 'pay_price', 'pay_issuccess', 'order_from', 'order_type', 'pay_type', 'paychannel_name', 'pay_typename', 'pay_outnumber', 'coursecat_branch', 'paytype_ischarge', 'coupons_name', 'ordercoupons_price', 'order_createtime', 'pay_successtime', 'order_note', 'pay_note');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}收费日记账报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $payList = $this->DataControl->selectClear($sql);
            if (!$payList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($payList as &$val) {
                $val['order_status'] = $order_status[$val['order_status']];
                $val['order_from'] = $order_from[$val['order_from']];
                $val['order_type'] = $order_type[$val['order_type']];
                if ($val['pay_type'] != '0') {
                    $val['coursecat_branch'] = '--';
                }
                $val['pay_type'] = $pay_type[$val['pay_type']];
                $val['pay_issuccess'] = $issuccess[$val['pay_issuccess']];
                $val['paytype_ischarge'] = $paytype_ischarge[$val['paytype_ischarge']];
                $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
                if ($val['pay_successtime']) {
                    $val['pay_successtime'] = date("Y-m-d H:i:s", $val['pay_successtime']);
                } else {
                    $val['pay_successtime'] = '--';
                }
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.pay_id
                from smc_payfee_order_pay A 
                left join smc_payfee_order B on A.order_pid=b.order_pid 
                left join smc_student C on B.student_id=C.student_id and B.company_id=c.company_id
                left join smc_code_paytype D on A.paytype_code=D.paytype_code
                where {$datawhere} 
                order by C.student_branch, A.order_pid,A.pay_successtime";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

//            $sql = "select a.companies_id,b.companies_cnname
//            from smc_payfee_order a
//            left join gmc_code_companies b on a.companies_id=b.companies_id
//            WHERE 1
//            and a.company_id = '{$request['company_id']}'
//            and a.school_id='{$this->school_id}'
//            group by a.companies_id ";
//            $companiesList = $this->DataControl->selectClear($sql);
//            $data['companieslist'] = $companiesList ? $companiesList : array();

            $data['list'] = $payList;

            return $data;
        }
    }

    //学员实际欠费明细表
    function studentUnReceReport($request)
    {
        $datawhere = " TTA.company_id='{$request['company_id']}' ";//and TTA.school_id='{$request['school_id']}'
        $datainwhere = " B.company_id='{$request['company_id']}'  ";//and B.school_id='{$request['school_id']}'

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= "  and TTA.school_id='{$request['school_id']}' ";
            $datainwhere .= "  and B.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (TTB.student_cnname like '%{$request['keyword']}%' 
            or TTB.student_enname like '%{$request['keyword']}%' 
            or TTB.student_idcard like '%{$request['keyword']}%' 
            or TTB.student_branch like '%{$request['keyword']}%' 
            or TTC.class_branch like '%{$request['keyword']}%')";
        }
        $endqueryday = date("Y-m-d");
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " set @pdept:=''; set @unpaid_this:=0; set @unpaid_yet:=0;
            SELECT TTA.school_id,TTF.school_branch,TTF.school_cnname
            ,TTA.student_id,TTB.student_branch,TTB.student_cnname,TTB.student_enname,TTD.channel_name
            ,TTE.course_id,TTE.course_branch,TTC.class_branch,TTC.class_cnname,TTC.class_enname 
            ,TTA.ordercourse_totalprice,TTA.ordercourse_buynums 
            ,unpaid_this-left_money as unpaid_money,FLOOR((unpaid_this-left_money)/unit_price) as unpaid_times 
            FROM( 
            select company_id,school_id,trading_pid,order_pid,student_id,course_id,class_id,ordercourse_buynums,ordercourse_totalprice,unpaid_all 
            ,if(@pdept=TA.order_pid 
            ,@unpaid_this:=if(unpaid_all-@unpaid_yet>ordercourse_totalprice,ordercourse_totalprice,unpaid_all-@unpaid_yet) 
            ,@unpaid_this:=if(unpaid_all>ordercourse_totalprice,ordercourse_totalprice,unpaid_all) 
            ) as unpaid_this 
            ,if(@pdept=TA.order_pid 
            ,@unpaid_yet:=@unpaid_yet+@unpaid_this 
            ,@unpaid_yet:=if(unpaid_all>ordercourse_totalprice,ordercourse_totalprice,unpaid_all) 
            ) as unpaid_yet 
            ,@pdept:=TA.order_pid 
            ,left_money 
            ,unit_price 
            from ( 
            SELECT B.company_id,B.school_id,B.trading_pid,B.order_pid,B.student_id,A.course_id,A.ordercourse_buynums,A.ordercourse_totalprice,   
            IFNULL((SELECT study_beginday FROM view_smc_student_class WHERE company_id=B.company_id AND school_id=B.school_id AND student_id=B.student_id  
            AND course_id=A.course_id AND study_beginday<='{$endqueryday}' ORDER BY study_beginday LIMIT 0,1),'9999-12-31') as st_date, 
            B.order_arrearageprice+IFNULL((SELECT SUM(pay_price) FROM smc_payfee_order_pay  
            WHERE order_pid=B.order_pid AND pay_successtime>='{$endqueryTimes}' AND pay_issuccess='1'),0) AS unpaid_all 
            ,C.coursebalance_figure-IFNULL((SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursebalance_log  
            WHERE student_id=C.student_id and course_id=C.course_id and school_id=C.school_id AND log_time>='{$endqueryTimes}' and log_class='0'),0) AS left_money 
            ,(SELECT class_id FROM view_smc_student_class WHERE company_id=B.company_id AND school_id=B.school_id AND student_id=B.student_id  
            AND course_id=A.course_id AND study_beginday<='{$endqueryday}'  order by study_beginday desc LIMIT 0,1) AS class_id 
            ,if(coursebalance_unitexpend>coursebalance_unitearning and coursebalance_unitearning>0,coursebalance_unitearning,coursebalance_unitexpend) as unit_price 
            FROM smc_payfee_order_course A  
            LEFT JOIN smc_payfee_order B ON A.order_pid=B.order_pid  
            LEFT JOIN smc_student_coursebalance C ON C.student_id=B.student_id and C.company_id=B.company_id and C.school_id=B.school_id and C.course_id=A.course_id 
            WHERE {$datainwhere} 
            AND B.order_status>0
            AND B.order_status<4
            AND B.order_createtime<'{$endqueryTimes}'  
            AND (order_arrearageprice>0  
            OR EXISTS(SELECT 1 FROM smc_payfee_order_pay WHERE order_pid=B.order_pid AND pay_successtime>='{$endqueryTimes}' AND pay_issuccess='1'))  
            ORDER BY A.order_pid,st_date DESC  
            )TA,(select @rownum :=0 , @pdept := null ,@rank:=0) R  
            )TTA 
            LEFT JOIN smc_student TTB ON TTA.company_id=TTB.company_id AND TTA.student_id=TTB.student_id  
            LEFT JOIN smc_class TTC ON TTA.company_id=TTC.company_id AND TTA.class_id=TTC.class_id  
            LEFT JOIN smc_student_guildpolicy TTD ON TTD.student_id=TTB.student_id  
            LEFT JOIN smc_course TTE ON TTA.course_id=TTE.course_id  
            left join smc_school TTF on TTA.company_id=TTF.company_id AND TTA.SCHOOL_ID=TTF.SCHOOL_ID  
            WHERE {$datawhere} 
            AND unpaid_this-left_money>0         
            AND exists(SELECT 1 FROM view_smc_student_class WHERE company_id=TTA.company_id AND school_id=TTA.school_id AND student_id=TTA.student_id AND course_id=TTA.course_id)
            ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
//            $this->DataControl->multiquery(" set @pdept:=0; set @unpaid_this:=0; set @unpaid_yet:=0; ");
            $dateexcelarray = $this->DataControl->selectMultiClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员欠费数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['ordercourse_totalprice'] = $dateexcelvar['ordercourse_totalprice'];
                    $datearray['ordercourse_buynums'] = $dateexcelvar['ordercourse_buynums'];
                    $datearray['unpaid_money'] = $dateexcelvar['unpaid_money'];

                    $unpaid_day = '--';
                    $unpaid_times = 0;
                    $total_price = 0;
                    $sqlRowQuery = "select a.income_price,
                        FROM_UNIXTIME(a.income_confirmtime,'%Y-%m-%d') as income_date
                        from smc_school_income a
                        where a.company_id='{$request['company_id']}'
                        and a.school_id='{$dateexcelvar['school_id']}'
                        and a.course_id='{$dateexcelvar['course_id']}'
                        and a.student_id='{$dateexcelvar['student_id']}'
                        and a.income_type='0'
                        and a.income_confirmtime<'{$endqueryTimes}' 
                        order by a.income_confirmtime desc,a.hourstudy_id desc
                        ";
                    $rowQueryarray = $this->DataControl->selectClear($sqlRowQuery);

                    if ($rowQueryarray) {
                        foreach ($rowQueryarray as $row) {
                            $total_price += $row['income_price'];
                            if ($total_price >= $dateexcelvar['unpaid_money']) {
                                $unpaid_day = $row['income_date'];
                                if ($row['income_price'] > 0) {
                                    $unpaid_times++;
                                }
                                break;
                            } else {
                                $unpaid_day = $row['income_date'];
                                if ($row['income_price'] > 0) {
                                    $unpaid_times++;
                                }
                            }
                        }
                    }
                    $datearray['unpaid_times'] = $unpaid_times;
                    $datearray['unpaid_day'] = $unpaid_day;
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", "学生ID", "学员编号", "中文名", "英文名", "专案名称", "课程别", "班级编号", "班级名称", "班级别名", "购买金额", "购买课次", "欠费金额", "欠费课次", '欠费日期'));
            $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'student_id', 'student_branch', 'student_cnname', 'student_enname', 'channel_name', 'course_branch', 'class_branch', 'class_cnname', 'class_enname', 'ordercourse_totalprice', 'ordercourse_buynums', 'unpaid_money', 'unpaid_times', 'unpaid_day');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学生实际欠费报表{$endqueryday}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num . ';';
//            $this->DataControl->multiquery(" set @pdept:=''; set @unpaid_this:=0; set @unpaid_yet:=0; ");
            $studentList = $this->DataControl->selectMultiClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员实际欠费数据";
                return false;
            }
            foreach ($studentList as &$var) {
                $unpaid_day = '--';
                $unpaid_times = 0;
                $total_price = 0;
                $sqlRowQuery = "select a.income_price,
                        FROM_UNIXTIME(a.income_confirmtime,'%Y-%m-%d') as income_date
                        from smc_school_income a
                        where a.company_id='{$request['company_id']}'
                        and a.school_id='{$var['school_id']}'
                        and a.course_id='{$var['course_id']}'
                        and a.student_id='{$var['student_id']}'
                        and a.income_type='0'
                        and a.income_confirmtime<'{$endqueryTimes}' 
                        order by a.income_confirmtime desc,a.hourstudy_id desc
                        ";
                $rowQueryarray = $this->DataControl->selectClear($sqlRowQuery);

                if ($rowQueryarray) {
                    $unpaid_times = 0;
                    foreach ($rowQueryarray as $row) {
                        $total_price += $row['income_price'];

                        if ($total_price >= $var['unpaid_money']) {
                            $unpaid_day = $row['income_date'];
                            if ($row['income_price'] > 0) {
                                $unpaid_times++;
                            }
                            break;
                        } else {
                            $unpaid_day = $row['income_date'];
                            if ($row['income_price'] > 0) {
                                $unpaid_times++;
                            }
                        }
                    }
                }

                $var['unpaid_times'] = $unpaid_times;
                $var['unpaid_day'] = $unpaid_day;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectMultiClear("  set @pdept:=''; set @unpaid_this:=0; set @unpaid_yet:=0;
                SELECT TTA.student_id,TTB.student_branch,TTB.student_cnname,TTB.student_enname,TTD.channel_name
                ,TTE.course_branch,TTC.class_branch,TTC.class_cnname,TTC.class_enname 
                ,TTA.ordercourse_totalprice,TTA.ordercourse_buynums 
                ,unpaid_this-left_money as unpaid_money,FLOOR((unpaid_this-left_money)/unit_price) as unpaid_times 
                FROM( 
                select company_id,school_id,trading_pid,order_pid,student_id,course_id,class_id,ordercourse_buynums,ordercourse_totalprice,unpaid_all 
                ,if(@pdept=TA.order_pid 
                ,@unpaid_this:=if(unpaid_all-@unpaid_yet>ordercourse_totalprice,ordercourse_totalprice,unpaid_all-@unpaid_yet) 
                ,@unpaid_this:=if(unpaid_all>ordercourse_totalprice,ordercourse_totalprice,unpaid_all) 
                ) as unpaid_this 
                ,if(@pdept=TA.order_pid 
                ,@unpaid_yet:=@unpaid_yet+@unpaid_this 
                ,@unpaid_yet:=if(unpaid_all>ordercourse_totalprice,ordercourse_totalprice,unpaid_all) 
                ) as unpaid_yet 
                ,@pdept:=TA.order_pid 
                ,left_money 
                ,unit_price 
                from ( 
                SELECT B.company_id,B.school_id,B.trading_pid,B.order_pid,B.student_id,A.course_id,A.ordercourse_buynums,A.ordercourse_totalprice,   
                IFNULL((SELECT study_beginday FROM view_smc_student_class WHERE company_id=B.company_id AND school_id=B.school_id AND student_id=B.student_id  
                AND course_id=A.course_id AND study_beginday<='{$endqueryday}' ORDER BY study_beginday LIMIT 0,1),'9999-12-31') as st_date, 
                B.order_arrearageprice+IFNULL((SELECT SUM(pay_price) FROM smc_payfee_order_pay  
                WHERE order_pid=B.order_pid AND pay_successtime>='{$endqueryTimes}' AND pay_issuccess='1'),0) AS unpaid_all 
                ,C.coursebalance_figure-IFNULL((SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursebalance_log  
                WHERE student_id=C.student_id and course_id=C.course_id and school_id=C.school_id AND log_time>='{$endqueryTimes}' and log_class='0'),0) AS left_money 
                ,(SELECT class_id FROM view_smc_student_class WHERE company_id=B.company_id AND school_id=B.school_id AND student_id=B.student_id  
                AND course_id=A.course_id AND study_beginday<='{$endqueryday}'  order by study_beginday desc LIMIT 0,1) AS class_id 
                ,if(coursebalance_unitexpend>coursebalance_unitearning and coursebalance_unitearning>0,coursebalance_unitearning,coursebalance_unitexpend) as unit_price 
                FROM smc_payfee_order_course A  
                LEFT JOIN smc_payfee_order B ON A.order_pid=B.order_pid  
                LEFT JOIN smc_student_coursebalance C ON C.student_id=B.student_id and C.company_id=B.company_id and C.school_id=B.school_id and C.course_id=A.course_id 
                WHERE {$datainwhere} 
                AND B.order_status>0
                AND B.order_status<4
                AND B.order_createtime<'{$endqueryTimes}'  
                AND (order_arrearageprice>0  
                OR EXISTS(SELECT 1 FROM smc_payfee_order_pay WHERE order_pid=B.order_pid AND pay_successtime>='{$endqueryTimes}' AND pay_issuccess='1'))  
                ORDER BY A.order_pid,st_date DESC  
                )TA,(select @rownum :=0 , @pdept := null ,@rank:=0) R  
                )TTA 
                LEFT JOIN smc_student TTB ON TTA.company_id=TTB.company_id AND TTA.student_id=TTB.student_id  
                LEFT JOIN smc_class TTC ON TTA.company_id=TTC.company_id AND TTA.class_id=TTC.class_id  
                LEFT JOIN smc_student_guildpolicy TTD ON TTD.student_id=TTB.student_id 
                LEFT JOIN smc_course TTE ON TTA.course_id=TTE.course_id  
                WHERE {$datawhere} 
                AND unpaid_this-left_money>0         
                AND exists(SELECT 1 FROM view_smc_student_class WHERE company_id=TTA.company_id AND school_id=TTA.school_id AND student_id=TTA.student_id AND course_id=TTA.course_id)
                ");
                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;
            return $data;
        }
    }

    //月支出明细表--X
    function monthlyExpendReport($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (C.student_cnname like '%{$request['keyword']}%' or C.student_enname like '%{$request['keyword']}%' 
            or C.student_idcard like '%{$request['keyword']}%' or C.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.expend_confirmtime <='{$endtime}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
            $datawhere .= " and A.expend_confirmtime >= '{$starttime}'";
        }

        if (isset($request['expend_type']) && $request['expend_type'] !== '') {
            $datawhere .= " and A.expend_type= '{$request['expend_type']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select  A.expend_id,A.trading_pid,
                    A.school_id,B.school_cnname,B.school_branch,
                    A.student_id,C.student_branch,C.student_cnname,C.student_enname,
                    A.expend_type,
                    A.expend_price,
                    A.expend_note 
                  from smc_school_expend A
                  left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id 
                  left join smc_student C on A.company_id=C.company_id and A.student_id=C.student_id 
                  where {$datawhere}
                  and A.company_id='{$request['company_id']}' 
                  order by B.school_branch,A.expend_type,A.student_id
              ";


        $expend_type = $this->LgArraySwitch(array("0" => "签呈报损", "1" => "认缴支出", "2" => "坏账核销"));


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无支出数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['expend_type'] = $expend_type[$dateexcelvar['expend_type']];
                    $datearray['expend_price'] = $dateexcelvar['expend_price'];
                    $datearray['expend_note'] = $dateexcelvar['expend_note'];
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "学员编号", "学员中文名", "学员英文名", "支出类型", "支出金额", '备注', '关联交易单号'));
            $excelfileds = array("school_branch", 'school_cnname', 'student_branch', 'student_cnname', 'student_enname', 'expend_type', 'expend_price', 'expend_note', 'trading_pid');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("月支出明细表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $incomeList = $this->DataControl->selectClear($sql);
            if (!$incomeList) {
                $this->error = true;
                $this->errortip = "无支出数据";
                return false;
            }

            foreach ($incomeList as &$var) {
                $var['expend_type'] = $expend_type[$var['expend_type']];
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.expend_id
                  from smc_school_expend A
                  left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id 
                  left join smc_student C on A.company_id=C.company_id and A.student_id=C.student_id 
                  where {$datawhere} 
                  and A.company_id='{$request['company_id']}' 
                  order by A.school_id,A.expend_type,A.student_id 
";
                $dbCount = $this->DataControl->selectClear($count_sql);
                if ($dbCount) {
                    $allnum = count($dbCount);;
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $incomeList;

            return $data;
        }
    }

    //班级结算学生收入明细报表
    function classEndStudentIncome($request)
    {
        $datawhere = "A.company_id='{$request['company_id']}'";//A.school_id='{$request['school_id']}' and

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
                or B.student_branch like '%{$request['keyword']}%' 
                or C.CLASS_ENNAME like '%{$request['keyword']}%' 
                or C.CLASS_CNNAME like '%{$request['keyword']}%' 
                or C.CLASS_BRANCH like '%{$request['keyword']}%')";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and C.class_enddate <= '{$request['end_time']}'";
        }
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and C.class_enddate >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT G.school_id,G.school_branch,G.school_cnname 
                ,C.class_id,C.class_branch,C.class_cnname,C.class_enname,C.class_stdate,C.class_enddate 
                ,B.student_id,B.student_branch,B.student_cnname,B.student_enname,A.study_beginday,A.study_endday 
                ,(select sum(X.timelog_playtimes) from smc_student_coursebalance_timelog X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.timelog_playclass=Y.code_playclass and X.timelog_playname=Y.code_playname AND Y.code_group='冲销') as rece_times 
                ,(select sum(X.timelog_playtimes) from smc_student_coursebalance_timelog X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.timelog_playclass=Y.code_playclass and X.timelog_playname=Y.code_playname AND Y.code_group='结转') as back_times 
                ,(select sum(X.timelog_playtimes) from smc_student_coursebalance_timelog X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.timelog_playclass=Y.code_playclass and X.timelog_playname=Y.code_playname AND Y.code_group='认列') as used_times 
                ,(select sum(log_playamount) from smc_student_coursebalance_log X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.log_class='0' and X.log_playclass=Y.code_playclass and X.log_playname=Y.code_playname AND Y.code_group='冲销') as rece_amount 
                ,(select sum(log_playamount) from smc_student_coursebalance_log X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.log_class='0' and X.log_playclass=Y.code_playclass and X.log_playname=Y.code_playname AND Y.code_group='结转') as back_amount 
                ,(select sum(log_playamount) from smc_student_coursebalance_log X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.log_class='0' and X.log_playclass=Y.code_playclass and X.log_playname=Y.code_playname AND Y.code_group='认列') as used_amount 
                ,F.coursebalance_figure as left_amount,F.coursebalance_time as left_times
                FROM smc_student_study A 
                LEFT JOIN smc_student B ON A.company_id=B.company_id AND A.student_id=B.student_id 
                LEFT JOIN smc_class C ON A.company_id=C.company_id AND A.class_id=C.class_id 
                LEFT JOIN smc_course D ON A.company_id=D.company_id AND C.course_id=D.course_id 
                LEFT JOIN smc_code_coursecat E ON A.company_id=E.company_id AND D.coursecat_id=E.coursecat_id 
                LEFT JOIN smc_student_coursebalance F ON A.company_id=F.company_id AND A.school_id=F.school_id AND A.student_id=F.student_id AND C.course_id=F.course_id 
                LEFT JOIN smc_school G ON A.company_id=G.company_id AND A.school_id=G.school_id 
                WHERE {$datawhere}
                AND C.class_status<>'-2' 
                order BY G.school_branch,C.class_branch
              ";


        $status_type = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "已毕业", "3" => "保留学籍", "-1" => "已离校"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无班级结算学生收入明细";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

//                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['class_id'] = $dateexcelvar['class_id'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_period'] = $dateexcelvar['class_stdate'] . '~' . $dateexcelvar['class_enddate'];
//                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_period'] = $dateexcelvar['study_beginday'] . '~' . $dateexcelvar['study_endday'];
                    $datearray['rece_times'] = $dateexcelvar['rece_times'];
                    $datearray['rece_amount'] = $dateexcelvar['rece_amount'];
                    $datearray['back_times'] = $dateexcelvar['back_times'];
                    $datearray['back_amount'] = $dateexcelvar['back_amount'];
                    $datearray['used_times'] = $dateexcelvar['used_times'];
                    $datearray['used_amount'] = $dateexcelvar['used_amount'];
                    $datearray['left_times'] = $dateexcelvar['left_times'];
                    $datearray['left_amount'] = $dateexcelvar['left_amount'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "班级编号", "班级名称", "班级别名", "开班时间", "学生编号", "学员中文名", '学员英文名', "就读时间", "冲销课次", "冲销金额", "结算课次", "结算金额", "认列课次", "认列金额", "剩余课次", "剩余金额"));
            $excelfileds = array("school_branch", "school_cnname", 'class_branch', 'class_cnname', 'class_enname', 'class_period', 'student_branch', 'student_cnname', 'student_enname', 'student_period', 'rece_times', 'rece_amount', 'back_times', 'back_amount', 'used_times', 'used_amount', 'left_times', 'left_amount');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}班级结算学生收入明细报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无班级结算学生收入明细";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['class_period'] = $var['class_stdate'] . '~' . $var['class_enddate'];
                $var['student_period'] = $var['study_beginday'] . '~' . $var['study_endday'];
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT A.school_id
                ,C.class_id
                ,B.student_id
                FROM smc_student_study A 
                LEFT JOIN smc_student B ON A.company_id=B.company_id AND A.student_id=B.student_id 
                LEFT JOIN smc_class C ON A.company_id=C.company_id AND A.class_id=C.class_id 
                WHERE {$datawhere}
                AND C.class_status<>'-2' 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $ClassCalc;

            return $data;
        }
    }

    //订单欠费
    function orderUnpaidReport($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.order_createtime <='{$endtime}'";
        } else {
            $endtime = time();
            $datawhere .= " and A.order_createtime <='{$endtime}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
        } else {
            $starttime = 0;
        }
        $datawhere .= " and A.order_createtime >= '{$starttime}'";

        $wherecondition = "";
        $havingcondition = " having 1 ";

        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
            switch ($request['pay_type']) {
                case "0":
                    $wherecondition .= " ifnull((select sum(ordercourse_totalprice) from smc_payfee_order_course where order_pid=A.order_pid) ,0) as ordercourse_totalprice, 
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='0'),0) as ordercourse_paidprice,";
                    $havingcondition .= " and ordercourse_totalprice-ordercourse_paidprice>0 ";
                    break;
                case "1":
                    $wherecondition .= " ifnull((select sum(ordergoods_totalprice) from smc_payfee_order_goods where order_pid=A.order_pid) ,0) as ordergoods_totalprice, 
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='1'),0) as ordergoods_paidprice,";
                    $havingcondition .= " and ordergoods_totalprice-ordergoods_paidprice>0 ";
                    break;
                case "2":
                    $wherecondition .= " ifnull((select sum(item_totalprice) from smc_payfee_order_item where order_pid=A.order_pid) ,0) as orderitem_totalprice, 
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='2'),0) as orderitem_paidprice,";
                    $havingcondition .= " and orderitem_totalprice-orderitem_paidprice>0 ";
                    break;
                default:
                    break;
            }
        }

        $wherecondition .= " A.order_createtime,A.order_note ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
 select  C.school_id,C.school_branch,C.school_cnname,C.school_shortname,C.school_enname,
 B.student_id,B.student_branch,B.student_cnname,B.student_enname,
 A.order_pid,A.order_from,A.order_type,A.order_status,
 A.order_allprice,A.order_coupon_price,A.order_market_price,
 A.order_paymentprice,A.order_paidprice,A.order_arrearageprice,ifnull(D.coursetype_branch,'--') as coursetype_branch, 
 {$wherecondition}
 from smc_payfee_order A
 left join smc_student B on A.student_id=B.student_id and A.company_id=B.company_id
 left join smc_school C on A.school_id=C.school_id and A.company_id=C.company_id
 left join smc_code_coursetype as D on D.coursetype_id=A.coursetype_id
 where {$datawhere} 
 and A.company_id='{$request['company_id']}'
 and A.order_arrearageprice>0
 and A.order_status>'0'
 {$havingcondition}
 order by A.order_createtime desc
              ";//  and A.school_id='{$request['school_id']}'

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $order_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无订单欠费数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_enname'] = $dateexcelvar['school_enname'];

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];

                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['order_from'] = $order_from[$dateexcelvar['order_from']];
                    $datearray['order_type'] = $order_type[$dateexcelvar['order_type']];
                    $datearray['order_status'] = $order_status[$dateexcelvar['order_status']];
                    $datearray['order_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['order_createtime']);
                    $datearray['order_note'] = $dateexcelvar['order_note'];

                    $datearray['order_allprice'] = $dateexcelvar['order_allprice'];
                    $datearray['order_coupon_price'] = $dateexcelvar['order_coupon_price'];
                    $datearray['order_market_price'] = $dateexcelvar['order_market_price'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                    $datearray['order_arrearageprice'] = $dateexcelvar['order_arrearageprice'];

                    if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                        switch ($request['pay_type']) {
                            case "0":
                                $datearray['ordercourse_totalprice'] = $dateexcelvar['ordercourse_totalprice'];
                                $datearray['ordercourse_paidprice'] = $dateexcelvar['ordercourse_paidprice'];
                                $datearray['ordercourse_ownprice'] = $dateexcelvar['ordercourse_totalprice'] - $dateexcelvar['ordercourse_paidprice'];
                                break;
                            case "1":
                                $datearray['ordergoods_totalprice'] = $dateexcelvar['ordergoods_totalprice'];
                                $datearray['ordergoods_paidprice'] = $dateexcelvar['ordergoods_paidprice'];
                                $datearray['ordergoods_ownprice'] = $dateexcelvar['ordergoods_totalprice'] - $dateexcelvar['ordergoods_paidprice'];
                                break;
                            case "2":
                                $datearray['orderitem_totalprice'] = $dateexcelvar['orderitem_totalprice'];
                                $datearray['orderitem_paidprice'] = $dateexcelvar['orderitem_paidprice'];
                                $datearray['orderitem_ownprice'] = $dateexcelvar['orderitem_totalprice'] - $dateexcelvar['orderitem_paidprice'];
                                break;
                            default:
                                break;
                        }
                    }

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_shortname", "school_id='{$request['school_id']}'");


            if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                switch ($request['pay_type']) {
                    case "0":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '课程应收金额', '课程已付金额', '课程欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'ordercourse_totalprice', 'ordercourse_paidprice', 'ordercourse_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_shortname']}订单欠费明细表-课程.xlsx"));
                        break;
                    case "1":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '教材应收金额', '教材已付金额', '教材欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'ordergoods_totalprice', 'ordergoods_paidprice', 'ordergoods_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_shortname']}订单欠费明细表-教材.xlsx");
                        break;
                    case "2":
                        $excelheader = array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '杂费应收金额', '杂费已付金额', '杂费欠费金额');
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'orderitem_totalprice', 'orderitem_paidprice', 'orderitem_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_shortname']}订单欠费明细表-杂费.xlsx"));
                        break;
                    default:
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_shortname']}订单欠费总表.xlsx"));
                        break;
                }
            } else {
                $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额'));
                $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice');
                query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_shortname']}订单欠费总表.xlsx"));
            }
//            $excelheader = array("学员编号", "学员中文名", "学员英文名", "支出类型", "支出金额", '备注', '关联交易单号');
//            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'expend_type', 'expend_price', 'expend_note', 'trading_pid');
//            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费表.xlsx");
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $unpaidList = $this->DataControl->selectClear($sql);
            if (!$unpaidList) {
                $this->error = true;
                $this->errortip = "无订单欠费数据";
                return false;
            }

            foreach ($unpaidList as &$var) {
                $var['order_from'] = $order_from[$var['order_from']];
                $var['order_type'] = $order_type[$var['order_type']];
                $var['order_status'] = $order_status[$var['order_status']];
                $var['order_createtime'] = date("Y-m-d H:i:s", $var['order_createtime']);

                if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                    switch ($request['pay_type']) {
                        case "0":
                            $var['ordercourse_ownprice'] = $var['ordercourse_totalprice'] - $var['ordercourse_paidprice'];
                            break;
                        case "1":
                            $var['ordergoods_ownprice'] = $var['ordergoods_totalprice'] - $var['ordergoods_paidprice'];
                            break;
                        case "2":
                            $var['orderitem_ownprice'] = $var['orderitem_totalprice'] - $var['orderitem_paidprice'];
                            break;
                        default:
                            break;
                    }
                }
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
 select A.order_pid,{$wherecondition}
 from smc_payfee_order A
 left join smc_student B on A.student_id=B.student_id and A.company_id=B.company_id
 where {$datawhere} 
 and A.company_id='{$request['company_id']}' 
 and A.order_arrearageprice>0
 and A.order_status>'0'
 {$havingcondition}
 order by A.order_createtime desc
"; //and A.school_id='{$request['school_id']}'
                $dbCount = $this->DataControl->selectClear($count_sql);
                if ($dbCount) {
                    $allnum = count($dbCount);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $unpaidList;

            return $data;
        }
    }

    function studEndcalcEstimateSummary($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' AND d.school_istest <> '1' AND d.school_isclose<> '1' AND e.course_classnum > 10";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id='{$request['school_id']}' ";
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
        } else {
            $datawhere .= " AND d.school_istest <> '1' AND d.school_isclose <> '1'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and d.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND d.school_istest <> '1' AND d.school_isclose<> '1' ";
        }

//        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
//            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
//            $datawhere .= " AND b.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
//        } 

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.student_cnname like '%{$request['keyword']}%' 
            or c.student_enname like '%{$request['keyword']}%' 
            or c.student_idcard like '%{$request['keyword']}%' 
            or c.student_branch like '%{$request['keyword']}%' 
            or b.class_enname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and b.class_enddate<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and b.class_enddate>= '{$firstday}'";
        }

        if (isset($request['class_branch']) && $request['class_branch'] !== '') {//教务系统调取了 这个报表加的字段
            $datawhere .= " and b.class_branch= '{$request['class_branch']}' and b.class_id > 0 ";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and e.course_id= '{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and e.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and e.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['course_isrenew']) && $request['course_isrenew'] !== '') {
            $datawhere .= " and e.course_isrenew= '{$request['course_isrenew']}'";
            $datawhere .= " and b.class_isnotrenew <>'{$request['course_isrenew']}'";
        }

        if (isset($request['course_issesson']) && $request['course_issesson'] !== '') {
            $datawhere .= " and e.course_issesson= '{$request['course_issesson']}'";
        }

        $havingwhere = " having 1 ";
        if (isset($request['is_renewal']) && $request['is_renewal'] !== '') {
            if ($request['is_renewal'] == "1") {
                $datawhere .= " and study_upgraderate>=100 ";
            } else {
                $datawhere .= " and study_upgraderate<100";
            }
        }

        if (isset($request['is_standard']) && $request['is_standard'] !== '') {
            if ($request['is_standard'] == "1") {
                $havingwhere .= " and class_num = course_class_num ";
            } else {
                $havingwhere .= " and class_num <> course_class_num ";
            }
        }

        if (isset($request['is_containsbreakoff']) && $request['is_containsbreakoff'] !== '') {
            if ($request['is_containsbreakoff'] == '0') {
                $datawhere .= " and not exists(select 1 from smc_class_breakoff where class_id=b.class_id and breakoff_status>=2 and breakoff_type=0) ";
                $is_containsbreakoff = 0;
            } else {
                $is_containsbreakoff = 1;
            }
        } else {
            $is_containsbreakoff = 1;
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select d.school_id,d.school_branch,d.school_cnname,d.school_tagbak,
                c.student_id,c.student_branch,c.student_cnname,c.student_enname
                ,j.channel_name
                
                ,ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id= st.result_id 
                AND st.student_id = c.student_id  and st.school_id=d.school_id and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0
                AND st.track_classname = '续费电访' AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times
                ,ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id= st.result_id 
                AND st.student_id = c.student_id  and st.school_id=d.school_id and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0
                AND st.track_classname = '主管电访' AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_main
                ,ifnull((SELECT st.track_note FROM smc_student_track AS st WHERE st.student_id = c.student_id and st.school_id=d.school_id 
                and (st.coursetype_id=0 or st.coursetype_id=e.coursetype_id) and st.track_from=0 
                AND st.track_classname = '续费电访' AND track_day>=b.class_stdate ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS track_note,
                b.class_id,b.class_branch,b.class_cnname,b.class_enname,b.class_enddate
                
                ,(select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=0) as main_teacher
                ,e.course_branch,e.course_cnname,f.coursecat_id,f.coursecat_branch,f.coursecat_cnname,g.coursetype_id,g.coursetype_branch,g.coursetype_cnname
                
                ,(SELECT count(1) FROM smc_class_hour WHERE class_id = b.class_id AND hour_ischecking >= 0 AND hour_iswarming = 0) as class_num
                ,e.course_classnum as course_class_num
                
                ,a.study_upgraderate
                ,a.study_nextprice
                ,a.study_nexttimes
                ,a.study_endday
                from 
                (select x.company_id,x.school_id,x.class_id,y.student_id,y.study_outdate as study_endday,y.study_upgraderate,y.study_nextprice,y.study_nexttimes
                from smc_class_endcalc x,smc_class_endcalc_study y where study_iscalculate>=0 and  x.endcalc_id=y.endcalc_id and y.study_outtype in('0','1','3','4')) a 
                left join smc_class b on a.class_id=b.class_id 
                left join smc_student c on a.student_id=c.student_id 
                left join smc_school d on a.school_id=d.school_id 
                left join smc_course e on b.course_id=e.course_id and a.company_id=e.company_id 
                LEFT JOIN smc_code_coursecat f ON e.coursecat_id = f.coursecat_id  AND a.company_id = f.company_id 
                LEFT JOIN smc_code_coursetype g ON f.coursetype_id = g.coursetype_id  AND a.company_id = g.company_id 
                left join smc_student_guildpolicy j on c.student_id=j.student_id and j.guildpolicy_enddate>=a.study_endday
                where b.class_status<>'-2'   
                and b.class_type='0'  
                and e.course_sellclass=0
                and {$datawhere} 
                {$havingwhere} 
                order by (case when d.school_istest=0 and d.school_isclose=0 then 1 when d.school_isclose=0 then 2 when d.school_istest=0 then 3 else 4 end),d.school_istest asc,field(d.school_sort,0),d.school_sort asc,d.school_createtime asc,d.school_id,g.coursetype_id,f.coursecat_id,b.class_id 
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            $mainData = array();
            if ($dateexcelarray) {
                $mainData = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['class_id'] = $dateexcelvar['class_id'];
                    $datearray['coursecat_id'] = $dateexcelvar['coursecat_id'];
                    $datearray['coursetype_id'] = $dateexcelvar['coursetype_id'];
                    $datearray['school_tagbak'] = $dateexcelvar['school_tagbak'];

                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];

                    $datearray['is_renewal'] = ($dateexcelvar['study_upgraderate'] >= 100) ? "Y" : "N";
                    $datearray['study_nexttimes'] = $dateexcelvar['study_nexttimes'];
                    $datearray['study_nextprice'] = $dateexcelvar['study_nextprice'];
                    $datearray['study_upgraderate'] = $dateexcelvar['study_upgraderate'];

                    $datearray['connect_times'] = trim($dateexcelvar['connect_times']) == '' ? '---' : $dateexcelvar['connect_times'];
                    $datearray['connect_main'] = trim($dateexcelvar['connect_main']) == '' ? '---' : $dateexcelvar['connect_main'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];

                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_enddate'] = $dateexcelvar['class_enddate'];
                    $datearray['study_endday'] = $dateexcelvar['study_endday'];
                    $datearray['main_teacher'] = $dateexcelvar['main_teacher'];

                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];

                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['course_class_num'] = $dateexcelvar['course_class_num'];

                    $mainData[] = $datearray;
                }
            }
            if (isset($request['school_id']) && $request['school_id'] !== '' && !is_array($request['school_id'])) {
                $excelName = $this->LgStringSwitch("{$schoolOne['school_cnname']}学员实际留班统计表");
            } else {
                $excelName = $this->LgStringSwitch("学员实际留班统计表");
            }

            $monthrange = array();
            $end = date('Ym', strtotime($lastday)); // 转换为月
            $i = 0;
            do {
                $month = date('Ym', strtotime($firstday . ' + ' . $i . ' month'));
                $monthrange[] = $month;
                $i++;
            } while ($month < $end);

            $exceldetailheader = $this->LgArraySwitch(array("校区编号", "校区名称", "学生编号", "学员中文名", '学员英文名', "专案名称", "是否留班", "留班课次", "留班金额", "留班课时比"
            , "电访结果", "沟通内容", "班级编号", "班级名称", "班级别名", "班级结束日期", "出班日期", "主教教师", "课程别编号", "课程别名称", "班种编号", "班种名称"
            , "班组编号", "班组名称", "班级课次", "课程别设定课次"));

            $excelclassheader = $this->LgArraySwitch(array("校区编号", "校区名称", "班级编号", "班种编号", "课程别编号", "班级别名", "班级结束日期", "结班月份", "主教教师"
            , "总人数", "公益人数", "总留班人数", '公益留班人数', '人数', '实际留班', '实际留班率'));

            $excelschoolheader = $this->LgArraySwitch(array("校区编号", "校区名称"));

            $fileName = $excelName;
            $fileType = ".xlsx";

//            if(isset($request['is_export_SmcToTeasx']) && $request['is_export_SmcToTeasx'] == '1'){
//                $SmcToTeasx = array();
//                $SmcToTeasx['is_containsbreakoff'] = $is_containsbreakoff;
//                $SmcToTeasx['exceldetailheader'] = $exceldetailheader;
//                $SmcToTeasx['excelclassheader'] = $excelclassheader;
//                $SmcToTeasx['excelschoolheader'] = $excelschoolheader;
//                $SmcToTeasx['mainData'] = $mainData;
//                $SmcToTeasx['monthrange'] = $monthrange;
//                $SmcToTeasx['fileName'] = $fileName;
//                $SmcToTeasx['fileType'] = $fileType;
//
//                $SmcToTeasxData['list'] = $SmcToTeasx;
//                return $SmcToTeasxData;
//            }

            excelcustom_studEndcalcEstimate($is_containsbreakoff, $exceldetailheader, $excelclassheader, $excelschoolheader, $mainData, $monthrange, $fileName, $fileType);

            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['connect_main'] = trim($var['connect_main']) == '' ? '---' : $var['connect_main'];
                $var['connect_times'] = trim($var['connect_times']) == '' ? '---' : $var['connect_times'];
                $var['is_renewal'] = $this->LgArraySwitch(($var['study_upgraderate'] >= 100) ? "是" : "否");
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select d.school_id,
                c.student_id,
                b.class_id
                
                ,(SELECT count(1) FROM smc_class_hour WHERE class_id = b.class_id AND hour_ischecking >= 0 AND hour_iswarming = 0) as class_num
                ,e.course_classnum as course_class_num
                
                ,a.study_upgraderate
                ,a.study_nextprice
                ,a.study_nexttimes
                from 
                (select x.company_id,x.school_id,x.class_id,y.student_id,y.study_outdate as study_endday,y.study_upgraderate,y.study_nextprice,y.study_nexttimes
                from smc_class_endcalc x,smc_class_endcalc_study y where study_iscalculate>=0 and  x.endcalc_id=y.endcalc_id and y.study_outtype in('0','1','3','4')) a
                left join smc_class b on a.class_id=b.class_id
                left join smc_student c on a.student_id=c.student_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_course e on b.course_id=e.course_id and a.company_id=e.company_id 
                LEFT JOIN smc_code_coursecat f ON e.coursecat_id = f.coursecat_id  AND a.company_id = f.company_id 
                LEFT JOIN smc_code_coursetype g ON f.coursetype_id = g.coursetype_id  AND a.company_id = g.company_id 
                where b.class_status<>'-2'  and b.class_type='0' and e.course_sellclass=0
                and {$datawhere} 
                {$havingwhere} 
              ";

                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $ClassCalc;

            return $data;
        }
    }

    //学员续课包预估统计表
    function studPackageEstimateDetail($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (g.student_cnname like '%{$request['keyword']}%' 
            or g.student_enname like '%{$request['keyword']}%' 
            or g.student_branch like '%{$request['keyword']}%' 
            or c.class_cnname like '%{$request['keyword']}%' 
            or c.class_enname like '%{$request['keyword']}%' 
            or c.class_branch like '%{$request['keyword']}%')";
        }

        $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and a.hour_day<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and a.hour_day>= '{$firstday}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and d.course_id= '{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and e.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and f.coursetype_id= '{$request['coursetype_id']}'";
        }

        $havingwhere = ' having 1';
        if (isset($request['hour_isbuy']) && $request['hour_isbuy'] !== '') {
            if ($request['hour_isbuy'] == "1") {
                $havingwhere .= " and track_result='续费' or hour_isbuy=1 ";
            } else {
                $havingwhere .= " and track_result<>'续费' or hour_isbuy<>1 ";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ta.*,
            ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id 
                AND st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result,                    
            ifnull((SELECT st.track_note FROM	smc_student_track AS st WHERE st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date 
                and track_day<=ta.max_date and st.track_from=0	and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_note
            from (
                select a.hourplan_id
                ,h.school_id
                ,h.school_branch
                ,h.school_shortname as school_cnname
                ,c.class_id
                ,c.class_branch
                ,c.class_enname
                ,c.class_cnname
                ,g.student_id
                ,g.student_branch
                ,g.student_cnname
                ,g.student_enname
                ,ifnull((select max(hour_isbuy) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),0) as hour_isbuy
                ,d.course_branch
                ,d.course_cnname
                ,e.coursecat_branch
                ,e.coursecat_cnname
                ,f.coursetype_id
                ,f.coursetype_branch
                ,f.coursetype_cnname
                ,ifnull((select min(hour_day) from temp_smc_hourplan x where x.student_id=a.student_id and x.school_id=a.school_id and x.hourplan_rank>a.hourplan_rank),a.hour_day) as max_date
                ,ifnull((select hour_day from temp_smc_hourplan x where x.student_id=a.student_id and x.school_id=a.school_id and x.hourplan_rank < a.hourplan_rank order by x.hourplan_rank desc limit 3,1),a.hour_day) as min_date
                ,i.coursebalance_figure
                ,i.coursebalance_time
                ,a.hour_day
                from temp_smc_hourplan a
                left join smc_class_hour b on a.hour_id=b.hour_id
                left join smc_class c on a.class_id=c.class_id
                left join smc_course d on c.course_id=d.course_id
                left join smc_code_coursecat e on d.coursecat_id=e.coursecat_id
                left join smc_code_coursetype f on e.coursetype_id=f.coursetype_id
                left join smc_student g on a.student_id=g.student_id
                left join smc_school h on a.school_id=h.school_id
                left join smc_student_coursebalance i on i.school_id=a.school_id and i.student_id=a.student_id and i.course_id=a.course_id
                where {$datawhere}
                and a.hour_isnode=1
                and b.hour_ischecking>-1
                and c.class_status>-2
                and d.course_sellclass=1
            ) ta
            where 1
            {$havingwhere} ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            $mainData = array();
            if ($dateexcelarray) {
                $mainData = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
//                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
//                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['hour_isbuy'] = ($dateexcelvar['hour_isbuy'] == '1' || $dateexcelvar['track_result'] == '续费') ? "是" : "否";

//                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
//                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
//                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
//                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
//                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
//                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];

                    $datearray['track_result'] = trim($dateexcelvar['track_result']) == '' ? '---' : $dateexcelvar['track_result'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $mainData[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", "是否续费", "课程别编号", "班种编号", "班组编号", "电访结果", '沟通内容', "课程剩余课次", "课程剩余金额", "应续包日期"));
            $excelfileds = array("student_branch", "student_cnname", 'student_enname', 'hour_isbuy', 'course_branch', 'coursecat_branch', 'coursetype_branch', 'track_result', 'track_note', 'coursebalance_time', 'coursebalance_figure', 'hour_day');
            query_to_excel($excelheader, $mainData, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员续课包预估统计表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['hour_isbuy'] = ($var['hour_isbuy'] == '1' || $var['track_result'] == '续费') ? "是" : "否";
                $var['track_result'] = trim($var['track_result']) == '' ? '---' : $var['track_result'];
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select ta.*,
                ifnull((SELECT rs.trackresult_name 
                    FROM smc_student_track AS st, smc_code_trackresult AS rs 
                    WHERE rs.trackresult_id = st.result_id AND st.student_id = ta.student_id AND st.track_classname = '续费电访' 
                    AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result,
                    
                ifnull((SELECT st.track_note FROM	smc_student_track AS st WHERE st.student_id = ta.student_id AND st.track_classname = '续费电访' 
                    AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0	and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_note
            from
            (
            select a.hourplan_id
            ,h.school_id
            ,h.school_branch
            ,h.school_shortname as school_cnname
            ,c.class_id
            ,c.class_branch
            ,c.class_enname
            ,g.student_id
            ,g.student_branch
            ,g.student_cnname
            ,g.student_enname
            ,ifnull((select max(hour_isbuy) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),0) as hour_isbuy
            ,d.course_branch
            ,d.course_cnname
            ,e.coursecat_branch
            ,e.coursecat_cnname
            ,f.coursetype_id
            ,f.coursetype_branch
            ,f.coursetype_cnname
            ,ifnull((select min(hour_day) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),a.hour_day) as max_date
            ,ifnull((select hour_day from temp_smc_hourplan x where x.student_id=a.student_id and x.school_id=a.school_id and x.hourplan_rank < a.hourplan_rank order by x.hourplan_rank desc limit 3,1),a.hour_day) as min_date
            ,i.coursebalance_figure
            ,i.coursebalance_time
            ,a.hour_day
            from temp_smc_hourplan a
            left join smc_class_hour b on a.hour_id=b.hour_id
            left join smc_class c on a.class_id=c.class_id
            left join smc_course d on c.course_id=d.course_id
            left join smc_code_coursecat e on d.coursecat_id=e.coursecat_id
            left join smc_code_coursetype f on e.coursetype_id=f.coursetype_id
            left join smc_student g on a.student_id=g.student_id
            left join smc_school h on a.school_id=h.school_id
            left join smc_student_coursebalance i on i.school_id=a.school_id and i.student_id=a.student_id and i.course_id=a.course_id
            where {$datawhere}
            and a.hour_isnode=1
            and b.hour_ischecking>-1
            and c.class_status>-2
            and d.course_sellclass=1
            ) ta
            where 1
            {$havingwhere} ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $ClassCalc;
            return $data;
        }
    }

    //续课包电访执行统计表
    function studPackageTrackSummary($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $lastday = date("Y-m-d", strtotime($request['end_time']));
            $datawhere .= " and a.hour_day<= '{$lastday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $firstday = date("Y-m-d", strtotime($request['start_time']));
            $datawhere .= " and a.hour_day>= '{$firstday}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and d.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tta.school_id
            ,tta.school_branch
            ,tta.school_cnname
            ,tta.district_cnname
            ,count(tta.hourplan_id) as total_num
            ,sum(tta.hour_isbuy) as renew_num
            ,sum(if(track_result='续费' and hour_isbuy=0,1,0)) as track_renewal_num
            ,sum(if(track_result like '%考虑%' and hour_isbuy=0,1,0)) as track_consider_num
            ,sum(if(track_result like '%流失%' and hour_isbuy=0,1,0)) as track_dicide_num
            ,sum(if(track_result='' and hour_isbuy=0,1,0)) as track_notyet_num
            ,sum(if(track_result_main='' and track_result<>'续费' and hour_isbuy=0,1,0)) as track_notyet_main_num
            from (
            select ta.*,tb.school_branch,tb.school_cnname,
            (SELECT district_cnname FROM gmc_company_district WHERE district_id =tb.district_id) AS district_cnname,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id 
                    AND st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id 
                    AND st.student_id = ta.student_id AND st.track_classname = '主管电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result_main,		
                ifnull((SELECT st.track_note FROM	smc_student_track AS st WHERE st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0	and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_note
            from (
            select a.hourplan_id
            ,a.student_id
            ,a.coursetype_id
            ,a.school_id
            ,ifnull((select max(hour_isbuy) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),0) as hour_isbuy
            ,ifnull((select min(hour_day) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),a.hour_day) as max_date
            ,ifnull((select hour_day from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank < a.hourplan_rank order by hourplan_rank desc limit 3,1),a.hour_day) as min_date
            from temp_smc_hourplan a
            left join smc_class_hour b on a.hour_id=b.hour_id
            left join smc_class c on a.class_id=c.class_id
            left join smc_course d on c.course_id=d.course_id
            where {$datawhere}
            and a.hour_isnode=1
            and b.hour_ischecking>-1
            and c.class_status>-2
            and d.course_sellclass=1
            ) ta
            left join smc_school tb on ta.school_id=tb.school_id
            where 1
            ) tta
            group by tta.school_id
            ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            $mainData = array();
            if ($dateexcelarray) {
                $mainData = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];

                    $datearray['total_num'] = $dateexcelvar['total_num'];
                    $datearray['renew_num'] = $dateexcelvar['renew_num'];
                    $datearray['unrenew_num'] = $dateexcelvar['total_num'] - $dateexcelvar['renew_num'];
                    $datearray['track_renewal_num'] = $dateexcelvar['track_renewal_num'];
                    $datearray['track_consider_num'] = $dateexcelvar['track_consider_num'];
                    $datearray['track_dicide_num'] = $dateexcelvar['track_dicide_num'];

                    $datearray['track_notyet_num'] = $dateexcelvar['track_notyet_num'];
                    $datearray['track_percentage'] = $dateexcelvar['total_num'] > 0 ? (round(($dateexcelvar['total_num'] - $dateexcelvar['track_notyet_num']) / $dateexcelvar['total_num'], 3) * 100 . "%") : '---';
                    $datearray['track_main_percentage'] = $dateexcelvar['total_num'] > 0 ? (round(($dateexcelvar['total_num'] - $dateexcelvar['track_notyet_main_num']) / $dateexcelvar['total_num'], 3) * 100 . "%") : '---';
                    $datearray['renewal_percentage'] = $dateexcelvar['total_num'] > 0 ? (round($dateexcelvar['renew_num'] / $dateexcelvar['total_num'], 2) * 100 . "%") : '---';
                    $mainData[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "所属区域", "应续课包人数", "已续课包人数", "未缴费电访人数", "电访表示续课包人数", "电访表示考虑人数", '电访确认流失', "未进行电访人数", "电访执行率", "主管电访执行率", "目前续课包率"));
            $excelfileds = array("school_branch", "school_cnname", 'district_cnname', 'total_num', 'renew_num', 'unrenew_num', 'track_renewal_num', 'track_consider_num', 'track_dicide_num', 'track_notyet_num', 'track_percentage', 'track_main_percentage', 'renewal_percentage');
            query_to_excel($excelheader, $mainData, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员续课包预估统计表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无电访信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['unrenew_num'] = $var['total_num'] - $var['renew_num'];
                $var['track_percentage'] = $var['total_num'] > 0 ? (round(($var['total_num'] - $var['track_notyet_num']) / $var['total_num'], 3) * 100 . "%") : '---';
                $var['track_main_percentage'] = $var['total_num'] > 0 ? (round(($var['total_num'] - $var['track_notyet_main_num']) / $var['total_num'], 3) * 100 . "%") : '---';
                $var['renewal_percentage'] = $var['total_num'] > 0 ? (round($var['renew_num'] / $var['total_num'], 2) * 100 . "%") : '---';
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select tta.school_id
            ,tta.school_branch
            ,tta.school_cnname
            ,tta.district_cnname
            ,count(tta.hourplan_id) as total_num
            ,sum(tta.hour_isbuy) as renew_num
            ,sum(if(track_result='续费' and hour_isbuy=0,1,0)) as track_renewal_num
            ,sum(if(track_result like '%考虑%' and hour_isbuy=0,1,0)) as track_consider_num
            ,sum(if(track_result like '%流失%' and hour_isbuy=0,1,0)) as track_dicide_num
            ,sum(if(track_result='' and hour_isbuy=0,1,0)) as track_notyet_num
            ,sum(if(track_result_main='' and track_result<>'续费' and hour_isbuy=0,1,0)) as track_notyet_main_num
            from (
            select ta.*,tb.school_branch,tb.school_cnname,
            (SELECT district_cnname FROM gmc_company_district WHERE district_id =tb.district_id) AS district_cnname,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id 
                    AND st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id 
                    AND st.student_id = ta.student_id AND st.track_classname = '主管电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0 and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_result_main,		
                ifnull((SELECT st.track_note FROM	smc_student_track AS st WHERE st.student_id = ta.student_id AND st.track_classname = '续费电访' AND track_day>=ta.min_date and track_day<=ta.max_date
                    and st.track_from=0	and st.school_id=ta.school_id and st.coursetype_id=ta.coursetype_id ORDER BY st.track_id DESC LIMIT 0,1),'') AS track_note
            from (
            select a.hourplan_id
            ,a.student_id
            ,a.coursetype_id
            ,a.school_id
            ,ifnull((select max(hour_isbuy) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),0) as hour_isbuy
            ,ifnull((select min(hour_day) from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank>a.hourplan_rank),a.hour_day) as max_date
            ,ifnull((select hour_day from temp_smc_hourplan where student_id=a.student_id and school_id=a.school_id and hourplan_rank < a.hourplan_rank order by hourplan_rank desc limit 3,1),a.hour_day) as min_date
            from temp_smc_hourplan a
            left join smc_class_hour b on a.hour_id=b.hour_id
            left join smc_class c on a.class_id=c.class_id
            left join smc_course d on c.course_id=d.course_id
            where {$datawhere}
            and a.hour_isnode=1
            and b.hour_ischecking>-1
            and c.class_status>-2
            and d.course_sellclass=1
            ) ta
            left join smc_school tb on ta.school_id=tb.school_id
            where 1
            ) tta
            group by tta.school_id
            ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $ClassCalc;
            return $data;
        }
    }

}