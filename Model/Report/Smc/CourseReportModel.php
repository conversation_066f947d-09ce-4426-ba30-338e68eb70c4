<?php


namespace Model\Report\Smc;

class CourseReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->staffer_id = $publicarray['staffer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //教师上课报表
    function TeacherClassCourse($request)
    {
        $datawhere = "ch.teaching_isdel = 0 and s.company_id = '{$this->company_id}'";

        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and h.hour_day >= '{$request['start_time']}' ";
            $week_start_day = $request['start_time'];
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and h.hour_day <= '{$request['end_time']}' ";
            $week_end_day = $request['end_time'];
        } else {
            $week_start_day = date("Y-m-d");
        }
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (s.staffer_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%')";
        }
        //教师筛选
        if (isset($request['re_staffer_id']) && $request['re_staffer_id'] != '') {
            $datawhere .= " and s.staffer_id='{$request['re_staffer_id']}'";
        }
        //班级筛选
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id='{$request['class_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT ss.school_cnname,ss.school_shortname,ss.school_branch,s.staffer_cnname,s.staffer_enname,s.staffer_branch,ch.teaching_type,h.hour_day,c.class_cnname,c.class_enname,c.class_branch
                FROM smc_staffer as s
                LEFT JOIN smc_class_hour_teaching as ch ON s.staffer_id=ch.staffer_id
                LEFT JOIN smc_class as c ON c.class_id = ch.class_id
                LEFT JOIN smc_school as ss ON ss.school_id = c.school_id
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id and h.hour_id=ch.hour_id
                WHERE {$datawhere} AND h.hour_id > 0  and h.hour_ischecking>=0
                ORDER BY ss.school_branch,h.hour_day ASC";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    if ($dateexcelvar['teaching_type'] && $dateexcelvar['teaching_type'] == '1') {
                        $datearray['staffer_type'] = $this->LgStringSwitch('助教');
                    } else {
                        $datearray['staffer_type'] = $this->LgStringSwitch('主教');
                    }
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "职工中文名", "职工英文名", "职工编号", "教师类型", "上课日期", "班级名称", "班级别名", "班级编号"));
            $excelfileds = array('school_cnname', 'school_branch', 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'staffer_type', 'hour_day', 'class_cnname', 'class_enname', 'class_branch');
            if (isset($request['end_time']) && $request['end_time'] != '') {
                $tem_name = "{$this->schoolOne['school_shortname']}教师上课报表{$week_start_day}-{$week_end_day}.xls";
            } else {
                $tem_name = "{$this->schoolOne['school_shortname']}教师上课报表{$week_start_day}.xls";
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }
            foreach ($dataList as &$v) {
                if ($v['teaching_type'] == '1') {
                    $v['staffer_type'] = '助教';
                } else {
                    $v['staffer_type'] = '主教';
                }
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(h.hour_id) as num
                                                            FROM smc_staffer as s
                                                            LEFT JOIN smc_class_hour_teaching as ch ON s.staffer_id=ch.staffer_id
                                                            LEFT JOIN smc_class as c ON c.class_id = ch.class_id
                                                            LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id and h.hour_id=ch.hour_id
                                                            WHERE {$datawhere} AND h.hour_id > 0  and h.hour_ischecking>=0");
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $dataList;
            return $data;
        }
    }

    //班级上课报表
    function classCourseList($request)
    {
        $datawhere = "c.company_id = '{$this->company_id}'";

        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and h.hour_day >= '{$request['start_time']}' ";
            $week_start_day = $request['start_time'];
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and h.hour_day <= '{$request['end_time']}' ";
            $week_end_day = $request['end_time'];
        } else {
            $week_start_day = date("Y-m-d");
        }
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        //课程别筛选
        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and co.course_id='{$request['course_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }
        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_cnname,s.school_shortname,s.school_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,
                co.course_cnname,co.course_branch,h.hour_day,CONCAT(h.hour_starttime,'~',h.hour_endtime) AS hour_period,
                (SELECT
                  group_concat(DISTINCT cr.classroom_cnname)
                  FROM smc_classroom as cr
                  WHERE cr.classroom_id = h.classroom_id AND cr.classroom_status = '1'
                ) as classroom_cnname,
                (SELECT
                  group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
                  FROM smc_class_teach as ct
                  LEFT JOIN smc_staffer as sf ON sf.staffer_id = ct.staffer_id
                  WHERE ct.class_id = c.class_id AND ct.teach_type = '0' AND ct.teach_status = '0'
                ) as main_teacher,
                (SELECT
                  group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
                  FROM smc_class_teach as ct
                  LEFT JOIN smc_staffer as sf ON sf.staffer_id = ct.staffer_id
                  WHERE ct.class_id = c.class_id AND ct.teach_type = '1' AND ct.teach_status = '0'
                ) as assistant_teacher
                FROM smc_class as c
                LEFT JOIN smc_school as s ON s.school_id = c.school_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id AND h.course_id = co.course_id
                WHERE {$datawhere} AND h.hour_id > 0 ORDER BY s.school_branch,h.hour_day DESC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无考勤数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['hour_period'] = $dateexcelvar['hour_period'];
                    $datearray['main_teacher'] = $dateexcelvar['main_teacher'];
                    $datearray['assistant_teacher'] = $dateexcelvar['assistant_teacher'];
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "班级别名", "班级编号", "课程别名称", "课程别编号", "上课日期", "上课时间段", "主教", "助教", "教室"));
            $excelfileds = array('school_cnname', 'school_branch', 'class_cnname', 'class_enname', 'class_branch', 'course_cnname', 'course_branch', 'hour_day', 'hour_period', 'main_teacher', 'assistant_teacher', 'classroom_cnname');
            if (isset($request['end_time']) && $request['end_time'] != '') {
                $tem_name = "{$this->schoolOne['school_shortname']}班级上课报表{$week_start_day}-{$week_end_day}.xls";
            } else {
                $tem_name = "{$this->schoolOne['school_shortname']}班级上课报表{$week_start_day}.xls";
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(c.class_id) as num
                                                            FROM smc_class as c
                                                            LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                            LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id AND h.course_id = co.course_id
                                                            WHERE {$datawhere} AND h.hour_id > 0");
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $dataList;
            return $data;
        }
    }

    //在籍新生统计表报表
    function isResidenceStudent($request)
    {
        $datawhere = "  AND A.company_id='{$request['company_id']}' ";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND F.school_istest <> '1' AND F.school_isclose <> '1'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (G.student_cnname like '%{$request['keyword']}%' 
            or G.student_enname like '%{$request['keyword']}%' 
            or G.student_idcard like '%{$request['keyword']}%' 
            or G.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] != '') {
            $starttime = strtotime($request['start_time']);
            $datawhere .= " and A.pay_successtime>= '{$starttime}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.pay_successtime<= '{$endtime}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "	SELECT 
                F.school_id,
                F.school_branch,
                F.school_cnname,
                F.school_enname,
                A.coursetype_id,
                A.coursetype_cnname,
                A.coursetype_branch,
                A.coursecat_id,
                A.coursecat_cnname,
                A.coursecat_branch,
                A.course_id,
                A.course_cnname,
                A.course_branch,
                A.student_id,
                G.student_branch,
                G.student_cnname,
                G.student_enname,
                (select guildstutype_code from smc_code_guildstutype where company_id=H.company_id and guildstutype_id=H.guildstutype_id) as channel_name,
                A.order_createtime,
                A.pay_successtime as pay_day,
                A.info_id,
                A.pay_price,
                A.trading_pid ,
                (select order_arrearageprice from smc_payfee_order where trading_pid=a.trading_pid) as unpaid_price,
                (select GROUP_CONCAT(tags_name) from smc_student_tags where student_id=a.student_id and (school_id=a.school_id or school_id='0')) as tags_name
                ,(select sum(ordercourse_buynums) from smc_payfee_order x,smc_payfee_order_course y where x.order_pid=y.order_pid and x.trading_pid=a.trading_pid) as buy_times
                FROM smc_student_registerinfo a 	
                LEFT JOIN smc_school F ON A.school_id = F.school_id 	AND F.company_id = A.company_id
                LEFT JOIN smc_student G ON G.student_id = A.student_id 	AND G.company_id = A.company_id
                LEFT JOIN smc_student_guildpolicy H ON H.student_id = G.student_id 
                WHERE A.info_status=1 {$datawhere}
                ORDER BY F.school_branch,A.coursetype_id,A.student_id,	A.order_createtime 
              ";
//        ,
//        (select GROUP_CONCAT(concat(z.course_branch,'-',y.class_branch,'-',y.class_enname)) from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and x.student_id=a.student_id and x.school_id=a.school_id
//    and y.company_id=z.company_id and y.course_id=z.course_id and z.coursetype_id=65) as courses_branch,
//                (select GROUP_CONCAT(concat(coupons_name,'~',FROM_UNIXTIME(coupons_bindingtime,'%Y-%m-%d'),'~',coupons_price)) from smc_student_coupons x where x.student_id=a.student_id and (x.school_id=a.school_id or x.school_id='0')
//    and x.coupons_isuse>-1) as coupons_name


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无在籍新生";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_enname'] = $dateexcelvar['school_enname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['tags_name'] = $dateexcelvar['tags_name'];
                    $datearray['register_date'] = date('Y-m-d', $dateexcelvar['order_createtime']);
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $datearray['pay_day'] = date('Y-m-d', $dateexcelvar['pay_day']);
                    $datearray['buy_times'] = $dateexcelvar['buy_times'];
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    $datearray['unpaid_price'] = $dateexcelvar['unpaid_price'];
//                    $datearray['courses_branch'] = $dateexcelvar['courses_branch'];
//                    $datearray['coupons_name'] = $dateexcelvar['coupons_name'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学校id", "校区编号", "校园名称", "检索代码", '班组名称', "班组代码", "班种名称", "班种代码", "课程别名称", "课程别代码", "学生编号", "学生中文名", "学生英文名", "专案名称", "学员标签", "下单日期", "交易编号", "新生日期", "购买课次", "首缴金额", "账单欠费金额"));
            $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'student_branch', 'student_cnname', 'student_enname', 'channel_name', 'tags_name', 'register_date', 'trading_pid', 'pay_day', 'buy_times', 'pay_price', 'unpaid_price');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}在籍新生统计表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $NewReg = $this->DataControl->selectClear($sql);
            if (!$NewReg) {
                $this->error = true;
                $this->errortip = "无在籍新生";
                return false;
            }

            foreach ($NewReg as &$var) {
//                $var['is_change'] = $var['pay_day'] == $var['change_day'] ? 1 : 0;
//                $var['notUseClick'] = $var['is_change'] == 0;

                $var['register_date'] = date('Y-m-d', $var['order_createtime']);
                $var['pay_day'] = date('Y-m-d', $var['pay_day']);
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT 
                A.company_id,
                A.coursetype_id,
                A.student_id
                FROM smc_student_registerinfo A 	
                LEFT JOIN smc_school F ON A.school_id = F.school_id 	AND F.company_id = A.company_id
                LEFT JOIN smc_student G ON G.student_id = A.student_id 	AND G.company_id = A.company_id
                WHERE A.info_status=1 {$datawhere}
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $NewReg;

            return $data;
        }
    }

    function registeredStu($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and b.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.coursetype_cnname,st.student_cnname,st.student_enname,st.student_branch,ifnull(sp.channel_name,'') as channel_name,
                    sum(a.coursebalance_figure) as coursebalance_figure,
                (select GROUP_CONCAT(tags_name) from smc_student_tags where student_id=a.student_id and (school_id=a.school_id or school_id='0')) as tags_name
                ,(select sum(student_balance+student_withholdbalance) from smc_student_balance where school_id=a.school_id and student_id=a.student_id) as student_balance
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                LEFT JOIN smc_code_coursetype c ON c.coursetype_id = b.coursetype_id
                left join smc_student as st on st.student_id=a.student_id
                left join smc_student_guildpolicy as sp on sp.student_id=st.student_id and sp.guildpolicy_enddate>=CURDATE()
                WHERE {$datawhere} 
                and a.school_id='{$this->school_id}' 
                and a.coursebalance_figure > 0
                GROUP BY a.school_id, a.student_id, b.coursetype_id ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['tags_name'] = $dateexcelvar['tags_name'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['student_balance'] = $dateexcelvar['student_balance'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("班组名称", "学生中文名", "学生英文名", "学生编号", "专案名称", "学员标签", '课组剩余金额', '账户余额'));
            $excelfileds = array('coursetype_cnname', 'student_cnname', 'student_enname', 'student_branch', 'channel_name', "tags_name", 'coursebalance_figure', 'student_balance');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("在籍学生明细表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            $count_sql = "
               SELECT a.coursebalance_id
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                left join smc_student as st on st.student_id=a.student_id
                WHERE {$datawhere} 
                and a.school_id='{$this->school_id}' 
                and a.coursebalance_figure > 0
                GROUP BY a.school_id, a.student_id, b.coursetype_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $data['allnum'] = $db_nums ? count($db_nums) : 0;

            $data['list'] = $studentList;

            return $data;
        }
    }

    function studyingStu($request)
    {
        $datawhere = "1 AND A.company_id='{$request['company_id']}' AND A.school_id = '{$request['school_id']}'";
        $datawhere1 = "1 AND a.company_id='{$request['company_id']}' AND a.school_id = '{$request['school_id']}'";
        $where = ' 1 ';
        $havingwhere = '';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $havingwhere .= " having (TB.student_cnname like '%{$request['keyword']}%' 
            or TB.student_enname like '%{$request['keyword']}%' 
            or TB.student_branch like '%{$request['keyword']}%'
            or class_branch like '%{$request['keyword']}%' 
            or class_enname like '%{$request['keyword']}%' 
            or class_cnname like '%{$request['keyword']}%')";

            $datawhere1 .= " and (c.student_cnname like '%{$request['keyword']}%' 
            or c.student_enname like '%{$request['keyword']}%' 
            or c.student_branch like '%{$request['keyword']}%'
            or b.class_branch like '%{$request['keyword']}%' 
            or b.class_enname like '%{$request['keyword']}%' 
            or b.class_cnname like '%{$request['keyword']}%')";
        }

        $request['fixedtime'] = date("Y-m-d");

        $datawhere .= " AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) >= DATE_SUB('{$request['fixedtime']}', INTERVAL D.coursetype_intervaltime DAY) ";
        $where .= " and TA.study_beginday<='{$request['fixedtime']}'";
        $where .= " and TA.study_endday>='{$request['fixedtime']}'";
//        $datawhere1 .= " and d.study_beginday<='{$request['fixedtime']}'";
//        $datawhere1 .= " and d.study_endday>='{$request['fixedtime']}'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
            $datawhere1 .= " and e.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and C.coursecat_id='{$request['coursecat_id']}'";
            $datawhere1 .= " and d.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if ($request['query_mode'] == '0') {
            $sql = "select a.school_id
                    ,f.school_branch
                    ,f.school_cnname
                    ,b.class_branch
                    ,b.class_cnname
                    ,b.class_enname
                    ,c.student_branch
                    ,c.student_cnname
                    ,c.student_enname
                    ,(case when a.study_beginday < b.class_stdate then b.class_stdate else a.study_beginday end) as study_beginday 
                    ,(case when a.study_endday > b.class_enddate then b.class_enddate else a.study_endday end) as study_endday 
                    ,ifnull(g.channel_name,'---') as channel_name
                    ,e.coursetype_branch
                    ,e.coursetype_cnname
                    ,h.coursecat_branch
                    ,h.coursecat_cnname
                    ,(select GROUP_CONCAT(tags_name) from smc_student_tags where student_id=a.student_id and (school_id=a.school_id or school_id='0')) as tags_name
                    from smc_student_study a
                    left join smc_class b on a.class_id=b.class_id
                    left join smc_student c on a.student_id=c.student_id
                    left join smc_course d on b.course_id=d.course_id
                    left join smc_code_coursetype e on d.coursetype_id=e.coursetype_id
                    left join smc_code_coursecat h on d.coursecat_id=h.coursecat_id
                    left join smc_school f on a.school_id=f.school_id
                    left join smc_student_guildpolicy g on a.student_id=g.student_id and g.guildpolicy_enddate>=CURDATE() and e.coursetype_id=65
                    where {$datawhere1}
                    and f.school_isclose=0
                    and f.school_istest=0
                    and b.class_type=0
                    and b.class_status>-2
                    AND a.school_id = '{$this->school_id}'
                    and ifnull(b.class_enddate,'')<>''
                    and ifnull(a.study_endday,'')<>''
                    and (case when a.study_beginday < b.class_stdate then b.class_stdate else a.study_beginday end)<=CURDATE()
                    and (case when a.study_endday > b.class_enddate then b.class_enddate else a.study_endday end)>=CURDATE()
                    group by a.school_id,e.coursetype_id,a.student_id
                    ORDER BY b.class_id DESC ";
        } else {
            $sql = "SELECT TA.school_id,TC.school_branch,TC.school_cnname,
            (select y.class_branch from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as class_branch,
            (select y.class_cnname from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as class_cnname,
            (select y.class_enname from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as class_enname,
            (select x.study_beginday from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as study_beginday,
            (select x.study_endday from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as study_endday,
            TA.student_id,TB.student_branch, TB.student_cnname, TB.student_enname,
            TA.coursetype_id,TA.coursetype_cnname ,
            TA.coursecat_id,TA.coursecat_cnname,TA.coursecat_branch,
            (SELECT ifnull(channel_name,'---')  FROM smc_student_guildpolicy WHERE student_id=TB.student_id and guildpolicy_enddate>='{$request['fixedtime']}') AS channel_name,
            (SELECT GROUP_CONCAT(tags_name) FROM smc_student_tags WHERE student_id=TA.student_id AND (school_id=TA.school_id OR school_id='0')) AS tags_name
            FROM
            (
                SELECT A.school_id,
                A.student_id,
                C.coursetype_id,D.coursetype_cnname ,
                group_concat(distinct C.coursecat_id) as coursecat_id, 
                group_concat(distinct E.coursecat_cnname) as coursecat_cnname,
                group_concat(distinct E.coursecat_branch) as coursecat_branch,
                MIN(A.study_beginday) AS study_beginday,MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS study_endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype D ON C.coursetype_id=D.coursetype_id 
                LEFT JOIN smc_code_coursecat E ON C.coursecat_id=E.coursecat_id 
                WHERE {$datawhere} 
                AND B.class_type='0' AND B.class_status>'-2'
                GROUP BY A.school_id,A.student_id,C.coursetype_id 
            ) AS TA 
            LEFT JOIN smc_student TB ON TA.student_id=TB.student_id
            LEFT JOIN smc_school TC ON TA.school_id=TC.school_id
            WHERE {$where} 
            {$havingwhere}
            ORDER BY TC.school_branch,class_branch
        ";//TA.study_beginday,TA.study_endday,
        }
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['tags_name'] = $dateexcelvar['tags_name'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    $datearray['study_endday'] = $dateexcelvar['study_endday'];
                    $datearray['reading_type'] = ($request['fixedtime'] > $dateexcelvar['study_endday']) ? '离班待读' : '在班在读';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学生编号", '学生中文名', '学生英文名', "专案名称", "学员标签", "班组名称", "班组编号", "班组名称", "班种编号", "班级编号", "班级别名", '入班时间', '离班时间', '在读状态'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'channel_name', 'tags_name', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'class_branch', 'class_enname', 'study_beginday', 'study_endday', 'reading_type');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("在读学生明细表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($studentList as &$var) {
                if ($request['fixedtime'] > $var['study_endday']) {
                    $var['reading_type'] = '离班待读';
                } else {
                    $var['reading_type'] = '在班在读';
                }
            }

            $data = array();

            if ($request['query_mode'] == '0') {
                $count_sql = "select a.school_id,e.coursetype_id,a.student_id                
                    from smc_student_study a
                    left join smc_class b on a.class_id=b.class_id
                    left join smc_student c on a.student_id=c.student_id
                    left join smc_course d on b.course_id=d.course_id
                    left join smc_code_coursetype e on d.coursetype_id=e.coursetype_id
                    left join smc_school f on a.school_id=f.school_id
                    left join smc_student_guildpolicy g on a.student_id=g.student_id and g.guildpolicy_enddate>=CURDATE() and e.coursetype_id=65
                    where {$datawhere1}
                    and f.school_isclose=0
                    and f.school_istest=0
                    and b.class_type=0
                    and b.class_status>-2
                    AND a.school_id = '{$this->school_id}'
                    and ifnull(b.class_enddate,'')<>''
                    and ifnull(a.study_endday,'')<>''
                    and (case when a.study_beginday < b.class_stdate then b.class_stdate else a.study_beginday end)<=CURDATE()
                    and (case when a.study_endday > b.class_enddate then b.class_enddate else a.study_endday end)>=CURDATE()
                    group by a.school_id,e.coursetype_id,a.student_id ";
            } else {
                $count_sql = "SELECT TA.school_id,
            (select y.class_branch from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as class_branch,
            (select y.class_cnname from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as class_cnname,
            (select y.class_enname from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$request['fixedtime']}' and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as class_enname,
            TA.student_id,TB.student_branch, TB.student_cnname, TB.student_enname,
            TA.coursetype_id
            FROM
            (
                SELECT A.school_id,
                A.student_id,
                C.coursetype_id,D.coursetype_cnname ,
                MIN(A.study_beginday) AS study_beginday,MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS study_endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype D ON C.coursetype_id=D.coursetype_id 
                WHERE {$datawhere} 
                AND B.class_type='0' AND B.class_status>'-2' 
                GROUP BY A.school_id,A.student_id,C.coursetype_id 
            ) AS TA 
            LEFT JOIN smc_student TB ON TA.student_id=TB.student_id
            LEFT JOIN smc_school TC ON TA.school_id=TC.school_id
            WHERE {$where} 
            {$havingwhere}";
            }
            $db_nums = $this->DataControl->selectClear($count_sql);
            $data['allnum'] = $db_nums ? count($db_nums) : 0;
            $data['list'] = $studentList;

            return $data;
        }
    }

    function estimateTrackSummaryReport($request)
    {
        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.school_branch like '%{$request['keyword']}%' 
            or a.school_cnname like '%{$request['keyword']}%' )";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and a.class_enddate<='{$request['end_time']}'";
        } else {
            $request['end_time'] = date("Y-m-d");
            $datawhere .= " and a.class_enddate<='{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and a.class_enddate>='{$request['start_time']}'";
        } else {
            $request['start_time'] = date("Y-m-01");
            $datawhere .= " and a.class_enddate>='{$request['start_time']}'";
        }

//        if (isset($request['month']) && $request['month'] !== '') {
//            $queryMonth = date('Y-m', strtotime($request['month'] . '-01'));
//        } else {
//            $queryMonth = date('Y-m', strtotime(time()));
//        }
//        $datawhere .= " and DATE_FORMAT(class_enddate,'%Y-%m')= '{$queryMonth}'";

        if (isset($request['is_containsbreakoff']) && $request['is_containsbreakoff'] == '0') {
            $datawhere .= " and not exists(select 1 from smc_class_breakoff where class_id=a.class_id and breakoff_status>=2 and breakoff_type=0) ";
            $is_containsbreakoff = 0;
        } else {
            $is_containsbreakoff = 1;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.school_id,a.school_branch,a.school_cnname,
            (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname,
            sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) and a.channel_name<>'',1,0)) as channel_renewal_count,
            sum(if(a.channel_name<>'',1,0)) as channel_count,
            sum(if(a.channel_name='',1,0)) as total_count,
            sum(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as renewal_count,
            sum(if((connect_times<>'' or ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01'))) and channel_name='',1,0)) as connect_count,
            sum(if(connect_times='续费' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_renewal_count,
            sum(if(connect_times like '%考虑%' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_consider_count,
            sum(if(connect_times like '%流失%' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_refuse_count,
            sum(if(connect_times='' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_notyet_count,
            sum(if(connect_main='' and connect_times<>'续费' and ((renewal_times<a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates<100 and a.class_enddate>='2022-01-01')) and channel_name='',1,0)) as connect_main_notyet_count
            from smc_student_course_estimate a
            left join smc_school as s on s.school_id=a.school_id 
            WHERE {$datawhere} 
            and course_isrenew=1 
            group by a.school_id
            order by s.school_sort asc,s.school_createtime asc,a.school_branch";

//        DATE_FORMAT(class_enddate,'%Y-%m') as calc_month,
//            group by a.school_id,calc_month

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无续费电访执行数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
//                    $datearray['calc_month'] = $dateexcelvar['calc_month'];
                    $datearray['channel_count'] = $dateexcelvar['channel_count'];
                    $datearray['channel_renewal_count'] = $dateexcelvar['channel_renewal_count'];
                    $datearray['channel_renewal_percentage'] = $dateexcelvar['channel_count'] > 0 ? (round($dateexcelvar['channel_renewal_count'] / $dateexcelvar['channel_count'], 2) * 100 . "%") : '---';
                    $datearray['total_count'] = $dateexcelvar['total_count'];
                    $datearray['renewal_count'] = $dateexcelvar['renewal_count'];
                    $datearray['unpaid_count'] = $dateexcelvar['total_count'] - $dateexcelvar['renewal_count'];
//                    $datearray['connect_count'] = $dateexcelvar['connect_count'];
                    $datearray['connect_renewal_count'] = $dateexcelvar['connect_renewal_count'];
                    $datearray['connect_consider_count'] = $dateexcelvar['connect_consider_count'];
                    $datearray['connect_refuse_count'] = $dateexcelvar['connect_refuse_count'];
                    $datearray['connect_notyet_count'] = $dateexcelvar['connect_notyet_count'];
                    $datearray['connect_percentage'] = $dateexcelvar['total_count'] > 0 ? (round(($dateexcelvar['total_count'] - $dateexcelvar['connect_notyet_count']) / $dateexcelvar['total_count'], 3) * 100 . "%") : '---';
                    $datearray['connect_main_percentage'] = $dateexcelvar['total_count'] > 0 ? (round(($dateexcelvar['total_count'] - $dateexcelvar['connect_main_notyet_count']) / $dateexcelvar['total_count'], 3) * 100 . "%") : '---';
                    $datearray['estimate_percentage'] = $dateexcelvar['total_count'] > 0 ? (round(($dateexcelvar['renewal_count'] + $dateexcelvar['connect_renewal_count']) / $dateexcelvar['total_count'], 2) * 100 . "%") : '---';
                    $datearray['renewal_percentage'] = $dateexcelvar['total_count'] > 0 ? (round($dateexcelvar['renewal_count'] / $dateexcelvar['total_count'], 2) * 100 . "%") : '---';
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "所属区域", "公益人数", "公益学生续费人数", "公益学生续费率", "升级留班班级人数", "已续费人数", "未缴费人数", "电访表示续费人数", "电访表示考虑人数", "电访确认流失", "未进行电访人数", '电访执行率', '主管电访执行率', '预估本月留班率', '目前续费率'));
            $excelfileds = array('school_branch', 'school_cnname', 'district_cnname', 'channel_count', 'channel_renewal_count', 'channel_renewal_percentage', 'total_count', "renewal_count", 'unpaid_count', 'connect_renewal_count', 'connect_consider_count', 'connect_refuse_count', 'connect_notyet_count', 'connect_percentage', 'connect_main_percentage', 'estimate_percentage', 'renewal_percentage');

            if ($is_containsbreakoff == '1') {
                query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("续费电访执行统计表（{$request['start_time']}~{$request['end_time']}）（含拆班）.xlsx"));
            } else {
                query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("续费电访执行统计表（{$request['start_time']}~{$request['end_time']}）（不含拆班）.xlsx"));
            }
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $schoolList = $this->DataControl->selectClear($sql);

            if (!$schoolList) {
                $this->error = true;
                $this->errortip = "无续费电访执行数据";
                return false;
            }

            foreach ($schoolList as &$var) {
                $var['channel_renewal_percentage'] = $var['channel_count'] > 0 ? (round($var['channel_renewal_count'] / $var['channel_count'], 2) * 100 . "%") : '---';
                $var['unpaid_count'] = $var['total_count'] - $var['renewal_count'];
                $var['connect_percentage'] = $var['total_count'] > 0 ? (round(($var['total_count'] - $var['connect_notyet_count']) / $var['total_count'], 3) * 100 . "%") : '---';
                $var['connect_main_percentage'] = $var['total_count'] > 0 ? (round(($var['total_count'] - $var['connect_main_notyet_count']) / $var['total_count'], 3) * 100 . "%") : '---';
                $var['estimate_percentage'] = $var['total_count'] > 0 ? (round(($var['renewal_count'] + $var['connect_renewal_count']) / $var['total_count'], 2) * 100 . "%") : '---';
                $var['renewal_percentage'] = $var['total_count'] > 0 ? (round($var['renewal_count'] / $var['total_count'], 2) * 100 . "%") : '---';
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.school_id,a.school_branch,a.school_cnname,
                    sum(if(a.channel_name<>'',1,0)) as channel_count,
                    sum(if(a.channel_name='',1,0)) as total_count
                    from smc_student_course_estimate a 
                    left join smc_school as s on s.school_id=a.school_id 
                    WHERE {$datawhere} 
                    and course_isrenew=1 
                    group by a.school_id ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $schoolList;

            return $data;
        }
    }

}