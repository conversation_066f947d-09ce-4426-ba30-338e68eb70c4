<?php


namespace Model\Report\Smc;

class StudentReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->staffer_id = $publicarray['staffer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //学员课程余额报表
    function studentCourseBalance($request)
    {
        $datawhere = " and A.company_id='{$this->company_id}' 
        and A.school_id='{$this->school_id}' ";

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and A.course_id='{$request['course_id']}'";
        }

        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }

        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        $datachoose = " and ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datachoose .= " (B.student_cnname like '%{$request['keyword']}%' or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['course_status']) && $request['course_status'] != '') {
            if ($request['course_status'] == 1) {
                $datachoose .= " and E.study_beginday<='{$endqueryday}' and E.study_endday>={'{$endqueryday}'}";
            } else {
                $datachoose .= " and (E.study_beginday>'{$endqueryday}' or E.study_endday<{'{$endqueryday}'})";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch
//                ,(SELECT l.log_finalamount FROM smc_student_coursebalance_log as l WHERE l.log_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$this->school_id}' and l.log_class='0' ORDER BY l.log_time DESC,l.log_id DESC limit 0,1) as coursebalance_figure
//                ,(SELECT l.timelog_finaltimes FROM smc_student_coursebalance_timelog as l WHERE l.timelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$this->school_id}' ORDER BY l.timelog_time DESC,l.timelog_id DESC limit 0,1) as coursebalance_time
//                ,c.course_id,c.course_cnname,c.course_branch,sc.class_cnname,sc.class_enname,sc.class_branch,sc.class_id,ss.study_beginday,ss.study_endday
//                ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_student_clockinginlog as scl on scl.hourstudy_id=sh.hourstudy_id where sh.class_id=sc.class_id and sh.student_id=t.student_id and scl.clockinginlog_createtime<'{$endqueryTimes}' limit 0,1) as hourNum
//            from smc_student as s,smc_student_coursebalance_timelog as t,smc_course as c,smc_student_study as ss,smc_class as sc
//            WHERE {$datawhere} GROUP BY t.student_id,t.course_id
//            HAVING (coursebalance_figure>0 or coursebalance_time>0)
//            ORDER BY coursebalance_figure desc,s.student_id desc";
        $sql = "select 
 B.student_id, B.student_cnname, B.student_enname, B.student_branch,
 (select log_finalamount FROM smc_student_coursebalance_log X where X.log_time <'{$endqueryTimes}' and X.student_id = A.student_id 
 and X.course_id = A.course_id and X.school_id =A.school_id order by X.log_time desc,X.log_id desc limit 0,1) as coursebalance_figure,
 (select X.timelog_finaltimes FROM smc_student_coursebalance_timelog X where X.timelog_time < '{$endqueryTimes}' and X.student_id = A.student_id 
 and X.course_id = A.course_id and X.school_id =A.school_id order by X.timelog_time desc,X.timelog_id desc limit 0,1) as coursebalance_time,
 C.course_id, C.course_cnname, C.course_branch,
 E.class_cnname, E.class_enname, E.class_branch, E.class_id,
 E.study_beginday, E.study_endday, E.deduct as hourNum 
 from smc_student_coursebalance A 
 left join smc_student B on A.company_id=B.company_id and A.student_id=B.student_id 
 left join smc_course C on A.company_id=C.company_id and A.course_id=C.course_id 
 left join smc_code_coursecat D on C.company_id=D.company_id and C.coursecat_id=D.coursecat_id 
 left join (
 		select student_id,course_id,
 		group_concat(distinct class_id) as class_id,group_concat(distinct class_branch) as class_branch,
 		group_concat(distinct class_cnname) as class_cnname,group_concat(distinct class_enname) as class_enname,
 		min(study_beginday) as study_beginday,max(study_endday) as study_endday,
 		count(hourstudy_checkin) as deduct_times
 		from view_smc_student_study_spend  A
 		where 1 {$datawhere}
 		and hour_day<='{$endqueryday}'
 		and study_beginday<='{$endqueryday}'
 		and hour_isfree=0
 		group by student_id,course_id
 ) as E on A.student_id=E.student_id and A.course_id=E.course_id 
 where 1 {$datawhere}
 {$datachoose}
 and A.coursebalance_createtime<'{$endqueryTimes}'
 group by A.student_id,A.course_id
 having (coursebalance_figure>0 or coursebalance_time>0)
 order by coursebalance_figure desc,B.student_id desc
        ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['hourNum'] = $dateexcelvar['hourNum'];
                    if ($dateexcelvar['class_id'] > 0 && $endqueryday <= $dateexcelvar['study_endday'] && $endqueryday >= $dateexcelvar['study_beginday']) {
                        $datearray['course_status'] = '未入班';
                    } else {
                        $datearray['course_status'] = '已入班';
                    }
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员ID", "学员中文名", "学员英文名", "学员编号", "课程别名称", "课程别编号", "课程别余额", "剩余课次", "课程消耗状态", "班级名称", "班级英文名", "班级编号", "班内耗课次数");
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time', 'course_status', 'class_cnname', 'class_enname', 'class_branch', 'hourNum');
            $tem_name = $this->schoolOne['school_cnname'] . '学员课程余额明细报表' . $endqueryday . '.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);

            if (!$list) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            $count_sql = "select 
 B.student_id,
 (select log_finalamount FROM smc_student_coursebalance_log X where X.log_time <'{$endqueryTimes}' and X.student_id = A.student_id 
 and X.course_id = A.course_id and X.school_id =A.school_id order by X.log_time desc,X.log_id desc limit 0,1) as coursebalance_figure,
 (select X.timelog_finaltimes FROM smc_student_coursebalance_timelog X where X.timelog_time < '{$endqueryTimes}' and X.student_id = A.student_id 
 and X.course_id = A.course_id and X.school_id =A.school_id order by X.timelog_time desc,X.timelog_id desc limit 0,1) as coursebalance_time,
 C.course_id
 from smc_student_coursebalance A 
 left join smc_student B on A.company_id=B.company_id and A.student_id=B.student_id 
 left join smc_course C on A.company_id=C.company_id and A.course_id=C.course_id 
 left join smc_code_coursecat D on C.company_id=D.company_id and C.coursecat_id=D.coursecat_id 
 left join (
 		select student_id,course_id,
 		group_concat(distinct class_id) as class_id,group_concat(distinct class_branch) as class_branch,
 		group_concat(distinct class_cnname) as class_cnname,group_concat(distinct class_enname) as class_enname,
 		min(study_beginday) as study_beginday,max(study_endday) as study_endday,
 		count(hourstudy_checkin) as deduct_times
 		from view_smc_student_study_spend  A
 		where 1 {$datawhere}
 		and hour_day<='{$endqueryday}'
 		and study_beginday<='{$endqueryday}'
 		and hour_isfree=0
 		group by student_id,course_id
 ) as E on A.student_id=E.student_id and A.course_id=E.course_id 
 where 1 {$datawhere}
 {$datachoose}
 and A.coursebalance_createtime<'{$endqueryTimes}'
 group by A.student_id,A.course_id
 having (coursebalance_figure>0 or coursebalance_time>0)
 order by coursebalance_figure desc,s.student_id desc";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }

    }

    //学员余额报表
    function studentBalance($request)
    {

        $datawhere = " 1 ";

        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }

        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch
,(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$this->school_id}' and l.balancelog_class='0' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as balance,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$this->school_id}' and l.balancelog_class='2' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_withholdbalance,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$this->school_id}' and l.balancelog_class='1' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_forwardbalance,
(select count(sc.coupons_id) from smc_student_coupons as sc where sc.student_id=s.student_id and sc.company_id='{$this->company_id}' and sc.coupons_createtime<'{$endqueryTimes}' and sc.coupons_isuse=0) as coupons_num
              from smc_student_enrolled as se
              left join smc_student as s on s.student_id=se.student_id
              where {$datawhere} and (se.enrolled_leavetime='' or se.enrolled_leavetime>'{$endqueryTimes}') and se.school_id='{$this->school_id}' and s.company_id='{$this->company_id}'
              HAVING (balance>0 or student_withholdbalance>0 or student_forwardbalance>0)
              order by (balance + student_withholdbalance) desc,s.student_id desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['balance'] = $dateexcelvar['balance'];
                    $datearray['student_withholdbalance'] = $dateexcelvar['student_withholdbalance'];
                    $datearray['all_balance'] = $dateexcelvar['balance'] + $dateexcelvar['student_withholdbalance'];
                    $datearray['student_forwardbalance'] = $dateexcelvar['student_forwardbalance'];
                    $datearray['coupons_num'] = $dateexcelvar['coupons_num'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员ID", "学员中文名", "学员英文名", "学员编号", "可退余额", "不可退余额", "账户总余额", "账户结转金额", "优惠券未使用数");
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'balance', 'student_withholdbalance', 'all_balance', 'student_forwardbalance', 'coupons_num');
            $tem_name = $this->schoolOne['school_cnname'] . '学员余额明细报表' . $endqueryday . '.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            foreach ($studentList as &$studentOne) {
                $studentOne['all_balance'] = $studentOne['balance'] + $studentOne['student_withholdbalance'];
            }
            $data = array();
            $count_sql = "select s.student_id
,(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$this->school_id}' and l.balancelog_class='0' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as balance,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$this->school_id}' and l.balancelog_class='2' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_withholdbalance,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$this->school_id}' and l.balancelog_class='1' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_forwardbalance
              from smc_student_enrolled as se
              left join smc_student as s on s.student_id=se.student_id
              where {$datawhere} and (se.enrolled_leavetime='' or se.enrolled_leavetime>'{$endqueryTimes}') and se.school_id='{$this->school_id}' and s.company_id='{$this->company_id}'
              HAVING (balance>0 or student_withholdbalance>0 or student_forwardbalance>0)
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $studentList;

            return $data;
        }
    }

    //班级流失报表
    function classLoss($request)
    {
        $datawhere = " 1 ";
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
            $datawhere .= " and scl.changelog_day<='{$endqueryday}'";
        }

        $startqueryday = '';
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $startqueryday = $request['starttime'];
            $datawhere .= " and scl.changelog_day>='{$startqueryday}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,scl.changelog_day,scl.changelog_note,csr.reason_note,st.staffer_cnname,scl.changelog_createtime
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              where {$datawhere} and scl.stuchange_code='B01' and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}'
              order by scl.changelog_day desc,scl.changelog_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['reason_note'] = $dateexcelvar['reason_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['changelog_createtime'] = $dateexcelvar['changelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "班级名称", "班级英文名", "班级编号", "异动日期", "异动原因", "操作人", "操作时间");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'changelog_day', 'reason_note', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->schoolOne['school_cnname'] . '学员班级流失明细报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($list as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              where {$datawhere} and scl.stuchange_code='B01' and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}'
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //学校流失报表
    function schoolLoss($request)
    {
        $datawhere = " 1 ";
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
            $datawhere .= " and scl.changelog_day<='{$endqueryday}'";
        }

        $startqueryday = '';
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $startqueryday = $request['starttime'];
            $datawhere .= " and scl.changelog_day>='{$startqueryday}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,scl.changelog_day,scl.changelog_note,csr.reason_note,st.staffer_cnname,scl.changelog_createtime,scl.stuchange_code,cs.stuchange_name
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              left join smc_code_stuchange as cs on cs.stuchange_code=scl.stuchange_code
              where {$datawhere} and scl.stuchange_code in ('B05','C02') and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}'
              order by scl.changelog_day desc,scl.changelog_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['reason_note'] = $dateexcelvar['reason_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['changelog_createtime'] = $dateexcelvar['changelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "流失类型", "异动日期", "异动原因", "操作人", "操作时间");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'stuchange_name', 'changelog_day', 'reason_note', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->schoolOne['school_cnname'] . '分校流失明细报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($list as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              where {$datawhere} and scl.stuchange_code in ('B05','C02') and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}'
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //待入班报表
    function stuStayInClass($request)
    {
        $datawhere = " 1 ";
        $datahaving = " 1 ";
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
            $endqueryTimes = strtotime($endqueryday) + (3600 * 24);
            $datahaving .= " and pricinglog_createtime<'{$endqueryTimes}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id='{$request['coursecat_id']}'";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id='{$request['course_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,s.student_id,s.student_cnname,s.student_cnname,s.student_branch,sc.course_cnname,sc.course_branch
              ,(select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.course_id=scb.course_id and ss.student_id=scb.student_id and ss.company_id='{$this->company_id}' and ss.school_id='{$this->school_id}' limit 0,1) as study_id
              ,(select scp.pricinglog_createtime from smc_student_coursebalance_pricinglog as scp where scp.student_id=scb.student_id and scp.pricing_id=scb.pricing_id and scp.course_id=scb.course_id and scb.school_id='{$this->school_id}' order by scp.pricinglog_createtime desc limit 0,1) as pricinglog_createtime
              from smc_student_coursebalance as scb
              left join smc_course as sc on sc.course_id=scb.course_id and sc.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scb.student_id
              where {$datawhere} and scb.company_id='{$this->company_id}' and scb.school_id='{$this->school_id}'
              HAVING (study_id=0 or study_id is null) and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) and {$datahaving}
              order by scb.coursebalance_figure desc,s.student_id desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员课程数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "课程别名称", "课程别编号", "课程余额", "课程剩余次数");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time');
            $tem_name = $this->schoolOne['school_cnname'] . '待入班明细报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无学员课程数据";
                return false;
            }

            $data = array();
            $count_sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
              ,(select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.course_id=scb.course_id and ss.student_id=scb.student_id and ss.company_id='{$this->company_id}' and ss.school_id='{$this->school_id}' limit 0,1) as study_id
              ,(select scp.pricinglog_createtime from smc_student_coursebalance_pricinglog as scp where scp.student_id=scb.student_id and scp.pricing_id=scb.pricing_id and scp.course_id=scb.course_id and scb.school_id='{$this->school_id}' order by scp.pricinglog_createtime desc limit 0,1) as pricinglog_createtime
              from smc_student_coursebalance as scb
              left join smc_course as sc on sc.course_id=scb.course_id and sc.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scb.student_id
              where {$datawhere} and scb.company_id='{$this->company_id}' and scb.school_id='{$this->school_id}'
              HAVING (study_id=0 or study_id is null) and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) and {$datahaving}
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //学员课次购买报表
    function stuCoursePurchaseOld($request)
    {
        $datawhere = " 1 ";
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];

        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sct.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,sc.course_cnname,sc.course_branch,scp.pricinglog_buytimes,scp.pricinglog_buyprice,scp.pricinglog_createtime,sct.coursecat_cnname,sct.coursecat_branch,scp.order_pid
              ,(select scl.stuchange_code from smc_student_changelog as scl left join smc_class as c on c.class_id=scl.class_id where c.course_id=scp.course_id and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}' and scl.stuchange_code in (select cs.stuchange_code from smc_code_stuchange as cs where cs.stuchange_type=0) and scl.changelog_createtime<'{$endqueryTimes}' order by scl.changelog_createtime desc limit 0,1) as stuchange_code
              ,(select l.timelog_finaltimes from smc_student_coursebalance_timelog as l where l.student_id=scp.student_id and l.school_id='{$this->school_id}' and l.course_id=scp.course_id and l.timelog_time<'{$endqueryTimes}' order by l.timelog_time desc limit 0,1) as finaltimes
              from smc_student_coursebalance_pricinglog as scp
              left join smc_course as sc on sc.course_id=scp.course_id and sc.company_id='{$this->company_id}'
              left join smc_code_coursecat as  sct ON sc.coursecat_id = sct.coursecat_id
              left join smc_student as s on s.student_id=scp.student_id
              where {$datawhere} and scp.school_id='{$this->school_id}' and scp.pricinglog_createtime<'{$endqueryTimes}'
              order by scp.pricinglog_createtime desc
              ";

        $in = "'A02','A03','A04','A05'";
        $to = "'A07','B01','B02','B03','B04','B06','E01','B07'";


        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无课次购买数据";
                return false;
            }


            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['pricinglog_buytimes'] = $dateexcelvar['pricinglog_buytimes'];
                    $datearray['pricinglog_buyprice'] = $dateexcelvar['pricinglog_buyprice'];
                    $datearray['pricinglog_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['pricinglog_createtime']);

                    if (!$dateexcelvar['stuchange_code']) {
                        $datearray['status'] = '未入班';
                    } else {
                        if (in_array($dateexcelvar['stuchange_code'], $in)) {
                            $datearray['status'] = '已入班';
                        } elseif (in_array($dateexcelvar['stuchange_code'], $to)) {
                            $datearray['status'] = '已出班';
                        }
                    }

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "班种名称", "班种编号", "订单编号", "课程别名称", "课程别编号", "购买课次数", "购买金额", "购买时间", "在班状态", "剩余课次");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'coursecat_cnname', 'coursecat_branch', "order_pid", 'course_cnname', 'course_branch', "pricinglog_buytimes", "pricinglog_buyprice", "pricinglog_createtime", "status", "finaltimes");
            $tem_name = $this->schoolOne['school_cnname'] . '学员课次购买报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无课次购买数据";
                return false;
            }

            foreach ($list as &$one) {
                $one['pricinglog_createtime'] = date("Y-m-d H:i:s", $one['pricinglog_createtime']);
            }

            $data = array();
            $count_sql = "select s.student_id
              from smc_student_coursebalance_pricinglog as scp
              left join smc_course as sc on sc.course_id=scp.course_id and sc.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scp.student_id
              where {$datawhere} and scp.school_id='{$this->school_id}' and scp.pricinglog_createtime<'{$endqueryTimes}'
              order by scp.pricinglog_createtime desc
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //学员延班报表
    function stuContinueClass($request)
    {

        $datawhere = " 1 ";
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
            $datawhere .= " and scl.changelog_day<='{$endqueryday}'";
        }

        $startqueryday = '';
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $startqueryday = $request['starttime'];
            $datawhere .= " and scl.changelog_day>='{$startqueryday}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' c.class_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,scl.changelog_day,scl.changelog_note,csr.reason_note,st.staffer_cnname,scl.changelog_createtime,cou.course_cnname,cou.course_branch
              ,(select l.timelog_finaltimes from smc_student_coursebalance_timelog as l where l.student_id=scl.student_id and l.timelog_time<=scl.changelog_createtime and l.school_id='{$this->school_id}' and (l.class_id=scl.class_id or c.course_id=l.course_id) order by l.timelog_time desc limit 0,1) as coursebalance_time
              ,(select l.log_finalamount from smc_student_coursebalance_log as l where l.student_id=scl.student_id and l.log_time<=scl.changelog_createtime and l.school_id='{$this->school_id}' and (l.class_id=scl.class_id or c.course_id=l.course_id) and l.log_class=0 order by l.log_time desc limit 0,1) as coursebalance_figure
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as cou on cou.course_id=c.course_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              where {$datawhere} and scl.stuchange_code='A07' and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}'
              order by scl.changelog_day desc,scl.changelog_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['reason_note'] = $dateexcelvar['reason_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['changelog_createtime'] = $dateexcelvar['changelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "班级名称", "班级英文名", "班级编号", "课程别名称", "课程别编号", "剩余课次", "课程别余额", "异动日期", "异动原因", "操作人", "操作时间");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'course_cnname', 'course_branch', 'coursebalance_time', 'coursebalance_figure', 'changelog_day', 'reason_note', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->schoolOne['school_cnname'] . '学员班级流失明细报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($list as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              where {$datawhere} and scl.stuchange_code='A07' and scl.company_id='{$this->company_id}' and scl.school_id='{$this->school_id}'
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //学员预收余额报表
    function stuAdvance($request)
    {

        $datawhere = "scb.school_id='{$this->school_id}' and scb.company_id='{$this->company_id}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or scb.coursecat_branch like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or scb.feetype_code like '%{$request['keyword']}%' or cf.feetype_name like '%{$request['keyword']}%')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and scb.coursecat_id='{$request['coursecat_id']}'";
        }

        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_branch, s.student_cnname, s.student_enname,scb.*,cf.feetype_name,cc.coursecat_cnname
              ,(select scbl.log_finalamount from smc_student_coursecatbalance_log as scbl where scbl.student_id=scb.student_id and scbl.coursecat_id=scb.coursecat_id and scbl.feetype_code=scb.feetype_code and scbl.school_id='{$this->school_id}' and scbl.log_time<'{$endqueryTimes}' order by scbl.log_time desc limit 0,1) as log_finalamount
              ,(select scbl.log_finaltime from smc_student_coursecatbalance_log as scbl where scbl.student_id=scb.student_id and scbl.coursecat_id=scb.coursecat_id and scbl.feetype_code=scb.feetype_code and scbl.school_id='{$this->school_id}' and scbl.log_time<'{$endqueryTimes}' order by scbl.log_time desc limit 0,1) as log_finaltime
              ,(select scbl.log_reason from smc_student_coursecatbalance_log as scbl where scbl.student_id=scb.student_id and scbl.coursecat_id=scb.coursecat_id and scbl.feetype_code=scb.feetype_code and scbl.school_id='{$this->school_id}' and scbl.log_time<'{$endqueryTimes}' order by scbl.log_time desc limit 0,1) as log_reason
              from smc_student_coursecatbalance as scb
              left join smc_student as s on s.student_id=scb.student_id
              left join smc_code_feetype as cf on cf.feetype_code=scb.feetype_code
              left join smc_code_coursecat as cc on cc.coursecat_id=scb.coursecat_id
              where {$datawhere} and scb.coursecatbalance_createtime<'{$endqueryTimes}'
              having (log_finalamount>0 or log_finaltime>0)
              order by scb.coursecatbalance_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预收余额";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['feetype_name'] = $dateexcelvar['feetype_name'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['log_finalamount'] = $dateexcelvar['log_finalamount'];
                    $datearray['log_finaltime'] = $dateexcelvar['log_finaltime'];
                    $datearray['log_reason'] = $dateexcelvar['log_reason'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("学员中文名", "学员英文名", "学员编号", "收费类型", "班种名称", "班种编号", "课程剩余余额", "课程剩余次数", "备注");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'feetype_name', 'coursecat_cnname', 'coursecat_branch', 'log_finalamount', 'log_finaltime', 'log_reason');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$this->schoolOne['school_cnname']}学员预收余额表.xlsx");
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select s.student_branch
              ,(select scbl.log_finalamount from smc_student_coursecatbalance_log as scbl where scbl.student_id=scb.student_id and scbl.coursecat_id=scb.coursecat_id and scbl.feetype_code=scb.feetype_code and scbl.school_id='{$this->school_id}' and scbl.log_time<'{$endqueryTimes}' order by scbl.log_time desc limit 0,1) as log_finalamount
              ,(select scbl.log_finaltime from smc_student_coursecatbalance_log as scbl where scbl.student_id=scb.student_id and scbl.coursecat_id=scb.coursecat_id and scbl.feetype_code=scb.feetype_code and scbl.school_id='{$this->school_id}' and scbl.log_time<'{$endqueryTimes}' order by scbl.log_time desc limit 0,1) as log_finaltime
              from smc_student_coursecatbalance as scb
              left join smc_student as s on s.student_id=scb.student_id
              left join smc_code_feetype as cf on cf.feetype_code=scb.feetype_code
              left join smc_code_coursecat as cc on cc.coursecat_id=scb.coursecat_id
              where {$datawhere} and scb.coursecatbalance_createtime<'{$endqueryTimes}'
              having (log_finalamount>0 or log_finaltime>0)
              limit 0,1";
                $dbCount = $this->DataControl->selectOne($count_sql);
                if ($dbCount) {
                    $allnum = $dbCount['stunums'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }
    }

    //班级学员信息报表
    function classInfoList($request)
    {

        $today = date("Y-m-d");
        $time = date("Y-m-d", time());
        if (isset($request['starttime']) && $request['starttime'] !== '') {

            $time = $request['starttime'];
            $week_start_day = date("Y-m-d", strtotime($time));

            $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));
        } else {

            $week_start_day = date("Y-m-d", strtotime($time));

            $week_end_day = date("Y-m-d", strtotime($time));
        }
        $datawhere = "1";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id='{$request['class_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and sc.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and cc.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_cnname,s.student_enname,s.student_branch,c.class_cnname,c.class_enname,c.class_branch,cc.coursecat_cnname,cc.coursecat_branch,sc.course_cnname,sc.course_branch,ss.study_beginday,ss.study_isreading
                from smc_student_study as ss
                left join smc_class as c on c.class_id=ss.class_id
                left join smc_student as s on s.student_id=ss.student_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
                where {$datawhere} and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}'
                and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' and ss.study_endday>='{$week_end_day}'
                order by ss.class_id desc
        ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                $isreading = array("0" => "不在读", "1" => "在读", "-1" => "已结束");
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    $datearray['study_isreading_name'] = $isreading[$dateexcelvar['study_isreading']];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("学员中文名", "学员英文名", "学员编号", "班级名称", '班级英文名', "班级编号", "班种名称", "班种编号", "课程别名称", "课程别编号", "入班日期", "在班状态");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'study_beginday', 'study_isreading_name');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}班级学员信息报表{$week_start_day}-{$week_end_day}.xlsx");
            exit;


        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $isreading = array("0" => "不在读", "1" => "在读", "-1" => "已结束");
            foreach ($classList as &$classOne) {
                $classOne['study_isreading_name'] = $isreading[$classOne['study_isreading']];
            }
            if (isset($request['is_count']) && $request['is_count'] == 1) {

                $count_sql = "select c.class_cnname,c.class_enname,c.class_branch,s.student_cnname
                              from smc_student_study as ss
                              left join smc_class as c on c.class_id=ss.class_id
                              left join smc_student as s on s.student_id=ss.student_id
                              left join smc_course as sc on sc.course_id=c.course_id
                              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
                              where {$datawhere} and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}'
                              and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' and ss.study_endday>='{$week_end_day}'";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;

            }
            $data['list'] = $classList;

            return $data;
        }

    }

    //学员免费课时报表
    function studentFreeCourseReport($request)
    {
        $datawhere = " and A.company_id='{$this->company_id}' 
        and A.school_id='{$this->school_id}' ";

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and g.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and A.course_id='{$request['course_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.student_cnname like '%{$request['keyword']}%' 
            or b.student_enname like '%{$request['keyword']}%' 
            or b.student_branch like '%{$request['keyword']}%' 
            or d.class_enname like '%{$request['keyword']}%' 
            or d.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $startqueryday = $request['start_time'];
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
            $datawhere .= " and a.order_updatatime>='{$starttime}'";
        } else {
            $startqueryday = "历史总计";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and a.order_updatatime<= '{$endtime}'";
        } else {
            $endqueryday = date('Y-m-d');
            $endtime = strtotime($endqueryday . ' 23:59:59');
            $datawhere .= " and a.order_updatatime<= '{$endtime}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.trading_pid,a.school_id,c.school_branch,c.school_cnname, 
            a.student_id,b.student_branch,b.student_cnname,e.channel_name, 
            g.coursecat_id,g.coursecat_branch,a.course_id,a.course_branch,a.order_alltimes, 
            a.class_id,d.class_branch,d.class_cnname,d.class_enname, 
            (select group_concat(hour_lessontimes) from smc_student_free_coursetimes where order_pid=a.order_pid ) as hour_lessontimes, 
            FROM_UNIXTIME(a.order_updatatime) as order_updatatime
            from smc_freehour_order a 
            left join smc_student b on a.student_id=b.student_id 
            left join smc_school c on a.school_id=c.school_id 
            left join smc_class d on a.class_id=d.class_id 
            left join smc_student_guildpolicy e on b.student_id=e.student_id and e.guildpolicy_enddate>=CURDATE()
            left join smc_course f on d.course_id=f.course_id 
            left join smc_code_coursecat g on f.coursecat_id=g.coursecat_id 
            where a.order_status=1 
            {$datawhere}            
            order by a.school_id,a.student_id,a.class_id 
        ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无免费课时数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['order_alltimes'] = $dateexcelvar['order_alltimes'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['hour_lessontimes'] = $dateexcelvar['hour_lessontimes'];
                    $datearray['order_updatatime'] = $dateexcelvar['order_updatatime'];
//                    $datearray['order_updatatime'] = date("Y-m-d H:i:s", $dateexcelvar['order_updatatime']);
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("交易编号", "校区编号", "校区名称", "学生编号", "学生姓名", "专案名称", "班种编号", "课程别编号", "赠送课次", "班级编号", "班级名称", "班级别名", "赠送指定课次", "赠送日期");
            $excelfileds = array('trading_pid', 'school_branch', 'school_cnname', 'student_branch', 'student_cnname', 'channel_name', 'coursecat_branch', 'course_branch', 'order_alltimes', 'class_branch', 'class_cnname', 'class_enname', 'hour_lessontimes', 'order_updatatime');
            $tem_name = $this->schoolOne['school_cnname'] . '学员课时赠送报表(' . $startqueryday . '~' . $endqueryday . ').xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);

            if (!$list) {
                $this->error = true;
                $this->errortip = "无免费课时数据";
                return false;
            }

            $count_sql = "select a.trading_pid 
            from smc_freehour_order a 
            left join smc_student b on a.student_id=b.student_id 
            left join smc_class d on a.class_id=d.class_id 
            left join smc_course f on d.course_id=f.course_id
            left join smc_code_coursecat g on f.coursecat_id=g.coursecat_id 
            where a.order_status=1 
            {$datawhere}";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }

    }

    function studentUnStudy($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (C.student_cnname like '%{$request['keyword']}%' 
            or C.student_enname like '%{$request['keyword']}%' 
            or C.student_idcard like '%{$request['keyword']}%' 
            or C.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and D.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and F.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " AND A.school_id = '{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT E.school_id
            ,E.school_branch
            ,E.school_cnname
            ,F.coursetype_id
            ,F.coursetype_branch
            ,F.coursetype_cnname
            ,D.coursecat_id
            ,D.coursecat_branch
            ,D.coursecat_cnname
            ,C.student_id
            ,C.student_branch
            ,C.student_cnname
            ,C.student_enname
            ,GROUP_CONCAT(B.course_branch) AS course_branches
            ,GROUP_CONCAT(B.course_cnname) AS course_cnnames
            ,SUM(A.coursebalance_figure) AS total_money
            ,SUM(A.coursebalance_time) AS total_times
            ,(select sp.parenter_mobile from smc_student_family as sf inner join smc_parenter as sp on sf.parenter_id=sp.parenter_id where sf.student_id=A.student_id and sf.family_isdefault='1' limit 0,1) as parenter_mobile
            ,(select max(y.clockinginlog_day) from smc_student_clockinginlog y,smc_student_hourstudy z,smc_class x,smc_course w 
              where y.hourstudy_id=z.hourstudy_id and z.class_id=x.class_id and x.course_id=w.course_id 
              and y.school_id=A.school_id and y.student_id=A.student_id and w.coursecat_id=D.coursecat_id) as coursecat_last_atte_date
            FROM smc_student_coursebalance A  
            LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id 
            LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id 
            LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id 
            INNER JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
            LEFT JOIN smc_code_coursetype F ON F.coursetype_id=D.coursetype_id AND F.company_id=D.company_id 
            WHERE {$datawhere} 
            AND A.company_id='{$this->company_id}' 
            AND B.course_inclasstype in (0,2) 
            AND A.coursebalance_time>0 
            -- AND A.coursebalance_status=0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z 
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id 
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id  
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id  
            AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1  
            AND X.study_endday>=CURDATE()) 
            GROUP BY E.school_id,C.student_id,D.coursecat_id 
            ORDER BY E.school_id,D.coursecat_id,C.student_id 
            ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学生待入班数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'] ? $dateexcelvar['parenter_mobile'] : '';
                    $datearray['course_branches'] = $dateexcelvar['course_branches'];
                    $datearray['course_cnnames'] = $dateexcelvar['course_cnnames'];
                    $datearray['total_money'] = $dateexcelvar['total_money'];
                    $datearray['total_times'] = $dateexcelvar['total_times'];
                    $datearray['coursecat_last_atte_date'] = $dateexcelvar['coursecat_last_atte_date'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("分校编号", "分校名称", "班组编号", "班组名称", "班种编号", "班种名称", "学生编号", "学生中文名", "学生英文名", "联系电话", "课程别编号", "课程别名称", "课程别余额", "剩余课次", "班种最后考勤日期"));
            $excelfileds = array('school_branch', 'school_cnname', 'coursetype_branch', 'coursetype_cnname', 'coursecat_branch', 'coursecat_cnname', 'student_branch', 'student_cnname', 'student_enname', 'parenter_mobile', 'course_branches', 'course_cnnames', 'total_money', 'total_times', 'coursecat_last_atte_date');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '班种待入班明细报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无学生待入班数据";
                return false;
            }

            $data = array();
            $count_sql = "SELECT E.school_id 
            ,D.coursecat_id 
            ,C.student_id 
            FROM smc_student_coursebalance A  
            LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id 
            LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id 
            LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id 
            LEFT JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
            LEFT JOIN smc_code_coursetype F ON F.coursetype_id=D.coursetype_id AND F.company_id=D.company_id 
            WHERE {$datawhere} 
            AND A.company_id='{$this->company_id}' 
            AND B.course_inclasstype in (0,1) 
            AND A.coursebalance_time>0 
            -- AND A.coursebalance_status=0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z 
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id 
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id  
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id  
            AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1  
            AND X.study_endday>=CURDATE()) 
            GROUP BY E.school_id,C.student_id,D.coursecat_id 
            ORDER BY E.school_id,D.coursecat_id,C.student_id 
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }

    }

    //延班报表
    function stuWaitClass($request)
    {
        $datawhere = "scl.stuchange_code='A07' and scl.company_id='{$this->company_id}'
             and not exists(select 1 from smc_student_study X,smc_class Y,smc_course Z where X.class_id=Y.class_id AND Y.company_id=Z.company_id
             AND Y.course_id=Z.course_id AND Z.coursetype_id=cou.coursetype_id AND X.student_id=scl.student_id AND X.school_id=scl.school_id AND X.study_endday>scl.changelog_day)
             and not exists(select 1 from smc_student_changelog as x where x.student_id=scl.student_id and x.school_id=scl.school_id 
             and (x.stuchange_code in ('B05','C02','C03') or (x.coursetype_id=cou.coursetype_id and x.stuchange_code='C04')) and x.changelog_day>scl.changelog_day) 
             and scb.coursebalance_time>0";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
            $datawhere .= " and scl.changelog_day<='{$endqueryday}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $startqueryday = $request['start_time'];
            $datawhere .= " and scl.changelog_day>='{$startqueryday}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and  scl.school_id='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and cou.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and cou.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or s.student_enname like '%{$request['keyword']}%' 
            or s.student_idcard like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%' 
            or c.class_cnname like '%{$request['keyword']}%' 
            or c.class_enname like '%{$request['keyword']}%' 
            or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and l.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND l.school_istest <> '1' AND l.school_isclose<> '1' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select l.school_id,l.school_branch,l.school_cnname,s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,scl.changelog_day,scl.changelog_note,csr.reason_note,concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ) ) as staffer_cnname,scl.changelog_createtime,cou.course_cnname,cou.course_branch
                ,scb.coursebalance_time
                ,scb.coursebalance_figure
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid and scl.company_id=sc.company_id
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as cou on cou.course_id=c.course_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              left join smc_school as l on scl.school_id=l.school_id
              left join smc_student_coursebalance scb on scb.student_id=scl.student_id and scb.school_id=c.school_id and scb.course_id=c.course_id
              where {$datawhere} 
              group by scl.changelog_id
              order by l.school_branch,scl.changelog_day desc,scl.changelog_createtime desc";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
//                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
//                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['reason_note'] = $dateexcelvar['reason_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['changelog_createtime'] = $dateexcelvar['changelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "班级名称", "班级别名", "班级编号", "课程别名称", "课程别编号", "剩余课次", "课程别余额", "异动日期", "异动原因", "操作人", "操作时间"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'course_cnname', 'course_branch', 'coursebalance_time', 'coursebalance_figure', 'changelog_day', 'reason_note', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '学员课程延班报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($list as &$one) {
                if ($one['reason_note'] == '' || $one['reason_note'] == null) {
                    $one['reason_note'] = $one['changelog_note'];
                }
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as cou on cou.course_id=c.course_id
              left join smc_school as l on scl.school_id=l.school_id
              left join smc_student_coursebalance scb on scb.student_id=scl.student_id and scb.school_id=c.school_id and scb.course_id=c.course_id
              where {$datawhere}
              group by scl.changelog_id 
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }

    }

    //学生课程余额
    function studentCourseBalancesLeftReport($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and A.company_id='{$request['company_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and C.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and D.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and E.coursetype_id='{$request['coursetype_id']}'";
        }

        $endqueryday = date("Y-m-d");
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
        }

        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.student_id,B.student_cnname,B.student_enname,B.student_branch,A.school_id 
        ,(select sum(G.student_balance+G.student_withholdbalance) from smc_student_balance G where G.student_id=B.student_id and G.school_id=A.school_id) as balance
        ,(SELECT SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END ) 
        FROM smc_student_balancelog as l WHERE l.balancelog_time >='{$endqueryTimes}' and l.student_id = A.student_id 
        and l.school_id = A.school_id and l.balancelog_class IN('0','2')) as plus_balance 
        ,(SELECT sum(if(l.log_playclass='+', l.log_playamount, -l.log_playamount)) FROM smc_student_coursebalance_log as l WHERE l.log_time >= '{$endqueryTimes}' and l.student_id = A.student_id 
        and l.course_id = A.course_id and l.school_id = A.school_id and l.log_class='0') as plus_figure
        ,(SELECT sum(if(l.timelog_playclass='+', l.timelog_playtimes, -l.timelog_playtimes)) FROM smc_student_coursebalance_timelog as l WHERE l.timelog_time >= '{$endqueryTimes}' and l.student_id = A.student_id 
        and l.course_id = A.course_id and l.school_id = A.school_id ) as plus_time
        ,J.companies_cnname
        ,E.coursetype_id,E.coursetype_cnname,E.coursetype_branch
        ,D.coursecat_id,D.coursecat_cnname,D.coursecat_branch
        ,C.course_id,C.course_cnname,C.course_branch
        ,A.coursebalance_figure,A.coursebalance_time,A.coursebalance_unitexpend
              from smc_student_coursebalance as A
              left join smc_student as B on B.student_id=A.student_id AND B.company_id=A.company_id
              left join smc_course as C on C.course_id=A.course_id AND C.company_id=A.company_id
              left join smc_code_coursecat as D on D.coursecat_id=C.coursecat_id AND C.company_id=D.company_id
              left join smc_code_coursetype as E on E.coursetype_id=D.coursetype_id AND E.company_id=D.company_id
              left join gmc_code_companies J ON A.companies_id=J.companies_id
              WHERE {$datawhere}
              AND A.coursebalance_createtime < '{$endqueryTimes}'
            HAVING (coursebalance_figure-ifnull(plus_figure,0)>0 or coursebalance_time-ifnull(plus_time,0)>0)
            ORDER BY A.student_id desc,D.coursecat_id,c.course_id
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                $one['student_balance'] = $one['balance'] - ($one['plus_balance'] ? $one['plus_balance'] : 0);
                $one['coursebalance_figure'] = $one['coursebalance_figure'] - ($one['plus_figure'] ? $one['plus_figure'] : 0);
                $one['coursebalance_time'] = $one['coursebalance_time'] - ($one['plus_time'] ? $one['plus_time'] : 0);

                $sql = "select cl.class_cnname,cl.class_enname,cl.class_branch,cl.class_id,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_student_clockinginlog as scl on scl.student_id=sh.student_id and scl.hourstudy_id=sh.hourstudy_id where sh.class_id=cl.class_id and sh.student_id='{$one['student_id']}' and scl.clockinginlog_createtime<'{$endqueryTimes}') as clockNum
                      ,(case when ss.study_beginday>'$endqueryday}' then '不在读' when ss.study_endday<'{$endqueryday}' then '已结束' else '在读' END) as class_status
                      from smc_student_study as ss
                      left join smc_class as cl on cl.class_id=ss.class_id
                      where cl.course_id='{$one['course_id']}' and ss.student_id='{$one['student_id']}' and ss.school_id='{$one['school_id']}'";
                $classOne = $this->DataControl->selectOne($sql);
                $one['class_cnname'] = $classOne ? $classOne['class_cnname'] : '--';
                $one['class_enname'] = $classOne ? $classOne['class_enname'] : '--';
                $one['class_branch'] = $classOne ? $classOne['class_branch'] : '--';
                $one['class_id'] = $classOne ? $classOne['class_id'] : '--';
                $one['clockNum'] = $classOne ? $classOne['clockNum'] : '--';
                $one['class_status'] = $classOne ? $classOne['class_status'] : '--';
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_unitexpend'] = $dateexcelvar['coursebalance_unitexpend'];
                    $datearray['student_balance'] = $dateexcelvar['student_balance'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'] ? $dateexcelvar['class_cnname'] : '--';
                    $datearray['class_enname'] = $dateexcelvar['class_enname'] ? $dateexcelvar['class_enname'] : '--';
                    $datearray['class_branch'] = $dateexcelvar['class_branch'] ? $dateexcelvar['class_branch'] : '--';
                    $datearray['clockNum'] = $dateexcelvar['clockNum'] ? $dateexcelvar['clockNum'] : '0';
                    $datearray['class_status'] = $dateexcelvar['class_status'] ? $dateexcelvar['class_status'] : '--';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员ID", "学员中文名", "学员英文名", "学员编号", "主体名称", "班组名称", "班组编号", "班种名称", "班种编号", "课程别名称", "课程别编号", "课程余额", "剩余课程", '耗课单价', "账户余额", "班级名称", "班级别名", "班级编号", "班内耗课次数", "在班状态"));
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'companies_cnname', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time', 'coursebalance_unitexpend', 'student_balance', 'class_cnname', 'class_enname', 'class_branch', 'clockNum', 'class_status');
            $tem_name = $this->LgStringSwitch($schoolOne['school_cnname'] . '学员课程余额明细报表' . $endqueryday . '.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);

            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学生数据";
                return false;
            }

            foreach ($studentList as &$one) {
                $one['student_balance'] = $one['balance'] - ($one['plus_balance'] ? $one['plus_balance'] : 0);
                $one['coursebalance_figure'] = $one['coursebalance_figure'] - ($one['plus_figure'] ? $one['plus_figure'] : 0);
                $one['coursebalance_time'] = $one['coursebalance_time'] - ($one['plus_time'] ? $one['plus_time'] : 0);

                $sql = "select cl.class_cnname,cl.class_enname,cl.class_branch,cl.class_id,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_student_clockinginlog as scl on scl.student_id=sh.student_id and scl.hourstudy_id=sh.hourstudy_id where sh.class_id=cl.class_id and sh.student_id='{$one['student_id']}' and scl.clockinginlog_createtime<'{$endqueryTimes}') as clockNum
                      ,(case when ss.study_beginday>'{$endqueryday}' then '不在读' when ss.study_endday<'{$endqueryday}' then '已结束' else '在读' END) as class_status
                      from smc_student_study as ss
                      left join smc_class as cl on cl.class_id=ss.class_id
                      where cl.course_id='{$one['course_id']}' and ss.student_id='{$one['student_id']}' and ss.school_id='{$one['school_id']}'";
                $classOne = $this->DataControl->selectOne($sql);
                $one['class_cnname'] = $classOne ? $classOne['class_cnname'] : '--';
                $one['class_enname'] = $classOne ? $classOne['class_enname'] : '--';
                $one['class_branch'] = $classOne ? $classOne['class_branch'] : '--';
                $one['class_id'] = $classOne ? $classOne['class_id'] : '--';
                $one['clockNum'] = $classOne ? $classOne['clockNum'] : '--';
                $one['class_status'] = $this->LgStringSwitch($classOne ? $classOne['class_status'] : '--');
            }
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectOne("select count(ta.student_id) as num from (select
        (SELECT sum(if(l.log_playclass='+', l.log_playamount, -l.log_playamount)) FROM smc_student_coursebalance_log as l WHERE l.log_time >= '{$endqueryTimes}' and l.student_id = A.student_id 
        and l.course_id = A.course_id and l.school_id = A.school_id and l.log_class='0') as plus_figure
        ,(SELECT sum(if(l.timelog_playclass='+', l.timelog_playtimes, -l.timelog_playtimes)) FROM smc_student_coursebalance_timelog as l WHERE l.timelog_time >= '{$endqueryTimes}' and l.student_id = A.student_id 
        and l.course_id = A.course_id and l.school_id = A.school_id ) as plus_time
        ,A.coursebalance_figure,A.coursebalance_time             
        from smc_student_coursebalance as A
              left join smc_student as B on B.student_id=A.student_id AND B.company_id=A.company_id
              left join smc_course as C on C.course_id=A.course_id AND C.company_id=A.company_id
              left join smc_code_coursecat as D on D.coursecat_id=C.coursecat_id AND C.company_id=D.company_id
              left join smc_code_coursetype as E on E.coursetype_id=D.coursetype_id AND E.company_id=D.company_id
              WHERE {$datawhere}
              AND A.coursebalance_createtime < '{$endqueryTimes}'
            HAVING (coursebalance_figure-ifnull(plus_figure,0)>0 or coursebalance_time-ifnull(plus_time,0)>0)) as ta");

                if ($cuntStunums) {
                    $allnum = $cuntStunums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;

            return $data;
        }
    }

    //学员资产余额
    function studentProperty($request)
    {
        $datawhere = " B.company_id='{$request['company_id']}'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
            or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' 
            or B.student_branch like '%{$request['keyword']}%')";
        }
        $endqueryday = date("Y-m-d");
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT A.school_id,A.student_id,B.company_id ,C.school_branch ,C.school_shortname
                ,B.student_branch 
                ,B.student_cnname 
                ,B.student_enname 
                
                ,(SELECT sum(itemtimes_figure) FROM smc_student_itemtimes WHERE student_id=A.student_id and company_id=B.company_id and school_id=A.school_id) AS itembalance_now 
                ,(SELECT sum(case when X.log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_itemtimes_log X,smc_student_itemtimes Y 
                WHERE X.itemtimes_id=Y.itemtimes_id AND Y.student_id=A.student_id and Y.company_id=B.company_id 
                and Y.school_id=A.school_id AND X.log_time>='{$endqueryTimes}') AS itembalance_plus 
                
                ,(select sum(D.student_balance+D.student_withholdbalance) from smc_student_balance D where D.student_id=A.student_id AND D.school_id=A.school_id AND D.company_id=B.company_id) AS balance_now
                ,(SELECT SUM(CASE WHEN balancelog_playclass = '+' THEN balancelog_playamount ELSE -balancelog_playamount END) 
                FROM smc_student_balancelog WHERE student_id=A.student_id AND balancelog_class IN('0','2') AND school_id=A.school_id AND company_id=B.company_id
                AND school_id=A.school_id AND balancelog_time>='{$endqueryTimes}') AS balance_plus 
                
                ,(select sum(trading_price) from smc_school_trading x where x.student_id=a.student_id and x.trading_createtime<='{$endqueryTimes}'and (trading_status<2 or x.trading_updatatime>='{$endqueryTimes}')) as trading_price
                
                ,(SELECT sum(coursebalance_figure) FROM smc_student_coursebalance WHERE student_id=A.student_id and company_id=B.company_id and school_id=A.school_id) AS coursebalance_now 
                ,(SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursebalance_log x WHERE student_id=A.student_id 
                and school_id=A.school_id AND log_time>='{$endqueryTimes}' and log_class='0'
                and not exists(select 1 from smc_payfee_order where student_id=x.student_id and school_id=x.school_id and trading_pid=x.trading_pid and order_status=-1)) AS coursebalance_plus 
                
                ,(SELECT sum(order_arrearageprice) FROM smc_payfee_order WHERE student_id=A.student_id and company_id=B.company_id  and order_type<>2
                and school_id=A.school_id and order_createtime<'{$endqueryTimes}' and order_status>0) AS unpaid_now 
                ,(SELECT sum(pay_price) FROM smc_payfee_order_pay X,smc_payfee_order Y WHERE X.order_pid=Y.order_pid AND Y.student_id=A.student_id and Y.company_id=B.company_id  and Y.order_type<>2
                and Y.school_id=A.school_id AND X.pay_successtime>='{$endqueryTimes}' and Y.order_createtime<'{$endqueryTimes}' and Y.order_status>0) AS unpaid_plus 
                
                ,(SELECT sum(Y.ordergoods_totalprice) from smc_student_erpgoods X,smc_payfee_order_goods Y 
                WHERE 1  AND X.student_id=A.student_id and school_id=A.school_id
                AND X.order_pid=Y.order_pid AND X.goods_id=Y.goods_id AND X.erpgoods_isfree=0 and X.erpgoods_isrefund=0 
                AND X.erpgoods_createtime<='{$endqueryTimes}' AND (X.erpgoods_receivetime>'{$endqueryTimes}' OR X.erpgoods_isreceive=0)) AS goods_left

                FROM smc_student_enrolled A 
                LEFT JOIN smc_student B ON B.student_id =A.student_id 
                LEFT JOIN smc_school C ON C.school_id=A.school_id
                WHERE {$datawhere}
                having ((ifnull(itembalance_now,0)-ifnull(itembalance_plus,0))<>0 
                or (ifnull(balance_now,0)-ifnull(balance_plus,0))<>0 
                or (ifnull(coursebalance_now,0)-ifnull(coursebalance_plus,0))<>0 
                or (ifnull(unpaid_now,0)+ifnull(unpaid_plus,0))<>0 
                or ifnull(goods_left,0)<>0
                or ifnull(trading_price,0)<>0)
                ORDER BY A.student_id ";

//        , (SELECT sum(coursecatbalance_figure) FROM smc_student_coursecatbalance WHERE student_id = A . student_id and company_id = B . company_id and school_id = A . school_id) as catbalance_now
//                ,(SELECT sum(case when log_playclass = '+' THEN log_playamount else -log_playamount END) FROM smc_student_coursecatbalance_log WHERE student_id = A . student_id
//    and school_id = A . school_id and log_time >= '{$endqueryTimes}') as catbalance_plus
//    or (ifnull(catbalance_now, 0) - ifnull(catbalance_plus, 0)) <> 0
//        ,(SELECT sum(property_integralbalance) FROM smc_student_virtual_property WHERE student_id=A.student_id ) AS integral_now
//                ,(SELECT sum(case when integrallog_playclass='+' THEN integrallog_playamount ELSE -integrallog_playamount END) FROM smc_student_integrallog
//                WHERE student_id=A.student_id AND integrallog_time>='{$endqueryTimes}') AS integral_plus
//        or (ifnull(integral_plus,0)-ifnull(integral_plus,0))<>0


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员资产数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['itembalance_left'] = ($dateexcelvar['itembalance_now'] ? $dateexcelvar['itembalance_now'] : 0) - ($dateexcelvar['itembalance_plus'] ? $dateexcelvar['itembalance_plus'] : 0);
                    $datearray['balance_left'] = ($dateexcelvar['balance_now'] ? $dateexcelvar['balance_now'] : 0) - ($dateexcelvar['balance_plus'] ? $dateexcelvar['balance_plus'] : 0);
//                    $datearray['catbalance_left'] = ($dateexcelvar['catbalance_now'] ? $dateexcelvar['catbalance_now'] : 0) - ($dateexcelvar['catbalance_plus'] ? $dateexcelvar['catbalance_plus'] : 0);
                    $datearray['coursebalance_left'] = ($dateexcelvar['coursebalance_now'] ? $dateexcelvar['coursebalance_now'] : 0) - ($dateexcelvar['coursebalance_plus'] ? $dateexcelvar['coursebalance_plus'] : 0);
                    $datearray['unpaid_left'] = ($dateexcelvar['unpaid_now'] ? $dateexcelvar['unpaid_now'] : 0) + ($dateexcelvar['unpaid_plus'] ? $dateexcelvar['unpaid_plus'] : 0);
                    $datearray['goods_left'] = ($dateexcelvar['goods_left'] ? $dateexcelvar['goods_left'] : 0);
                    $datearray['trading_price'] = ($dateexcelvar['trading_price'] ? $dateexcelvar['trading_price'] : 0);
//                    $datearray['integral_left'] = ($dateexcelvar['integral_now'] ? $dateexcelvar['integral_now'] : 0) - ($dateexcelvar['integral_plus'] ? $dateexcelvar['integral_plus'] : 0);
                    $datearray['property_left'] = $datearray['goods_left'] + $datearray['itembalance_left'] + $datearray['balance_left'] + $datearray['coursebalance_left'] - $datearray['unpaid_left'];// + $datearray['catbalance_left']

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员ID", "学员中文名", "学员英文名", "学员编号", "杂费余额", "账户余额", "课程余额", "订单欠费", "待领用资产", "在途资产", "资产总额"));//, "预收余额", "剩余积分"
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'itembalance_left', 'balance_left', 'coursebalance_left', 'unpaid_left', 'goods_left', 'trading_price', 'property_left');//, 'catbalance_left', "integral_left"
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员账户余额明细报表{$endqueryday}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员资产数据";
                return false;
            }

            foreach ($studentList as &$studentOne) {
                $studentOne['itembalance_left'] = ($studentOne['itembalance_now'] ? $studentOne['itembalance_now'] : 0) - ($studentOne['itembalance_plus'] ? $studentOne['itembalance_plus'] : 0);
                $studentOne['balance_left'] = ($studentOne['balance_now'] ? $studentOne['balance_now'] : 0) - ($studentOne['balance_plus'] ? $studentOne['balance_plus'] : 0);
//                $studentOne['catbalance_left'] = ($studentOne['catbalance_now'] ? $studentOne['catbalance_now'] : 0) - ($studentOne['catbalance_plus'] ? $studentOne['catbalance_plus'] : 0);
                $studentOne['coursebalance_left'] = ($studentOne['coursebalance_now'] ? $studentOne['coursebalance_now'] : 0) - ($studentOne['coursebalance_plus'] ? $studentOne['coursebalance_plus'] : 0);
                $studentOne['unpaid_left'] = ($studentOne['unpaid_now'] ? $studentOne['unpaid_now'] : 0) + ($studentOne['unpaid_plus'] ? $studentOne['unpaid_plus'] : 0);
                $studentOne['goods_left'] = ($studentOne['goods_left'] ? $studentOne['goods_left'] : 0);
//                $studentOne['integral_left'] = ($studentOne['integral_now'] ? $studentOne['integral_now'] : 0) - ($studentOne['integral_plus'] ? $studentOne['integral_plus'] : 0);
                $studentOne['property_left'] = $studentOne['goods_left'] + $studentOne['itembalance_left'] + $studentOne['balance_left'] + $studentOne['coursebalance_left'] - $studentOne['unpaid_left'];//+ $studentOne['catbalance_left']
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("SELECT A.student_id
                ,(SELECT sum(itemtimes_figure) FROM smc_student_itemtimes WHERE student_id=A.student_id and company_id=B.company_id and school_id=A.school_id) AS itembalance_now 
                ,(SELECT sum(case when X.log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_itemtimes_log X,smc_student_itemtimes Y 
                WHERE X.itemtimes_id=Y.itemtimes_id AND Y.student_id=A.student_id and Y.company_id=B.company_id 
                and Y.school_id=A.school_id AND X.log_time>='{$endqueryTimes}') AS itembalance_plus 
                
                ,(select sum(D.student_balance+D.student_withholdbalance) from smc_student_balance D where D.student_id=A.student_id AND D.school_id=A.school_id AND D.company_id=B.company_id) AS balance_now
                ,(SELECT SUM(CASE WHEN balancelog_playclass = '+' THEN balancelog_playamount ELSE -balancelog_playamount END) 
                FROM smc_student_balancelog WHERE student_id=A.student_id AND balancelog_class IN('0','2') AND school_id=A.school_id AND company_id=B.company_id
                AND school_id=A.school_id AND balancelog_time>='{$endqueryTimes}') AS balance_plus 
                
                ,(select sum(trading_price) from smc_school_trading x where x.student_id=a.student_id and x.trading_createtime<='{$endqueryTimes}'and (trading_status<2 or x.trading_updatatime>='{$endqueryTimes}')) as trading_price
                
                ,(SELECT sum(coursebalance_figure) FROM smc_student_coursebalance WHERE student_id=A.student_id and company_id=B.company_id and school_id=A.school_id) AS coursebalance_now 
                ,(SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursebalance_log x WHERE student_id=A.student_id 
                and school_id=A.school_id AND log_time>='{$endqueryTimes}' and log_class='0'
                and not exists(select 1 from smc_payfee_order where student_id=x.student_id and school_id=x.school_id and trading_pid=x.trading_pid and order_status=-1)) AS coursebalance_plus 
                
                ,(SELECT sum(order_arrearageprice) FROM smc_payfee_order WHERE student_id=A.student_id and company_id=B.company_id 
                and school_id=A.school_id and order_createtime<'{$endqueryTimes}' and order_status>0) AS unpaid_now 
                ,(SELECT sum(pay_price) FROM smc_payfee_order_pay X,smc_payfee_order Y WHERE X.order_pid=Y.order_pid AND Y.student_id=A.student_id and Y.company_id=B.company_id 
                and Y.school_id=A.school_id AND X.pay_successtime>='{$endqueryTimes}' and Y.order_createtime<'{$endqueryTimes}' and Y.order_status>0) AS unpaid_plus 
                
                ,(SELECT sum(Y.ordergoods_totalprice) from smc_student_erpgoods X,smc_payfee_order_goods Y 
                WHERE 1  AND X.student_id=A.student_id and school_id=A.school_id
                AND X.order_pid=Y.order_pid AND X.goods_id=Y.goods_id AND X.erpgoods_isfree=0
                AND X.erpgoods_createtime<='{$endqueryTimes}' AND (X.erpgoods_receivetime>'{$endqueryTimes}' OR X.erpgoods_isreceive=0)) AS goods_left

                FROM smc_student_enrolled A 
                LEFT JOIN smc_student B ON B.student_id =A.student_id 
                LEFT JOIN smc_school C ON C.school_id=A.school_id
                WHERE {$datawhere}
                having ((ifnull(itembalance_now,0)-ifnull(itembalance_plus,0))<>0 
                or (ifnull(balance_now,0)-ifnull(balance_plus,0))<>0 
                or (ifnull(coursebalance_now,0)-ifnull(coursebalance_plus,0))<>0 
                or (ifnull(unpaid_now,0)+ifnull(unpaid_plus,0))<>0 
                or ifnull(goods_left,0)<>0
                or ifnull(trading_price,0)<>0)
                ORDER BY A.student_id ");

//                ,(SELECT sum(coursecatbalance_figure) FROM smc_student_coursecatbalance WHERE student_id=A.student_id and company_id=B.company_id and school_id=A.school_id) AS catbalance_now
//                ,(SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursecatbalance_log WHERE student_id=A.student_id
//                and school_id=A.school_id AND log_time>='{$endqueryTimes}') AS catbalance_plus
//                or (ifnull(catbalance_now,0)-ifnull(catbalance_plus,0))<>0
//                ,(SELECT sum(property_integralbalance) FROM smc_student_virtual_property WHERE student_id=A.student_id ) AS integral_now
//                ,(SELECT sum(case when integrallog_playclass='+' THEN integrallog_playamount ELSE -integrallog_playamount END) FROM smc_student_integrallog
//                WHERE student_id=A.student_id AND integrallog_time>='{$endqueryTimes}') AS integral_plus
//                or (ifnull(integral_plus,0)-ifnull(integral_plus,0))<>0

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;
            return $data;
        }
    }

    //账户余额表
    function studentTimebalance($request)
    {
        $datawhere = "M.company_id='{$request['company_id']}' 
                 and M.school_id='{$request['school_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or s.student_enname like '%{$request['keyword']}%' 
            or s.student_idcard like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%')";
        }
        $endqueryday = date("Y-m-d");
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT L.school_id,L.school_branch,L.school_cnname,
        s.student_id,
        s.student_cnname,
        s.student_enname,
        s.student_branch,
        sum(M.student_balance) as student_balance,
        sum(M.student_withholdbalance) as student_withholdbalance,
        (SELECT	SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END )
        FROM	smc_student_balancelog AS l WHERE	l.balancelog_time >= '{$endqueryTimes}' AND l.student_id = M.student_id 
        AND l.school_id = M.school_id AND l.balancelog_class = '0') AS plus_balance,
        (SELECT	l.balancelog_finalamount 
        FROM	smc_student_balancelog AS l WHERE	l.balancelog_time < '{$endqueryTimes}' AND l.student_id = M.student_id 
        AND l.school_id = M.school_id AND l.balancelog_class = '1' ORDER BY	l.balancelog_id DESC 	LIMIT 0,1) AS forwardprice,
        (SELECT	SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END ) 
        FROM smc_student_balancelog AS l WHERE l.balancelog_time >= '{$endqueryTimes}' AND l.student_id = M.student_id 
        AND l.school_id = M.school_id AND l.balancelog_class = '2' ) AS plus_withholdbalance 
        FROM smc_student_balance AS M 
        LEFT JOIN smc_student S ON M.company_id = S.company_id AND M.student_id = S.student_id 
        LEFT JOIN SMC_SCHOOL L ON M.SCHOOL_ID=L.SCHOOL_ID AND M.company_id=L.company_id
        WHERE {$datawhere}	
        group by m.school_id,m.student_id
        HAVING ( student_balance - IFNULL(plus_balance,0) > 0 OR student_withholdbalance - IFNULL(plus_withholdbalance,0) > 0 OR forwardprice > 0 ) 
        ORDER BY L.school_branch,s.student_id DESC ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['balance'] = $dateexcelvar['student_balance'] - ($dateexcelvar['plus_balance'] ? $dateexcelvar['plus_balance'] : 0);
                    $datearray['withholdbalance'] = $dateexcelvar['student_withholdbalance'] - ($dateexcelvar['plus_withholdbalance'] ? $dateexcelvar['plus_withholdbalance'] : 0);
                    $datearray['allprice'] = $datearray['balance'] + $datearray['withholdbalance'];
                    $datearray['forwardprice'] = $dateexcelvar['forwardprice'] ? $dateexcelvar['forwardprice'] : 0;

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学校ID", "分校编号", "分校名称", "学员ID", "学员中文名", "学员英文名", "学员编号", "账户可退余额", "账户不可退余额", "账户总余额", "账户结转金额"));
            $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'student_id', 'student_cnname', 'student_enname', 'student_branch', 'balance', 'withholdbalance', 'allprice', 'forwardprice');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员账户余额明细报表{$endqueryday}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            foreach ($studentList as &$studentOne) {
                $studentOne['balance'] = $studentOne['student_balance'] - ($studentOne['plus_balance'] ? $studentOne['plus_balance'] : 0);
                $studentOne['withholdbalance'] = $studentOne['student_withholdbalance'] - ($studentOne['plus_withholdbalance'] ? $studentOne['plus_withholdbalance'] : 0);
                $studentOne['allprice'] = $studentOne['withholdbalance'] + $studentOne['balance'];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("SELECT
                s.student_id,
                sum(M.student_balance) as student_balance,
                sum(M.student_withholdbalance) as student_withholdbalance,
                (SELECT	SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END )
                FROM	smc_student_balancelog AS l WHERE	l.balancelog_time >= '{$endqueryTimes}' AND l.student_id = M.student_id 
                AND l.school_id = M.school_id AND l.balancelog_class = '0') AS plus_balance,
                (SELECT	l.balancelog_finalamount 
                FROM	smc_student_balancelog AS l WHERE	l.balancelog_time < '{$endqueryTimes}' AND l.student_id = M.student_id 
                AND l.school_id = M.school_id AND l.balancelog_class = '1' ORDER BY	l.balancelog_id DESC 	LIMIT 0,1) AS forwardprice,
                (SELECT	SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END ) 
                FROM smc_student_balancelog AS l WHERE l.balancelog_time >= '{$endqueryTimes}' AND l.student_id = M.student_id 
                AND l.school_id = M.school_id AND l.balancelog_class = '2' ) AS plus_withholdbalance 
                FROM smc_student_balance AS M 
                LEFT JOIN smc_student S ON M.company_id = S.company_id AND M.student_id = S.student_id 
                WHERE {$datawhere}	
                group by m.school_id,m.student_id
                HAVING ( student_balance - IFNULL(plus_balance,0) > 0 OR student_withholdbalance - IFNULL(plus_withholdbalance,0) > 0 OR forwardprice > 0 ) 
                ORDER BY s.student_id DESC ");
                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;
            return $data;
        }
    }

    //杂费余额
    function stuItemLeftReport($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
            or B.student_enname like '%{$request['keyword']}%' 
            or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and D.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and D.course_id='{$request['course_id']}'";
        }

        if (isset($request['feetype_code']) && $request['feetype_code'] !== '') {
            $datawhere .= " and E.feeitem_class = '{$request['feetype_code']}'";
        }

        $endtime = date('Y-m-d');
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = $request['end_time'];
        } else {
            $endtime = date('Y-m-d');
        }

        $starttime = strtotime($endtime . ' 23:59:59');
        $datawhere .= " and A.itemtimes_createtime <= '{$starttime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT  C.school_branch 
                ,C.school_cnname 
                ,F.coursecat_branch 
                ,F.coursecat_cnname 
                ,D.course_branch 
                ,D.course_cnname 
                ,B.student_branch 
                ,B.student_cnname 
                ,B.student_enname 
                ,E.feeitem_branch 
                ,E.feeitem_cnname 
                ,E.feeitem_class 
                ,A.itemtimes_figure 
                ,A.itemtimes_number 
                ,(SELECT sum(case when log_playclass='-' THEN log_playamount ELSE -log_playamount END) FROM smc_student_itemtimes_log
                WHERE itemtimes_id=A.itemtimes_id AND student_id=A.student_id and itemtimes_id=a.itemtimes_id AND log_time>'{$starttime}') AS minus_amount
                ,(SELECT  sum(case when log_playclass='-' THEN 1 ELSE 0 END) FROM smc_student_itemtimes_log 
                WHERE itemtimes_id=A.itemtimes_id AND student_id=A.student_id and itemtimes_id=a.itemtimes_id AND log_time>'{$starttime}') AS minus_number
                FROM smc_student_itemtimes A 
                LEFT JOIN smc_student B ON A.student_id =B.student_id AND A.company_id=B.company_id 
                LEFT JOIN smc_school C ON A.school_id=C.school_id AND A.company_id=C.company_id 
                LEFT JOIN smc_course D ON A.course_id=D.course_id AND A.company_id=D.company_id 
                LEFT JOIN smc_code_feeitem E ON A.feeitem_id=E.feeitem_id AND A.company_id=E.company_id 
                left join smc_code_coursecat as F on F.coursecat_id=D.coursecat_id AND D.company_id=F.company_id
                WHERE {$datawhere} 
                and A.company_id='{$request['company_id']}'
                and A.school_id='{$request['school_id']}'
                having ((A.itemtimes_figure+IFNULL(minus_amount,0))>0 or (itemtimes_number+IFNULL(minus_number,0))>0)
                order by C.school_branch,B.student_branch,D.course_branch,E.feeitem_branch
              ";
        $feeitem_class = array("0" => "课程杂费", "1" => "普通杂费");

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['feeitem_class'] = $feeitem_class[$dateexcelvar['feeitem_class']];
                    $datearray['feeitem_cnname'] = $dateexcelvar['feeitem_cnname'];
                    $datearray['left_figure'] = $dateexcelvar['itemtimes_figure'] + ($dateexcelvar['minus_amount'] ? $dateexcelvar['minus_amount'] : 0);
                    $datearray['left_number'] = $dateexcelvar['itemtimes_number'] + $dateexcelvar['minus_number'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "班种名称", "班种编号", "课程别名称", "课程别编号", "杂费类型", "杂费名称", "剩余金额", "剩余次数"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'feeitem_class', 'feeitem_cnname', 'left_figure', 'left_number');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员杂项费用余额报表{$request['fixedtime']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $itemList = $this->DataControl->selectClear($sql);
            if (!$itemList) {
                $this->error = true;
                $this->errortip = "学员无杂项费用余额数据";
                return false;
            }
            $data = array();
            foreach ($itemList as &$val) {
                $val['left_figure'] = $val['itemtimes_figure'] + ($val['minus_amount'] ? $val['minus_amount'] : 0);
                $val['left_number'] = $val['itemtimes_number'] + $val['minus_number'];
                $val['feeitem_class'] = $feeitem_class[$val['feeitem_class']];
            }


            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT  
                A.itemtimes_id 
                ,A.itemtimes_figure 
                ,A.itemtimes_number 
                ,(SELECT sum(case when log_playclass='-' THEN log_playamount ELSE -log_playamount END) FROM smc_student_itemtimes_log
                WHERE itemtimes_id=A.itemtimes_id AND student_id=A.student_id and itemtimes_id=a.itemtimes_id AND log_time>'{$starttime}') AS minus_amount
                ,(SELECT  sum(case when log_playclass='-' THEN 1 ELSE 0 END) FROM smc_student_itemtimes_log 
                WHERE itemtimes_id=A.itemtimes_id AND student_id=A.student_id and itemtimes_id=a.itemtimes_id AND log_time>'{$starttime}') AS minus_number
                FROM smc_student_itemtimes A 
                LEFT JOIN smc_student B ON A.student_id =B.student_id AND A.company_id=B.company_id 
                LEFT JOIN smc_school C ON A.school_id=C.school_id AND A.company_id=C.company_id 
                LEFT JOIN smc_course D ON A.course_id=D.course_id AND A.company_id=D.company_id 
                LEFT JOIN smc_code_feeitem E ON A.feeitem_id=E.feeitem_id AND A.company_id=E.company_id 
                left join smc_code_coursecat as F on F.coursecat_id=D.coursecat_id AND D.company_id=F.company_id
                WHERE {$datawhere} 
                and A.company_id='{$request['company_id']}'  
                and A.school_id='{$request['school_id']}'  
                having ((A.itemtimes_figure+IFNULL(minus_amount,0))>0 or (itemtimes_number+IFNULL(minus_number,0))>0)
                order by C.school_branch,B.student_branch,D.course_branch,E.feeitem_branch 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $itemList;

            return $data;
        }
    }

    //学生课次购买报表
    function stuCoursePurchase($request)
    {
        $datawhere = " sc.company_id='{$request['company_id']}' ";
        $endqueryday = date("Y-m-d");

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and scp.school_id='{$request['school_id']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $startqueryTimes = strtotime($request['start_time']);
        } else {
            $startqueryTimes = strtotime($endqueryday) - 3600 * 24 * 30;
        }
        $datawhere .= " and scp.pricinglog_createtime>='{$startqueryTimes}'";
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
            $endqueryTimes = strtotime($endqueryday) + (3600 * 24);
        } else {
            $endqueryTimes = strtotime($endqueryday) + (3600 * 24);
        }
        $datawhere .= " and scp.pricinglog_createtime<'{$endqueryTimes}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and scy.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select l.school_id,l.school_branch,l.school_cnname,
              s.student_id,s.student_cnname,s.student_enname,s.student_branch
              ,sc.course_cnname,sc.course_branch,scp.pricinglog_buytimes,scp.pricinglog_buyprice,scp.pricinglog_createtime
              ,sct.coursecat_cnname,sct.coursecat_branch,scp.order_pid,scy.coursetype_cnname,scy.coursetype_branch
              ,(select scl.stuchange_code from smc_student_changelog as scl left join smc_class as c on c.class_id=scl.class_id where c.course_id=scp.course_id AND scl.student_id = scp.student_id and scl.company_id='{$this->company_id}' and scl.school_id=scp.school_id and scl.stuchange_code in (select cs.stuchange_code from smc_code_stuchange as cs where cs.stuchange_type=0) and scl.changelog_createtime<'{$endqueryTimes}' and scl.changelog_createtime >='{$startqueryTimes}' order by scl.changelog_createtime desc limit 0,1) as stuchange_code
              ,(select l.timelog_finaltimes from smc_student_coursebalance_timelog as l where l.student_id=scp.student_id and l.school_id=scp.school_id and l.course_id=scp.course_id and l.timelog_time<'{$endqueryTimes}'  and l.timelog_time >='{$startqueryTimes}' order by l.timelog_time desc limit 0,1) as finaltimes
              from smc_student_coursebalance_pricinglog as scp
              left join smc_course as sc on sc.course_id=scp.course_id
              left join smc_code_coursecat as  sct ON sc.coursecat_id = sct.coursecat_id
              left join smc_code_coursetype as  scy ON scy.coursetype_id = sct.coursetype_id
              left join smc_student as s on s.student_id=scp.student_id 
              left join smc_school as l on scp.school_id=l.school_id 
              where {$datawhere}
              order by l.school_branch,scp.pricinglog_createtime desc
              ";

        $in = array('A02', 'A03', 'A04', 'A05');
        $to = array('A07', 'B01', 'B02', 'B03', 'B04', 'B06', 'E01', 'B07');


        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无课次购买数据";
                return false;
            }


            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['pricinglog_buytimes'] = $dateexcelvar['pricinglog_buytimes'];
                    $datearray['pricinglog_buyprice'] = $dateexcelvar['pricinglog_buyprice'];
                    $datearray['pricinglog_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['pricinglog_createtime']);


                    if (!$dateexcelvar['stuchange_code']) {
                        $datearray['status'] = $this->LgStringSwitch('未入班');
                    } else {
                        if (in_array($dateexcelvar['stuchange_code'], $in)) {
                            $datearray['status'] = $this->LgStringSwitch('已入班');
                        } elseif (in_array($dateexcelvar['stuchange_code'], $to)) {
                            $datearray['status'] = $this->LgStringSwitch('已出班');
                        }
                    }
                    $datearray['finaltimes'] = $dateexcelvar['finaltimes'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "学员中文名", "学员英文名", "学员编号", "班组名称", "班组编号", "班种名称", "班种编号", "订单编号", "课程别名称", "课程别编号", "购买课次数", "购买金额", "购买时间", "在班状态", "剩余课次"));
            $excelfileds = array("school_branch", "school_cnname", 'student_cnname', 'student_enname', 'student_branch', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', "order_pid", 'course_cnname', 'course_branch', "pricinglog_buytimes", "pricinglog_buyprice", "pricinglog_createtime", "status", "finaltimes");
            $tem_name = $this->schoolOne['school_cnname'] . '学生课次购买报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无课次购买数据";
                return false;
            }

            foreach ($list as &$one) {
                $one['pricinglog_createtime'] = date("Y-m-d H:i:s", $one['pricinglog_createtime']);
                if (!$one['stuchange_code']) {
                    $one['status'] = $this->LgStringSwitch('未入班');
                } else {
                    if (in_array($one['stuchange_code'], $in)) {
                        $one['status'] = $this->LgStringSwitch('已入班');
                    } elseif (in_array($one['stuchange_code'], $to)) {
                        $one['status'] = $this->LgStringSwitch('已出班');
                    }
                }
            }

            $data = array();
            $count_sql = "select count(s.student_id) AS  countnums
              from smc_student_coursebalance_pricinglog as scp
              left join smc_course as sc on sc.course_id=scp.course_id
              left join smc_student as s on s.student_id=scp.student_id
              left join smc_school as l on scp.school_id=l.school_id 
              where {$datawhere}
              order by scp.pricinglog_createtime desc";
            $dbNums = $this->DataControl->selectOne($count_sql);
            if ($dbNums) {
                $allnum = $dbNums['countnums'];
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //学员班级流失报表
    function studentClassOff($request)
    {
        $datawhere = " 1 ";
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and scl.changelog_day<='{$request['end_time']}'";
        }
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and scl.changelog_day>='{$request['start_time']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and c.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and sc.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and ct.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and scl.school_id='{$this->school_id}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,G.channel_name,
              c.class_id,c.class_cnname,c.class_enname,c.class_branch,
              scl.changelog_day,scl.changelog_note,scl.staffer_id,scl.changelog_createtime,ch.change_reason,
              cc.coursecat_cnname,cc.coursecat_branch,sc.course_branch,sc.course_cnname,ct.coursetype_branch,ct.coursetype_cnname
              ,(select max(y.clockinginlog_day) from smc_student_hourstudy x ,smc_student_clockinginlog y where x.hourstudy_id=y.hourstudy_id and x.class_id=c.class_id and x.student_id=s.student_id) as last_atte_date
              from smc_student_changelog as scl
              left join smc_student_change as ch on ch.change_pid=scl.change_pid and scl.company_id=ch.company_id 
              left join smc_student as s on s.student_id=scl.student_id 
              left join smc_class as c on c.class_id=scl.class_id 
              left join smc_course as sc on sc.course_id=c.course_id 
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id 
              left join smc_code_coursetype as ct on ct.coursetype_id=cc.coursetype_id 
              left join smc_student_guildpolicy G ON G.student_id=s.student_id and guildpolicy_enddate>=scl.changelog_day
              where {$datawhere} and scl.stuchange_code='B01' and scl.company_id='{$this->company_id}'
              order by scl.changelog_day desc,scl.changelog_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['last_atte_date'] = $dateexcelvar['last_atte_date'];
                    $datearray['change_reason'] = $dateexcelvar['change_reason'];
                    $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_enname", "staffer_id='{$dateexcelvar['staffer_id']}'");
                    $datearray['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
                    $datearray['changelog_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['changelog_createtime']);;
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "专案名称", "班级名称", "班级别名", "班级编号", "班组编号", "班组名称", "班种编号", "班种名称", "课程别编号", "课程别名称", "异动日期", '最后考勤日期', "异动原因", "操作人", "操作时间"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'channel_name', 'class_cnname', 'class_enname', 'class_branch', 'coursetype_branch', 'coursetype_cnname', 'coursecat_branch', 'coursecat_cnname', 'course_branch', 'course_cnname', 'changelog_day', 'last_atte_date', 'change_reason', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '学员班级流失明细报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($list as &$one) {

                $reasonOne = $this->DataControl->getFieldOne("smc_code_stuchange_reason", "reason_note", "company_id='{$this->company_id}' and reason_code='{$one['reason_code']}'");
                $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_enname", "staffer_id='{$one['staffer_id']}'");
                $one['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];

                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as ch on ch.change_pid=scl.change_pid and scl.company_id=ch.company_id 
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as sc on sc.course_id=c.course_id 
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id 
              left join smc_code_coursetype as ct on ct.coursetype_id=cc.coursetype_id 
              where {$datawhere} and scl.stuchange_code='B01' and scl.company_id='{$this->company_id}'
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //分校流失报表
    function studentSchoolOff($request)
    {
        $datawhere = "scl.stuchange_code in ('C02','C04') and scl.company_id='{$this->company_id}'";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and scl.changelog_day<='{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and scl.changelog_day>='{$request['start_time']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and scl.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['changelog_note']) && $request['changelog_note'] !== '') {
            if ($request['changelog_note'] == '0') {
                $datawhere .= " and ifnull(scl.changelog_note,'')=''";
            } else {
                $datawhere .= " and ifnull(scl.changelog_note,'')<>''";
            }
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and scl.school_id='{$this->school_id}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and scl.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or st.staffer_cnname like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,G.channel_name,scl.changelog_day,scl.changelog_note,csr.reason_note
              ,ifnull(cc.coursetype_branch,'--') as coursetype_branch,ifnull(cc.coursetype_cnname,'--') as coursetype_cnname
              ,concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ) ) as staffer_cnname
              ,scl.changelog_createtime,scl.stuchange_code,cs.stuchange_name
              ,ifnull((SELECT rs.trackresult_name FROM smc_student_track AS sta, smc_code_trackresult AS rs WHERE rs.trackresult_id = sta.result_id AND sta.student_id = s.student_id 
                AND sta.track_classname = '流失电访' ORDER BY sta.track_id DESC LIMIT 0, 1 ),'') AS connect_times
              ,(select max(y.clockinginlog_day) from smc_student_clockinginlog y where y.school_id=scl.school_id and y.student_id=scl.student_id) as last_atte_date
              ,(select max(y.clockinginlog_day) from smc_student_clockinginlog y,smc_student_hourstudy z,smc_class x,smc_course w 
              where y.hourstudy_id=z.hourstudy_id and z.class_id=x.class_id and x.course_id=w.course_id 
              and  y.school_id=scl.school_id and y.student_id=scl.student_id and w.coursetype_id=scl.coursetype_id) as coursetype_last_atte_date
              ,scl.changelog_category
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_coursetype as cc on cc.coursetype_id=scl.coursetype_id
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              left join smc_code_stuchange as cs on cs.stuchange_code=scl.stuchange_code 
              left join smc_student_guildpolicy G ON G.student_id=s.student_id and G.guildpolicy_enddate>=scl.changelog_day
              where {$datawhere}
              group by scl.changelog_id
              order by scl.changelog_day desc,scl.changelog_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员流失数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
                $one['changelog_note'] = $one['changelog_note'] == '' ? '--' : $one['changelog_note'];
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['changelog_category'] = $dateexcelvar['changelog_category'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $datearray['connect_times'] = $dateexcelvar['connect_times'];
                    $datearray['last_atte_date'] = $dateexcelvar['stuchange_code'] == 'C04' ? $dateexcelvar['coursetype_last_atte_date'] : $dateexcelvar['last_atte_date'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_enname'] ? $dateexcelvar['staffer_cnname'] . '-' . $dateexcelvar['staffer_enname'] : $dateexcelvar['staffer_cnname'];
                    $datearray['changelog_createtime'] = $dateexcelvar['changelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "专案名称", "异动类型", "异动日期", "异动班组名称", "异动班组编号", "流失类型", "流失原因", "电访结果", "最后考勤日期", "操作人", "操作时间"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'channel_name', 'stuchange_name', 'changelog_day', 'coursetype_cnname', 'coursetype_branch', 'changelog_category', 'changelog_note', 'connect_times', 'last_atte_date', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '学员流失报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无学员流失数据";
                return false;
            }
            foreach ($list as &$one) {
                $one['staffer_cnname'] = $one['staffer_enname'] ? $one['staffer_cnname'] . '-' . $one['staffer_enname'] : $one['staffer_cnname'];
                $one['connect_times'] = trim($one['connect_times']) == '' ? '---' : $one['connect_times'];
                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
                $one['last_atte_date'] = $one['stuchange_code'] == 'C04' ? $one['coursetype_last_atte_date'] : $one['last_atte_date'];
                $one['changelog_note'] = $one['changelog_note'] == '' ? '--' : $one['changelog_note'];
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_coursetype as cc on cc.coursetype_id=scl.coursetype_id
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              where {$datawhere}
              group by scl.changelog_id
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //积分交易明细列表
    function studentIntegrallog($request)
    {
        $datawhere = " 1 ";
        $datawhere .= " and i.company_id='{$request['company_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['integrallog_playclass']) && $request['integrallog_playclass'] !== "") {
            $datawhere .= " and i.integrallog_playclass ='{$request['integrallog_playclass']}'";
        }
        if (isset($request['integraltype_id']) && $request['integraltype_id'] !== "") {
            $datawhere .= " and i.integraltype_id ='{$request['integraltype_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.student_cnname,
                s.student_enname,
                s.student_branch,
                i.integrallog_playclass,
                i.integrallog_playclass as integrallog_playclass_name,
                i.student_id,
                i.integrallog_playname,
                i.integraltype_id,
                i.integrallog_playamount,
                i.integrallog_reason,
                FROM_UNIXTIME( i.integrallog_time, '%Y-%m-%d %H:%i:%s' ) AS integrallog_time,
                st.staffer_cnname,
                t.integraltype_name
            FROM
                smc_student_integrallog AS i
                LEFT JOIN smc_student AS s ON i.student_id = s.student_id
                left join smc_staffer as st on st.staffer_id = i.staffer_id
                left join smc_code_integraltype as t on t.integraltype_id = i.integraltype_id
	            LEFT JOIN smc_student_enrolled AS e ON i.student_id = e.student_id 
            WHERE
                {$datawhere} and e.school_id = '{$request['school_id']}'
            GROUP BY
                i.integrallog_id 
            ORDER BY
                i.integrallog_time DESC ";

        $status = array("+" => "积分增加", "-" => "积分扣除");
        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无积分交易数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['integrallog_playclass_name'] = $status[$dateexcelvar['integrallog_playclass_name']];
                    $datearray['integraltype_name'] = $dateexcelvar['integraltype_name'];
                    $datearray['integrallog_playamount'] = $dateexcelvar['integrallog_playclass'] . $dateexcelvar['integrallog_playamount'];
                    $datearray['integrallog_reason'] = $dateexcelvar['integrallog_reason'];
                    $datearray['integrallog_time'] = $dateexcelvar['integrallog_time'];
                    $datearray['status'] = '已完成';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学员中文名', '学员英文名', '学员编号', '所属类别', '积分类型', '交易积分', '交易原因', '完成时间', '状态'));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'integrallog_playclass_name', 'integraltype_name', 'integrallog_playamount', 'integrallog_reason', 'integrallog_time', 'status');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '学生积分交易明细表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无积分交易数据";
                return false;
            }
            foreach ($list as &$val) {
                $val['integrallog_playclass_name'] = $status[$val['integrallog_playclass_name']];
                $val['status'] = '已完成';
                $val['integrallog_playamount'] = $val['integrallog_playclass'] . $val['integrallog_playamount'];
            }
            $data = array();
            $count_sql = "SELECT i.integrallog_id 
            FROM
                smc_student_integrallog AS i
                LEFT JOIN smc_student AS s ON i.student_id = s.student_id
                left join smc_staffer as st on st.staffer_id = i.staffer_id
                left join smc_code_integraltype as t on t.integraltype_id = i.integraltype_id
	            LEFT JOIN smc_student_enrolled AS e ON i.student_id = e.student_id 
            WHERE
                {$datawhere} and e.school_id = '{$request['school_id']}'
            GROUP BY
                i.integrallog_id 
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //公益学生免费课次明细
    function studentChannelInfo($request)
    {
        $datawhere = " 1 and a.company_id ='{$request['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (e.student_cnname like '%{$request['keyword']}%' 
            or e.student_enname like '%{$request['keyword']}%' or e.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['guildstutype_id']) && $request['guildstutype_id'] !== "") {
            $datawhere .= " and a.guildstutype_id ='{$request['guildstutype_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " and d.school_istest<>'1' and d.school_isclose<>'1'";
        }

        if ($request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND a.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
        } else {
            $request['start_time'] = date("Y-m-01");
        }
        $datawhere .= " and b.guildpolicy_enddate>='{$request['start_time']}'";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
        } else {
            $request['end_time'] = date("Y-m-d");
        }
        $datawhere .= " and b.guildpolicy_startdate<='{$request['end_time']}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT d.school_branch
                ,d.school_cnname,
                e.student_branch,
                e.student_cnname,
                e.student_enname,
                (select enrolled_status from smc_student_enrolled where student_id=a.student_id and school_id=a.school_id) as enrolled_status,
                c.guildstutype_name,
                b.guildpolicy_startdate,
                b.guildpolicy_enddate,
                b.guildpolicy_remark,
                a.apply_status,
                a.apply_toclasstimes,
                a.apply_oldusedtimes as used_times_old,
                (	SELECT count(X.hourstudy_id) AS USED_COUNT 
                    FROM smc_student_hourstudy X 
                    LEFT JOIN smc_class_hour Y ON X.class_id=Y.class_id AND X.hour_id=Y.hour_id 
                    LEFT JOIN smc_course Z ON Y.course_id=Z.course_id 
                    LEFT JOIN smc_student_clockinginlog W ON X.hourstudy_id=W.hourstudy_id
                    left join smc_class Q ON X.class_id=Q.class_id
                    WHERE X.student_id=A.student_id
                    AND Z.coursetype_id='65'
                    AND Y.hour_day>='2020-05-01'
                    and q.class_type=0
                    AND Y.hour_isfree=0 
                    AND Y.hour_iswarming=0 
                    AND W.clockinginlog_price=0
                    ) as used_times_new,-- 3.0至今消耗课次
                (	SELECT count(X.hourstudy_id) AS USED_COUNT 
                    FROM smc_student_hourstudy X 
                    LEFT JOIN smc_class_hour Y ON X.class_id=Y.class_id AND X.hour_id=Y.hour_id 
                    LEFT JOIN smc_course Z ON Y.course_id=Z.course_id 
                    LEFT JOIN smc_student_clockinginlog W ON X.hourstudy_id=W.hourstudy_id
                    left join smc_class Q ON X.class_id=Q.class_id
                    WHERE X.student_id=A.student_id
                    AND Z.coursetype_id='65'
                    AND Y.hour_day>='{$request['start_time']}'
                    AND Y.hour_day<='{$request['end_time']}'
                    and q.class_type=0
                    AND Y.hour_isfree=0 
                    AND Y.hour_iswarming=0 
                    AND W.clockinginlog_price=0
                    ) as used_times_month,
                (SELECT COUNT(1) FROM smc_student_free_coursetimes X,smc_course Y,smc_class_hour W 
                WHERE X.course_id=Y.course_id AND X.student_id=B.student_id AND is_use=0 AND X.class_id=W.class_id AND X.hour_lessontimes=W.hour_lessontimes
                AND W.hour_day>'2021-02-28' and Y.coursetype_id='65') as free_times_new,-- 账面剩余未消耗赠课
                (	SELECT max(Y.HOUR_DAY) AS USED_COUNT 
                    FROM smc_student_hourstudy X 
                    LEFT JOIN smc_class_hour Y ON X.class_id=Y.class_id AND X.hour_id=Y.hour_id 
                    LEFT JOIN smc_course Z ON Y.course_id=Z.course_id 
                    LEFT JOIN smc_student_clockinginlog W ON X.hourstudy_id=W.hourstudy_id
                    left join smc_class Q ON X.class_id=Q.class_id
                    WHERE X.student_id=A.student_id
                    AND Z.coursetype_id='65'
                    AND Y.hour_day>='2020-05-01'
                    and q.class_type=0
                    AND Y.hour_isfree=0 
                    AND Y.hour_iswarming=0 
                    AND W.clockinginlog_price=0
                    ) as last_atte_date,
                (select group_concat(concat(school_branch,'-',school_cnname)) 
                from smc_student_enrolled x,smc_school y 
                where x.school_id=y.school_id and x.enrolled_status>=0 and x.student_id=a.student_id) as school_info_now 
                FROM smc_guildstu_apply A
                LEFT JOIN smc_student_guildpolicy B ON A.apply_id=B.apply_id
                LEFT JOIN smc_code_guildstutype C ON A.guildstutype_id=C.guildstutype_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_student e on a.student_id=e.student_id
                WHERE A.apply_status>0 
                and {$datawhere}
                and e.student_branch is not null 
                order by b.guildpolicy_startdate desc";

        $status = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "新生", "-1" => "已离校"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无公益学员数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['enrolled_status'] = $dateexcelvar['enrolled_status'] == '' ? '未入校' : $status[$dateexcelvar['enrolled_status']];
                    $datearray['guildstutype_name'] = $dateexcelvar['guildstutype_name'];
                    $datearray['guildpolicy_startdate'] = $dateexcelvar['guildpolicy_startdate'];
                    $datearray['guildpolicy_enddate'] = $dateexcelvar['guildpolicy_enddate'];
                    $datearray['apply_toclasstimes'] = $dateexcelvar['apply_toclasstimes'];
                    $datearray['used_times'] = $dateexcelvar['used_times_old'] + $dateexcelvar['used_times_new'];
                    $datearray['free_times_new'] = $dateexcelvar['free_times_new'];
                    $datearray['used_times_month'] = $dateexcelvar['used_times_month'];
                    $datearray['free_times_left'] = $dateexcelvar['apply_toclasstimes'] - $dateexcelvar['used_times_old'] - $dateexcelvar['used_times_new'] - $dateexcelvar['free_times_new'];
                    $datearray['last_atte_date'] = $dateexcelvar['last_atte_date'];
                    $datearray['school_info_now'] = $dateexcelvar['school_info_now'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区编号 ', '校区名称', '学员中文名', '学员英文名', '学员编号', '学员状态', '公益来源', '公益开始日期', '公益结束日期', '转吉的堡课次', '已消耗课次', '已赠送未消耗课次', '区间消耗免费课次', '剩余未赠送课次', '最近消耗免费课次日期', '学员当前所在校'));
            $excelfileds = array('school_branch', 'school_cnname', 'student_cnname', 'student_enname', 'student_branch', 'enrolled_status', 'guildstutype_name', 'guildpolicy_startdate', 'guildpolicy_enddate', 'apply_toclasstimes', 'used_times', 'free_times_new', 'used_times_month', 'free_times_left', 'last_atte_date', 'school_info_now');
            $tem_name = $this->LgStringSwitch("公益学员课时消耗报表({$request['start_time']}~{$request['end_time']}).xlsx");
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无公益学员数据";
                return false;
            }
            foreach ($list as &$val) {
                $val['enrolled_status'] = $val['enrolled_status'] == '' ? '未入校' : $status[$val['enrolled_status']];
                $val['used_times'] = $val['used_times_old'] + $val['used_times_new'];
                $val['free_times_left'] = $val['apply_toclasstimes'] - $val['used_times_old'] - $val['used_times_new'] - $val['free_times_new'];
            }
            $data = array();
            $count_sql = "
            SELECT a.apply_id 
                FROM smc_guildstu_apply A
                LEFT JOIN smc_student_guildpolicy B ON A.apply_id=B.apply_id
                LEFT JOIN smc_code_guildstutype C ON A.guildstutype_id=C.guildstutype_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_student e on a.student_id=e.student_id
                WHERE A.apply_status>0 
                and {$datawhere}
                and e.student_branch is not null 
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

}