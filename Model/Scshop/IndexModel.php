<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scshop;

class IndexModel extends modelTpl
{
    public $m;
    public $company_id;

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
    }

    //选择校区
    function SchoolApi($paramArray){
        $stuSchool = $this->DataControl->selectClear("SELECT
	s.school_id,
	s.district_id,
	s.school_cnname,
	s.school_address,
	(
		SELECT
			group_concat(c.class_cnname)
		FROM
			smc_student_study AS st,smc_class AS c
		WHERE
			st.school_id = s.school_id AND st.class_id = c.class_id
		AND st.student_id = e.student_id
	) AS class_cnname
FROM
	smc_student_enrolled AS e
LEFT JOIN smc_school AS s ON s.school_id = e.school_id
WHERE
	1
AND e.student_id = '{$paramArray['student_id']}'
AND (
	e.enrolled_status = '0'
	OR enrolled_status = '1'
)");
        if(!$stuSchool){
            $stuSchool = array();
        }

        $datawhere = "s.school_istest = '0' AND s.school_isclose = '0' AND s.company_id = '{$this->company_id}'";
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' OR s.school_enname like '%{$paramArray['keyword']}%')";
        }

        $addressOne = $this->Distance($paramArray['city_name']);
        $addressArray = json_decode($addressOne, true);
        //

        if(isset($paramArray['lng']) && $paramArray['lng']!=='' && $paramArray['lng'] !=='0'){
            $paramArray['lng'] = $paramArray['lng'];
        }else{
            $paramArray['lng'] = $addressArray['result']['location']['lng'];
        }

        if(isset($paramArray['lat']) && $paramArray['lat']!=='' && $paramArray['lat'] !=='0'){
            $paramArray['lat'] = $paramArray['lat'];
        }else{
            $paramArray['lat'] = $addressArray['result']['location']['lat'];
        }
        $schoolList = $this->DataControl->selectClear("SELECT
	s.school_id,
	s.school_cnname,
	s.school_city,
	s.school_address,
	s.district_id,
	ROUND(
		6378.138 * 2 * ASIN(
			SQRT(
				POW(
					SIN(
						({$paramArray['lng']}* PI() / 180 - s.school_longitude * PI() / 180) / 2
					),
					2
				) + COS({$paramArray['lng']}* PI() / 180) * COS(s.school_longitude * PI() / 180) * POW(
					SIN(
						({$paramArray['lat']}* PI() / 180 - s.school_latitude * PI() / 180) / 2
					),
					2
				)
			)
		) * 1000
	) AS distance
FROM
	smc_school AS s
WHERE
	{$datawhere}
ORDER BY distance ASC
LIMIT 0,3");
        if($schoolList){
            foreach($schoolList as &$v){
                $v['distance'] = round($v['distance']/1000,2);//距离
            }
        }else{
            $schoolList = array();
        }
        $cityOne = $this->DataControl->selectOne("SELECT region_id FROM smc_code_region WHERE region_level = '3' AND (region_name LIKE '%{$paramArray['city_name']}%')");
        $citySchool = $this->DataControl->selectClear("SELECT s.school_id,s.school_cnname,s.school_city,s.school_address,s.district_id,
	ROUND(
		6378.138 * 2 * ASIN(
			SQRT(
				POW(
					SIN(
						({$paramArray['lng']}* PI() / 180 - s.school_longitude * PI() / 180) / 2
					),
					2
				) + COS({$paramArray['lng']}* PI() / 180) * COS(s.school_longitude * PI() / 180) * POW(
					SIN(
						({$paramArray['lat']}* PI() / 180 - s.school_latitude * PI() / 180) / 2
					),
					2
				)
			)
		) * 1000
	) AS distance
FROM smc_school as s WHERE {$datawhere} AND s.school_city = '{$cityOne['region_id']}'
ORDER BY distance ASC
 limit 0,40");
        if($citySchool){
            foreach($citySchool as &$v){
                $v['distance'] = round($v['distance']/1000,2);//距离
            }
        }else{
            $citySchool = array();
        }

        $data = array();
        $data['list'] = $stuSchool;
        $data['schoolList'] = $schoolList;
        $data['cityschool'] = $citySchool;

        return $data;
    }

    //获取距离
    function Distance($address){

        $url = 'http://apis.map.qq.com/ws/geocoder/v1/?address='.$address.'&key=T2TBZ-JVDWU-CIYVV-2VX7S-5JHQT-XSFQN';
        $ch = curl_init();
        $timeout = 5;
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        $contents = curl_exec($ch);
        curl_close($ch);

        return $contents;
    }

    //微商城首页
    function HomePage($paramArray){

        $class = $this->DataControl->getFieldOne("shop_code_class","class_id","company_id = '{$this->company_id}'","order by class_id asc");

        //获取商品适配校
        $goodwhere = " AND g.company_id = '{$this->company_id}' AND g.class_id = '{$class['class_id']}' AND g.sellgoods_type = '2'
        AND (
            g.sellgoods_fitschool = '0'
            OR (g.sellgoods_fitschool = '1'
                AND g.sellgoods_id IN
                (SELECT
                    gs.sellgoods_id
                FROM
                    shop_sellgoods_school AS gs
                WHERE
                    gs.school_id = '{$paramArray['school_id']}'
                )
            )
        ) AND (
            g.sellgoods_fitarea = '0'
            OR (g.sellgoods_fitarea = '1'
                AND g.sellgoods_id IN
                (SELECT
                    d.sellgoods_id
                FROM
                    shop_sellgoods_district as d
                WHERE
                    d.district_id = '{$paramArray['district_id']}'
                )
            )
        )";

        //限时抢购
        $nowDay = date("Y-m-d");
        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_type,g.sellgoods_listimg,FROM_UNIXTIME( g.sellgoods_limitendtime, '%Y-%m-%d %H:%i:%s' ) as sellgoods_limitendtime
                FROM
                    shop_sellgoods as g
                WHERE
                    g.sellgoods_issale = '1' AND g.sellgoods_islimittime = '1' AND FROM_UNIXTIME( g.sellgoods_limitstarttime, '%Y-%m-%d' ) >= '{$nowDay}' AND FROM_UNIXTIME( g.sellgoods_limitendtime, '%Y-%m-%d' ) <= '{$nowDay}' {$goodwhere}
                LIMIT 0,2";
        $flashSale = $this->DataControl->selectClear($sql);
        if(!$flashSale){
            $flashSale = array();
        }

        //热销
        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_type,g.sellgoods_listimg
                FROM
                    shop_sellgoods as g
                WHERE
                    g.sellgoods_issale = '1' AND g.sellgoods_ishot = '1' {$goodwhere}
                LIMIT 0,2";
        $goodsHot = $this->DataControl->selectClear($sql);
        if(!$goodsHot){
            $goodsHot = array();
        }

        //推荐
        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_type,g.sellgoods_listimg
                FROM
                    shop_sellgoods as g
                WHERE
                    g.sellgoods_issale = '1' AND g.sellgoods_isromhome = '1' {$goodwhere}
                LIMIT 0,2";
        $goodsRom = $this->DataControl->selectClear($sql);
        if(!$goodsRom){
            $goodsRom = array();
        }

        //排行榜
        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_type,g.sellgoods_listimg
                FROM
                    shop_sellgoods as g
                WHERE
                    g.sellgoods_issale = '1' AND g.sellgoods_isromhome = '1' {$goodwhere}
                ORDER BY g.sellgoods_sort DESC LIMIT 0,2";
        $goodsRanking = $this->DataControl->selectClear($sql);
        if(!$goodsRanking){
            $goodsRanking = array();
        }

        $data['flashSale'] = $flashSale;
        $data['goodsHot'] = $goodsHot;
        $data['goodsRom'] = $goodsRom;
        $data['goodsRanking'] = $goodsRanking;

        return $data;
    }

    //获取推荐商品
    function RecommendGoods($paramArray){

        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($paramArray['class_id']) && $paramArray['class_id'] !== '' && isset($paramArray['class_type']) && $paramArray['class_type'] !== ''){
            $class_id = $paramArray['class_id'];
            $class_type = $paramArray['class_type'];
        }else{
            $class = $this->DataControl->getFieldOne("shop_code_class","class_id,class_type","company_id = '{$this->company_id}' and class_type = '2'","order by class_id asc");
            $class_id = $class['class_id'];
            $class_type = $class['class_type'];
        }

        //获取商品适配校
        $goodwhere = " AND g.company_id = '{$this->company_id}' AND g.class_id = '{$class_id}' AND g.sellgoods_type = '{$class_type}'
        AND (
            g.sellgoods_fitschool = '0'
            OR (g.sellgoods_fitschool = '1'
                AND g.sellgoods_id IN
                (SELECT
                    gs.sellgoods_id
                FROM
                    shop_sellgoods_school AS gs
                WHERE
                    gs.school_id = '{$paramArray['school_id']}'
                )
            )
        ) AND (
            g.sellgoods_fitarea = '0'
            OR (g.sellgoods_fitarea = '1'
                AND g.sellgoods_id IN
                (SELECT
                    d.sellgoods_id
                FROM
                    shop_sellgoods_district as d
                WHERE
                    d.district_id = '{$paramArray['district_id']}'
                )
            )
        )";

        //为你推荐
        $sql = "SELECT g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.feeitem_price,g.tuition_id,g.coursepacks_id,g.goods_id,
                    (SELECT c.category_name FROM shop_code_category as c WHERE c.category_id = g.category_id) as category_name
                FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND g.sellgoods_ishot = '1' {$goodwhere} LIMIT {$pagestart},{$num}";
        $goodsRomList = $this->DataControl->selectClear($sql);
        if ($goodsRomList) {
            foreach ($goodsRomList as &$val) {
                if ($val['sellgoods_type'] == '0') {
                    $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$val['tuition_id']}'");
                    $val['price'] = $a['tuition_sellingprice'];
                    $val['old_price'] = $a['tuition_originalprice'];
                } elseif ($val['sellgoods_type'] == '1') {
                    $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$val['coursepacks_id']}'");
                    $val['price'] = $b['price'];
                } elseif ($val['sellgoods_type'] == '2') {
                    $c = $this->DataControl->selectOne("select goods_originalprice,goods_vipprice from erp_goods where goods_id = '{$val['goods_id']}'");
                    $val['price'] = $c['goods_vipprice'];
                    $val['old_price'] = $c['goods_originalprice'];
                } elseif ($val['sellgoods_type'] == '3') {
                    $val['price'] = $val['feeitem_price'];
                }
            }
        } else {
            $goodsRomList = array();
        }

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(g.sellgoods_id) as num FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND g.sellgoods_ishot = '1' {$goodwhere}");

        $data = array();
        $data['list'] = $goodsRomList;
        $data['allnums'] = $db_nums['num'];

        return $data;
    }

    //奇瓦迪商铺
    function ShopList($paramArray)
    {

        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        //获取商品适配校
        $datawhere = "g.company_id = '{$this->company_id}' AND g.class_id = '{$paramArray['class_id']}' AND g.sellgoods_type = '{$paramArray['class_type']}'
        AND (
            g.sellgoods_fitschool = '0'
            OR (g.sellgoods_fitschool = '1'
                AND g.sellgoods_id IN
                (SELECT
                    gs.sellgoods_id
                FROM
                    shop_sellgoods_school AS gs
                WHERE
                    gs.school_id = '{$paramArray['school_id']}'
                )
            )
        ) AND (
            g.sellgoods_fitarea = '0'
            OR (g.sellgoods_fitarea = '1'
                AND g.sellgoods_id IN
                (SELECT
                    d.sellgoods_id
                FROM
                    shop_sellgoods_district as d
                WHERE
                    d.district_id = '{$paramArray['district_id']}'
                )
            )
        )";

        if (isset($paramArray['category_id']) && $paramArray['category_id'] != '') {
            $datawhere .= " and g.category_id = '{$paramArray['category_id']}'";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (g.sellgoods_name like '%{$paramArray['keyword']}%')";

            if (!$this->DataControl->getOne("shop_parenter_searchlog", "company_id = '{$this->company_id}' and parenter_id = '{$paramArray['parenter_id']}' and student_id = '{$paramArray['student_id']}' and searchlog_keyword='{$paramArray['keyword']}'")) {
                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['parenter_id'] = $paramArray['parenter_id'];
                $data['student_id'] = $paramArray['student_id'];
                $data['searchlog_keyword'] = $paramArray['keyword'];
                $data['searchlog_createtime'] = time();
                $this->DataControl->insertData("shop_parenter_searchlog", $data);
            }
        }

        if (isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1') {
            $orderby = " ORDER BY price ASC";
            if (isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != '') {
                $orderby .= ",g.sellgoods_sales DESC";
            }
        } elseif (isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2') {
            $orderby = " ORDER BY price DESC";
            if (isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != '') {
                $orderby .= ",g.sellgoods_sales DESC";
            }
        } elseif (isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != '') {
            $orderby = " ORDER BY g.sellgoods_sales DESC";
            if (isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1') {
                $orderby .= ",price ASC";
            } elseif (isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2') {
                $orderby .= ",price DESC";
            }
        }

        $class = $this->DataControl->getFieldOne("shop_code_class", "class_type", "class_id='{$paramArray['class_id']}'");
        if ($class['class_type'] == '2') {
            $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,eg.goods_originalprice as old_price,eg.goods_vipprice as price,
                    (SELECT c.category_name FROM shop_code_category as c WHERE c.category_id = g.category_id) as category_name
                FROM
                    shop_sellgoods as g
                LEFT JOIN
                    erp_goods as eg ON eg.goods_id = g.goods_id
                WHERE
                    g.sellgoods_issale = '1' AND {$datawhere}
                {$orderby} LIMIT {$pagestart},{$num}";
        } elseif ($class['class_type'] == '4') {
            $sql = "SELECT
                        g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,p.products_originalprice as old_price,p.products_sellingprice as price,
                        (SELECT c.category_name FROM shop_code_category as c WHERE c.category_id = g.category_id) as category_name
                    FROM
                        shop_sellgoods as g
                    LEFT JOIN
                        smc_fee_pricing_products as p ON p.products_id = g.products_id
                    WHERE
                        g.sellgoods_issale = '1' AND {$datawhere}
                    {$orderby} LIMIT {$pagestart},{$num}";
        }
        $goodsList = $this->DataControl->selectClear($sql);
        if(!$goodsList){
            $goodsList = array();
        }

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(g.sellgoods_id) as num FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND {$datawhere}");

        $data = array();
        $data['list'] = $goodsList;
        $data['allnums'] = $db_nums['num'];

        return $data;
    }

    //课程学院
    function ClassCollege($paramArray){
        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        //获取商品适配校
        $goodswhere = "g.company_id = '{$this->company_id}' AND g.class_id = '{$paramArray['class_id']}'
        AND g.sellgoods_issale = '1' AND g.sellgoods_type IN (0,1)
        AND (
            g.sellgoods_fitschool = '0'
            OR (g.sellgoods_fitschool = '1'
                AND g.sellgoods_id IN
                (SELECT
                    gs.sellgoods_id
                FROM
                    shop_sellgoods_school AS gs
                WHERE
                    gs.school_id = '{$paramArray['school_id']}'
                )
            )
        ) AND (
            g.sellgoods_fitarea = '0'
            OR (g.sellgoods_fitarea = '1'
                AND g.sellgoods_id IN
                (SELECT
                    d.sellgoods_id
                FROM
                    shop_sellgoods_district as d
                WHERE
                    d.district_id = '{$paramArray['district_id']}'
                )
            )
        )";

        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $goodswhere .= " and (g.sellgoods_name like '%{$paramArray['keyword']}%')";
            if(!$this->DataControl->getOne("shop_parenter_searchlog","company_id = '{$this->company_id}' and parenter_id = '{$paramArray['parenter_id']}' and student_id = '{$paramArray['student_id']}' and searchlog_keyword='{$paramArray['keyword']}'")){
                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['parenter_id'] = $paramArray['parenter_id'];
                $data['student_id'] = $paramArray['student_id'];
                $data['searchlog_keyword'] = $paramArray['keyword'];
                $data['searchlog_createtime'] = time();
                $this->DataControl->insertData("shop_parenter_searchlog", $data);
            }
        }
        $goodidArray = $this->DataControl->selectClear("SELECT g.sellgoods_id FROM shop_sellgoods as g WHERE {$goodswhere}");
        $goodid = array();
        if($goodidArray){
            foreach($goodidArray as $goodidOne){
                $goodid[] = $goodidOne['sellgoods_id'];
            }
        }
        $goodidString = implode(",",$goodid);
        if($goodidString == ''){
            $goodidString = '0';
        }
        $datawhere = "g.sellgoods_id IN ($goodidString)";

        $pricingwhere = "AND t.tuition_id = g.tuition_id AND t.pricing_id IN (SELECT
	p.pricing_id
FROM
	smc_fee_pricing AS p,
	smc_fee_agreement AS a
WHERE
	a.agreement_id = p.agreement_id
AND a.agreement_startday <= CURDATE()
AND a.agreement_endday >= CURDATE()
AND a.company_id = '{$this->company_id}' AND a.agreement_status = '1')";

        $warehousewhere = " AND c.coursepacks_id = g.coursepacks_id AND c.warehouse_id IN (SELECT
	w.warehouse_id
FROM
	smc_fee_warehouse AS w,
	smc_fee_agreement AS a
WHERE
	a.agreement_id = w.agreement_id
AND a.agreement_startday <= CURDATE()
AND a.agreement_endday >= CURDATE()
AND a.company_id = '{$this->company_id}' AND a.agreement_status = '1')";


        if(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1'){
            $orderby = " ORDER BY price ASC";
            if(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
                $orderby .= ",g.sellgoods_sales DESC";
            }
        }elseif(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2'){
            $orderby = " ORDER BY price DESC";
            if(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
                $orderby .= ",g.sellgoods_sales DESC";
            }
        }elseif(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
            $orderby = " ORDER BY g.sellgoods_sales DESC";
            if(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1'){
                $orderby .= ",price ASC";
            }elseif(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2'){
                $orderby .= ",price DESC";
            }
        }

        $sqlleft = "SELECT
                    g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,g.sellgoods_pageview,
                    t.tuition_originalprice as old_price,
                    t.tuition_sellingprice as price
                FROM
                    shop_sellgoods as g,smc_fee_pricing_tuition as t
                WHERE  {$datawhere} {$pricingwhere}";

        $sqlright = "SELECT
                    g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,g.sellgoods_pageview,
                    (SELECT SUM(t.tuition_originalprice) FROM smc_fee_pricing_tuition AS t, smc_fee_warehouse_courses AS c WHERE c.pricing_id = t.pricing_id AND c.coursepacks_id = g.coursepacks_id) AS old_price,
                    (SELECT SUM(tuition_sellingprice) FROM smc_fee_warehouse_courses as c WHERE c.coursepacks_id = g.coursepacks_id) as price
                FROM
                    shop_sellgoods as g,smc_fee_warehouse_coursepacks AS c WHERE {$datawhere} {$warehousewhere}";

        $sql = "SELECT goods.* FROM(($sqlleft) union ($sqlright)) as goods {$orderby} limit {$pagestart},{$num}";
        $result = array();
        $goodList = $this->DataControl->selectClear($sql);
        if($goodList){
            $result['list'] = $goodList;
        }else{
            $result['list'] = array();
        }

        $sql = "SELECT count(goods.sellgoods_id) as nums FROM(($sqlleft) union ($sqlright)) as goods ORDER BY price ASC";
        $goodNums = $this->DataControl->selectOne($sql);
        $result['allnum'] = $goodNums['nums'];
        return $result;
    }

    //活动赛事
    function ActivityMatch($paramArray){

        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        //获取商品适配校
        $datawhere = "g.company_id = '{$this->company_id}' AND g.class_id = '{$paramArray['class_id']}' AND g.sellgoods_type = '{$paramArray['class_type']}'
        AND (
            g.sellgoods_fitschool = '0'
            OR (g.sellgoods_fitschool = '1'
                AND g.sellgoods_id IN
                (SELECT
                    gs.sellgoods_id
                FROM
                    shop_sellgoods_school AS gs
                WHERE
                    gs.school_id = '{$paramArray['school_id']}'
                )
            )
        ) AND (
            g.sellgoods_fitarea = '0'
            OR (g.sellgoods_fitarea = '1'
                AND g.sellgoods_id IN
                (SELECT
                    d.sellgoods_id
                FROM
                    shop_sellgoods_district as d
                WHERE
                    d.district_id = '{$paramArray['district_id']}'
                )
            )
        )";

        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (g.sellgoods_name like '%{$paramArray['keyword']}%')";

            if(!$this->DataControl->getOne("shop_parenter_searchlog","company_id = '{$this->company_id}' and parenter_id = '{$paramArray['parenter_id']}' and student_id = '{$paramArray['student_id']}' and searchlog_keyword='{$paramArray['keyword']}'")){
                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['parenter_id'] = $paramArray['parenter_id'];
                $data['student_id'] = $paramArray['student_id'];
                $data['searchlog_keyword'] = $paramArray['keyword'];
                $data['searchlog_createtime'] = time();
                $this->DataControl->insertData("shop_parenter_searchlog", $data);
            }
        }

        if(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1'){
            $orderby = " ORDER BY price ASC";
            if(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
                $orderby .= ",g.sellgoods_sales DESC";
            }
        }elseif(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2'){
            $orderby = " ORDER BY price DESC";
            if(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
                $orderby .= ",g.sellgoods_sales DESC";
            }
        }elseif(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
            $orderby = " ORDER BY g.sellgoods_sales DESC";
            if(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1'){
                $orderby .= ",price ASC";
            }elseif(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2'){
                $orderby .= ",price DESC";
            }
        }

        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,g.feeitem_price as price,
                    (SELECT c.category_name FROM shop_code_category as c WHERE c.category_id = g.category_id) as category_name
                FROM
                    shop_sellgoods as g
                WHERE
                    g.sellgoods_issale = '1' AND {$datawhere}
                {$orderby} LIMIT {$pagestart},{$num}";
        $goodsList = $this->DataControl->selectClear($sql);
        if(!$goodsList){
            $goodsList = array();
        }

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(g.sellgoods_id) as num FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND {$datawhere}");

        $data = array();
        $data['list'] = $goodsList;
        $data['allnums'] = $db_nums['num'];

        return $data;
    }

    //商品详情
    function goodsLook($paramArray){
        $goodsOne = $this->DataControl->selectOne("SELECT sellgoods_id,sellgoods_name,sellgoods_type,sellgoods_remark,sellgoods_listimg,sellgoods_detailimg,sellgoods_cartchange,sellgoods_pageview,sellgoods_unit,feeitem_price,tuition_id,coursepacks_id,goods_id FROM shop_sellgoods WHERE sellgoods_id= '{$paramArray['sellgoods_id']}'");
        if($goodsOne){
            $collection = $this->DataControl->getFieldOne("shop_parenter_collection","collection_id","company_id='{$this->company_id}' and parenter_id='{$paramArray['parenter_id']}' and student_id='{$paramArray['student_id']}' and sellgoods_id='{$goodsOne['sellgoods_id']}'");
            if($collection){
                $goodsOne['collection_id'] = $collection['collection_id'];
            }
            if ($goodsOne['sellgoods_type'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$goodsOne['tuition_id']}'");
                $goodsOne['price'] = $a['tuition_sellingprice'];
                $goodsOne['old_price'] = $a['tuition_originalprice'];
            } elseif ($goodsOne['sellgoods_type'] == '1') {
                $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$goodsOne['coursepacks_id']}'");
                $goodsOne['price'] = $b['price'];
            } elseif ($goodsOne['sellgoods_type'] == '2') {
                $c = $this->DataControl->selectOne("select goods_originalprice,goods_vipprice from erp_goods where goods_id = '{$goodsOne['goods_id']}'");
                $goodsOne['price'] = $c['goods_vipprice'];
                $goodsOne['old_price'] = $c['goods_originalprice'];
            } elseif ($goodsOne['sellgoods_type'] == '3') {
                $goodsOne['price'] = $goodsOne['feeitem_price'];
            } elseif ($goodsOne['sellgoods_type'] == '4') {
                $d = $this->DataControl->getFieldOne("smc_fee_pricing_products","products_originalprice,products_sellingprice","products_id = '{$goodsOne['products_id']}'");
                $goodsOne['price'] = $d['products_sellingprice'];
                $goodsOne['old_price'] = $d['products_originalprice'];
            }
        }

        $data = array();
        $data['sellgoods_pageview'] = $goodsOne['sellgoods_pageview'] + 1;
        $this->DataControl->updateData("shop_sellgoods","sellgoods_id='{$goodsOne['sellgoods_id']}'", $data);

        if($browse = $this->DataControl->getOne("shop_parenter_browse","company_id='{$this->company_id}' and parenter_id='{$paramArray['parenter_id']}' and student_id='{$paramArray['student_id']}' and sellgoods_id='{$goodsOne['sellgoods_id']}'")){
            if(date("Y-m-d",$browse['browse_createtime']) != date("Y-m-d")){
                $record = array();
                $record['company_id'] = $this->company_id;
                $record['parenter_id'] = $paramArray['parenter_id'];
                $record['student_id'] = $paramArray['student_id'];
                $record['sellgoods_id'] = $goodsOne['sellgoods_id'];
                $record['browse_createtime'] = time();
                $this->DataControl->insertData("shop_parenter_browse", $record);
            }
        }else{
            $record = array();
            $record['company_id'] = $this->company_id;
            $record['parenter_id'] = $paramArray['parenter_id'];
            $record['student_id'] = $paramArray['student_id'];
            $record['sellgoods_id'] = $goodsOne['sellgoods_id'];
            $record['browse_createtime'] = time();
            $this->DataControl->insertData("shop_parenter_browse", $record);
        }

        $cartnum = $this->DataControl->selectOne("SELECT COUNT(cart_id) as num FROM shop_cart WHERE company_id = '{$this->company_id}' AND school_id = '{$paramArray['school_id']}' AND parenter_id = '{$paramArray['parenter_id']}' AND student_id = '{$paramArray['student_id']}'");

        $data = array();
        $data['list'] = $goodsOne;
        $data['allnums'] = $cartnum['num'];

        return $data;
    }

    //搜索-输入内容
    function SearchGoods($paramArray){
        $class = $this->DataControl->getFieldOne("shop_code_class","class_id","company_id = '{$this->company_id}' and class_type = '1'");

        //获取商品适配校
        $datawhere = "g.company_id = '{$this->company_id}' AND g.class_id = '{$class['class_id']}'
        AND (
            g.sellgoods_fitschool = '0'
            OR (g.sellgoods_fitschool = '1'
                AND g.sellgoods_id IN
                (SELECT
                    gs.sellgoods_id
                FROM
                    shop_sellgoods_school AS gs
                WHERE
                    gs.school_id = '{$paramArray['school_id']}'
                )
            )
        ) AND (
            g.sellgoods_fitarea = '0'
            OR (g.sellgoods_fitarea = '1'
                AND g.sellgoods_id IN
                (SELECT
                    d.sellgoods_id
                FROM
                    shop_sellgoods_district as d
                WHERE
                    d.district_id = '{$paramArray['district_id']}'
                )
            )
        )";

        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (g.sellgoods_name like '%{$paramArray['keyword']}%')";

            if(!$this->DataControl->getOne("shop_parenter_searchlog","company_id = '{$this->company_id}' and parenter_id = '{$paramArray['parenter_id']}' and student_id = '{$paramArray['student_id']}' and searchlog_keyword='{$paramArray['keyword']}'")){
                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['parenter_id'] = $paramArray['parenter_id'];
                $data['student_id'] = $paramArray['student_id'];
                $data['searchlog_keyword'] = $paramArray['keyword'];
                $data['searchlog_createtime'] = time();
                $this->DataControl->insertData("shop_parenter_searchlog", $data);
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1'){
            $orderby = " ORDER BY price ASC";
            if(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
                $orderby .= ",g.sellgoods_sales DESC";
            }
        }elseif(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2'){
            $orderby = " ORDER BY price DESC";
            if(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
                $orderby .= ",g.sellgoods_sales DESC";
            }
        }elseif(isset($paramArray['orderby_sales']) && $paramArray['orderby_sales'] != ''){
            $orderby = " ORDER BY g.sellgoods_sales DESC";
            if(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '1'){
                $orderby .= ",price ASC";
            }elseif(isset($paramArray['orderby_price']) && $paramArray['orderby_price'] == '2'){
                $orderby .= ",price DESC";
            }
        }


        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,g.sellgoods_pageview,t.tuition_originalprice as old_price,t.tuition_sellingprice as price,
                    (SELECT c.category_name FROM shop_code_category as c WHERE c.category_id = g.category_id) as category_name
                FROM
                    shop_sellgoods as g
                LEFT JOIN
                    smc_fee_pricing_tuition as t ON t.tuition_id = g.tuition_id
                WHERE
                    g.sellgoods_issale = '1' AND {$datawhere} AND g.sellgoods_type = '0'
                {$orderby}";
        $goodsList = $this->DataControl->selectClear($sql);

        $sql = "SELECT
                    g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_unit,g.sellgoods_pageview,
                    (SELECT SUM(tuition_sellingprice) FROM smc_fee_warehouse_courses as c WHERE c.coursepacks_id = g.coursepacks_id) as price,
                    (SELECT c.category_name FROM shop_code_category as c WHERE c.category_id = g.category_id) as category_name
                FROM
                    shop_sellgoods as g
                WHERE
                    g.sellgoods_issale = '1' AND {$datawhere} AND g.sellgoods_type = '1'
                {$orderby}";
        $goodList = $this->DataControl->selectClear($sql);

        $data = array();
        if($goodsList){
            $db_numOne = $this->DataControl->selectOne("SELECT COUNT(g.sellgoods_id) as num FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND {$datawhere} AND g.sellgoods_type = '0'");
            if($goodList){
                $db_numTwo = $this->DataControl->selectOne("SELECT COUNT(g.sellgoods_id) as num FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND {$datawhere} AND g.sellgoods_type = '1'");
                $dataList = array_merge($goodsList,$goodList);
                $data['allnums'] = $db_numOne['num'] + $db_numTwo['num'];
            }else{
                $dataList = $goodsList;
                $data['allnums'] = $db_numOne['num'];
            }
        }else{
            if($goodList){
                $db_numTwo = $this->DataControl->selectOne("SELECT COUNT(g.sellgoods_id) as num FROM shop_sellgoods as g WHERE g.sellgoods_issale = '1' AND {$datawhere} AND g.sellgoods_type = '1'");
                $dataList = $goodList;
                $data['allnums'] = $db_numTwo['num'];
            }else{
                $dataList = array();
                $data['allnums'] = 0;
            }
        }
        if($dataList){
            $dataList = array_slice($dataList, $pagestart, $num);
        }

        $data['list'] = $dataList;

        return $data;
    }

    //订单消息
    function OrderMessage($paramArray){

        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $messageList = $this->DataControl->selectClear("SELECT * FROM shop_message WHERE company_id = '{$this->company_id}' AND student_id = '{$paramArray['student_id']}' LIMIT {$pagestart},{$num}");
        if(!$messageList){
            $messageList = array();
        }

        $db_nums = $this->DataControl->selectOne("SELECT COUNT(message_id) as num FROM shop_message WHERE company_id = '{$this->company_id}' AND student_id = '{$paramArray['student_id']}'");

        $data = array();
        $data['list'] = $messageList;
        $data['allnums'] = $db_nums['num'];

        return $data;
    }

    //热搜
    function HotSearch($paramArray){
        $datawhere = "company_id = '{$this->company_id}'";
        if(isset($paramArray['class_id'])){
            if($paramArray['class_id'] == '1'){
                $datawhere .= " and hotword_type = '2'";
            }else{
                $datawhere .= " and hotword_type = '1'";
            }
        }else{
            $datawhere .= " and hotword_type = '0'";
        }

        $datalist = $this->DataControl->selectClear("SELECT hotword_name AS sellgoods_name FROM shop_hotword WHERE {$datawhere} LIMIT 0,5");

        return $datalist;
    }
}
