<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scshop;

class OrderModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = false;

    function __construct()
    {
        parent::__construct();
    }


    //获取孩子可用订单优惠券
    function StuOrderCoupons($paramArray)
    {
        $sql = "select c.coupons_id,c.coupons_pid,c.coupons_name from smc_student_coupons as c
WHERE c.student_id = '{$paramArray['student_id']}' and c.coupons_playclass = '1' and c.coupons_isuse = '0'";
        $coupous = $this->DataControl->selectClear($sql);
        $count = $this->DataControl->selectOne("select count(c.coupons_id) as num from smc_student_coupons as c
WHERE c.student_id = '{$paramArray['student_id']}' and c.coupons_playclass = '1' and c.coupons_isuse = '0'");

        $alreadyCoupons = array();

        $price = $this->DataControl->selectOne("select sum(ordercoupons_price) as price
from smc_student_coupons as c left join smc_payfee_order_coupons as o on o.coupons_pid = c.coupons_pid
where o.order_pid = '{$paramArray['order_pid']}' and coupons_playclass = '1'");
        if ($price) {
            $res['price'] = $price['price'];
        } else {
            $res['price'] = '0';
        }

        $res['list'] = $coupous;
        $res['count'] = $count['num'];
        if ($alreadyCoupons) {
            $res['alreadyCoupons'] = $alreadyCoupons;
        } else {
            $res['alreadyCoupons'] = array();
        }
        return $res;
    }

    //订单确认商品列表
    function OrderGoodsList($paramArray)
    {
        $paramArray['sellgoods_id'] = stripslashes($paramArray['sellgoods_id']);
        $sql = "
            SELECT
                s.sellgoods_id,
                s.sellgoods_name,
                s.sellgoods_type,
                s.sellgoods_cartchange,
                s.sellgoods_listimg,
                s.tuition_id,
                s.coursepacks_id,
                s.class_id,
                s.goods_id,
                s.course_id,
                s.products_id,
                s.feeitem_price,
                s.feeitem_branch 
            FROM
                shop_sellgoods AS s
            WHERE
                s.sellgoods_id in ({$paramArray['sellgoods_id']})";
        $goodsList = $this->DataControl->selectClear($sql);

        foreach ($goodsList as &$val) {
            if ($val['sellgoods_type'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$val['tuition_id']}'");
                $val['price'] = $a['tuition_sellingprice'];
                $price = $a['tuition_sellingprice'];

                $item = array();
                $item['price'] = $price;
                $item['course_id'] = $val['course_id'];
                $item['sellgoods_id'] = $val['sellgoods_id'];
                $item['student_id'] = $paramArray['student_id'];
                $item['company_id'] = $paramArray['company_id'];
                $item['school_id'] = $paramArray['school_id'];
                $item['coupons_isuse'] = '0';

                $Model = new \Model\Smc\RegistrationModel();
                $dataList = $Model->getAvailableTicketList($item);

                $val['GoodsCoupons'] = $dataList;

            } elseif ($val['sellgoods_type'] == '1') {
                $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$val['coursepacks_id']}'");
                $val['price'] = $b['price'];
                $price = $b['price'];


            } elseif ($val['sellgoods_type'] == '2') {
                $c = $this->DataControl->selectOne("select goods_vipprice from erp_goods where goods_id = '{$val['goods_id']}'");
                $val['price'] = $c['goods_vipprice'];

                $item = array();
                $item['price'] = $c['goods_vipprice'];
                $item['student_id'] = $paramArray['student_id'];
                $item['company_id'] = $paramArray['company_id'];
                $item['school_id'] = $paramArray['school_id'];
                $item['goods_id'] = $val['goods_id'];
                $item['is_product'] = '1';
                $item['coupons_isuse'] = '0';

                $Model = new \Model\Smc\RegistrationModel();
                $dataList = $Model->getAvailableTicketList($item);

                $val['GoodsCoupons'] = $dataList;

            } elseif ($val['sellgoods_type'] == '3') {
                $val['price'] = $val['feeitem_price'];

            } elseif ($val['sellgoods_type'] == '4') {
                $d = $this->DataControl->getFieldOne("smc_fee_pricing_products", "products_sellingprice", "products_id = '{$val['products_id']}'");
                $val['price'] = $d['products_sellingprice'];

            }


            if ($paramArray['num']) {
                $num = json_decode(stripslashes($paramArray['num']), true);
                foreach ($num as &$v) {
                    if ($v['sellgoods_id'] == $val['sellgoods_id']) {
                        $val['num'] = intval($v['cart_nums']);
                    }
                }
            }

            if ($val['sellgoods_type'] == '1') {
                $pricing = $this->DataControl->selectClear("select pricing_id from smc_fee_warehouse_courses where coursepacks_id = '{$val['coursepacks_id']}'");
                foreach ($pricing as &$vals) {
                    $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "pricing_id,course_id,tuition_sellingprice,tuition_buypiece", "pricing_id = '{$vals['pricing_id']}'");
                    $vals['pricing_id'] = $a['pricing_id'];
                    $vals['course_id'] = $a['course_id'];
                    $vals['sellingprice'] = intval($a['tuition_sellingprice']);
                    $vals['num'] = intval($a['tuition_buypiece']);
                    $b = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$a['pricing_id']}'");
                    $vals['agreement_id'] = $b['agreement_id'];
                    $vals['class_id'] = '';
                    $c = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id = '{$a['course_id']}'");
                    $vals['course_inclasstype'] = $c['course_inclasstype'];


                    $item = array();
                    $item['price'] = $price;
                    $item['course_id'] = $a['course_id'];
                    $item['student_id'] = $paramArray['student_id'];
                    $item['company_id'] = $paramArray['company_id'];
                    $item['school_id'] = $paramArray['school_id'];
                    $item['coupons_isuse'] = '0';

                    $Model = new \Model\Smc\RegistrationModel();
                    $dataList = $Model->getAvailableTicketList($item);

                    $vals['GoodsCoupons'] = $dataList;


                }
                $val['list'] = $pricing;
            }


            if ($val['sellgoods_type'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "pricing_id,course_id,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$val['tuition_id']}'");
                $val['pricing_id'] = $a['pricing_id'];
                $val['course_id'] = $a['course_id'];
                $val['sellingprice'] = intval($a['tuition_sellingprice']);
                $val['num'] = intval($a['tuition_buypiece']);
                $b = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$a['pricing_id']}'");
                $val['agreement_id'] = $b['agreement_id'];
                $val['class_id'] = '';
                $c = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id = '{$a['course_id']}'");
                $val['course_inclasstype'] = $c['course_inclasstype'];

            }

            if ($val['goods_id'] > '0') {
                $couponsPrice = $this->DataControl->selectOne("select sum(ordercoupons_price) as price from smc_payfee_order_coupons as c where c.order_pid = '{$paramArray['order_pid']}' and c.goods_id = '{$val['goods_id']}'");
            }
            if ($val['course_id'] > '0') {
                $couponsPrice = $this->DataControl->selectOne("select sum(ordercoupons_price) as price from smc_payfee_order_coupons as c where c.order_pid = '{$paramArray['order_pid']}' and c.course_id = '{$val['course_id']}'");
            }
            $val['couponsPrice'] = $couponsPrice['price'];

            if (!$couponsPrice['price']) {
                $val['couponsPrice'] = '0';
            }


            $count = $this->DataControl->selectOne("select count(c.coupons_id) as num from smc_student_coupons as c WHERE c.student_id = '{$paramArray['student_id']}' and c.coupons_playclass = '0'");

            $val['CouponsCount'] = $count['num'];

            if ($val['goods_id'] > '0') {
                $alreadyCoupons = $this->DataControl->selectClear("select c.coupons_id,c.coupons_pid,c.coupons_name from smc_student_coupons as c left join smc_payfee_order_coupons as o on o.coupons_pid = c.coupons_pid where o.goods_id = '{$val['goods_id']}' and o.order_pid = '{$paramArray['order_pid']}' and coupons_playclass = '0'");
            }
            if ($val['course_id'] > '0') {
                $alreadyCoupons = $this->DataControl->selectClear("select c.coupons_id,c.coupons_pid,c.coupons_name from smc_student_coupons as c left join smc_payfee_order_coupons as o on o.coupons_pid = c.coupons_pid where o.course_id = '{$val['course_id']}' and o.order_pid = '{$paramArray['order_pid']}' and coupons_playclass = '0'");
            }

            if ($alreadyCoupons) {
                $val['alreadyCoupons'] = $alreadyCoupons;
            } else {
                $val['alreadyCoupons'] = array();
            }
        }

        $res['list'] = $goodsList;

        return $res;
    }

    //获取商品优惠券列表
    function GoodsCouponsList($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
//        $course_id = $this->DataControl->getFieldOne("shop_models","course_id","models_id = '{$paramArray['models_id']}'");
        $sql = "
            SELECT
                c.coupons_id,
                c.coupons_pid,
                c.coupons_name,
                c.coupons_type,
                c.coupons_price,
                c.coupons_discount,
                FROM_UNIXTIME( c.coupons_exittime, '%Y-%m-%d' ) AS coupons_exittime,
                t.applytype_cnname,
                t.applytype_rule
            FROM
                smc_student_coupons AS c left join smc_student_coupons_apply as a on c.apply_id = a.apply_id left join smc_code_couponsapplytype as t on t.applytype_branch = a.applytype_branch
            WHERE
                c.student_id = '{$paramArray['student_id']}' 
                AND c.coupons_playclass = '0'
                AND coupons_isuse = '{$paramArray['coupons_isuse']}'
            GROUP BY c.coupons_id 
            LIMIT {$pagestart},{$num}";
        $coupous = $this->DataControl->selectClear($sql);

        $all_num = count($coupous);
//        foreach($coupous as &$val){
//            $a = $this->DataControl->selectClear("select a.course_id from  as a WHERE a.apply_id = '{$val['apply_id']}'");
//        }

        if (!$coupous) {
            $res['list'] = array();
            $res['allnum'] = 0;
        } else {
            $res['list'] = $coupous;
            $res['allnum'] = $all_num;
        }

        return $res;
    }

    //获取订单优惠券列表
    function OrderCouponsList($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = " 
            SELECT
                c.coupons_id,
                c.coupons_pid,
                c.coupons_name,
                c.coupons_type,
                c.coupons_price,
                c.coupons_discount,
                FROM_UNIXTIME( c.coupons_exittime, '%Y-%m-%d' ) AS coupons_exittime,
                t.applytype_cnname,
                t.applytype_rule
            FROM
                smc_student_coupons AS c left join smc_student_coupons_apply as a on c.apply_id = a.apply_id left join smc_code_couponsapplytype as t on t.applytype_branch = a.applytype_branch
            WHERE
                c.student_id = '{$paramArray['student_id']}' 
                AND c.coupons_playclass = '1'
                AND coupons_isuse = '{$paramArray['coupons_isuse']}'
            GROUP BY c.coupons_id
            LIMIT {$pagestart},{$num}";
        $coupous = $this->DataControl->selectClear($sql);

        $all_num = count($coupous);


        if (!$coupous) {
            $res['list'] = array();
            $res['allnum'] = 0;
        } else {
            $res['list'] = $coupous;
            $res['allnum'] = $all_num;
        }

        return $res;
    }

    //提交订单
    function SubmitOrder($paramArray)
    {
        //$schoolOne=$this->DataControl->getFieldOne("smc_school","companies_id","school_id='{$paramArray['school_id']}'");

        /*$data = array();
        $data['trading_pid'] = rand(0, 9999999999);
        $data['order_pid'] = rand(0, 9999999999);
        $data['company_id'] = $paramArray['company_id'];
        $data['companies_id'] = $schoolOne['companies_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['order_from'] = '0';
        $data['order_type'] = '0';
        $data['order_status'] = '1';
        $data['order_allprice'] = $paramArray['order_allprice'];
        $data['order_coupon_price'] = $paramArray['order_coupon_price'];
        $data['order_paymentprice'] = $paramArray['order_paymentprice'];
        $data['order_createtime'] = time();

        $datas = array();
        $paramArray['coupons'] = stripslashes($paramArray['coupons']);
        $couponsList = json_decode($paramArray['coupons'], true);
        foreach ($couponsList as $item) {
            $datas['order_pid'] = $data['order_pid'];
            $datas['course_id'] = $item['course_id'];
            $datas['coupons_pid'] = $item['coupons_pid'];
            $datas['sellgoods_id'] = $item['sellgoods_id'];
            $datas['ordercoupons_price'] = $item['ordercoupons_price'];

            $this->DataControl->insertData('smc_payfee_order_coupons', $data);
            $datass = array();
            $datass['coupons_isuse'] = '1';
            $datass['coupons_usetime'] = time();
            $this->DataControl->updateData("smc_student_coupons", "coupons_pid = '{$item['coupons_pid']}'", $datass);
        }

        if ($this->DataControl->insertData("", $data)) {
            $res = array('error' => 0, 'errortip' => "提交订单成功!");
        } else {
            $res = array('error' => 1, 'errortip' => "提交订单失败!");
        }
        */
        $res = array('error' => 1, 'errortip' => "提交订单接口已关闭!");
        return $res;
    }

    //根据订单编号获取支付信息
    function PayInfo($paramArray)
    {
        $sql = "
            SELECT
                p.pay_price,
                p.pay_pid,
                p.order_pid,
                p.pay_issuccess,
                s.student_cnname,
                s.student_enname,
                s.student_branch,
                (SELECT t.school_shortname FROM smc_school as t WHERE t.school_id = o.school_id) as school_shortname,
                o.order_from,o.order_type,o.order_status 
            FROM
                smc_payfee_order_pay AS p
                left join smc_payfee_order as o on p.order_pid = o.order_pid
                LEFT JOIN smc_student AS s ON s.student_id = o.student_id 
            WHERE
                p.pay_pid = '{$paramArray['pay_pid']}'";

        $order_pid = $this->DataControl->getFieldOne("smc_payfee_order_pay", "order_pid", "pay_pid = '{$paramArray['pay_pid']}'");
        $issign = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id", "order_pid = '{$order_pid['order_pid']}' and protocol_issign = '1'");
        if ($issign) {
            $status = '1';
        } else {
            $status = '0';
        }


        $info = $this->DataControl->selectOne($sql);

        $res['list'] = $info;
        $res['issign'] = $status;

        return $res;
    }

    //获取订单跟踪记录
    function getOrderTrack($paramArray)
    {
        $sql = "
            SELECT
                t.tracks_title,
                t.tracks_information,
                FROM_UNIXTIME( t.tracks_time, '%Y-%m-%d %H:%i:%s' ) AS tracks_time
            FROM
                smc_payfee_order_tracks AS t
            WHERE
                t.order_pid = '{$paramArray['order_pid']}'
            ORDER BY t.tracks_time DESC";

        $info = $this->DataControl->selectClear($sql);

        $res['list'] = $info;

        return $res;
    }

    //我的协议
    function MyProtocol($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_cnname like '%{$paramArray['keyword']}%' or c.course_branch like '%{$paramArray['keyword']}%' or c.course_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['order_pid']) && $paramArray['order_pid'] !== "") {
            $datawhere .= " and p.order_pid ='{$paramArray['order_pid']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['student_id'] && $paramArray['paypid'] == '') {

            $student_id = $paramArray['student_id'];

        } else {
            $order_pid = $this->DataControl->getFieldOne("smc_payfee_order_pay", "order_pid", "pay_pid = '{$paramArray['paypid']}'");
            $id = $this->DataControl->getFieldOne("smc_payfee_order", "student_id,company_id", "order_pid = '{$order_pid['order_pid']}'");
            $student_id = $id['student_id'];

            $issign = $this->DataControl->getFieldOne("gmc_company", "company_issign", "company_id = '{$id['company_id']}'");
        }

        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_price,
                p.protocol_recourse,
                p.order_pid,
                p.protocol_pid,
                p.protocol_issign,
                FROM_UNIXTIME( p.protocol_createtime, '%Y-%m-%d %H:%i:%s' ) AS protocol_createtime,
                c.course_cnname,
                c.course_branch
            FROM
                smc_student_protocol AS p left join smc_course as c on p.course_id = c.course_id
            WHERE
                {$datawhere} and p.protocol_isdel = '0' and p.student_id = '{$student_id}'
            Order by p.protocol_issign ASC,p.protocol_createtime DESC
            LIMIT {$pagestart},{$num}";
        $info = $this->DataControl->selectClear($sql);

        if($info){
            foreach($info as &$value){
                if($value['protocol_recourse']){
                    $value['course_cnname'] = $value['protocol_recourse'];
                }
            }
        }


        $sqls = "
            SELECT
                p.protocol_id,
                p.protocol_price,
                p.protocol_recourse,
                p.order_pid,
                p.protocol_pid,
                p.protocol_issign,
                FROM_UNIXTIME( p.protocol_createtime, '%Y-%m-%d %H:%i:%s' ) AS protocol_createtime,
                c.course_cnname,
                c.course_branch
            FROM
                smc_student_protocol AS p left join smc_course as c on p.course_id = c.course_id
            WHERE
                {$datawhere} and p.protocol_isdel = '0' and p.student_id = '{$student_id}'";
        $infos = $this->DataControl->selectClear($sqls);

        $all_num = count($infos);

        if (!$info) {
            $res['list'] = array();
            $res['allnum'] = 0;
        } else {
            $res['list'] = $info;
            $res['allnum'] = $all_num;
            $res['issign'] = $issign['company_issign'];
        }

        return $res;
    }

    //申请退款挽留页面
    function refundToStay($paramArray)
    {
        $sql = "
            SELECT
                s.sellgoods_id,
                s.sellgoods_name,
                s.sellgoods_type,
                s.sellgoods_desc,
                s.sellgoods_listimg,
                s.tuition_id,
                s.coursepacks_id,
                s.goods_id
            FROM
                smc_payfee_order_goods AS g
                LEFT JOIN shop_sellgoods AS s ON g.sellgoods_id = s.sellgoods_id 
            WHERE
                g.order_pid = '{$paramArray['order_pid']}' 
                AND g.sellgoods_id > 0";
        $goodsList = $this->DataControl->selectClear($sql);
        foreach ($goodsList as &$val) {
            if ($val['sellgoods_type'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$val['tuition_id']}'");
                $val['price'] = $a['tuition_sellingprice'];
            } elseif ($val['sellgoods_type'] == '1') {
                $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$val['coursepacks_id']}'");
                $val['price'] = $b['price'];
            } elseif ($val['sellgoods_type'] == '2') {
                $c = $this->DataControl->selectOne("select goods_vipprice from erp_goods where goods_id = '{$val['goods_id']}'");
                $val['price'] = $c['goods_vipprice'];
            } elseif ($val['sellgoods_type'] == '3') {
                $val['price'] = $c['feeitem_price'];
            }
        }

        $phone = $this->DataControl->getFieldOne("gmc_company", "company_fax", "company_id = '{$paramArray['company_id']}'");
        $allprice = $this->DataControl->getFieldOne("smc_payfee_order", "order_paymentprice", "order_pid = '{$paramArray['order_pid']}'");

        $res['list'] = $goodsList;
        $res['phone'] = $phone['company_fax'];
        $res['allprice'] = $allprice['order_paymentprice'];

        return $res;
    }

    //订单详情
    function orderDetail($paramArray)
    {
        if ($paramArray['order_type'] == '0') {
            $sql = "
                SELECT
                    p.pay_issuccess,
                    p.pay_pid,
                    p.pay_price,
                    f.pricing_name,
	                se.sellgoods_id,
	                se.sellgoods_listimg,
	                p.pay_typename,
	                FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d' ) AS pay_successtime
                FROM
                    smc_payfee_order_pay AS p
                    LEFT JOIN smc_payfee_order_course AS c ON p.order_pid = c.order_pid
                    LEFT JOIN smc_fee_pricing AS f ON f.pricing_id = c.pricing_id
                    left join smc_fee_pricing_tuition as t on t.pricing_id = c.pricing_id
	                left join shop_sellgoods as se on se.tuition_id = t.tuition_id
                WHERE
                    p.order_pid = '{$paramArray['order_pid']}' and p.pay_issuccess > '-1'
                    group by p.pay_pid";
        } else {
            $sql = "select p.pay_issuccess,p.pay_pid,p.pay_typename,FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d' ) AS pay_successtime,p.pay_price 
FROM smc_payfee_order_pay as p where p.order_pid = '{$paramArray['order_pid']}' and p.pay_issuccess > '-1'";
        }
        $goodsList = $this->DataControl->selectClear($sql);

        if ($paramArray['order_type'] == '0') {
            $a = $this->DataControl->selectClear("select c.course_cnname,oc.ordercourse_buynums as num,oc.ordercourse_totalprice from smc_payfee_order_course as oc left join smc_course as c on c.course_id = oc.course_id where oc.order_pid = '{$paramArray['order_pid']}' UNION ALL select g.goods_cnname,og.ordergoods_buynums,og.ordergoods_unitprice from smc_payfee_order_goods as og left join erp_goods as g on g.goods_id = og.goods_id where og.order_pid = '{$paramArray['order_pid']}' UNION ALL select f.feeitem_cnname AS goods_cnname,i.item_buynums AS ordergoods_buynums,i.item_unitprice AS ordergoods_unitprice from smc_payfee_order_item AS i LEFT JOIN smc_code_feeitem AS f ON f.feeitem_branch = i.feeitem_branch AND f.company_id = '{$paramArray['company_id']}' where i.order_pid = '{$paramArray['order_pid']}'");
        } else {
            $a = $this->DataControl->selectClear("select g.goods_cnname,og.ordergoods_buynums,og.ordergoods_unitprice from smc_payfee_order_goods as og left join erp_goods as g on g.goods_id = og.goods_id where og.order_pid = '{$paramArray['order_pid']}' UNION ALL select f.feeitem_cnname AS goods_cnname,i.item_buynums AS ordergoods_buynums,i.item_unitprice AS ordergoods_unitprice from smc_payfee_order_item AS i LEFT JOIN smc_code_feeitem AS f ON f.feeitem_branch = i.feeitem_branch AND f.company_id = '{$paramArray['company_id']}' where i.order_pid = '{$paramArray['order_pid']}'");
        }

        if($goodsList){
            foreach ($goodsList as &$val) {
                if (!$val['sellgoods_listimg'] || trim($val['sellgoods_listimg']) == '') {
                    $val['sellgoods_listimg'] = 'https://pic.kedingdang.com/schoolmanage/202004301621x714466209.png';
                }
            }
        }

        $order = $this->DataControl->getFieldOne("smc_payfee_order", "order_pid,FROM_UNIXTIME( order_createtime, '%Y-%m-%d %H:%i:%s' ) AS order_createtime,order_from,order_from as order_from_status,order_type,order_allprice,order_coupon_price,order_paymentprice,order_arrearageprice,order_status", "order_pid = '{$paramArray['order_pid']}'");
        $pay_pid = $this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_pid", "order_pid = '{$paramArray['order_pid']}'");
        if ($order['order_from'] == '0') {
            $order['order_from'] = '家长自订';
        } elseif ($order['order_from'] == '1') {
            $order['order_from'] = '教师下单';
        } elseif ($order['order_from'] == '2') {
            $order['order_from'] = '系统同步';
        }

        if ($order['order_status'] == '0' || $order['order_status'] == '1' || $order['order_status'] == '3') {
            $order['order_status_name'] = "待支付";
            $order['order_status'] = '1';  //判断页面显示的按钮是什么按钮
        } elseif ($order['order_status'] == '4') {
            $order['order_status_name'] = "已完成";
            $order['order_status'] = '2';
        } elseif ($order['order_status'] == '-1') {
            $order['order_status_name'] = "已取消";
            $order['order_status'] = '3';
        } elseif ($order['order_status'] == '2') {
            $order['order_status_name'] = "支付中";
            $order['order_status'] = '4';
        }

        $pay = $this->DataControl->selectClear("select pay_typename,pay_pid,FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d %H:%i:%s' ) AS pay_successtime,pay_successtime as pay_successtimes,pay_issuccess,paytype_code from smc_payfee_order_pay as p where p.order_pid = '{$order['order_pid']}'  and p.pay_issuccess > '-1'");

        if($pay){
            foreach ($pay as &$val) {
                if ($val['pay_successtimes'] == '0') {
                    $val['pay_successtime'] = '';
                }
            }
        }

//        if($order['order_status'] == '0'){
//            $order['order_status_name'] = '待审核';
//        }elseif($order['order_status'] == '1'){
//            $order['order_status_name'] = '待支付';
//        }elseif($order['order_status'] == '2'){
//            $order['order_status_name'] = '支付中';
//        }elseif($order['order_status'] == '3'){
//            $order['order_status_name'] = '处理中';
//        }elseif($order['order_status'] == '4'){
//            $order['order_status_name'] = '已完成';
//        }elseif($order['order_status'] == '-1'){
//            $order['order_status_name'] = '已取消';
//        }elseif($order['order_status'] == '-2'){
//            $order['order_status_name'] = '已拒绝';
//        }

//        $pro = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id", "order_pid = '{$paramArray['order_pid']}' and protocol_issign = '1'");
//        if ($pro) {
//            $res['status'] = 1;
//        } else {
//            $res['status'] = 0;
//        }

        $order_protocol = $this->DataControl->selectOne("select po.order_type,c.school_payaftersignlimit
                ,(select count(1) from smc_student_protocol where student_id=po.student_id and order_pid=po.order_pid and protocol_isdel=0) as protocol_num
                ,(select count(1) from smc_student_protocol where student_id=po.student_id and order_pid=po.order_pid and protocol_isdel=0 and protocol_issign=1) as protocol_sign_num 
                from smc_payfee_order as po 
                left join smc_school  as c on po.school_id=c.school_id
                where po.order_pid = '{$order['order_pid']}' ");
        if ($order_protocol && $order_protocol['school_payaftersignlimit'] == 1) {
            if ($order_protocol['order_type'] == 0 && ($order_protocol['protocol_num'] == 0 || $order_protocol['protocol_num'] !== $order_protocol['protocol_sign_num'])) {
                $res['status'] = 0;
            } else {
                $res['status'] = 1;
            }
        } else {
            $res['status'] = 1;
        }

        $res['list'] = $goodsList;
        $res['order'] = $order;
        $res['pay_pid'] = $pay_pid['pay_pid'];
        $res['pay'] = $pay;
        $res['goods'] = $a;

        return $res;
    }

    //获取学员的订单列表
    function myOrders($paramArray)
    {
        $datawhere = "o.company_id = '{$paramArray['company_id']}'  and o.student_id = '{$paramArray['student_id']}'";//and o.order_type <> '2'
        if (isset($paramArray['order_status']) && $paramArray['order_status'] == '1') {
            $datawhere .= " and  (o.order_status = '0' or o.order_status = '1' or o.order_status = '2' or o.order_status = '3' )";
        } elseif (isset($paramArray['order_status']) && $paramArray['order_status'] == '2') {
            $datawhere .= " and  (o.order_status = '4'  )";
        } elseif (isset($paramArray['order_status']) && $paramArray['order_status'] == '3') {
            $datawhere .= " and  (o.order_status = '-1'  )";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT o.trading_pid,o.order_id,o.order_pid,FROM_UNIXTIME(o.order_createtime, '%Y-%m-%d') as createtime,o.order_status,o.order_paymentprice,o.order_type,o.order_from,o.order_arrearageprice
                FROM smc_payfee_order AS o WHERE {$datawhere} and o.order_type <> '1' ORDER BY o.order_createtime DESC limit {$pagestart},{$num} ";//
        $orderlist = $this->DataControl->selectClear($sql);
        //是否可以重新下单 1 可以  0 不可以
        $placeorder = 1;
        if ($orderlist) {
            $orderStatus = array("0" => "待审核", "1" => "待付款", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝");
            foreach ($orderlist as &$ordervar) {

                $b = $this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_id", "order_pid = '{$ordervar['order_pid']}' and pay_issuccess <> '0'");

                if ($b) {
                    $ordervar['iscancel'] = 0;
                } else {
                    $ordervar['iscancel'] = 1;
                }

                $a = $this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_id", "order_pid = '{$ordervar['order_pid']}'");
                if ($a) {
                    $ordervar['status'] = 1;
                } else {
                    $ordervar['status'] = 0;
                }

                $ordervar['order_status_name'] = $orderStatus[$ordervar['order_status']];
                if ($ordervar['order_status'] == '0' || $ordervar['order_status'] == '1'
                    || $ordervar['order_status'] == '2' || $ordervar['order_status'] == '3') {
                    $ordervar['order_status'] = '1';  //判断页面显示的按钮是什么按钮
                } elseif ($ordervar['order_status'] == '4') {
                    $ordervar['order_status'] = '2';
                } elseif ($ordervar['order_status'] == '-1') {
                    $ordervar['order_status'] = '3';
                }

                if ($ordervar['order_type'] == '0') {
                    $goodslist = $this->DataControl->selectClear("
SELECT
	c.course_id AS sellgoods_id,
	c.course_cnname AS goods_cnname,
	c.course_branch AS goods_tags,
	o.ordercourse_totalprice AS old_price,
	o.ordercourse_totalprice AS price,
	c.course_imglist AS goods_img,
	o.ordercourse_buynums AS buynums,
	o.ordercourse_totalprice AS totalprice
FROM
	smc_payfee_order_course AS o,smc_course AS c
WHERE o.course_id = c.course_id AND
	o.order_pid =  '{$ordervar['order_pid']}' UNION ALL
SELECT
	s.sellgoods_id AS sellgoods_id,
	g.goods_cnname,
	s.sellgoods_tags,
	g.goods_originalprice AS old_price,
	g.goods_vipprice AS price,
	s.sellgoods_listimg AS goods_img,
	o.ordergoods_buynums AS buynums,
	o.ordergoods_totalprice AS totalprice
FROM
	smc_payfee_order_goods AS o
	LEFT JOIN erp_goods AS g ON o.goods_id = g.goods_id
	LEFT JOIN shop_sellgoods AS s ON o.sellgoods_id = s.sellgoods_id 
	AND s.sellgoods_type = '2' 
WHERE
	o.order_pid = '{$ordervar['order_pid']}' UNION ALL
SELECT
	f.feeitem_id AS sellgoods_id,
	f.feeitem_cnname AS goods_cnname,
	f.feeitem_cnname AS sellgoods_tags,
	i.item_unitprice AS old_price,
	i.item_unitprice AS price,
	ss.sellgoods_listimg AS goods_img,
	i.item_buynums AS buynums,
	i.item_totalprice AS totalprice
FROM
	smc_payfee_order_item AS i
	LEFT JOIN smc_code_feeitem AS f ON f.feeitem_branch = i.feeitem_branch 
	AND f.company_id = '{$paramArray['company_id']}'
	LEFT JOIN shop_sellgoods AS ss ON ss.sellgoods_id = i.sellgoods_id 
	AND ss.company_id = '{$paramArray['company_id']}' 
WHERE
	i.order_pid = '{$ordervar['order_pid']}' 
	AND i.feeitem_branch <> ''");

                    $placeorder = '0';

//                    o.ordercourse_unitrefund AS old_price,
//	                o.ordercourse_unitprice AS price,
                } elseif ($ordervar['order_type'] == '1') {
                    $goodslist = $this->DataControl->selectClear("
SELECT
	s.sellgoods_id AS sellgoods_id,
	g.goods_cnname,
	s.sellgoods_tags,
	g.goods_originalprice AS old_price,
	g.goods_vipprice AS price,
	s.sellgoods_listimg AS goods_img,
	o.ordergoods_buynums AS buynums,
	o.ordergoods_totalprice AS totalprice,
	s.sellgoods_cartchange,
	s.class_id 
FROM
	smc_payfee_order_goods AS o
	LEFT JOIN erp_goods AS g ON o.goods_id = g.goods_id
	LEFT JOIN shop_sellgoods AS s ON o.sellgoods_id = s.sellgoods_id 
	AND s.sellgoods_type = '2' 
WHERE
	o.order_pid = '{$ordervar['order_pid']}' UNION ALL
SELECT
	f.feeitem_id AS sellgoods_id,
	f.feeitem_cnname AS goods_cnname,
	f.feeitem_cnname AS sellgoods_tags,
	i.item_unitprice AS old_price,
	i.item_unitprice AS price,
	'' AS goods_img,
	i.item_buynums AS buynums,
	i.item_totalprice AS totalprice,
	'' as sellgoods_cartchange,
	'' as class_id 
FROM
	smc_payfee_order_item AS i
	LEFT JOIN smc_code_feeitem AS f ON f.feeitem_branch = i.feeitem_branch 
	AND f.company_id = '{$paramArray['company_id']}'
	LEFT JOIN shop_sellgoods AS s ON i.sellgoods_id = s.sellgoods_id 
	AND s.company_id = '{$paramArray['company_id']}'
WHERE
	i.order_pid = '{$ordervar['order_pid']}' 
	AND i.feeitem_branch <> ''");

                    if ($goodslist) {
                        foreach ($goodslist as &$goodsvar) {
                            if ($ordervar['order_from'] == '1' || $goodsvar['class_id'] <> '1') {
                                $placeorder = '0';
                            }
                        }
                    }

                } elseif ($ordervar['order_type'] == '2') {
//                    $payOne = $this->DataControl->selectOne("select * from smc_payfee_order_pay WHERE order_pid = '{$ordervar['order_pid']}'");
                    $goodslist = array();
                    $goodslist[0]['sellgoods_id'] = '';
                    $goodslist[0]['goods_cnname'] = "充值类订单";
//                    $goodslist[0]['sellgoods_tags'] = '';
                    $goodslist[0]['old_price'] = $ordervar['order_paymentprice'];
                    $goodslist[0]['price'] = $ordervar['order_paymentprice'];
                    $goodslist[0]['goods_img'] = '';
                    $goodslist[0]['buynums'] = 1;
                    $goodslist[0]['totalprice'] = $ordervar['order_paymentprice'];
                    $goodslist[0]['sellgoods_cartchange'] = '';
//                    $goodslist[0]['class_id'] = '';
                }

                if (is_array($goodslist)) {
                    foreach ($goodslist as &$goodsvar) {
//                        $goodsvar['sellgoods_cartchange'] = 0;
                        if ($goodsvar['old_price'] < 0) {
                            $goodsvar['old_price'] = 0;
                        }
                        if ($goodsvar['price'] < 0) {
                            $goodsvar['price'] = 0;
                        }
                        if ($goodsvar['totalprice'] < 0) {
                            $goodsvar['totalprice'] = 0;
                        }
                        if ($goodsvar['goods_img'] == '') {
                            $goodsvar['goods_img'] = "https://pic.kedingdang.com/schoolmanage/201912152050x969940430.jpg";
                        }
                    }
                    $ordervar['goodsnum'] = count($goodslist);
                    $ordervar['goodslist'] = $goodslist;
                } else {
                    $ordervar['goodsnum'] = 0;
                    $ordervar['goodslist'] = array();
                }

                $ordervar['placeorder'] = $placeorder;
            }
        }

        $allnum = $this->DataControl->selectOne(" SELECT COUNT(o.order_id) as num FROM smc_payfee_order AS o WHERE {$datawhere}");


        $result = array();
        $result["data"] = $orderlist;
        $result["allnum"] = $allnum['num'];

        return $result;
    }

    function createRechargeOrder($request)
    {
        $studentOne = $this->DataControl->getOne('smc_student', "student_branch='{$request['student_branch']}'");
        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "请输入正确的学员编号";
            return false;
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,company_id", "school_branch='{$request['school_branch']}'");
        if (!$schoolOne) {
            $this->error = true;
            $this->errortip = "请输入正确的校区编号";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$schoolOne['company_id']}' and account_class=1");

        $publicArray = array();
        $publicArray['company_id'] = $schoolOne['company_id'];
        $publicArray['school_id'] = $schoolOne['school_id'];
        $publicArray['staffer_id'] = $stafferOne['staffer_id'];

        $OrderModel = new \Model\Smc\OrderModel($publicArray);

        if (!isset($request['mobile']) || $request['mobile'] == '') {
            $this->error = true;
            $this->errortip = "请输入手机号";
            return false;
        }

        if (!isset($request['companies_id']) || $request['companies_id'] == '') {
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        if (!isset($request['price']) || $request['price'] <= 0 || $request['price'] == '') {
            $this->error = true;
            $this->errortip = "请输入正确的充值金额";
            return false;
        }

        $price = $request['price'];

        $title = $this->LgStringSwitch("创建订单");

        $order_pid = $OrderModel->createOrder($studentOne['student_id'], $code = "Recharge", $price, '', $order_type = 2, $coupon_price = 0, $market_price = 0, $title, $this->LgStringSwitch("订单提交成功，请尽快支付~"), '', 0, 1, '', 0, 0,0,0,0,$request['companies_id']);

        $OrderHandleModel = new \Model\Smc\OrderHandleModel($publicArray, $order_pid);
        $paypid = $OrderHandleModel->orderPay('', '0', '账户充值', '', $request['price'], '', 3);

        return $paypid;
    }


    public function __call($method, $args)
    {

    }
}
