<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/1
 * Time: 11:42
 */

namespace Model\Scshop;


class PublicModel extends \Model\modelTpl{
    public $aeskey = 'kedingdang%C4567';
    public $error = false;
    public $errortip = false;
    public $oktip = false;//专案提示
    function __construct() {
        parent::__construct ();
    }

    //用户获取token
    function getParentToken($params=array()){
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_tokencode,parenter_tokenencrypt","parenter_id='{$params['parenter_id']}'");
        if(!$parenterOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-m")));//-d
        if($md5tokenbar == $parenterOne["parenter_tokenencrypt"]){
            $token = $parenterOne["parenter_tokenencrypt"];
        }else{
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m")));//-d
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }


    function loadSchoolLongLat($paramArray){
        $schoolList = $this->DataControl->selectClear("select school_id,school_address from smc_school
WHERE company_id = '{$paramArray['company_id']}' AND school_isclose = '0' AND school_istest = '0' AND school_longitude = '0' limit 0,30");
        if($schoolList){
            foreach ($schoolList as $schoolvar){
                $paramarray = array(
                    'output' => "json",
                    'address' => $schoolvar['school_address'],
                    'key' => "6760888f01d2996303c3c3ef3919da5c"
                );

                $getMapurl = request_by_curl("http://restapi.amap.com/v3/geocode/geo", dataEncode($paramarray),"GET");
                $json_play = new \Webjson();
                $PointsJson = $json_play->decode($getMapurl,"1");
                $location = explode(",",$PointsJson['geocodes']['0']['location']);
                $data = array();
                $data['school_longitude'] = $location['0'];
                $data['school_latitude'] = $location['1'];
                $this->DataControl->updateData("smc_school","school_id = '{$schoolvar['school_id']}'  ",$data);
            }
            return true;
        }else{
            return false;
        }
    }

    function getCompanyOne($paramArray){
        $CompanyOne  = $this->DataControl->selectOne("select g.company_id,g.company_shopicourl,g.company_shortname,g.company_cnname,
g.company_logo,g.company_fax,g.company_issign,g.company_isstock FROM gmc_company AS g
where g.company_id = '{$paramArray['company_id']}' limit 0,1");

        $CompanyOne['publichaimg'] = 'https://pic.kedingdang.com/schoolmanage/202004301621x714466209.png';

        return $CompanyOne;
    }

    /*function getProCityAreas($paramArray){
        $Province = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '1'");
        if($Province){
            foreach($Province as &$Provincevar){
                $City = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$Provincevar['region_id']}'");
                $Provincevar['children'] = $City;
                if($Provincevar['children']){
                    foreach($Provincevar['children'] as &$Cityvar){
                        $Area = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$Cityvar['region_id']}'");
                        $Cityvar['children'] = $Area;
                    }
                }
            }
        }
        return $Province;
    }*/

    //获取集团下属学校省份数据
    function getSchoolProvince($paramArray){
        $datawhere = "s.school_province = r.region_id AND s.school_istest <> '1'
        AND s.school_isclose = '0' AND s.school_province <> '0' AND s.company_id = '{$paramArray['company_id']}'";
        $Province = $this->DataControl->selectClear("SELECT r.region_id, r.region_name
FROM smc_school AS s, smc_code_region AS r WHERE {$datawhere} GROUP BY r.region_id");
        return $Province;
    }

    //获取集团下属学校城市数据
    function getSchoolCity($paramArray){
        $datawhere = "s.school_city = r.region_id AND s.school_istest <> '1'
        AND s.school_isclose = '0' AND s.school_city <> '0' AND s.company_id = '{$paramArray['company_id']}'";
        if(isset($request['region_id']) && $request['region_id'] != ''){
            $datawhere .= " and r.parent_id = '{$request['region_id']}'";
        }
        $City = $this->DataControl->selectClear("SELECT r.region_id, r.region_name
FROM smc_school AS s, smc_code_region AS r WHERE {$datawhere} GROUP BY r.region_id");
        return $City;
    }

    //获取集团下属学校区域数据
    function getSchoolAreas($paramArray){
        $datawhere = "s.school_area = r.region_id AND s.school_istest <> '1'
        AND s.school_isclose = '0' AND s.school_area <> '0' AND s.company_id = '{$paramArray['company_id']}'";
        if(isset($request['region_id']) && $request['region_id'] != ''){
            $datawhere .= " and r.parent_id = '{$request['region_id']}'";
        }
        $Areas = $this->DataControl->selectClear("SELECT r.region_id, r.region_name
FROM smc_school AS s, smc_code_region AS r WHERE {$datawhere} GROUP BY r.region_id");
        return $Areas;
    }

    //获取集团下属所有学校数据
    function getAllSchoolCity($paramArray){
        $datawhere = "s.school_city = r.region_id AND s.school_istest <> '1' AND s.school_isclose = '0'";
        if(isset($paramArray['company_id']) && $paramArray['company_id'] != ''){
            $datawhere .= " and s.company_id = '{$paramArray['company_id']}'";
        }
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and r.region_name like '%{$paramArray['keyword']}%'";
        }
        if(isset($paramArray['region_initial']) && $paramArray['region_initial'] != ''){
            $datawhere .= " and r.region_initial = '{$paramArray['region_initial']}'";
        }
        $Province = $this->DataControl->selectClear("SELECT
	r.region_id,
	r.region_name,
	r.region_initial
FROM
	smc_school AS s,
	smc_code_region AS r
WHERE {$datawhere} GROUP BY r.region_initial ASC, r.region_id ASC");
        if($Province){
            $allList = array();
            foreach ($Province as $ProvinceVar){
                $allList[$ProvinceVar['region_initial']]['letter'] = $ProvinceVar['region_initial'];
                $allList[$ProvinceVar['region_initial']]['list'][] = $ProvinceVar;
            }
        }

        $datalist = array();
        $datainitial = array();
        if($allList){
            $k = 0;
            foreach ($allList as $key=>$listVar){
                $datainitial[]=$key;
                $datalist[$k] = $listVar;
                $k++;
            }
        }
        $schoolCity = array();
        $schoolCity['result'] = $datalist;
        $schoolCity['initial'] = $datainitial;

        return $schoolCity;
    }

    //加密结束函数
    function jmstingLogin($paramArray){
        $jmstring = $paramArray['jm'];
        $aes = new \Aesencdec($this->aeskey,'','AES-128-ECB');
        $xssting = $aes->decrypt($jmstring);//解密
        $xsArray = json_decode($xssting,1);//转化为数组
        if((string)$xsArray['timesteps'] !== trim($paramArray['timesteps'])){
            $this->errortip = '授权时间和连接时间不一致';
            $this->error = true;
            return false;
        }

        if($paramArray['limit'] != '1'){
            if($xsArray['timesteps']+60*5 < time() || $xsArray['timesteps']-60 > time()){
                $maxtimes = date("Y-m-d H:i:s",$xsArray['timesteps']+60*5);
                $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";
                $this->error = true;
                return false;
            }
        }


        if($xsArray['branch'] == ''){
            $this->errortip = '编号未获取成功！';
            $this->error = true;
            return false;
        }

        //激活用户信息
        $studentOne = $this->DataControl->selectOne("SELECT s.student_id,s.company_id,s.student_branch,s.student_cnname FROM smc_student AS s
WHERE s.student_branch = '{$xsArray['branch']}'");
        if(!$studentOne){
            $studentOne = $this->DataControl->selectOne("SELECT s.student_id, s.company_id, s.student_branch, s.student_cnname
FROM smc_student AS s,smc_student_family AS f
WHERE s.student_id = f.student_id AND f.family_mobile = '{$xsArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' limit 0,1");
            /*if(!$studentOne){
                $this->errortip = '学员编号不存在，请确认学号准确性';
                $this->error = true;
                return false;
            }*/
        }
        if($studentOne){
            $familyOne = $this->DataControl->selectOne("select f.family_id,f.parenter_id from smc_student_family as f
WHERE f.family_mobile = '{$xsArray['mobile']}' AND f.student_id = '{$studentOne['student_id']}'");
            /*if(!$familyOne){
                $this->errortip = '您的手机号不在学员家属信息内，请联系教师添加！';
                $this->error = true;
                return false;
            }*/
        }
        if(!$this->DataControl->getFieldOne("smc_parenter", "parenter_id", " parenter_mobile = '{$xsArray['mobile']}'")){
            //激活课叮铛账号
            $parenter = array();
            $parenter['parenter_mobile'] = $xsArray['mobile'];
            $parenter['parenter_pass'] = md5(substr($xsArray['mobile'],-6));
            $parenter['parenter_bakpass'] = substr($xsArray['mobile'],-6);
            $parenter['parenter_addtime'] = time();
            $this->DataControl->insertData("smc_parenter", $parenter);
        }

        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", " parenter_mobile = '{$xsArray['mobile']}'");
        $schoolOne = $this->DataControl->selectOne("SELECT s.school_id,s.district_id FROM smc_school AS s,smc_student_enrolled AS e
WHERE s.school_id = e.school_id AND e.student_id = '{$studentOne['student_id']}' AND e.enrolled_status >= '0' ORDER BY e.enrolled_createtime DESC limit 0,1");
        if(!$schoolOne){
//            $bakinfo = request_by_curl("https://api.kidcastle.com.cn/Jdbpay/hrmworker","mobile={$parenterOne['parenter_mobile']}","GET",array());
            $bakinfo = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi","mobile={$parenterOne['parenter_mobile']}","GET",array());
            $apiArray = json_decode($bakinfo,'1');
//            $datawork = array();
//            $datawork['mobile'] = $parenterOne['parenter_mobile'];
//            $bakinfo = request_by_curl("https://api.kidcastle.com.cn/Jdbpay/hrmworker",dataEncode($datawork),"GET",array());
//            $apiArray = json_decode($bakinfo,'1');
            if($apiArray['error'] == '0'){
                $result = array();
                $result['parenter_id'] = $parenterOne['parenter_id'];
                $result['company_id'] = $studentOne['company_id'];
                $result['student_id'] = 0;
                $result['school_id'] = 0;
                $result['district_id'] = 0;
                $result['token'] = $this->getParentToken($parenterOne);

                //是否是职工
                $parenterOne = $this->DataControl->getFieldOne("smc_parenter",'parenter_mobile',"parenter_id='{$parenterOne['parenter_id']}'");
                if($parenterOne) {
                    $stafferOne = $this->DataControl->getFieldOne('smc_staffer','staffer_id,staffer_cnname',"staffer_mobile='{$parenterOne['parenter_mobile']}' and company_id = '{$studentOne['company_id']}' ");
                    if ($stafferOne) {
                        $result['isstaff'] = 1;
                    }else {
                        $bakinfo = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi", "mobile={$parenterOne['parenter_mobile']}", "GET", array());
                        $apiArray = json_decode($bakinfo, '1');
                        if($apiArray['error'] == '0') {
                            $result['isstaff'] = 1;
                        }else{
                            $result['isstaff'] = 0;
                            $result['lgerrortip'] = '您好，您的信息未登记在系统内，无法使用微商城，请联系您的老师确认您的信息状态，谢谢。';
                        }
                    }
                }else {
                    $result['isstaff'] = 0;
                    $result['lgerrortip'] = '您好，您的信息未登记在系统内，无法使用微商城，请联系您的老师确认您的信息状态，谢谢。';
                }
                return $result;
            }else{
                $this->errortip = '您没有入校记录，联系您的教师添加！';
                $this->error = true;
                return false;
            }
        }else{
            $result = array();
            //家长的
            $result['parenter_id'] = $parenterOne['parenter_id'];
            //学员的
            $result['company_id'] = is_null($studentOne['company_id'])?0:$studentOne['company_id'];
            $result['student_id'] = is_null($studentOne['student_id'])?0:$studentOne['student_id'];
            //学校的
            $result['school_id'] = is_null($schoolOne['school_id'])?0:$schoolOne['school_id'];
            $result['district_id'] = is_null($schoolOne['district_id'])?0:$schoolOne['district_id'];
            $result['token'] = $this->getParentToken($parenterOne);

            $stafferOne = $this->DataControl->getFieldOne('smc_staffer','staffer_id,staffer_cnname',"staffer_mobile='{$xsArray['mobile']}' and company_id = '{$studentOne['company_id']}' ");
            if ($stafferOne) {
                $result['isstaff'] = 1;
            }else {
//                $bakinfo = request_by_curl("https://api.kidcastle.com.cn/Jdbpay/hrmworker", "mobile={$xsArray['mobile']}", "GET", array());
                $bakinfo = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi", "mobile={$xsArray['mobile']}", "GET", array());
                $apiArray = json_decode($bakinfo, '1');
                if($apiArray['error'] == '0') {
                    $result['isstaff'] = 1;
                }else{
                    $result['isstaff'] = 0;
                    $result['lgerrortip'] = '您好，您的信息未登记在系统内，无法使用微商城，请联系您的老师确认您的信息状态，谢谢。';
                }
            }

            $this->DataControl->updateData("smc_parenter", "parenter_id = '{$parenterOne['parenter_id']}'", array("parenter_lasttime" => time(), "parenter_lastip" => real_ip()));
        }

        return $result;
    }
}