<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scshop;

class CartModel extends modelTpl
{
    public $m;
    public $company_id;

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
    }

    //购物车列表
    function CartList($paramArray){
        $datawhere = "1";
        if(isset($paramArray['class_id']) && $paramArray['class_id'] != '' && isset($paramArray['class_type']) && $paramArray['class_type'] != ''){
            $datawhere .= "  AND g.class_id = '{$paramArray['class_id']}' AND g.sellgoods_type = '{$paramArray['class_type']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] != '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] != '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $CartList = $this->DataControl->selectClear("SELECT
                                                            c.cart_id,c.cart_nums,c.is_change,g.sellgoods_name,g.sellgoods_id,g.sellgoods_name,g.sellgoods_type,g.sellgoods_listimg,g.sellgoods_howtobuy,g.sellgoods_desc,g.sellgoods_unit,g.feeitem_price,g.tuition_id,g.course_id,g.coursepacks_id,g.goods_id,g.feeitem_price,g.class_id,g.products_id,
                                                            (SELECT cc.class_type FROM shop_code_class as cc WHERE cc.class_id = g.class_id) as class_type
                                                       FROM
                                                            shop_cart as c
                                                       LEFT JOIN
                                                            shop_sellgoods as g ON c.sellgoods_id = g.sellgoods_id
                                                       WHERE
                                                            {$datawhere} AND c.company_id = '{$this->company_id}' AND c.school_id = '{$paramArray['school_id']}' AND c.parenter_id = '{$paramArray['parenter_id']}' AND c.student_id = '{$paramArray['student_id']}'
                                                       ORDER BY c.cart_id DESC LIMIT {$pagestart},{$num}");
        if ($CartList) {
            foreach ($CartList as &$val) {
                $collection = $this->DataControl->getFieldOne("shop_parenter_collection","collection_id","company_id='{$this->company_id}' and parenter_id='{$paramArray['parenter_id']}' and student_id='{$paramArray['student_id']}' and sellgoods_id='{$val['sellgoods_id']}'");
                if($collection){
                    $val['collection_id'] = $collection['collection_id'];
                }
                if ($val['sellgoods_type'] == '0') {
                    $a = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_originalprice,tuition_sellingprice,tuition_buypiece", "tuition_id = '{$val['tuition_id']}'");
                    $b = $this->DataControl->getFieldOne("smc_course", "coursetype_id", "course_id = '{$val['course_id']}'");
                    $val['price'] = $a['tuition_sellingprice'];
                    $val['coursetype_id'] = $b['coursetype_id'];
                } elseif ($val['sellgoods_type'] == '1') {
                    $b = $this->DataControl->selectOne("select sum(tuition_sellingprice) as price from smc_fee_warehouse_courses where coursepacks_id = '{$val['coursepacks_id']}'");
                    $val['price'] = $b['price'];
                } elseif ($val['sellgoods_type'] == '2') {
                    $c = $this->DataControl->getFieldOne("erp_goods","goods_vipprice","goods_id = '{$val['goods_id']}'");
                    $val['price'] = $c['goods_vipprice'];
                } elseif ($val['sellgoods_type'] == '3') {
                    $val['price'] = $val['feeitem_price'];
                } elseif ($val['sellgoods_type'] == '4') {
                    $d = $this->DataControl->getFieldOne("smc_fee_pricing_products","products_sellingprice","products_id = '{$val['products_id']}'");
                    $val['price'] = $d['products_sellingprice'];
                }
            }
        } else {
            $CartList = array();
        }
        
        $db_nums = $this->DataControl->selectOne("SELECT
                                                        COUNT(c.cart_id) as num
                                                    FROM
                                                        shop_cart as c
                                                    LEFT JOIN
                                                        shop_sellgoods as g ON c.sellgoods_id = g.sellgoods_id
                                                    WHERE {$datawhere} AND c.company_id = '{$this->company_id}' AND c.school_id = '{$paramArray['school_id']}' AND c.parenter_id = '{$paramArray['parenter_id']}' AND c.student_id = '{$paramArray['student_id']}'");

        $data = array();
        $data['list'] = $CartList;
        $data['allnums'] = $db_nums['num'];

        return $data;
    }

    //添加购物车
    function AddCart($paramArray){
        $sellgoods_id  = json_decode(stripslashes($paramArray['list']),true);
        foreach($sellgoods_id as $v){
            $v['sellgoods_id'] = addslashes($v['sellgoods_id']);
            $v['sellgoods_cartchange'] = addslashes($v['sellgoods_cartchange']);
            $v['goods_num'] = addslashes($v['goods_num']);
            $cartOne = $this->DataControl->selectOne("SELECT cart_id,cart_nums,is_change FROM shop_cart
WHERE company_id = '{$this->company_id}' AND school_id = '{$paramArray['school_id']}'
AND parenter_id = '{$paramArray['parenter_id']}' AND student_id = '{$paramArray['student_id']}'
AND sellgoods_id = '{$v['sellgoods_id']}'");

            $data = array();
            if($cartOne){
                if($cartOne['is_change'] == '1'){
                    $data['is_change'] = $v['sellgoods_cartchange'];
                    $data['cart_nums'] = $cartOne['cart_nums'] + $v['goods_num'];
                    $cartPaly = $this->DataControl->updateData("shop_cart","cart_id = '{$cartOne['cart_id']}'",$data);
                }else{
                    $res = array('error' => 1, 'errortip' => "该商品加入购物车数量已达到限制，不允许继续添加!");
                    return $res;
                }
            }else{
                $data['company_id'] = $paramArray['company_id'];
                $data['school_id'] = $paramArray['school_id'];
                $data['parenter_id'] = $paramArray['parenter_id'];
                $data['student_id'] = $paramArray['student_id'];
                $data['sellgoods_id'] = $v['sellgoods_id'];
                $data['is_change'] = $v['sellgoods_cartchange'];
                $data['cart_nums'] = $v['goods_num'];
                $cartPaly = $this->DataControl->insertData("shop_cart", $data);
            }
        }

        if($cartPaly){
            $res = array('error' => 0, 'errortip' => "加入购物车成功!");
        }else{
            $res = array('error' => 1, 'errortip' => "加入购物车失败!");
        }

        return $res;
    }

    //购物车修改商品数量
    function EditCart($paramArray){
        $data = array();
        $data['cart_nums'] = $paramArray['goods_num'];
        if($this->DataControl->updateData("shop_cart","cart_id = '{$paramArray['cart_id']}'", $data)){
            $res = array('error' => 0,'errortip' => "购物车商品数量修改成功!");
        }else{
            $res = array('error' => 1,'errortip' => "购物车商品数量修改失败!");
        }

        return $res;
    }

    //移除购物车
    function RemoveCart($paramArray){
        $cartArray = json_decode(stripslashes($paramArray['list']),true);
        if(is_array($cartArray)){
            foreach($cartArray as $val){
                $val = addslashes($val);
                $dataid = $this->DataControl->delData("shop_cart","cart_id='{$val}'");
            }
        }

        if($dataid){
            $res = array('error' => 0, 'errortip' => '移除购物车成功');
        }else{
            $res = array('error' => 1, 'errortip' => '移除购物车失败');
        }

        return $res;
    }

    //收藏购物车商品
    function Collection($paramArray){
        $cart_id  = json_decode(stripslashes($paramArray['list']),true);

        foreach($cart_id as $val){
            $data = array();
            $data['company_id'] = $paramArray['company_id'];
            $data['parenter_id'] = $paramArray['parenter_id'];
            $data['student_id'] = $paramArray['student_id'];
            $data['sellgoods_id'] = $val;
            $data['collection_createtime'] = time();
            $dataid = $this->DataControl->insertData("shop_parenter_collection",$data);
            if($dataid){
                $this->DataControl->delData("shop_cart","cart_id='{$val}'");
            }
        }

        if($dataid){
            $res = array('error' => 0, 'errortip' => '收藏成功');
        }else{
            $res = array('error' => 1, 'errortip' => '商品已收藏，不可重复收藏');
        }

        return $res;
    }
}
