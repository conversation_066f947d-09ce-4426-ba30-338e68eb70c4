<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scshop;

class InvoiceModel extends modelTpl
{
    public $m;

    function __construct()
    {
        parent::__construct();
    }

    //选择开票课程列表
    function ChoiceInvoiceList($paramArray)
    {
        $datawhere = ' 1 ';
        if (isset($paramArray['protocol_id']) && $paramArray['protocol_id'] !== "") {
            $datawhere .= " and oc.protocol_id ='{$paramArray['protocol_id']}'";
        }
        $sql = " 
            SELECT
	oc.protocol_id,
	oc.protocol_nums,
	c.course_id,
	c.course_cnname,
	c.course_imglist,
	s.sellgoods_id,
	s.sellgoods_name,
	oc.protocol_price,
	s.sellgoods_listimg,
	st.student_cnname
FROM
	smc_student_protocol AS oc
	LEFT JOIN smc_course AS c ON oc.course_id = c.course_id
	LEFT JOIN smc_payfee_order_course AS pc ON oc.order_pid = pc.order_pid
	LEFT JOIN smc_fee_pricing_tuition AS t ON oc.course_id = t.course_id
	LEFT JOIN shop_sellgoods AS s ON s.tuition_id = t.tuition_id 
	left join smc_student as st on st.student_id = oc.student_id
WHERE
	{$datawhere} and oc.order_pid = '{$paramArray['order_pid']}' 
GROUP BY
	oc.protocol_id";
        $courseList = $this->DataControl->selectClear($sql);

        $res['list'] = $courseList;

        return $res;
    }

    //开票课程详情
    function InvoiceDetailList($paramArray)
    {
        $sql = " 
            SELECT
                c.course_id,
                oc.order_pid,
                oc.protocol_nums,
                c.course_cnname,
                c.course_imglist,
                oc.protocol_price,
                s.sellgoods_detailimg
            FROM
                smc_student_protocol AS oc
                LEFT JOIN smc_course AS c ON oc.course_id = c.course_id
                left join smc_payfee_order_course as pc on oc.order_pid = pc.order_pid
                left join smc_fee_pricing_tuition as t on t.pricing_id = pc.pricing_id
                left join shop_sellgoods as s on s.tuition_id = t.tuition_id
            WHERE
                oc.protocol_id = '{$paramArray['protocol_id']}'
                GROUP BY c.course_id";
        $courseDetail = $this->DataControl->selectOne($sql);
        $res['courseDetail'] = $courseDetail;

        return $res;
    }

    //我的发票-已开票
    function AlreadyInvoiceList($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$paramArray['student_branch']}'");
        $sql = " 
            SELECT
                i.invoice_id,
                i.invoice_title,
                i.invoice_allprice,
                i.order_pid,
                i.invoice_code,
                i.invoice_status,
                i.parenter_id,
                p.protocol_pid,
                t.parenter_cnname,
                s.staffer_cnname,
                o.ordercourse_buynums
            FROM
                shop_invoice AS i left join smc_student_protocol as p on p.order_pid = i.order_pid left join smc_parenter as t on i.parenter_id = t.parenter_id left join smc_staffer as s on s.staffer_id = i.staffer_id left join smc_payfee_order_course as o on o.order_pid = p.order_pid
            WHERE
                i.student_id = '{$paramArray['student_id']}' and i.invoice_status = '1' and i.invoice_cancel = '0'
            GROUP BY i.invoice_id
            ORDER BY i.invoice_createtime DESC
            LIMIT {$pagestart},{$num}";

        $invoiceList = $this->DataControl->selectClear($sql);

        if($invoiceList){
            foreach($invoiceList as &$val){
                if($val['parenter_id'] > 0){
                    $val['invoice_title'] = $val['parenter_cnname'];
                }else{
                    $val['invoice_title'] = $val['staffer_cnname'];
                }
            }
        }

        $all_num = count($invoiceList);

        if(!$invoiceList){
            $res['list'] = array();
            $res['allnum'] = 0;
        }else{
            $res['list'] = $invoiceList;
            $res['allnum'] = $all_num;
        }

        return $res;
    }

    //我的发票-未开票
    function UnInvoiceList($paramArray)
    {
//        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$paramArray['student_branch']}'");
        $order_pid = $this->DataControl->selectOne("
SELECT
	p.protocol_id,
	p.order_pid,
	c.course_imglist,
	c.course_cnname,
	p.protocol_nums,
	p.protocol_isinvoice,
    o.ordercourse_buynums,
    (select pay_id from smc_payfee_order_pay as op 
    where op.order_pid = p.order_pid 
    and op.paytype_code in ('dcep','qrcode','bankcard','cash','pos','alipay','wechat','yinlian') 
    AND op.pay_issuccess > 0
    limit 0,1) as a               
FROM smc_student_protocol AS p left join smc_course as c on p.course_id = c.course_id left join smc_payfee_order_course as o on o.order_pid = p.order_pid
WHERE p.student_id = '{$paramArray['student_id']}' and p.protocol_isinvoice = '0' and p.protocol_isaudit = '1' and p.protocol_isdel = '0' group by p.protocol_id having a > 0 order by protocol_id DESC");

        $sql = " 
           SELECT
	p.protocol_id,
	p.order_pid,
	c.course_imglist,
	c.course_cnname,
	p.protocol_nums,
    p.protocol_price,
	p.protocol_isinvoice,
    o.ordercourse_buynums,
    (select pay_id from smc_payfee_order_pay as op 
    where op.order_pid = p.order_pid 
    and op.paytype_code in ('dcep','qrcode','bankcard','cash','pos','alipay','wechat','yinlian') 
    AND op.pay_issuccess > 0
    limit 0,1) as a               
FROM smc_student_protocol AS p left join smc_course as c on p.course_id = c.course_id left join smc_payfee_order_course as o on o.order_pid = p.order_pid
WHERE p.student_id = '{$paramArray['student_id']}' and p.protocol_isinvoice = '0' and p.protocol_isaudit = '1' and p.protocol_isdel = '0' group by p.protocol_id having a > 0";
        $courseList = $this->DataControl->selectClear($sql);
        if($courseList){
            $index = 0;
            $price = 0;
            foreach($courseList AS &$courseOne){
                $index++;
                $count = count($courseList);
                if($courseOne['course_imglist'] ==''){
                    $courseOne['course_imglist'] = "https://pic.kedingdang.com/schoolmanage/201912152050x969940430.jpg";
                }

                $paytime = $this->DataControl->selectOne("select p.pay_successtime from smc_payfee_order_pay as p where p.order_pid = '{$courseOne['order_pid']}' and p.pay_issuccess = '1' order by p.pay_successtime DESC");
                $now = time();
                $date = $paytime['pay_successtime'] + (30*24*3600);
                if($now >= $date){
                    $courseOne['isdelay'] = '1';
                }else{
                    $courseOne['isdelay'] = '0';
                }

            }

        }





        $res['list'] = $courseList;

        return $res;
    }

    //我的发票-查看发票
    function InvoiceDetail($paramArray)
    {
        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$paramArray['invoice_id']}'");


        if($invoiceOne['invoice_pdfurl']){
            $result = $invoiceOne['invoice_pdfurl'];
        }else{
            $param = '{
    "invoiceSn": "' . $invoiceOne['invoice_serialnumber'] . '",
    "invoiceCode": "' . $invoiceOne['invoice_code'] . '",
    "invoiceNo": "' . $invoiceOne['invoice_number'] . '"
}';

            $getBackurl = request_by_curl("https://wxp.easyfapiao.com/v2/haizhouJiaotong/getInvoiceUrl", $param, "POST");
            $List = json_decode($getBackurl, true);

            $result = $List['body']['downloadUrl'];
        }

        $data = array();
        $data['invoice_pdfurl'] = $result;
        $this->DataControl->updateData("shop_invoice","invoice_id='{$paramArray['invoice_id']}'",$data);





        $sql = " 
            SELECT
                i.invoice_status,
                o.order_status,
                i.order_pid,
                FROM_UNIXTIME( o.order_createtime, '%Y-%m-%d %H:%i:%s' ) AS order_createtime,
                i.invoice_type,
                i.invoice_content,
                i.invoice_title,
                i.invoice_wxurl,
                i.invoice_pdfurl
            FROM
                shop_invoice AS i left join smc_payfee_order as o on o.order_pid = i.order_pid
            WHERE
                i.invoice_id = '{$paramArray['invoice_id']}'";
        $InvoiceDetail = $this->DataControl->selectOne($sql);
        $res['InvoiceDetail'] = $InvoiceDetail;

        return $res;
    }

    public function __call($method, $args)
    {

    }
}
