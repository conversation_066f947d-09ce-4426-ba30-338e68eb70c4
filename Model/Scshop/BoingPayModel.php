<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/27
 * Time: 17:28
 */

namespace Model\Scshop;


class BoingPayModel extends \Model\modelTpl{
    public $u;
    public $t;
    public $c;
    public $error = false;
    public $errortip = false;
    public $oktip = false;//专案提示
    public $companyOne = false;
    public $payfeeOrderOne = false;
    public $BoingPay;
    public $BoingPaypswd = "jdb2018";

    function __construct($company_id ='0') {
        parent::__construct ();
        if($company_id !== '0'){
            $this->verdictcompany($company_id);
        }

        $this->BoingPay = new \BoingPay();
        $this->BoingPayStart();
    }

    function verdictcompany($company_id){
        $this->companyOne = $this->DataControl->getFieldOne("gmc_company","company_id,company_cnname,company_boningcode","company_id = '{$company_id}'");
        if(!$this->companyOne){
            $this->error = true;
            $this->errortip = "员工信息不存在";
        }
    }

    function BoingPayStart(){
        $this->BoingPay->setAppId($this->companyOne['company_boningcode']);
        $this->BoingPay->setServerUrl("https://epay.aggreg.cmbchina.com/service/soa");
        //DSAPublicKeyPath ：DSA公钥文件所在目录
        //DSAPrivateKeyPath ：DSA私钥文件所在目录
        //DSAPrivateKeyPass ：DSA私钥密码
        $this->BoingPay->setDSAKey(ROOT_PATH . "Paycf/{$this->companyOne['company_id']}/cloud_dsa_public.pem", ROOT_PATH . "Paycf/{$this->companyOne['company_id']}/kidcastleDSAKey.pfx", $this->BoingPaypswd);
        //RSAPublicKeyPath ：RSA公钥文件所在目录
        //RSAPrivateKeyPath ：RSA私钥文件所在目录
        //RSAPrivateKeyPass ：RSA私钥密码
        $this->BoingPay->setRSAKey(ROOT_PATH . "Paycf/{$this->companyOne['company_id']}/cloud_rsa_public.pem", ROOT_PATH . "Paycf/{$this->companyOne['company_id']}/kidcastleRSAKey.pfx", $this->BoingPaypswd);
    }

    /**查询招行渠道订单支付状态
     **/
    function orderPayStatus($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderOne['companies_id']}'");

        $this->BoingPay->setApiVersion("2.0");

        $result = $this->BoingPay->getCashier()->getPayStatus($storeOne['companies_cmbappid'],'',$orderPay['pay_pid']);

        if ($result["result"]["pay_result"] == "succeed") {
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$orderOne['company_id']}' AND account_class = '1'");
            if ($orderPay && ($orderPay['pay_issuccess'] != 1 || ($orderPay['pay_issuccess'] == 1 && $orderPay['pay_price']==$orderOne['order_paymentprice'] && $orderOne['order_status']>0 && $orderOne['order_status']<4))) {
                $publiclist = array();
                $publiclist['company_id'] = $orderOne['company_id'];//门店编号
                $publiclist['school_id'] = $orderOne['school_id'];//门店编号
                $publiclist['staffer_id'] = $stafferOne['staffer_id'];//门店编号
                $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);
                $createtime = $result["result"]['create_time'];
                $paytype_code = $result['result']['pay_interface_no'] == '3004' ? 'wechat' : 'alipay';//支付通道3004微信
                $bakjson = json_encode($result["result"], JSON_UNESCAPED_UNICODE);
                $orderPayModel->orderPaylog($orderPay['pay_pid'], $result["result"]['pay_request_no'], ($result["result"]['pay_i_fee'] / 100), '', $paytype_code, $createtime, '0', $bakjson, '', 'cmb');
            }
        }
        return $result;
    }

    function orderPayStatusOne($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderOne['companies_id']}'");

        $this->BoingPay->setApiVersion("2.0");

        $result = $this->BoingPay->getCashier()->getPayStatus($storeOne['companies_cmbappid'],'',$orderPay['pay_pid']);
        return $result;
    }

    function OrderPay($pay_pid,$paymenttype,$openid='')
    {
        if($paymenttype == 'wxpay'){
            $PayResult = $this->WxPay($pay_pid,$openid);
            ajax_return($PayResult['result']);
        }elseif($paymenttype == 'alipay'){
            $PayResult = $this->AliPay($pay_pid,$openid);
            ajax_return($PayResult['result']);
        }elseif($paymenttype == 'ewmpay'){
            return $this->Ewmpay($pay_pid);
        }elseif($paymenttype == 'bsaocpay'){
           return $this->Bsaocpay($pay_pid,$openid);
        }elseif($paymenttype == 'TestEwmpay'){
            return $this->TestEwmpay($pay_pid);
        }
    }

    /**B扫C触发支付
     *$openid 是扫码枪获得用户的支付编号
     **/
    function Bsaocpay($pay_pid,$openid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderOne['companies_id']}'");

        if(!$storeOne || $storeOne['companies_cmbappid'] == '' || $storeOne['school_cmbshopcode'] == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        if(!isset($openid) || trim($openid) == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '请传入用户收款码编号！';
            return $bakresult;
        }

        if($orderPay['pay_issuccess'] == '0') {
            $this->BoingPay->setApiVersion("2.0");
            //应用ID               必传
            $sysParams["mch_app_id"] = $storeOne['companies_cmbappid'];
            //收款码          必传
            $sysParams["bar_code"] = $openid;
            //商户订单号      必传
            $sysParams["out_order_no"] = $orderPay['pay_pid'];
            //订单名称        必传
            $sysParams["order_name"] = "{$this->companyOne['company_cnname']}微商城支付订单";
            //支付金额        必传
            $sysParams["pay_amount"] = $orderPay['pay_price']*100;
            //外部终端编号    可选
            $sysParams["out_terminal_id"] = "";
            //终端编号        可选
            $sysParams["terminal_id"] = "";
            //终端ip          必传
            $sysParams["terminal_ip"] = real_ip();
            //外部门店编号    可选
            $sysParams["out_store_id"] = "";
            //门店编号       可选
            $sysParams["store_id"] = $storeOne['school_cmbshopcode'];
            //商品明细      可选
            $sysParams["goods_detail"] = "";
            //不参与优惠金额  可选
            $sysParams["undiscountable_amount"] = "0";
            //订单描述   可选
            $sysParams["order_desc"] = "order_desc";
            //操作员编号  可选
            $sysParams["operator_id"] = "";
//            print_r($sysParams);
            $result = $this->BoingPay->getCashier()->barcodeScanPay($sysParams);
//            var_dump($result);
            if ($result["status"] == "OK") {
                if ($result["result"]["pay_result"] == "succeed") {
                    if ($result["result"]["pay_result"] == "succeed") {
                        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$orderOne['company_id']}' AND account_class = '1'");
                        if ($orderPay && $orderPay['pay_issuccess'] !=1) {
                            $publiclist = array();
                            $publiclist['company_id'] = $orderOne['company_id'];//门店编号
                            $publiclist['school_id'] = $orderOne['school_id'];//门店编号
                            $publiclist['staffer_id'] = $stafferOne['staffer_id'];//门店编号
                            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);
                            $createtime = $result["result"]['create_time'];
                            $paytype_code = $result['result']['pay_interface_no'] == '3004' ? 'wechat' : 'alipay';//支付通道3004微信
                            $bakjson = json_encode($result["result"], JSON_UNESCAPED_UNICODE);
                            $orderPayModel->orderPaylog($orderPay['pay_pid'], $result["result"]['pay_request_no'], ($result["result"]['pay_i_fee'] / 100), '', $paytype_code, $createtime, '0', $bakjson, '', 'cmb');
                        }
                    }
                    //支付成功
                } elseif ($result["result"]["pay_result"] == "running") {
//                    echo '<br>支付中，请调用查询接口确认结果';
                } elseif ($result["result"]["pay_result"] == "failed") {
//                    echo '<br>支付失败';
                    //支付失败
                }
            } else if ($result["status"] == "error") {
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '支付信息获取失败！';
                $bakresult['result'] = $result;
                return $bakresult;
            }
            $bakresult = array();
            $bakresult['error'] = '0';
            $bakresult['errortip'] = '支付信息获取成功！';
            $bakresult['result'] = $result;
            return $bakresult;
        }else{
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '订单已支付请勿重复提交！';
            return $bakresult;
        }
    }

    function AliPay($pay_pid,$openid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderOne['companies_id']}'");

        if(!$storeOne || $storeOne['companies_cmbappid'] == '' || $storeOne['school_cmbshopcode'] == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
        if($orderPay['pay_issuccess'] == '0') {
            $this->BoingPay->setApiVersion("2.0");
            //应用编号    必选
            $sysParams["mch_app_id"] = $storeOne['companies_cmbappid'];
            //支付通道编号  必选
            $sysParams["pay_interface_no"] = "3003";
            //商户订单号  必选
            $sysParams["out_order_no"] = $orderPay['pay_pid'];
            //订单名称   必选
            $sysParams["order_name"] = "{$this->companyOne['company_cnname']}微商城支付订单";
            //支付金额    必选
            $sysParams["pay_amount"] = $orderPay['pay_price']*100;
            //门店编号      可选
            $sysParams["store_id"] = $storeOne['school_cmbshopcode'];
            //支付渠道用户ID    可选（JSAPI必传）
//            $sysParams["buyer_user_id"] = $memberOne['parenter_wxtoken'];
//            //支付渠道用户ID    可选（JSAPI必传）
//            $sysParams["mch_wx_app_id"] = "wx7378534679eeecdf";
            //后台通知地址  可选
            $sysParams["back_url"] = "https://scshopapi.kedingdang.com/BoingPay/OrderBak";
            //订单开始时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["start_time"] = date("Y-m-d H:i:s");
            //订单过期时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["expire"] = date("Y-m-d H:i:s", time() + 3600 * 24 * 7);
            //订单描述  可选
            $sysParams["order_desc"] = "order_desc";
            //支付场景  必选
            $sysParams["pay_context"] = "JSAPI";
            //用户IP  必选
            $sysParams["user_ip"] = real_ip();

            $service = 'cod.pay.order.native_pay';
            $result = $this->BoingPay->getService()->call($service, $sysParams);

            $bakresult = array();
            $bakresult['error'] = '0';
            $bakresult['errortip'] = '支付信息获取成功！';
            $bakresult['result'] = $result;
            return $bakresult;
        }else{
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '订单已支付请勿重复提交！';
            return $bakresult;
        }
    }

    function WxPay($pay_pid,$openid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderOne['companies_id']}'");

        if(!$storeOne || $storeOne['companies_cmbappid'] == '' || $storeOne['school_cmbshopcode'] == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }


        if($openid == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未获取微信授权，请选择二维码支付！';
            $result = array();
            $result['errortip'] = '未获取微信授权，请选择二维码支付！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        if($orderPay['pay_issuccess'] == '0') {
            $this->BoingPay->setApiVersion("2.0");
            //应用编号    必选
            $sysParams["mch_app_id"] = $storeOne['companies_cmbappid'];
            //支付通道编号  必选
            $sysParams["pay_interface_no"] = "3004";
            //商户订单号  必选
            $sysParams["out_order_no"] = $orderPay['pay_pid'];
            //订单名称   必选
            $sysParams["order_name"] = "{$this->companyOne['company_cnname']}微商城支付订单";
            //支付金额    必选
            $sysParams["pay_amount"] = $orderPay['pay_price']*100;
            //门店编号      可选
            $sysParams["store_id"] = $storeOne['school_cmbshopcode'];
            //支付渠道用户ID    可选（JSAPI必传）
            $sysParams["buyer_user_id"] = $openid;
            //支付渠道用户ID    可选（JSAPI必传）
            $sysParams["mch_wx_app_id"] = "wx7378534679eeecdf";
            //后台通知地址  可选
            $sysParams["back_url"] = "https://scshopapi.kedingdang.com/BoingPay/OrderBak";
            //订单开始时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["start_time"] = date("Y-m-d H:i:s");
            //订单过期时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["expire"] = date("Y-m-d H:i:s", time() + 3600 * 24 * 7);
            //订单描述  可选
            $sysParams["order_desc"] = "order_desc";
            //支付场景  必选
            $sysParams["pay_context"] = "JSAPI";
            //用户IP  必选
            $sysParams["user_ip"] = real_ip();

            $service = 'cod.pay.order.native_pay';
            $result = $this->BoingPay->getService()->call($service, $sysParams);

            $bakresult = array();
            $bakresult['error'] = '0';
            $bakresult['errortip'] = '支付信息获取成功！';
            $bakresult['result'] = $result;
            return $bakresult;
        }else{
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '订单已支付请勿重复提交！';
            $result = array();
            $result['errortip'] = '订单已支付请勿重复提交！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
    }

    function Ewmpay($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderPay['companies_id']}'");

        if(!$storeOne || $storeOne['companies_cmbappid'] == '' || $storeOne['school_cmbshopcode'] == ''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        if($orderPay['pay_order_no'] != '' && $orderPay['pay_outpidjson'] != ''){
            header('Content-Type:image/png');
            $outpidarray = json_decode($orderPay['pay_outpidjson'], true);
            $codeUrl = $outpidarray['qr_code'];

            require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
            $QRcode = new \QRcode();
            $errorCorrectionLevel = 'H';//容错级别
            $matrixPointSize = 8;//生成图片大小
            echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            exit;
        }

        if($orderPay['pay_issuccess'] == '0'){
            $this->BoingPay->setApiVersion("2.0");
            //应用编号    必选
            $sysParams["mch_app_id"] = $storeOne['companies_cmbappid'];
            //商户订单号  必选
            $sysParams["out_order_no"] = $orderPay['pay_pid'];
            //订单名称   必选
            $sysParams["order_name"] = "{$this->companyOne['company_cnname']}微商城支付订单";
            //支付金额    必选
            $sysParams["pay_amount"] = $orderPay['pay_price']*100;
            //终端编号      可选
            //$sysParams["terminal_id"] = "";
            //商户微信公众号ID  可选
            //$sysParams["terminal_no"] = "";
            //门店编号      可选
            $sysParams["store_id"] = $storeOne['school_cmbshopcode'];
            //商品明细      可选
            //$sysParams["goods_detail"] = "吉的堡WK1学费明细";
            //后台通知地址  必选
            $sysParams["back_url"] = "https://scshopapi.kedingdang.com/BoingPay/OrderBak";
            //订单开始时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["start_time"] = date("Y-m-d H:i:s");
            //订单过期时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["expire"] = date("Y-m-d H:i:s",time()+3600*24*7);
            //订单描述  可选
            $sysParams["order_desc"] = "{$this->companyOne['company_cnname']}微商城商品支付,总计需支付{$orderPay['pay_price']}元";
            //操作员编号  可选
            //$sysParams["operator_id"] = "";
            //限制支付方式 可选   微信：上传此参数no_credit--可限制用户不能使用信用卡支付
            //$sysParams["limit_pay"] = "";
            //备注  可选
            //$sysParams["remark"] = "";

            $service = 'cod.pay.order.request';
            $result = $this->BoingPay->getService()->call($service, $sysParams);
            if($result['status'] == 'OK'){
                header('Content-Type:image/png');

                $updata = array();
                $updata['pay_order_no'] = $result['result']['order_no'];
                $updata['pay_outpidjson'] = json_encode($result['result']);
                $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$pay_pid}'", $updata);

                $codeUrl = $result['result']['qr_code'];
                require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
                $QRcode = new \QRcode();
                $errorCorrectionLevel = 'H';//容错级别
                $matrixPointSize = 8;//生成图片大小
                echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            }else{
                $this->error = true;
                print_r($service);
                print_r($sysParams);
                print_r($storeOne);
                print_r($result);
                echo "无法生成支付二维码";
            }
        }
    }

    function BoingPayBak($REQUESTARRAY){
        $result = $this->BoingPay->getCashier()->getNotifyResult($REQUESTARRAY);
        $eventJson = $result['event'];
        $eventArray = json_decode($eventJson,1);

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid,pay_issuccess","pay_pid='{$eventArray['out_order_no']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,school_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderOne['company_id']}' AND account_class = '1'");
        $eventJson = $result['event'];
        $eventArray = json_decode($eventJson,1);

        if($orderPay && $orderPay['pay_issuccess'] !== '1'){
            $publiclist = array();
            $publiclist['company_id'] = $orderOne['company_id'];//门店编号
            $publiclist['school_id'] = $orderOne['school_id'];//门店编号
            $publiclist['staffer_id'] = $stafferOne['staffer_id'];//门店编号
            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);

            $createtime = $eventArray['pay_time'];
            $paytype_code = $eventArray['pay_interface_no']=='3004'?'wechat':'alipay';//支付通道3004微信
            $bakjson = $eventJson;
            $orderPayModel->orderPaylog( $orderPay['pay_pid'],$eventArray['pay_request_no'],($eventArray['pay_i_fee']/100),$eventArray['pay_note'],$paytype_code,$createtime,'0',$bakjson,'','cmb');
        }
//        exit(1);
//
//        if($eventArray['event_type'] == 'pay_complete'){
//
//        }else{
//            exit(0);
//        }
    }


    function MohismPayBak($payArray){
        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid","pay_pid='{$payArray['pid']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,school_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderOne['company_id']}' AND account_class = '1'");
        if($orderPay && $orderPay['pay_issuccess'] !== '1'){
            $publiclist = array();
            $publiclist['company_id'] = $orderOne['company_id'];//门店编号
            $publiclist['school_id'] = $orderOne['school_id'];//门店编号
            $publiclist['staffer_id'] = $stafferOne['staffer_id'];//门店编号
            print_r($publiclist);
            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);

            $createtime = date("Y-m-d H:i:s");
            $paytype_code = '墨智支付';//支付通道3004微信
            $payno = 'MZPAY'.rand(111111,999999);//支付通道3004微信
            $pay_note = '墨智专用测试支付反馈记录';
            $bakjson = '{"u":"BoingPay","t":"OrderBak","sign":"MC0CFQCFOsoM9LkmsBjwts++QRxZkdFgeAIUZ6zcpHDELIIpHZqQyKaafp0047s=","event":"{"order_no":"844328533118455808","store_id":"1074551952936837120","pay_scene":"4","pay_interface_no":"3004","bank_type":"OTHERS","pay_amount":1,"out_order_no":"ZFBNK620042811570060043","actual_pay_amount":1,"buyer_user_id":"oiGYnwcZRDEUAno7101fq_JgTxWA","mch_app_id":"1074551952127336448","pay_time":"2020-04-29 13:36:44","channel_trade_no":"4200000522202004292645642007","pay_request_no":"1255370389236781056","event_type":"pay_complete","pay_product_no":"300402","receipt_amount":1,"pay_i_fee":0,"terminal_id":"0"}","app_id":"1045495093844287488","version":"1.0","timestamp":"*************"}';
            $bools = $orderPayModel->orderPaylog( $orderPay['pay_pid'],$payno,0.01,$pay_note,$paytype_code,$createtime,'0',$bakjson,'','mohism');
            var_dump($bools);
        }else{
            echo '已支付';
        }
    }

    //退款入口  --   API修改
    function RefundView($pay_pid,$refund_pid){
        $payOrder = $this->DataControl->selectOne("SELECT p.pay_pid,p.pay_price,l.paylog_bakjson
FROM smc_payfee_order as o,smc_payfee_order_pay AS p,smc_payfee_order_paylog AS l
WHERE o.order_pid = p.order_pid AND p.pay_pid = l.pay_pid AND p.pay_pid = '{$pay_pid}' limit 0,1");

        $bakjson = str_replace('"[',"[",$payOrder['paylog_bakjson']);
        $bakjson = str_replace(']"',"]",$bakjson);
        $payhsOrder = json_decode($bakjson,1);
        //应用ID               必传
        $sysParams["mch_app_id"] = $payhsOrder['mch_app_id'];  //$companiesOne['companies_appid'];
        //订单号             必传
        $sysParams["order_no"] = $payhsOrder['order_no']; //$request['pid']
        //商户退款请求号     必传
        $sysParams["out_refund_order_no"] = $refund_pid;
        //退款金额           必传
        $sysParams["refund_amount"] = $payOrder['pay_price'] * 100;
        //退款备注           可传
        $sysParams["refund_remark"] = "吉的堡API退款，手工唤起API退款流程！";
        //门店ID             可传
        $sysParams["store_id"] =  $payhsOrder['store_id'];
        $result = $this->BoingPay->getCashier()->refund($sysParams);
        if ($result["status"] == "OK") {
            if ($result["result"]["refund_result"] == "succeed") {
                return array('status'=>1,'message'=>'退款成功');  //退款成功
            } elseif ($result["result"]["refund_result"] == "running") {
                $appId = $payhsOrder['mch_app_id']; //商户应用ID         必传
                $orderNo = $payhsOrder['order_no']; //订单号         必传
                $refundOrderNo = $refund_pid;//退款请求号     必传
                $res = $this->BoingPay->getCashier()->getRefundStatus($appId, $orderNo, $refundOrderNo);
                if($res['result']['refund_result'] == "succeed") {
                    return array('status'=>1,'message'=>'退款成功');
                } elseif($res['result']['refund_result'] == "running") {
                    return array('status'=>1,'message'=>'退款进行中');
                } elseif($res['result']['refund_result'] == "failed") {
                    return array('status'=>2,'message'=>'退款失败');
                }
            } elseif ($result["result"]["refund_result"] == "failed") {
                return array('status'=>2,'message'=>'退款失败');
            }
        } elseif ($result["status"] == "error") {
            return array('status'=>2,'message'=>$result['message']);
        }else{
            return array('status'=>3,'message'=>"接口退款异常,无法正常退款!");
        }
    }

    //退款入口  --   未曾修改
    function RefundQueryView($pay_pid,$refund_pid){
        $payOrder = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_cmbappid","companies_id='{$payOrder['companies_id']}'");
        $paylogOrder = $this->DataControl->getOne("smc_payfee_order_paylog","pay_pid='{$pay_pid}'");
        $bakjson = str_replace('"[',"[",$paylogOrder['paylog_bakjson']);
        $bakjson = str_replace(']"',"]",$bakjson);
        $payhsOrder = json_decode($bakjson,1);
        $result = $this->BoingPay->getCashier()->getRefundStatus($companiesOne['companies_cmbappid'],$payhsOrder['order_no'],$refund_pid);
        return $result;
    }


    //取消或者失效的订单 对招行进行撤销操作
    function OrderPayCancel($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderPay['companies_id']}'");

        if(!$storeOne || $storeOne['companies_cmbappid'] == '' || $storeOne['school_cmbshopcode'] == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        if($orderPay['pay_issuccess'] == '-1' && $orderPay['pay_order_no'] != ''){
            $this->BoingPay->setApiVersion("2.0");

            //应用编号    必选
            $sysParamsOne["mch_app_id"] = $storeOne['companies_cmbappid'];
            //好收订单号  必选
            $sysParamsOne["order_no"] = $orderPay['pay_order_no'];
            $service = 'cod.pay.order.cancel';
            $result = $this->BoingPay->getService()->call($service, $sysParamsOne);
            if($result['status'] == 'OK'){
                $bakresult = array();
                $bakresult['error'] = '0';
                $bakresult['errortip'] = '对应订单撤销成功！';
                $bakresult['result'] = $result;
                return $bakresult;
            }else{
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '对应订单撤销失败！';
                $result = array();
                $result['errortip'] = '对应订单撤销失败！';
                $bakresult['result'] = $result;
                return $bakresult;
            }
        }
    }

    function TestEwmpay($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");
        $storeOne = $this->DataControl->selectOne("SELECT c.companies_cmbappid, s.companies_storenumber AS school_cmbshopcode
FROM gmc_code_companies AS c, smc_school_companies AS s WHERE s.companies_id = c.companies_id AND s.school_id = '{$orderOne['school_id']}'
AND c.companies_id = '{$orderPay['companies_id']}'");

        if(!$storeOne || $storeOne['companies_cmbappid'] == '' || $storeOne['school_cmbshopcode'] == ''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付APPID或适配肢体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        if($orderPay['pay_issuccess'] == '0'){
            $this->BoingPay->setApiVersion("2.0");
            //应用编号    必选
            $sysParams["mch_app_id"] = $storeOne['companies_cmbappid'];
            //商户订单号  必选
            $sysParams["out_order_no"] = $orderPay['pay_pid'];
            //订单名称   必选
            $sysParams["order_name"] = "{$this->companyOne['company_cnname']}微商城支付订单";
            //支付金额    必选
            $sysParams["pay_amount"] = $orderPay['pay_price']*100;
            //终端编号      可选
            //$sysParams["terminal_id"] = "";
            //商户微信公众号ID  可选
            //$sysParams["terminal_no"] = "";
            //门店编号      可选
            $sysParams["store_id"] = $storeOne['school_cmbshopcode'];
            //商品明细      可选
            //$sysParams["goods_detail"] = "吉的堡WK1学费明细";
            //后台通知地址  必选
            $sysParams["back_url"] = "https://scshopapi.kedingdang.com/BoingPay/OrderBak";
            //订单开始时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["start_time"] = date("Y-m-d H:i:s");
            //订单过期时间  yyyy-MM-dd HH:mm:ss，默认24小时
            $sysParams["expire"] = date("Y-m-d H:i:s",time()+3600*24*7);
            //订单描述  可选
            $sysParams["order_desc"] = "{$this->companyOne['company_cnname']}微商城商品支付,总计需支付{$orderPay['pay_price']}元";
            //操作员编号  可选
            //$sysParams["operator_id"] = "";
            //限制支付方式 可选   微信：上传此参数no_credit--可限制用户不能使用信用卡支付
            //$sysParams["limit_pay"] = "";
            //备注  可选
            //$sysParams["remark"] = "";

            $service = 'cod.pay.order.request';
            $result = $this->BoingPay->getService()->call($service, $sysParams);
            if($result['status'] == 'OK'){
                print_r($service);
                print_r($sysParams);
                print_r($storeOne);
                print_r($result);
                echo "正确的报文";
                exit;
            }else{
                $this->error = true;
                print_r($service);
                print_r($sysParams);
                print_r($storeOne);
                print_r($result);
                echo "无法生成支付二维码";
            }
        }
    }

}