<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scshop;

class IntegralModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $parenterOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['parenter_id'])) {
            if (!$this->verdictStaffer($publicarray['parenter_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($parenter_id)
    {

        $this->parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id,parenter_cnname,parenter_mobile", "parenter_id = '{$parenter_id}'");

        if (!$this->parenterOne) {
            $this->error = true;
            $this->errortip = "家长信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //获取孩子可用商品优惠券
    function getIntegralGoods($paramArray)
    {

        $datawhere = " 1 ";
        $datahave = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_enname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['goods_ishot']) && $paramArray['goods_ishot'] != '' && $paramArray['goods_ishot'] == 1) {
            $datawhere .= " AND g.goods_ishot = '1' ";
        }

        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] != '') {
            $datawhere .= " AND g.prodtype_code = '{$paramArray['prodtype_code']}' ";
        }

        if (isset($paramArray['integralsection_id']) && $paramArray['integralsection_id'] != '') {
            $sectionOne = $this->DataControl->getFieldOne("smc_code_integralsection", "integralsection_begin,integralsection_end", "integralsection_id='{$paramArray['integralsection_id']}'");

            if ($sectionOne['integralsection_begin']) {
                $datawhere .= " AND g.goods_integral >= '{$sectionOne['integralsection_begin']}'";
            }

            if ($sectionOne['integralsection_end']) {
                $datawhere .= " AND g.goods_integral <= '{$sectionOne['integralsection_end']}'";
            }
        }

        $stuIntegralOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$paramArray['student_id']}'");

        $stuIntegralOne['property_integralbalance'] = $stuIntegralOne['property_integralbalance'] ? $stuIntegralOne['property_integralbalance'] : 0;

        if (isset($paramArray['can_exchange']) && $paramArray['can_exchange'] == 1) {
            $datawhere .= " AND g.goods_integral <= '{$stuIntegralOne['property_integralbalance']}'";
        }

        if (isset($paramArray['have_stock'])) {
            if ($paramArray['have_stock'] == 1) {
                $datahave .= " AND (goods_repertory-integralgoods_number)>0";
            } else {
                $datahave .= " AND (goods_repertory-integralgoods_number)<=0";
            }

        }

        if (isset($paramArray['have_integral']) && $paramArray['have_integral'] == 1) {
            $datawhere .= " AND g.goods_integral <= {$stuIntegralOne['property_integralbalance']}";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_id,
                g.goods_cnname,
                g.goods_img,
                g.goods_integral
                ,(select eg.goods_repertory from smc_erp_goods_repertory as eg where eg.school_id = '{$paramArray['school_id']}' and eg.goods_id = g.goods_id limit 0,1) as goods_repertory
                ,(select sum(si.integralgoods_number) from smc_student_integralgoods as si where si.school_id = '{$paramArray['school_id']}' and si.goods_id = g.goods_id and si.integralgoods_isreceive > '-1') as integralgoods_number
            FROM
                erp_goods AS g
            WHERE {$datawhere}
                and g.goods_isintegral = '1'
                AND g.goods_integralsale = '1'
                AND g.goods_issale = '1'
                AND g.goods_shopgoodssale = '1'
                and g.company_id = '{$paramArray['company_id']}'
            having {$datahave}
            order by g.goods_id asc
            limit {$pagestart},{$num}";
        $goodsList = $this->DataControl->selectClear($sql);

        if (!$goodsList) {
            $this->error = true;
            if (isset($paramArray['goods_ishot']) && $paramArray['goods_ishot'] != '' && $paramArray['goods_ishot'] == 1) {
                $this->errortip = "暂无热门商品哦，去看看其他商品吧";
            } else {
                $this->errortip = "暂无可兑换的商品哦~";
            }

            return false;
        }

        foreach ($goodsList as &$val) {
            if ($val['goods_integral'] > $stuIntegralOne['property_integralbalance']) {
                $val['status'] = '0';
            } else {
                $val['status'] = '1';
            }

            $val['stock'] = ($val['goods_repertory'] - $val['integralgoods_number']) > 0 ? 1 : 0;
        }

        $count_sql = "SELECT
                g.goods_id,(select eg.goods_repertory from smc_erp_goods_repertory as eg where eg.school_id = '{$paramArray['school_id']}' and eg.goods_id = g.goods_id limit 0,1) as goods_repertory
                ,(select sum(si.integralgoods_number) from smc_student_integralgoods as si where si.school_id = '{$paramArray['school_id']}' and si.goods_id = g.goods_id and si.integralgoods_isreceive > '-1') as integralgoods_number
            FROM
                erp_goods AS g
            WHERE {$datawhere}
                and g.goods_isintegral = '1'
                AND g.goods_integralsale = '1'
                AND g.goods_issale = '1'
                and g.company_id = '{$paramArray['company_id']}'
                having {$datahave}";

        $db_nums = $this->DataControl->selectClear($count_sql);

        $data = array();
        $data['list'] = $goodsList;
        $data['allnum'] = $db_nums ? count($db_nums) : 0;

        return $data;
    }

    function getGoodsDetail($paramArray)
    {

        $sql = "select g.goods_id,g.goods_cnname,g.goods_integral,g.goods_img,ifnull(g.goods_chartimg,'[]') as goods_chartimg,g.goods_isimg,g.goods_introduce,g.prodtype_code,ifnull(r.goods_repertory, '0') as stock
              from erp_goods as g left join smc_erp_goods_repertory as r on g.goods_id = r.goods_id and school_id = '{$paramArray['school_id']}'
              where g.goods_id='{$paramArray['goods_id']}' and g.company_id='{$this->company_id}' and g.goods_issale = '1' and g.goods_isintegral = '1' and g.goods_integralsale='1'";
        $goodsOne = $this->DataControl->selectOne($sql);

        if ($goodsOne) {
            $num = $this->DataControl->selectOne("select sum(integralgoods_number) as integralgoods_number from smc_student_integralgoods where school_id = '{$paramArray['school_id']}' and goods_id = '{$paramArray['goods_id']}' and integralgoods_isreceive > '-1'");
            $goodsOne['stock'] = $goodsOne['stock'] - $num['integralgoods_number'];
            if ($goodsOne['stock'] < 0) {
                $goodsOne['stock'] = 0;
            }
        }

        if (!$goodsOne) {
            $this->error = true;
            $this->errortip = "无对应积分商品";
            return false;
        }

        return $goodsOne;
    }

    //获取积分类型
    function integraltype($paramArray)
    {

        $sql = "SELECT
                ci.integraltype_id,
                ci.integraltype_name
            FROM
                smc_student_integrallog AS i
            left join smc_code_integraltype as ci on ci.integraltype_id=i.integraltype_id
            WHERE
                i.student_id = '{$paramArray['student_id']}' and i.company_id='{$this->company_id}'
            group by ci.integraltype_id";

//        $sql = "
//            SELECT
//                i.integraltype_id,
//                i.integraltype_name
//            FROM
//                smc_code_integraltype AS i
//            WHERE
//                (i.company_id = '{$this->company_id}' or i.company_id=0)";
        $typeList = $this->DataControl->selectClear($sql);
        if (!$typeList) {
            $this->error = true;
            $this->errortip = "无对应积分类型";
            return false;
        }

        $typeList = array();

        return $typeList;
    }

    //学员积分明细
    function StuIntegralDetail($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['integraltype_id']) && $paramArray['integraltype_id'] !== "") {
            $datawhere .= " and i.integraltype_id ='{$paramArray['integraltype_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "
            SELECT
                i.integrallog_id,
                i.integrallog_playname,
                i.integrallog_playclass,
                i.integrallog_playamount,
                i.integrallog_rule as integraltype_name,   
                FROM_UNIXTIME(i.integrallog_time, '%Y-%m-%d %H:%i') as integrallog_time
            FROM
                smc_student_integrallog AS i
            WHERE
                {$datawhere} and i.student_id = '{$paramArray['student_id']}' and i.company_id='{$this->company_id}'
            order by i.integrallog_time desc
            limit {$pagestart},{$num}";
        $typeList = $this->DataControl->selectClear($sql);

        if (!$typeList) {
            $this->error = true;
            $this->errortip = "暂无积分使用明细哦~";
            return false;
        }

        $count_sql = "SELECT
                i.integrallog_id
            FROM
                smc_student_integrallog AS i
            WHERE
                {$datawhere} and i.student_id = '{$paramArray['student_id']}' and i.company_id='{$this->company_id}'";

        $db_nums = $this->DataControl->selectClear($count_sql);

        $data = array();
        $data['list'] = $typeList;
        $data['allnum'] = $db_nums ? count($db_nums) : 0;

        return $data;
    }

    //学员积分明细-奇趣星球
    function integralDetail($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and ctime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and ctime <= '{$paramArray['end_time']}'";
        }

        $datawheres = " 1 ";

        if (isset($paramArray['type']) && $paramArray['type'] == '1') {
            $datawheres .= " and i.integrallog_playclass = '+'";
        }
        if (isset($paramArray['type']) && $paramArray['type'] == '2') {
            $datawheres .= " and i.integrallog_playclass = '-'";
        }
        if (isset($paramArray['type']) && $paramArray['type'] == "3") {
            $datawheres .= " and i.integrallog_playname = '积分提现'";
        }

        if($paramArray['isapp'] == '1'){
            $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$paramArray['student_branch']}'");
            $paramArray['student_id'] = $sid['student_id'];
        }

        $sql = "
            SELECT
                i.integrallog_id,
                i.integrallog_playname,
                i.integrallog_playclass,
                i.integrallog_playamount,
                i.integrallog_rule as integraltype_name,   
                FROM_UNIXTIME(i.integrallog_time, '%Y-%m-%d %H:%i') as integrallog_time,
                FROM_UNIXTIME(i.integrallog_time, '%Y-%m-%d') as ctime
            FROM
                smc_student_integrallog AS i
            WHERE
                {$datawheres} and i.student_id = '{$paramArray['student_id']}' and i.company_id='{$paramArray['company_id']}'
            HAVING {$datawhere}
            order by i.integrallog_time desc";
        $typeList = $this->DataControl->selectClear($sql);

        if($typeList){
            foreach($typeList as &$val){
                if($val['integrallog_playclass'] == '+'){
                    $val['type'] = '1';
                }
                if($val['integrallog_playclass'] == '-'){
                    $val['type'] = '2';
                }
                if($val['integrallog_playname'] == '积分提现'){
                    $val['type'] = '3';
                }
            }
        }

        if (!$typeList) {
            $this->error = true;
            $this->errortip = "暂无积分使用明细哦~";
            return false;
        }

        $count_sql = "SELECT
                i.integrallog_id,
                 FROM_UNIXTIME(i.integrallog_time, '%Y-%m-%d') as ctime
            FROM
                smc_student_integrallog AS i
            WHERE
                i.student_id = '{$paramArray['student_id']}' and i.company_id='{$paramArray['company_id']}'
                HAVING {$datawhere}";

        $db_nums = $this->DataControl->selectClear($count_sql);

        $data = array();
        $data['list'] = $typeList;
        $data['allnum'] = $db_nums ? count($db_nums) : 0;

        return $data;
    }

    //兑换记录
    function StuIntegralExchange($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                i.integralgoods_id,
                i.integralgoods_score,
                i.integralgoods_number,
                FROM_UNIXTIME(i.integralgoods_createtime, '%Y-%m-%d %H:%i') as integralgoods_createtime,
                s.school_cnname,
                s.school_address,
                g.goods_cnname,
                g.goods_img,
                i.integralgoods_isreceive
            FROM
                smc_student_integralgoods AS i
                LEFT JOIN smc_school as s on s.school_id = i.school_id
                LEFT JOIN erp_goods as g on g.goods_id = i.goods_id
            WHERE
                i.student_id = '{$paramArray['student_id']}'
            order by i.integralgoods_createtime desc
            limit {$pagestart},{$num}";;
        $logList = $this->DataControl->selectClear($sql);

        if (!$logList) {
            $this->error = true;
            $this->errortip = "暂无积分兑换记录哦~";
            return false;
        }
        $count_sql = "SELECT
                i.integralgoods_id
            FROM
                smc_student_integralgoods AS i
                LEFT JOIN smc_school as s on s.school_id = i.school_id
                LEFT JOIN erp_goods as g on g.goods_id = i.goods_id
            WHERE
                i.student_id = '{$paramArray['student_id']}'";

        $db_nums = $this->DataControl->selectClear($count_sql);

        $data = array();
        $data['list'] = $logList;
        $data['allnum'] = $db_nums ? count($db_nums) : 0;

        return $data;
    }


    function getProdtype($paramArray)
    {

        $sql = "select p.prodtype_code,p.prodtype_name
              from erp_goods as g
              inner join smc_code_prodtype as p on g.prodtype_code=p.prodtype_code and p.company_id='{$this->company_id}'
              where g.goods_isintegral = '1' and g.goods_shopgoodssale = '1' and g.goods_integralsale='1' and g.company_id='{$this->company_id}'
              group by p.prodtype_code
              ";

        $codeList = $this->DataControl->selectClear($sql);
        if (!$codeList) {
            $this->error = true;
            $this->errortip = "暂无货品分类哦";
            return false;
        }
        return $codeList;

    }

    function getSection($paramArray)
    {

        $sql = "select ci.integralsection_id,ci.integralsection_begin,ci.integralsection_end
              from smc_code_integralsection as ci
              where ci.company_id='{$this->company_id}'
              order by ci.integralsection_begin asc
              ";

        $sectionList = $this->DataControl->selectClear($sql);
        if (!$sectionList) {
            $this->error = true;
            $this->errortip = "暂无积分区间哦";
            return false;
        }
        return $sectionList;
    }

    function exchangeGoods($paramArray)
    {
        $goodsOne = $this->DataControl->getFieldOne("erp_goods", "goods_integral", "goods_isintegral='1' and goods_shopgoodssale='1' and goods_integralsale='1' and goods_id='{$paramArray['goods_id']}' and company_id='{$this->company_id}'");
        if (!$goodsOne) {
            $this->error = true;
            $this->errortip = "无对应可兑换积分商品";
            return false;
        }

        $re = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$paramArray['goods_id']}'");
        if ($re) {
            $rep = $re['goods_repertory'];
        } else {
            $rep = '0';
        }

        if ($paramArray['num'] > $rep) {
            $this->error = true;
            $this->errortip = "剩余库存不足";
            return false;
        }

        $stuIntegralOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$paramArray['student_id']}'");

        $stuIntegralOne['property_integralbalance'] = $stuIntegralOne['property_integralbalance'] ? $stuIntegralOne['property_integralbalance'] : 0;

        if ($stuIntegralOne['property_integralbalance'] < ($paramArray['num'] * $goodsOne['goods_integral'])) {
            $this->error = true;
            $this->errortip = "积分不足";
            return false;
        }

        $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
        $bool = $BalanceModel->reduceStuIntegral($paramArray['student_id'], $paramArray['num'] * $goodsOne['goods_integral'], 0, 1, 0, $this->parenterOne['parenter_id'], $this->LgStringSwitch('积分商城兑换'), $this->LgStringSwitch('积分商城兑换'), 0 ,0);
        if ($bool) {
            $data = array();
            $data['school_id'] = $paramArray['school_id'];
            $data['student_id'] = $paramArray['student_id'];
            $data['parenter_id'] = $paramArray['parenter_id'];
            $data['goods_id'] = $paramArray['goods_id'];
            $data['integralgoods_score'] = $goodsOne['goods_integral'];
            $data['integralgoods_number'] = $paramArray['num'];
            $data['integralgoods_createtime'] = time();

            $integralgoods_id = $this->DataControl->insertData("smc_student_integralgoods", $data);

            $goods_data = array();
            $goods_data['student_id'] = $paramArray['student_id'];
            $goods_data['school_id'] = $paramArray['school_id'];
            $goods_data['goods_id'] = $paramArray['goods_id'];
            $goods_data['integralgoods_id'] = $integralgoods_id;
            $goods_data['erpgoods_createtime'] = time();
            $this->DataControl->insertData("smc_student_erpgoods", $goods_data);

            $trackdate = array();
            $trackdate['integralgoods_id'] = $integralgoods_id;
            $trackdate['tracks_title'] = '兑换商品';
            $trackdate['tracks_information'] = '积分商城使用' . $goodsOne['goods_integral'] . '积分兑换商品';
            $trackdate['staffer_id'] = $paramArray['staffer_id'];
            $trackdate['tracks_time'] = time();
            $this->DataControl->insertData("smc_integral_tracks", $trackdate);

            $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$paramArray['student_id']}'");

            foreach ($parenter as &$val) {
                $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$paramArray['student_id']}'");
                $goods_name = $this->DataControl->getFieldOne("erp_goods", "goods_cnname", "goods_id = '{$paramArray['goods_id']}'");
                $restInt = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$paramArray['student_id']}'");
                $address = $this->DataControl->getFieldOne("smc_school", "school_address", "school_id = '{$paramArray['school_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$paramArray['company_id']}' and masterplate_name = '积分兑换成功通知'");
                if ($isset) {
                    $wxid = $isset['masterplate_wxid'];
                } else {
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '积分兑换成功通知'");
                    $wxid = $masterplate['masterplate_wxid'];
                }
                $token = $this->getParentToken($val);

                $a = $student_cnname['student_cnname'] . '学员您好，您已成功兑换商品';
                $b = $goods_name['goods_cnname'];
                $c = $goodsOne['goods_integral'] * $paramArray['num'];
                $d = $restInt['property_integralbalance'];
                $e = $address['school_address'];
                $f = date("Y年m月d日 H:i");
                $g = '点击这里查看兑换详情哦～';

                $paramto = array();
                $paramto['student_id'] = $paramArray['student_id'];
                $paramto['parenter_id'] = $val['parenter_id'];
                $paramto['token'] = $token;

                $sendApiSting = request_by_curl("https://scptcapi.kedingdang.com/LoginAssistant/getjmJosn", dataEncode($paramto), "GET", array());

                $sendApiArray = json_decode($sendApiSting, "1");

                $str = $sendApiArray['return']['jmstring'];

                $plus = str_replace("+", "%2B", $str);

                $pp = str_replace("=", "%3D", $plus);

                $h = "https://{$paramArray['company_id']}.scshop.kedingdang.com/home?timesteps=" . $sendApiArray['return']['timesteps'] . "&jmstring=" . $pp . "&type=integralRecord&student_id={$paramArray['student_id']}&limit=1&cid={$paramArray['company_id']}&s_id={$paramArray['student_id']}";

                $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $paramArray['student_id']);
                $wxteModel->IntegralExchange($a, $b, $c, $d, $e, $f, $g, $h, $wxid);
            }


            return $goodsOne['goods_integral'] * $paramArray['num'];

        } else {
            $this->error = true;
            $this->errortip = $BalanceModel->errortip;
            return false;
        }

    }

    /**
     * 获取孩子的卡券列表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/5 0005
     * @param $paramArray
     * @return array
     */
    function getCouponsListApi($paramArray)
    {
        $datawhere = "  cp.student_id='{$paramArray['student_id']}' and cp.coupons_isuse <>'-1' ";
        $today = date("Y-m-d");
        if (isset($paramArray['is_outdate']) && $paramArray['is_outdate'] !== '') {
            if ($paramArray['is_outdate'] == 1) {
                //已过期
                $datawhere .= " and cp.coupons_endtime <'{$today}'";
            }
        }
        $sql = "select
         cp.coupons_id,
          cp.coupons_name,
          cp.coupons_activity_name,
          cp.coupons_price,
          cp.coupons_starttime,
          cp.coupons_endtime,
          cp.coupons_use_condition,
          cp.coupons_endtime
         from smc_integral_cooperation_coupons as cp where {$datawhere}";

        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as $key => $value) {
                $spacing = strtotime(date("Y-m-d", strtotime($value['coupons_endtime']))) + 24 * 3600 - strtotime(date("Y-m-d"));
                switch ($spacing) {
                    case $spacing < 0 :
                        $dataList[$key]['outday_tip'] = "已过期";
                        break;
                    case $spacing <= 3600 * 24 * 2 and $spacing > 0:
                        $dataList[$key]['outday_tip'] = "即将过期";
                        break;
                    case $spacing <= 3600 * 24 * 3 and $spacing > 3600 * 24 * 2:
                        $dataList[$key]['outday_tip'] = "两天内过期";
                        break;
                    case $spacing <= 3600 * 24 * 4 and $spacing > 3600 * 24 * 3:
                        $dataList[$key]['outday_tip'] = "三天内过期";
                        break;
                    case $spacing <= 3600 * 24 * 5 and $spacing > 3600 * 24 * 4:
                        $dataList[$key]['outday_tip'] = "四天内过期";
                        break;
                    case $spacing <= 3600 * 24 * 6 and $spacing > 3600 * 24 * 5:
                        $dataList[$key]['outday_tip'] = "五天内过期";
                        break;
                    case $spacing <= 3600 * 24 * 7 and $spacing > 3600 * 24 * 6:
                        $dataList[$key]['outday_tip'] = "六天内过期";
                        break;
                    default :
                        $dataList[$key]['outday_tip'] = '';
                }
                $dataList[$key]['coupons_endtime'] = date("Y-m-d", strtotime($value['coupons_endtime']));
                $dataList[$key]['coupons_starttime'] = date("Y-m-d", strtotime($value['coupons_starttime']));
            }
        }
        return $dataList;
    }

    /**
     * 获取卡券详情
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/7 0007
     * @param $paramArray
     * @return array|bool|mixed
     */
    function getCouponsOneApi($paramArray)
    {
        $dataOne = $this->DataControl->getOne("smc_integral_cooperation_coupons", "coupons_id='{$paramArray['coupons_id']}'");
        $dataOne['coupons_endtime'] = date("Y-m-d", strtotime($dataOne['coupons_endtime']));
        if (!$dataOne) {
            $dataOne = array();
        }
        return $dataOne;
    }

     static $couponsArray = array(
        '0' => array('title' => '才气多多福袋', 'coupons_code' => 'A', 'sort' => 1, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/caiqiduoduo-icon.png'),
        '1' => array('title' => '才气多多福袋', 'coupons_code' => 'A', 'sort' => 5, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/caiqiduoduo-icon.png'),
        '2' => array('title' => '运气多多福袋', 'coupons_code' => 'B', 'sort' => 2, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/yunqiduoduo-icon.png'),
        '3' => array('title' => '运气多多福袋', 'coupons_code' => 'B', 'sort' => 6, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/yunqiduoduo-icon.png'),
        '4' => array('title' => '福气多多福袋', 'coupons_code' => 'C', 'sort' => 3, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/fuqiduoduo-icon.png'),
        '5' => array('title' => '福气多多福袋', 'coupons_code' => 'C', 'sort' => 7, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/fuqiduoduo-icon.png'),
        '6' => array('title' => '谢谢参与', 'coupons_code' => '0', 'sort' => 4, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/xiexie-icon.png'),
        '7' => array('title' => '谢谢参与', 'coupons_code' => '0', 'sort' => 8, 'img' => 'https://jdbstuapp.oss-cn-shanghai.aliyuncs.com/IntegralMallApp/weapp/xiexie-icon.png')
    );

    /**
     * 福袋抽奖获取可抽奖的奖券
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     * @return array
     */
    function getFuActivityRewardApi($request)
    {
        $array_sort = array_column(static::$couponsArray, "sort");
        array_multisort($array_sort, SORT_ASC, static::$couponsArray);
        $data = static::$couponsArray;
        return $data;
    }

    /**
     * 福袋抽奖-抽奖
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     * @param $request
     * @return array
     */
    function getFuPackageActivity($request)
    {
        if (!isset($request['student_id'])) {
            $this->error = 1;
            $this->errortip = '请先登录';
            return array();
        }
        if (isset($request['student_id']) && $request['student_id'] == '') {
            $this->error = 1;
            $this->errortip = '请先登录';
            return array();
        }
        $chanceNum = $this->getStudentChance($request);
        if ($chanceNum <= 0) {
            $this->error = 1;
            $this->errortip = '您已没有抽奖机会了';
            return array();
        }
        $coupons_package_array = static::$couponsArray;
        $A_couponNum = $this->DataControl->selectOne("select count(g.couponslog_id) as Anum from smc_integral_stu_couponslog as g where g.couponslog_type='A'");
        $B_couponNum = $this->DataControl->selectOne("select count(g.couponslog_id) as Bnum from smc_integral_stu_couponslog as g where g.couponslog_type='B'");
        $C_couponNum = $this->DataControl->selectOne("select count(g.couponslog_id) as Cnum from smc_integral_stu_couponslog as g where g.couponslog_type='C'");
        $rand = rand(0, 5);
        if ($A_couponNum['Anum'] >= '99') {
            foreach ($coupons_package_array as $key => $value) {
                if ($value['coupons_code'] == 'A') {
                    unset($coupons_package_array[$key]);
                }
            }
        }
        if ($B_couponNum['Bnum'] >= '199') {
            foreach ($coupons_package_array as $key => $value) {
                if ($value['coupons_code'] == 'B') {
                    unset($coupons_package_array[$key]);
                }
            }
        }
        if ($C_couponNum['Cnum'] >= '699') {
            foreach ($coupons_package_array as $key => $value) {
                if ($value['coupons_code'] == 'C') {
                    unset($coupons_package_array[$key]);
                }
            }
        }
        $coupons_package_values = array_values($coupons_package_array);
        if (count($coupons_package_values) > 2) {
            $rand = rand(0, count($coupons_package_values) - 3);
        }
        $coupons_code = $coupons_package_values[$rand]['coupons_code'];
        switch ($coupons_code) {
            case 'A':
                $this->combineFuPackageByType($request['student_id'], $coupons_code);
                $this->addStuCouponslog($request['student_id'], $coupons_code,$request['school_id']);
                $result = array();
                $result['coupons_code'] = $coupons_code;
                $result['coupons_tip'] = "才气多多福袋x1";
                $this->error = 0;
                $this->errortip = '才气多多福袋x1';
                break;
            case 'B':
                $this->combineFuPackageByType($request['student_id'], $coupons_code);
                $this->addStuCouponslog($request['student_id'], $coupons_code,$request['school_id']);
                $result = array();
                $result['coupons_code'] = $coupons_code;
                $result['coupons_tip'] = "运气多多福袋x1";
                $this->error = 0;
                $this->errortip = '运气多多福袋x1';
                break;
            case 'C':
                $this->combineFuPackageByType($request['student_id'], $coupons_code);
                $this->addStuCouponslog($request['student_id'], $coupons_code,$request['school_id']);
                $result = array();
                $result['coupons_code'] = $coupons_code;
                $result['coupons_tip'] = "福气多多福袋x1";
                $this->error = 0;
                $this->errortip = '福气多多福袋x1';
                break;
            case '0':
                $result = array();
                $result['coupons_code'] = '0';
                $result['coupons_tip'] = "谢谢参与";
                $this->error = -1;
                $this->errortip = '谢谢参与';
                break;
            default:
                $result = array();
                $result['coupons_code'] = '0';
                $result['coupons_tip'] = "谢谢参与";
                $this->error = -1;
                $this->errortip = '谢谢参与';
        }
        return $result;
    }

    /**
     * 获取学生的抽奖次数
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     */
    function getStudentChance($request)
    {
        $datawhere = " a.student_id='{$request['student_id']}'  ";
        if ($request['company_id'] == '8888') {
            $datawhere .= "and c.coursetype_id='65'";
        } else {
            $datawhere .= "and c.coursetype_id='57'";
        }
        $starttime = strtotime("2021-01-01");
        $having = " pay_successtime>='{$starttime}'";
//        $endtime = strtotime(date("2021-02-t"));
//        $datawhere .= " and A.pay_successtime<={$endtime}";

        $sql = "
            SELECT
                a.company_id AS company_id,
                a.school_id AS school_id,
                a.student_id AS student_id,
                c.coursetype_id AS coursetype_id,
                min( m.pay_successtime ) AS pay_successtime 
            FROM
                smc_payfee_order_pay m
                LEFT JOIN smc_payfee_order a ON a.order_pid = m.order_pid
                LEFT JOIN smc_payfee_order_course b ON m.order_pid = b.order_pid
                LEFT JOIN smc_course c ON b.course_id = c.course_id 
                AND c.company_id = a.company_id 
            WHERE
                {$datawhere}
                AND m.pay_issuccess = 1 
                AND m.pay_successtime > 0 
                AND m.paytype_code <> 'feewaiver' 
                AND m.pay_type = 0 
                AND a.order_status > - 2 
                AND a.order_paidprice > 0 
                AND NOT EXISTS (
            SELECT
                1 
            FROM
                smc_student_coursebalance_log x
                JOIN smc_course y 
            WHERE
                x.course_id = y.course_id 
                AND y.company_id = a.company_id 
                AND x.student_id = a.student_id 
                AND y.coursetype_id = c.coursetype_id 
                AND x.log_playname LIKE '2.0%' 
                ) 
                AND NOT EXISTS (
            SELECT
                1 
            FROM
                smc_student_coursecatbalance x
                JOIN smc_code_coursecat y 
            WHERE
                x.coursecat_id = y.coursecat_id 
                AND y.company_id = a.company_id 
                AND y.company_id = x.company_id 
                AND x.feetype_code IN ( 'Deposit', 'Manage', 'Times' ) 
                AND x.student_id = a.student_id 
                AND y.coursetype_id = c.coursetype_id 
                AND x.coursecatbalance_createtime < ( m.pay_successtime - 86400 ) 
                ) 
                AND NOT EXISTS ( SELECT 1 FROM smc_integral_stu_couponslog AS L WHERE L.student_id = a.student_id ) 
            GROUP BY
                a.company_id,
                a.student_id,
                c.coursetype_id 
            HAVING
                {$having}
        ";
        $dataOne = $this->DataControl->selectOne($sql);
        if ($dataOne) {
            $chance_num = 1;
        } else {
            $chance_num = 0;
        }
        return $chance_num;
    }

    /**
     * 组成一个福袋
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     *
     */
    private function combineFuPackageByType($student_id, $coupons_type)
    {
        $data = array();
        $data['coupons_activity_name'] = "福袋抽奖";
        $data['student_id'] = $student_id;
        $data['coupons_compose_package'] = $coupons_type;
        $data['coupons_lotterytime'] = time();
        switch ($coupons_type) {
            case 'A':
                $where1 = "student_id=0 and coupons_code='HLD500'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='YZSP198'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='KLTN01'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='KLTN02'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='LW50'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                //卡通尼乐园
                $where1 = "student_id=0 and coupons_code='KTN01'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                break;
            case 'B':
                $where1 = "student_id=0 and coupons_code='HLD300'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='YZSP168'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='KLTN02'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                //卡通尼乐园
                $where1 = "student_id=0 and coupons_code='KTN02'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                break;
            case 'C':
                $where1 = "student_id=0 and coupons_code='HLD98'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='KLTN01'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                $where1 = "student_id=0 and coupons_code='YZSP40'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                //卡通尼乐园
                $where1 = "student_id=0 and coupons_code='KTN03'";
                $couponsOne = $this->DataControl->getFieldOne("smc_integral_cooperation_coupons", "coupons_id", $where1);
                $this->DataControl->updateData("smc_integral_cooperation_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $data);

                break;
            default:
                return false;
        }
        return true;
    }

    /**
     * 生成抽奖记录
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/6 0006
     * @param $student_id
     * @param $coupons_type
     */
    private function addStuCouponslog($student_id, $coupons_type, $school_id = 0)
    {
        $data = array();
        $data['student_id'] = $student_id;
        $data['couponslog_type'] = $coupons_type;
        $data['school_id'] = $school_id;
        $data['couponslog_createtime'] = time();
        $this->DataControl->insertData("smc_integral_stu_couponslog", $data);
    }


    public function __call($method, $args)
    {

    }

    function addTemplogInfo($companies_id, $agency_id, $action_status, $action_type, $table_name, $request_info)
    {
        $tempdata = array();
        $tempdata['companies_id'] = $companies_id;
        $tempdata['agency_id'] = $agency_id;
        $tempdata['templog_status'] = $action_status;
        $tempdata['templog_type'] = $action_type;
        $tempdata['templog_table'] = $table_name;
        $tempdata['templog_info'] = json_encode($request_info);
        $tempdata['templog_time'] = time();
        $this->DataControl->insertData('cmb_trans_templog', $tempdata);
    }

    function studParentBroacast()
    {
        $date = date('Y-m-d', strtotime('-3 days'));
        $now = date('Y-m-d', time());
        $sql = "
            SELECT
                a.student_id,
                a.companies_id,
                (select max(
		FROM_UNIXTIME(
			tt.broadcast_createtime,
			'%Y-%m-%d'
		)
	) AS broadcast_createtime from cmb_trans_transfer as tt where tt.student_id = a.student_id) as aa,
                s.school_cnname,
                max( a.income_date ) AS income_date,
                max(a.hourstudy_id ) AS hourstudy_id
            FROM
                cmb_trans_transfer a
                LEFT JOIN smc_school AS s ON a.school_id = s.school_id
                LEFT JOIN gmc_code_companies c ON c.companies_id = a.companies_id 
            WHERE
                1 
                AND a.company_id = '8888'
                AND a.income_isconfirm = 0 
                AND a.income_date >= '{$date}' 
                AND a.income_date < '{$now}' 
                AND c.companies_issupervise = 1 
                AND c.companies_agencyid <> '' 
                AND a.income_id>0
            GROUP BY
                a.student_id
                having 
                 aa < '{$now}' 
            limit 0,10
        ";

        $incomeList = $this->DataControl->selectClear($sql);
        if ($incomeList) {
            foreach ($incomeList as $incomeOne) {

                $parenter_id = $this->DataControl->selectClear("select f.parenter_id,w.parenter_wxtoken from smc_student_family as f left join smc_parenter_wxchattoken as w on f.parenter_id = w.parenter_id where student_id = '{$incomeOne['student_id']}' and parenter_wxtoken <> '' and wxchatnumber_id = '6'");

                if ($parenter_id) {
                    foreach ($parenter_id as $val) {
                        $openid = $val['parenter_wxtoken'];
                        $keyword1 = "课后考勤家长确认";
                        $time = $incomeOne['income_date'];
                        $keyword2 = date('Y-m-d', strtotime("+3 days $time"));
                        $keyword3 = $incomeOne['school_cnname'];
                        $sid = $incomeOne['student_id'];
                        $pid = $val['parenter_id'];
                        $hid = $incomeOne['hourstudy_id'];
                        $Model = new \Model\Scptc\MineModel();
                        $Model->ComfirmCheck($openid, $keyword1, $keyword2, $keyword3, $sid, $pid, $hid);

                    }
                }

                $data = array();
                $data['broadcast_createtime'] = time();
                $data['transfer_updatetime'] = time();
                $updatetransfer = $this->DataControl->updateData('cmb_trans_transfer', "hourstudy_id = '{$incomeOne['hourstudy_id']}'", $data);
                if (!$updatetransfer) {
                    $this->addTemplogInfo($incomeOne['companies_id'], $incomeOne['agency_id'], '0', 'update_bc', 'cmb_trans_transfer', $data);
                }


            }

        }
        return true;
    }

}
