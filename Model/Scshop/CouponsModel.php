<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scshop;

class CouponsModel extends modelTpl{
    public $m;

    function __construct() {
        parent::__construct ();
    }

    //根据学员编号获取孩子信息
    function GetStudentOne($paramArray)
    {
        $sql = " select s.student_id,s.student_branch,s.student_cnname,s.student_sex,TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ) AS student_age,c.school_cnname,c.school_branch,c.school_id 
                from smc_student as s
                LEFT JOIN smc_student_enrolled as t on s.student_id = t.student_id 
                LEFT JOIN smc_school as c ON c.school_id = t.school_id 
                WHERE s.student_branch = '{$paramArray['student_branch']}' 
                and  t.enrolled_status <> '-1' and  t.enrolled_status <> '2' 
                -- GROUP BY s.student_id 
                ORDER BY t.enrolled_createtime DESC ";
        // WHERE (s.student_branch = '{$paramArray['student_branch']}' or s.student_id = '{$paramArray['getstudent_id']}')  20230608 改的，查出两条数据觉得不对
        $res = $this->DataControl->selectClear($sql);

        if($paramArray['ctype'] == '1'){//同胞（老数据）
            $siblingOne = $this->DataControl->selectOne(" select siblings_parentname,siblings_parentmobile,student_prove_img as siblings_prove_imgone,siblings_prove_img,'' as apply_reson,'' as student_prove_img from shop_student_coupons_apply_siblings where student_id = '{$paramArray['student_id']}' and siblings_branch = '{$paramArray['student_branch']}' order by apply_id desc limit 0,1 ");
        }elseif($paramArray['ctype'] == '2'){//员工子女（老数据）
            $siblingOne = $this->DataControl->selectOne(" select '' as siblings_parentname,'' as siblings_parentmobile,'' as siblings_prove_imgone,'' as siblings_prove_img,a.apply_reson,b.student_prove_img from smc_student_coupons_apply as a,shop_student_coupons_apply_staff as b  where a.student_id = '{$res[0]['student_id']}' and a.applytype_branch = 'wscyuangong' and a.apply_id = b.apply_id and b.student_id = '{$res[0]['student_id']}' order by a.apply_id desc limit 0,1 ");
        }
        $data = array();
        $data['list'] = $res;
        $data['other'] = $siblingOne?$siblingOne:'';
        return $data;
    }

    //获取孩子可用商品优惠券
    function StuGoodsCoupons($paramArray)
    {
        $sql = "select c.coupons_id,c.coupons_pid,c.coupons_name from smc_student_coupons as c
WHERE c.student_id = '{$paramArray['student_id']}' and c.coupons_playclass = '0' and coupons_isuse = '0'";
        $coupousList = $this->DataControl->selectClear($sql);

        $coupousCount = $this->DataControl->selectOne("select count(c.coupons_id) from smc_student_coupons as c
WHERE c.student_id = '{$paramArray['student_id']}' and c.coupons_playclass = '0'");

        $res['list'] = $coupousList;
        $res['count'] = $coupousCount;
        return $res;
    }









    public function __call($method, $args) {

    }
}
