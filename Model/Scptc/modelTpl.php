<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Scptc;


class modelTpl extends \Model\modelTpl{

    public function __construct(){
        parent::__construct();
    }
	
	public function __call($method, $args)
	{
		echo "unknown method " . $method;
		return false;
		
	}

    public function addCrmWorkLog($company_id,$school_id,$marketer_id,$module,$type,$content,$module_id=0){
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");

        $stafferOne = $this->DataControl->getFieldOne("crm_marketer","staffer_id","marketer_id='{$marketer_id}'");
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['school_id'] =$school_id;
        $logData['staffer_id'] =$stafferOne['staffer_id'];
        $logData['worklog_module'] =$module;
        $logData['worklog_type'] =$type;
        $logData['worklog_content'] =$content;
        $logData['worklog_ip'] =real_ip();
        $logData['worklog_time'] =time();
        $this->DataControl->insertData('crm_staffer_worklog',$logData);
    }



    /**
	 * @param $client_id
	 * @param $marketer_id
	 * @param $ismajor
	 * @return bool
	 *  验证是否为负责人 --跟进时验证 --传0
	 * 验证为否为主负责人 ---流失时验证 -- 传1
	 */
	
	function verificationTrack($client_id, $marketer_id, $ismajor = 0)
	{
		
		$where = "marketer_id={$marketer_id}  and client_id={$client_id} and principal_leave =0";
		if ($ismajor == 1) {
			$where .= " and  principal_ismajor=1";
		}
		$principalOne = $this->DataControl->getOne('crm_client_principal', $where);
		if ($principalOne) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * @param $client_id
	 * @return bool
	 * 检测用户状态 是否没有被分配  1-false被分配
	 */
	function checkClientstatus($client_id,$school_id)
	{
		if (!$client_id) {
			return false;
		}
		$clientstatus = $this->DataControl->getFieldOne("crm_client", 'client_distributionstatus', "client_id={$client_id}");
		$schoolenterOne = $this->DataControl->getFieldOne('crm_client_schoolenter','schoolenter_id',"client_id={$client_id} and school_id='{$school_id}'");
		if($clientstatus && $schoolenterOne)
		if ($clientstatus['client_distributionstatus'] == 1) {
			return false;
		} else {
			return true;
		}
	}
	
	/**
	 * @param $client_id
	 * @return bool
	 * 检测用户是处在柜询或试听状态
	 */
	function checkIsvisit($client_id,$school_id)
	{
		if (!$client_id) {
			return false;
		}
		
		$clientIsinvite = $this->DataControl->getOne("crm_client_invite", "client_id='{$client_id}'and school_id='{$school_id}'", ' Order by invite_createtime Desc');
		$clientIsaudition = $this->DataControl->getOne("crm_client_audition", "client_id='{$client_id}' and school_id='{$school_id}'", ' Order by audition_createtime Desc');
		if ( ($clientIsinvite && ($clientIsinvite['invite_isvisit'] == 0)) || ($clientIsaudition && ($clientIsaudition['audition_isvisit'] == 0)) ) {
			return false;
		}else{
			return true;
		}
	}
	
	function checkIsvisitDate($client_id,$date){
		
		$clientIsaudition =   $this->DataControl->getOne("crm_client_audition","audition_visittime='{$date}' and client_id ='{$client_id}' and audition_isvisit =0");
		$clientIsinvite =   $this->DataControl->getOne("crm_client_invite","invite_visittime='{$date}' and client_id ='{$client_id}' and invite_isvisit =0");
		if ( ($clientIsinvite && ($clientIsinvite['invite_isvisit'] == 0)) || ($clientIsaudition && ($clientIsaudition['audition_isvisit'] == 0)) ) {
			return false;
		}else{
			return true;
		}

	}
	
	/**
	 * @param $icard
	 * @return bool
	 * 检测用户身份证是否冲重复 ,重复不能添加
	 * 新增时 不用 传$client_id
	 * 编辑时 传 $client_id
	 */
	function  checkClientIcard($icard,$client_id=0){
	  
		
		 $datawhere = "1 and client_icard ='{$icard}'";
		 if(isset($client_id) && $client_id !==0 ){
			 $datawhere .= "and client_id <> '{$client_id}'";
		 }
		  $clientOne = $this->DataControl->selectOne("select client_id from crm_client where {$datawhere} ");
		 
		
		 if($clientOne){
		 	  return false;   //有重复的
		 }else{
		 	  return true;    //没重复
		 }
	}


	//11111111111111111
	
}
