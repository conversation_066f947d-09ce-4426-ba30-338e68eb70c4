<?php

namespace Model\Scptc;

Class TimetableModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();


    function monthList($request){

        $starttime=strtotime($request['month'].'-01');
        $endtime=strtotime(date('Y-m-t', $starttime));

        $time1=$starttime;
        $time2=$endtime;
        $monarr=array();
        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $monarr[] = date('Y-m-d',$time1);

            $time1=strtotime("+1 day",$time1);
        }
        if(!$monarr){
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }
        $tem_array=array();
        foreach($monarr as $val){
            $sql="select cht.teaching_id
                  from smc_class_hour_teaching as cht
                  left join smc_class_hour as ch on ch.hour_id=cht.hour_id
                  where ch.hour_day='{$val}' and cht.staffer_id='{$request['staffer_id']}' and ch.hour_ischecking<>'-1'
                  ";

            $data=array();
            $data['year']=date("Y",strtotime($val));
            $data['month']=date("m",strtotime($val));
            $data['day']=date("d",strtotime($val));
            if($this->DataControl->selectOne($sql)){
                $data['status']=1;
            }else{
                $data['status']=0;
            }
            $tem_array[]=$data;
        }

        return $tem_array;

    }

    function studentTimetable($request){
        $datawhere=" 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['fixedtime']) && $request['fixedtime']!=''){
            $datawhere.=" and ch.hour_day='{$request['fixedtime']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $datawhere.=" and ch.hour_day>='{$request['starttime']}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $datawhere.=" and ch.hour_day<='{$request['endtime']}'";
        }

        $sql="select ch.hour_id,ch.hour_day,c.class_cnname,sch.school_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,st.staffer_mobile
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0'
              order by ch.hour_starttime asc ";
        $sql.=" limit {$pagestart},{$num}";

        $hourList=$this->DataControl->selectClear($sql);

        if(!$hourList){
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $status=array('0'=>'待考勤','1'=>'已考勤','-1'=>'已取消');
        $week=array('0'=>'周日','1'=>'周一','2'=>'周二','3'=>'周三','4'=>'周四','5'=>'周五','6'=>'周六','7'=>'周日');

        foreach($hourList as &$hourOne){
            $hourOne['hour_ischecking_name']=$status[$hourOne['hour_ischecking']];
            $hourOne['week']=$week[date('w',$hourOne['hour_day'])];
            $hourOne['hour_evaluate']=1/20;
        }

        $data=array();
        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$hourList;

        return $data;

    }

    function hourItem($request){

        $sql="select c.class_cnname,sch.school_cnname,st.staffer_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,c.class_fullnums,ch.hour_content,ch.hour_ischecking,st.staffer_mobile
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0'
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              where ch.hour_id='{$request['hour_id']}'
              ";
        $hourOne=$this->DataControl->selectOne($sql);

        if(!$hourOne){
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $status=array('0'=>'待考勤','1'=>'已考勤','-1'=>'已取消');

        $hourOne['hour_ischecking_name']=$status[$hourOne['hour_ischecking']];

        return $hourOne;

    }

    function hourStuRemark($request){

        $sql="select s.student_cnname,sh.hourcomment_content,sh.hourcomment_score
              from eas_student_hourcomment as sh
              left join smc_student as s on s.student_id=sh.student_id
              where sh.hour_id='{$request['hour_id']}'
              order by sh.hourcomment_createtime desc
              ";

        $commentList=$this->DataControl->selectClear($sql);

        if(!$commentList){
            $this->error = true;
            $this->errortip = "无学员评论";
            return false;
        }

        return $commentList;
    }

    function stuMonthList($request){
        $datawhere= " 1 ";
        $starttime=strtotime($request['month'].'-01');
        $endtime=strtotime(date('Y-m-t', $starttime));

        if(isset($request['hour_way']) && $request['hour_way']!=''){
            $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
        }

        $time1=$starttime;
        $time2=$endtime;
        $monarr=array();
        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $monarr[] = date('Y-m-d',$time1);

            $time1=strtotime("+1 day",$time1);
        }
        if(!$monarr){
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }
        $tem_array=array();
        foreach($monarr as $val){
            $sql="select ch.hour_id
                  from smc_class_hour as ch
                  left join smc_student_study as ss on ss.class_id=ch.class_id
                  where {$datawhere} and ch.hour_day='{$val}' and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
                  order by ch.hour_starttime asc
                  ";

            $data=array();
            $data['year']=date("Y",strtotime($val));
            $data['month']=date("m",strtotime($val));
            $data['day']=date("d",strtotime($val));
            if($this->DataControl->selectOne($sql)){
                $data['status']=1;
            }else{
                $data['status']=0;
            }
            $tem_array[]=$data;
        }

        return $tem_array;

    }

    function hourContent($request){

        $sql="select ch.hour_content
              from smc_class_hour as ch where ch.hour_id='{$request['hour_id']}'";

        $hourOne=$this->DataControl->selectOne($sql);
        if(!$hourOne){
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        return $hourOne;
    }

    function updateHourContent($request){

        $data=array();
        $data['hour_content']=$request['hour_content'];
        $data['hour_updatatime']=time();
        if($this->DataControl->updateData("smc_class_hour","hour_id='{$request['hour_id']}'",$data)){
            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据错误";
            return false;
        }

    }

    function stuTimetableList($request){
        $datawhere=" sch.school_id = '{$request['school_id']}' ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['hour_way']) && $request['hour_way']!='' && $request['hour_way']!='不限'){
            $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
        }

        if(isset($request['fixedtime']) && $request['fixedtime']!=''){
            $datawhere.=" and ch.hour_day='{$request['fixedtime']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $datawhere.=" and ch.hour_day>='{$request['starttime']}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $datawhere.=" and ch.hour_day<='{$request['endtime']}'";
        }

        $sql="select ss.student_id,ch.class_id,ch.hour_id,ch.hour_day,ch.hour_number,ch.hour_name,c.class_cnname,c.class_enname,ch.hour_way,sch.school_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_mobile,c.course_id
              ,st.staffer_cnname
              ,(select AVG(stu.sturemarkstar_score) from eas_classhour_sturemark as cs left join eas_classhour_sturemarkstar as stu on stu.sturemark_id=cs.sturemark_id where cs.hour_id=ch.hour_id and cs.student_id='{$request['student_id']}' limit 0,1) as sturemarkstar_score
              ,(select sh.hourcomment_level from eas_student_hourcomment as sh where sh.hour_id=ch.hour_id and sh.student_id='{$request['student_id']}' limit 0,1) as hourcomment_level
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum
              ,(select sh.hourstudy_checkin from smc_student_hourstudy as sh where sh.class_id=c.class_id and sh.hour_id=ch.hour_id and sh.student_id='{$request['student_id']}' limit 0,1) as hourstudy_checkin
              from smc_class_hour as ch
              left join smc_student_study as ss on ss.class_id=ch.class_id
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
              order by ch.hour_day ASC,ch.hour_starttime asc ";

        $sql.=" limit {$pagestart},{$num}";

        $hourList=$this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "非常满意", "1" => "一般", "2" => "不满意"));

        if($hourList){
            foreach($hourList as &$val){
                $val['hourcomment_level'] = $status[$val['hourcomment_level']];

            }
        }

        if(!$hourList){
            $this->error = true;
            $this->errortip = "暂无课表信息，请换个日期查询";
            return false;
        }

        $status=array('0'=>'未上课','1'=>'已上课','-1'=>'已取消');
        $checkin=array('0'=>'异常','1'=>'签到');
        $week=array('0'=>'周日','1'=>'周一','2'=>'周二','3'=>'周三','4'=>'周四','5'=>'周五','6'=>'周六','7'=>'周日');

        foreach($hourList as &$hourOne){
            $hourOne['hour_ischecking_name']=$status[$hourOne['hour_ischecking']];
            if(!$hourOne['hourstudy_checkin'] && $hourOne['hourstudy_checkin']!='0'){
                $hourOne['hourstudy_checkin_name']='未上课';
            }else{
                $hourOne['hourstudy_checkin_name']=$checkin[$hourOne['hourstudy_checkin']];
            }
            if($hourOne['hour_way']=='0'){
                $hourOne['hour_way']='实体课';
            }elseif($hourOne['hour_way']=='1'){
                $hourOne['hour_way']='线上课';
            }else{
                $hourOne['hour_way']='未知';
            }

            $sql="select cc.coursetype_isname from smc_course as sc left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id where sc.course_id='{$hourOne['course_id']}'";
            $typeOne=$this->DataControl->selectOne($sql);
            if($typeOne['coursetype_isname']==0){
                $hourOne['class_cnname']=$hourOne['class_enname'];
            }

            $hourOne['week']=$week[date('w',$hourOne['hour_day'])];
            //教师对学员点评
            $sturemark = $this->DataControl->selectClear("SELECT t.* 
                          from eas_classhour_sturemark as s 
                          LEFT JOIN eas_classhour_sturemarkstar as t ON s.sturemark_id = t.sturemark_id
                          WHERE s.class_id = '{$hourOne['class_id']}' and s.hour_id = '{$hourOne['hour_id']}' and s.student_id = '{$hourOne['student_id']}' ");
            if($sturemark){
                $allnum = count($sturemark);
                $allscore = '0';
                foreach ($sturemark as $sturemarkvar){
                    $allscore += $sturemarkvar['sturemarkstar_score'];
                }
                $hourOne['comment'] = sprintf("%.1f",$allscore / $allnum);
            }else{
                $hourOne['comment'] = '0';
            }
            //学员对教师的评价
            $hourcomment = $this->DataControl->selectClear("SELECT s.hourcomment_score,hourcomment_level
                          from eas_student_hourcomment as s 
                          WHERE s.hour_id = '{$hourOne['hour_id']}' and s.student_id = '{$hourOne['student_id']}' ");
            if($hourcomment){
                $allnum = count($hourcomment);
                $allscore = '0';
                foreach ($hourcomment as $hourcommentvar){
                    $allscore += $hourcommentvar['hourcomment_score'];
                }
                $hourOne['evaluate'] = sprintf("%.1f",$allscore / $allnum);
            }else{
                $hourOne['evaluate'] = '0';
            }
//            $hourOne['comment'] = '点评';
//            $hourOne['evaluate'] = '评价';

            if( ($hourOne['comment'] == '' || $hourOne['comment'] == '0') && ($hourOne['evaluate'] == '' || $hourOne['evaluate'] == '0')){
                $hourOne['isevaluate'] = '1';
            }else{
                $hourOne['isevaluate'] = '0';
            }
        }

        $data=array();
        $count_sql = "select ch.hour_id
              from smc_class_hour as ch
              left join smc_student_study as ss on ss.class_id=ch.class_id
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$hourList;

        return $data;

    }






    function isHourApi($request){


        $dateList = json_decode(stripslashes($request['date']),true);
        $hourList = array();
        foreach ($dateList as $k => $item) {
            $datawhere=" sch.school_id = '{$request['school_id']}' ";

            if(isset($request['hour_way']) && $request['hour_way']!='' && $request['hour_way']!='不限'){
                $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
            }

            $datawhere .= " and ch.hour_day='{$item['date']}'";

            $sql = "select ch.hour_day
              from smc_class_hour as ch
              left join smc_student_study as ss on ss.class_id=ch.class_id
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
              order by ch.hour_day ASC,ch.hour_starttime asc ";

            $a = $this->DataControl->selectOne($sql);

            if ($a) {
                $hourList[$k]['status'] = '1';
            }else{
                $hourList[$k]['status'] = '0';
            }
            $hourList[$k]['hour_day'] = $item['date'];

        }

        return $hourList;

    }

    function stuHourItem($request){

        $datawhere = ' 1 ';

        if (isset($request['s_id']) && $request['s_id'] !== '') {
            $datawhere .= "and  sh.student_id='{$request['s_id']}'";
        }else{
            $datawhere .= "and  sh.student_id='{$request['student_id']}'";
        }


        $sql="select c.class_id,c.course_id,c.class_cnname,c.class_enname,sch.school_cnname,st.staffer_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,c.class_fullnums,ch.hour_content,ch.hour_way,ch.hour_number,st.staffer_mobile,st.staffer_img,ch.hour_ischecking,ch.hour_name,stt.staffer_cnname as staffer_assistant
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select sh.hourstudy_checkin from smc_student_hourstudy as sh where {$datawhere} and sh.hour_id='{$request['hour_id']}' limit 0,1) as hourstudy_checkin
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_class_hour_teaching as htt on htt.hour_id=ch.hour_id and htt.teaching_type='1' and htt.teaching_isdel='0'
              left join smc_staffer as stt on stt.staffer_id=htt.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where ch.hour_id='{$request['hour_id']}'
              ";
        $hourOne=$this->DataControl->selectOne($sql);

        if(!$hourOne){
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $status=array('0'=>'未上课','1'=>'已上课','-1'=>'已取消');
        $way=array('0'=>'实体课','1'=>'线上课');
        $checkin=array('0'=>'异常','1'=>'出勤');

        $hourOne['hour_ischecking_name']=$status[$hourOne['hour_ischecking']];
        $hourOne['hour_way_name']=$way[$hourOne['hour_way']];
        $hourOne['hourstudy_checkin']=$checkin[$hourOne['hourstudy_checkin']];
        $hourOne['staffer_assistant']=$hourOne['staffer_assistant']?$hourOne['staffer_assistant']:'暂无';

        $sql="select cc.coursetype_isname from smc_course as sc left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id where sc.course_id='{$hourOne['course_id']}'";
        $typeOne=$this->DataControl->selectOne($sql);
        if($typeOne['coursetype_isname']==0){
            $hourOne['class_cnname']=$hourOne['class_enname'];
        }

        $datawhere = ' 1 ';

        if (isset($request['s_id']) && $request['s_id'] !== '') {
            $datawhere .= "and h.student_id='{$request['s_id']}'";
        }else{
            $datawhere .= "and h.student_id='{$request['student_id']}'";
        }

        $sql="SELECT g.timelog_playtimes,h.hourstudy_checkin,sc.course_checkingintype
              FROM smc_student_coursebalance_timelog AS g,smc_student_hourstudy AS h,smc_course as sc
              WHERE {$datawhere} and h.hourstudy_id = g.hourstudy_id and g.course_id=sc.course_id and h.hour_id = '{$request['hour_id']}'";
        $cost = $this->DataControl->selectOne($sql);

        $datawhere = ' 1 ';

        if (isset($request['s_id']) && $request['s_id'] !== '') {
            $datawhere .= "and  cs.student_id='{$request['s_id']}'";
        }else{
            $datawhere .= "and  cs.student_id='{$request['student_id']}'";
        }

        $sql="select sr.sturemarkstar_name,sr.sturemarkstar_score
              from eas_classhour_sturemark as cs
              left join eas_classhour_sturemarkstar as sr on sr.sturemark_id=cs.sturemark_id
              where {$datawhere} and cs.hour_id='{$request['hour_id']}'
              ";

        $tempList=$this->DataControl->selectClear($sql);

        if(!$tempList){
            $tempList=array();
        }

        $sql="select cs.sturemark_comment,cs.sturemark_picturejson,s.student_cnname,s.student_enname,s.student_sex,s.student_img,stu.homeworkstu_score
              from eas_classhour_sturemark as cs
              left join smc_student  as s on s.student_id=cs.student_id
              left join eas_homeworkstu as stu on stu.hour_id=cs.hour_id and stu.student_id=cs.student_id
              where {$datawhere} and cs.hour_id='{$request['hour_id']}'";

        $info=$this->DataControl->selectOne($sql);

        if(!$info){
            $info=array();
        }

        $hourOne['student_cnname'] = $info['student_cnname'];
        $hourOne['student_img'] = $info['student_img'];
        $hourOne['student_sex'] = $info['student_sex'];

        $img = $this->DataControl->getFieldOne("smc_student","student_img","student_id = '{$request['student_id']}'");

        $datawhere = ' 1 ';

        if (isset($request['s_id']) && $request['s_id'] !== '') {
            $datawhere .= "and  sh.student_id='{$request['s_id']}'";
        }else{
            $datawhere .= " and sh.student_id='{$request['student_id']}'";
        }

        $sql="select sh.hourcomment_id,sh.hourcomment_content,sh.hourcomment_score,sh.hourcomment_level,sh.hourcomment_anonymous,s.student_cnname,s.student_enname,s.student_img 
              from eas_student_hourcomment as sh 
              LEFT JOIN smc_student as s ON s.student_id = sh.student_id
              where {$datawhere} and sh.hour_id='{$request['hour_id']}'";

        $stuCommentInfo=$this->DataControl->selectOne($sql);
        if($stuCommentInfo) {
            $stuCommentInfo['hourcomment_score'] = sprintf("%.1f", $stuCommentInfo['hourcomment_score']);
        }

        $noun = $this->DataControl->selectClear("select n.noun_word from eas_student_hourcomment_noun_apply as a left join eas_student_hourcomment_noun as n on a.noun_id = n.noun_id where a.hourcomment_id = '{$stuCommentInfo['hourcomment_id']}'");

        if(!$stuCommentInfo){
            $stuCommentInfo=array();
        }

        $datawhere = ' 1 ';

        if (isset($request['s_id']) && $request['s_id'] !== '') {
            $datawhere .= "and  cs.student_id='{$request['s_id']}'";
        }else{
            $datawhere .= "and  cs.student_id='{$request['student_id']}'";
        }

        $score_num = $this->DataControl->selectClear("SELECT ROUND(AVG(sr.sturemarkstar_score), 1) as score_num 
                                                        FROM eas_classhour_sturemark as cs
                                                        LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                                                        WHERE {$datawhere} AND cs.hour_id='{$request['hour_id']}' AND cs.class_id='{$hourOne['class_id']}'");

        $score = $score_num[0]['score_num'];

        $course_id = $this->DataControl->getFieldOne("smc_class_hour","course_id","hour_id = '{$request['hour_id']}'");

        $datawhere = ' 1 ';

        if (isset($request['s_id']) && $request['s_id'] !== '') {
            $datawhere .= "and  student_id='{$request['s_id']}'";
        }else{
            $datawhere .= "and  student_id='{$request['student_id']}'";
        }

        $restCourse = $this->DataControl->getFieldOne("smc_student_coursebalance","coursebalance_time","{$datawhere} and course_id = '{$course_id['course_id']}'");

        $data=array();
        if(!$cost){
            $data['cost'] = '0';
        }else{
            if($cost['timelog_playtimes'] == '1' && $cost['hourstudy_checkin']=='1'){
                $data['cost'] = '1';
            }elseif($cost['timelog_playtimes'] == '1' && $cost['hourstudy_checkin']=='0'){
                if($cost['course_checkingintype']==5){
                    $data['cost'] = '0';
                }else{
                    $data['cost'] = '1';
                }
            }else{
                $data['cost'] = '0';
            }
        }


        $data['hourOne']=$hourOne;
        $data['tempList']=$tempList;
        $data['info']=$info;
        $data['stuCommentInfo']=$stuCommentInfo;
        $data['noun']=$noun;
        $data['score']=$score;
        $data['img']=$img['student_img'];
        $data['time']=$restCourse['coursebalance_time'];

        return $data;

    }

    function commentHour($request){
        $data=array();
        $data['hour_id']=$request['hour_id'];
        $data['student_id']=$request['student_id'];
        $data['hourcomment_content']=$request['hourcomment_content'];
        $data['hourcomment_level'] = $request['hourcomment_level'];
        $data['hourcomment_anonymous']=$request['hourcomment_anonymous'];
        $data['hourcomment_createtime']=time();

        if($hourcomment_id = $this->DataControl->insertData("eas_student_hourcomment",$data)){

            $nounList = json_decode(stripslashes($request['noun']),true);
            foreach ($nounList as $item) {
                $data = array();
                $data['noun_id'] = $item['noun_id'];
                $data['hourcomment_id'] = $hourcomment_id;
                $this->DataControl->insertData('eas_student_hourcomment_noun_apply',$data);
            }

            $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$request['company_id']}' and masterplate_name = '点评完成通知' and masterplate_class = '0'");
            if($isset){
                $wxid = $isset['masterplate_wxid'];
            }else{
                $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '点评完成通知' and masterplate_class = '0'");
                $wxid = $masterplate['masterplate_wxid'];
            }

            $staffer = $this->DataControl->getFieldOne("smc_class_hour_teaching","staffer_id,class_id","hour_id = '{$request['hour_id']}'");
            $student_cnname = $this->DataControl->getFieldOne("smc_student","student_cnname","student_id = '{$request['student_id']}'");
            $course_id = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id = '{$staffer['class_id']}'");
            $course_cnname = $this->DataControl->getFieldOne("smc_course","course_cnname","course_id = '{$course_id['course_id']}'");
            $a = $request['staffer_cnname'].'老师您好，已有学员完成课堂评价，请注意查收哦~';
            $b = $course_cnname['course_cnname'];
            $c = $student_cnname['student_cnname'];
            $d = date('m月d日 H:i', time());
            $e = '点击这里查看评价详情';
            $f = "https://tesc.kedingdang.com/ClassReview/home";

            $count = $this->DataControl->selectOne("select count(*) as a from eas_student_hourcomment where hour_id = '{$request['hour_id']}'");
            //$num = $this->DataControl->selectOne("select count(ho.hourstudy_id) as a from smc_student_hourstudy as ho where ho.hour_id = '{$request['hour_id']}' and hourstudy_checkin = '1'");

            if($count['a'] == '1'){
                $wxteModel = new \Model\Api\ZjwxChatModel($staffer['staffer_id']);
                $wxteModel->StuComFirst($a,$b,$c,$d,$e,$f,$wxid);
            }

            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }










}
