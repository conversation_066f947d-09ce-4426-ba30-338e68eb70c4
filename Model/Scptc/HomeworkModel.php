<?php

namespace Model\Scptc;

Class HomeworkModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();


    function homeworkList($request){

        $datawhere=" 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime=strtotime($request['starttime']);
            $datawhere .= " and h.homework_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime=strtotime($request['endtime'].' 23:59:59');
            $datawhere .= " and h.homework_createtime <= '{$endtime}'";
        }

        $sql="select h.homework_id,h.homework_title,h.homework_createtime
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.homeworkstu_status='3') as readNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.homeworkstu_status='1') as submitNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.homeworkstu_comment<>'') as commentNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id) as allNum
              from eas_homework as h
              where {$datawhere} and h.staffer_id='{$request['staffer_id']}'
              ";

        $sql.="limit {$pagestart},{$num}";

        $workList=$this->DataControl->selectClear($sql);

        if(!$workList){
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        foreach($workList as &$workOne){

            $workOne['homework_createtime']=date("Y-m-d H:i",$workOne['homework_createtime']);

            $sql="select c.class_cnname,count(hk.student_id) as stuNum
                  from eas_homeworkstu as hk
                  left join smc_class as c on c.class_id=hk.class_id
                  where hk.homework_id='{$workOne['homework_id']}'
                  group by hk.class_id
                  order by hk.class_id asc
                  ";

            $classList=$this->DataControl->selectClear($sql);
            if($classList){
                $workOne['classInfo']=$classList;
            }else{
                $workOne['classInfo']=array();
            }
        }

        $data=array();
        $count_sql = "select h.homework_id
              from eas_homework as h
              where {$datawhere} and h.staffer_id='{$request['staffer_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$workList;

        return $data;

    }

    function homeworkReceiveInfo($request){
        $sql="select c.class_id,c.class_cnname,count(hk.student_id) as stuNum
                  from eas_homeworkstu as hk
                  left join smc_class as c on c.class_id=hk.class_id
                  where hk.homework_id='{$request['homework_id']}'
                  group by hk.class_id
                  order by hk.class_id asc
                  ";

        $classList=$this->DataControl->selectClear($sql);
        if(!$classList){
            $this->error = true;
            $this->errortip = "无相关接收信息";
            return false;
        }

        foreach($classList as &$classOne){

            $sql="select hk.student_id,s.student_cnname
                  from eas_homeworkstu as hk
                  left join smc_student as s on s.student_id=hk.student_id
                  where hk.homework_id='{$request['homework_id']}' and hk.class_id='{$classOne['class_id']}'
                  group by hk.student_id
                  order by hk.student_id asc
                  ";
            $studentList=$this->DataControl->selectClear($sql);
            if(!$studentList){
                $studentList=array();
            }
            $classOne['studentList']=$studentList;

        }

        return $classList;

    }

    function homeworkStuList($request){
        $datawhere=" 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['homeworkstu_status']) && $request['homeworkstu_status'] !== '') {
            $datawhere .= " and h.homeworkstu_status = '{$request['homeworkstu_status']}'";
        }

        $sql="select s.student_id,s.student_cnname,h.homeworkstu_status,h.homeworkstu_score
              from eas_homeworkstu as h
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'";

        $sql.="limit {$pagestart},{$num}";

        $studentList=$this->DataControl->selectClear($sql);

        if(!$studentList){
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        $status=array('0'=>'待完成','1'=>'已提交','2'=>'未读','3'=>'已读','4'=>'已评分');
        foreach($studentList as &$studentOne){
            if($studentOne['homeworkstu_status']=='4'){
                $studentOne['status_name']=$studentOne['homeworkstu_score'];
            }else{
                $studentOne['status_name']=$status[$studentOne['homeworkstu_status']];
            }
        }

        $data=array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$studentList;

        return $data;

    }

    function homeworkStuOne($request){

        $sql="select s.student_cnname,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl
              ,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              where h.homework_id='{$request['homework_id']}' and h.student_id='{$request['student_id']}'";
        $studentOne=$this->DataControl->selectOne($sql);

        if(!$studentOne){
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }
        if($studentOne['homeworkstu_sendtime']){
            $studentOne['homeworkstu_sendtime']=date("Y-m-d H:i",$studentOne['homeworkstu_sendtime']);
        }else{
            $studentOne['homeworkstu_sendtime']='未提交';
        }


        return $studentOne;
    }

    function getTempType($request){

        $datawhere=" 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and h.homeworktemp_title like '%{$request['keyword']}%'";
        }

        $sql="select h.homeworktemp_id,h.homeworktemp_title,h.homeworktemp_remark from eas_code_homeworktemp as h
              where {$datawhere} and h.company_id='{$request['company_id']}' and h.homeworktemp_type='{$request['homeworktemp_type']}'
              group by h.homeworktemp_id
              order by h.homeworktemp_id asc
              ";

        $sql.="limit {$pagestart},{$num}";
        $tempList=$this->DataControl->selectClear($sql);
        if(!$tempList){
            $this->error = true;
            $this->errortip = "无作业模板";
            return false;
        }


        $data=array();
        $count_sql = "select h.homeworktemp_id from eas_code_homeworktemp as h
              where {$datawhere} and h.company_id='{$request['company_id']}' and h.homeworktemp_type='{$request['homeworktemp_type']}'
              group by h.homeworktemp_id
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$tempList;

        return $data;

    }

    function stuHomeworkList($request){
        $datawhere=" 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and h.student_id = '{$request['student_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }
        $sql="select s.student_cnname,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl
              ,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'";

        $sql.="limit {$pagestart},{$num}";

        $studentList=$this->DataControl->selectClear($sql);

        if(!$studentList){
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        foreach($studentList as &$studentOne){
            if($studentOne['homeworkstu_sendtime']){
                $studentOne['homeworkstu_sendtime']=date("Y-m-d H:i",$studentOne['homeworkstu_sendtime']);
            }else{
                $studentOne['homeworkstu_sendtime']='未提交';
            }
        }

        $data=array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$studentList;

        return $data;
    }

    function submitComment($request){

        $data=array();
        $data['homeworkstu_score']=$request['homeworkstu_score'];
        $data['homeworkstu_status']=4;
        $data['homeworkstu_comment']=$request['homeworkstu_comment'];
        $data['homeworkstu_commentmedia']=$request['homeworkstu_commentmedia'];
        $data['homeworkstu_commentradiourl']=$request['homeworkstu_commentradiourl'];
        $data['homeworkstu_commenttime']=time();

        if($this->DataControl->updateData("eas_homeworkstu","homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'",$data)){
            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function hourList($request){

        $datawhere=" 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and h.homeworktemp_title like '%{$request['keyword']}%'";
        }

        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $datawhere .= " and ch.hour_day = '{$request['fixedtime']}'";
        }else{
            $day=date("Y-m-d");
            $datawhere .= " and ch.hour_day = '{$day}'";
        }

        $sql="select ch.hour_day,c.class_cnname,sch.school_cnname,c.class_fullnums,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,ch.class_id
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0'
              order by ch.hour_starttime asc
              ";

        $sql.="limit {$pagestart},{$num}";

        $hourList=$this->DataControl->selectClear($sql);

        if(!$hourList){
            $this->error = true;
            $this->errortip = "无课时数据";
            return false;
        }

        $data=array();
        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$hourList;

        return $data;

    }

    function classStuList($request){

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql="select ss.class_id,ss.student_id,s.student_cnname
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where ss.class_id in ({$request['class_ids']}) and ss.study_isreading='1'";

        $sql.="limit {$pagestart},{$num}";

        $studentList=$this->DataControl->selectClear($sql);

        if(!$studentList){
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        $data=array();
        $count_sql = "select ss.class_id
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where ss.class_id in ({$request['class_ids']}) and ss.study_isreading='1'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$studentList;

        return $data;

    }

    function submitHomework($request){

        $list=json_decode(stripslashes($request['list']),true);
        if(!$list){
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }


        $data=array();
        $data['homework_title']=$request['homework_title'];
        $data['homework_content']=$request['homework_content'];
        $data['homework_mediajson']=$request['homework_mediajson']?str_replace("\\/", "/", json_encode($request['homework_mediajson'],JSON_UNESCAPED_UNICODE)):'';
        $data['homework_radiourl']=$request['homework_radiourl']?$request['homework_radiourl']:'';
        $data['staffer_id']=$request['staffer_id'];
        $data['homework_createtime']=time();

        $homework_id=$this->DataControl->insertData("eas_homework",$data);

        foreach($list as $val){
            $data=array();
            $data['homework_id']=$homework_id;
            $data['class_id']=$val['class_id'];
            $data['student_id']=$val['student_id'];
            $data['homeworkstu_createtime']=time();
            $this->DataControl->insertData("eas_homeworkstu",$data);
        }

        return true;

    }

    function getStuHomeworkList($request){

        $datawhere=" c.school_id = '{$request['school_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ho.homework_title like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and h.student_id = '{$request['student_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }

        if (isset($request['homework_id']) && $request['homework_id'] !== '') {
            $datawhere .= " and h.homework_id = '{$request['homework_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime=strtotime($request['starttime']);
            $datawhere .= " and h.homeworkstu_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime=strtotime($request['endtime'].' 23:59:59');
            $datawhere .= " and h.homeworkstu_createtime <= '{$endtime}'";
        }

        $sql="select h.homework_id,h.student_id,h.hour_id,s.student_cnname,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,ho.homework_isrefer,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl,st.staffer_mobile,h.homeworkstu_createtime,st.staffer_img,ho.homework_content,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime,st.staffer_cnname,c.class_id,c.class_cnname
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              left join smc_staffer as st on st.staffer_id=ho.staffer_id
              left join smc_class as c on c.class_id=h.class_id
              where {$datawhere} and ((ho.homework_isrefer = '1' and (homeworkstu_status = '0' or homeworkstu_status = '2' or homeworkstu_status = '3' or homeworkstu_status = '5')) or (ho.homework_isrefer = '0' and (homeworkstu_status = '0' or homeworkstu_status = '2')))
              order by h.homeworkstu_createtime desc
              ";
        $sql.="limit {$pagestart},{$num}";

        $studentList=$this->DataControl->selectClear($sql);

        if(!$studentList){
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        foreach($studentList as &$studentOne){
            if($studentOne['homeworkstu_sendtime']){
                $studentOne['homeworkstu_sendtime']=date("Y-m-d H:i",$studentOne['homeworkstu_sendtime']);
            }else{
                $studentOne['homeworkstu_sendtime']='未提交';
            }
            $studentOne['homeworkstu_createtime']=date("Y-m-d H:i:s",$studentOne['homeworkstu_createtime']);
            if($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '0'){
                $studentOne['homeworkstu_status_name'] = '未提交';
            }elseif($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '2'){
                $studentOne['homeworkstu_status_name'] = '未提交';
            }elseif($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '3'){
                $studentOne['homeworkstu_status_name'] = '未提交';
            }elseif($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '5'){
                $studentOne['homeworkstu_status_name'] = '待订正';
            }elseif($studentOne['homework_isrefer'] == '0' && $studentOne['homeworkstu_status'] == '0'){
                $studentOne['homeworkstu_status_name'] = '未查看';
            }elseif($studentOne['homework_isrefer'] == '0' && $studentOne['homeworkstu_status'] == '2'){
                $studentOne['homeworkstu_status_name'] = '未查看';
            }
            $hour = $this->DataControl->getFieldOne("smc_class_hour","hour_name,hour_day,hour_starttime,hour_endtime","hour_id = '{$studentOne['hour_id']}'");
            $studentOne['time'] = $hour['hour_day'].' '.$hour['hour_starttime'].'-'.$hour['hour_endtime'];
            $studentOne['lesson'] = $hour['hour_name'];
        }

        $data=array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              left join smc_staffer as st on st.staffer_id=ho.staffer_id
              left join smc_class as c on c.class_id=h.class_id
              where {$datawhere} and ((ho.homework_isrefer = '1' and (homeworkstu_status = '0' or homeworkstu_status = '2' or homeworkstu_status = '3' or homeworkstu_status = '5')) or (ho.homework_isrefer = '0' and (homeworkstu_status = '0' or homeworkstu_status = '2')))
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$studentList;

        return $data;

    }

    function getStuDoneHomeworkList($request){

        $datawhere=" c.school_id = '{$request['school_id']}' ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and h.student_id = '{$request['student_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }

        if (isset($request['homework_id']) && $request['homework_id'] !== '') {
            $datawhere .= " and h.homework_id = '{$request['homework_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime=strtotime($request['starttime']);
            $datawhere .= " and h.homeworkstu_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime=strtotime($request['endtime'].' 23:59:59');
            $datawhere .= " and h.homeworkstu_createtime <= '{$endtime}'";
        }

        $sql="select h.homework_id,h.student_id,h.hour_id,s.student_cnname,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,ho.homework_isrefer,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl,st.staffer_mobile,h.homeworkstu_createtime,st.staffer_img,ho.homework_content,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime,st.staffer_cnname,c.class_id,c.class_cnname
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              left join smc_staffer as st on st.staffer_id=ho.staffer_id
              left join smc_class as c on c.class_id=h.class_id
              where {$datawhere} and ((ho.homework_isrefer = '1' and (homeworkstu_status = '1' or homeworkstu_status = '6' or homeworkstu_status = '4')) or (ho.homework_isrefer = '0' and (homeworkstu_status = '0' or homeworkstu_status = '3')))
              order by h.homeworkstu_createtime desc
              ";
        $sql.="limit {$pagestart},{$num}";

        $studentList=$this->DataControl->selectClear($sql);

        if(!$studentList){
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        foreach($studentList as &$studentOne){
            if($studentOne['homeworkstu_sendtime']){
                $studentOne['homeworkstu_sendtime']=date("Y-m-d H:i",$studentOne['homeworkstu_sendtime']);
            }else{
                $studentOne['homeworkstu_sendtime']='未提交';
            }
            $studentOne['homeworkstu_createtime']=date("Y-m-d H:i:s",$studentOne['homeworkstu_createtime']);
            if($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '1'){
                $studentOne['homeworkstu_status_name'] = '已提交';
            }elseif($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '6'){
                $studentOne['homeworkstu_status_name'] = '已提交';
            }elseif($studentOne['homework_isrefer'] == '1' && $studentOne['homeworkstu_status'] == '4'){
                $studentOne['homeworkstu_status_name'] = '已提交';
            }elseif($studentOne['homework_isrefer'] == '0' && $studentOne['homeworkstu_status'] == '3'){
                $studentOne['homeworkstu_status_name'] = '已查看';
            }
            $hour = $this->DataControl->getFieldOne("smc_class_hour","hour_name,hour_day,hour_starttime,hour_endtime","hour_id = '{$studentOne['hour_id']}'");
            $studentOne['time'] = $hour['hour_day'].' '.$hour['hour_starttime'].'-'.$hour['hour_endtime'];
            $studentOne['lesson'] = $hour['hour_name'];
        }

        $data=array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              left join smc_staffer as st on st.staffer_id=ho.staffer_id
              left join smc_class as c on c.class_id=h.class_id
              where {$datawhere} and ((ho.homework_isrefer = '1' and (homeworkstu_status = '1' or homeworkstu_status = '6' or homeworkstu_status = '4')) or (ho.homework_isrefer = '0' and (homeworkstu_status = '0' or homeworkstu_status = '3')))
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list']=$studentList;

        return $data;

    }

    function homeworkItem($request){
        $sql="select h.homework_title,h.homework_content,h.homework_mediajson,h.homework_radiourl,s.staffer_id,s.staffer_cnname,h.homework_createtime
              from eas_homework as h
              left join smc_staffer as s on s.staffer_id=h.staffer_id
              where h.homework_id='{$request['homework_id']}'";

        $workOne=$this->DataControl->selectOne($sql);

        if(!$workOne){
            $this->error = true;
            $this->errortip = "无相关作业";
            return false;
        }

        $workOne['homework_createtime']=date("Y-m-d H:i:s",$workOne['homework_createtime']);
        $sql="select c.class_id,c.class_cnname,count(hk.student_id) as stuNum
                  from eas_homeworkstu as hk
                  left join smc_class as c on c.class_id=hk.class_id
                  where hk.homework_id='{$request['homework_id']}'
                  group by hk.class_id
                  order by hk.class_id asc
                  ";

        $classList=$this->DataControl->selectClear($sql);
        if(!$classList){
            $classList=array();
        }

        foreach($classList as &$classOne){

            $sql="select hk.student_id,s.student_cnname
                  from eas_homeworkstu as hk
                  left join smc_student as s on s.student_id=hk.student_id
                  where hk.homework_id='{$request['homework_id']}' and hk.class_id='{$classOne['class_id']}'
                  group by hk.student_id
                  order by hk.student_id asc
                  ";
            $studentList=$this->DataControl->selectClear($sql);
            if(!$studentList){
                $studentList=array();
            }
            $classOne['studentList']=$studentList;

        }

        $workOne['classInfo']=$classList;


        return $workOne;
    }


    //查看作业
    function HomeworkDetailApi($request)
    {
        if($request['s_id']){
            $sql = "
            SELECT 
                e.homework_title,
                e.homework_content,
                h.homeworkstu_comment,
                e.homework_mediajson,
                e.homework_radiourl,
                e.homework_isrefer,
                h.homeworkstu_content,
                h.homeworkstu_mediajson,
                h.homeworkstu_radiourl,
                h.homeworkstu_commentmedia,
                h.homeworkstu_score,
                h.homeworkstu_status,
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                FROM_UNIXTIME( h.homeworkstu_commenttime, '%Y-%m-%d %H:%i:%s' ) AS homeworkstu_commenttime,
                FROM_UNIXTIME( h.homeworkstu_sendtime, '%Y-%m-%d %H:%i:%s' ) AS homeworkstu_sendtime,
                FROM_UNIXTIME( e.homework_createtime, '%Y-%m-%d %H:%i:%s' ) AS homework_createtime,
                c.class_cnname,
                o.hour_day,
                o.hour_starttime,
                o.hour_name,
                o.hour_endtime
            FROM
                eas_homeworkstu AS h
                LEFT JOIN eas_homework AS e ON h.homework_id = e.homework_id
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                LEFT JOIN smc_class as c on c.class_id = h.class_id
                LEFT JOIN smc_class_hour as o on o.hour_id = h.hour_id
                where e.homework_id = '{$request['homework_id']}' and h.student_id = '{$request['s_id']}'";
        }else{
            $sql = "
            SELECT 
                e.homework_title,
                e.homework_content,
                h.homeworkstu_comment,
                e.homework_mediajson,
                e.homework_radiourl,
                e.homework_isrefer,
                h.homeworkstu_content,
                h.homeworkstu_mediajson,
                h.homeworkstu_radiourl,
                h.homeworkstu_commentmedia,
                h.homeworkstu_score,
                h.homeworkstu_status,
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                FROM_UNIXTIME( h.homeworkstu_commenttime, '%Y-%m-%d %H:%i:%s' ) AS homeworkstu_commenttime,
                FROM_UNIXTIME( h.homeworkstu_sendtime, '%Y-%m-%d %H:%i:%s' ) AS homeworkstu_sendtime,
                FROM_UNIXTIME( e.homework_createtime, '%Y-%m-%d %H:%i:%s' ) AS homework_createtime,
                c.class_cnname,
                o.hour_day,
                o.hour_starttime,
                o.hour_name,
                o.hour_endtime
            FROM
                eas_homeworkstu AS h
                LEFT JOIN eas_homework AS e ON h.homework_id = e.homework_id
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                LEFT JOIN smc_class as c on c.class_id = h.class_id
                LEFT JOIN smc_class_hour as o on o.hour_id = h.hour_id
                where e.homework_id = '{$request['homework_id']}' and h.student_id = '{$request['student_id']}'";
        }


        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    function handHomework($request){
        $data=array();
        $data['homeworkstu_content']=$request['homeworkstu_content'];
        $data['homeworkstu_mediajson']=$request['homeworkstu_mediajson'];
        $data['homeworkstu_radiourl']=$request['homeworkstu_radiourl'];
        $data['homeworkstu_sendtime']=time();
        $data['homeworkstu_status']=1;

        $status = $this->DataControl->getFieldOne("eas_homeworkstu","homeworkstu_status","homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'");


        if($this->DataControl->updateData("eas_homeworkstu","homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'",$data)){

            $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$request['company_id']}' and masterplate_name = '作业完成通知' and masterplate_class = '0'");
            if($isset){
                $wxid = $isset['masterplate_wxid'];
            }else{
                $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '作业完成通知' and masterplate_class = '0'");
                $wxid = $masterplate['masterplate_wxid'];
            }

            $title = $this->DataControl->getFieldOne("eas_homework","homework_title","homework_id = '{$request['homework_id']}'");
            $student_cnname = $this->DataControl->getFieldOne("smc_student","student_cnname","student_id = '{$request['student_id']}'");
            $a = $request['staffer_cnname'].'老师您好，已有学员提交作业，请注意查收哦~';
            $b = $student_cnname['student_cnname'];
            $c = $title['homework_title'];
            $d = date('m月d日 H:i', time());
            $e = '点击这里查看作业详情，请尽快点评';

            if($status['homeworkstu_status'] == '4'){
                $f = "https://tesc.kedingdang.com/HomeWork/JobDetails?homework_id={$request['homework_id']}&student_id={$request['student_id']}&toComment=2";

            }else{
                $f = "https://tesc.kedingdang.com/HomeWork/JobDetails?homework_id={$request['homework_id']}&student_id={$request['student_id']}&toComment=1";

            }

            $count = $this->DataControl->selectOne("select count(*) as a from eas_homeworkstu where homework_id = '{$request['homework_id']}' and homeworkstu_sendtime > '0'");
            $num = $this->DataControl->selectOne("select count(*) as a from eas_homeworkstu where homework_id = '{$request['homework_id']}'");

            if($count['a'] == '1'){
                $wxteModel = new \Model\Api\ZjwxChatModel($request['staffer_id']);
                $wxteModel->StuHomFirst($a,$b,$c,$d,$e,$f,$wxid);
            }

            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }

    }

    function updateHomework($request){
        $data=array();
        $data['homeworkstu_content']=$request['homeworkstu_content'];
        $data['homeworkstu_mediajson']=$request['homeworkstu_mediajson'];
        $data['homeworkstu_radiourl']=$request['homeworkstu_radiourl'];
        $data['homeworkstu_sendtime']=time();
        if($request['correct']){
            $data['homeworkstu_status']=6;
        }else{
            $data['homeworkstu_status']=1;
        }

        if($this->DataControl->updateData("eas_homeworkstu","homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'",$data)){
            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }

    }

    function updateStatusAction($request){
        $data=array();

        $a = $this->DataControl->getFieldOne("eas_homeworkstu","homeworkstu_status","homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'");

        $data['homeworkstu_status']= '3';

        if($a['homeworkstu_status'] == '2'){
            if($this->DataControl->updateData("eas_homeworkstu","homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'",$data)){
                return true;
            }else{
                $this->error = true;
                $this->errortip = "数据库错误";
                return false;
            }
        }else{
            return true;
        }

    }




}