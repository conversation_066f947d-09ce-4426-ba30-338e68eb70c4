<?php

namespace Model\Scptc;

class MineModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

//    function __construct($publicarray)
//    {
//        parent::__construct();
//        if (is_array($publicarray)) {
//            $this->setPublic($publicarray);
//            $this->publicarray = $publicarray;
//        }
//    }


    //个人资料
    function PersonnalInfo($paramArray)
    {
        $sql = "
            SELECT
                s.*,
                f.family_mobile,
                f.parenter_id
            FROM
                smc_student AS s
            inner JOIN smc_student_family AS f ON f.student_id = s.student_id
            WHERE s.student_id = '{$paramArray['student_id']}' AND f.parenter_id = '{$paramArray['parenter_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $this->DataControl->updateData("smc_student_family", "parenter_id = '{$paramArray['parenter_id']}' and family_ismain = '1'", array("family_ismain" => 0));
        $this->DataControl->updateData("smc_student_family", "parenter_id = '{$paramArray['parenter_id']}' and student_id = '{$paramArray['student_id']}'", array("family_ismain" => 1));


        if ($NoticeDetail) {
            foreach ($NoticeDetail as &$NoticeVar) {
                $schoollist = $this->DataControl->selectOne("SELECT e.school_id,s.school_branch,s.school_cnname,s.school_shortname,s.school_type FROM smc_student_enrolled AS e 
                                                                LEFT JOIN smc_school as s ON s.school_id = e.school_id 
                                                                WHERE e.student_id = '{$NoticeVar['student_id']}' and e.enrolled_status = '1'");

                $NoticeVar['school_id'] = $schoollist['school_id'];
                $NoticeVar['school_shortname'] = $schoollist['school_shortname'];
                $NoticeVar['school_cnname'] = $schoollist['school_cnname'];
                $NoticeVar['school_type'] = $schoollist['school_type'];
                $NoticeVar['school_branch'] = $schoollist['school_branch'];
                $isbooking = $this->DataControl->getFieldOne("smc_parenter_wxchattoken", "wxchattoken_id", "parenter_id = '{$NoticeVar['parenter_id']}' and wxchatnumber_id = '6' and booking_status = '1'");
                if ($isbooking) {
                    $NoticeVar['isbooking'] = '1';
                } else {
                    $NoticeVar['isbooking'] = '0';
                }
                $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$NoticeVar['company_id']}'");
                $NoticeVar['company_cnname'] = $company['company_cnname'];
                $NoticeVar['company_logo'] = $company['company_logo'];
                $property = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id = '{$NoticeVar['student_id']}'");
                $NoticeVar['property_integralbalance'] = $property['property_integralbalance'];
                $issupervise = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "coursebalance_issupervise = '1' and student_id = '{$NoticeVar['student_id']}'");
                if (!$issupervise) {
                    $NoticeVar['student_isknow'] = '1';
                }
            }
        }

        $company = $this->DataControl->getFieldOne("gmc_company", "company_isshowhour,company_isshowpay,company_ismajor,company_isintegral,company_iswelfare,company_isshop,company_logo", "company_id = '{$paramArray['company_id']}'");

        $field = array();
        $field["student_cnname"] = "学员姓名";
        $field["student_sex"] = "性别";
        $field["student_img"] = "头像";
        $field["student_branch"] = "学员编号";
        $field["student_birthday"] = "出生日期";
        $field["family_mobile"] = "手机号码";

        $company_cnname = $this->DataControl->getFieldOne("gmc_company", "company_shortname", "company_id = '{$paramArray['company_id']}'");

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取个人资料成功', 'result' => $result, 'name' => $company_cnname['company_shortname'], 'company' => $company);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取个人资料失败', 'result' => $result);
        }

        return $res;
    }

    //学生是否知晓
    function StuIsknow($paramArray){
        $isknow = $this->DataControl->getFieldOne("smc_student","student_isknow","student_id = '{$paramArray['student_id']}'");

        if($isknow){
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $isknow);
        }else{
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $isknow);
        }
        return $res;

    }

    //修改出生日期
    function ChangeBirthdayAction($paramArray)
    {
        $data = array();
        $data['student_birthday'] = $paramArray['student_birthday'];
        $this->DataControl->updateData('smc_student', "student_id = '{$paramArray['student_id']}'", $data);

        $res = array('error' => '0', 'errortip' => "修改出生日期成功", 'result' => array());

        return $res;
    }

    //修改头像
    function ChangeImgAction($paramArray)
    {
        $data = array();
        $data['student_img'] = $paramArray['student_img'];
        $this->DataControl->updateData('smc_student', "student_id = '{$paramArray['student_id']}'", $data);

        $res = array('error' => '0', 'errortip' => "修改头像成功", 'result' => array());

        return $res;
    }

    //查看家庭联系信息
    function FamilyInfo($paramArray)
    {
        $sql = "
            SELECT
                f.family_id,
                f.family_relation,
                p.parenter_cnname,
                f.family_cnname,
                f.family_mobile
            FROM
                smc_student_family AS f left join smc_parenter as p on f.family_mobile = p.parenter_mobile
            WHERE f.student_id = '{$paramArray['student_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        if ($NoticeDetail) {
            foreach ($NoticeDetail as &$val) {
                if (!$val['parenter_cnname']) {
                    $val['parenter_cnname'] = $val['family_cnname'];
                }
            }
        }


        $field = array();
        $field["family_relation"] = "亲属关系";
        $field["parenter_cnname"] = "亲属姓名";
        $field["family_mobile"] = "手机号码";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '查看家庭联系信息成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看家庭联系信息失败', 'result' => $result);
        }

        return $res;
    }

    //反馈学校列表
    function ComplainSchool($paramArray)
    {
        $sql = "
            SELECT
                DISTINCT c.school_cnname ,c.school_id
            FROM
                smc_student_study AS s
                LEFT JOIN smc_school AS c ON s.school_id = c.school_id 
            WHERE
                s.student_id = '{$paramArray['student_id']}' and s.study_isreading = 1";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["school_cnname"] = "校区名称";
        $field["school_id"] = "学校id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈学校列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈学校列表失败', 'result' => $result);
        }

        return $res;
    }

    //反馈班级列表
    function ComplainClass($paramArray)
    {
        $sql = "
            SELECT 
                c.class_id,
                c.class_cnname
            FROM
                smc_class as c,smc_student_study AS s
            WHERE c.class_id = s.class_id AND c.school_id = '{$paramArray['school_id']}'
                AND s.student_id = '{$paramArray['student_id']}' and c.class_status = '1' ORDER BY s.study_endday DESC";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["class_id"] = "班级id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈班级列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈班级列表失败', 'result' => $result);
        }

        return $res;
    }

    //我的课表课时数
    function getTimeNum($paramArray)
    {
        $sid = $this->DataControl->getFieldOne("smc_student","student_id","student_branch = '{$paramArray['student_branch']}'");

        $allnum = $this->DataControl->selectOne("select from ");

        $sql = "
            SELECT 
                c.class_id,
                c.class_cnname
            FROM
                smc_class as c,smc_student_study AS s
            WHERE c.class_id = s.class_id AND c.school_id = '{$paramArray['school_id']}'
                AND s.student_id = '{$paramArray['student_id']}' and c.class_status = '1' ORDER BY s.study_endday DESC";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["class_id"] = "班级id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈班级列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈班级列表失败', 'result' => $result);
        }

        return $res;
    }

    //反馈教师列表
    function ComplainTeacher($paramArray)
    {
        $sql = "
            SELECT
                t.staffer_id,s.staffer_cnname
            FROM
                smc_class_teach as t left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                t.class_id = '{$paramArray['class_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_cnname"] = "教师名称";
        $field["staffer_id"] = "教师id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈教师列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈教师列表失败', 'result' => $result);
        }

        return $res;
    }

    //发布投诉
    function SendComplainAction($paramArray)
    {
        $data = array();
        $data['complain_type'] = $paramArray['complain_type'];
        $data['school_id'] = $paramArray['school_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['class_id'] = $paramArray['class_id'];
        $data['complain_content'] = $paramArray['complain_content'];
        $data['complain_mediajson'] = $paramArray['complain_mediajson'];
        $data['complain_createtime'] = time();
        $this->DataControl->insertData('eas_mine_complain', $data);

        $res = array('error' => '0', 'errortip' => "发布投诉成功", 'result' => array());

        return $res;
    }

    //切换孩子列表
    function ChangeStudentList($paramArray)
    {
        $sql = "
           SELECT
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                s.company_id,
                c.school_cnname,
                c.school_shortname,
                c.school_id,
                co.company_cnname
            FROM
                smc_student_family AS f
                inner JOIN smc_student AS s ON f.student_id = s.student_id
                left join smc_student_study as ss on ss.student_id = f.student_id
                inner join smc_school as c on ss.school_id = c.school_id
                inner join gmc_company as co on s.company_id = co.company_id
            WHERE
                f.family_mobile = '{$paramArray['mobile']}' AND f.parenter_id = '{$paramArray['parenter_id']}'
                GROUP BY f.student_id";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["student_cnname"] = "学员名称";
        $field["student_id"] = "学员id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取切换孩子列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取切换孩子列表失败', 'result' => $result);
        }

        return $res;
    }

//    //我的成长
//    function MyGrowUp($paramArray)
//    {
//        $datawhere = " 1 ";
//        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
//            $start_time = strtotime($paramArray['start_time']);
//            $datawhere .= " and h.hourcomment_createtime >= '{$start_time}'";
//        }
//        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
//            $end_time = strtotime($paramArray['end_time']);
//            $datawhere .= " and h.hourcomment_createtime <= '{$end_time}'";
//        }
//
//        $classlist = $this->DataControl->selectClear("select h.hour_id,h.hourcomment_score,o.hour_name,o.hour_lessontimes
//                          from eas_student_hourcomment as h
//                          LEFT JOIN smc_class_hour as o ON h.hour_id = o.hour_id
//                          WHERE {$datawhere} GROUP BY h.hour_id");
//        if($classlist){
//            $class_name = array();
//            $class_score = array();
//            foreach ($classlist as $classvar){
//                $class_name[] = $classvar['hour_name'];
//                $class_score[] = sprintf("%.1f", $classvar['hourcomment_score']);
//            }
//        }
//
//        $datawhereh = " 1 ";
//        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
//            $start_time = strtotime($paramArray['start_time']);
//            $datawhereh .= " and h.sturemark_createtime >= '{$start_time}'";
//        }
//        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
//            $end_time = strtotime($paramArray['end_time']);
//            $datawhereh .= " and h.sturemark_createtime <= '{$end_time}'";
//        }
//        $homeworklist = $this->DataControl->selectClear("select h.hour_id,o.hour_name,o.hour_lessontimes,
//                          (SELECT AVG(s.sturemarkstar_score) FROM eas_classhour_sturemarkstar as s WHERE h.sturemark_id = s.sturemark_id ) as  sturemarkscore
//                          from eas_classhour_sturemark as h
//                          LEFT JOIN smc_class_hour as o ON h.hour_id = o.hour_id
//                          WHERE {$datawhereh} GROUP BY h.hour_id");
//        if($classlist){
//            $homework_id = array();
//            $homework_date = array();
//            $homework_score = array();
//            foreach ($homeworklist as $homeworkvar){
//                $homework_id[] = $homeworkvar['hour_id'];
//                $homework_date[] = $homeworkvar['hour_name'];
//                $homework_score[] = sprintf("%.1f", $homeworkvar['sturemarkscore']);
//            }
//        }
//
//        $field = array();
//        $field["class_date"] = "课堂日期";
//        $field["class_score"] = "课堂分数";
//        $field["homework_date"] = "作业日期";
//        $field["homework_score"] = "作业分数";
//
//        $a = array();
//        $a['class_date'] = $class_name;
//        $a['class_score'] = $class_score;
//        $a['homework_id'] = $homework_id;
//        $a['homework_date'] = $homework_date;
//        $a['homework_score'] = $homework_score;
//
////        $a = array();
////        $a['class_date'] = ["12-16", "12-17", "12-18", "12-19", "12-20", "12-21", "12-22"];
////        $a['class_score'] = [2.3, 3.5, 3.6, 2.8, 4, 0.5, 4.3];
////        $a['homework_date'] = ["12-16", "12-17", "12-18", "12-19", "12-20", "12-21", "12-22"];
////        $a['homework_score'] = [65, 75, 70, 80, 73, 93, 75];
//
//        if ($a) {
//            $result = array();
//            $result["field"] = $field;
//            $result["data"] = $a;
//            $res = array('error' => '0', 'errortip' => '获取我的成长成功', 'result' => $result);
//        } else {
//            $result = array();
//            $result["field"] = $field;
//            $result["data"] = array();
//            $result["all_num"] = 0;
//            $res = array('error' => '1', 'errortip' => '获取我的成长失败', 'result' => $result);
//        }
//
//        return $res;
//    }


    //我的成长
    function MyGrowUp($paramArray)
    {
        $datawhere = "hc.student_id = '{$paramArray['student_id']}'";
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and ch.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and ch.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and ch.hour_day >= '{$week['nowweek_start']}' and ch.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and ch.hour_day >= CURDATE() and ch.hour_day <= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and ch.hour_day >= CURDATE() and ch.hour_day <= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and ch.hour_day >= '{$month[0]}' and ch.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and ch.hour_day >= '{$lastmonth[0]}' and ch.hour_day <= '{$lastmonth[1]}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$paramArray['class_id']}'";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%'  or c.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.class_cnname,c.class_enname,ch.hour_name,ch.hour_way,ch.hour_number,ch.hour_day,ch.hour_starttime,ch.hour_endtime,
                (SELECT sf.staffer_cnname
                    FROM smc_class_hour_teaching as ht
                    LEFT JOIN smc_staffer as sf ON sf.staffer_id=ht.staffer_id
                    WHERE ht.class_id = ch.class_id AND ht.hour_id = hc.hour_id AND ht.teaching_type='0'
                ) as staffer_cnname,
                (SELECT ROUND(AVG(sr.sturemarkstar_score), 1) FROM eas_classhour_sturemark as cs
                  LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                  WHERE cs.student_id=hc.student_id AND cs.hour_id=hc.hour_id
                ) as score_num,
                (SELECT cr.classroom_cnname FROM smc_classroom as cr WHERE cr.classroom_id=ch.classroom_id) as classroom_cnname,
                (SELECT cs.sturemark_comment FROM eas_classhour_sturemark as cs WHERE cs.hour_id=hc.hour_id AND cs.student_id=hc.student_id) as sturemark_comment
                FROM smc_student_hourstudy as hc
                LEFT JOIN smc_class_hour as ch ON ch.hour_id = hc.hour_id
                LEFT JOIN smc_class as c ON c.class_id = ch.class_id
                WHERE {$datawhere} ORDER BY ch.hour_day DESC";

        $sqls = "SELECT c.class_cnname,c.class_enname,ch.hour_name,ch.hour_way,ch.hour_number,ch.hour_day,ch.hour_starttime,ch.hour_endtime,hc.homeworkstu_score,hc.homeworkstu_comment,
                (SELECT sf.staffer_cnname
                    FROM smc_class_hour_teaching as ht
                    LEFT JOIN smc_staffer as sf ON sf.staffer_id=ht.staffer_id
                    WHERE ht.class_id = hc.class_id AND ht.hour_id = hc.hour_id AND ht.teaching_type='0'
                ) as staffer_cnname,
                (SELECT cr.classroom_cnname FROM smc_classroom as cr WHERE cr.classroom_id=ch.classroom_id) as classroom_cnname
                FROM eas_homeworkstu as hc
                LEFT JOIN smc_class_hour as ch ON ch.hour_id = hc.hour_id
                LEFT JOIN smc_class as c ON c.class_id = hc.class_id
                WHERE {$datawhere} ORDER BY ch.hour_day DESC";

        $ketanglist = $this->DataControl->selectClear($sql);
        if ($ketanglist) {
            foreach ($ketanglist as &$val) {
                $val['ketang_appraise'] = '课堂评价';
                if (!$val['score_num']) {
                    $val['score_num'] = '暂无评分';
                }
                if ($val['hour_way'] == '0') {
                    $val['hour_way'] = '实体课';
                } elseif ($val['hour_way'] == '1') {
                    $val['hour_way'] = '线上课';
                } else {
                    $val['hour_way'] = '未知';
                }
            }
        } else {
            $ketanglist = array();
        }
        $worklist = $this->DataControl->selectClear($sqls);
        if ($worklist) {
            foreach ($worklist as &$val) {
                $val['work_appraise'] = '作业评价';
                if (!$val['homeworkstu_score']) {
                    $val['homeworkstu_score'] = '暂无评分';
                }
                if ($val['hour_way'] == '0') {
                    $val['hour_way'] = '实体课';
                } elseif ($val['hour_way'] == '1') {
                    $val['hour_way'] = '线上课';
                } else {
                    $val['hour_way'] = '未知';
                }
            }
        } else {
            $worklist = array();
        }
        $dataList = array_merge($ketanglist, $worklist);

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $ketang_num = $this->DataControl->selectOne("SELECT COUNT(hc.hourstudy_id) as num
                                                           FROM smc_student_hourstudy as hc
                                                           LEFT JOIN smc_class_hour as ch ON ch.hour_id = hc.hour_id
                                                           LEFT JOIN smc_class as c ON c.class_id = ch.class_id
                                                           WHERE {$datawhere}");
            $work_num = $this->DataControl->selectOne("SELECT COUNT(hc.homework_id) as num
                                                         FROM eas_homeworkstu as hc
                                                         LEFT JOIN smc_class_hour as ch ON ch.hour_id = hc.hour_id
                                                         LEFT JOIN smc_class as c ON c.class_id = hc.class_id
                                                         WHERE {$datawhere}");
            if (!$ketang_num) {
                $ketang_num['num'] = 0;
            }
            if (!$work_num) {
                $work_num['num'] = 0;
            }
            if (isset($paramArray['type']) && $paramArray['type'] == '0') {
                $data['allnums'] = $ketang_num['num'];
            } elseif (isset($paramArray['type']) && $paramArray['type'] == '1') {
                $data['allnums'] = $work_num['num'];
            } else {
                $data['allnums'] = $ketang_num['num'] + $work_num['num'];
            }
        } else {
            $data['allnums'] = '0';
        }

        if (isset($paramArray['type']) && $paramArray['type'] == '0') {
            $ketanglist = array_slice($ketanglist, $pagestart, $num);
            $data['list'] = $ketanglist;
        } elseif (isset($paramArray['type']) && $paramArray['type'] == '1') {
            $worklist = array_slice($worklist, $pagestart, $num);
            $data['list'] = $worklist;
        } else {
            $dataList = array_slice($dataList, $pagestart, $num);
            $data['list'] = $dataList;
        }
        return $data;
    }


    //上课统计
    function ClassStatistics($paramArray)
    {
        $datawhere = "ssc.school_id = '{$paramArray['school_id']}' and ssc.student_id='{$paramArray['student_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sc.course_cnname like '%{$paramArray['keyword']}%' or sc.course_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['course_id']) {
            $sql = "select sc.course_id,sc.course_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname,ssc.coursebalance_time as restClass,ssc.coursebalance_figure as allpay,scf.courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend
              ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc limit 0,1) as study_isreading,(select SUM(poc.ordercourse_buynums) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=ssc.course_id and po.student_id=ssc.student_id and po.school_id=ssc.school_id and po.order_status>0 limit 0,1) as allClass
              ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_class as cl on cl.class_id=sh.class_id where sh.student_id=ssc.student_id and cl.course_id=sc.course_id) as hourstudyNum
              from smc_student_coursebalance as ssc
              left join smc_student_coursebalance_pricinglog as scp On scp.student_id =ssc.student_id and  scp.course_id =ssc.course_id
              left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=scp.pricing_id
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=scp.pricing_id
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              where {$datawhere}  and ssc.course_id = '{$paramArray['course_id']}'
              group by ssc.coursebalance_id
              order by ssc.coursebalance_createtime DESC
              limit {$pagestart},{$num}
              ";
        } else {
            $sql = "select sc.course_id,sc.course_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname
                    ,ssc.coursebalance_time as restClass,ssc.coursebalance_figure as allpay,scf.courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend
                    ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc limit 0,1) as study_isreading,(select SUM(poc.ordercourse_buynums) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=ssc.course_id and po.student_id=ssc.student_id and po.school_id=ssc.school_id and po.order_status>0 limit 0,1) as allClass
                    ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_class as cl on cl.class_id=sh.class_id where sh.student_id=ssc.student_id and cl.course_id=sc.course_id) as hourstudyNum
                      from smc_student_coursebalance as ssc
                      left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
                      left join smc_course as sc on sc.course_id=ssc.course_id
                      left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=ssc.pricing_id
                      left join smc_fee_pricing_tuition as pt on pt.pricing_id=ssc.pricing_id
                      left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
                      where {$datawhere}
                      group by ssc.coursebalance_id
                      order by ssc.coursebalance_createtime DESC
                      limit {$pagestart},{$num}
              ";
        }


        $NoticeDetail = $this->DataControl->selectClear($sql);

        if ($NoticeDetail) {
            foreach ($NoticeDetail as &$value) {
                if ($value['allClass'] && $value['allClass'] > $value['hourstudyNum']) {
                    $value['alreadyClass'] = $value['hourstudyNum'];
                } else {
                    $value['allClass'] = '--';
                    $value['alreadyClass'] = '--';
                }

                if ($value['allClass'] != ($value['alreadyClass'] + $value['restClass'])) {
                    $value['allClass'] = '--';
                }

            }
        }

        $allNums = $this->DataControl->selectOne("
            SELECT
               COUNT(*) as a
            FROM
               (select sc.course_id,sc.course_cnname as class_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname,scp.pricinglog_buytimes as allClass,ssc.coursebalance_time as restClass,ssc.coursebalance_figure as allpay,scf.courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend
              ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc limit 0,1) as study_isreading
              from smc_student_coursebalance as ssc
              left join smc_student_coursebalance_pricinglog as scp On scp.student_id =ssc.student_id and  scp.course_id =ssc.course_id
              left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=scp.pricing_id
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=scp.pricing_id
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              where {$datawhere}
              group by ssc.coursebalance_id
              order by ssc.coursebalance_createtime DESC) as a");
        $allnums = $allNums['a'];

        $field = array();
        $field["class_id"] = "课程id";
        $field["class_cnname"] = "课程名称";
        $field["allPay"] = "缴费总金额";
        $field["allClass"] = "总课时";
        $field["alreadyClass"] = "已上课时";
        $field["restClass"] = "剩余课时";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取上课统计成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无课程统计信息', 'result' => $result);
        }

        return $res;
    }

    //课消详情
    function ClassPayDetail($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and ho.hour_day >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and ho.hour_day <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] !== "") {
            $datawhere .= " and ho.hour_way ='{$paramArray['hour_way']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                l.log_id,
                ho.hour_id,
                ho.hour_way,
                ho.hour_number,
                ho.hour_way as hour_way_name,
                ho.hour_day as date,
                cl.class_cnname,
                cl.class_enname,
                ho.hour_name,
                ho.hour_starttime,
                ho.hour_endtime,
                r.classroom_cnname as class_room,
                s.staffer_cnname,
                l.log_playamount as price,
                ho.hour_way,
                sc.school_cnname
            FROM
                smc_student_coursebalance_log AS l
                LEFT JOIN smc_student_hourstudy AS h ON l.hourstudy_id = h.hourstudy_id
                LEFT JOIN smc_class_hour AS ho ON ho.hour_id = h.hour_id
                LEFT JOIN smc_class AS cl ON cl.class_id = ho.class_id
                LEFT JOIN smc_classroom AS r ON r.classroom_id = ho.classroom_id 
                left join smc_class_hour_teaching as t on t.hour_id = ho.hour_id
                left join smc_staffer as s on s.staffer_id = t.staffer_id
                left join smc_school as sc on sc.school_id = l.school_id
            WHERE
                {$datawhere}
                AND l.course_id = '{$paramArray['course_id']}' and l.student_id = '{$paramArray['student_id']}'
                AND l.hourstudy_id > '0'
                GROUP by l.log_id
            Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        if ($NoticeDetail) {
            foreach ($NoticeDetail as &$value) {
                $value['time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $value['class_time'] = (strtotime($value['hour_endtime']) - strtotime($value['hour_starttime'])) / 60;
                if ($value['hour_way_name'] == '0') {
                    $value['hour_way_name'] = '实体课';
                } else {
                    $value['hour_way_name'] = '线上课';
                }
            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*) as a
            FROM
                (SELECT
                ho.hour_day as date,
                cl.class_cnname,
                ho.hour_starttime,
                ho.hour_endtime,
                r.classroom_cnname as class_room,
                s.staffer_cnname,
                l.log_playamount as price,
                ho.hour_way,
                sc.school_cnname
            FROM
                smc_student_coursebalance_log AS l
                LEFT JOIN smc_student_hourstudy AS h ON l.hourstudy_id = h.hourstudy_id
                LEFT JOIN smc_class_hour AS ho ON ho.hour_id = h.hour_id
                LEFT JOIN smc_class AS cl ON cl.class_id = ho.class_id
                LEFT JOIN smc_classroom AS r ON r.classroom_id = ho.classroom_id 
                left join smc_class_hour_teaching as t on t.hour_id = ho.hour_id
                left join smc_staffer as s on s.staffer_id = t.staffer_id
                left join smc_school as sc on sc.school_id = l.school_id
            WHERE
                {$datawhere}
                AND l.course_id = '{$paramArray['course_id']}' and l.student_id = '{$paramArray['student_id']}'
                AND l.hourstudy_id > '0'
                GROUP by l.log_id) as a");
        $allnums = $all_num[0]['a'];

        $field = array();
        $field["school_cnname"] = "校区名称";
        $field["class_room"] = "教室";
        $field["staffer_cnname"] = "职工名称";
        $field["time"] = "上课时间";
        $field["date"] = "上课日期";
        $field["class_cnname"] = "课程名称";
        $field["class_time"] = "时常";
        $field["hour_number"] = "线上教室";
        $field["price"] = "金额";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取课消详情成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无课消明细哦～', 'result' => $result);
        }

        return $res;
    }

    //投诉列表（学校）
    function ScComplainList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and c.complain_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and c.complain_createtime <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.complain_id,
                s.school_cnname,
                c.complain_content,
                ss.student_cnname,
                ss.student_enname,
                ss.student_branch,
                f.family_mobile,
                FROM_UNIXTIME( c.complain_createtime, '%Y-%m-%d %H:%i' ) AS complain_createtime
            FROM
                eas_mine_complain AS c
                LEFT JOIN smc_school AS s ON c.school_id = s.school_id
                LEFT JOIN smc_student AS ss ON c.student_id = ss.student_id
                LEFT JOIN smc_student_family AS f ON c.student_id = f.student_id
           WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '0' order by c.complain_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.complain_id)
            FROM
                eas_mine_complain AS c
            WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '0' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }

    //投诉列表（教师）
    function TeaComplainList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and c.complain_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and c.complain_createtime <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.complain_id,
                s.school_cnname,
                c.complain_content,
                ss.student_cnname,
                ss.student_enname,
                ss.student_branch,
                f.family_mobile,
                FROM_UNIXTIME( c.complain_createtime, '%Y-%m-%d %H:%i' ) AS complain_createtime,
                st.staffer_cnname,
                st.staffer_branch
            FROM
                eas_mine_complain AS c
                LEFT JOIN smc_school AS s ON c.school_id = s.school_id
                LEFT JOIN smc_student AS ss ON c.student_id = ss.student_id
                LEFT JOIN smc_student_family AS f ON c.student_id = f.student_id
                left join smc_staffer as st on st.staffer_id = c.staffer_id
           WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '1'  order by c.complain_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.complain_id)
            FROM
                eas_mine_complain AS c
            WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '1' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }

    //投诉详情
    function ComplainDetail($paramArray)
    {
        $sql = "
            SELECT
                s.school_cnname,
                c.complain_content,
                ss.student_cnname,
                ss.student_img,
                FROM_UNIXTIME( c.complain_createtime, '%Y-%m-%d %H:%i' ) AS complain_createtime,
                c.complain_mediajson
            FROM
                eas_mine_complain AS c
                LEFT JOIN smc_school AS s ON c.school_id = s.school_id
                LEFT JOIN smc_student AS ss ON c.student_id = ss.student_id
                LEFT JOIN smc_student_family AS f ON c.student_id = f.student_id
                left join smc_staffer as st on st.staffer_id = c.staffer_id
           WHERE c.complain_id = '{$paramArray['complain_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["school_cnname"] = "学校";
        $field["complain_content"] = "投诉与建议";
        $field["student_cnname"] = "学员中文名";
        $field["student_img"] = "头像";
        $field["complain_createtime"] = "创建时间";
        $field["complain_mediajson"] = "视屏或者图片或者语音json";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取投诉详情成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取投诉详情失败', 'result' => $result);
        }

        return $res;
    }


    //通讯录列表
    function TimList($paramArray)
    {
        $sql = "
            SELECT s.class_id,c.class_cnname
                FROM smc_student_study as s 
                LEFT JOIN smc_class as c ON s.class_id = c.class_id 
                left join smc_course as co on co.course_id = c.course_id
                WHERE
                  s.company_id = '{$paramArray['company_id']}'
                and c.school_id = '{$paramArray['school_id']}'
                and s.student_id = '{$paramArray['student_id']}'
                and s.study_isreading = '1'
                and c.class_status = '1'
                GROUP BY s.class_id ORDER BY co.course_inclasstype ASC";
        $ClassList = $this->DataControl->selectClear($sql);

        $student = $this->DataControl->selectClear("
            select s.staffer_cnname,s.staffer_id,s.staffer_enname,s.staffer_img,s.staffer_sex,c.class_id,s.staffer_branch,c.teaching_type
                        from smc_class_hour_teaching as c
                        INNER JOIN smc_staffer AS s ON s.staffer_id = c.staffer_id
                        left join smc_class as l on c.class_id = l.class_id
                        where l.school_id = '{$paramArray['school_id']}' GROUP BY s.staffer_id,c.class_id");
        $son = array();
        foreach ($ClassList as $k => &$v) {
            $son[$k]['class_cnname'] = $v['class_cnname'];
            $son[$k]['class_id'] = $v['class_id'];
            foreach ($student as $key => $value) {
                if ($value['class_id'] == $v['class_id']) {
                    $son[$k]['children'][$key]['teaching_type'] = $value['teaching_type'];
                    $son[$k]['children'][$key]['staffer_cnname'] = $value['staffer_cnname'];
                    $son[$k]['children'][$key]['staffer_enname'] = $value['staffer_enname'];
                    $son[$k]['children'][$key]['staffer_img'] = $value['staffer_img'];
                    $son[$k]['children'][$key]['staffer_id'] = $value['staffer_id'];
                    $son[$k]['children'][$key]['staffer_sex'] = $value['staffer_sex'];
                    if (!$value['staffer_img']) {
                        if ($value['staffer_sex'] == '女') {
                            $son[$k]['children'][$key]['staffer_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191622x254703483.png';
                        } else {
                            $son[$k]['children'][$key]['staffer_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191622x427725268.png';
                        }
                    }
                }

            }

        }

        if ($ClassList) {
            $res = array('error' => '0', 'errortip' => '获取通讯录列表成功', 'result' => $son);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无通讯录信息', 'result' => array());
        }

        return $res;
    }


    //我的班级 --- 97--20200303
    function myClassList($paramArray)
    {
        $datawhere = "1";

        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== "") {
            $datawhere .= " and s.class_id = '{$paramArray['class_id']}'";
        }

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id = '{$paramArray['course_id']}'";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_branch like '%{$paramArray['keyword']}%' or c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.class_id,c.class_type,c.class_branch,c.class_cnname,c.class_enname,c.class_stdate,c.class_enddate,c.class_status,u.course_branch,u.course_cnname,
                (SELECT l.school_cnname FROM smc_school as l WHERE l.school_id = s.school_id ) as school_cnname,
                (SELECT count(ss.study_id) FROM smc_student_study as ss WHERE s.class_id = ss.class_id and ss.study_isreading = '1') as class_fullnums,
                (SELECT COUNT(t.student_id) FROM smc_student_study as t WHERE t.class_id = s.class_id and t.study_isreading = '1' and t.school_id = s.school_id) as readallnum,
                (SELECT COUNT(h.hour_id) FROM smc_class_hour as h WHERE h.class_id = s.class_id and hour_ischecking > '-1') as planhournum,
                (SELECT COUNT(h.hour_id) FROM smc_class_hour as h WHERE h.class_id = s.class_id and h.hour_iswarming = '0' and h.hour_ischecking = '1') as uphournum 
                FROM smc_student_study as s 
                LEFT JOIN smc_class as c ON (s.class_id = c.class_id ) 
                LEFT JOIN smc_course as u ON c.course_id = u.course_id 
                WHERE
                 {$datawhere}
                and s.company_id = '{$paramArray['company_id']}'
                and c.school_id = '{$paramArray['school_id']}'
                and s.student_id = '{$paramArray['student_id']}'
                and s.study_isreading = '1'
                -- and c.class_type = '0'
                GROUP BY s.class_id ORDER BY c.class_status DESC
                Limit {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);

        $class_status = array("待开班", "进行中", "已结束", "已删除");
        if ($dataList) {
            foreach ($dataList as &$dataVar) {
                //教师
                $teacherlist = $this->DataControl->selectClear("select s.staffer_cnname
                        from smc_class_hour_teaching as c
                        left join smc_staffer as s ON s.staffer_id = c.staffer_id 
                        where c.class_id = '{$dataVar['class_id']}' GROUP BY s.staffer_id");
                $teacherstr = '';
                if ($teacherlist) {
                    $teacherArray = array();
                    foreach ($teacherlist as $teacherlistVar) {
                        $teacherArray[] = $teacherlistVar['staffer_cnname'];
                    }
                    $teacherstr = implode(",", $teacherArray);
                }
                $dataVar['teachername'] = $teacherstr;

                if ($dataVar['class_type'] == '0') {
                    $dataVar['class_type'] = '父班';
                } elseif ($dataVar['class_type'] == '1') {
                    $dataVar['class_type'] = '子班';
                }
                $dataVar['class_status'] = $class_status[$dataVar['class_status']];
            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.class_id)
            FROM smc_student_study as s 
            LEFT JOIN smc_class as c ON (s.class_id = c.class_id ) 
            LEFT JOIN smc_course as u ON c.course_id = u.course_id 
            WHERE
             {$datawhere}
            and s.company_id = '{$paramArray['company_id']}' 
            and s.school_id = '{$paramArray['school_id']}' 
            and s.student_id = '{$paramArray['student_id']}' 
            and s.study_isreading = '1' 
            -- and c.class_type = '0'");
        $allnums = $all_num[0][0];


        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $dataList;

        return $data;
    }

    //我的班级课程别 --- 97--20200303
    function myClassCourseList($paramArray)
    {
        $datawhere = "  ";
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id = '{$paramArray['course_id']}'";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_branch like '%{$paramArray['keyword']}%' or c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%')";
        }

        $sql = "SELECT c.course_id,u.course_branch,u.course_cnname
                FROM smc_student_study as s 
                LEFT JOIN smc_class as c ON (s.class_id = c.class_id ) 
                LEFT JOIN smc_course as u ON c.course_id = u.course_id 
                WHERE s.company_id = '{$paramArray['company_id']}'
                and s.student_id = '{$paramArray['student_id']}' 
                and c.school_id = '{$paramArray['school_id']}'
                and s.study_isreading = '1'
                and c.course_id != '' and c.class_type = '0'  
                {$datawhere}
                GROUP BY c.course_id ";
        $dataList = $this->DataControl->selectClear($sql);

        if ($dataList) {
            $all_num = count($dataList);
        } else {
            $all_num = '0';
        }
        $allnums = $all_num;

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $dataList;

        return $data;
    }

    //我的班级 --- 97--20200303
    function myClassHourList($paramArray)
    {

        $datawhere = " 1 ";
        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] !== "") {
            $datawhere .= " and h.hour_way = '{$paramArray['hour_way']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT h.class_id,h.hour_id,h.hour_way,h.hour_name,h.hour_ischecking,h.hour_number,h.classroom_id,h.hour_day,h.hour_starttime,h.hour_endtime,
                (SELECT n.classroom_cnname FROM smc_classroom as n WHERE n.classroom_id = h.classroom_id) as classroom_cnname,
                (SELECT GROUP_CONCAT(s.staffer_cnname) FROM smc_class_hour_teaching AS t LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id WHERE t.hour_id = h.hour_id AND t.class_id = h.class_id AND t.teaching_type = '0' ORDER BY t.teaching_type DESC) AS staffer_cnname
                FROM smc_class_hour as h LEFT JOIN smc_student_study AS s ON h.class_id = s.class_id 
	AND s.student_id = '{$paramArray['student_id']}' 
                WHERE {$datawhere} and h.class_id = '{$paramArray['class_id']}' and h.hour_ischecking > '-1' AND h.hour_day >= s.study_beginday 
                order by h.hour_day ASC
                Limit {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);

        if ($dataList) {
            foreach ($dataList as &$dataVar) {

                $a = $this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "student_id = '{$paramArray['student_id']}' and hour_id = '{$dataVar['hour_id']}' and hourstudy_checkin = '1'");

                if ($a) {
                    $dataVar['status'] = '1';
                } else {
                    $dataVar['status'] = '0';
                }


                if ($dataVar['hour_ischecking'] == '1') {
                    $dataVar['hour_ischecking'] = '已上课';
                } else {
                    $dataVar['hour_ischecking'] = '未上课';
                }

                if ($dataVar['hour_way'] == '0') {
                    $dataVar['hour_way'] = '实体课';
                } elseif ($dataVar['hour_way'] == '1') {
                    $dataVar['hour_way'] = '线上课';
                } else {
                    $dataVar['hour_way'] = '未知';
                }


                //教师对学员点评
                $sturemark = $this->DataControl->selectClear("SELECT t.* 
                          from eas_classhour_sturemark as s 
                          LEFT JOIN eas_classhour_sturemarkstar as t ON s.sturemark_id = t.sturemark_id
                          WHERE s.class_id = '{$dataVar['class_id']}' and s.hour_id = '{$dataVar['hour_id']}' and s.student_id = '{$paramArray['student_id']}' ");
                if ($sturemark) {
                    $allnum = count($sturemark);
                    $allscore = '0';
                    foreach ($sturemark as $sturemarkvar) {
                        $allscore += $sturemarkvar['sturemarkstar_score'];
                    }
                    $dataVar['comment'] = sprintf("%.1f", $allscore / $allnum);
                } else {
                    $dataVar['comment'] = '0';
                }
                //学员对教师的评价
                $hourcomment = $this->DataControl->selectClear("SELECT s.hourcomment_score,s.hourcomment_level
                          from eas_student_hourcomment as s 
                          WHERE s.hour_id = '{$dataVar['hour_id']}' and s.student_id = '{$paramArray['student_id']}' ");
                if ($hourcomment) {
                    $allnum = count($hourcomment);
                    $allscore = '0';
                    foreach ($hourcomment as $hourcommentvar) {
                        $allscore += $hourcommentvar['hourcomment_score'];
                    }
                    $dataVar['evaluate'] = sprintf("%.1f", $allscore / $allnum);
                    $status = $this->LgArraySwitch(array("0" => "非常满意", "1" => "一般", "2" => "不满意"));
                    $dataVar['hourcomment_level'] = $status[$hourcomment[0]['hourcomment_level']];
                } else {
                    $dataVar['evaluate'] = '0';
                    $dataVar['hourcomment_level'] = '';
                }
//            $dataVar['comment'] = '点评';
//            $dataVar['evaluate'] = '评价';


                if ($dataVar['comment'] == '' && $dataVar['evaluate'] == '') {
                    $dataVar['isevaluate'] = '1';
                } else {
                    $dataVar['isevaluate'] = '0';
                }

            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(h.hour_id)
            FROM smc_class_hour as h
            WHERE {$datawhere} and h.class_id = '{$paramArray['class_id']}' and h.hour_ischecking > '-1'");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $dataList;

        return $data;
    }


    //我的班级 --- 97--20200303
    function myClassTeacherList($paramArray)
    {

        $datawhere = " 1 ";

        $sql = "SELECT
            s.staffer_cnname,
            s.staffer_enname,
            s.staffer_mobile,
            s.staffer_sex,
            te.teachtype_name,
            teaching_type
        FROM
            smc_class_hour_teaching AS t left join smc_staffer as s on t.staffer_id = s.staffer_id
            left join smc_code_teachtype as te on te.teachtype_code = t.teachtype_code
        WHERE
             t.class_id = '{$paramArray['class_id']}'
             group by t.staffer_id ";
        $dataList = $this->DataControl->selectClear($sql);

        if ($dataList) {
            foreach ($dataList as &$dataVar) {
                $dataVar['staffer_mobile'] = substr($dataVar['staffer_mobile'], 0, 3) . '****' . substr($dataVar['staffer_mobile'], 7);
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(t.staffer_id)
            FROM smc_class_teach as t 
            LEFT JOIN smc_staffer as s ON s.staffer_id = t.staffer_id 
            LEFT JOIN smc_code_teachtype as p ON p.teachtype_code = t.teachtype_code 
            WHERE {$datawhere} and t.class_id = '{$paramArray['class_id']}' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $dataList;

        return $data;
    }

    function getStuClass($request)
    {
        $sql = "SELECT A.student_id,A.class_id
            ,(CASE WHEN C.course_issupervise = '1' AND E.coursebalance_issupervise = '1' THEN 0 ELSE 1 END) as class_type
            ,B.class_cnname,B.class_enname,B.class_branch
            ,C.course_id,C.course_cnname,C.course_branch
            ,(SELECT Y.staffer_cnname FROM smc_class_hour_teaching AS X, smc_staffer AS Y
                WHERE X.staffer_id = Y.staffer_id AND X.class_id = A.class_id AND teaching_type = 0 LIMIT 0,1) AS class_teacher
            ,(SELECT Y.staffer_native FROM smc_class_hour_teaching AS X, smc_staffer AS Y
                WHERE X.staffer_id = Y.staffer_id AND X.class_id = A.class_id AND teaching_type = 0 LIMIT 0,1) AS staffer_native
            ,(SELECT Y.staffer_img FROM smc_class_hour_teaching AS X,smc_staffer AS Y 
                WHERE X.staffer_id = Y.staffer_id AND X.class_id = A.class_id AND teaching_type = 0 LIMIT 0,1) AS staffer_img
            ,(select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=B.class_id and Y.hour_ischecking>=0 and Y.hour_iswarming=0) as class_allnum 
            ,(select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=B.class_id and Y.hour_ischecking>0 and Y.hour_iswarming=0) as renum 
            
            -- ,(select coursebalance_time from smc_student_coursebalance X where X.course_id=B.course_id AND X.student_id=A.student_id AND X.school_id=A.school_id) AS coursebalance_time
            -- ,(select count(X.log_id) from smc_student_coursebalance_log X where X.course_id=B.course_id AND X.student_id=A.student_id AND X.school_id=A.school_id and X.hourstudy_id>0) AS renum 
            FROM smc_student_study A 
            LEFT JOIN smc_class B ON A.class_id=B.class_id 
            LEFT JOIN smc_course C ON B.course_id=C.course_id
            LEFT JOIN smc_code_coursetype D ON C.coursetype_id=D.coursetype_id	
            LEFT JOIN smc_student_coursebalance E ON E.school_id=A.school_id AND E.student_id=A.student_id AND E.course_id = B.course_id 
            WHERE A.company_id='{$request['company_id']}'
            AND A.school_id='{$request['school_id']}'
            AND A.student_id='{$request['student_id']}'
            AND B.class_status = '1'
            AND A.study_isreading = '1'
            --  and ((C.course_issupervise = '1' AND E.coursebalance_issupervise = '1') OR c.course_inclasstype=2)
            ORDER BY class_type,B.class_status DESC,A.study_endday desc";

        $classList = $this->DataControl->selectClear($sql);

        if (!$classList) {
//            $this->error = true;
//            $this->errortip = "无数据";
            return array('error' => 1, 'errortip' => "无数据", 'result' => $classList);
        } else {
            foreach ($classList as &$val) {
                $staffer = $this->DataControl->selectClear("
                SELECT s.staffer_cnname,s.staffer_img 
                FROM smc_class_hour_teaching AS t
                LEFT JOIN smc_staffer AS s ON t.staffer_id = s.staffer_id 
                WHERE t.class_id = '{$val['class_id']}'
                GROUP BY s.staffer_id");
                if($staffer){
                    $val['staffer'] = $staffer;
                }else{
                    $val['staffer'] = array();
                }
                $val['renum'] = $val['renum'];
                $val['allnum'] = $val['class_allnum'];
                if($val['staffer_native'] == '1'){
                    $val['class_teacher'] = '--';
                }
            }
        }
        $data = array();
        $data['list'] = $classList ? $classList : array();

        $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $data);
        return $res;
    }

    function getStuTimeTableJxt($request)
    {

        $datawhere = " 1 ";

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and ss.company_id='{$request['company_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

        if (isset($request['hour_id']) && $request['hour_id'] !== '') {
            $datawhere .= " and ch.hour_id = '{$request['hour_id']}'";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and ch.class_id = '{$request['class_id']}'";
        }
        $havingwhere = '1';
        if ($request['status'] == '0') {
            $datawhere .= " and ch.hour_ischecking = '1' and sc.coursebalance_issupervise = '1' and co.course_issupervise = '1' ";
            $havingwhere .= " and income_isconfirm = '0'";
        }
        if($request['type'] == '1'){
            //我的约课
            $havingwhere .= " and class_type = 1 and booking_status <> 0 ";
        }else{
            $havingwhere .= " and class_type = 0 or booking_status = 0 ";
        }


        $sql = "select ch.hour_id,ch.hour_starttime,ch.hour_endtime,ch.hour_name
            ,co.course_branch,cl.classroom_branch,cl.classroom_cnname
            ,(CASE WHEN co.course_inclasstype=2 THEN 1 ELSE 0 END) as class_type
            ,ch.hour_way,ch.hour_ischecking,c.class_cnname,co.course_cnname
            ,s.staffer_cnname,ch.hour_day,hour_noon,c.class_enname
            ,sc.coursebalance_unitearning,s.staffer_img,co.course_inclasstype
            ,(SELECT group_concat( DISTINCT concat(staffer_cnname,(CASE WHEN ifnull(Y.staffer_enname,'')='' THEN '' ELSE concat('-', Y.staffer_enname )END))) 
                FROM smc_class_hour_teaching AS X,smc_staffer AS Y WHERE X.staffer_id = Y.staffer_id AND X.class_id = ch.class_id AND X.teaching_isdel = 0 AND teaching_type = 0) AS class_teacher
            ,(select coursebalance_time from smc_student_coursebalance X where X.course_id=c.course_id AND X.student_id=ss.student_id AND X.school_id=c.school_id) AS coursebalance_time
            ,(select count(X.log_id) from smc_student_coursebalance_log X where X.course_id=c.course_id AND X.student_id=ss.student_id AND X.school_id=c.school_id and X.hourstudy_id > 0) AS renumm
            ,ch.hour_lessontimes
            ,(select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=c.class_id and Y.hour_ischecking>=0 and Y.hour_iswarming=0) as class_allnum
            ,cc.companies_id,co.coursetype_id,co.coursecat_id,co.course_id,ch.class_id,sh.hourstudy_checkin
            ,sh.hourstudy_id
            ,IFNULL(er.income_isconfirm,0) as income_isconfirm
            ,IFNULL(K.booking_status,-1) as booking_status
            ,c.class_appointnum,ifnull(K.booking_id,0) AS booking_id
            ,(select count(booking_id) from smc_class_booking where class_id=ch.class_id and hour_id=ch.hour_id and booking_status=0) as booking_num
            ,UNIX_TIMESTAMP(concat(hour_day,' ',hour_starttime)) as hour_time
            ,(select ccd.teachtype_code from smc_code_teachtype as ccd where ccd.teachtype_code=cht.teachtype_code limit 0,1) as teachtype_codeccd 
            from smc_class_hour as ch 
            left join smc_class as c on c.class_id=ch.class_id
            left join smc_course as co on co.course_id=c.course_id	
            left join smc_school_coursecat_subject as cs on cs.coursecat_id = co.coursecat_id and cs.school_id =c.school_id
            left join gmc_code_companies as cc on cc.companies_id = cs.companies_id 
            left join smc_student_study as ss on ss.class_id=c.class_id 
            left join smc_classroom as cl on cl.classroom_id=ch.classroom_id 
            left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type='0' and cht.teaching_isdel='0' 
            left join smc_staffer as s on s.staffer_id=cht.staffer_id 
            left join smc_student_coursebalance as sc on sc.school_id=ss.school_id and sc.student_id=ss.student_id and sc.course_id = ch.course_id 
            LEFT JOIN smc_student_hourstudy AS sh ON sh.hour_id = ch.hour_id and sh.student_id=ss.student_id
            left join cmb_trans_transfer as er on er.hourstudy_id = sh.hourstudy_id 
            LEFT JOIN smc_class_booking K ON K.hour_id = ch.hour_id and K.student_id=ss.student_id and K.booking_status=0
            where {$datawhere} 
            and ss.school_id='{$request['school_id']}'
            and ss.student_id='{$request['student_id']}'
            and ch.hour_day>=ss.study_beginday 
            and ch.hour_day<=ss.study_endday
            AND ch.hour_ischecking > '-1'
            AND ch.hour_isfree = '0'
            and (( not exists (select 1 from smc_class_hour as x where x.hour_id=ch.hour_id and x.hour_ischecking=1 
                    and (select y.hourstudy_id from smc_student_hourstudy as y where y.hour_id=x.hour_id and y.student_id=ss.student_id ) is null) 
                    and (co.course_inclasstype<>2 or ch.hour_day<=DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-13 DAY))) 
                or (co.course_inclasstype=2 and (ch.hour_ischecking=0 or k.booking_id>0) and ch.hour_day<=DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-13 DAY)))       
            group by ch.hour_id 
            HAVING {$havingwhere} and (class_type=0 or hour_time>UNIX_TIMESTAMP(now()) or booking_id>0)
            order by hour_day ASC,ch.hour_starttime ASC
            ";

        $hourList = $this->DataControl->selectClear($sql);

        if ($hourList) {
            foreach ($hourList as &$val) {
                if($val['teachtype_codeccd'] == '05EM'){
                    $val['class_teacher'] = '--';
                }
                if ($val['class_type'] == '0') {
                    if ($val['hour_ischecking'] < '1') {
                        $val['status'] = '未上课';
                    } else {
                        if ($val['income_isconfirm'] == '1') {
                            $val['status'] = '已确认';
                        } else {
//                            $val['status'] = '待确认';
                            $val['status'] = '已上课';
                        }
                    }
                    $val['allnum'] = $val['coursebalance_time'] + $val['renumm'];
                    $val['renum'] = $val['hour_lessontimes'] - ($val['class_allnum'] - $val['allnum']);
                } else {
                    if ($val['hour_ischecking'] == '1') {
                        if ($val['hourstudy_checkin'] == '1') {
                            $val['status'] = '已上课';
                        } else {
                            $val['status'] = '缺勤';
                        }
                    } else {
                        if ($val['booking_status'] == '0') {
                            $val['status'] = '已预约';
                            if ($val['hour_time'] <= (time() + 86400)) {
                                $val['tips'] = '上课前24小时内不可取消预约';
                            }
                        } else {
                            if ($val['class_appointnum'] > 0 && $val['booking_num'] >= $val['class_appointnum']) {
                                $val['status'] = '已约满';
                            } else {
                                $val['status'] = '待预约';
                            }
                        }
                    }
                    $val['allnum'] = $val['class_allnum'];
                    $val['renum'] = $val['hour_lessontimes'];
                    $val['showtime'] = $val['hour_starttime'];
                }
                if ($val['class_appointnum'] > 0) {
                    if ($val['booking_num'] > $val['class_appointnum']) {
                        $val['booking_state'] = $val['booking_num'] . '/' . $val['booking_num'];
                    } else {
                        $val['booking_state'] = $val['booking_num'] . '/' . $val['class_appointnum'];
                    }
                } else {
                    $val['booking_state'] = '';
                }

                if($val['class_type'] == '1'){
                    $val['hour_day'] = $val['hour_day'].' '.$val['hour_starttime'];
                }
            }
        }
        $data = array();
        $data['list'] = $hourList;

        if (!$hourList) {
            $data['list'] = array();
            if ($request['status'] == '0') {
                $res = array('error' => 1, 'errortip' => "您还没有课程，好好放松一下吧～", 'result' => $data);
            } else {
                $res = array('error' => 1, 'errortip' => "暂无您可以预约的课程哦~", 'result' => $data);
            }
        } else {
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $data);
        }
        return $res;
    }

    function getStuTimeTable($request)
    {

        $datawhere = " 1 ";

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and ss.company_id='{$request['company_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

        if (isset($request['hour_id']) && $request['hour_id'] !== '') {
            $datawhere .= " and ch.hour_id = '{$request['hour_id']}'";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and ch.class_id = '{$request['class_id']}'";
        }
        $havingwhere = '1';
        if ($request['status'] == '0') {
            $datawhere .= " and ch.hour_ischecking = '1' and sc.coursebalance_issupervise = '1' and co.course_issupervise = '1' ";
            $havingwhere .= " and income_isconfirm = '0'";
        }
        if($request['type'] == '1'){
            //我的约课
            $havingwhere .= " and class_type =1 ";
        }


        $sql = "select ch.hour_id,ch.hour_starttime,ch.hour_endtime,ch.hour_name
            ,co.course_branch,cl.classroom_branch,cl.classroom_cnname
            ,(CASE WHEN co.course_inclasstype=2 THEN 1 ELSE 0 END) as class_type 
            ,ch.hour_way,ch.hour_ischecking,c.class_cnname,co.course_cnname
            ,s.staffer_cnname,ch.hour_day,hour_noon,c.class_enname
            ,sc.coursebalance_unitearning,s.staffer_img,co.course_inclasstype
            ,(SELECT group_concat( DISTINCT concat(staffer_cnname,(CASE WHEN ifnull(Y.staffer_enname,'')='' THEN '' ELSE concat('-', Y.staffer_enname )END))) 
                FROM smc_class_hour_teaching AS X,smc_staffer AS Y WHERE X.staffer_id = Y.staffer_id AND X.class_id = ch.class_id AND X.teaching_isdel = 0 AND teaching_type = 0) AS class_teacher
            ,(select coursebalance_time from smc_student_coursebalance X where X.course_id=c.course_id AND X.student_id=ss.student_id AND X.school_id=c.school_id) AS coursebalance_time
            ,(select count(X.log_id) from smc_student_coursebalance_log X where X.course_id=c.course_id AND X.student_id=ss.student_id AND X.school_id=c.school_id and X.hourstudy_id > 0) AS renumm
            ,ch.hour_lessontimes
            ,(select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=c.class_id and Y.hour_ischecking>=0 and Y.hour_iswarming=0) as class_allnum
            ,cc.companies_id,co.coursetype_id,co.coursecat_id,co.course_id,ch.class_id,sh.hourstudy_checkin
            ,sh.hourstudy_id
            ,IFNULL(er.income_isconfirm,0) as income_isconfirm
            ,IFNULL(K.booking_status,-1) as booking_status
            ,c.class_appointnum,ifnull(K.booking_id,0) AS booking_id
            ,(select count(booking_id) from smc_class_booking where class_id=ch.class_id and hour_id=ch.hour_id and booking_status=0) as booking_num
            ,UNIX_TIMESTAMP(concat(hour_day,' ',hour_starttime)) as hour_time
            from smc_class_hour as ch 
            left join smc_class as c on c.class_id=ch.class_id
            left join smc_course as co on co.course_id=c.course_id	
            left join smc_school_coursecat_subject as cs on cs.coursecat_id = co.coursecat_id and cs.school_id =c.school_id
            left join gmc_code_companies as cc on cc.companies_id = cs.companies_id 
            left join smc_student_study as ss on ss.class_id=c.class_id 
            left join smc_classroom as cl on cl.classroom_id=ch.classroom_id 
            left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type='0' and cht.teaching_isdel='0' 
            left join smc_staffer as s on s.staffer_id=cht.staffer_id 
            left join smc_student_coursebalance as sc on sc.school_id=ss.school_id and sc.student_id=ss.student_id and sc.course_id = ch.course_id 
            LEFT JOIN smc_student_hourstudy AS sh ON sh.hour_id = ch.hour_id and sh.student_id=ss.student_id
            left join cmb_trans_transfer as er on er.hourstudy_id = sh.hourstudy_id 
            LEFT JOIN smc_class_booking K ON K.hour_id = ch.hour_id and K.student_id=ss.student_id and K.booking_status=0
            where {$datawhere} 
            and ss.school_id='{$request['school_id']}'
            and ss.student_id='{$request['student_id']}'
            and ch.hour_day>=ss.study_beginday 
            and ch.hour_day<=ss.study_endday
            AND ch.hour_ischecking > '-1'
            AND ch.hour_isfree = '0'
            and ((sc.coursebalance_issupervise = '1' and co.course_issupervise = '1' 
                and not exists (select 1 from smc_class_hour as x where x.hour_id=ch.hour_id and x.hour_ischecking=1 
                    and (select y.hourstudy_id from smc_student_hourstudy as y where y.hour_id=x.hour_id and y.student_id=ss.student_id ) is null) 
                    and (co.course_inclasstype<>2 or ch.hour_day<=DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-13 DAY))) 
                or (co.course_inclasstype=2 and (ch.hour_ischecking=0 or k.booking_id>0) and ch.hour_day<=DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-13 DAY)))       
            group by ch.hour_id 
            HAVING {$havingwhere} and (class_type=0 or hour_time>UNIX_TIMESTAMP(now()) or booking_id>0)
            order by hour_day ASC,ch.hour_starttime ASC
            ";

        $hourList = $this->DataControl->selectClear($sql);

        if ($hourList) {
            foreach ($hourList as &$val) {
                if ($val['class_type'] == '0') {
                    if ($val['hour_ischecking'] < '1') {
                        $val['status'] = '未上课';
                    } else {
                        if ($val['income_isconfirm'] == '1') {
                            $val['status'] = '已确认';
                        } else {
                            $val['status'] = '待确认';
                        }
                    }
                    $val['allnum'] = $val['coursebalance_time'] + $val['renumm'];
                    $val['renum'] = $val['hour_lessontimes'] - ($val['class_allnum'] - $val['allnum']);
                } else {
                    if ($val['hour_ischecking'] == '1') {
                        if ($val['hourstudy_checkin'] == '1') {
                            $val['status'] = '已上课';
                        } else {
                            $val['status'] = '缺勤';
                        }
                    } else {
                        if ($val['booking_status'] == '0') {
                            $val['status'] = '已预约';
                            if ($val['hour_time'] <= (time() + 86400)) {
                                $val['tips'] = '上课前24小时内不可取消预约';
                            }
                        } else {
                            if ($val['class_appointnum'] > 0 && $val['booking_num'] >= $val['class_appointnum']) {
                                $val['status'] = '已约满';
                            } else {
                                $val['status'] = '待预约';
                            }
                        }
                    }
                    $val['allnum'] = $val['class_allnum'];
                    $val['renum'] = $val['hour_lessontimes'];
                    $val['showtime'] = $val['hour_starttime'];
                }
                if ($val['class_appointnum'] > 0) {
                    if ($val['booking_num'] > $val['class_appointnum']) {
                        $val['booking_state'] = $val['booking_num'] . '/' . $val['booking_num'];
                    } else {
                        $val['booking_state'] = $val['booking_num'] . '/' . $val['class_appointnum'];
                    }
                } else {
                    $val['booking_state'] = '';
                }
            }
        }
        $data = array();
        $data['list'] = $hourList;

        if (!$hourList) {
            $data['list'] = array();
            if ($request['status'] == '0') {
                $res = array('error' => 1, 'errortip' => "您还没有课程，好好放松一下吧～", 'result' => $data);
            } else {
                $res = array('error' => 1, 'errortip' => "本周没课，好好放松一下吧~", 'result' => $data);
            }
        } else {
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $data);
        }
        return $res;
    }

    function addBooking($request)
    {
        $data = array();
        $data['hour_id'] = $request['hour_id'];
        $data['class_id'] = $request['class_id'];
        $data['student_id'] = $request['student_id'];
        $data['booking_createtime'] = time();
        if ($this->DataControl->getOne('smc_class_booking', "hour_id='{$request['hour_id']}' and  student_id='{$request['student_id']}' and booking_status =0 ")) {
            $this->errortip = "该学员已经预约过该课时";
            $this->error = 1;
            return false;
        }

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_ischecking,UNIX_TIMESTAMP(concat(hour_day,' ',hour_starttime)) as hour_time", "hour_id='{$request['hour_id']}'");
        if ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "已上课，不可预约";
            return false;
        } else if ($hourOne['hour_time'] <= time()) {
            $this->error = 1;
            $this->errortip = "已过期，不可预约";
            return false;
        }

        $sql = "select a.class_appointnum
                ,(select count(booking_id) from smc_class_booking where class_id=a.class_id and hour_id='{$request['hour_id']}'and booking_status =0) as booking_num
                from smc_class a
                where a.class_id='{$request['class_id']}' 
                and a.class_status>=0";
        $hourOne = $this->DataControl->selectOne($sql);
        if ($hourOne && $hourOne['class_appointnum'] > 0 && $hourOne['booking_num'] >= $hourOne['class_appointnum']) {
            $this->error = 1;
            $this->errortip = "该课时学员已约满，请预约其他课时";
            return false;
        }

        if ($this->DataControl->insertData("smc_class_booking", $data)) {
            $this->errortip = "预约成功，请按时到校上课";
            $this->error = 0;
            return true;
        } else {
            $this->errortip = "新增失败";
            $this->error = 1;
            return false;
        }
    }

    function cancelBooking($request)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_ischecking,UNIX_TIMESTAMP(concat(hour_day,' ',hour_starttime)) as hour_time", "hour_id='{$request['hour_id']}'");
        if ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "已上课，无法取消预约";
            return false;
        } else if ($hourOne['hour_time'] <= (time() + 86400)) {
            $this->error = 1;
            $this->errortip = "上课前24小时内不可取消预约";
            return false;
        }

        $sql = "select booking_id from smc_class_booking x where student_id = '{$request['student_id']}' 
                                             and booking_status = -1 and booking_updatatime>UNIX_TIMESTAMP(date_sub(now(),interval 30 day)) ";
        $list = $this->DataControl->selectClear($sql);
        if ($list) {
            $this->error = 1;
            $this->errortip = "您本月已取消过预约，暂无权限取消预约";
            return false;
        }

        $booking = $this->DataControl->getOne('smc_class_booking', "class_id='{$request['class_id']}' and  hour_id ='{$request['hour_id']}' and  student_id ='{$request['student_id']}' and booking_status = 0");
        if ($booking) {
            $booking_data = array();
            $booking_data['booking_status'] = '-1';
            $booking_data['booking_updatatime'] = time();
            if ($this->DataControl->updateData('smc_class_booking', "booking_id='{$booking['booking_id']}'", $booking_data)) {
                return true;
            } else {
                return false;
            }
        }
        $this->error = 1;
        $this->errortip = "预约取消失败";
        return false;
    }

    function getStuTimeTableDetail($request)
    {
        $startTime = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y")));
        $endTime = date("Y-m-d", mktime(0, 0, 0, date("m"), date("d") - date("w") + 7, date("Y")));

        $datawhere = " 1 ";

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and ss.company_id='{$request['company_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        } else {
            $datawhere .= " and ch.hour_day >= '{$startTime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        } else {
            $datawhere .= " and ch.hour_day <= '{$endTime}'";
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

        if (isset($request['hour_id']) && $request['hour_id'] !== '') {
            $datawhere .= " and ch.hour_id = '{$request['hour_id']}'";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and ch.class_id = '{$request['class_id']}'";
        }
        $havingwhere = '1';
        if ($request['status'] == '0') {
            $datawhere .= " and ch.hour_ischecking = '1'";
            $havingwhere .= " and income_isconfirm = '0'";
        }
        if($request['type'] == '1'){
            //我的约课
            $havingwhere .= " and class_type =1 ";
        }

        $sql = "select ch.hour_id,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_cnname,ch.hour_way
        ,ch.hour_ischecking,c.class_cnname,co.course_cnname,s.staffer_cnname,ch.hour_day,hour_noon,c.class_enname,sc.coursebalance_unitearning,s.staffer_img
        ,(CASE WHEN co.course_inclasstype=2 THEN 1 ELSE 0 END) as class_type
        ,(SELECT group_concat(DISTINCT concat(staffer_cnname,(CASE WHEN ifnull( Y.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', Y.staffer_enname ) END ))) 
        FROM smc_class_hour_teaching AS X, smc_staffer AS Y WHERE X.staffer_id = Y.staffer_id AND X.class_id = ch.class_id AND X.teaching_isdel = 0 AND teaching_type = 0) AS class_teacher			
        ,ch.hour_lessontimes
		,(select hourstudy_checkin from smc_student_hourstudy as sh where sh.student_id = '{$request['student_id']}' and sh.hour_id = ch.hour_id) as hourstudy_checkin
		,(select hourstudy_id from smc_student_hourstudy as sh where sh.student_id = '{$request['student_id']}' and sh.hour_id = ch.hour_id) as hourstudy_id
		,cc.companies_id,co.coursetype_id,co.coursecat_id,co.course_id,ch.class_id,IFNULL(er.income_isconfirm,0) as income_isconfirm
            ,(select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=c.class_id and Y.hour_ischecking>=0 and Y.hour_iswarming=0) as class_allnum
            ,(select coursebalance_time from smc_student_coursebalance X where X.course_id=c.course_id AND X.student_id=ss.student_id AND X.school_id=c.school_id) AS coursebalance_time
            ,(select count(X.log_id) from smc_student_coursebalance_log X where X.course_id=c.course_id AND X.student_id=ss.student_id AND X.school_id=c.school_id and X.hourstudy_id > 0) AS renumm
            ,IFNULL(K.booking_status,-1) as booking_status
            ,c.class_appointnum,ifnull(K.booking_id,0) AS booking_id
            ,(select count(booking_id) from smc_class_booking where class_id=ch.class_id and hour_id=ch.hour_id and booking_status=0) as booking_num
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_school_coursecat_subject as cs on cs.coursecat_id = co.coursecat_id and cs.school_id = '{$request['school_id']}'
		      left join gmc_code_companies as cc on cc.companies_id = cs.companies_id
              left join smc_student_study as ss on ss.class_id=c.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type='0' and cht.teaching_isdel='0'
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              left join smc_student_coursebalance as sc on sc.school_id = '{$request['school_id']}' and sc.student_id = '{$request['student_id']}' and sc.course_id = ch.course_id
              LEFT JOIN smc_student_hourstudy AS sh ON sh.hour_id = ch.hour_id and sh.student_id = '{$request['student_id']}'
		      left join cmb_trans_transfer as er on er.hourstudy_id = sh.hourstudy_id
		      LEFT JOIN smc_class_booking K ON K.hour_id = ch.hour_id and K.student_id=ss.student_id and K.booking_status=0
              where {$datawhere} 
              and ss.school_id='{$request['school_id']}'  
              and ss.student_id='{$request['student_id']}'
		      AND ch.hour_isfree = '0'
              and ss.study_isreading=1 and ch.hour_day>=ss.study_beginday and ch.hour_day<=ss.study_endday
              and ((sc.coursebalance_issupervise = '1' and co.course_issupervise = '1' 
                and not exists (select 1 from smc_class_hour as x where x.hour_id=ch.hour_id and x.hour_ischecking=1 
                    and (select y.hourstudy_id from smc_student_hourstudy as y where y.hour_id=x.hour_id and y.student_id=ss.student_id ) is null) 
                    and (co.course_inclasstype<>2 or ch.hour_day<=DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-13 DAY))) 
                or (co.course_inclasstype=2 and (ch.hour_ischecking=0 or k.booking_id>0) and ch.hour_day<=DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-13 DAY)))    
              group by ch.hour_id
              HAVING {$havingwhere}
              order by hour_day ASC,ch.hour_starttime ASC
        ";

        $hourList = $this->DataControl->selectClear($sql);

        if ($hourList) {
            foreach ($hourList as &$val) {
                if ($val['class_type'] == '0') {
                    if ($val['hour_ischecking'] < '1') {
                        $val['status'] = '0';
                    } else {
                        if ($val['income_isconfirm'] == '1') {
                            $val['status'] = '2';
                        } else {
                            $val['status'] = '1';
                        }
                    }
                    $val['allnum'] = $val['coursebalance_time'] + $val['renumm'];
                    $val['renum'] = $val['hour_lessontimes'] - ($val['class_allnum'] - $val['allnum']);
                } else {
                    $val['status'] = '-1';
                    $val['allnum'] = $val['class_allnum'];
                    $val['renum'] = $val['hour_lessontimes'];
                    $val['showtime'] = $val['hour_starttime'];
                }
            }
        }

        $data = array();
        $data['list'] = $hourList;

        if (!$hourList) {
            $data['list'] = array();
            $res = array('error' => 1, 'errortip' => "本周没课，好好放松一下吧~", 'result' => $data);
        } else {
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $data);
        }

        return $res;
    }

    function getTitle($request)
    {
        $sql = "SELECT 
                    (select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=A.class_id and Y.hour_ischecking=1 and Y.hour_iswarming = 0) as renum,
                    (select count(Y.hour_id) from smc_class_hour as Y where Y.class_id=A.class_id and Y.hour_ischecking>=0 and Y.hour_iswarming = 0) as allnum
                FROM smc_student_study A 
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                WHERE A.company_id='{$request['company_id']}'
                AND A.school_id='{$request['school_id']}'
                AND A.student_id='{$request['student_id']}'
                AND B.class_status>'-2'
                ORDER BY B.class_status DESC,a.study_endday desc";
        $classList = $this->DataControl->selectClear($sql);

        $renum = '0';
        $allnum = '0';

        if ($classList) {
            foreach ($classList as &$val) {
                $renum += $val['renum'];
                $allnum += $val['allnum'];
            }
        }

        $data = array();
        $data['renum'] = $renum;
        $data['allnum'] = $allnum;
        return $data;
    }

    function getIp($request)
    {
        $hourstudy = $this->DataControl->getFieldOne("cmb_trans_transfer", "confirm_createtime,confirm_ip", "hourstudy_id = '{$request['hourstudy_id']}'");


        $data = array();

        $data['status'] = '1';
        $data['time'] = date('Y年m月d H:i:s', $hourstudy['confirm_createtime']);
        $data['ip'] = $hourstudy['confirm_ip'];
        return $data;
    }

    function setPushInfo($request)
    {

        $tempList = json_decode(stripslashes($request['tempStr']), 1);

        if ($tempList) {

            foreach ($tempList as $tempOne) {
                $data = array();
                $data['booking_status'] = '1';
                $this->DataControl->updateData("smc_parenter_wxchattoken", "parenter_wxtoken='{$tempOne['opentoken_string']}' and wxchatnumber_id='6'", $data);
            }
        } else {
            $this->error = true;
            $this->errortip = "无对应参数";
            return false;
        }

        return true;

    }

    function ComfirmCheck($openid, $keyword1, $keyword2, $keyword3, $sid, $pid, $hid)
    {
        $data = '{
  "touser": "' . $openid . '",
  "template_id": "-J-rlmgQojvqHv6wEHJWczM07oeWLY3k6m2GVudFlXI",
  "page": "pages/paddingSchedule/main?sId=' . $sid . '",
  "data": {
      "thing1": {
          "value": "' . $keyword1 . '"
      },
      "time3": {
          "value": "' . $keyword2 . '"
      },
      "thing4": {
          "value": "' . $keyword3 . '"
      }
  }
}';
        return $this->SendWeixinMis($data, "com", $sid, $pid, $hid);
    }

    function httpPost($data, $url)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            return curl_errno($ch);
        }
        curl_close($ch);
        return $tmpInfo;
    }

    function httpGet($url)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, 500);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);  // 视频教程中 这两个设为 true 我使用时会报错，改成 0 正常
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

        /*  为保证第三方服务器与微信服务器之间数据传输的安全性，所有微信接口采用https方式调用，必须使用上面2行代码打开ssl安全校验。
            如果在部署过程中代码在此处验证失败，请到 httpL//curl.haxx.se/ca/cacert.pem 下载新的证书判别文件。

            CURLOPT_SSL_VERIFYHOST 的值

            设为 0 表示 不检查证书
            设为 1 表示 检查证书中是否有CN(common name)字段
            设为 2 表示 在1的基础上校验当前的域名是否与CN匹配  */

        $tmpInfo = curl_exec($ch);
        curl_close($ch);
        return $tmpInfo;
    }


    function SendWeixinMis($data, $log_type = '', $student_id = '0', $parenter_id = '0', $hourstudy_id = '0')
    {
        $appid = 'wx45d70456847ac845';
        $secret = '7cf208b45cd418d073d7def4d6f63489';

        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$secret}";

        $result = $this->httpGet($url);


//        print($result . "\n");

        $arr = json_decode($result, true);
        $token = $arr['access_token'];

        $url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={$token}";

        $result = $this->httpPost($data, $url);

//        var_dump($result);die;

//        print($result . "\n");

        $json_play = new \Webjson();
        $retueninfo = $json_play->decode($result, "1");


        if ($retueninfo['errmsg'] == 'ok') {
            $date = array();
            $date['student_id'] = $student_id;
            $date['parenter_id'] = $parenter_id;
            $date['log_type'] = $log_type;
            $date['log_status'] = 1;
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);
            $data = array();
            $data['broadcast_createtime'] = time();
            $data['transfer_updatetime'] = time();
            $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id = '{$hourstudy_id}'", $data);
            return true;
        } else {
            $date = array();
            $date['student_id'] = $student_id;
            $date['parenter_id'] = $parenter_id;
            $date['log_type'] = $log_type;
            $date['log_status'] = 0;
            $date['log_errmsg'] = $result;
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);
            return false;
        }
    }


}