<?php

namespace Model\Scptc;

Class AppraiseModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
    }

    //学员端去登陆
    function LineThreeStuUrl($request)
    {
        $lineroomsOne = $this->DataControl->selectOne("select l.* from smc_linerooms as l where l.hour_id='{$request['hour_id']}'");
        if (!$lineroomsOne) {
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }
        $studentOne = $this->DataControl->selectOne("select t.student_cnname,t.student_enname from smc_student as t where t.student_id='{$request['student_id']}'  ");
        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "请选择学员";
            return false;
        }

        if ($lineroomsOne['linerooms_threenumber'] == '') {
            $LineClassModel = new \Model\Smc\LineClassModel($request);
            $threenumber = $LineClassModel->CreateLineThreenumber($lineroomsOne['linerooms_id']);
            if ($threenumber) {
                $lineroomsOne['linerooms_threenumber'] = $threenumber['threenumber'];
                $lineroomsOne['linerooms_chairmanpwd'] = $threenumber['chairmanpwd'];
                $lineroomsOne['linerooms_confuserpwd'] = $threenumber['confuserpwd'];
            } else {
                $this->error = true;
                $this->errortip = $LineClassModel->errortip;
                return false;
            }
        }
        $url = "https://global.talk-cloud.net/WebAPI/entry/?";
        $postdata = array();
        $postdata['domain'] = 'shjdbjm';
        $postdata['serial'] = $lineroomsOne['linerooms_threenumber'];
        $postdata['username'] = $studentOne['student_enname'] == '' ? $studentOne['student_cnname'] : $studentOne['student_cnname'];
        $postdata['usertype'] = '2';
        $postdata['ts'] = time();
        $postdata['auth'] = MD5("4j7UDVpdHXgosq9z{$postdata['ts']}{$postdata['serial']}{$postdata['usertype']}");
        if ($lineroomsOne['linerooms_passwordrequired'] == 0) {
            $postdata['userpassword'] = '';
        } else {
            $postdata['userpassword'] = bin2hex(@mcrypt_encrypt(MCRYPT_RIJNDAEL_128, '4j7UDVpdHXgosq9z', $lineroomsOne['linerooms_confuserpwd'], MCRYPT_MODE_ECB));
        }
        $postdata['extradata'] = "mohism";
        $postdata['jumpurl'] = "https://easxapi.kedingdang.com/";
        $postUrl = $url . dataEncode($postdata);
        return $postUrl;
    }

    //获取回放课程
    function LineThreeRecordPlayback($request)
    {
        $lineOne = $this->DataControl->selectOne("select linerooms_id,linerooms_starttime,linerooms_endtime from smc_linerooms where  hour_id= '{$request['hour_id']}'");
        $hourOne = $this->DataControl->selectOne("select hour_id,hour_day from smc_class_hour where hour_id='{$request['hour_id']}' ");
        $Model = new \Model\Smc\LineClassModel();
        $starttime = strtotime($hourOne['hour_day']);
        $endtime = strtotime($hourOne['hour_day']) + 24 * 60*60 - 1;
        $dataList = $Model->GetLineThreeRecord($lineOne['linerooms_id'], $starttime, $endtime);
        return $dataList;
    }

    /**
     * @param $paramArray
     * @return array
     * 课堂评价
     */
    function getAppraiseList($paramArray)
    {
        $datawhere = "s.school_id = '{$paramArray['school_id']}' and hs.student_id='{$paramArray['student_id']}'";

        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] != '' && $paramArray['hour_way'] != '不限') {
            $datawhere .= " and ch.hour_way='{$paramArray['hour_way']}'";
        }

        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and ch.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and ch.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and ch.hour_day >= '{$week['nowweek_start']}' and ch.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and ch.hour_day <= CURDATE() and ch.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and ch.hour_day <= CURDATE() and ch.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and ch.hour_day >= '{$month[0]}' and ch.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and ch.hour_day >= '{$lastmonth[0]}' and ch.hour_day <= '{$lastmonth[1]}'";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$paramArray['starttime']}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$paramArray['endtime']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT hs.class_id,hs.hour_id,hs.student_id,hs.hourstudy_id,hs.hourstudy_checkin,ch.hour_way,ch.hour_number,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_name,c.class_cnname,c.class_enname,s.school_cnname,cl.classroom_cnname,
                (SELECT sf.staffer_cnname
                  FROM smc_class_hour_teaching as ht
                  LEFT JOIN smc_staffer as sf ON sf.staffer_id=ht.staffer_id
                  WHERE ht.class_id=hs.class_id AND ht.hour_id=hs.hour_id AND ht.teaching_type='0'
                ) as staffer_cnname,
                (SELECT sh.hourcomment_level FROM eas_student_hourcomment as sh WHERE sh.student_id=hs.student_id AND sh.hour_id=hs.hour_id) as hourcomment_level,
                (SELECT ROUND(AVG(sr.sturemarkstar_score), 1) FROM eas_classhour_sturemark as cs
                  LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                  WHERE cs.student_id=hs.student_id AND cs.hour_id=hs.hour_id AND cs.class_id=hs.class_id
                ) as score_num
                FROM smc_student_hourstudy as hs
                LEFT JOIN smc_class_hour as ch ON ch.hour_id=hs.hour_id
                LEFT JOIN smc_class as c ON c.class_id=hs.class_id
                LEFT JOIN smc_classroom as cl ON cl.classroom_id=ch.classroom_id
                LEFT JOIN smc_school as s ON s.school_id=c.school_id
                WHERE {$datawhere} ORDER BY ch.hour_day,ch.hour_starttime ASC LIMIT {$pagestart},{$num}";

        $datalist = $this->DataControl->selectClear($sql);
        $status = array('0' => '未上课', '1' => '已上课');
        $statuss = array('0' => '非常满意', '1' => '一般', '2' => '不满意');
        $checkin = array('0' => '异常', '1' => '签到');
        if ($datalist) {
            foreach ($datalist as &$v) {
                $v['hour_ischecking_name'] = $status[$v['hour_ischecking']];
                $v['hourcomment_level'] = $statuss[$v['hourcomment_level']];
                if (!$v['hourstudy_checkin'] && $v['hourstudy_checkin'] != '0') {
                    $v['hourstudy_checkin_name'] = '未上课';
                } else {
                    $v['hourstudy_checkin_name'] = $checkin[$v['hourstudy_checkin']];
                }
                if ($v['hour_way'] == '0') {
                    $v['hour_way'] = '实体课';
                } elseif ($v['hour_way'] == '1') {
                    $v['hour_way'] = '线上课';
                } else {
                    $v['hour_way'] = '未知';
                }

                //教师对学员点评
                $sturemark = $this->DataControl->selectClear("SELECT t.* 
                          from eas_classhour_sturemark as s 
                          LEFT JOIN eas_classhour_sturemarkstar as t ON s.sturemark_id = t.sturemark_id
                          WHERE s.class_id = '{$v['class_id']}' and s.hour_id = '{$v['hour_id']}' and s.student_id = '{$v['student_id']}' ");
                if ($sturemark) {
                    $allnum = count($sturemark);
                    $allscore = '0';
                    foreach ($sturemark as $sturemarkvar) {
                        $allscore += $sturemarkvar['sturemarkstar_score'];
                    }
                    $v['comment'] = sprintf("%.1f", $allscore / $allnum);
                } else {
                    $v['comment'] = '0';
                }
                //学员对教师的评价
                $hourcomment = $this->DataControl->selectClear("SELECT s.hourcomment_score 
                          from eas_student_hourcomment as s 
                          WHERE s.hour_id = '{$v['hour_id']}' and s.student_id = '{$v['student_id']}' ");
                if ($hourcomment) {
                    $allnum = count($hourcomment);
                    $allscore = '0';
                    foreach ($hourcomment as $hourcommentvar) {
                        $allscore += $hourcommentvar['hourcomment_score'];
                    }
                    $v['evaluate'] = sprintf("%.1f", $allscore / $allnum);
                } else {
                    $v['evaluate'] = '0';
                }
//            $v['comment'] = '点评';
//            $v['evaluate'] = '评价';

                if ($v['comment'] == '' && $v['evaluate'] == '') {
                    $v['isevaluate'] = '1';
                } else {
                    $v['isevaluate'] = '0';
                }

            }
        } else {
            $datalist = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(hs.hourstudy_id) as num
                                                      FROM smc_student_hourstudy as hs
                                                      LEFT JOIN smc_class_hour as ch ON ch.hour_id=hs.hour_id
                                                      LEFT JOIN smc_class as c ON c.class_id=hs.class_id
                                                      LEFT JOIN smc_classroom as cl ON cl.classroom_id=ch.classroom_id
                                                      LEFT JOIN smc_school as s ON s.school_id=c.school_id
                                                      WHERE {$datawhere}");
            if ($all_num) {
                $data['allnums'] = $all_num['num'];
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 课堂评价详情
     */
    function AppraiseDteail($paramArray)
    {
        $dataOne = $this->DataControl->selectOne("SELECT hs.hourstudy_checkin,hs.student_id,sd.student_cnname,sd.student_img,sd.student_sex,c.class_id,c.class_cnname,c.class_enname,c.course_id,s.school_cnname,cl.classroom_cnname,sf.staffer_id,sf.staffer_cnname,sf.staffer_mobile,ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_content,ch.hour_ischecking,ch.hour_number,ch.hour_way,ch.hour_name,sff.staffer_cnname as staffer_assistant,ch.hour_number
                                                   FROM smc_student_hourstudy as hs
                                                   LEFT JOIN smc_student as sd ON sd.student_id=hs.student_id
                                                   LEFT JOIN smc_class_hour as ch ON ch.hour_id=hs.hour_id
                                                   LEFT JOIN smc_class as c ON c.class_id=hs.class_id
                                                   LEFT JOIN smc_classroom as cl ON cl.classroom_id=ch.classroom_id
                                                   LEFT JOIN smc_school as s ON s.school_id=c.school_id
                                                   LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id=hs.class_id AND ht.hour_id=hs.hour_id AND ht.teaching_type='0'
                                                   LEFT JOIN smc_staffer as sf ON sf.staffer_id=ht.staffer_id
                                                   LEFT JOIN smc_class_hour_teaching as htt ON htt.class_id=hs.class_id AND htt.hour_id=hs.hour_id AND htt.teaching_type='1'
                                                   LEFT JOIN smc_staffer as sff ON sff.staffer_id=htt.staffer_id
                                                   WHERE hs.hourstudy_id='{$paramArray['hourstudy_id']}'");
        $status = array('0' => '未上课', '1' => '已上课');
        $checkin = array('0' => '异常', '1' => '签到');
        $dataOne['hour_ischecking_name'] = $status[$dataOne['hour_ischecking']];
        if (!$dataOne['hourstudy_checkin'] && $dataOne['hourstudy_checkin'] != '0') {
            $dataOne['hourstudy_checkin_name'] = '未上课';
        } else {
            $dataOne['hourstudy_checkin_name'] = $checkin[$dataOne['hourstudy_checkin']];
        }
        $dataOne['staffer_assistant']=$dataOne['staffer_assistant']?$dataOne['staffer_assistant']:'暂无';
        if ($dataOne['hour_way'] == '0') {
            $dataOne['hour_way'] = '实体课';
        } elseif ($dataOne['hour_way'] == '1') {
            $dataOne['hour_way'] = '线上课';
        } else {
            $dataOne['hour_way'] = '未知';
        }

        $restCourse = $this->DataControl->getFieldOne("smc_student_coursebalance","coursebalance_time","school_id = '{$paramArray['school_id']}' and student_id = '{$dataOne['student_id']}' and course_id = '{$dataOne['course_id']}'");

        $sql="SELECT g.timelog_playtimes,h.hourstudy_checkin,sc.course_checkingintype
              FROM smc_student_coursebalance_timelog AS g,smc_student_hourstudy AS h,smc_course as sc
              WHERE h.hourstudy_id = g.hourstudy_id and g.course_id=sc.course_id and h.hourstudy_id = '{$paramArray['hourstudy_id']}'";
        $cost = $this->DataControl->selectOne($sql);

        $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_img", "staffer_id='{$dataOne['staffer_id']}'");
        $staffer['student_cnname'] = $dataOne['student_cnname'];
        $staffer['student_img'] = $dataOne['student_img'];
        $staffer['student_sex'] = $dataOne['student_sex'];
        $score_num = $this->DataControl->selectClear("SELECT ROUND(AVG(sr.sturemarkstar_score), 1) as score_num
                                              FROM eas_classhour_sturemark as cs
                                              LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                                              WHERE cs.student_id='{$dataOne['student_id']}' AND cs.hour_id='{$dataOne['hour_id']}' AND cs.class_id='{$dataOne['class_id']}'");
        $staffer['score_num'] = $score_num[0]['score_num'];

        $staffer['sturemark'] = $this->DataControl->selectClear("SELECT cs.sturemark_comment,cs.sturemark_picturejson,sr.sturemarkstar_name,sr.sturemarkstar_score 
                                     FROM eas_classhour_sturemark as cs 
                                     LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id 
                                     WHERE cs.student_id='{$dataOne['student_id']}' AND cs.hour_id='{$dataOne['hour_id']}' AND cs.class_id='{$dataOne['class_id']}'");

        $appraise = $this->DataControl->getFieldOne("eas_student_hourcomment", "hourcomment_id,hourcomment_content,hourcomment_level,hourcomment_anonymous", "student_id='{$dataOne['student_id']}' and hour_id='{$dataOne['hour_id']}'");

        $nuno = $this->DataControl->selectClear("select n.noun_word from eas_student_hourcomment_noun_apply as a left join eas_student_hourcomment_noun as n on a.noun_id = n.noun_id where hourcomment_id = '{$appraise['hourcomment_id']}'");

        if ($appraise) {
            $appraise['hourcomment_score'] = sprintf("%.1f", $appraise['hourcomment_score']);
        } else {
            $appraise = array();
        }

        $sql="select cc.coursetype_isname from smc_course as sc left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id where sc.course_id='{$dataOne['course_id']}'";
        $typeOne=$this->DataControl->selectOne($sql);
        if($typeOne['coursetype_isname']==0){
            $dataOne['class_cnname']=$dataOne['class_enname'];
        }

        $data = array();
        $data['list'] = $dataOne;
        $data['staffer'] = $staffer;
        $data['appraise'] = $appraise;
        $data['nuno'] = $nuno;
        $data['restCourse'] = $restCourse['coursebalance_time'];
        if(!$cost){
            $data['cost'] = '0';
        }else{
            if($cost['timelog_playtimes'] == '1' && $cost['hourstudy_checkin']=='1'){

                $data['cost'] = '1';
            }elseif($cost['timelog_playtimes'] == '1' && $cost['hourstudy_checkin']=='0'){
                if($cost['course_checkingintype']==5){
                    $data['cost'] = '0';
                }else{
                    $data['cost'] = '1';
                }
            }else{
                $data['cost'] = '0';
            }
        }


        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 根据评价程度获取形容词
     */
    function hourcommentNoun($paramArray)
    {
        $NounList = $this->DataControl->selectClear("select noun_id,noun_word from eas_student_hourcomment_noun where company_id = '{$paramArray['company_id']}' and hourcomment_level = '{$paramArray['hourcomment_level']}' limit 0,7");

        return $NounList;
    }


    /**
     * @param $paramArray
     * @return array
     * 添加课堂评价
     */
    function AddAppraise($paramArray)
    {
        $data = array();
        $data['hour_id'] = $paramArray['hour_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['hourcomment_content'] = addslashes($paramArray['hourcomment_content']);
        $data['hourcomment_anonymous'] = $paramArray['hourcomment_anonymous'];
        $data['hourcomment_level'] = $paramArray['hourcomment_level'];
        $data['hourcomment_createtime'] = time();
        if ($hourcomment_id = $this->DataControl->insertData('eas_student_hourcomment', $data)) {
            $nounList = json_decode(stripslashes($paramArray['noun']),true);
            foreach ($nounList as $item) {
                $data = array();
                $data['noun_id'] = $item['noun_id'];
                $data['hourcomment_id'] = $hourcomment_id;
                $this->DataControl->insertData('eas_student_hourcomment_noun_apply',$data);
            }
            $res = array('error' => 0, 'errortip' => '添加课堂评价成功');

            $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$paramArray['company_id']}' and masterplate_name = '点评完成通知' and masterplate_class = '0'");
            if($isset){
                $wxid = $isset['masterplate_wxid'];
            }else{
                $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '点评完成通知' and masterplate_class = '0'");
                $wxid = $masterplate['masterplate_wxid'];
            }

            $staffer = $this->DataControl->getFieldOne("smc_class_hour_teaching", "staffer_id,class_id", "hour_id = '{$paramArray['hour_id']}'");
            $course_id = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id = '{$staffer['class_id']}'");
            $course_cnname = $this->DataControl->getFieldOne("smc_course","course_cnname","course_id = '{$course_id['course_id']}'");
            $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$paramArray['student_id']}'");
            $a = $paramArray['staffer_cnname'] . '老师您好，已有学员完成课堂评价，请注意查收哦~';
            $b = $course_cnname['course_cnname'];
            $c = $student_cnname['student_cnname'];
            $d = date('m月d日 H:i', time());
            $e = '点击这里查看评价详情';
            $f = "https://tesc.kedingdang.com/ClassReview/DetailsSchedule?hour_id={$paramArray['hour_id']}&activeName=b";

            $count = $this->DataControl->selectOne("select count(*) as a from eas_student_hourcomment where hour_id = '{$paramArray['hour_id']}'");
            //$num = $this->DataControl->selectOne("select count(ho.hourstudy_id) as a from smc_student_hourstudy as ho where ho.hour_id = '{$paramArray['hour_id']}' and hourstudy_checkin = '1'");

            if ($count['a'] == '1') {
                $wxteModel = new \Model\Api\ZjwxChatModel($staffer['staffer_id']);
                $wxteModel->StuComFirst($a, $b, $c, $d, $e, $f, $wxid);
            }

        } else {
            $res = array('error' => 1, 'errortip' => '添加课堂评价失败');
        }
        return $res;
    }

    //我的班级
    function getClass($paramArray)
    {
        $sql = "SELECT
                    c.class_id,
                    c.class_cnname,
                    c.class_enname
                FROM
                    smc_student_study as s
                    LEFT JOIN smc_class as c ON c.class_id = s.class_id
                WHERE
                    s.school_id = '{$paramArray['school_id']}' AND s.student_id = '{$paramArray['student_id']}'";

        $datalist = $this->DataControl->selectClear($sql);

        return $datalist;
    }

}