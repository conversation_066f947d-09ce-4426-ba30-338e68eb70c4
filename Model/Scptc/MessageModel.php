<?php

namespace Model\Scptc;


Class MessageModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

//    function __construct($publicarray)
//    {
//        parent::__construct();
//        if (is_array($publicarray)) {
//            $this->setPublic($publicarray);
//            $this->publicarray = $publicarray;
//        }
//    }
//
//    function setPublic($publicarray)
//    {
//        if (isset($publicarray['company_id'])) {
//            $this->company_id = $publicarray['company_id'];
//        } else {
//            $this->error = true;
//            $this->errortip = "企业ID必须传入";
//            return false;
//        }
//        if (isset($publicarray['staffer_id'])) {
//            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
//
//                $this->error = true;
//                $this->errortip = "操作人不存在!";
//                return false;
//            }
//        } else {
//            $this->error = true;
//            $this->errortip = "操作ID必须传入";
//            return false;
//        }
//    }
//
//
//    function verdictStaffer($staffer_id)
//    {
//
//        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
//
//        if (!$this->stafferOne) {
//            $this->error = true;
//            $this->errortip = "教师信息不存在";
//            return false;
//        } else {
//            $this->staffer_id = $staffer_id;
//        }
//    }

    //班级通知列表
    function ClassMessage($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and m.message_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and m.message_createtime <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                m.message_id,
                m.message_title,
                m.message_content,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id) as allnum,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id and ms.message_isread = 1) as readnum
           FROM
                eas_class_message AS m
           WHERE {$datawhere} and m.school_id = '{$paramArray['school_id']}' order by m.message_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(m.message_id)
            FROM
                eas_class_message AS m
            WHERE {$datawhere} and m.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0][0];

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["allnum"] = "总人数";
        $field["readnum"] = "已读人数";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取班级通知成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取班级通知失败', 'result' => $result);
        }

        return $res;
    }

    //查看阅读情况
    function ReadSituation($paramArray)
    {
        $result = array();

        $readnum = $this->DataControl->selectClear("select count(ms.messagestu_id) as readnum from eas_class_messagestu as ms where ms.message_id = '{$paramArray['message_id']}' and ms.message_isread = '1'");
        $result['readnum'] = $readnum[0]['readnum'];

        $allnum = $this->DataControl->selectClear("select count(ms.messagestu_id) as allnum from eas_class_messagestu as ms where ms.message_id = '{$paramArray['message_id']}'");
        $result['allnum'] = $allnum[0]['allnum'];


        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.student_cnname,
                s.student_sex,
                s.student_img,
                ms.message_isread 
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}'
            Limit {$pagestart},{$num}";
        $ReadDetail = $this->DataControl->selectClear($sql);


        $all_num = $this->DataControl->select("
            SELECT
                COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}'");
        $allnums = $all_num[0][0];


        $result['data'] = $ReadDetail;
        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => '查看阅读情况成功', 'result' => $result);

        return $res;
    }

    //选择接收人班级
    function ChoiceSelecterClass($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                (select count(*) from smc_student_study as s WHERE s.study_isreading = '1' and s.class_id = c.class_id) as allnum
            FROM
                smc_class AS c 
            WHERE
                {$datawhere}
            AND c.school_id = '{$paramArray['school_id']}' 
            AND c.class_status = '1'
            Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.class_id)
             FROM
                smc_class AS c 
            WHERE
                {$datawhere}
            AND c.school_id = '{$paramArray['school_id']}' 
            AND c.class_status = '1'");
        $allnums = $all_num[0][0];

        if ($NoticeDetail) {
            $result = array();
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取选择接收人班级成功', 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $result["all_num"] = '0';
            $res = array('error' => '1', 'errortip' => '获取选择接收人班级失败', 'result' => $result);
        }

        return $res;
    }

    //选择接收人学员
    function ChoiceSelecterStudent($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_img,
                s.student_sex
            FROM
                smc_student_study AS ss
                LEFT JOIN smc_student AS s ON s.student_id = ss.student_id 
            WHERE
                ss.class_id = '{$paramArray['class_id']}' 
                AND ss.study_isreading = '1'
                Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(ss.study_id)
            FROM
                smc_student_study AS ss
                LEFT JOIN smc_student AS s ON s.student_id = ss.student_id 
            WHERE
                ss.class_id = '{$paramArray['class_id']}' 
                AND ss.study_isreading = '1'");
        $allnums = $all_num[0][0];


        $class_name = $this->DataControl->getFieldOne("smc_class","class_cnname","class_id = '{$paramArray['class_id']}'");

        if ($NoticeDetail) {
            $result = array();
            $result["data"] = $NoticeDetail;
            $result["class_cnname"] = $class_name['class_cnname'];
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取选择接收人学员成功', 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取选择接收人学员失败', 'result' => $result);
        }

        return $res;
    }

    //发布通知
    function SendMessageAction($paramArray){
        $data = array();
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['message_title'] = $paramArray['message_title'];
        $data['message_content'] = $paramArray['message_content'];
        $data['message_radiourl'] = $paramArray['message_radiourl'];
        $data['message_mediajson'] = $paramArray['message_mediajson'];
        $data['message_createtime'] = time();
        $id = $this->DataControl->insertData('eas_class_message',$data);

        $data = array();
        $schoolList = json_decode(stripslashes($paramArray['student']),true);
        foreach ($schoolList as $item) {
            $data['class_id'] = $item['class_id'];
            $data['student_id'] = $item['student_id'];
            $data['message_id'] = $id;
            $data['message_isread'] = '0';
            $data['message_createtime'] = time();
            $this->DataControl->insertData('eas_class_messagestu',$data);
        }

        $res = array('error' => '0', 'errortip' => "发布通知成功", 'result' => array());

        return $res;
    }


    //查看接收人
    function GetSelector($paramArray)
    {
        $result = array();

        $data1 = $this-> DataControl->selectClear("select c.class_id,c.class_cnname from eas_class_messagestu as m left join smc_class as c on m.class_id = c.class_id where m.message_id = '{$paramArray['message_id']}' GROUP BY c.class_id");

        $data2 = $this-> DataControl->selectClear("select c.student_id,c.student_cnname,c.student_img,c.student_sex,class_id from eas_class_messagestu as m left join smc_student as c on m.student_id = c.student_id where m.message_id = '{$paramArray['message_id']}'  GROUP BY c.student_id");

        $data = $this->tree($data1,$data2);

        $result['children'] = $data;

        $res = array('error' => '0', 'errortip' => "发布通知成功", 'result' => $result);

        return $res;
    }

    function tree($items1,$items2)
    {
        $son = array();
        if(is_array($items1)){
            foreach($items1 as $k=>&$v) {
                $son[$k]['class_cnname'] = $v['class_cnname'];
                $son[$k]['class_id'] = $v['class_id'];
                foreach ($items2 as $key=>$value) {
                    if ($v['class_id'] == $value['class_id']) {
                        $son[$k]['children'][$key]['student_cnname'] = $value['student_cnname'];
                        $son[$k]['children'][$key]['student_img'] = $value['student_img'];
                        $son[$k]['children'][$key]['student_id'] = $value['student_id'];
                        $son[$k]['children'][$key]['student_sex'] = $value['student_sex'];
                    }
                }
            }
        }
        return $son;
    }


    //修改密码
    function updatePassAction($paramArray){
        $data = array();
        if($paramArray['staffer_pass1'] == $paramArray['staffer_pass2']){
            $data['staffer_pass'] = md5($paramArray['staffer_pass1']);
            $data['staffer_bakpass'] = $paramArray['staffer_pass1'];
        }else{
            ajax_return(array('error' => 1,'errortip' => "两次输入的密码不相同！"));
        }


        $field = array();
        $field["staffer_pass"] = "md5密码";
        $field["staffer_bakpass"] = "密码备注";
        if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['staffer_id']}'",$data) ) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改密码成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '修改密码失败', 'result' => $result);
        }
        return $res;
    }

    //班级通知详情
    function MessageDetail($paramArray)
    {
        $sql = "
           SELECT
                m.message_id,
                m.message_title,
                m.message_content,
                m.message_radiourl,
                m.message_mediajson,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                s.staffer_cnname,
                s.staffer_mobile,
                cl.class_cnname,
                cl.class_enname,
                cl.class_branch
            FROM
                eas_class_message AS m
                LEFT JOIN smc_staffer AS s ON m.staffer_id = s.staffer_id 
                left join eas_class_messagestu as me on me.message_id = m.message_id
                left join smc_class as cl on me.class_id = cl.class_id
                       WHERE m.message_id = '{$paramArray['message_id']}'
                        GROUP BY m.message_id
";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["message_radiourl"] = "音频URL";
        $field["message_mediajson"] = "视屏或者图片json";
        $field["message_createtime"] = "通知时间";
        $field["staffer_cnname"] = "通知人";
        $field["staffer_mobile"] = "通知人联系方式";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取班级通知详情成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班级通知详情失败', 'result' => $result);
        }

        return $res;
    }

    //修改头像
    function updateImgAction($paramArray){
        $data = array();

        $data['staffer_img'] = $paramArray['staffer_img'];
        $data['staffer_updatetime'] = time();

        $field = array();
        $field["staffer_img"] = "头像";
        if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['staffer_id']}'",$data) ) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改头像成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '修改头像失败', 'result' => $result);
        }
        return $res;
    }

    //根据student_id查学员名字
    function StudentCnname($paramArray)
    {
        $student_cnname = $this->DataControl->getFieldOne("smc_student","student_cnname","student_id = '{$paramArray['student_id']}'");

        $result["name"] = $student_cnname['student_cnname'];
        $res = array('error' => '0', 'errortip' => '获取学员名字成功', 'result' => $result);

        return $res;
    }

    //个人通知列表
    function PersonnalMessage($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT ms.message_isread, m.message_title, m.message_content, m.message_id,s.staffer_cnname,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime
            FROM
                eas_class_messagestu AS ms
            LEFT JOIN
                eas_class_message AS m on ms.message_id = m.message_id
            LEFT JOIN 
                smc_staffer AS s ON s.staffer_id = m.staffer_id
            WHERE
                m.school_id = '{$paramArray['school_id']}' and ms.student_id = '{$paramArray['student_id']}' and m.message_type = '0'
            ORDER BY m.message_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
            LEFT JOIN
                eas_class_message AS m on ms.message_id = m.message_id
            WHERE
                m.school_id = '{$paramArray['school_id']}' and  ms.student_id = '{$paramArray['student_id']}'");
        $allnums = $all_num[0][0];

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["message_isread"] = "是否已读";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取个人通知列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂未发布通知哦~', 'result' => $result);
        }

        return $res;
    }

    //学校公告列表
    function SchoolMessage($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                m.message_title,
                m.message_id,
                m.message_content,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime 
            FROM
                eas_class_message AS m 
            WHERE
                m.school_id = '{$paramArray['school_id']}' and m.message_type = '1' and (m.message_object = '0' or m.message_object = '1')
            Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        if($NoticeDetail){
            foreach($NoticeDetail as &$val){
                $a = $this->DataControl->getFieldOne("eas_class_messagestu","messagestu_id","message_id = '{$val['message_id']}' and student_id = '{$paramArray['student_id']}' and message_isread = '1'");
                if($a){
                    $val['message_isread'] = '1';
                }else{
                    $val['message_isread'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(m.message_id)
            FROM
                eas_class_message AS m
            WHERE
                m.school_id = '{$paramArray['school_id']}' and m.message_type = '1' and (m.message_object = '0' or m.message_object = '1')");
        $allnums = $all_num[0][0];

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["message_isread"] = "是否已读";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取学校公告列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂未发布公告哦~', 'result' => $result);
        }

        return $res;
    }

    //阅读通知
    function ReadMessageAction($paramArray){

        if($paramArray['s_id']){
            if($paramArray['type'] == '1'){
                $data = array();
                $data['message_isread'] = '1';
                $data['message_readtime'] = time();
                $this->DataControl->updateData('eas_class_messagestu',"student_id = '{$paramArray['s_id']}' and message_id = '{$paramArray['message_id']}'",$data);
            }else{
                $a = $this->DataControl->getFieldOne("eas_class_messagestu","messagestu_id","message_id = '{$paramArray['message_id']}' and student_id = '{$paramArray['s_id']}'");
                if(!$a){
                    $data = array();
                    $data['message_id'] = $paramArray['message_id'];
                    $data['student_id'] = $paramArray['s_id'];
                    $data['message_isread'] = '1';
                    $data['message_readtime'] = time();
                    $data['message_createtime'] = time();
                    $this->DataControl->insertData('eas_class_messagestu',$data);
                }
            }
        }else{
            if($paramArray['type'] == '1'){
                $data = array();
                $data['message_isread'] = '1';
                $data['message_readtime'] = time();
                $this->DataControl->updateData('eas_class_messagestu',"student_id = '{$paramArray['student_id']}' and message_id = '{$paramArray['message_id']}'",$data);
            }else{
                $a = $this->DataControl->getFieldOne("eas_class_messagestu","messagestu_id","message_id = '{$paramArray['message_id']}' and student_id = '{$paramArray['student_id']}'");
                if(!$a){
                    $data = array();
                    $data['message_id'] = $paramArray['message_id'];
                    $data['student_id'] = $paramArray['student_id'];
                    $data['message_isread'] = '1';
                    $data['message_readtime'] = time();
                    $data['message_createtime'] = time();
                    $this->DataControl->insertData('eas_class_messagestu',$data);
                }
            }
        }


        $res = array('error' => '0', 'errortip' => "阅读通知成功", 'result' => array());

        return $res;
    }




}