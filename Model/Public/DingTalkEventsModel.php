<?php

namespace Model\PublicModel;

/**
 * 钉钉事件消息模型类 - 基于新数据库表结构
 *
 * 核心设计原则：
 * 1. 所有消息和待办都必须走事件机制
 * 2. 发送的消息必须走模板
 * 3. 所有发送信息都从 dd_events 表开始
 * 4. 不提供直接发送接口，确保数据一致性和可追溯性
 *
 * 数据库表：
 * - dd_config_event: 业务事件配置表
 * - dd_events: 事件触发日志表（核心表）
 * - dd_events_misuser: 消息用户明细表
 * - dd_events_todouser: 待办用户明细表
 * - dd_todo_tasks: 钉钉待办任务表
 * - dd_message_template: 钉钉消息模板表
 */

class DingTalkEventsModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = '';
    public $result = array();
    
    private $sender;
    
    public function __construct()
    {
        parent::__construct();
        require_once dirname(__FILE__) . '/DingTalkEventsSenderModel.php';
        $this->sender = new DingTalkEventsSenderModel();
    }
    
    /**
     * 根据事件代码触发消息发送
     * 
     * @param string $eventCode 事件代码
     * @param array $eventData 事件数据
     * @param array $customRecipients 自定义接收人（可选，为空时使用配置的接收人规则）
     * @return bool
     */
    public function triggerEvent($eventCode, $eventData, $customRecipients = array())
    {
        try {
            // 1. 获取事件配置
            $eventConfig = $this->getEventConfig($eventCode);
            if (!$eventConfig) {
                $this->error = true;
                $this->errortip = "事件配置不存在: {$eventCode}";
                return false;
            }
            
            // 2. 检查事件是否启用
            if ($eventConfig['event_status'] != 1) {
                $this->error = true;
                $this->errortip = "事件已禁用: {$eventCode}";
                return false;
            }
            
            // 3. 生成事件UUID
            $eventsUuid = $this->generateUUID();
            
            // 4. 创建事件日志记录
            $eventsId = $this->createEventLog($eventCode, $eventsUuid, $eventData);
            if (!$eventsId) {
                $this->error = true;
                $this->errortip = "创建事件日志失败";
                return false;
            }
            
            // 5. 确定接收人
            $recipients = $this->determineRecipients($eventConfig, $customRecipients);
            if (empty($recipients)) {
                $this->error = true;
                $this->errortip = "没有找到有效的接收人";
                return false;
            }
            
            // 6. 获取消息模板
            $template = $this->getMessageTemplate($eventConfig, $eventData);
            if (!$template) {
                $this->error = true;
                $this->errortip = "消息模板不存在";
                return false;
            }
            
            // 7. 渲染消息内容
            $messageContent = $this->renderMessageContent($template, $eventData);
            
            // 8. 发送消息
            $sendResult = $this->sendMessage($eventConfig, $template, $messageContent, $recipients, $eventsUuid);
            
            // 9. 更新事件日志
            $this->updateEventLog($eventsId, $sendResult);
            
            // 10. 创建用户明细记录
            $this->createUserDetails($eventsId, $recipients, $eventConfig['message_type']);
            
            // 11. 如果需要创建待办任务
            if ($eventConfig['event_iscreate_todo'] == 1) {
                $this->createTodoTask($eventsId, $eventConfig, $messageContent, $recipients, $eventData);
            }
            
            $this->error = false;
            $this->errortip = '';
            $this->result = $sendResult;
            
            return true;
            
        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '事件触发异常: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 发送卡片消息（通过事件）
     *
     * @param string $title 消息标题
     * @param string $content 消息内容
     * @param string $buttonTitle 按钮标题
     * @param string $buttonUrl 按钮链接
     * @param array $recipients 接收人列表
     * @param array $config 配置参数
     * @return bool
     */
    public function sendCard($title, $content, $buttonTitle, $buttonUrl, $recipients, $config = array())
    {
        // 构建事件数据
        $eventData = array(
            'title' => $title,
            'content' => $content,
            'button_title' => $buttonTitle,
            'button_url' => $buttonUrl,
            'message_type' => 'card',
            'config' => $config
        );

        // 通过通用消息事件发送
        return $this->triggerEvent('general_message', $eventData, $recipients);
    }

    /**
     * 发送文本消息（通过事件）
     *
     * @param string $content 消息内容
     * @param array $recipients 接收人列表
     * @param array $config 配置参数
     * @return bool
     */
    public function sendText($content, $recipients, $config = array())
    {
        // 构建事件数据
        $eventData = array(
            'content' => $content,
            'message_type' => 'text',
            'config' => $config
        );

        // 通过通用消息事件发送
        return $this->triggerEvent('general_message', $eventData, $recipients);
    }

    /**
     * 发送Markdown消息（通过事件）
     *
     * @param string $title 消息标题
     * @param string $content 消息内容
     * @param array $recipients 接收人列表
     * @param array $config 配置参数
     * @return bool
     */
    public function sendMarkdown($title, $content, $recipients, $config = array())
    {
        // 构建事件数据
        $eventData = array(
            'title' => $title,
            'content' => $content,
            'message_type' => 'markdown',
            'config' => $config
        );

        // 通过通用消息事件发送
        return $this->triggerEvent('general_message', $eventData, $recipients);
    }

    /**
     * 创建待办任务（通过事件）
     *
     * @param string $subject 待办标题
     * @param string $description 待办描述
     * @param array $executors 执行者列表
     * @param int $dueHours 截止时间（小时）
     * @param array $config 配置参数
     * @return bool
     */
    public function createTodo($subject, $description, $executors, $dueHours, $config = array())
    {
        // 构建事件数据
        $eventData = array(
            'subject' => $subject,
            'description' => $description,
            'due_hours' => $dueHours,
            'message_type' => 'todo',
            'config' => $config
        );

        // 通过通用待办事件发送
        return $this->triggerEvent('general_todo', $eventData, $executors);
    }
    
    /**
     * 完成待办任务
     * 
     * @param int $eventsId 事件ID
     * @param int $stafferId 完成者员工ID
     * @param string $ddUserId 完成者钉钉用户ID
     * @param string $remark 完成备注
     * @return bool
     */
    public function completeTodo($eventsId, $stafferId, $ddUserId, $remark = '')
    {
        try {
            // 1. 检查待办用户记录是否存在
            $todoUserSql = "SELECT * FROM dd_events_todouser WHERE events_id = {$eventsId} AND staffer_id = {$stafferId}";
            $todoUser = $this->DataControl->selectOne($todoUserSql);

            if (!$todoUser) {
                $this->error = true;
                $this->errortip = "待办任务不存在或用户没有权限";
                return false;
            }

            // 2. 检查任务状态
            if ($todoUser['todouser_status'] != 0) {
                $this->error = true;
                $this->errortip = "待办任务已完成或已取消";
                return false;
            }

            // 3. 调用钉钉API完成待办任务
            $eventSql = "SELECT events_dduuid FROM dd_events WHERE events_id = {$eventsId}";
            $eventData = $this->DataControl->selectOne($eventSql);

            if ($eventData && !empty($eventData['events_dduuid'])) {
                // 调用钉钉API更新待办任务状态
                $apiResult = $this->sender->updateTodoStatus($eventData['events_dduuid'], $ddUserId, true);

                if (!$apiResult) {
                    // API调用失败，但不阻止本地状态更新
                    error_log("钉钉待办任务完成API调用失败: " . $this->sender->errortip);
                }
            }

            // 4. 更新用户待办状态
            $this->DataControl->updateData('dd_events_todouser',
                "events_id = {$eventsId} AND staffer_id = {$stafferId}",
                array(
                    'todouser_status' => 1, // 已完成
                    'complete_time' => time(),
                    'todouser_action' => 'complete'
                )
            );

            // 5. 更新事件状态
            $this->DataControl->updateData('dd_events', "events_id = {$eventsId}", array(
                'events_status' => 1, // 已完成
                'events_remark' => $remark
            ));

            // 5. 如果dd_todo_tasks表中有记录，也更新它
            $todoTask = $this->getTodoTaskByEventsId($eventsId);
            if ($todoTask) {
                $updateData = array(
                    'status' => 1, // 已完成
                    'complete_time' => time(),
                    'complete_staffer_id' => $stafferId,
                    'complete_dd_user_id' => $ddUserId,
                    'update_time' => time()
                );

                $this->DataControl->updateData('dd_todo_tasks', "events_id = {$eventsId}", $updateData);

                // 6. 如果是任一人完成模式，标记其他用户为已取消
                if ($todoTask['completion_mode'] == 1) {
                    $this->DataControl->updateData('dd_events_todouser',
                        "events_id = {$eventsId} AND staffer_id != {$stafferId}",
                        array(
                            'todouser_status' => 2, // 已取消
                            'todouser_action' => 'cancel'
                        )
                    );
                }
            }

            $this->error = false;
            $this->errortip = '';
            $this->result = array('events_id' => $eventsId, 'status' => 'completed');

            return true;

        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '完成待办任务异常: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 获取用户的待办任务列表
     * 
     * @param int $stafferId 员工ID
     * @param int $status 状态筛选（可选）
     * @param int $limit 返回数量限制
     * @return array
     */
    public function getUserTodoList($stafferId, $status = null, $limit = 20)
    {
        try {
            $conditions = array("t.creator_staffer_id = {$stafferId} OR FIND_IN_SET({$stafferId}, t.executor_staffer_ids)");
            
            if ($status !== null) {
                $conditions[] = "t.status = " . intval($status);
            }
            
            $whereClause = implode(' AND ', $conditions);
            
            $sql = "
                SELECT 
                    t.*,
                    e.event_code,
                    e.events_uuid,
                    e.events_create_time,
                    FROM_UNIXTIME(t.create_time) as create_time_str,
                    FROM_UNIXTIME(t.due_time) as due_time_str,
                    FROM_UNIXTIME(t.complete_time) as complete_time_str
                FROM dd_todo_tasks t
                LEFT JOIN dd_events e ON t.events_id = e.events_id
                WHERE {$whereClause}
                ORDER BY t.create_time DESC
                LIMIT {$limit}
            ";
            
            $todoList = $this->DataControl->selectClear($sql);
            
            $this->error = false;
            $this->errortip = '';
            $this->result = $todoList ?: array();
            
            return $todoList ?: array();
            
        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '获取待办任务列表异常: ' . $e->getMessage();
            return array();
        }
    }
    
    /**
     * 获取事件配置
     */
    private function getEventConfig($eventCode)
    {
        $sql = "SELECT * FROM dd_config_event WHERE event_code = '{$eventCode}' AND event_status = 1";
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 创建事件日志记录
     */
    private function createEventLog($eventCode, $eventsUuid, $eventData)
    {
        $logData = array(
            'event_code' => $eventCode,
            'events_uuid' => $eventsUuid,
            'events_dduuid' => '', // 钉钉返回的UUID，稍后更新
            'events_data' => json_encode($eventData, JSON_UNESCAPED_UNICODE),
            'events_send_results' => '',
            'events_create_time' => time(),
            'events_process_status' => 0, // 处理中
            'events_status' => 0, // 待处理
            'events_range' => 0,
            'events_remark' => '',
            'events_error' => ''
        );

        return $this->DataControl->insertData('dd_events', $logData);
    }



    /**
     * 确定接收人
     */
    private function determineRecipients($eventConfig, $customRecipients)
    {
        if (!empty($customRecipients)) {
            return $customRecipients;
        }

        // 解析接收人规则
        $recipientRules = json_decode($eventConfig['event_recipient_rules'], true);
        if (!$recipientRules) {
            return array();
        }

        $recipients = array();

        switch ($recipientRules['type']) {
            case 'role':
                // 根据角色获取用户
                $recipients = $this->getUsersByRoles($recipientRules['roles']);
                break;

            case 'department':
                // 根据部门获取用户
                $recipients = $this->getUsersByDepartments($recipientRules['departments']);
                break;

            case 'users':
                // 直接指定用户
                $recipients = $recipientRules['user_ids'];
                break;
        }

        return $recipients;
    }

    /**
     * 获取消息模板
     */
    private function getMessageTemplate($eventConfig, $eventData = array())
    {
        $eventCode = $eventConfig['event_code'];
        $messageType = $eventConfig['message_type'];

        // 对于通用事件，从事件数据中获取消息类型
        if (isset($eventData['message_type'])) {
            $messageType = $eventData['message_type'];
        }

        // 首先尝试获取事件特定模板（通过 template_code）
        $sql = "
            SELECT * FROM dd_message_template
            WHERE template_code = '{$eventCode}'
            AND template_status = 1
            LIMIT 1
        ";

        $template = $this->DataControl->selectOne($sql);

        // 如果没有找到事件特定模板，尝试获取通用模板
        if (!$template) {
            $templateCode = 'general_' . $messageType;
            $sql = "
                SELECT * FROM dd_message_template
                WHERE template_code = '{$templateCode}'
                AND template_status = 1
                LIMIT 1
            ";

            $template = $this->DataControl->selectOne($sql);
        }

        // 如果还是没有找到，使用默认模板
        if (!$template) {
            $sendParams = json_decode($eventConfig['event_send_params'], true);
            $source = isset($sendParams['source']) ? intval($sendParams['source']) : 1;

            $sql = "
                SELECT * FROM dd_message_template
                WHERE message_type = '{$messageType}'
                AND template_source = {$source}
                AND template_status = 1
                LIMIT 1
            ";

            $template = $this->DataControl->selectOne($sql);
        }

        return $template;
    }

    /**
     * 渲染消息内容
     */
    private function renderMessageContent($template, $eventData)
    {
        $title = $template['template_title'];
        $content = $template['template_content'];
        $buttonTitle = $template['template_button_title'];
        $buttonUrl = $template['template_button_url'];

        // 替换变量
        foreach ($eventData as $key => $value) {
            $placeholder = '{' . $key . '}';
            $title = str_replace($placeholder, $value, $title);
            $content = str_replace($placeholder, $value, $content);
            $buttonTitle = str_replace($placeholder, $value, $buttonTitle);
            $buttonUrl = str_replace($placeholder, $value, $buttonUrl);
        }

        return array(
            'title' => $title,
            'content' => $content,
            'button_title' => $buttonTitle,
            'button_url' => $buttonUrl
        );
    }

    /**
     * 发送消息
     */
    private function sendMessage($eventConfig, $template, $messageContent, $recipients, $eventsUuid)
    {
        // 使用模板中的消息类型，而不是事件配置中的
        $messageType = $template['message_type'];

        $messageData = array(
            'message_type' => $messageType,
            'title' => $messageContent['title'],
            'content' => $messageContent['content'],
            'button_title' => $messageContent['button_title'],
            'button_url' => $messageContent['button_url'],
            'recipients' => $recipients,
            'events_uuid' => $eventsUuid,
            'config' => json_decode($eventConfig['event_send_params'], true)
        );

        return $this->sender->sendMessage($messageData);
    }

    /**
     * 更新事件日志
     */
    private function updateEventLog($eventsId, $sendResult)
    {
        // 获取完整的API响应结果
        $apiResult = $this->sender->result;

        // 确保存储的是完整的API响应JSON，而不是布尔值
        $responseJson = '';
        if (is_array($apiResult) || is_object($apiResult)) {
            $responseJson = json_encode($apiResult, JSON_UNESCAPED_UNICODE);
        } elseif (is_string($apiResult)) {
            $responseJson = $apiResult;
        } else {
            // 如果没有有效的API响应，记录发送结果
            $responseJson = json_encode(array(
                'success' => $sendResult,
                'timestamp' => date('Y-m-d H:i:s'),
                'error' => $sendResult ? '' : $this->sender->errortip
            ), JSON_UNESCAPED_UNICODE);
        }

        $updateData = array(
            'events_send_results' => $responseJson,
            'events_process_status' => $sendResult ? 1 : 2, // 1=成功, 2=失败
        );

        // 如果API返回了任务ID或消息ID，存储到events_dduuid字段
        if (is_array($apiResult)) {
            if (isset($apiResult['id'])) {
                $updateData['events_dduuid'] = $apiResult['id'];
            } elseif (isset($apiResult['data']['id'])) {
                $updateData['events_dduuid'] = $apiResult['data']['id'];
            } elseif (isset($apiResult['data']['taskId'])) {
                $updateData['events_dduuid'] = $apiResult['data']['taskId'];
            }
        }

        // 如果发送失败，记录错误信息
        if (!$sendResult) {
            $updateData['events_error'] = $this->sender->errortip;
        }

        $this->DataControl->updateData('dd_events', "events_id = {$eventsId}", $updateData);
    }

    /**
     * 创建用户明细记录
     */
    private function createUserDetails($eventsId, $recipients, $messageType)
    {
        foreach ($recipients as $recipient) {
            // 从接收人数组中提取用户信息
            $ddUserId = '';
            $stafferId = 0;
            $userName = '';

            if (is_array($recipient)) {
                $ddUserId = isset($recipient['dd_user_id']) ? $recipient['dd_user_id'] : '';
                $stafferId = isset($recipient['staffer_id']) ? intval($recipient['staffer_id']) : 0;
                $userName = isset($recipient['name']) ? $recipient['name'] : '';
            } elseif (is_string($recipient)) {
                $ddUserId = $recipient;
                // 如果只有钉钉ID，尝试获取用户信息
                $userInfo = $this->getUserInfoByDdUserId($ddUserId);
                $stafferId = $userInfo['staffer_id'] ?: 0;
                $userName = $userInfo['name'] ?: '';
            }

            if (empty($ddUserId)) {
                continue; // 跳过无效的接收人
            }

            if ($messageType === 'todo') {
                // 创建待办用户明细
                $todoUserData = array(
                    'events_id' => $eventsId,
                    'staffer_id' => $stafferId,
                    'dduser_id' => $ddUserId,
                    'dduser_name' => $userName,
                    'todouser_priority' => 2, // 普通优先级
                    'todouser_status' => 0, // 待处理
                    'complete_time' => 0,
                    'todouser_isread' => 0,
                    'todouser_readtime' => 0,
                    'todouser_action' => 'create',
                    'create_time' => time()
                );

                $this->DataControl->insertData('dd_events_todouser', $todoUserData);
            } else {
                // 创建消息用户明细
                $misUserData = array(
                    'events_id' => $eventsId,
                    'staffer_id' => $stafferId,
                    'dduser_id' => $ddUserId,
                    'dduser_name' => $userName,
                    'misuser_isread' => 0,
                    'misuser_readtime' => 0,
                    'create_time' => time()
                );

                $this->DataControl->insertData('dd_events_misuser', $misUserData);
            }
        }
    }

    /**
     * 生成UUID
     */
    private function generateUUID()
    {
        return 'evt_' . date('YmdHis') . '_' . mt_rand(100000, 999999);
    }

    /**
     * 根据角色获取用户
     */
    private function getUsersByRoles($roles)
    {
        // 这里需要根据实际的用户角色表来实现
        // 暂时返回测试用户
        return array('619268940');
    }

    /**
     * 根据部门获取用户
     */
    private function getUsersByDepartments($departments)
    {
        // 这里需要根据实际的部门用户表来实现
        // 暂时返回测试用户
        return array('619268940');
    }

    /**
     * 根据钉钉用户ID获取用户信息
     */
    private function getUserInfoByDdUserId($ddUserId)
    {
        // 这里需要根据实际的用户表来实现
        // 暂时返回模拟数据
        return array(
            'staffer_id' => 1,
            'name' => '测试用户',
            'dd_user_id' => $ddUserId
        );
    }

    /**
     * 创建待办任务
     */
    private function createTodoTask($eventsId, $eventConfig, $messageContent, $recipients, $eventData)
    {
        // 解析发送参数
        $sendParams = json_decode($eventConfig['event_send_params'], true);

        // 构建执行者ID列表
        $executorStafferIds = array();
        $executorDdUserIds = array();

        foreach ($recipients as $recipient) {
            if (is_array($recipient)) {
                $ddUserId = isset($recipient['dd_user_id']) ? $recipient['dd_user_id'] : '';
                $stafferId = isset($recipient['staffer_id']) ? intval($recipient['staffer_id']) : 0;

                if ($ddUserId) {
                    $executorDdUserIds[] = $ddUserId;
                    $executorStafferIds[] = $stafferId ?: 0;
                }
            } elseif (is_string($recipient)) {
                $userInfo = $this->getUserInfoByDdUserId($recipient);
                $executorStafferIds[] = $userInfo['staffer_id'] ?: 0;
                $executorDdUserIds[] = $recipient;
            }
        }

        // 计算截止时间（默认24小时）
        $dueHours = isset($eventData['due_hours']) ? intval($eventData['due_hours']) : 24;
        $dueTime = time() + ($dueHours * 3600);

        $todoData = array(
            'events_id' => $eventsId,
            'source' => $sendParams['source'] ?: 1,
            'trigger' => 1, // 自动触发
            'service_channel' => $sendParams['service_channel'] ?: 10,
            'categrate' => json_encode($sendParams['categrate'] ?: array(100, 110)),
            'subject' => $messageContent['title'],
            'description' => $messageContent['content'],
            'due_time' => $dueTime,
            'priority' => $eventConfig['event_priority'] ?: 2,
            'completion_mode' => isset($eventData['completion_mode']) ? intval($eventData['completion_mode']) : 1,
            'creator_staffer_id' => 0, // 系统创建
            'creator_dd_user_id' => 'system',
            'executor_staffer_ids' => implode(',', $executorStafferIds),
            'executor_dd_user_ids' => implode(',', $executorDdUserIds),
            'pc_url' => $messageContent['button_url'],
            'mobile_url' => $messageContent['button_url'],
            'status' => 0, // 待处理
            'auto_send_msg' => 1,
            'create_time' => time(),
            'update_time' => time(),
            'complete_time' => 0,
            'complete_staffer_id' => 0,
            'complete_dd_user_id' => '',
            'business_type' => isset($eventData['business_type']) ? $eventData['business_type'] : '',
            'business_id' => isset($eventData['business_id']) ? intval($eventData['business_id']) : 0,
            'company_id' => isset($eventData['company_id']) ? intval($eventData['company_id']) : 0,
            'school_id' => isset($eventData['school_id']) ? intval($eventData['school_id']) : 0
        );

        return $this->DataControl->insertData('dd_todo_tasks', $todoData);
    }

    /**
     * 根据事件ID获取待办任务
     */
    private function getTodoTaskByEventsId($eventsId)
    {
        $sql = "SELECT * FROM dd_todo_tasks WHERE events_id = {$eventsId}";
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 检查待办任务权限
     */
    private function checkTodoPermission($eventsId, $stafferId)
    {
        $sql = "
            SELECT COUNT(*) as count
            FROM dd_todo_tasks
            WHERE events_id = {$eventsId}
            AND (creator_staffer_id = {$stafferId} OR FIND_IN_SET({$stafferId}, executor_staffer_ids))
        ";

        $result = $this->DataControl->selectOne($sql);
        return $result && $result['count'] > 0;
    }

    /**
     * 健康检查
     */
    public function healthCheck()
    {
        try {
            $result = $this->sender->healthCheck();

            $this->error = $this->sender->error;
            $this->errortip = $this->sender->errortip;
            $this->result = $this->sender->result;

            return $result;

        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '健康检查异常: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取事件日志列表
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $eventCode 事件代码（可选）
     * @return array
     */
    public function getEventLogs($page = 1, $limit = 10, $eventCode = '')
    {
        try {
            $offset = ($page - 1) * $limit;

            $whereClause = "WHERE 1=1";
            if (!empty($eventCode)) {
                $whereClause .= " AND event_code = '{$eventCode}'";
            }

            // 获取总数
            $countSql = "SELECT COUNT(*) as total FROM dd_events {$whereClause}";
            $countResult = $this->DataControl->selectOne($countSql);
            $total = $countResult ? intval($countResult['total']) : 0;

            // 获取列表
            $sql = "
                SELECT * FROM dd_events
                {$whereClause}
                ORDER BY events_id DESC
                LIMIT {$offset}, {$limit}
            ";

            $list = $this->DataControl->selectAll($sql);

            $this->result = array(
                'list' => $list ?: array(),
                'pagination' => array(
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                )
            );

            return true;

        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '获取事件日志异常: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * 获取待办任务列表
     *
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param int $status 状态（-1:全部, 0:未完成, 1:已完成）
     * @return array
     */
    public function getTodoList($page = 1, $limit = 10, $status = -1)
    {
        try {
            $offset = ($page - 1) * $limit;

            $whereClause = "WHERE 1=1";
            if ($status >= 0) {
                $whereClause .= " AND task_status = {$status}";
            }

            // 获取总数
            $countSql = "SELECT COUNT(*) as total FROM dd_todo_tasks {$whereClause}";
            $countResult = $this->DataControl->selectOne($countSql);
            $total = $countResult ? intval($countResult['total']) : 0;

            // 获取列表
            $sql = "
                SELECT * FROM dd_todo_tasks
                {$whereClause}
                ORDER BY task_id DESC
                LIMIT {$offset}, {$limit}
            ";

            $list = $this->DataControl->selectAll($sql);

            $this->result = array(
                'list' => $list ?: array(),
                'pagination' => array(
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                )
            );

            return true;

        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '获取待办列表异常: ' . $e->getMessage();
            return false;
        }
    }



    /**
     * 获取最后错误信息
     */
    public function getLastError()
    {
        return array(
            'error' => $this->error,
            'errortip' => $this->errortip,
            'result' => $this->result
        );
    }
}
