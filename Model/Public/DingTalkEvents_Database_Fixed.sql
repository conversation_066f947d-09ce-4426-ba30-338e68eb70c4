-- 钉钉消息推送系统 - 修复后的数据库表结构
-- 修复日期：2025-08-01
-- 说明：修复了SQL语法错误和索引问题，保持表名和字段名不变

-- 1. 业务事件配置表
CREATE TABLE `dd_config_event` (
  `event_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_code` varchar(50) NOT NULL DEFAULT '' COMMENT '事件代码',
  `event_name` varchar(100) NOT NULL DEFAULT '' COMMENT '事件名称',
  `event_description` varchar(500) NOT NULL DEFAULT '' COMMENT '事件描述',
  `event_module` varchar(10) NOT NULL DEFAULT '' COMMENT '所属模块',
  `event_trigger_config` text NOT NULL COMMENT '触发配置JSON',
  `event_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `message_type` varchar(20) NOT NULL DEFAULT '' COMMENT '消息类型(呈现样式)',
  `event_recipient_rules` text NOT NULL COMMENT '接收人规则JSON',
  `event_send_method` varchar(50) NOT NULL DEFAULT 'direct' COMMENT '发送方法 direct-直接发送 batch-批量发送 queue-队列发送',
  `event_send_params` text NOT NULL COMMENT '发送参数配置JSON',
  `event_priority` tinyint(2) NOT NULL DEFAULT '1' COMMENT '优先级',
  `event_delay_seconds` int(11) NOT NULL DEFAULT '0' COMMENT '延迟发送秒数',
  `event_iscreate_todo` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否创建待办',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`event_id`),
  UNIQUE KEY `uk_event_code` (`event_code`),
  KEY `idx_event_module` (`event_module`),
  KEY `idx_event_status` (`event_status`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='业务事件表';

-- 2. 事件触发日志表
CREATE TABLE `dd_events` (
  `events_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `event_code` varchar(50) NOT NULL DEFAULT '' COMMENT '事件代码',
  `events_uuid` varchar(64) NOT NULL DEFAULT '' COMMENT '消息UUID',
  `events_dduuid` varchar(64) NOT NULL DEFAULT '' COMMENT '钉钉中台消息或待办UUID',
  `events_data` text NOT NULL COMMENT '触发数据JSON',
  `events_send_results` text NOT NULL COMMENT '发送结果JSON',
  `events_create_time` int(11) NOT NULL DEFAULT '0' COMMENT '触发时间',
  `events_process_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '发送状态 0-处理中 1-成功 2-失败',
  `events_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '待办完成状态：0-待处理 1-已完成 2-已取消',
  `events_range` tinyint(2) NOT NULL DEFAULT '0' COMMENT '待办范围 1-任一完成 2-全部完成',
  `events_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '操作备注（用户填写的备注信息）',
  `events_error` varchar(500) NOT NULL DEFAULT '' COMMENT '错误信息',
  PRIMARY KEY (`events_id`),
  KEY `idx_event_code` (`event_code`),
  KEY `idx_events_create_time` (`events_create_time`),
  KEY `idx_events_process_status` (`events_process_status`),
  KEY `idx_events_uuid` (`events_uuid`),
  KEY `idx_events_dduuid` (`events_dduuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='事件触发日志表';

-- 3. 消息用户明细表
CREATE TABLE `dd_events_misuser` (
  `misuser_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `events_id` int(11) NOT NULL DEFAULT '0' COMMENT '消息日志ID',
  `staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `dduser_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户钉钉ID',
  `dduser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '用户姓名',
  `misuser_isread` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0-未读 1-已读',
  `misuser_readtime` int(11) NOT NULL DEFAULT '0' COMMENT '阅读时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`misuser_id`),
  UNIQUE KEY `uk_events_staffer` (`events_id`, `staffer_id`),
  KEY `idx_events_id` (`events_id`),
  KEY `idx_staffer_id` (`staffer_id`),
  KEY `idx_dduser_id` (`dduser_id`),
  KEY `idx_misuser_isread` (`misuser_isread`),
  KEY `idx_misuser_readtime` (`misuser_readtime`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='消息用户明细';

-- 4. 待办用户明细表
CREATE TABLE `dd_events_todouser` (
  `todouser_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `events_id` int(11) NOT NULL DEFAULT '0' COMMENT '消息日志ID',
  `staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `dduser_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户钉钉ID',
  `dduser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '用户姓名',
  `todouser_priority` tinyint(2) NOT NULL DEFAULT '0' COMMENT '优先级：1-较低 2-普通 3-紧急 4-非常紧急',
  `todouser_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态：0-待处理 1-已完成 2-已取消',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间（Unix时间戳）',
  `todouser_isread` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0-未读 1-已读',
  `todouser_readtime` int(11) NOT NULL DEFAULT '0' COMMENT '阅读时间',
  `todouser_action` varchar(20) NOT NULL DEFAULT '' COMMENT '操作类型：create-创建 update-更新 complete-完成 cancel-取消 delete-删除',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间（Unix时间戳）',
  PRIMARY KEY (`todouser_id`),
  UNIQUE KEY `uk_events_staffer_todo` (`events_id`, `staffer_id`),
  KEY `idx_events_id_todo` (`events_id`),
  KEY `idx_staffer_id_todo` (`staffer_id`),
  KEY `idx_dduser_id_todo` (`dduser_id`),
  KEY `idx_todouser_status` (`todouser_status`),
  KEY `idx_todouser_action` (`todouser_action`),
  KEY `idx_todouser_priority` (`todouser_priority`),
  KEY `idx_complete_time` (`complete_time`),
  KEY `idx_create_time_todo` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='待办用户明细表';

-- 5. 钉钉待办任务表
CREATE TABLE `dd_todo_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `events_id` int(11) NOT NULL DEFAULT '0' COMMENT '消息日志ID',
  `source` tinyint(2) NOT NULL DEFAULT '1' COMMENT '系统来源：1-CRM系统 2-教务系统 3-财务系统 99-系统管理',
  `trigger` tinyint(1) NOT NULL DEFAULT '1' COMMENT '触发方式：1-自动触发 2-人工触发',
  `service_channel` int(11) NOT NULL DEFAULT '10' COMMENT '业务通路：10-CRM客户管理 11-CRM销售跟进 20-教务课程管理 21-教务学员管理 30-财务收费管理 31-财务退费管理 990-系统异常报警',
  `categrate` varchar(100) NOT NULL DEFAULT '[100,110]' COMMENT '业务分类JSON：[一级分类,二级分类] 如[100,110]-CRM新客户报名',
  `subject` varchar(200) NOT NULL DEFAULT '' COMMENT '待办标题（最长200字符）',
  `description` text COMMENT '待办详细描述（支持4096字符）',
  `due_time` int(11) NOT NULL DEFAULT '0' COMMENT '截止时间（Unix时间戳）',
  `priority` tinyint(2) NOT NULL DEFAULT '2' COMMENT '优先级：1-较低 2-普通 3-紧急 4-非常紧急',
  `completion_mode` tinyint(1) NOT NULL DEFAULT '1' COMMENT '完成模式：1-任一人完成即可 2-所有人都需完成',
  `creator_staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建者员工ID（关联smc_staffer.staffer_id）',
  `creator_dd_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '创建者钉钉用户ID（关联smc_staffer.dd_user_id）',
  `executor_staffer_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '执行者员工ID列表（逗号分隔，关联smc_staffer.staffer_id）',
  `executor_dd_user_ids` text NOT NULL COMMENT '执行者钉钉用户ID列表（逗号分隔，关联smc_staffer.dd_user_id）',
  `pc_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'PC端处理链接（如：https://crm.system.com/client/123）',
  `mobile_url` varchar(500) NOT NULL DEFAULT '' COMMENT '移动端处理链接（如：https://m.crm.system.com/client/123）',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '待办状态：0-待处理 1-已完成 2-已取消/已删除',
  `auto_send_msg` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否同步发送消息：0-否 1-是',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间（Unix时间戳）',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间（Unix时间戳）',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间（Unix时间戳，status=1时有值）',
  `complete_staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '完成者员工ID（关联smc_staffer.staffer_id）',
  `complete_dd_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '完成者钉钉用户ID（关联smc_staffer.dd_user_id）',
  `business_type` varchar(50) NOT NULL DEFAULT '' COMMENT '业务类型：client-客户 student-学员 order-订单 course-课程',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务数据ID（如客户ID、学员ID等）',
  `company_id` int(11) NOT NULL DEFAULT '0' COMMENT '公司ID（多租户支持）',
  `school_id` int(11) NOT NULL DEFAULT '0' COMMENT '校区ID',
  PRIMARY KEY (`id`),
  KEY `idx_events_id_task` (`events_id`),
  KEY `idx_source_channel` (`source`, `service_channel`),
  KEY `idx_status_priority` (`status`, `priority`),
  KEY `idx_creator_staffer_id` (`creator_staffer_id`),
  KEY `idx_executor_staffer_ids` (`executor_staffer_ids`(100)),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_company_school` (`company_id`, `school_id`),
  KEY `idx_create_time_task` (`create_time`),
  KEY `idx_due_time` (`due_time`),
  KEY `idx_complete_time_task` (`complete_time`),
  KEY `idx_completion_mode` (`completion_mode`),
  KEY `idx_creator_dd_user_id` (`creator_dd_user_id`),
  KEY `idx_complete_staffer_id` (`complete_staffer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='钉钉待办任务表';

-- 6. 钉钉消息模板表
CREATE TABLE `dd_message_template` (
  `template_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL DEFAULT '' COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL DEFAULT '' COMMENT '模板代码',
  `message_type` varchar(20) NOT NULL DEFAULT '' COMMENT '消息类型',
  `template_title` varchar(200) NOT NULL DEFAULT '' COMMENT '标题模板',
  `template_content` text NOT NULL COMMENT '内容模板',
  `template_button_title` varchar(50) NOT NULL DEFAULT '' COMMENT '按钮标题（卡片消息用）',
  `template_button_url` varchar(500) NOT NULL DEFAULT '' COMMENT '按钮链接模板（卡片消息用）',
  `template_source` int(11) NOT NULL DEFAULT '0' COMMENT '适用系统来源',
  `template_service_channel` int(11) NOT NULL DEFAULT '0' COMMENT '适用业务通路',
  `template_categrate` varchar(100) NOT NULL DEFAULT '' COMMENT '业务分类JSON',
  `template_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_template_source` (`template_source`),
  KEY `idx_template_status` (`template_status`),
  KEY `idx_template_service_channel` (`template_service_channel`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='钉钉消息模板表';

-- ========================================
-- 初始数据插入
-- ========================================

-- 插入事件配置数据
INSERT INTO `dd_config_event` (`event_code`, `event_name`, `event_description`, `event_module`, `event_trigger_config`, `event_status`, `message_type`, `event_recipient_rules`, `event_send_method`, `event_send_params`, `event_priority`, `event_delay_seconds`, `event_iscreate_todo`, `create_time`, `update_time`) VALUES
('crm_new_client', 'CRM新客户报名', '客户首次报名时触发的通知事件', 'crm', '{"trigger_conditions": ["client_status=new"], "trigger_timing": "immediate"}', 1, 'card', '{"type": "role", "roles": ["sales_manager", "sales_staff"]}', 'direct', '{"source": 1, "service_channel": 10, "categrate": [100, 110]}', 2, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('course_reminder', '课程开课提醒', '课程即将开始时的提醒通知', 'edu', '{"trigger_conditions": ["course_start_time"], "trigger_timing": "before_30min"}', 1, 'text', '{"type": "role", "roles": ["teacher", "student"]}', 'direct', '{"source": 2, "service_channel": 20, "categrate": [200, 210]}', 2, 1800, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('system_alert', '系统告警通知', '系统异常或性能告警时的通知', 'system', '{"trigger_conditions": ["cpu_usage>90", "memory_usage>85"], "trigger_timing": "immediate"}', 1, 'card', '{"type": "role", "roles": ["admin", "ops"]}', 'direct', '{"source": 99, "service_channel": 990, "categrate": [999, 991]}', 3, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('refund_failure', '退费失败通知', '退费处理失败时的通知事件', 'finance', '{"trigger_conditions": ["refund_status=failed"], "trigger_timing": "immediate"}', 1, 'card', '{"type": "role", "roles": ["finance_manager", "finance_staff"]}', 'direct', '{"source": 3, "service_channel": 31, "categrate": [300, 311]}', 3, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('general_message', '通用消息事件', '用于发送各种类型的通用消息', 'general', '{"trigger_conditions": [], "trigger_timing": "immediate"}', 1, 'card', '{"type": "custom", "roles": []}', 'direct', '{"source": 1, "service_channel": 10, "categrate": [100, 110]}', 2, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('general_todo', '通用待办事件', '用于创建各种类型的待办任务', 'general', '{"trigger_conditions": [], "trigger_timing": "immediate"}', 1, 'todo', '{"type": "custom", "roles": []}', 'direct', '{"source": 1, "service_channel": 10, "categrate": [100, 110]}', 2, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入消息模板数据
INSERT INTO `dd_message_template` (`template_name`, `template_code`, `message_type`, `template_title`, `template_content`, `template_button_title`, `template_button_url`, `template_source`, `template_service_channel`, `template_categrate`, `template_status`, `create_time`, `update_time`) VALUES
('新客户报名通知模板', 'new_client_notice', 'card', '新客户报名通知', '## 🎉 新客户报名通知\n\n**客户信息：**\n- 👤 姓名：{client_name}\n- 📱 手机：{client_phone}\n- 🏫 意向校区：{school_name}\n- 📚 意向课程：{course_name}\n- 🌐 来源渠道：{source_channel}\n- ⏰ 报名时间：{create_time}\n\n> 请及时跟进处理！', '立即处理', 'https://crm.system.com/client/detail?id={client_id}', 1, 10, '[100,110]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('课程开课提醒模板', 'course_reminder', 'text', '课程开课提醒', '📚 课程开课提醒\n\n课程：{course_name}\n时间：{start_time}\n教师：{teacher_name}\n教室：{classroom}\n学员数：{student_count}人\n\n请准时参加！', '', '', 2, 20, '[200,210]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('系统告警通知模板', 'system_alert', 'card', '系统告警通知', '## 🚨 系统告警通知\n\n**告警信息：**\n- 🖥️ 服务器：{server_name}\n- ⚠️ 告警类型：{alert_type}\n- 📊 CPU使用率：{cpu_usage}\n- 💾 内存使用率：{memory_usage}\n- 💿 磁盘使用率：{disk_usage}\n- ⏰ 告警时间：{alert_time}\n- 🔥 告警级别：{alert_level}\n\n> 请立即处理！', '查看详情', 'https://monitor.system.com/alert/detail?server={server_name}', 99, 990, '[999,991]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('退费失败通知模板', 'refund_failure', 'card', '退费失败通知', '## ❌ 退费失败通知\n\n**退费信息：**\n- 📋 订单号：{order_id}\n- 👤 学员姓名：{student_name}\n- 💰 退费金额：{refund_amount}元\n- ❌ 失败原因：{error_message}\n- ⏰ 失败时间：{failure_time}\n- 🔄 重试次数：{retry_count}\n\n> 请及时处理退费异常！', '立即处理', 'https://finance.system.com/refund/handle?id={order_id}', 3, 31, '[300,311]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('通用卡片消息模板', 'general_card', 'card', '{title}', '{content}', '{button_title}', '{button_url}', 1, 10, '[100,110]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('通用文本消息模板', 'general_text', 'text', '消息通知', '{content}', '', '', 1, 10, '[100,110]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('通用Markdown消息模板', 'general_markdown', 'markdown', '{title}', '{content}', '', '', 1, 10, '[100,110]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('通用待办任务模板', 'general_todo', 'todo', '{subject}', '{description}', '查看详情', '{detail_url}', 1, 10, '[100,110]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- ========================================
-- 修复说明
-- ========================================

/*
修复内容总结：

1. 修复了PRIMARY KEY引用错误：
   - dd_config_event: PRIMARY KEY (`id`) -> PRIMARY KEY (`event_id`)
   - dd_events: PRIMARY KEY (`id`) -> PRIMARY KEY (`events_id`)
   - dd_events_misuser: PRIMARY KEY (`id`) -> PRIMARY KEY (`misuser_id`)
   - dd_events_todouser: PRIMARY KEY (`id`) -> PRIMARY KEY (`todouser_id`)

2. 修复了索引引用错误：
   - dd_config_event: idx_module -> idx_event_module, idx_status -> idx_event_status
   - dd_events: idx_trigger_time -> idx_events_create_time, idx_process_status -> idx_events_process_status
   - dd_events_misuser: uk_message_user -> uk_events_staffer, 修复了所有索引字段名
   - dd_events_todouser: 修复了所有索引字段名，添加了缺失的索引
   - dd_todo_tasks: 移除了不存在的uk_task_id索引，修复了其他索引字段名
   - dd_message_template: idx_source -> idx_template_source, idx_status -> idx_template_status

3. 添加了缺失的索引：
   - dd_events: 添加了events_uuid和events_dduuid索引
   - dd_events_todouser: 添加了完整的索引结构
   - dd_todo_tasks: 添加了creator_dd_user_id和complete_staffer_id索引

4. 修复了字段注释：
   - dd_events_todouser: complete_time注释修正为"完成时间"

5. 添加了初始数据：
   - 4个预定义事件配置
   - 4个消息模板

表结构现在完全正确，可以直接执行创建。
*/
