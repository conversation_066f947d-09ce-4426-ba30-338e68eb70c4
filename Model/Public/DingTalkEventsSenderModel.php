<?php

namespace Model\PublicModel;

/**
 * 钉钉事件消息发送器类 - 基于新数据库表结构
 * 
 * 负责实际的消息发送和钉钉API交互
 * 支持多种消息类型和待办任务创建
 */

class DingTalkEventsSenderModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = '';
    public $result = array();
    
    // 钉钉消息中台域名
    const MESSAGE_DOMAIN = 'http://**************:8083';
    
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * 发送消息（基于事件配置）
     * 
     * @param array $messageData 消息数据
     * @return bool
     */
    public function sendMessage($messageData)
    {
        try {
            // 验证消息数据
            if (!$this->validateMessageData($messageData)) {
                return false;
            }

            $messageType = $messageData['message_type'];

            switch ($messageType) {
                case 'card':
                    return $this->sendCardMessage($messageData);

                case 'text':
                    return $this->sendTextMessage($messageData);

                case 'markdown':
                    return $this->sendMarkdownMessage($messageData);

                case 'oa':
                    return $this->sendOAMessage($messageData);

                case 'todo':
                    return $this->sendTodoMessage($messageData);

                default:
                    $this->error = true;
                    $this->errortip = "不支持的消息类型: {$messageType}";
                    return false;
            }

        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '发送消息异常: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * 验证消息数据
     */
    private function validateMessageData($messageData)
    {
        // 检查必需字段
        if (empty($messageData['message_type'])) {
            $this->error = true;
            $this->errortip = '消息类型不能为空';
            return false;
        }

        if (empty($messageData['recipients'])) {
            $this->error = true;
            $this->errortip = '接收人不能为空';
            return false;
        }

        // 根据消息类型验证特定字段
        switch ($messageData['message_type']) {
            case 'text':
                if (empty($messageData['content'])) {
                    $this->error = true;
                    $this->errortip = '文本消息内容不能为空';
                    return false;
                }
                break;

            case 'card':
                if (empty($messageData['title']) || empty($messageData['content'])) {
                    $this->error = true;
                    $this->errortip = '卡片消息标题和内容不能为空';
                    return false;
                }
                break;

            case 'markdown':
                if (empty($messageData['title']) || empty($messageData['content'])) {
                    $this->error = true;
                    $this->errortip = 'Markdown消息标题和内容不能为空';
                    return false;
                }
                break;

            case 'oa':
                if (empty($messageData['title']) || empty($messageData['content'])) {
                    $this->error = true;
                    $this->errortip = 'OA消息标题和内容不能为空';
                    return false;
                }
                break;

            case 'todo':
                if (empty($messageData['title']) || empty($messageData['content'])) {
                    $this->error = true;
                    $this->errortip = '待办任务标题和内容不能为空';
                    return false;
                }
                break;

            default:
                $this->error = true;
                $this->errortip = '不支持的消息类型: ' . $messageData['message_type'];
                return false;
        }

        return true;
    }

    /**
     * 直接发送消息（不基于事件）
     * 
     * @param array $messageData 消息数据
     * @return bool
     */
    public function sendDirectMessage($messageData)
    {
        return $this->sendMessage($messageData);
    }
    
    /**
     * 发送卡片消息
     */
    private function sendCardMessage($messageData)
    {
        $url = self::MESSAGE_DOMAIN . '/api/msg/notify/card';

        // 处理跳转URL - 确保URL格式正确
        $buttonUrl = isset($messageData['button_url']) ? $messageData['button_url'] : 'https://www.baidu.com';
        $jumpMode = isset($messageData['config']['jump_mode']) ? $messageData['config']['jump_mode'] : 'external_browser';
        $processedUrl = $this->processJumpUrl($buttonUrl, $jumpMode);

        // 确保URL是有效的
        if (empty($processedUrl) || $processedUrl === '#') {
            $processedUrl = 'dingtalk://dingtalkclient/page/link?url=' . urlencode('https://www.baidu.com') . '&pc_slide=false';
        }

        // 逐个发送给每个用户（避免多用户发送问题）
        $recipients = $messageData['recipients'];
        $allSuccess = true;

        foreach ($recipients as $recipient) {
            $userId = '';
            if (is_array($recipient) && isset($recipient['dd_user_id'])) {
                $userId = $recipient['dd_user_id'];
            } elseif (is_string($recipient)) {
                $userId = $recipient;
            }

            if (empty($userId)) {
                continue;
            }

            // 构建请求数据 - 使用固定的成功格式
            $requestData = array(
                'title' => (string)$messageData['title'],
                'content' => (string)$messageData['content'],
                'card_type' => 1, // 固定使用单按钮卡片
                'userid_list' => $userId, // 单个用户
                'source' => 1,
                'trigger' => 1,
                'service_channel' => 10,
                'categrate' => array(100, 110),
                'record_mode' => 1,
                'card' => array(
                    'single_title' => isset($messageData['button_title']) ? (string)$messageData['button_title'] : '查看详情',
                    'single_url' => $processedUrl
                )
            );

            $result = $this->sendHttpRequest($url, $requestData);
            if (!$result) {
                $allSuccess = false;
            }
        }

        return $allSuccess;
    }
    
    /**
     * 发送文本消息
     */
    private function sendTextMessage($messageData)
    {
        $url = self::MESSAGE_DOMAIN . '/api/msg/notify/text';

        $requestData = array(
            'content' => $messageData['content'],
            'userid_list' => $this->formatRecipients($messageData['recipients']),
            'source' => isset($messageData['config']['source']) ? $messageData['config']['source'] : 1,
            'trigger' => isset($messageData['config']['trigger']) ? $messageData['config']['trigger'] : 1,
            'service_channel' => isset($messageData['config']['service_channel']) ? $messageData['config']['service_channel'] : 10,
            'categrate' => isset($messageData['config']['categrate']) ? $messageData['config']['categrate'] : array(100, 110),
            'record_mode' => isset($messageData['config']['record_mode']) ? $messageData['config']['record_mode'] : 1
        );

        return $this->sendHttpRequest($url, $requestData);
    }
    
    /**
     * 发送Markdown消息
     */
    private function sendMarkdownMessage($messageData)
    {
        $url = self::MESSAGE_DOMAIN . '/api/msg/notify/markdown';

        $requestData = array(
            'title' => $messageData['title'],
            'content' => $messageData['content'],
            'userid_list' => $this->formatRecipients($messageData['recipients']),
            'source' => isset($messageData['config']['source']) ? $messageData['config']['source'] : 1,
            'trigger' => isset($messageData['config']['trigger']) ? $messageData['config']['trigger'] : 1,
            'service_channel' => isset($messageData['config']['service_channel']) ? $messageData['config']['service_channel'] : 10,
            'categrate' => isset($messageData['config']['categrate']) ? $messageData['config']['categrate'] : array(100, 110),
            'record_mode' => isset($messageData['config']['record_mode']) ? $messageData['config']['record_mode'] : 1
        );

        return $this->sendHttpRequest($url, $requestData);
    }
    
    /**
     * 发送OA消息
     */
    private function sendOAMessage($messageData)
    {
        $url = self::MESSAGE_DOMAIN . '/api/msg/notify/oa';

        // 处理跳转URL
        $buttonUrl = isset($messageData['button_url']) ? $messageData['button_url'] : '';
        $jumpMode = isset($messageData['config']['jump_mode']) ? $messageData['config']['jump_mode'] : 'normal';
        $processedUrl = $this->processJumpUrl($buttonUrl, $jumpMode);

        // 分离移动端和PC端URL
        $mobileUrl = $processedUrl;
        $pcUrl = $processedUrl;

        $requestData = array(
            'userid_list' => $this->formatRecipients($messageData['recipients']),
            'source' => isset($messageData['config']['source']) ? $messageData['config']['source'] : 1,
            'trigger' => isset($messageData['config']['trigger']) ? $messageData['config']['trigger'] : 1,
            'service_channel' => isset($messageData['config']['service_channel']) ? $messageData['config']['service_channel'] : 10,
            'categrate' => isset($messageData['config']['categrate']) ? $messageData['config']['categrate'] : array(100, 110),
            'record_mode' => isset($messageData['config']['record_mode']) ? $messageData['config']['record_mode'] : 1,
            'oa' => array(
                'title' => $messageData['title'],
                'content' => $messageData['content']
            )
        );

        // 根据跳转模式设置URL
        if ($jumpMode == 'mobile_only') {
            // 只有移动端链接
            $requestData['oa']['message_url'] = $mobileUrl;
        } elseif ($jumpMode == 'pc_only') {
            // 只有PC端链接
            $requestData['oa']['pc_message_url'] = $pcUrl;
        } else {
            // 同时支持移动端和PC端
            $requestData['oa']['message_url'] = $mobileUrl;
            $requestData['oa']['pc_message_url'] = $pcUrl;
        }

        return $this->sendHttpRequest($url, $requestData);
    }
    
    /**
     * 发送待办任务
     */
    private function sendTodoMessage($messageData)
    {
        $url = self::MESSAGE_DOMAIN . '/api/msg/task/new';
        
        // 计算截止时间（默认24小时后）
        $dueHours = isset($messageData['config']['due_hours']) ? intval($messageData['config']['due_hours']) : 24;
        $dueTime = time() + ($dueHours * 3600);
        
        $requestData = array(
            'source' => isset($messageData['config']['source']) ? $messageData['config']['source'] : 1,
            'trigger' => isset($messageData['config']['trigger']) ? $messageData['config']['trigger'] : 1,
            'service_channel' => isset($messageData['config']['service_channel']) ? $messageData['config']['service_channel'] : 10,
            'categrate' => isset($messageData['config']['categrate']) ? $messageData['config']['categrate'] : array(100, 110),
            'creator' => isset($messageData['config']['creator_dd_user_id']) ? $messageData['config']['creator_dd_user_id'] : $this->getFirstRecipientId($messageData['recipients']),
            'executorIds' => $this->formatTodoRecipients($messageData['recipients']),
            'completion_mode' => isset($messageData['config']['completion_mode']) ? intval($messageData['config']['completion_mode']) : 1,
            'task_info' => array(
                'subject' => $messageData['title'],
                'description' => $messageData['content'],
                'dueTime' => $dueTime * 1000, // 转换为毫秒
                'priority' => isset($messageData['config']['priority']) ? intval($messageData['config']['priority']) : 20,
                'detailUrl' => array(
                    'appUrl' => $messageData['button_url'] ?: 'https://www.example.com/todo',
                    'pcUrl' => $messageData['button_url'] ?: 'https://www.example.com/todo'
                )
            ),
            'auto_send_msg' => isset($messageData['config']['auto_send_msg']) ? intval($messageData['config']['auto_send_msg']) : 1
        );
        
        return $this->sendHttpRequest($url, $requestData);
    }
    
    /**
     * 更新待办任务状态
     * 
     * @param string $taskId 任务ID
     * @param string $ddUserId 操作用户钉钉ID
     * @param bool $isDone 是否完成
     * @return bool
     */
    public function updateTodoStatus($taskId, $ddUserId, $isDone)
    {
        $url = self::MESSAGE_DOMAIN . '/api/msg/task/update';
        
        $requestData = array(
            'taskId' => $taskId,
            'executorIds' => array(
                'user_ids' => array($ddUserId)
            ),
            'done' => $isDone,
            'creator' => array(
                'userId' => $ddUserId
            )
        );
        
        return $this->sendHttpRequest($url, $requestData);
    }
    
    /**
     * 健康检查
     */
    public function healthCheck()
    {
        try {
            $url = self::MESSAGE_DOMAIN . '/api/health';
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'User-Agent: DingTalkEventsSystem/2.0'
            ));
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                $this->error = false;
                $this->errortip = '';
                $this->result = array(
                    'status' => 'healthy',
                    'domain' => self::MESSAGE_DOMAIN,
                    'response_time' => microtime(true)
                );
                return true;
            } else {
                $this->error = true;
                $this->errortip = "健康检查失败，HTTP状态码: {$httpCode}";
                return false;
            }
            
        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = '健康检查异常: ' . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendHttpRequest($url, $data, $method = 'POST')
    {
        try {
            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'User-Agent: DingTalkEventsSystem/2.0'
            ));

            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
            }

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $this->error = true;
                $this->errortip = "HTTP请求失败: {$error}";
                return false;
            }

            if ($httpCode !== 200) {
                $this->error = true;
                $this->errortip = "HTTP请求失败，状态码: {$httpCode}";
                return false;
            }

            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error = true;
                $this->errortip = "响应JSON解析失败: " . json_last_error_msg() . "，原始响应: {$response}";
                return false;
            }

            // 处理钉钉API响应格式
            return $this->handleResponse($result, $data);

        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = "发送HTTP请求异常: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * 格式化接收人列表（用于普通消息）
     */
    private function formatRecipients($recipients)
    {
        $userIds = array();
        foreach ($recipients as $recipient) {
            if (is_array($recipient) && isset($recipient['dd_user_id'])) {
                $userIds[] = $recipient['dd_user_id'];
            } elseif (is_string($recipient)) {
                $userIds[] = $recipient;
            }
        }
        // 返回逗号分隔的字符串，而不是数组
        return implode(',', $userIds);
    }

    /**
     * 格式化待办任务接收人列表（用于待办任务）
     */
    private function formatTodoRecipients($recipients)
    {
        $userIds = array();
        foreach ($recipients as $recipient) {
            if (is_array($recipient) && isset($recipient['dd_user_id'])) {
                $userIds[] = $recipient['dd_user_id'];
            } elseif (is_string($recipient)) {
                $userIds[] = $recipient;
            }
        }
        // 待办任务的executorIds需要返回数组
        return $userIds;
    }

    /**
     * 获取第一个接收人ID（用作创建者）
     */
    private function getFirstRecipientId($recipients)
    {
        foreach ($recipients as $recipient) {
            if (is_array($recipient) && isset($recipient['dd_user_id'])) {
                return $recipient['dd_user_id'];
            } elseif (is_string($recipient)) {
                return $recipient;
            }
        }
        return 'system'; // 默认值
    }

    /**
     * 处理跳转URL
     */
    private function processJumpUrl($url, $jumpMode = 'normal')
    {
        if (empty($url) || $url === '#') {
            return '';
        }

        switch ($jumpMode) {
            case 'external_browser':
                // 强制跳转外部浏览器 - 使用钉钉协议
                return "dingtalk://dingtalkclient/page/link?url=" . urlencode($url) . "&pc_slide=false";

            case 'internal_browser':
                // 内部浏览器打开，URL保持不变
                return $url;

            case 'dingtalk_app':
                // 跳转到钉钉应用内页面
                return $url;

            case 'mini_program':
                // 小程序跳转，需要特殊处理
                return $url;

            default:
                // 默认使用内部浏览器
                return $url;
        }
    }

    /**
     * 处理钉钉API响应
     */
    private function handleResponse($response, $requestData)
    {
        if (!$response) {
            $this->error = true;
            $this->errortip = '响应数据为空';
            return false;
        }

        // 记录消息发送日志
        $this->logMessage($requestData, $response);

        // 根据钉钉API文档，成功响应的结构
        // 钉钉API返回格式：{"code": 200, "msg": "ok", "data": {...}}
        if (isset($response['code']) && $response['code'] == 200) {
            $this->error = false;
            $this->errortip = isset($response['msg']) ? $response['msg'] : '操作成功';
            $this->result = isset($response['data']) ? $response['data'] : $response;
            return true;
        } else {
            $this->error = true;
            $this->errortip = isset($response['msg']) ? $response['msg'] : (isset($response['message']) ? $response['message'] : '操作失败');
            $this->result = $response;
            return false;
        }
    }

    /**
     * 记录消息发送日志
     */
    private function logMessage($requestData, $response)
    {
        try {
            // 生成唯一UUID
            $uuid = $this->generateReqId();

            // 根据数据库表结构映射字段
            $logData = array(
                'from_id' => 0, // 系统发送，暂时设为0
                'uuid' => $uuid,
                'request_data' => json_encode($requestData, JSON_UNESCAPED_UNICODE),
                'response_data' => json_encode($response, JSON_UNESCAPED_UNICODE),
                'success' => (isset($response['code']) && $response['code'] == 200) ? 1 : 0,
                'create_time' => time(),
                'user_list' => json_encode(isset($requestData['userid_list']) ? $requestData['userid_list'] : array()),
                'message_type' => $this->getMessageTypeFromRequest($requestData)
            );

            // 尝试插入日志（如果表存在的话）
            try {
                $this->DataControl->insertData('dd_message_log', $logData);
            } catch (\Exception $e) {
                // 忽略日志插入错误，不影响主要功能
            }

        } catch (\Exception $e) {
            // 忽略日志记录错误
        }
    }

    /**
     * 从请求数据中获取消息类型
     */
    private function getMessageTypeFromRequest($requestData)
    {
        if (isset($requestData['task_info'])) {
            return 'todo';
        } elseif (isset($requestData['title']) && isset($requestData['content'])) {
            return 'card';
        } elseif (isset($requestData['content']) && !isset($requestData['title'])) {
            return 'text';
        } else {
            return 'unknown';
        }
    }

    /**
     * 获取最后错误信息
     */
    public function getLastError()
    {
        return array(
            'error' => $this->error,
            'errortip' => $this->errortip,
            'result' => $this->result
        );
    }

    /**
     * 生成请求ID
     */
    private function generateReqId()
    {
        return 'req_' . date('YmdHis') . '_' . mt_rand(100000, 999999);
    }
}
