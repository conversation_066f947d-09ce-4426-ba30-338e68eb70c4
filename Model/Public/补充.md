
/api/msg/notify/text

成功-普通用例

{
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [100,101],
    "content": "这个是文字测试：{% mock 'ctitle' %}",
    "userid_list": "{{user_id}}"
}

成功-同时多用户发送用例

{
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [100,101],
    "content": "这个是文字测试：{% mock 'ctitle' %}",
    "userid_list": "{{user_ids}}"
}

失败-缺少用户参数用例
{
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [100,101],
    "content": "这个是文字测试：{% mock 'ctitle' %}"
}

失败-缺少来源参数
{
    "trigger": 1,
    "service_channel": 1,
    "categrate": [100,101],
    "content": "这个是文字测试：{% mock 'ctitle' %}",
    "userid_list": "{{user_id}}"
}

/api/msg/notify/oa

成功-普通跳转
{
    "oa": {
        "message_url": "https://www.baidu.com",
        "pc_message_url": "https://www.163.com",
        "title": "您收到新的OA消息:{% mock 'ctitle' %}",
        "content": "点击查看详情>>"
    },
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}

成功-只有移动端链接_没有PC端链接
{
    "oa": {
        "message_url": "https://www.baidu.com",
        "title": "您收到新的OA消息:{% mock 'ctitle' %}",
        "content": "本链接只能移动端打开，不能PC端打开。>>"
    },
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}

成功-PC端+移动端使用浏览器打开相同自有站点
{
    "oa": {
        "message_url": "dingtalk://dingtalkclient/page/link?url={{pc_url|encodeURIComponent}}&pc_slide=false",
        "title": "您收到新的OA消息:{% mock 'ctitle' %}",
        "content": "本链接PC浏览器打开+移动端dingding生态内端打开。"
    },
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}


成功-PC端+移动端使用浏览器打开相同自有站点-自有系统PC跳转
{
    "oa": {
        "pc_message_url": "dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{'http://jdb.wendugroup.com/redirect'|encodeURIComponent}}",
        "message_url": "http://jdb.wendugroup.com",
        "title": "您收到新的OA消息:{% mock 'ctitle' %}",
        "content": "本链接通过中转页打开自由紫铜，PC浏览器打开+移动端dingding生态内端打开。"
    },
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}


成功-PC端使用浏览器+移动端其他链接
{
    "oa": {
        "message_url": "dingtalk://dingtalkclient/page/link?url={{pc_url|encodeURIComponent}}&pc_slide=false",
        "pc_message_url":  "dingtalk://dingtalkclient/page/link?url={{'https://www.hao123.com/?src=from_pc_logon'|encodeURIComponent}}&pc_slide=false",
        "title": "您收到新的OA消息:{% mock 'ctitle' %}",
        "content": "本链接PC浏览器打开+移动端dingding生态内端打开。"
    },
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}

成功-跳转到钉钉应用

{
    "oa": {
        "message_url": "dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{pc_url|encodeURIComponent}}",
        "title": "您收到新的OA消息:{% mock 'ctitle' %}",
        "content": "本链接PC钉钉工作台打开+移动端dingding生态内端打开，跳转页可以获取免登码，自己实现后续的PC跳转"
    },
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}


/api/msg/notify/markdown

成功-带普通链接
{
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [
        100,
        102
    ],
    "userid_list": "{{user_id}}",
    "title": "这里是外面显示的标题: {% mock 'qq' %}",
    "content": "### 领取新的线索  \n  > 咨询师主管给你分配了任务 \n\n  **线索如下**  \n  - [学生A](https://www.baidu.com)  \n  - [学生B](https://www.163.com)"
}

成功-带应用链接
{
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [
        100,
        102
    ],
    "userid_list": "{{user_id}}",
    "title": "这里是外面显示的标题: {% mock 'qq' %}",
    "content": "### 跳转应用测试  \n  > 咨询师主管给你分配了任务 \n\n  **线索如下**  \n  - [学生A](dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{pc_url|encodeURIComponent}})"
}


成功-带图带格式
{
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [
        100,
        102
    ],
    "userid_list": "{{user_id}}",
    "title": "这里是外面显示的标题: {% mock 'qq' %}",
    "content": "### 跳转应用测试  \n  咨询师主管给你分配了任务 \n\n  **线索如下**  \n  ![](http://gips2.baidu.com/it/u=195724436,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960)"
}


/api/msg/notify/card
成功-竖直多按钮卡片
{
    "title": "竖按钮卡片消息测试 {% mock 'qq' %}",
    "content": "这里是卡片消息内容1  \n  *请关注*  \n  竖直排列按钮",
    "card_type": 2,
    "card": {
        "btn_orientation": 0,
        "btn_json_list": [
            {
                "title": "活动1-百度原生",
                "action_url": "https://www.baidu.com"
            },
            {
                "title": "活动2-163",
                "action_url": "https://www.163.com"
            },
            {
                "title": "活动3-百度-PC浏览器跳转",
                "action_url": "dingtalk://dingtalkclient/page/link?url={{pc_url|encodeURIComponent}}&pc_slide=false"
            },
            {
                "title": "活动4-百度-钉钉应用跳转",
                "action_url": "dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{pc_url|encodeURIComponent}}"
            }
        ]
    },
    "source": 1,
    "trigger": 1,
    "service_channel": 2,
    "categrate": [
        100,
        101
    ],
    "userid_list": "{{user_id}}"
}


成功-整体单卡片-普通链接
{
    "source": 1,
    "trigger": 1,
    "service_channel": 2,
    "categrate": [200,202],
    "userid_list": "{{user_id}}",
    "title": "测试单卡片{% mock 'qq' %}",
    "content": "![](http://gips2.baidu.com/it/u=195724436,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960)  \n  2024年教育行业整体低迷，什么原因导致市场偏差？",
    "card_type": 1,
    "card": {
        "single_title": "去看看",
        "single_url": "{{pc_url}}"
    }
}


成功-整体单卡片-带跳转PC浏览器链接
{
    "source": 1,
    "trigger": 1,
    "service_channel": 2,
    "categrate": [200,202],
    "userid_list": "{{user_id}}",
    "title": "测试单卡片跳转PC {% mock 'qq' %}",
    "content": "2024年教育行业整体低迷，什么原因导致市场偏差？",
    "card_type": 1,
    "card": {
        "single_title": "去看看",
        "single_url": "dingtalk://dingtalkclient/page/link?url={{pc_url|encodeURIComponent}}&pc_slide=false"
    }
}

成功-横直多按钮卡片-带图多连接
{
    "title": "横按钮卡片消息测试 {% mock 'qq' %}",
    "content": "![](http://gips2.baidu.com/it/u=195724436,3554684702&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960)  \n  这里是卡片消息内容1  \n  **请关注**  \n  横直排列按钮",
    "card_type": 2,
    "card": {
        "btn_orientation": 1,
        "btn_json_list": [
            {
                "title": "百度原生",
                "action_url": "https://www.baidu.com"
            },
            {
                "title": "百度-PC浏览器跳转",
                "action_url": "dingtalk://dingtalkclient/page/link?url={{pc_url|encodeURIComponent}}&pc_slide=false"
            },
            {
                "title": "钉钉应用跳转",
                "action_url": "dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{pc_url|encodeURIComponent}}"
            }
        ]
    },
    "source": 1,
    "trigger": 1,
    "service_channel": 1,
    "categrate": [
        200,
        201
    ],
    "userid_list": "{{user_id}}"
}


/api/msg/task/new
成功-普通链接-紧急
{
    "task_info": {
        "subject": "请审批新的教务单据，单据号: {% mock 'qq' %}",
        "description": "这里是备注文字",
        "priority": 30,
        "detailUrl": {
            "appUrl": "https://www.baidu.com",
            "pcUrl": "https://www.baidu.com"
        }
    },
    "creator": "{{user_id}}",
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        100,
        101
    ],
    "executorIds": [
        "{{user_id}}"
    ]
}


成功-普通链接同时发送消息-紧急 Copy
{
    "task_info": {
        "subject": "请审批新的教务单据，单据号: {% mock 'qq' %}",
        "description": "这里是备注文字",
        "priority": 30,
        "detailUrl": {
            "appUrl": "https://www.baidu.com",
            "pcUrl": "https://www.baidu.com"
        }
    },
    "auto_send_msg": 1,
    "creator": "{{user_id}}",
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        100,
        101
    ],
    "executorIds": [
        "{{user_id}}"
    ]
}


成功-普通链接-低优先级-带截止时间
{
    "task_info": {
        "subject": "请审批新的教务单据，单据号: {% mock 'qq' %}",
        "description": "这里是备注文字",
        "priority": 10,
        "dueTime": 1727682228000,
        "detailUrl": {
            "appUrl": "https://www.baidu.com",
            "pcUrl": "https://www.baidu.com"
        }
    },
    "creator": "{{user_id}}",
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        100,
        101
    ],
    "executorIds": [
        "{{user_id}}"
    ]
}

成功-应用链接-低优先级-带截止时间

{
    "task_info": {
        "subject": "请审批新的教务单据，单据号: {% mock 'qq' %}",
        "description": "这里是备注文字",
        "priority": 10,
        "dueTime": 1727682228000,
        "detailUrl": {
            "appUrl": "dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{pc_url|encodeURIComponent}}",
            "pcUrl": "dingtalk://dingtalkclient/action/openapp?corpid={{corpid}}&container_type=work_platform&app_id=0_{{agent_id}}&redirect_type=jump&redirect_url={{pc_url|encodeURIComponent}}"
        }
    },
    "creator": "{{user_id}}",
    "source": 1,
    "trigger": 2,
    "service_channel": 1,
    "categrate": [
        100,
        101
    ],
    "executorIds": [
        "{{user_id}}"
    ]
}

/api/msg/task/update
成功-完成待办
{
    "taskId": "task34b31e05d6502a4e3611a36a012b8ea6",
    "executorIds": {
        "user_ids": [
            "{{user_id}}"
        ]
    },
    "done": true,
    "creator": {
        "userId": "{{user_id}}"
    }
}
