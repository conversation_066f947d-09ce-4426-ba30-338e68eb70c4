# 钉钉事件消息系统 v2.0 - 完整文档

## 📋 目录
1. [系统概述](#系统概述)
2. [核心架构](#核心架构)
3. [数据库表结构](#数据库表结构)
4. [快速开始](#快速开始)
5. [事件配置](#事件配置)
6. [消息模板](#消息模板)
7. [API接口详解](#api接口详解)
8. [使用示例](#使用示例)
9. [跳转模式配置](#跳转模式配置)
10. [故障排查](#故障排查)

## 🎯 系统概述

钉钉事件消息系统是一个基于事件驱动的消息通知架构，支持多种消息类型的统一发送和管理。

### ✨ 核心特性
- 🎯 **事件驱动**: 所有消息都通过事件机制触发
- 📝 **模板化**: 支持消息模板和变量替换
- 🔄 **多消息类型**: 卡片、文本、Markdown、OA、待办任务
- 🌐 **跳转控制**: 支持外部浏览器、内部浏览器等跳转模式
- 📊 **完整日志**: 记录事件触发、发送结果、用户明细
- ⚡ **高可用**: 支持失败重试和错误处理
- 🔒 **数据验证**: 完整的消息数据验证机制

## 核心架构

```
业务系统 → 事件触发 → 模板渲染 → 消息发送 → 钉钉客户端
    ↓           ↓           ↓           ↓           ↓
  业务逻辑   事件配置   消息模板   消息中台API   用户接收
```

### 主要组件

1. **DingTalkEventsModel.php** - 事件管理核心类
2. **DingTalkEventsSenderModel.php** - 消息发送处理类
3. **数据库表** - 存储事件配置、模板、日志等
4. **消息中台API** - 外部钉钉消息服务

## 数据库表结构

### 表结构总览

钉钉事件消息系统包含以下6个核心数据表：

| 表名 | 作用 | 说明 |
|------|------|------|
| **dd_config_event** | 事件配置表 | 存储事件的基本配置信息 |
| **dd_events** | 事件日志表 | 记录每次事件触发的详细日志 |
| **dd_events_misuser** | 消息用户明细表 | 记录消息发送给哪些用户及阅读状态 |
| **dd_events_todouser** | 待办用户明细表 | 记录待办任务分配给哪些用户及完成状态 |
| **dd_todo_tasks** | 钉钉待办任务表 | 存储待办任务的详细信息 |
| **dd_message_template** | 消息模板表 | 存储各种消息类型的模板 |

### 表关系说明

```
dd_config_event (事件配置)
    ↓ (通过 event_code 关联)
dd_events (事件日志)
    ↓ (通过 events_id 关联)
    ├── dd_events_misuser (消息用户明细)
    ├── dd_events_todouser (待办用户明细)
    └── dd_todo_tasks (待办任务)

dd_message_template (消息模板)
    ↓ (通过 template_code = event_code 关联)
dd_config_event (事件配置)
```

**关联关系**：
- `dd_config_event.event_code` ← → `dd_message_template.template_code`
- `dd_config_event.event_code` ← → `dd_events.event_code`
- `dd_events.events_id` ← → `dd_events_misuser.events_id`
- `dd_events.events_id` ← → `dd_events_todouser.events_id`
- `dd_events.events_id` ← → `dd_todo_tasks.events_id`

## 🚀 快速开始

### 1. 数据库初始化

执行数据库初始化脚本：

```sql
-- 执行 Model/Public/DingTalkEvents_Database_Fixed.sql
-- 该脚本会创建所有必需的表和初始数据
```

### 2. 基本使用示例

```php
// 引入钉钉事件消息类
require_once 'Model/Public/DingTalkEventsModel.php';
$dingTalkEvents = new \Model\PublicModel\DingTalkEventsModel();

// 构建事件数据
$eventData = array(
    'title' => '测试消息',
    'content' => '这是一条测试消息',
    'detail_url' => 'https://www.example.com',
    'create_time' => date('Y-m-d H:i:s')
);

// 构建接收人
$recipients = array(
    array(
        'dd_user_id' => '619268940',
        'staffer_id' => 0,
        'name' => '张三'
    )
);

// 触发事件（使用已配置的事件）
$result = $dingTalkEvents->triggerEvent('general_card', $eventData, $recipients);

if ($result) {
    echo "消息发送成功！";
} else {
    $error = $dingTalkEvents->getLastError();
    echo "消息发送失败：" . $error['errortip'];
}
```

### 3. 支持的消息类型

| 消息类型 | 说明 | 特点 |
|---------|------|------|
| **card** | 卡片消息 | 支持标题、内容、按钮，可点击跳转 |
| **text** | 文本消息 | 纯文本内容，简单直接 |
| **markdown** | Markdown消息 | 支持Markdown格式，富文本显示 |
| **oa** | OA消息 | 办公消息格式，支持PC和移动端链接 |
| **todo** | 待办任务 | 可创建待办任务，支持完成状态跟踪 |

### 1. dd_config_event - 事件配置表
```sql
CREATE TABLE `dd_config_event` (
  `event_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `event_code` varchar(50) NOT NULL COMMENT '事件代码',
  `event_name` varchar(100) NOT NULL COMMENT '事件名称',
  `event_description` text COMMENT '事件描述',
  `event_module` varchar(50) DEFAULT NULL COMMENT '所属模块',
  `event_trigger_config` text COMMENT '触发条件配置(JSON)',
  `event_status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `message_type` enum('card','text','markdown','oa','todo') DEFAULT 'card' COMMENT '消息类型',
  `event_recipient_rules` text COMMENT '接收人规则(JSON)',
  `event_send_method` enum('direct','queue','delay') DEFAULT 'direct' COMMENT '发送方式',
  `event_send_params` text COMMENT '发送参数(JSON)',
  `event_priority` tinyint(1) DEFAULT '2' COMMENT '优先级:1高,2中,3低',
  `event_delay_seconds` int(11) DEFAULT '0' COMMENT '延迟发送秒数',
  `event_iscreate_todo` tinyint(1) DEFAULT '0' COMMENT '是否创建待办:0否,1是',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`event_id`),
  UNIQUE KEY `event_code` (`event_code`)
) COMMENT='钉钉事件配置表';
```

### 2. dd_message_template - 消息模板表
```sql
CREATE TABLE `dd_message_template` (
  `template_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `message_type` enum('card','text','markdown','oa','todo') NOT NULL COMMENT '消息类型',
  `template_title` varchar(200) DEFAULT NULL COMMENT '消息标题',
  `template_content` text COMMENT '消息内容',
  `template_button_title` varchar(50) DEFAULT NULL COMMENT '按钮标题',
  `template_button_url` varchar(500) DEFAULT NULL COMMENT '按钮链接',
  `template_source` tinyint(1) DEFAULT '1' COMMENT '来源:1系统,2自定义',
  `template_service_channel` varchar(20) DEFAULT '10' COMMENT '服务渠道',
  `template_categrate` varchar(100) DEFAULT '[100,110]' COMMENT '分类',
  `template_status` tinyint(1) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  UNIQUE KEY `template_code` (`template_code`)
) COMMENT='钉钉消息模板表';
```

### 3. dd_events - 事件日志表
```sql
CREATE TABLE `dd_events` (
  `events_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `event_code` varchar(50) NOT NULL COMMENT '事件代码',
  `events_uuid` varchar(100) NOT NULL COMMENT '事件UUID',
  `events_dduuid` varchar(100) DEFAULT NULL COMMENT '钉钉返回UUID',
  `events_data` text COMMENT '事件数据(JSON)',
  `events_send_results` text COMMENT '发送结果(JSON)',
  `events_create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `events_process_status` tinyint(1) DEFAULT '0' COMMENT '处理状态:0处理中,1成功,2失败',
  `events_status` tinyint(1) DEFAULT '0' COMMENT '事件状态',
  `events_range` tinyint(1) DEFAULT '0' COMMENT '事件范围',
  `events_remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `events_error` varchar(500) DEFAULT NULL COMMENT '错误信息',
  PRIMARY KEY (`events_id`),
  KEY `event_code` (`event_code`),
  KEY `events_uuid` (`events_uuid`)
) COMMENT='钉钉事件日志表';
```

### 4. dd_events_misuser - 消息用户明细表
```sql
CREATE TABLE `dd_events_misuser` (
  `misuser_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `events_id` int(11) NOT NULL DEFAULT '0' COMMENT '消息日志ID',
  `staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `dduser_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户钉钉ID',
  `dduser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '用户姓名',
  `misuser_isread` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0-未读 1-已读',
  `misuser_readtime` int(11) NOT NULL DEFAULT '0' COMMENT '阅读时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`misuser_id`),
  UNIQUE KEY `uk_events_staffer` (`events_id`, `staffer_id`),
  KEY `idx_events_id` (`events_id`),
  KEY `idx_staffer_id` (`staffer_id`),
  KEY `idx_dduser_id` (`dduser_id`)
) COMMENT='消息用户明细表';
```

### 5. dd_events_todouser - 待办用户明细表
```sql
CREATE TABLE `dd_events_todouser` (
  `todouser_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `events_id` int(11) NOT NULL DEFAULT '0' COMMENT '消息日志ID',
  `staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `dduser_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户钉钉ID',
  `dduser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '用户姓名',
  `todouser_priority` tinyint(2) NOT NULL DEFAULT '0' COMMENT '优先级：1-较低 2-普通 3-紧急 4-非常紧急',
  `todouser_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态：0-待处理 1-已完成 2-已取消',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间（Unix时间戳）',
  `todouser_isread` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读 0-未读 1-已读',
  `todouser_readtime` int(11) NOT NULL DEFAULT '0' COMMENT '阅读时间',
  `todouser_action` varchar(20) NOT NULL DEFAULT '' COMMENT '操作类型：create-创建 update-更新 complete-完成 cancel-取消 delete-删除',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间（Unix时间戳）',
  PRIMARY KEY (`todouser_id`),
  UNIQUE KEY `uk_events_staffer_todo` (`events_id`, `staffer_id`),
  KEY `idx_events_id_todo` (`events_id`),
  KEY `idx_staffer_id_todo` (`staffer_id`),
  KEY `idx_dduser_id_todo` (`dduser_id`),
  KEY `idx_todouser_status` (`todouser_status`),
  KEY `idx_todouser_action` (`todouser_action`),
  KEY `idx_todouser_priority` (`todouser_priority`),
  KEY `idx_complete_time` (`complete_time`),
  KEY `idx_create_time_todo` (`create_time`)
) COMMENT='待办用户明细表';
```

### 6. dd_todo_tasks - 钉钉待办任务表
```sql
CREATE TABLE `dd_todo_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `events_id` int(11) NOT NULL DEFAULT '0' COMMENT '消息日志ID',
  `source` tinyint(2) NOT NULL DEFAULT '1' COMMENT '系统来源：1-CRM系统 2-教务系统 3-财务系统 99-系统管理',
  `trigger` tinyint(1) NOT NULL DEFAULT '1' COMMENT '触发方式：1-自动触发 2-人工触发',
  `service_channel` int(11) NOT NULL DEFAULT '10' COMMENT '业务通路：10-CRM客户管理 11-CRM销售跟进 20-教务课程管理 21-教务学员管理 30-财务收费管理 31-财务退费管理 990-系统异常报警',
  `categrate` varchar(100) NOT NULL DEFAULT '[100,110]' COMMENT '业务分类JSON：[一级分类,二级分类] 如[100,110]-CRM新客户报名',
  `subject` varchar(200) NOT NULL DEFAULT '' COMMENT '待办标题（最长200字符）',
  `description` text COMMENT '待办详细描述（支持4096字符）',
  `due_time` int(11) NOT NULL DEFAULT '0' COMMENT '截止时间（Unix时间戳）',
  `priority` tinyint(2) NOT NULL DEFAULT '2' COMMENT '优先级：1-较低 2-普通 3-紧急 4-非常紧急',
  `completion_mode` tinyint(1) NOT NULL DEFAULT '1' COMMENT '完成模式：1-任一人完成即可 2-所有人都需完成',
  `creator_staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建者员工ID（关联smc_staffer.staffer_id）',
  `creator_dd_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '创建者钉钉用户ID（关联smc_staffer.dd_user_id）',
  `executor_staffer_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '执行者员工ID列表（逗号分隔，关联smc_staffer.staffer_id）',
  `executor_dd_user_ids` text NOT NULL COMMENT '执行者钉钉用户ID列表（逗号分隔，关联smc_staffer.dd_user_id）',
  `pc_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'PC端处理链接（如：https://crm.system.com/client/123）',
  `mobile_url` varchar(500) NOT NULL DEFAULT '' COMMENT '移动端处理链接（如：https://m.crm.system.com/client/123）',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '待办状态：0-待处理 1-已完成 2-已取消/已删除',
  `auto_send_msg` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否同步发送消息：0-否 1-是',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间（Unix时间戳）',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间（Unix时间戳）',
  `complete_time` int(11) NOT NULL DEFAULT '0' COMMENT '完成时间（Unix时间戳，status=1时有值）',
  `complete_staffer_id` int(11) NOT NULL DEFAULT '0' COMMENT '完成者员工ID（关联smc_staffer.staffer_id）',
  `complete_dd_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '完成者钉钉用户ID（关联smc_staffer.dd_user_id）',
  `business_type` varchar(50) NOT NULL DEFAULT '' COMMENT '业务类型：client-客户 student-学员 order-订单 course-课程',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务数据ID（如客户ID、学员ID等）',
  `company_id` int(11) NOT NULL DEFAULT '0' COMMENT '公司ID（多租户支持）',
  `school_id` int(11) NOT NULL DEFAULT '0' COMMENT '校区ID',
  PRIMARY KEY (`id`),
  KEY `idx_events_id_task` (`events_id`),
  KEY `idx_source_channel` (`source`, `service_channel`),
  KEY `idx_status_priority` (`status`, `priority`),
  KEY `idx_creator_staffer_id` (`creator_staffer_id`),
  KEY `idx_executor_staffer_ids` (`executor_staffer_ids`(100)),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_company_school` (`company_id`, `school_id`),
  KEY `idx_create_time_task` (`create_time`),
  KEY `idx_due_time` (`due_time`),
  KEY `idx_complete_time_task` (`complete_time`),
  KEY `idx_completion_mode` (`completion_mode`),
  KEY `idx_creator_dd_user_id` (`creator_dd_user_id`)
) COMMENT='钉钉待办任务表';
```

## 事件配置

### 配置事件的步骤

#### 1. 插入事件配置
```sql
INSERT INTO `dd_config_event` (
    `event_code`, `event_name`, `event_description`, `event_module`,
    `event_trigger_config`, `event_status`, `message_type`,
    `event_recipient_rules`, `event_send_method`, `event_send_params`,
    `event_priority`, `event_delay_seconds`, `event_iscreate_todo`,
    `create_time`, `update_time`
) VALUES (
    'jindie_error',                                    -- 事件代码
    '金蝶系统异常通知',                                  -- 事件名称
    '金蝶系统保存失败或异常时触发的通知事件',              -- 事件描述
    'jindie',                                         -- 所属模块
    '{"trigger_conditions": ["error_occurred"], "trigger_timing": "immediate"}', -- 触发配置
    1,                                                -- 启用状态
    'card',                                           -- 消息类型：卡片
    '{"type": "custom", "roles": []}',                -- 接收人规则
    'direct',                                         -- 直接发送
    '{"source": 1, "service_channel": 10, "categrate": [100, 110], "jump_mode": "external_browser"}', -- 发送参数
    3,                                                -- 优先级：低
    0,                                                -- 无延迟
    0,                                                -- 不创建待办
    UNIX_TIMESTAMP(),                                 -- 创建时间
    UNIX_TIMESTAMP()                                  -- 更新时间
);
```

#### 2. 事件配置字段说明

| 字段 | 说明 | 示例值 |
|------|------|--------|
| event_code | 事件唯一标识 | `jindie_error` |
| message_type | 消息类型 | `card`, `text`, `markdown`, `oa`, `todo` |
| event_send_params | 发送参数JSON | `{"source": 1, "jump_mode": "external_browser"}` |
| event_iscreate_todo | 是否创建待办任务 | `0`不创建, `1`创建 |

#### 3. 跳转模式配置
在 `event_send_params` 中配置 `jump_mode`：
- `external_browser` - 外部浏览器打开
- `internal_browser` - 钉钉内置浏览器打开  
- `dingtalk_app` - 钉钉应用内打开
- `mini_program` - 小程序打开

## 消息模板

### 创建消息模板

#### 1. 插入模板配置
```sql
INSERT INTO `dd_message_template` (
    `template_name`, `template_code`, `message_type`,
    `template_title`, `template_content`, `template_button_title`, `template_button_url`,
    `template_source`, `template_service_channel`, `template_categrate`,
    `template_status`, `create_time`, `update_time`
) VALUES (
    '金蝶系统异常通知模板',                              -- 模板名称
    'jindie_error',                                   -- 模板代码（与事件代码对应）
    'card',                                           -- 消息类型
    '金蝶保存失败通知',                                 -- 消息标题
    '**您提交的退费订单异常**\n\n- **订单号：** {refund_pid}\n- **校区:** {school_name}\n- **异常原因:** {error_message}\n\n**请及时处理并重新发起流程**', -- 消息内容
    '查看详情',                                        -- 按钮标题
    '{detail_url}',                                   -- 按钮链接
    1,                                                -- 系统模板
    '10',                                             -- 服务渠道
    '[100,110]',                                      -- 分类
    1,                                                -- 启用状态
    UNIX_TIMESTAMP(),                                 -- 创建时间
    UNIX_TIMESTAMP()                                  -- 更新时间
);
```

#### 2. 模板变量说明

模板支持变量替换，使用 `{变量名}` 格式：

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| {refund_pid} | 退费单号 | `21819DFURDI250731CZGSXA` |
| {school_name} | 校区名称 | `上海江桥万达校` |
| {error_message} | 错误信息 | `字段"申请人"是必填项` |
| {detail_url} | 详情链接 | `https://erp.example.com/refund/detail/xxx` |

#### 3. 不同消息类型的模板格式

**卡片消息 (card)**
```
template_title: 消息标题
template_content: 支持Markdown格式的内容
template_button_title: 按钮文字
template_button_url: 跳转链接
```

**文本消息 (text)**
```
template_content: 纯文本内容
其他字段可为空
```

**Markdown消息 (markdown)**
```
template_title: 消息标题
template_content: Markdown格式内容
```

**OA消息 (oa)**
```
template_title: 消息标题
template_content: 消息内容
template_button_title: 按钮文字
template_button_url: 跳转链接
```

**待办任务 (todo)**
```
template_title: 待办标题
template_content: 待办描述
template_button_title: 按钮文字（可选）
template_button_url: 处理链接
```

## API接口详解

### 核心类：DingTalkEventsModel

#### 1. 触发事件 - triggerEvent()

**方法签名**
```php
public function triggerEvent($eventCode, $eventData, $customRecipients = array())
```

**参数说明**
- `$eventCode` (string) - 事件代码，对应 dd_config_event.event_code
- `$eventData` (array) - 事件数据，用于模板变量替换
- `$customRecipients` (array) - 自定义接收人列表

**接收人格式**
```php
$customRecipients = array(
    array(
        'dd_user_id' => '619268940',    // 钉钉用户ID
        'staffer_id' => 1001,           // 员工ID（可选）
        'name' => '张三'                // 用户姓名（可选）
    ),
    // ... 更多接收人
);
```

**返回值**
- `true` - 事件触发成功
- `false` - 事件触发失败，可通过 `getLastError()` 获取错误信息

#### 2. 获取错误信息 - getLastError()

```php
public function getLastError()
```

**返回格式**
```php
array(
    'error' => true,
    'errortip' => '错误描述',
    'result' => false
)
```

#### 3. 获取用户待办任务 - getUserTodoList()

```php
public function getUserTodoList($ddUserId, $page = 1, $limit = 20)
```

### 核心类：DingTalkEventsSenderModel

#### 1. 发送消息 - sendMessage()

**方法签名**
```php
public function sendMessage($messageData)
```

**消息数据格式**
```php
$messageData = array(
    'message_type' => 'card',                    // 消息类型
    'title' => '消息标题',                       // 标题
    'content' => '消息内容',                     // 内容
    'button_title' => '查看详情',                // 按钮标题
    'button_url' => 'https://example.com',      // 按钮链接
    'recipients' => array(                       // 接收人列表
        array(
            'dd_user_id' => '619268940',
            'staffer_id' => 0,
            'name' => '用户名'
        )
    ),
    'events_uuid' => 'evt_xxx',                 // 事件UUID
    'config' => array(                          // 配置参数
        'source' => 1,
        'service_channel' => 10,
        'categrate' => array(100, 110),
        'jump_mode' => 'external_browser'
    )
);
```

## 使用示例

### 示例1：金蝶异常通知

#### 1. 业务代码中触发事件

```php
// 在 JindieModel.php 中
private function sendDingTalkNotice($refundPid, $errorMessage)
{
    try {
        // 获取退费单信息
        $sql = "SELECT a.*, b.school_cnname FROM smc_refund_order a
                LEFT JOIN smc_school b ON b.school_id = a.school_id
                WHERE a.refund_pid = '{$refundPid}' LIMIT 1";
        $orderOne = $this->DataControl->selectOne($sql);

        if (!$orderOne) {
            return;
        }

        // 构建事件数据
        $eventData = array(
            'refund_pid' => $refundPid,
            'school_name' => $orderOne['school_cnname'],
            'error_message' => $errorMessage,
            'detail_url' => "https://erp.example.com/refund/detail/" . $refundPid,
            'business_type' => 'jindie_refund_error',
            'business_id' => 0,
            'create_time' => date('Y-m-d H:i:s')
        );

        // 构建接收人
        $recipients = array('619268940', '619268929', '619268935');
        $eventRecipients = array();
        foreach ($recipients as $ddUserId) {
            $eventRecipients[] = array(
                'dd_user_id' => $ddUserId,
                'staffer_id' => 0,
                'name' => '系统管理员'
            );
        }

        // 触发事件
        require_once dirname(__FILE__) . '/../Public/DingTalkEventsModel.php';
        $dingTalkEvents = new \Model\PublicModel\DingTalkEventsModel();
        $result = $dingTalkEvents->triggerEvent('jindie_error', $eventData, $eventRecipients);

        // 记录日志
        if ($result) {
            error_log("钉钉通知发送成功 - 退费单: {$refundPid}");
        } else {
            $errorInfo = $dingTalkEvents->getLastError();
            error_log("钉钉通知发送失败 - 退费单: {$refundPid}, 错误: " . $errorInfo['errortip']);
        }

    } catch (\Exception $e) {
        error_log("钉钉通知异常 - 退费单: {$refundPid}, 异常: " . $e->getMessage());
    }
}
```

#### 2. 在金蝶保存失败时调用

```php
// 在 saveExpenseReimbursement() 方法中
if (!$responseArray['Result']['ResponseStatus']['IsSuccess']) {
    // 记录失败日志
    $errorMsg = isset($responseArray['Result']['ResponseStatus']['Errors'][0]['Message'])
        ? $responseArray['Result']['ResponseStatus']['Errors'][0]['Message']
        : '保存失败';
    $this->endApiLog($responseArray, false, $errorMsg);

    // 发送钉钉失败通知
    $this->sendDingTalkNotice($request['refund_pid'], $errorMsg);

    $this->error = true;
    $this->errortip = $errorMsg;
    return false;
}
```

### 示例2：创建CRM新客户通知

#### 1. 配置事件
```sql
INSERT INTO `dd_config_event` VALUES (
    NULL, 'crm_new_client', 'CRM新客户报名', '客户首次报名时触发的通知事件', 'crm',
    '{"trigger_conditions": ["client_status=new"], "trigger_timing": "immediate"}',
    1, 'card', '{"type": "role", "roles": ["sales_manager", "sales_staff"]}',
    'direct', '{"source": 1, "service_channel": 10, "categrate": [100, 110]}',
    2, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
);
```

#### 2. 配置模板
```sql
INSERT INTO `dd_message_template` VALUES (
    NULL, 'CRM新客户报名通知模板', 'crm_new_client', 'card',
    '新客户报名通知',
    '**有新客户报名啦！**\n\n- **客户姓名：** {client_name}\n- **联系电话：** {client_phone}\n- **报名课程：** {course_name}\n- **校区：** {school_name}\n\n**请及时跟进处理**',
    '查看客户详情', '{detail_url}',
    1, '10', '[100,110]', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
);
```

#### 3. 业务代码触发
```php
// 在客户报名成功后
$eventData = array(
    'client_id' => $clientId,
    'client_name' => $clientName,
    'client_phone' => $clientPhone,
    'course_name' => $courseName,
    'school_name' => $schoolName,
    'detail_url' => "https://crm.example.com/client/detail/" . $clientId,
    'create_time' => date('Y-m-d H:i:s')
);

$dingTalkEvents = new \Model\PublicModel\DingTalkEventsModel();
$result = $dingTalkEvents->triggerEvent('crm_new_client', $eventData);
```

## 跳转模式配置

### 1. 外部浏览器跳转 (推荐)

**配置方式**
```json
{
    "source": 1,
    "service_channel": 10,
    "categrate": [100, 110],
    "jump_mode": "external_browser"
}
```

**生成的URL格式**
```
dingtalk://dingtalkclient/page/link?url=https%3A//example.com&pc_slide=false
```

### 2. 钉钉内置浏览器跳转

**配置方式**
```json
{
    "jump_mode": "internal_browser"
}
```

**URL保持原样**
```
https://example.com
```

### 3. 钉钉应用内跳转

**配置方式**
```json
{
    "jump_mode": "dingtalk_app"
}
```

## 系统逻辑串联

### 完整流程图

```
1. 业务触发
   ↓
2. 调用 triggerEvent()
   ↓
3. 获取事件配置 (dd_config_event)
   ↓
4. 确定接收人
   ↓
5. 创建事件日志 (dd_events)
   ↓
6. 获取消息模板 (dd_message_template)
   ↓
7. 渲染消息内容 (变量替换)
   ↓
8. 发送消息 (调用消息中台API)
   ↓
9. 更新事件日志 (发送结果)
   ↓
10. 创建消息用户明细 (dd_events_misuser)
   ↓
11. 创建待办任务 (dd_todo_tasks，如果配置了)
   ↓
12. 创建待办用户明细 (dd_events_todouser，如果创建了待办)
```

### 关键逻辑说明

#### 1. 模板获取优先级
```php
// 1. 首先尝试获取事件特定模板
$sql = "SELECT * FROM dd_message_template
        WHERE template_code = '{$eventCode}' AND template_status = 1";

// 2. 如果没有找到，尝试获取通用模板
$templateCode = 'general_' . $messageType;
$sql = "SELECT * FROM dd_message_template
        WHERE template_code = '{$templateCode}' AND template_status = 1";

// 3. 最后使用默认模板
$sql = "SELECT * FROM dd_message_template
        WHERE message_type = '{$messageType}' AND template_status = 1
        ORDER BY template_id ASC LIMIT 1";
```

#### 2. 变量替换逻辑
```php
foreach ($eventData as $key => $value) {
    $placeholder = '{' . $key . '}';
    $title = str_replace($placeholder, $value, $title);
    $content = str_replace($placeholder, $value, $content);
    $buttonTitle = str_replace($placeholder, $value, $buttonTitle);
    $buttonUrl = str_replace($placeholder, $value, $buttonUrl);
}
```

#### 3. 跳转URL处理
```php
switch ($jumpMode) {
    case 'external_browser':
        return "dingtalk://dingtalkclient/page/link?url=" . urlencode($url) . "&pc_slide=false";
    case 'internal_browser':
        return $url;
    default:
        return $url;
}
```

## 故障排查

### 常见问题及解决方案

#### 1. 消息发送失败
**检查步骤**
1. 查看 `dd_events` 表中的 `events_error` 字段
2. 检查事件配置是否正确 (`dd_config_event`)
3. 验证模板是否存在 (`dd_message_template`)
4. 确认接收人钉钉ID是否正确

**调试SQL**
```sql
-- 查看最近的事件日志
SELECT * FROM dd_events ORDER BY events_id DESC LIMIT 10;

-- 查看失败的事件
SELECT * FROM dd_events WHERE events_process_status = 2;

-- 查看事件配置
SELECT * FROM dd_config_event WHERE event_code = 'jindie_error';

-- 查看消息用户明细
SELECT * FROM dd_events_misuser WHERE events_id = 101;

-- 查看待办用户明细
SELECT * FROM dd_events_todouser WHERE events_id = 101;

-- 查看待办任务详情
SELECT * FROM dd_todo_tasks WHERE events_id = 101;
```

#### 2. 模板变量未替换
**可能原因**
1. 事件数据中的键名与模板变量名不匹配
2. 模板获取失败
3. 变量替换逻辑异常

**解决方法**
```php
// 调试事件数据和模板
error_log("Event Data: " . json_encode($eventData));
error_log("Template Content: " . $template['template_content']);
```

#### 3. 跳转不正确
**检查配置**
```sql
-- 查看事件发送参数
SELECT event_send_params FROM dd_config_event WHERE event_code = 'jindie_error';
```

**确保包含正确的跳转模式**
```json
{"jump_mode": "external_browser"}
```

### 调试接口

系统提供了多个调试接口：

1. **检查事件配置** - `/DingTalkEventsTest/checkJindieErrorConfigAction`
2. **调试事件数据** - `/DingTalkEventsTest/debugTodoDataAction?events_id=XXX`
3. **测试消息发送** - `/DingTalkEventsTest/testGeneralMessageAction`
4. **运行完整测试** - `/DingTalkEventsTest/runAllTestsAction`

### 性能优化建议

1. **数据库索引**
   - 在 `event_code` 字段上建立索引
   - 在 `events_uuid` 字段上建立索引
   - 在 `dd_user_id` 字段上建立索引

2. **缓存策略**
   - 缓存事件配置信息
   - 缓存消息模板内容

3. **异步处理**
   - 对于非紧急消息，可以使用队列异步发送
   - 配置 `event_send_method = 'queue'`

## 📚 附录

### 系统文件结构

```
Model/Public/
├── DingTalkEventsModel.php              # 事件管理核心类
├── DingTalkEventsSenderModel.php        # 消息发送处理类
├── DingTalkEvents_Database_Fixed.sql    # 数据库初始化脚本
└── DingTalk_Events_System_Documentation.md  # 本文档
```

### 核心类说明

| 类名 | 文件 | 作用 |
|------|------|------|
| **DingTalkEventsModel** | DingTalkEventsModel.php | 事件管理、模板处理、数据库操作 |
| **DingTalkEventsSenderModel** | DingTalkEventsSenderModel.php | 消息发送、API调用、格式处理 |

### 消息中台API端点

| 消息类型 | API端点 | 说明 |
|---------|---------|------|
| 文本消息 | `/api/msg/notify/text` | 发送纯文本消息 |
| 卡片消息 | `/api/msg/notify/card` | 发送可点击的卡片消息 |
| Markdown消息 | `/api/msg/notify/markdown` | 发送Markdown格式消息 |
| OA消息 | `/api/msg/notify/oa` | 发送办公消息 |
| 待办任务 | `/api/msg/task/new` | 创建待办任务 |

### 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| **v2.0** | 2025-08-03 | 完整重构，事件驱动架构，支持5种消息类型 |
| v1.0 | 2024-07-31 | 初始版本，基础消息发送功能 |

### 技术支持

- **开发团队**: 系统开发团队
- **技术栈**: PHP 5.6+, MySQL 5.7+, 钉钉开放平台
- **依赖**: 消息中台系统 (http://116.62.241.132:8083)

---

**📄 文档版本**: v2.0
**🕒 最后更新**: 2025-08-03
**👥 维护者**: 系统开发团队
**📧 技术支持**: 请联系系统管理员
