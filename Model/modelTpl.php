<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model;


class modelTpl
{
    public $DataControl;
    public $AnalyzeControl;
    public $companyOne = array();//操作公司
    public $schoolOne = array();//操作学校

    public function __construct()
    {
        //数据库操作
        $this->DataControl = new \Dbmysql();
        $this->AnalyzeControl = new \Analyzesql();
        $public = Input('request.', '', 'trim,addslashes');
        if (isset($public['company_id']) && $public['company_id'] !== '') {
            $this->companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_language,company_misopen,company_isaddstudent,company_isopenspecialnumber,company_isnointention", "company_id = '{$public['company_id']}'");
        }

        if (isset($public['school_id']) && $public['school_id'] !== '') {
            $this->schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id,companies_id", "school_id = '{$public['school_id']}'");
        }elseif (isset($public['school_branch']) && $public['school_branch'] !== '') {
            $this->schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id,school_id,companies_id", "school_branch = '{$public['school_branch']}'");
            if(!$this->companyOne) {
                $this->companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_language,company_misopen,company_isaddstudent", "company_id = '{$this->schoolOne['company_id']}'");
            }
        }
    }

    //根据班种ID,班别ID,校园ID取课程对应主体
    function getSchoolCourseCompanies($school_id, $coursecat_id = 0, $course_id = 0,$coursepacks_id=0)
    {
        if ($coursecat_id == 0 && $course_id == 0) {
            return false;
        }
        $datawhere = "c.coursecat_id = s.coursecat_id AND s.school_id = '{$school_id}'";
        if ($course_id != 0) {
            $datawhere .= " AND c.course_id = '{$course_id}'";
        }
        if ($coursecat_id != 0) {
            $datawhere .= " AND s.coursecat_id = '{$coursecat_id}'";
        }

        $companiesOne = $this->DataControl->selectOne("SELECT if('{$coursepacks_id}'>0 and s.merge_companies_id>0,s.merge_companies_id,s.companies_id) as companies_id FROM smc_school_coursecat_subject AS s, smc_course AS c WHERE {$datawhere} limit 0,1");


        if ($companiesOne) {
            return $companiesOne;
        } else {
            $companiesOne = $this->DataControl->selectOne("SELECT s.companies_id FROM smc_school AS s WHERE s.school_id = '{$school_id}' limit 0,1");
            return $companiesOne;
        }
    }

    function getStuBalance($student_id, $company_id, $school_id, $companies_id=0,$from=0)
    {


        if($from==0){
            $sql = "select s.student_forwardprice,b.* from smc_student as s,smc_student_balance as b
                    WHERE b.student_id = s.student_id
                    and b.school_id = '{$school_id}'
                    and s.student_id='{$student_id}'
                    and b.companies_id='{$companies_id}'
                    and s.company_id='{$company_id}'";
        }else{
            $sql = "select s.student_id,s.student_forwardprice,sum(student_balance) as student_balance,sum(b.student_withholdbalance) as student_withholdbalance,b.school_id,b.company_id
                from smc_student as s,smc_student_balance as b
                WHERE b.student_id = s.student_id 
                and b.school_id = '{$school_id}' 
                and s.student_id='{$student_id}' 
                and s.company_id='{$company_id}'";
        }


        $stublcOne = $this->DataControl->selectOne($sql);

        if (!$stublcOne && $companies_id>0) {
            $data = array();
            $data['company_id'] = $company_id;
            $data['companies_id'] = $companies_id;
            $data['school_id'] = $school_id;
            $data['student_id'] = $student_id;
            $this->DataControl->insertData("smc_student_balance", $data);

            $stublcOne = $this->DataControl->selectOne($sql);

        }
        return $stublcOne;
    }


    //生成唯一用户标识id -- guid
    function create_guid()
    {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid = substr($charid, 0, 8)
            . substr($charid, 8, 4)
            . substr($charid, 12, 4)
            . substr($charid, 16, 4)
            . substr($charid, 20, 12);// "}"
        return $uuid;
    }

    function create_shortguid()
    {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid = substr($charid, 0, 8)
            . substr($charid, 8, 4)
            . substr($charid, 12, 4);// "}"
        return $uuid;
    }

//$hyphen = chr(45);// "-"
//$uuid = chr(123)// "{"
//    .substr($charid, 0, 8).$hyphen
//    .substr($charid, 8, 4).$hyphen
//    .substr($charid,12, 4).$hyphen
//    .substr($charid,16, 4).$hyphen
//    .substr($charid,20,12)
//    .chr(125);// "}"

    function createStuRandom($student_branch)
    {
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)];
        $rangtime = substr(date("ymdHis", time()), 2);
        $Random = $student_branch . $rangtime . $rangtr;
        return $Random;
    }

    public function LgArraySwitch($cnarray)
    {
        if ($this->companyOne['company_language'] == 'tw') {
            $Model = new \Model\jianfanModel();
            $dataString = $Model->gb2312_big5(json_encode($cnarray, JSON_UNESCAPED_UNICODE));
            $cnarray = json_decode($dataString, true);
            return $cnarray;
        } else {
            return $cnarray;
        }
    }

    public function LgStringSwitch($cnstring)
    {
        if ($this->companyOne['company_language'] == 'tw') {
            $Model = new \Model\jianfanModel();
            $cnstring = $Model->gb2312_big5($cnstring);
            return $cnstring;
        } else {
            return $cnstring;
        }
    }


    public function __call($method, $args)
    {
        echo "unknown method " . $method;
        return false;

    }

    public static $WORK_DAY = [
        1 => ['en' => 'Monday','cn'=>'星期一'],
        2 => ['en' => 'Tuesday','cn'=>'星期二'],
        3 => ['en' => 'Wednesday','cn'=>'星期三'],
        4 => ['en' => 'Thursday','cn'=>'星期四'],
        5 => ['en' => 'Friday','cn'=>'星期五'],
        6 => ['en' => 'Saturday','cn'=>'星期六'],
        0 => ['en' => 'Sunday','cn'=>'星期日']
    ];


    
    /**
     * 生成二维码
     * @param string $content 二维码内容
     * @param string $type 返回类型：base64(返回base64编码) 或 file(返回文件路径)
     * @param int $size 二维码大小
     * @param string $level 纠错级别：L、M、Q、H
     * @return string|false 返回二维码base64编码或文件路径，失败返回false
     */
    function generateQRCode($content, $type = 'base64', $size = 8, $level = 'H') {
        try {
            // 引入二维码生成类
            require_once(ROOT_PATH . 'Core/Classlib/Webqrcode.php');
            
            if ($type == 'base64') {
                // 返回base64编码的图片
                ob_start();
                \QRcode::png($content, false, $level, $size, 2);
                $imageData = ob_get_contents();
                ob_end_clean();
                
                if ($imageData) {
                    $base64 = base64_encode($imageData);
                    return "data:image/png;base64," . $base64;
                }
            } elseif ($type == 'file') {
                // 生成文件并返回路径
                $fileName = 'qrcode_' . md5($content . time()) . '.png';
                $filePath = ROOT_PATH . 'temp/' . $fileName;
                
                // 确保temp目录存在
                if (!is_dir(ROOT_PATH . 'temp/')) {
                    mkdir(ROOT_PATH . 'temp/', 0755, true);
                }
                
                \QRcode::png($content, $filePath, $level, $size, 2);
                
                if (file_exists($filePath)) {
                    return $filePath;
                }
            }
            
            return false;
        } catch (\Exception $e) {
            
            return false;
        }
    }

    /**
     * 生成支付二维码（直接输出到浏览器）
     * @param string $content 二维码内容
     * @param int $size 二维码大小
     * @param string $level 纠错级别
     */
    function outputQRCode($content, $size = 8, $level = 'H') {
        try {
            // 引入二维码生成类
            require_once(ROOT_PATH . 'Core/Classlib/Webqrcode.php');
            
            // 设置输出头
            header('Content-Type: image/png');
            header('Cache-Control: no-cache');
            
            // 直接输出二维码
            \QRcode::png($content, false, $level, $size, 2);
        } catch (\Exception $e) {
            // 错误处理
            header('Content-Type: text/plain');
            echo "二维码生成失败：" . $e->getMessage();
        }
    }


    /**
     * 添加名单状态变更时间
     * @param $company_id
     * @param $school_id
     * @param $client_id
     * @param $timerecord_type 0 错误数据 1 待分配时间 2 已分配时间 3 名单无效时间 4 名单无效审核时间 5 撞单时间
     * @param $fmarketer_id 记录来源人  0 错误数据 >0 名单来源操作人 -1 记录来源程序触发
     * @return void
     */
    function addClientTimerecord($company_id=0,$school_id=0,$client_id=0,$timerecord_type,$fmarketer_id=0,$notes=''){
        $data = array();
        $data['company_id'] = $company_id;
        $data['school_id'] = $school_id;
        $data['client_id'] = $client_id;
        $data['timerecord_type'] = $timerecord_type;
        $data['timerecord_fmarketer_id'] = $fmarketer_id;
        $data['timerecord_from_time'] = time();
        $data['timerecord_from_notes'] = $notes;
        $data['timerecord_ishandle'] = 0;
        $data['timerecord_createtime'] = time();
        $data['timerecord_updatetime'] = time();
        $log_id = $this->DataControl->insertData('crm_client_timerecord',$data);
        if($log_id){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 名单状态变更时间  -- 提醒职工操作后标记接口
     * @param $company_id
     * @param $school_id
     * @param $client_id
     * @param $timerecord_type
     * @param $hmarketer_id
     * @return void
     */
    function updateClientTimerecord($company_id=0,$school_id=0,$client_id=0,$timerecord_type,$hmarketer_id=0){
        $recordOne = $this->DataControl->selectOne("select 1 from crm_client_timerecord where company_id = '{$company_id}' and school_id = '{$school_id}' and client_id = '{$client_id}' and timerecord_type = '{$timerecord_type}' and timerecord_ishandle = '0' limit 0,1 ");
        if($recordOne){
            $pdata = array();
            $pdata['timerecord_ishandle'] = 1;
            $pdata['timerecord_hmarketer_id'] = $hmarketer_id;
            $pdata['timerecord_handle_time'] = time();
            $pdata['timerecord_updatetime'] = time();
            $this->DataControl->updateData('crm_client_timerecord', "client_id='{$client_id}' and audition_isvisit = '0' ", $pdata);
            return true;
        }else{
            return false;
        }
    }

}
