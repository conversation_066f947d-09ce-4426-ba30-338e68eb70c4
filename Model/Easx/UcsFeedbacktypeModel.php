<?php
/**
 * 客诉-分类
 */

namespace Model\Easx;

class UcsFeedbacktypeModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array(), $order_pid = 0)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    /**
     * 必要参数
     * @param $publicarray
     */
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = 1;
            $this->errortip = "企业ID必传1";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }

//        if (isset($publicarray['school_id'])) {
//            $this->school_id = $publicarray['school_id'];
//        } else {
//            $this->error = true;
//            $this->errortip = "学校ID必须传入";
//            $this->result = array();
//            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
//        }

    }

    /**
     * 分类列表 -zjc
     * @param $paramArray
     * @return array
     */
    function feedbacktypeList($paramArray)
    {
//        if(!isset($paramArray['feedbacktype_belongs']) || $paramArray['feedbacktype_belongs']='')
//        {
//            $this->error = 1;
//            $this->errortip = "参数缺失:分类所属";
//            $this->result = array();
//            return false;
//        }
        $datawhere="cf.company_id='{$paramArray['company_id']}'  AND cf.feedbacktype_status=1";
        //是否选了学校
        if(!empty($paramArray['school_id']))
        {
            $datawhere .=" AND cf.feedbacktype_belongs=2";
        }
        //是否选了分类
        if(!empty($paramArray['feedbacktype_catgory']))
        {
            $datawhere .=" AND cf.feedbacktype_catgory='{$paramArray['feedbacktype_catgory']}' ";
        }

        //如果学校存在 归属 学校
        //如果学校不存在 归属 集团

//        $result = [
//            'datalist'=>[
//                [
//                    'feedbacktype_id'=>2,
//                    'feedbacktype_name'=>"投诉教学环境",
//                    'feedbacktype_note'=>"投诉教学环境咋样"
//
//                ]
//            ],
//            'allnum'=>1
//        ];
        $dataList = $this->DataControl->selectClear("SELECT cf.feedbacktype_id,cf.feedbacktype_name,cf.feedbacktype_note 
FROM ucs_code_feedbacktype cf 
WHERE {$datawhere} 
");

        $result = array();
        $result['datalist'] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "暂无分类";
            $this->result = $result;
            return false;
        }



    }


}