<?php
/**
 * 客诉-工单消息
 */

namespace Model\Easx;


class UcsRepairordermessageModel extends UcsCommonModel
{
    /**
     * 消息列表 -zjc
     * @return bool
     */
    function messageList($paramArray)
    {
        $datawhere=" m.staffer_id='{$paramArray['staffer_id']}' ";
        //分页
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $dataList = $this->DataControl->selectClear("
        SELECT m.*,r.repairorder_contactname,r.repairorder_contactmobile,r.repairorder_updatetime   
        FROM ucs_repairorder_message m 
        LEFT JOIN ucs_repairorder r ON m.repairorder_pid=r.repairorder_pid 
        WHERE {$datawhere}
        LIMIT {$pagestart},{$num} 
        ");
        if(!empty($dataList))
        {
            foreach ($dataList as $key =>$value)
            {
                $dataList[$key]['message_createtime'] = date('Y-m-d H:i:s',$value['message_createtime']);
                if(!empty($value['repairorder_updatetime']))
                {
                    $dataList[$key]['repairorder_updatetime'] = date('Y-m-d H:i:s',$value['repairorder_updatetime']);
                }else{
                    $dataList[$key]['repairorder_updatetime'] = '';
                }

            }

        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT COUNT(m.message_id) as datanum 
        FROM ucs_repairorder_message m 
        LEFT JOIN ucs_repairorder r ON m.repairorder_pid=r.repairorder_pid 
        WHERE {$datawhere}
        ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum']+0;
        }else{
            $count = 0;
        }

        $result['datalist'] = is_array($dataList)?$dataList:array();
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "消息列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无数据!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 消息详情 -zjc
     * @param $paramArray
     * @return bool
     */
    function messageDetail($paramArray)
    {
        if(!isset($paramArray['message_id']) || $paramArray['message_id']=='')
        {
            $this->error = 1;
            $this->errortip = "参数缺失:消息ID";
            $this->result = array();
            return false;
        }
        $dataList = $this->DataControl->selectOne("
        SELECT m.*,r.repairorder_contactname,r.repairorder_contactmobile,r.repairorder_updatetime   
        FROM ucs_repairorder_message m 
        LEFT JOIN ucs_repairorder r ON m.repairorder_pid=r.repairorder_pid 
        WHERE m.message_id='{$paramArray['message_id']}' 
        ");
        if(!empty($dataList))
        {
            $dataList['message_createtime'] = date('Y-m-d H:i:s',$dataList['message_createtime']);
            if(!empty($dataList['repairorder_updatetime']))
            {
                $dataList['repairorder_updatetime'] = date('Y-m-d H:i:s',$dataList['repairorder_updatetime']);
            }
        }

        $result['datalist'] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "详情获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "没有找到该消息:(";
            $this->result = $result;
            return false;
        }
    }



}