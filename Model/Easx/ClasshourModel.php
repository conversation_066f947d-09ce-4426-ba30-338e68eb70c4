<?php

namespace Model\Easx;

Class ClasshourModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }


    /**
     * 获取对应课时需要点评的人
     * author: ling
     * 对应接口文档 0001
     */
    function getHourStudent($request)
    {

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '40';
        }
        $datawhere = '1';
        if (isset($request['is_sturemark']) && $request['is_sturemark'] !== '') {
            if ($request['is_sturemark'] == 1) {
                $datawhere .= " and  cs.sturemark_status  = 0 ";
            } elseif ($request['is_sturemark'] == 0) {
                $datawhere .= "  and (cs.sturemark_id is null or cs.sturemark_status = 1 )";
            }

        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            select s.student_cnname,s.student_id,s.student_enname,s.student_img,cs.sturemark_comment,cs.sturemark_id,cs.sturemark_createtime,s.student_branch,s.student_sex,s.student_birthday,cs.sturemark_status,sturemark_picturejson
            from smc_student_hourstudy  as sh
            left join smc_student as s  ON sh.student_id = s.student_id
            left join eas_classhour_sturemark as cs On  cs.student_id = s.student_id and cs.hour_id = sh.hour_id 
        where {$datawhere} and sh.hour_id = '{$request['hour_id']}' and sh.hourstudy_checkin = '1' limit {$pagestart},{$num}
        ";

        $dataList = $this->DataControl->selectClear($sql);


        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$val) {

                $val['sturemark_createtime'] = date("Y-m-d H:i:s", $val['sturemark_createtime']);
                $remarkList = $this->DataControl->selectClear("select * from  eas_classhour_sturemarkstar where sturemark_id='{$val['sturemark_id']}'  ");
                $data_remark = $remarkList == false ? array() : $remarkList;
                if ($data_remark) {
                    $val['remark_star'] = $data_remark;
                } else {
                    $val['remark_star'] = array();
                }
                if ($val['sturemark_id'] !== NUll) {
                    $val['sturemark_status_name'] = $val['sturemark_status'] == 0 ? '已点评' : '已保存';
                } else {
                    $val['sturemark_status'] = -1;
                    $val['sturemark_status_name'] = "未点评";
                }
            }
        }
        $data = array();
        $data['list'] = $dataList;

        $all_num = $this->DataControl->selectOne("select  count(student_id) as  all_num from smc_student_hourstudy as sh  where sh.hour_id = '{$request['hour_id']}' ");
        if ($all_num) {
            $data['num'] = $all_num['all_num'];
        } else {
            $data['num'] = 0;
        }

        return $data;
    }

    /**
     * 获取集团课程的评星模板
     * author: ling
     * 对应接口文档 0001
     */
    function getRemarkTempStart($request)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "class_id,course_id", "hour_id='{$request['hour_id']}'");

        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere = " and cs.sturemarktemp_title like %{$request['keyword']}%";
        }
        $sql = "select cs.sturemarktemp_id,cs.sturemarktemp_title,sturemarktemp_highscore,sturemarktemp_lowscore from eas_code_sturemarktemp as  cs,eas_code_sturemark_tempcourse as  cst where cst.sturemarktemp_id = cs.sturemarktemp_id and  cst.course_id ='{$hourOne['course_id']}' and  cs.company_id ='{$request['company_id']}' and {$datawhere} order  by cs.sturemarktemp_sort ASC ";
        $remarktemp = $this->DataControl->selectClear($sql);
        if (!$remarktemp) {
            $sql = "select cs.sturemarktemp_id,cs.sturemarktemp_title,sturemarktemp_highscore,sturemarktemp_lowscore from eas_code_sturemarktemp as  cs,eas_code_sturemark_tempcourse as  cst where cst.sturemarktemp_id = cs.sturemarktemp_id and  cst.course_id ='0' and  cs.company_id ='{$request['company_id']}' and {$datawhere} order  by cs.sturemarktemp_sort ASC ";
            $remarktemp = $this->DataControl->selectClear($sql);
        }

        if (!$remarktemp) {
            $remarktemp = array();
        } else {
            foreach ($remarktemp as &$val) {
                $personal_capital = $this->DataControl->selectOne("
            select  avg(sturemarkstar_score) as  sturemarkstar_score
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}' and student_id ='{$request['student_id']}'
            ");
                $class_average = $this->DataControl->selectOne("
            select  avg(sturemarkstar_score) as  sturemarkstar_score
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}'
            ");

                $stu_fullstar = $this->DataControl->selectOne("
            select  count(cs.sturemark_id)  as sturemark_num
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}' and student_id ='{$request['student_id']}' and sturemarkstar_score = '{$val['sturemarktemp_highscore']}'
            ");
                $class_fullstar = $this->DataControl->selectOne("
            select  count(cs.sturemark_id)  as sturemark_num
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}' and student_id ='{$request['student_id']}'
            ");

                $val['personal_capita'] = $personal_capital['sturemarkstar_score'] == false ? '0' : round($personal_capital['sturemarkstar_score'], 1);
                $val['class_average'] = $class_average['sturemarkstar_score'] == NULL ? 0 : round($class_average['sturemarkstar_score'], 1);
                $val['stu_fullstar'] = $stu_fullstar['sturemark_num'] == Null ? 0 : round($stu_fullstar['sturemark_num'], 1);
                $val['class_fullstar'] = $class_fullstar['sturemark_num'] == Null ? 0 : round($class_fullstar['sturemark_num'], 1);
            }
        }
        $data = array();
        $data['list'] = $remarktemp;
        return $data;
    }

    /**
     *  获取学员点评的详情
     *
     */

    function getStuHourRemarkOne($request)
    {
        $remarkOne = $this->DataControl->selectOne("select * from eas_classhour_sturemark  where sturemark_id='{$request['sturemark_id']}'  limit 0,1");
        $hourOne = $this->DataControl->selectOne("select * from smc_class_hour where hour_id='{$remarkOne['hour_id']}' limit 0,1");
        if ($remarkOne) {
            $starList = $this->DataControl->selectClear("select * from eas_classhour_sturemarkstar where  sturemark_id='{$request['sturemark_id']}' ");
            if ($starList) {
                foreach ($starList as &$val) {
                    $personal_capital = $this->DataControl->selectOne("
            select  avg(sturemarkstar_score) as  sturemarkstar_score
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}' and student_id ='{$remarkOne['student_id']}'
            ");
                    $class_average = $this->DataControl->selectOne("
            select  avg(sturemarkstar_score) as  sturemarkstar_score
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}'
            ");

                    $stu_fullstar = $this->DataControl->selectOne("
            select  count(cs.sturemark_id)  as sturemark_num
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}' and student_id ='{$remarkOne['student_id']}'
            ");
                    $class_fullstar = $this->DataControl->selectOne("
            select  count(cs.sturemark_id)  as sturemark_num
            from eas_classhour_sturemark as cs
            left join  eas_classhour_sturemarkstar as cr On cs.sturemark_id = cr.sturemark_id
            where sturemarktemp_id = '{$val['sturemarktemp_id']}'  and  class_id='{$hourOne['class_id']}' 
            ");

                    $val['personal_capita'] = $personal_capital['sturemarkstar_score'] == false ? '0' : round($personal_capital['sturemarkstar_score'], 1);
                    $val['class_average'] = $class_average['sturemarkstar_score'] == NULL ? 0 : round($class_average['sturemarkstar_score'], 1);
                    $val['stu_fullstar'] = $stu_fullstar['sturemark_num'] == Null ? 0 : round($stu_fullstar['sturemark_num'], 1);
                    $val['class_fullstar'] = $class_fullstar['sturemark_num'] == Null ? 0 : round($class_fullstar['sturemark_num'], 1);
                }
            }

            $remarkOne['star_list'] = $starList;
        } else {
            $remarkOne = array();
        }

        return $remarkOne;
    }


    /**
     *  获取学员的信息
     *
     */
    function getStudentApi($request)
    {
        $student_list = json_decode(stripslashes($request['student_list']), true);
        $data = array();
        if (is_array($student_list) && count($student_list) > 0) {
            $str_student_id = implode(',', $student_list);
            $studentList = $this->DataControl->selectClear("select student_cnname,student_enname,student_img,student_id,student_branch,student_sex from smc_student where student_id in ({$str_student_id}) ");
            if (!$studentList) {
                $studentList = array();
            }
            $data['list'] = $studentList;
            return $data;
        } else {
            $data['list'] = array();
            return $data;
        }
    }


    /**
     * 获取的点评的评价模板
     * author: ling
     * 对应接口文档 0001
     */
    function getRemarkTemp($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cr.reeamrktemp_title like '%{$request['keyword']}%' or cr.remarktemp_content like '%{$request['keyword']}%')";
        }
        $temp = array();
        $sql = "select  cr.*,crt.remarktemp_type_name from  eas_code_remarktemp  as cr 
                left join eas_code_remarktemp_type as crt ON cr.remarktemp_type_id = crt.remarktemp_type_id
                where cr.company_id='{$request['company_id']}' and cr.remarktemp_status =1 and {$datawhere} and  crt.remarktemp_type_status =1    ";

        $dataList = $this->DataControl->selectClear($sql);


        if (!$dataList) {
            $temp = array();
        } else
            foreach ($dataList as $key => $value) {
                $temp[$value['remarktemp_type_id']]['title'] = $value['remarktemp_type_name'];
                $temp[$value['remarktemp_type_id']]['list'][] = $value;


            }

        $data = array();
        $data['list'] = $temp;
        return $data;
    }

    /**
     * 提交评价
     * author: ling
     * 对应接口文档 0001
     */
    function submitHourRemark($request)
    {
        $remark_id = 0;
        $student_list = json_decode(stripslashes($request['student_list']), true);

        if (!is_array($student_list) || count($student_list) < 1) {
            $this->error = 1;
            $this->errortip = "请选择学员";
            return false;
        }
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "class_id,hour_ischecking,hour_day,hour_starttime,hour_endtime", "hour_id='{$request['hour_id']}'");
        if ($hourOne['hour_ischecking'] <> 1) {
            $this->error = 1;
            $this->errortip = "该课时暂未点名";
            return false;
        }

        if ($student_list) {
            foreach ($student_list as &$value) {
                $data = array();
                $data['hour_id'] = $request['hour_id'];
                $data['staffer_id'] = $this->staffer_id;
                $data['class_id'] = $hourOne['class_id'];
                $data['student_id'] = $value['student_id'];
                $data['sturemark_status'] = $request['sturemark_status'];
                $data['sturemark_comment'] = $request['sturemark_comment'];
                $data['sturemark_picturejson'] = $request['sturemark_picturejson'];
                if ($remarkOne = $this->DataControl->getFieldOne('eas_classhour_sturemark', 'sturemark_id', "hour_id='{$request['hour_id']}' and student_id='{$value['student_id']}'")) {
                    $data['sturemark_updatetime'] = time();
                    $this->DataControl->updateData('eas_classhour_sturemark', "sturemark_id='{$remarkOne['sturemark_id']}'", $data);
                    $remark_id = $remarkOne['sturemark_id'];
                } else {
                    $company = $this->DataControl->getFieldOne("gmc_company", "company_isshowhour", "company_id = '{$request['company_id']}'");
                    $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$value['student_id']}'");
                    $parenter = $this->DataControl->selectOne("
                        SELECT
                            p.parenter_cnname,
                            p.parenter_id 
                        FROM
                            smc_student_family AS f
                            LEFT JOIN smc_parenter AS p ON f.parenter_id = p.parenter_id
                            LEFT JOIN smc_student AS s ON f.student_id = s.student_id
                            JOIN smc_parenter_wxchattoken AS w ON w.parenter_id = f.parenter_id 
                            AND w.company_id = s.company_id 
                            AND w.parenter_wxtoken IS NOT NULL
                        WHERE f.student_id  = '{$value['student_id']}'    
                        GROUP BY
                            f.student_id");
                    $course_id = $this->DataControl->getFieldOne("smc_class", "course_id,class_cnname", "class_id = '{$hourOne['class_id']}'");
                    $coursetime = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_time", "course_id =     '{$course_id['course_id']}' and student_id = '{$value['student_id']}' and school_id = '{$request['school_id']}'");
                    $hourstudy = $this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "student_id =     '{$value['student_id']}' and hour_id = '{$request['hour_id']}'");
                    $course_cnname = $this->DataControl->getFieldOne("smc_course", "course_cnname", "course_id = '{$course_id['course_id']}'");

                    $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '点评完成通知'");


                    if ($company['company_isshowhour'] == '1') {
//                        $a = $student_cnname['student_cnname'].'学员您好，本节课您已到课';
//                        $b = $course_cnname['course_cnname'];
//                        $c = $coursetime['coursebalance_time'];
//                        $d = $hourOne['hour_day'].' '.$hourOne['hour_starttime'].'-'.$hourOne['hour_endtime'];
//                        $e = '扣除1课次';
//                        $f = '点击这里查看评价详情';
//                        $g = "https://scptc.kedingdang.com/ClassReview/DetailsSchedule?hourstudy_id={$hourstudy['hourstudy_id']}&cid={$request['company_id']}";
//                        $wxteModel = new \Model\Api\ZxwxChatModel($parenter['parenter_id'],$value['student_id']);
//                        $wxteModel->TeComment($a,$b,$c,$d,$e,$f,$g,$wxid);
                    } else {
                        $staffer = $this->DataControl->selectOne("select t.teaching_type,s.staffer_cnname from smc_class_hour_teaching as t left join smc_staffer as s on s.staffer_id = t.staffer_id where t.hour_id = '{$request['hour_id']}' and t.staffer_id = '{$this->staffer_id}'");
                        $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$value['student_id']}'");

                        foreach ($parenter as &$val) {
                            if ($isset) {
                                $wxid = $isset['masterplate_wxid'];
                            } else {
                                $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '点评完成通知'");
                                $wxid = $masterplate['masterplate_wxid'];
                            }

                            $hourstudy_id = $this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "student_id = '{$value['student_id']}' and hour_id = '{$request['hour_id']}'");

                            $staffer_cnname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$request['staffer_id']}'");

                            $a = $student_cnname['student_cnname'] . '学员您好，老师已完成课堂点评';
                            $b = $course_cnname['course_cnname'];
                            $c = $staffer_cnname['staffer_cnname'];
                            $d = date("Y年m月d日 H:i");
                            $e = '点击这里即可查看点评详情哦~';
                            $f = "https://scptc.kedingdang.com/ClassReview/DetailsSchedule?hourstudy_id={$hourstudy_id['hourstudy_id']}&s_id={$value['student_id']}";
                            $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $value['student_id']);
                            $wxteModel->Comment($a, $b, $c, $d, $e, $f, $wxid);
                        }


//                        $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$request['company_id']}' and masterplate_name = '学员考勤通知'");
//                        if($isset){
//                            $wxid = $isset['masterplate_wxid'];
//                        }else{
//                            $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '学员考勤通知'");
//                            $wxid = $masterplate['masterplate_wxid'];
//                        }
//
//                        $a = $student_cnname['student_cnname'].'学员您好，本节课已考勤成功';
//                        $b = '正常';
//                        $c = $hourOne['hour_day'].' '.$hourOne['hour_starttime'].'-'.$hourOne['hour_endtime'];
//                        $d = $course_id['class_cnname'];
//                        if($staffer['teaching_type'] == '0'){
//                            $e = $staffer['staffer_cnname'].'（主教老师）';
//                        }else{
//                            $e = $staffer['staffer_cnname'].'（助教老师）';
//                        }
//                        $f = '点此可查看详情哦～';
//                        $g = "https://scptc.kedingdang.com/LookTimetable/timeoutDetail?hour_id={$request['hour_id']}&cid={$request['company_id']}&s_id={$value['student_id']}";
//                        $wxteModel = new \Model\Api\ZxwxChatModel($parenter['parenter_id'],$value['student_id']);
//                        $wxteModel->TeCommentTwo($a,$b,$c,$d,$e,$f,$g,$wxid);
                    }

                    $data['sturemark_createtime'] = time();
                    $remark_id = $this->DataControl->insertData("eas_classhour_sturemark", $data);
                }
                $remarkstar_list = json_decode(stripslashes($request['remarkstar_list']), true);

                if ($remarkstar_list && $remark_id) {
                    $this->DataControl->delData('eas_classhour_sturemarkstar', "sturemark_id='{$remark_id}'");
                    foreach ($remarkstar_list as $val) {
                        $star_data = array();
                        $star_data['sturemarkstar_name'] = $val['sturemarkstar_name'];
                        $star_data['sturemarkstar_score'] = $val['sturemarkstar_score'];
                        $star_data['sturemarktemp_id'] = $val['sturemarktemp_id'];
                        $star_data['sturemark_id'] = $remark_id;
                        $this->DataControl->insertData('eas_classhour_sturemarkstar', $star_data);
                    }
                }


            }
        }

        $this->error = 0;
        $this->errortip = "评价成功";
        return $remark_id;
    }

    /**
     * 撤回点评
     * author: ling
     * 对应接口文档 0001
     */
    function withdrawStuHourRemark($request)
    {
        $sturemarkOne = $this->DataControl->getFieldOne("eas_classhour_sturemark", "sturemark_id,hour_id", "sturemark_id='{$request['sturemark_id']}'");
        if ($sturemarkOne) {
            if ($this->DataControl->delData('eas_classhour_sturemark', "sturemark_id='{$request['sturemark_id']}'")) {
                $this->error = 0;
                $this->errortip = "撤回成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "撤回失败";
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = "查无数据";
            return false;
        }
    }

    /**
     * 获取班级基本信息
     * author: ling
     * 对应接口文档 0001
     */
    function getClassOneApi($request)
    {
        $sql = "select c.class_id,c.father_id,c.class_cnname,class_enname,class_branch,l.school_cnname,
                (select DISTINCT concat(sr.staffer_cnname,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END ) ) from smc_class_teach as th left join smc_staffer as sr on sr.staffer_id=th.staffer_id where th.class_id = c.class_id and th.teach_type = 0 and th.teach_status = 0 limit 1) as staffer_cnname,
                (select DISTINCT concat(sr.staffer_cnname,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END ) ) from smc_class_teach as th left join smc_staffer as sr on sr.staffer_id=th.staffer_id where th.class_id = c.class_id and th.teach_type = 1 and th.teach_status = 0 limit 1) as fu_staffer_cnname,
          (select count(h.hour_id)
              from smc_class_hour as h
              left join smc_course as co ON co.course_id = h.course_id
              left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
              left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
              where h.class_id = c.class_id and h.hour_ischecking <>'-1' and h.hour_iswarming = 0  and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
          ) as hour_allnum,
          (select count(h.hour_id)
              from smc_class_hour as h
              left join smc_course as co ON co.course_id = h.course_id
              left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
              left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
              where h.class_id = c.class_id and CONCAT(h.hour_day,' ',h.hour_endtime) <= NOW() and h.hour_iswarming = 0 and h.hour_ischecking>-1
              and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
          ) as hour_checkingnum,
          (select count(DISTINCT sh.hour_branch) from eas_student_hour as sh where sh.class_id =c.class_id ) as resign_hour_num
          from smc_class as c
          left join smc_school as l ON l.school_id = c.school_id
          where c.class_id='{$request['class_id']}' and c.school_id='{$request['school_id']}'";
        $classOne = $this->DataControl->selectOne($sql);
        if ($classOne) {
            $classOne['no_resign_hour_num'] = $classOne['hour_allnum'] - $classOne['resign_hour_num'];
        } else {
            $classOne = array();
        }
        return $classOne;
    }
}