<?php

namespace Model\Easx;

Class TrainingModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            if(!$this->setPublic($publicarray)){
                return false;
            }
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'], $publicarray['re_postbe_id'])) {
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id, $re_postbe_id=0)
    {

        if($re_postbe_id>0){
            $this->stafferOne = $this->DataControl->selectOne("SELECT
                                                                      s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_mobile,s.account_class,p.post_id,sp.postpart_isteregulator,sp.postpart_istraining
                                                                   FROM
                                                                      smc_staffer as s
                                                                   LEFT JOIN
                                                                      gmc_staffer_postbe as p ON p.staffer_id = s.staffer_id
                                                                   LEFT JOIN
                                                                      smc_school_postpart as sp ON sp.postpart_id = p.postpart_id
                                                                   WHERE
                                                                      s.company_id='{$this->company_id}' AND s.staffer_id = '{$staffer_id}' AND p.postbe_id = '{$re_postbe_id}'");

            if(!$this->stafferOne['postpart_istraining']){
                $this->error = true;
                $this->errortip = "无培训访问权限，请联系管理员开启";
                return false;
            }
        }else{
            $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        }

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 职业成长路线
     */
    function GrowthPath(){
        $datawhere = "1";
        if($this->stafferOne['account_class'] == '0'){
            $careeList = $this->DataControl->selectClear("SELECT c.career_id,c.career_cnname FROM eas_career as c WHERE c.company_id='{$this->company_id}' AND c.career_id IN (SELECT p.career_id FROM eas_post_career as p WHERE p.post_id = '{$this->stafferOne['post_id']}')");
            $datawhere .= " AND co.course_id IN (SELECT a.course_id FROM eas_course_adaptive as a WHERE a.post_id = '{$this->stafferOne['post_id']}')";
        }else{
            $careeList = $this->DataControl->selectClear("SELECT c.career_id,c.career_cnname FROM eas_career as c WHERE c.company_id='{$this->company_id}'");
        }
        if($careeList){
            foreach($careeList as &$v){
                $v['stage_list'] = $this->DataControl->selectClear("SELECT s.stage_id,s.stage_cnname,s.career_id,
                                                                        (SELECT COUNT(t.trainhour_id)
                                                                          FROM eas_course as co
                                                                          LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                                                                          LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                                                                          WHERE co.stage_id=s.stage_id
                                                                        ) as total_num,
                                                                        (SELECT COUNT(l.trainhour_id)
                                                                          FROM eas_course as co
                                                                          LEFT JOIN eas_learning as l ON l.course_id = co.course_id
                                                                          WHERE l.teacher_branch='{$this->stafferOne['staffer_branch']}' AND l.learning_status='2' AND co.stage_id=s.stage_id
                                                                        ) as complete_num,
                                                                        (SELECT COUNT(cs.stage_id)
                                                                          FROM eas_career_stage as cs
                                                                          WHERE cs.career_id=s.career_id
                                                                        ) as stage_num,
                                                                        (SELECT COUNT(e.examine_id)
                                                                          FROM eas_career_examine as e
                                                                          WHERE e.teacher_branch='{$this->stafferOne['staffer_branch']}' AND e.stage_id=s.stage_id AND e.examine_ispass='1'
                                                                        ) as exam_num
                                                                        FROM eas_career_stage as s
                                                                        WHERE s.career_id='{$v['career_id']}' GROUP BY s.stage_id");
                $course = $this->DataControl->selectOne("SELECT co.course_id FROM eas_course as co WHERE co.career_id='{$v['career_id']}' AND {$datawhere} ORDER BY co.stage_id ASC");
                $v['course_id'] = $course['course_id'];
            }
            foreach($careeList as &$val){
                if($val['stage_list']){
                    foreach($val['stage_list'] as &$v){
                        if($v['total_num'] == $v['complete_num'] && $v['stage_num'] == $v['exam_num']){
                            $v['progress'] = '1';
                            $v['completedNum'] = '100%';
                            $stage_id = $this->DataControl->selectOne("SELECT stage_id FROM eas_career_stage WHERE stage_id>'{$v['stage_id']}' AND career_id='{$v['career_id']}' ORDER BY stage_id ASC");
                            $val['stage_id'] = $stage_id['stage_id'];
                        }elseif($v['complete_num'] == 0){
                            $v['progress'] = '3';
                            $v['completedNum'] = '0%';
                        }else{
                            $v['progress'] = '2';
                            $v['completedNum'] = round(($v['complete_num'] + $v['exam_num']) / ($v['total_num'] + $v['stage_num']), 2) * 100 .'%';
                        }
                    }
                    $progress = array_column($val['stage_list'],'progress');
                    $completedNum = array_column($val['stage_list'],'completedNum');
                    $val['progress'] = floor(array_sum($progress) / count($progress));
                    $val['completedNum'] = floor(array_sum($completedNum) / count($completedNum)) .'%';
                }

            }

        }

        return $careeList;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 立即学习
     */
    function AtOnceStudy($paramArray){
        $course = $this->DataControl->selectOne("SELECT course_id FROM eas_learning WHERE teacher_branch='{$this->stafferOne['staffer_branch']}' AND learning_status<>'0' ORDER BY learning_createtime DESC ");
        $studyinfo = $this->DataControl->selectOne("SELECT c.career_cnname FROM eas_course as co LEFT JOIN eas_career as c ON c.career_id=co.career_id WHERE co.course_id={$course['course_id']}");

        $stage_num = $this->DataControl->selectOne("SELECT COUNT(stage_id) as stage_num FROM eas_career_stage WHERE career_id = '{$paramArray['career_id']}'");

        $studyinfo['stage_num'] = $stage_num['stage_num'];
        $stage = $this->DataControl->selectClear("SELECT stage_id FROM eas_career_stage WHERE career_id='{$paramArray['career_id']}'");
        $exam_num = 0;
        if($stage){
            foreach($stage as $v){
                $examine = $this->DataControl->getFieldOne("eas_career_examine","examine_ispass","stage_id='{$v['stage_id']}' and examine_papertime>'0'","order by examine_papertime desc");
                if($examine['examine_ispass'] == 1){
                    $exam_num++;
                }
            }
        }
        $studyinfo['schedule'] = $exam_num .'/'. $stage_num['stage_num'];

        $total = $this->DataControl->selectOne("SELECT COUNT(t.trainhour_id) as total_num
                                                  FROM eas_career_stage as s 
                                                  LEFT JOIN eas_course as co ON co.stage_id=s.stage_id
                                                  LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id 
                                                  LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                                                  WHERE s.career_id='{$paramArray['career_id']}'");

        $complete = $this->DataControl->selectOne("SELECT COUNT(l.trainhour_id) as complete_num
                                                     FROM eas_learning as l
                                                     LEFT JOIN eas_course as co ON l.course_id=co.course_id
                                                     LEFT JOIN eas_career_stage AS s ON s.stage_id = co.stage_id
                                                     WHERE l.teacher_branch='{$this->stafferOne['staffer_branch']}' AND l.learning_status='2' AND s.career_id='{$paramArray['career_id']}'");

        $course_num = $this->DataControl->selectOne("SELECT COUNT(q.course_id) as course_num FROM
                                                       (SELECT l.course_id,l.teacher_branch,s.career_id
                                                       FROM eas_learning as l
                                                       LEFT JOIN eas_course as co ON l.course_id=co.course_id
                                                       LEFT JOIN eas_career_stage AS s ON s.stage_id = co.stage_id
                                                       WHERE l.learning_status='2'
                                                       GROUP BY l.course_id) as q WHERE q.teacher_branch='{$this->stafferOne['staffer_branch']}' AND q.career_id='{$paramArray['career_id']}'");

        $studyinfo['completedNum'] = round((($complete['complete_num']  / $total['total_num']) * $course_num['course_num'] / $stage_num['stage_num']), 3) * 100 .'%';

        $listinfo = $this->DataControl->selectClear("SELECT s.stage_id,s.stage_cnname,co.course_id,co.course_name,
                                                        (SELECT a.career_isexam FROM eas_course_adaptive as a LEFT JOIN gmc_staffer_postbe as p ON p.post_id = a.post_id WHERE p.company_id = '{$this->company_id}' AND p.school_id = '{$paramArray['school_id']}' AND p.staffer_id = '{$this->staffer_id}' AND a.course_id = co.course_id) as career_isexam,
                                                        (SELECT e.examine_score FROM eas_career_examine as e WHERE s.stage_id=e.stage_id AND e.examine_papertime>0 ORDER BY e.examine_papertime DESC LIMIT 1) as examine_score,
                                                        (SELECT e.examine_ispass FROM eas_career_examine as e WHERE s.stage_id=e.stage_id AND e.examine_papertime>0 ORDER BY e.examine_papertime DESC LIMIT 1) as examine_ispass,
                                                        (SELECT COUNT(t.trainhour_id)
                                                          FROM eas_course as co
                                                          LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                                                          LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                                                          WHERE co.stage_id=s.stage_id
                                                        ) as total_num,
                                                        (SELECT COUNT(l.trainhour_id)
                                                          FROM eas_course as c
                                                          LEFT JOIN eas_learning as l ON l.course_id = c.course_id
                                                          WHERE l.teacher_branch='{$this->stafferOne['staffer_branch']}' AND l.learning_status='2' AND c.stage_id=s.stage_id
                                                        ) as complete_num
                                                        FROM eas_career_stage as s
                                                        LEFT JOIN eas_course as co ON co.stage_id=s.stage_id
                                                        LEFT JOIN eas_career_examine as e ON s.stage_id=e.stage_id AND e.examine_papertime>0
                                                        WHERE s.career_id='{$paramArray['career_id']}' GROUP BY s.stage_id ORDER BY examine_id ASC ");
        if($listinfo){
            foreach($listinfo as &$v){
                if($v['examine_score'] && $v['examine_ispass'] == '0'){
                    $v['exam_name'] = '不及格';
                    $v['exam_situation'] = $v['exam_name'] .' '. $v['examine_score'] .'分';
                }elseif($v['examine_ispass'] == '1'){
                    $v['exam_name'] = '及格';
                    $v['exam_situation'] = $v['exam_name'] .' '. $v['examine_score'] .'分';
                }else{
                    $v['exam_name'] = '暂未考核';
                    $v['exam_situation'] = $v['exam_name'];
                }
                $v['study_situation'] = $v['complete_num'] .'/'. $v['total_num'];
            }
        }

        $data = array();
        $data['list'] = $studyinfo;
        $data['listinfo'] = $listinfo;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 点击考核效果展示
     */
    function ExamInfo($paramArray){
        $lasttimeexam = $this->DataControl->getFieldOne("eas_career_examine","examine_score,examine_examtime","stage_id='{$paramArray['stage_id']}' and teacher_branch='{$this->stafferOne['staffer_branch']}'","ORDER BY examine_papertime DESC");

        $examinfo = $this->DataControl->selectOne("SELECT s.stage_cnname,s.stage_examtime AS question_time,COUNT(q.question_id) as question_num
                                                     FROM eas_career_stage as s
                                                     LEFT JOIN eas_career_question as q ON q.stage_id=s.stage_id
                                                     WHERE s.stage_id='{$paramArray['stage_id']}'");
        $data = array();
        $data['list'] = $examinfo;
        $data['lasttimeexam'] = $lasttimeexam;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 培训 -- 获取检测编号
     */
    function getTestpaperQuestion($paramArray){
        $paper_data = $this->DataControl->selectClear("SELECT question_id,question_time FROM eas_career_question WHERE stage_id='{$paramArray['stage_id']}' ORDER BY RAND()");

        $result = array();
        if($paper_data){
            $data = array();
            do{
                $data['examine_branch'] = $this->creatBranch();
            }while($this->DataControl->selectOne("select examine_id from eas_career_examine where examine_branch='{$data['examine_branch']}'"));
            $data['teacher_branch'] = $this->stafferOne['staffer_branch'];
            $data['stage_id'] = $paramArray['stage_id'];
            $data['examine_createtime'] = time();
            $dataid = $this->DataControl->insertData('eas_career_examine',$data);
            if($dataid){
                foreach($paper_data as $v){
                    $examineData = array();
                    $examineData['examine_id'] = $dataid;
                    $examineData['question_id'] = $v['question_id'];
                    $this->DataControl->insertData('eas_career_examine_question',$examineData);
                }
            }

            $result["list"]['examine_branch'] = $data['examine_branch'];
            $res = array('error' => '0', 'errortip' => '获取真题试卷信息', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '无相关真题', 'result' => $result);
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 通过检测编号获取题目信息
     */
    function getSubjects($paramArray){
        $datawhere="1";
        if(isset($paramArray['examine_branch']) && $paramArray['examine_branch'] !== ''){
            $datawhere .= " and e.examine_branch = '{$paramArray['examine_branch']}' ";
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '必须传入检测编号', 'result' => $result);
            return $res;
        }

        $sql = "SELECT
                    b.question_id
                FROM
                    eas_career_examine_question AS b
                LEFT JOIN eas_career_examine AS e ON e.examine_id = b.examine_id
                LEFT JOIN eas_career_question AS f ON f.question_id = b.question_id
                WHERE
                    {$datawhere} AND f.question_id<>'0'";

        $subjects = $this->DataControl->selectClear($sql);
        $result = array();
        if($subjects){
            $result["list"] = $subjects;
            $test_time = $this->DataControl->selectOne("SELECT s.stage_examtime FROM eas_career_examine as e LEFT JOIN eas_career_stage as s ON s.stage_id = e.stage_id WHERE {$datawhere}");
            $result["test_time"] = $test_time['stage_examtime'];
            $result["examine_branch"] = $paramArray['examine_branch'];
        }else{
            $result["list"] = array();
        }

        $res = array('error' => '0', 'errortip' => '通过检测编号获取题目信息', 'result' => $result);
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 根据题目ID获取问题信息
     */
    function getQuestionJsonArray($paramArray,$is_analysis=0){
        if(!$is_analysis){
            $lasttimeexam = $this->DataControl->getFieldOne("eas_career_examine","examine_score,examine_examtime","stage_id='{$paramArray['stage_id']}' and teacher_branch='{$this->stafferOne['staffer_branch']}'","ORDER BY examine_papertime DESC");
        }

        $question_list = json_decode(stripslashes($paramArray['exam_list']),true);
        if($question_list){
            $list = array();
            foreach($question_list as $v){
                if($is_analysis){
                    $list[] = $this->DataControl->selectOne("SELECT q.question_id,q.question_pid,q.question_title,q.question_content,q.question_correct,q.question_analysis,eq.is_right,eq.answers
                                                            FROM eas_career_question as q LEFT JOIN eas_career_examine_question as eq ON eq.question_id=q.question_id
                                                            WHERE q.question_id='{$v['question_id']}' AND eq.examine_id='{$v['examine_id']}'");
                }else{
                    $list[] = $this->DataControl->getFieldOne("eas_career_question","question_id,question_pid,question_title,question_content","question_id='{$v['question_id']}'");
                }
            }
            if($list){
                $jiexi = array();
                foreach($list as &$v){
                    $v['option'] = $this->DataControl->selectClear("SELECT answers_optionname,answers_option FROM eas_career_answers WHERE question_id='{$v['question_id']}'");
                    if($is_analysis){
                        $exam_num = $this->DataControl->selectOne("SELECT COUNT(q.examine_id) as exam_num
                                                                     FROM eas_career_examine_question as q
                                                                     LEFT JOIN eas_career_examine as e ON e.examine_id=q.examine_id
                                                                     WHERE q.question_id='{$v['question_id']}' AND e.teacher_branch='{$this->stafferOne['staffer_branch']}' AND e.examine_papertime > 0 ");

                        $correct_num = $this->DataControl->selectOne("SELECT COUNT(q.examine_id) as correct_num
                                                                        FROM eas_career_examine_question as q
                                                                        LEFT JOIN eas_career_examine as e ON e.examine_id=q.examine_id
                                                                        WHERE q.question_id='{$v['question_id']}' AND e.teacher_branch='{$this->stafferOne['staffer_branch']}' AND e.examine_papertime > 0 AND q.is_right='1'");
                        $jiexi['exam_num'] = $exam_num['exam_num'];
                        $jiexi['correctrate'] = round(($correct_num['correct_num'] / $exam_num['exam_num'] * 100), 2) . '%';
                        $answers = $this->DataControl->selectOne("SELECT q.answers
                                                                    FROM eas_career_examine_question as q
                                                                    LEFT JOIN eas_career_examine as e ON e.examine_id=q.examine_id
                                                                    WHERE q.question_id='{$v['question_id']}' AND e.teacher_branch='{$this->stafferOne['staffer_branch']}' AND e.examine_papertime > 0 AND q.is_right='0'
                                                                    GROUP BY q.answers
                                                                    ORDER BY COUNT(q.answers) DESC");
                        $jiexi['answers'] = $answers['answers'];
                        $v['total'] = $jiexi;
                    }
                }
            }
        }else{
            $result["list"] = array();
            $list = array('error' => '1', 'errortip' => '题目ID不存在', 'result' => $result);
        }

        $data = array();
        $data['list'] = $list;
        if(!$is_analysis) {
            $data['exam_score'] = $lasttimeexam;
        }

        return $data;
    }



    /**
     * @param $paramArray
     * @return array
     * 培训 -- 提交试卷获取分数
     */
    function getScore($paramArray){
        if(isset($paramArray['examine_branch']) && $paramArray['examine_branch'] !== ''){
            $datawhere = "e.examine_branch='{$paramArray['examine_branch']}'";
        }else{
            $result["list"] = array();
            $res = array('error' => '0', 'errortip' => '必须传入检测编号', 'result' => $result);
            return $res;
        }

        if(!isset($paramArray['examine_examtime']) || $paramArray['examine_examtime'] == ''){
            $result["list"] = array();
            $res = array('error' => '0', 'errortip' => '必须传入答题时间', 'result' => $result);
            return $res;
        }

        $examineOne = $this->DataControl->selectOne("SELECT e.examine_id,COUNT(q.question_id) as question_num FROM eas_career_examine as e LEFT JOIN eas_career_examine_question as q ON q.examine_id=e.examine_id WHERE {$datawhere}");
        $question_num = $examineOne['question_num'];

        if(!$examineOne){
            $result["list"] = array();
            $res = array('error' => '0', 'errortip' => '检测编号不存在', 'result' => $result);
            return $res;
        }

        $data = array();
        $right_question = 0;
        foreach($paramArray['answer_list'] as $key => $val){
            $exam_data = array();
            $tem_question = $this->DataControl->selectOne("select b.question_correct from eas_career_question as b where b.question_id={$val['question_id']}");
            if ($tem_question['question_correct'] == $val['answer']) {
                $right_question = $right_question + 1;
                $exam_data['is_right'] = 1;
            }else {
                $exam_data['is_right'] = 0;
            }
            $exam_data['answers'] = $val['answer'];
            $this->DataControl->updateData("eas_career_examine_question", "examine_id = '{$examineOne['examine_id']}' and question_id='{$val['question_id']}' ", $exam_data);
        }

        $data['examine_score'] = ceil($right_question * 100 / $question_num);
        if($data['examine_score'] >= 60){
            $data['examine_ispass'] = '1';
        }
        $data['examine_examtime'] = round($paramArray['examine_examtime']);
        $data['examine_correctnums'] = $question_num;
        $data['examine_papertime'] = time();
        if($this->DataControl->updateData("eas_career_examine", "examine_branch = '{$paramArray['examine_branch']}' ", $data)){
            $result["list"]['examine_score'] = $data['examine_score'];
            $result["list"]['examine_branch'] = $paramArray['examine_branch'];
            $result["list"]['examine_id'] = $examineOne['examine_id'];
            $res = array('error' => '0', 'errortip' => '获取最终答题结果成功', 'result' => $result);
        }else{
            $result["list"] = array();
            $res = array('error' => '1', 'errortip' => '获取最终答题结果失败', 'result' => $result);
        }

        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 考核记录查看答案
     */
    function LookAnswer($paramArray){
        $examOne = array();
        $dataOne = $this->DataControl->selectOne("SELECT e.examine_score,COUNT(q.question_id) as question_num,
                                                (SELECT COUNT(qt.question_id) FROM eas_career_examine_question as qt WHERE qt.examine_id=e.examine_id AND qt.is_right='0') as wrong_num
                                                FROM eas_career_examine as e LEFT JOIN eas_career_examine_question as q ON q.examine_id=e.examine_id WHERE e.examine_id='{$paramArray['examine_id']}'");

        $examOne['examine_score'] = $dataOne['examine_score'];
        $examOne['wrong_num'] = $dataOne['wrong_num'];

        $datawhere = "1";
        if(isset($paramArray['wrong_topic']) && $paramArray['wrong_topic'] !== ''){
            $datawhere .= " and eq.is_right='0'";
        }

        $exam_list = $this->DataControl->selectClear("SELECT e.examine_id,eq.question_id FROM eas_career_examine as e
                                        LEFT JOIN eas_career_examine_question as eq ON eq.examine_id=e.examine_id
                                        LEFT JOIN eas_career_question as q ON q.question_id=eq.question_id
                                        WHERE e.examine_id='{$paramArray['examine_id']}' AND {$datawhere}");
        $examOne['exam_list'] = json_encode($exam_list);
        $data = $this->getQuestionJsonArray($examOne,1);

        $result = array();
        if($data['list']){
            $result['examOne'] = $examOne;
            $result['list'] = $data['list'];
            $res = array('error' => '0', 'errortip' => '查看答题成功', 'result' => $result);
        }else{
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => '暂无答案信息', 'result' => $result);
        }
        return $res;
    }



    //随机获取验证码
    function creatBranch(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $Str_num="1234567890";
        $randStr= $Str[rand(0,26)].$Str[rand(0,26)].date("YmdHis",time()).$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)];
        return $randStr;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 考核记录
     */
    function ExamRecord($paramArray){
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT
                    examine_id,examine_examtime,examine_score,examine_ispass,examine_createtime
                FROM
                    eas_career_examine
                WHERE
                    stage_id = '{$paramArray['stage_id']}' AND teacher_branch = '{$this->stafferOne['staffer_branch']}'
                ORDER BY
                    examine_createtime DESC
                Limit {$pagestart},{$num}";

        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$v){
                if($v['examine_score'] && $v['examine_ispass'] == '0'){
                    $v['exam_name'] = '未及格';
                }elseif($v['examine_ispass'] == '1' && $v['examine_score'] < 80){
                    $v['exam_name'] = '及格';
                }elseif($v['examine_score'] >= 80 && $v['examine_score'] < 100){
                    $v['exam_name'] = '良好';
                }elseif($v['examine_score'] == 100){
                    $v['exam_name'] = '优秀';
                }else{
                    $v['exam_name'] = '考核中途退出';
                }
                if(!$v['examine_score']){
                    $v['examine_score'] = '暂无分数';
                }
                $v['examine_examtime'] = $v['examine_examtime'] . '分钟';
                $v['examine_createtime'] = date("Y-m-d H:i",$v['examine_createtime']);
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(examine_id) AS num FROM eas_career_examine WHERE stage_id='{$paramArray['stage_id']}' AND teacher_branch='{$this->stafferOne['staffer_branch']}'");
            if ($all_num) {
                $data['allnums'] = $all_num['num'];
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $datalist;

        return $data;
    }



    /**
     * @param $paramArray
     * @return array
     * 培训 -- 查看课程
     */
    function SeeCourse($paramArray){

        $course = $this->DataControl->selectOne("SELECT co.course_id,co.course_name,co.course_intro,co.course_img,
                                                   (SELECT c.career_branch FROM eas_career as c WHERE c.career_id=co.career_id) as career_branch,
                                                   (SELECT s.stage_cnname FROM eas_career_stage as s WHERE s.stage_id=co.stage_id) as stage_cnname,
                                                   (SELECT COUNT(q.learning_id) as num FROM (SELECT l.learning_id FROM eas_learning as l WHERE l.course_id='{$paramArray['course_id']}' GROUP BY l.teacher_branch) as q) as number
                                                   FROM eas_course as co
                                                   WHERE co.course_id='{$paramArray['course_id']}'");
        if($course){
            $collect = $this->DataControl->getFieldOne("eas_course_collect","collect_status","course_id='{$course['course_id']}' and teacher_branch='{$this->stafferOne['staffer_branch']}'");
            if($collect['collect_status'] == '1'){
                $course['is_collect'] = true;
            }else{
                $course['is_collect'] = false;
            }
            $chapter = $this->DataControl->selectClear("SELECT chapter_id,chapter_name FROM eas_course_chapter WHERE course_id='{$course['course_id']}'");
            if($chapter){
                foreach($chapter as &$v){
                    $v['trainhour'] = $this->DataControl->selectClear("SELECT t.trainhour_id,t.trainhour_name,t.trainhour_class,t.trainhour_fileurl,
                                                                           (SELECT l.learning_status FROM eas_learning as l WHERE l.trainhour_id=t.trainhour_id AND l.teacher_branch='{$this->stafferOne['staffer_branch']}') as learning_status
                                                                           FROM eas_course_trainhour as t
                                                                           WHERE t.chapter_id='{$v['chapter_id']}'");
                    if($v['trainhour']){
                        foreach($v['trainhour'] as &$val){
                            if($val['trainhour_class'] == '2'){
                                $ppt_num = $this->DataControl->selectOne("SELECT COUNT(trainhour_id) as num FROM eas_course_trainhour_pptpage WHERE trainhour_id='{$val['trainhour_id']}'");
                                $val['img_num'] = $ppt_num['num'];
                            }
                            if(!$val['learning_status']){
                                $val['learning_status'] = 0;
                            }
                        }
                    }else{
                        $v['trainhour'] = array();
                    }
                }
            }else{
                $chapter = array();
            }

            $material_total = $this->DataControl->selectClear("SELECT (SELECT COUNT(t.trainhour_id) FROM eas_course_trainhour as t WHERE t.chapter_id=cp.chapter_id AND trainhour_class='0') as video_num,
                                                                (SELECT COUNT(t.trainhour_id) FROM eas_course_trainhour as t WHERE t.chapter_id=cp.chapter_id AND trainhour_class='1') as audio_num,
                                                                (SELECT COUNT(t.trainhour_id) FROM eas_course_trainhour as t WHERE t.chapter_id=cp.chapter_id AND trainhour_class='2') as ppt_num
                                                                FROM eas_course_chapter as cp
                                                                WHERE cp.course_id='{$course['course_id']}'");
            $audio_num = array_column($material_total,'audio_num');
            $video_num = array_column($material_total,'video_num');
            $ppt_num = array_column($material_total,'ppt_num');
            $total[0]['audio_num'] = array_sum($audio_num);
            $total[1]['video_num'] = array_sum($video_num);
            $total[2]['ppt_num'] = array_sum($ppt_num);

            $staffer = $this->DataControl->selectClear("SELECT sf.staffer_cnname,sf.staffer_enname,sf.staffer_img
                                                          FROM eas_learning as l
                                                          LEFT JOIN smc_staffer as sf ON sf.staffer_branch = l.teacher_branch
                                                          WHERE l.course_id='{$paramArray['course_id']}' AND l.learning_status = '1' AND sf.staffer_leave = '0' GROUP BY l.teacher_branch");
            if($staffer){
                foreach($staffer as &$val){
                    if($val['staffer_enname']){
                        $val['staffer_cnname'] = $val['staffer_cnname'].'-'.$val['staffer_enname'];
                    }
                }
            }else{
                $staffer = array();
            }

            $dynamic = $this->DataControl->selectClear("SELECT sf.staffer_cnname,sf.staffer_enname,sf.staffer_img,l.learning_createtime,co.course_name
                                                          FROM eas_learning as l
                                                          LEFT JOIN smc_staffer as sf ON sf.staffer_branch = l.teacher_branch
                                                          LEFT JOIN eas_course as co ON co.course_id = l.course_id
                                                          WHERE l.learning_status = '1' AND l.company_id = '{$this->company_id}' AND co.course_id = '{$paramArray['course_id']}' AND sf.staffer_leave = '0'
                                                          GROUP BY co.course_id LIMIT 30");
            if($dynamic){
                foreach($dynamic as &$v){
                    $v['learning_status_name'] = '正在学习';
                    $v['dynamic_time'] = $this->first_time($v['learning_createtime']);
                    if($v['staffer_enname']){
                        $v['staffer_cnname'] = $v['staffer_cnname'].'-'.$v['staffer_enname'];
                    }
                }
            }else{
                $dynamic = array();
            }

            $data = array();
            $data['list'] = $course;
            $data['catalog'] = $chapter;
            $data['total'] = $total;
            $data['staffer'] = $staffer;
            $data['dynamic'] = $dynamic;
            $res = array('error' => '0', 'errortip' => '获取课程信息成功', 'result' => $data);

        }else{
            $res = array('error' => '1', 'errortip' => '暂无课程信息', 'result' => array());
        }

        return $res;
    }

    function first_time($first_time){
        if ((time() - $first_time) < 3600) {
            $time = floor((time() - $first_time) %86400/60);
            $date_time = $time.'分钟前';
        }elseif((time() - $first_time) < 86400) {
            $time = floor((time() - $first_time) %86400/3600);
            $date_time = $time.'小时前';
        }else{
            $time = floor((time() - $first_time) /86400);
            $date_time = $time.'天前';
        }
        return $date_time;
    }


    /**
     * @param $paramArray
     * @return array
     * 查看课程 -- 观看课程
     */
    function WatchVideo($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT t.trainhour_id,t.trainhour_name,t.trainhour_coverimg,t.trainhour_fileurl,t.trainhour_class,l.learning_id,l.learning_note,l.learning_status,l.learning_watchtime,l.learning_watchfinish,l.learning_iswatch,
                                                (SELECT co.course_name FROM eas_course as co WHERE co.course_id=l.course_id) as course_name
                                                FROM eas_course_trainhour as t 
                                                LEFT JOIN eas_learning as l ON l.trainhour_id=t.trainhour_id
                                                WHERE t.trainhour_id='{$paramArray['trainhour_id']}' AND l.teacher_branch='{$this->stafferOne['staffer_branch']}'");
        if($dataOne['trainhour_class'] == '2'){
            $dataOne['trainhour_img'][] = $this->DataControl->selectClear("SELECT pptpage_imgurl,pptpage_sort FROM eas_course_trainhour_pptpage WHERE trainhour_id='{$dataOne['trainhour_id']}'");
        }
        $chapter = $this->DataControl->selectClear("SELECT chapter_id,chapter_name FROM eas_course_chapter WHERE course_id='{$paramArray['course_id']}'");
        if($chapter){
            foreach($chapter as &$v){
                $v['trainhour'] = $this->DataControl->selectClear("SELECT t.trainhour_id,t.trainhour_name,t.trainhour_class,
                                                                      (SELECT l.learning_status FROM eas_learning as l WHERE l.trainhour_id=t.trainhour_id AND l.teacher_branch='{$this->stafferOne['staffer_branch']}') as learning_status
                                                                      FROM eas_course_trainhour as t
                                                                      WHERE t.chapter_id='{$v['chapter_id']}'");
                if($v['trainhour']){
                    foreach($v['trainhour'] as &$val){
                        $val['trainhour_img'] = $this->DataControl->selectClear("SELECT pptpage_imgurl,pptpage_sort FROM eas_course_trainhour_pptpage WHERE trainhour_id='{$val['trainhour_id']}'");
                        if(!$val['trainhour_img']){
                            $val['trainhour_img'] = array();
                        }
                    }
                }
            }
        }else{
            $chapter = array();
        }

        $data = array();
        $data['list'] = $dataOne;
        $data['chapter'] = $chapter;
        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 查看课程 -- 观看课程(全屏)
     */
    function FullScreen($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT t.trainhour_id,t.trainhour_name,t.trainhour_coverimg,t.trainhour_fileurl,t.trainhour_class,l.learning_id,l.learning_note,l.learning_status,l.learning_watchtime,l.learning_watchfinish,l.learning_iswatch
                                                FROM eas_course_trainhour as t 
                                                LEFT JOIN eas_learning as l ON l.trainhour_id=t.trainhour_id
                                                WHERE t.trainhour_id='{$paramArray['trainhour_id']}'");

        $chapter = $this->DataControl->selectClear("SELECT chapter_id,chapter_name FROM eas_course_chapter WHERE course_id='{$paramArray['course_id']}'");
        if($chapter){
            foreach($chapter as &$v){
                $v['trainhour'] = $this->DataControl->selectClear("SELECT th.trainhour_id,th.trainhour_name,th.trainhour_coverimg,th.trainhour_fileurl FROM eas_course_trainhour as th
                                        LEFT JOIN eas_learning as l ON l.trainhour_id=th.trainhour_id
                                        WHERE th.trainhour_class<>'2' AND l.learning_status<>'0' AND th.chapter_id='{$v['chapter_id']}'");
            }
        }else{
            $chapter = array();
        }
        $data = array();
        $data['list'] = $dataOne;
        $data['chapter'] = $chapter;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 培训 -- 课程添加笔记
     */
    function PersonNoteApi($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT learning_id,learning_note FROM eas_learning WHERE teacher_branch='{$this->stafferOne['staffer_branch']}' AND trainhour_id='{$paramArray['trainhour_id']}'");
        $result = array();
        if($dataOne){
            $data['learning_note'] = $paramArray['learning_note'];
            if($dataOne['learning_note']){
                $tip = "修改";
            }else{
                $tip = "添加";
            }
            if($this->DataControl->updateData("eas_learning","learning_id='{$dataOne['learning_id']}'",$data)){
                $res = array('error' => 0, 'errortip' => '笔记'.$tip.'成功', 'result' => $result);
            }else{
                $res = array('error' => 1, 'errortip' => '笔记'.$tip.'失败', 'result' => $result);
            }
        }else{
            $res = array('error' => 1, 'errortip' => '暂未创建学习课程信息', 'result' => $result);
        }

        return $res;
    }



    /**
     * @param $paramArray
     * @return array
     * 课程 -- 创建收藏信息
     */
    function CourseCollect($paramArray){
        if($this->DataControl->getOne("eas_course_collect","course_id='{$paramArray['course_id']}' and teacher_branch='{$this->stafferOne['staffer_branch']}'")){
            $res = array('error' => 1, 'errortip' => '已创建过收藏信息');
        }else{
            $data = array();
            $data['course_id'] = $paramArray['course_id'];
            $data['teacher_branch'] = $this->stafferOne['staffer_branch'];
            $dataid = $this->DataControl->insertData("eas_course_collect",$data);
            if($dataid){
                $res = array('error' => 0, 'errortip' => '收藏信息创建成功');
            }else{
                $res = array('error' => 1, 'errortip' => '收藏信息创建失败');
            }
        }

        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 课程 -- 添加/取消收藏
     */
    function AddCourseCollect($paramArray){

        $data = array();
        $data['collect_status'] = $paramArray['collect_status'];
        $dataid = $this->DataControl->updateData("eas_course_collect","course_id='{$paramArray['course_id']}' and teacher_branch='{$this->stafferOne['staffer_branch']}'",$data);
        if($paramArray['collect_status'] == '1'){
            $collect = '收藏';
        }else{
            $collect = '取消收藏';
        }

        $result = array();
        if($dataid){
            $res = array('error' => 0, 'errortip' => $collect.'成功', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => $collect.'失败', 'result' => $result);
        }

        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 查看课程 -- 创建学习课程信息
     */
    function AddWatchVideoInfo($paramArray){
        $learning = $this->DataControl->getFieldOne("eas_learning","learning_id","teacher_branch='{$this->stafferOne['staffer_branch']}' and course_id='{$paramArray['course_id']}' and chapter_id='{$paramArray['chapter_id']}' and trainhour_id='{$paramArray['trainhour_id']}'");
        $result = array();
        if($learning){
            $result['learning_id'] = $learning['learning_id'];
            $res = array('error' => 1, 'errortip' => '已创建过学习课程信息', 'result' => $result);
            return $res;
        }
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['teacher_branch'] = $this->stafferOne['staffer_branch'];
        $data['course_id'] = $paramArray['course_id'];
        $data['chapter_id'] = $paramArray['chapter_id'];
        $data['trainhour_id'] = $paramArray['trainhour_id'];
        $data['learning_status'] = '0';
        $data['learning_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_learning", $data);
        if($dataid){
            $result['learning_id'] = $dataid;
            $res = array('error' => 0, 'errortip' => '学习课程信息创建成功', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '学习课程信息创建失败', 'result' => $result);
        }

        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 查看课程 -- 学习课程完成
     */
    function WatchVideoComplete($paramArray){
        $data = array();
        $data['learning_status'] = $paramArray['learning_status'];
        $learning = $this->DataControl->getFieldOne("eas_learning","learning_watchtime","learning_id='{$paramArray['learning_id']}'");
        if($paramArray['learning_watchtime'] > $learning['learning_watchtime']){
            $data['learning_watchtime'] = $paramArray['learning_watchtime'];
            $data['learning_watchfinish'] = $paramArray['learning_watchfinish'];
        }
        if($paramArray['learning_iswatch']){
            $data['learning_iswatch'] = $paramArray['learning_iswatch'];
        }

        $tip = "";
        if($paramArray['learning_status'] == '2'){
            $data['learning_finishtime'] = time();
            $tip = "已完成";
        }elseif($paramArray['learning_status'] == '1'){
            $tip = "学习中";
        }
        if($this->DataControl->updateData("eas_learning", "learning_id='{$paramArray['learning_id']}'", $data)){
            $res = array('error' => 0, 'errortip' => '学习课程' . $tip);
        }else{
            $res = array('error' => 1, 'errortip' => '学习课程失败');
        }
        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 培训 -- 获取公开课信息
     */
    function OpenCourse($paramArray){
        $datawhere = "1";
        if(isset($paramArray['course_type']) && $paramArray['course_type'] !== ''){
            $datawhere .= " and co.course_type='{$paramArray['course_type']}'";
        }
        if(isset($paramArray['course_recommend']) && $paramArray['course_recommend'] !== ''){
            $datawhere .= " and co.course_recommend='{$paramArray['course_recommend']}'";
        }
        if(isset($paramArray['course_popular']) && $paramArray['course_popular'] !== ''){
            $datawhere .= " and co.course_popular='{$paramArray['course_popular']}'";
        }
        $newest = "";
        if(isset($paramArray['course_newest']) && $paramArray['course_newest'] !== ''){
            $newest = " ORDER BY co.course_createtime DESC";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT co.course_id,co.course_name,co.course_intro,co.course_img,
                (SELECT COUNT(DISTINCT l.teacher_branch) FROM eas_learning as l LEFT JOIN smc_staffer as sf ON sf.staffer_branch = l.teacher_branch WHERE l.course_id = co.course_id AND l.learning_status = '1' AND sf.staffer_leave = '0') as learn_num
                FROM eas_course as co WHERE {$datawhere} AND co.company_id='{$this->company_id}' {$newest} LIMIT {$pagestart},{$num}";

        $learning = $this->DataControl->selectClear($sql);

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] !== "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(co.course_id) as num
                                                        FROM eas_course as co WHERE {$datawhere} AND co.company_id='{$this->company_id}'");
            if (is_array($all_num)) {
                $data['allnums'] = $all_num['num'];
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $learning;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 培训 -- 职务统计培训报表
     */
    function PostTotalTrainReport($paramArray){
        $datawhere = "c.company_id='{$this->company_id}'";

        if(isset($paramArray['career_id']) && $paramArray['career_id'] !== ''){
            $datawhere .= " and c.career_id='{$paramArray['career_id']}'";
        }
        if(isset($paramArray['course_type']) && $paramArray['course_type'] !== ''){
            $course_type = $paramArray['course_type'];
        }else{
            $res = array('error' => 1, 'errortip' => '职务统计类型必须传入');
            return $res;
        }
        if($paramArray['account_class'] !== '1'){
            $datawhere .= " and l.teacher_branch='{$this->stafferOne['staffer_branch']}'";
        }

        if($course_type == 1){
            $sql = "SELECT c.career_id,c.career_cnname,
                    (SELECT COUNT(q.teacher_branch) FROM
                      (SELECT l.teacher_branch,co.career_id
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      LEFT JOIN eas_learning as l ON l.course_id=co.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                      WHERE co.course_type={$course_type}
                      GROUP BY l.teacher_branch) as q WHERE q.career_id=c.career_id
                    ) as OnJob_num,
                    (SELECT COUNT(co.course_id)
                      FROM eas_course as co
                      WHERE co.course_type={$course_type} AND co.career_id=c.career_id
                    ) as train_num,
                    (SELECT COUNT(q.trainhour_id) FROM
                      (SELECT t.trainhour_id,co.career_id,co.course_id,cp.chapter_id
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      WHERE co.course_type={$course_type}
                      ) as q WHERE q.career_id=c.career_id AND q.trainhour_id NOT IN (SELECT ln.trainhour_id FROM eas_learning as ln WHERE ln.course_id=q.course_id AND ln.chapter_id=q.chapter_id)
                    ) as unlearned_num,
                    (SELECT COUNT(t.trainhour_id)
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      LEFT JOIN eas_learning as l ON l.course_id=co.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                      WHERE co.course_type={$course_type} AND co.career_id=c.career_id AND l.learning_status='1'
                    ) as Inlearn_num,
                    (SELECT COUNT(t.trainhour_id)
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      LEFT JOIN eas_learning as l ON l.course_id=co.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                      WHERE co.course_type={$course_type} AND co.career_id=c.career_id AND l.learning_status='2'
                    ) as finish_num
                    FROM eas_career as c
                    LEFT JOIN eas_course as co ON co.career_id=c.career_id
                    LEFT JOIN eas_learning as l ON l.course_id=co.course_id
                    WHERE {$datawhere} GROUP BY c.career_id";
        }else{
            $sql = "SELECT c.career_id,c.career_cnname,
                    (SELECT COUNT(q.teacher_branch) FROM
                      (SELECT l.teacher_branch,co.career_id
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      LEFT JOIN eas_learning as l ON l.course_id=co.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                      WHERE co.course_type={$course_type}
                      GROUP BY l.teacher_branch) as q
                    ) as OnJob_num,
                    (SELECT COUNT(co.course_id)
                      FROM eas_course as co
                      WHERE co.course_type={$course_type}
                    ) as train_num,
                    (SELECT COUNT(q.trainhour_id) FROM
                      (SELECT t.trainhour_id,co.career_id,co.course_id,cp.chapter_id
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      WHERE co.course_type={$course_type}
                      ) as q WHERE q.trainhour_id NOT IN (SELECT ln.trainhour_id FROM eas_learning as ln WHERE ln.course_id=q.course_id AND ln.chapter_id=q.chapter_id)
                    ) as unlearned_num,
                    (SELECT COUNT(t.trainhour_id)
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      LEFT JOIN eas_learning as l ON l.course_id=co.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                      WHERE co.course_type={$course_type} AND l.learning_status='1'
                    ) as Inlearn_num,
                    (SELECT COUNT(t.trainhour_id)
                      FROM eas_course as co
                      LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                      LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                      LEFT JOIN eas_learning as l ON l.course_id=co.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                      WHERE co.course_type={$course_type} AND l.learning_status='2'
                    ) as finish_num
                    FROM eas_career as c
                    LEFT JOIN eas_course as co ON co.career_id=c.career_id
                    LEFT JOIN eas_learning as l ON l.course_id=co.course_id
                    WHERE {$datawhere} GROUP BY c.career_id";
        }


        $datalist = $this->DataControl->selectClear($sql);

        return $datalist;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 教师统计报表
     */
    function TeacherReport($paramArray){
        $datawhere = "sf.company_id='{$this->company_id}'";

        if(isset($paramArray['career_id']) && $paramArray['career_id'] !== ''){
            $datawhere .= " and c.career_id='{$paramArray['career_id']}'";
        }
        if(isset($paramArray['keyword']) && $paramArray['keyword'] !== ''){
            $datawhere .=" and (sf.staffer_cnname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['re_career_id']) && $paramArray['re_career_id'] !== ''){
            $datawhere .= " and c.career_id='{$paramArray['re_career_id']}'";
        }else{
            if($paramArray['account_class'] == '0'){
                $datawhere .= " and sf.staffer_id='{$this->staffer_id}'";// and cs.school_id='{$paramArray['school_id']}'
            }
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,sf.staffer_branch,c.career_cnname,
                (SELECT COUNT(co.course_id)
                  FROM eas_course as co
                  WHERE co.course_type='1' AND co.career_id = c.career_id
                ) as shouldtrain_num,
                (SELECT COUNT(DISTINCT co.course_id)
                  FROM eas_course as co
                  LEFT JOIN eas_learning as l ON co.course_id=l.course_id
                  WHERE co.course_type='1' AND co.company_id = l.company_id AND l.learning_status='2' AND l.teacher_branch=sf.staffer_branch AND co.career_id = c.career_id
                ) as finishtrain_num,
                (SELECT COUNT(co.course_id)
                  FROM eas_course as co
                  WHERE co.course_type='0' AND co.company_id = sf.company_id
                ) as shouldopenclass_num
                FROM smc_staffer as sf
                LEFT JOIN smc_class_teach AS ct ON ct.staffer_id = sf.staffer_id
                LEFT JOIN smc_class AS cs ON cs.class_id = ct.class_id
                LEFT JOIN eas_learning as l ON l.teacher_branch=sf.staffer_branch
                LEFT JOIN eas_course as co ON co.course_id=l.course_id
                LEFT JOIN eas_career as c ON c.career_id=co.career_id
                WHERE {$datawhere} AND sf.staffer_leave='0' GROUP BY sf.staffer_id LIMIT {$pagestart},{$num}";

        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$val){
                if($val['staffer_enname']){
                    $val['staffer_cnname'] = $val['staffer_cnname'].'-'.$val['staffer_enname'];
                }
                $learning = $this->DataControl->selectClear("SELECT l.learning_status
                                                               FROM eas_course as co
                                                               LEFT JOIN eas_learning as l ON co.course_id=l.course_id
                                                               WHERE co.course_type='0' AND l.teacher_branch='{$val['staffer_branch']}'");
                if($learning){
                    $k = 0;
                    foreach($learning as $v){
                        if($v['learning_status'] == 2){
                            $k++;
                        }
                    }
                    if($k == count($learning)){
                        $val['finishopenclass_num'] = $val['shouldopenclass_num'];
                    }else{
                        $val['finishopenclass_num'] = 0;
                    }
                }else{
                    $val['finishopenclass_num'] = 0;
                }
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] !== "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.staffer_id) as num FROM
                                                        (SELECT sf.staffer_id
                                                        FROM smc_staffer as sf
                                                        LEFT JOIN smc_class_teach AS ct ON ct.staffer_id = sf.staffer_id
                                                        LEFT JOIN smc_class AS cs ON cs.class_id = ct.class_id
                                                        LEFT JOIN eas_learning as l ON l.teacher_branch=sf.staffer_branch
                                                        LEFT JOIN eas_course as co ON co.course_id=l.course_id
                                                        LEFT JOIN eas_career as c ON c.career_id=co.career_id
                                                        WHERE {$datawhere} AND sf.staffer_leave='0' GROUP BY sf.staffer_id) as q");
            if (is_array($all_num)) {
                $data['allnums'] = $all_num['num'];
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $datalist;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 培训 -- 公开课统计报表
     */
    function OpenClassReport($paramArray){
        $datawhere = "co.company_id='{$this->company_id}'";

        if(isset($paramArray['career_id']) && $paramArray['career_id'] !== ''){
            $datawhere .= " and c.career_id='{$paramArray['career_id']}'";
        }
        if(isset($paramArray['keyword']) && $paramArray['keyword'] !== ''){
            $datawhere .=" and (co.course_name like '%{$paramArray['keyword']}%')";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT co.course_name,
                (SELECT COUNT(a.adaptive_id)
                  FROM eas_course_adaptive as a
                  WHERE a.course_id=co.course_id
                ) as fitjob_num,
                (SELECT COUNT(q.teacher_branch) FROM
                  (SELECT l.teacher_branch,cp.course_id
                  FROM eas_course_chapter as cp
                  LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                  LEFT JOIN eas_learning as l ON l.course_id=cp.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                  GROUP BY l.teacher_branch) as q WHERE q.course_id=co.course_id
                ) as OnJob_num,
                (SELECT COUNT(q.trainhour_id) FROM
                  (SELECT t.trainhour_id,cp.course_id,cp.chapter_id
                  FROM eas_course_chapter as cp
                  LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                  ) as q WHERE q.course_id=co.course_id AND q.trainhour_id NOT IN (SELECT ln.trainhour_id FROM eas_learning as ln WHERE ln.course_id=q.course_id AND ln.chapter_id=q.chapter_id)
                ) as unlearned_num,
                (SELECT COUNT(t.trainhour_id)
                  FROM eas_course_chapter as cp
                  LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                  LEFT JOIN eas_learning as l ON l.course_id=cp.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                  WHERE cp.course_id=co.course_id AND l.learning_status='1'
                ) as Inlearn_num,
                (SELECT COUNT(t.trainhour_id)
                  FROM eas_course_chapter as cp
                  LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                  LEFT JOIN eas_learning as l ON l.course_id=cp.course_id AND l.chapter_id=cp.chapter_id AND l.trainhour_id=t.trainhour_id
                  WHERE cp.course_id=co.course_id AND l.learning_status='2'
                ) as finish_num
                FROM eas_course as co
                LEFT JOIN eas_career as c ON c.career_id=co.career_id
                WHERE {$datawhere} AND co.course_type='0' LIMIT {$pagestart},{$num}";

        $datalist = $this->DataControl->selectClear($sql);

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] !== "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(co.course_id) as num
                                                    FROM eas_course as co LEFT JOIN eas_career as c ON c.career_id=co.career_id WHERE {$datawhere} AND co.course_type='0'");
            if (is_array($all_num)) {
                $data['allnums'] = $all_num['num'];
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $datalist;

        return $data;
    }
}