<?php
/**
 * 客诉-主题
 */

namespace Model\Easx;

class UcsFeedbackthemeModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array(), $order_pid = 0)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    /**
     * 必要参数
     * @param $publicarray
     */
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = 1;
            $this->errortip = "企业ID必传1";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }

        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }

    }


    /**
     * 主题列表 -zjc
     * @param $paramArray
     * @return bool
     */
    function feedbackthemeList($paramArray)
    {

//        if(!isset($paramArray['feedbacktype_id']) || $paramArray['feedbacktype_id']='')
//        {
//            $this->error = 1;
//            $this->errortip = "参数缺失:分类ID";
//            $this->result = array();
//            return false;
//        }

//        $result = [
//            'datalist'=>[
//                [
//                    'feedbacktheme_id'=>2,
//                    'feedbacktheme_name'=>"投诉英语班教学环境",
//                    'feedbacktheme_note'=>"投诉英语班的教学环境"
//
//                ]
//            ],
//            'allnum'=>1
//        ];

        $datawhere="ct.company_id='{$paramArray['company_id']}'  ";
//        //是否选了学校
//        if(!empty($paramArray['school_id']))
//        {
//            $datawhere .=" AND ct.school_id='{$paramArray['school_id']}' ";
//        }

        //是否选了分类
        if(!empty($paramArray['feedbacktype_id']))
        {
            $datawhere .=" AND ct.feedbacktype_id='{$paramArray['feedbacktype_id']}' ";
        }

        $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_ucsuserlevel","postbe_id='{$paramArray['re_postbe_id']}'");

        $dataList = $this->DataControl->selectClear("SELECT ct.feedbacktheme_id,ct.feedbacktheme_name,ct.feedbacktheme_note 
        FROM ucs_code_feedbacktheme ct 
        LEFT JOIN ucs_code_feedbacktype AS cf ON ct.feedbacktype_id=cf.feedbacktype_id
        WHERE {$datawhere} AND ct.feedbacktheme_rolelist LIKE '%{$postbeOne['postbe_ucsuserlevel']}%'
        ");

        $result = array();
        $result['datalist'] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "暂无主题";
            $this->result = $result;
            return false;
        }

    }

}