<?php

namespace Model\Easx;

Class IndexModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'], $publicarray['re_postbe_id'])) {
                $this->error = true;
                $this->errortip = "操作人ID必须传入";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作人ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id, $re_postbe_id=0)
    {
        if($re_postbe_id>0){
            $this->stafferOne = $this->DataControl->selectOne("SELECT
                                                                      s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_mobile,s.account_class,sp.postpart_isteregulator
                                                                   FROM
                                                                      smc_staffer as s
                                                                   LEFT JOIN
                                                                      gmc_staffer_postbe as p ON p.staffer_id = s.staffer_id
                                                                   LEFT JOIN
                                                                      smc_school_postpart as sp ON sp.postpart_id = p.postpart_id
                                                                   WHERE
                                                                      s.company_id='{$this->company_id}' AND s.staffer_id = '{$staffer_id}' AND p.postbe_id = '{$re_postbe_id}'");
        }else{
            $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        }

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 获取教师信息
     */
    function getCompanyidApi($paramArray)
    {
        $datawhere = "1";
        if($this->publicarray['re_postbe_id']){
            $datawhere .= " and p.postbe_id = '{$this->publicarray['re_postbe_id']}'";
        }
        $stafferOne = $this->DataControl->selectOne("SELECT s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_img,s.staffer_sex,s.account_class,sp.postpart_isteregulator,sp.postpart_name,s.company_id,ss.school_id,ss.school_cnname,ss.school_shortname,c.company_cnname,
                                                        p.post_id,sp.postpart_isbeike,sp.postpart_istraining,sp.postpart_isregister,
                                                       (SELECT i.info_birthday FROM smc_staffer_info as i WHERE i.staffer_id=s.staffer_id LIMIT 1) as info_birthday,
                                                       (SELECT cp.post_name FROM gmc_company_post as cp WHERE cp.post_id=p.post_id) as post_name,
                                                       (SELECT pl.postlevel_cnname FROM gmc_company_postlevel as pl WHERE pl.postlevel_id=p.postlevel_id) as postlevel_cnname
                                                       FROM smc_staffer as s
                                                       LEFT JOIN gmc_staffer_postbe as p ON s.staffer_id = p.staffer_id
                                                       LEFT JOIN smc_school_postpart as sp ON sp.postpart_id = p.postpart_id
                                                       LEFT JOIN gmc_company as c on s.company_id = c.company_id
                                                       LEFT JOIN smc_school as ss on ss.school_id = p.school_id
                                                       WHERE s.company_id='{$this->company_id}' and s.staffer_id='{$this->staffer_id}' AND {$datawhere} limit 0,1");
        if($stafferOne) {
            $result = array();
            $result['company_id'] = $stafferOne['company_id'];
            $result['staffer_id'] = $stafferOne['staffer_id'];
            $result['account_class'] = $stafferOne['account_class'];

            if ($paramArray['school_id']) {
                $dataOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_cnname,school_shortname", "school_id='{$paramArray['school_id']}'");
                $result['school_id'] = $dataOne['school_id'];
                $result['school_cnname'] = $dataOne['school_cnname'];
                $result['school_shortname'] = $dataOne['school_shortname'];
            } else {
                $dataOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_cnname,school_shortname", "school_id<>'0'", 'order by school_id desc');
                $result['school_id'] = $dataOne['school_id'];
                $result['school_cnname'] = $dataOne['school_cnname'];
                $result['school_shortname'] = $dataOne['school_shortname'];
            }
            if ($stafferOne['account_class'] == '1') {
                if ($stafferOne['post_name']) {
                    $result['post_name'] = $stafferOne['post_name'];
                } else {
                    $result['post_name'] = '管理员';
                }
            } else {
                $result['post_name'] = $stafferOne['post_name'];
            }

            $result['post_id'] = $stafferOne['post_id'];
            $result['company_cnname'] = $stafferOne['company_cnname'];
            if ($stafferOne['staffer_enname']) {
                $result['staffer_cnname'] = $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'];
            } else {
                $result['staffer_cnname'] = $stafferOne['staffer_cnname'];
            }
            $result['staffer_enname'] = $stafferOne['staffer_enname'];
            $result['staffer_img'] = $stafferOne['staffer_img'];
            $result['staffer_sex'] = $stafferOne['staffer_sex'];
            $result['info_birthday'] = $stafferOne['info_birthday'];
            $result['postpart_isteregulator'] = $stafferOne['postpart_isteregulator'];
            $result['postrole_name'] = $stafferOne['postpart_name'];
            $result['postlevel_cnname'] = $stafferOne['postlevel_cnname'];
            $result['postpart_isbeike'] = $stafferOne['postpart_isbeike'];
            $result['postpart_istraining'] = $stafferOne['postpart_istraining'];
            $result['postpart_isregister'] = $stafferOne['postpart_isregister'];

            $postbeOne = $this->DataControl->selectOne("SELECT p.postrole_id,r.postrole_dataequity
                                                          FROM gmc_staffer_postbe as p
                                                          LEFT JOIN gmc_company_postrole as r ON r.postrole_id = p.postrole_id
                                                          WHERE p.postbe_id = '{$paramArray['re_postbe_id']}' and p.school_id = '0'
                                                          ORDER BY p.postbe_ismianjob DESC limit 0,1");
            $result['postrole_id'] = $postbeOne['postrole_id'];

            if (!$result['postrole_id']) {
                $result['dataequity'] = '1';
            } else {
                $result['dataequity'] = $postbeOne['postrole_dataequity'];
            }

            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }else{
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }

        return $res;
    }
}