<?php
/**
 * 客诉-日程安排
 */

namespace Model\Easx;


class UcsScheduleModel extends UcsCommonModel
{
    /**
     * 日历
     * @param $paramArray
     * @return bool
     */
    function scheduleList($paramArray)
    {
        $date = getthemonth($paramArray['yearMonth']);

        $count = date('j', strtotime($date[1]));
        $mothListArray = array();
        for ($i = 1; $i <= $count; $i++) {
            if ($i < 10) {
                $i = '0' . $i;
            }
            $data = array();
            $data['yearMonth'] = date('Y-m', strtotime($date[0]));
            $data['daytime'] = $data['yearMonth'] . "-" . $i;
            $data['day'] = $i;
            $data['week'] = date('w', strtotime($data['daytime']));
            if(date("Y-m-d") == $data['daytime']){
                $data['isnow'] = 1;
            }else{
                $data['isnow'] = 0;
            }
            $data['event_id'] = 0;
            array_push($mothListArray, $data);
        }
        $month_firstday = $mothListArray[0]['daytime'];
        $month_nextday = $mothListArray[$count-1]['daytime'];

        $week = intval(date("w",strtotime($month_firstday)));
        $last_data = array();
        if($week > 0){
            do{
                $last_data[] = date("Y-m-d",strtotime("$month_firstday - $week days"));
                $week--;
            }while($week > 0);
        }
        $weeks = intval(date("w",strtotime($month_nextday)));
        $next_data = array();
        if($weeks >= 0){
            if(($weeks == 0 || $weeks == 1) && $count == 31){
                $weeks = 6 - $weeks;
            }else{
                $weeks = 13 - $weeks;
            }
            do{
                $next_data[] = date("Y-m-d",strtotime("$month_nextday + $weeks days"));
                $weeks--;
            }while($weeks > 0);
        }
        asort($next_data);

        if($last_data) {
            $merge_month = array_merge($this->getDateInfo($last_data), $mothListArray);
        }else{
            $merge_month = $mothListArray;
        }

        if($next_data) {
            $merge_month = array_merge($merge_month, $this->getDateInfo($next_data));
        }

        $sql = "SELECT sd.schedule_remindtime,sd.schedule_note,sf.staffer_cnname,sf.staffer_enname,sf.staffer_mobile
                FROM ucs_schedule as sd
                LEFT JOIN smc_staffer as sf ON  sf.staffer_id = sd.staffer_id
                WHERE sd.company_id = '{$paramArray['company_id']}' AND sd.school_id = '{$paramArray['school_id']}' AND sd.staffer_id = '{$paramArray['staffer_id']}'";

        $schedule = $this->DataControl->selectClear($sql);
        if($schedule){
            foreach($schedule as $values){
                foreach($merge_month as $keys => $items){
                    if(date("Y-m-d", $values['schedule_remindtime']) == $items['daytime']){
                        $merge_month[$keys]['is_have'] = 1;
                    }
                }
            }
        }

        $theWeek = array();
        for($i = 0; $i <= 6; $i++){
            foreach($merge_month as $k => $value){
                if($i == $k){
                    $theWeek[$i] = $value;
                }
            }
        }
        $dataList = array();
        $dataList['theWeek'] = $theWeek;
        $dataList['datalist'] = $merge_month;

        if($dataList){
            $this->error = 0;
            $this->errortip = "获取成功!";
            $this->result = $dataList;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = array();
            return false;
        }
    }

    function getDateInfo($month)
    {
        $data = array();
        foreach($month as $key => $item){
            $data[$key]['yearMonth'] = date('Y-m', strtotime($item));
            $data[$key]['daytime'] = $item;
            $data[$key]['day'] = date('d', strtotime($item));
            $data[$key]['week'] = date('w', strtotime($item));
            $data[$key]['isnow'] = 0;
            $data[$key]['event_id'] = 0;
            $data[$key]['isotherday'] = 1;
        }

        return $data;
    }

    /**
     * 详情
     * @param $paramArray
     * @return bool
     */
    function scheduleDetail($paramArray)
    {
        $day = array();
        $day[] = $paramArray['daytime'];
        $thisday = $this->getDateInfo($day);

        $sql = "SELECT FROM_UNIXTIME( sd.schedule_remindtime, '%Y-%m-%d' ) as schedule_remindtime,FROM_UNIXTIME( sd.schedule_createtime, '%Y-%m-%d' ) as schedule_createtime,sf.staffer_cnname,sf.staffer_enname,sf.staffer_mobile
                FROM ucs_schedule as sd
                LEFT JOIN smc_staffer as sf ON  sf.staffer_id = sd.staffer_id
                WHERE sd.company_id = '{$paramArray['company_id']}' AND sd.school_id = '{$paramArray['school_id']}' AND sd.staffer_id = '{$paramArray['staffer_id']}'
                AND FROM_UNIXTIME( sd.schedule_remindtime, '%Y-%m-%d' ) = '{$paramArray['daytime']}'";
        $schedule = $this->DataControl->selectClear($sql);
        if($schedule){
            foreach($thisday as &$val){
                foreach($schedule as $item){
                    $val['schedule_remindtime'] = $item['schedule_remindtime'];
                    $val['schedule_createtime'] = $item['schedule_createtime'];
                }
            }
        }else{
            $schedule = array();
        }
        $result = array();
        $result['datalist'] = $schedule;
        if($result){
            $this->error = 0;
            $this->errortip = "获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = array();
            return false;
        }

    }

    /**
     * 提醒列表 -zjc
     * @return bool
     */
    function repairorderReminderRecord($paramArray)
    {
        $datawhere=" s.company_id='{$this->company_id}' AND s.school_id='{$this->school_id}' AND s.staffer_id='{$this->staffer_id}' ";
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " AND (r.repairorder_contactmobile like '%{$paramArray['keyword']}%')";
        }
        //分页
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;
        $today_start_time=strtotime(date("Y-m-d",time()));//今天开始时间
        $dataList = $this->DataControl->selectClear("
        SELECT r.repairorder_contactname,r.repairorder_contactmobile,r.repairorder_status,r.feedbacktype_catgory,s.schedule_remindtime,s.schedule_note,s.schedule_createtime,
(SELECT IFNULL(COUNT(rt.track_id),0)
FROM ucs_repairorder_track rt
WHERE rt.repairorder_id=s.repairorder_id AND rt.track_type IN(1,2,3,4)
) AS track_num,
(SELECT IFNULL(COUNT(rt.track_id),0)
FROM ucs_repairorder_track rt
WHERE rt.repairorder_id=s.repairorder_id AND rt.track_type IN(1,2,3,4) AND rt.track_createtime>s.schedule_remindtime AND rt.staffer_id='{$this->staffer_id}'
) AS today_track_num  
        FROM ucs_schedule s 
        LEFT JOIN ucs_repairorder AS r ON  s.repairorder_id=r.repairorder_id 
        WHERE {$datawhere}
        ORDER BY s.schedule_remindtime ASC
        LIMIT {$pagestart},{$num} 
        ");
        if(!empty($dataList))
        {
            foreach ($dataList as $key => $value)
            {
                $dataList[$key]['schedule_remindtime'] = date('Y-m-d H:i:s',$value['schedule_remindtime']);
                //设定时间之后的跟踪次数统计
                if($value['today_track_num']>0)
                {
                    $dataList[$key]['is_track'] = 1;
                }else{
                    $dataList[$key]['is_track'] = 0;
                }

                //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案
                if($value['repairorder_status']==0)
                {
                    $dataList[$key]['repairorder_status_name'] = '待受理';
                }elseif ($value['repairorder_status']==1)
                {
                    $dataList[$key]['repairorder_status_name'] = '处理中';
                }elseif ($value['repairorder_status']==2)
                {
                    $dataList[$key]['repairorder_status_name'] = '已处理';
                }elseif ($value['repairorder_status']==3)
                {
                    $dataList[$key]['repairorder_status_name'] = '已结案';
                }elseif ($value['repairorder_status']==4)
                {
                    $dataList[$key]['repairorder_status_name'] = '已回访';
                }
                //类型 1:投诉 2:建议 3:表扬 4:其他
                if ($value['feedbacktype_catgory']==1) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '投诉';
                } elseif ($value['feedbacktype_catgory']==2) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '建议';
                } elseif ($value['feedbacktype_catgory']==3) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '表扬';
                } else {
                    $dataList[$key]['feedbacktype_catgory_name'] = '其他';
                }
                $dataList[$key]['track_createtime'] = date('Y-m-d H:i:s',$value['schedule_createtime']);

            }

        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT COUNT(s.schedule_id) as datanum 
        FROM ucs_schedule s 
        LEFT JOIN ucs_repairorder AS r ON  s.repairorder_id=r.repairorder_id 
        WHERE {$datawhere}
        ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum']+0;
        }else{
            $count = 0;
        }

        $result['datalist'] = is_array($dataList)?$dataList:array();
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "提醒列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无提醒!";
            $this->result = $result;
            return false;
        }

    }


}