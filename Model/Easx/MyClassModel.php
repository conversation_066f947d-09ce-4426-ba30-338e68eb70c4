<?php

namespace Model\Easx;

Class MyClassModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

//    function __construct($publicarray)
//    {
//        parent::__construct();
//        if (is_array($publicarray)) {
//            $this->setPublic($publicarray);
//            $this->publicarray = $publicarray;
//        }
//    }

    //班级列表
    function ClassList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                c.class_enname,
                c.class_branch,
                co.course_cnname,
                co.course_branch,
                s.staffer_cnname,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = t.class_id and ss.study_isreading = '1') as classNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id) as planNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id and h.hour_ischecking = '1' and h.hour_iswarming = '0') as alreadyNum,
                c.class_stdate,
                c.class_enddate,
                c.class_status
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['staffer_id']}' and class_status > '-2' and c.school_id = '{$paramArray['school_id']}'
                group by c.class_id
            Limit {$pagestart},{$num}";
        $ClassList = $this->DataControl->selectClear($sql);

        if($ClassList){
            $status=array('0'=>'待开班','1'=>'进行中','-1'=>'已结束');
            foreach($ClassList as &$val){
                $val['class_status']=$status[$val['class_status']];
            }

        }

        $all_num = $this->DataControl->select("SELECT COUNT(q.teaching_id) FROM
                                                (SELECT
                                                   t.teaching_id
                                                FROM
                                                    smc_class_hour_teaching AS t
                                                    left join smc_class as c on t.class_id = c.class_id
                                                    left join smc_course as co on c.course_id = co.course_id
                                                    left join smc_staffer as s on t.staffer_id = s.staffer_id
                                                WHERE
                                                    {$datawhere} and t.staffer_id = '{$paramArray['staffer_id']}' and class_status > '-2' and c.school_id = '{$paramArray['school_id']}' group by c.class_id) as q");
        $allnums = $all_num[0][0];

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["class_enname"] = "英文名";
        $field["course_cnname"] = "课程别";
        $field["course_branch"] = "课程别编号";
        $field["staffer_cnname"] = "教师";
        $field["classNum"] = "班级人数";
        $field["planNum"] = "计划";
        $field["alreadyNum"] = "已上";
        $field["class_stdate"] = "开班时间";
        $field["class_enddate"] = "结束时间";
        $field["class_status"] = "班级状态";

        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取班级列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何带班信息', 'result' => $result);
        }

        return $res;
    }

    //全员列表
    function telClassList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                c.class_enname,
                c.class_branch,
                co.course_cnname,
                co.course_branch,
                s.staffer_cnname,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = t.class_id and ss.study_isreading = '1') as classNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id) as planNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id and h.hour_ischecking = '1' and h.hour_iswarming = '0') as alreadyNum,
                c.class_stdate,
                c.class_enddate,
                c.class_status
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and class_status > '-2' and c.school_id = '{$paramArray['school_id']}'
                group by c.class_id
            Limit {$pagestart},{$num}";
        $ClassList = $this->DataControl->selectClear($sql);

        if($ClassList){
            $status=array('0'=>'待开班','1'=>'进行中','-1'=>'已结束');
            foreach($ClassList as &$val){
                $val['class_status']=$status[$val['class_status']];
            }

        }

        $all_num = $this->DataControl->select("SELECT COUNT(q.teaching_id) FROM
                                                (SELECT
                                                   t.teaching_id
                                                FROM
                                                    smc_class_hour_teaching AS t
                                                    left join smc_class as c on t.class_id = c.class_id
                                                    left join smc_course as co on c.course_id = co.course_id
                                                    left join smc_staffer as s on t.staffer_id = s.staffer_id
                                                WHERE
                                                    {$datawhere} and class_status > '-2' and c.school_id = '{$paramArray['school_id']}' group by c.class_id) as q");
        $allnums = $all_num[0][0];

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["class_enname"] = "英文名";
        $field["course_cnname"] = "课程别";
        $field["course_branch"] = "课程别编号";
        $field["staffer_cnname"] = "教师";
        $field["classNum"] = "班级人数";
        $field["planNum"] = "计划";
        $field["alreadyNum"] = "已上";
        $field["class_stdate"] = "开班时间";
        $field["class_enddate"] = "结束时间";
        $field["class_status"] = "班级状态";

        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取班级列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何带班信息', 'result' => $result);
        }

        return $res;
    }

    //班级详情
    function ClassDetail($paramArray)
    {
        $sql = "
            SELECT
                c.class_cnname,
                c.class_enname,
                c.class_branch,
                co.course_cnname,
                co.course_branch,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = c.class_id and ss.study_isreading = '1') as classNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = c.class_id and h.hour_ischecking<>'-1') as planNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = c.class_id and h.hour_ischecking = '1') as alreadyNum,
                c.class_stdate,
                c.class_enddate,
                c.class_status,
                (select group_concat(DISTINCT st.staffer_cnname) from smc_class_hour_teaching as ct left join smc_staffer as st on st.staffer_id=ct.staffer_id where ct.class_id=c.class_id and ct.teaching_isdel=0 ) as staffer_cnname
            FROM
                smc_class as c
                left join smc_course as co on c.course_id = co.course_id
            WHERE
                c.class_id = '{$paramArray['class_id']}'
                GROUP BY c.class_id";
        $ClassDetail = $this->DataControl->selectClear($sql);

        if($ClassDetail){
            $status=array('0'=>'待开班','1'=>'进行中','-1'=>'已结束');
            foreach($ClassDetail as &$val){
                $val['class_status']=$status[$val['class_status']];
            }

        }

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["class_enname"] = "英文名";
        $field["course_cnname"] = "课程别";
        $field["course_branch"] = "课程别编号";
        $field["staffer_cnname"] = "教师";
        $field["classNum"] = "班级人数";
        $field["planNum"] = "计划";
        $field["alreadyNum"] = "已上";
        $field["class_stdate"] = "开班时间";
        $field["class_enddate"] = "结束时间";
        $field["class_status"] = "班级状态";

        if ($ClassDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $ClassDetail;
            $res = array('error' => '0', 'errortip' => '获取班级详情成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班级详情失败', 'result' => $result);
        }

        return $res;
    }

    //班级人数
    function ClassStudent($paramArray)
    {
        $datawhere = " 1 and s.study_isreading = '1'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%' or f.family_mobile like '%{$paramArray['keyword']}%')";
        }

        $sql = "
            SELECT
                st.student_cnname,
                st.student_enname,
                st.student_branch,
                st.student_sex,
                st.student_img,
                f.family_mobile,
                scb.coursebalance_time as restNum,
                ( SELECT count( h.hour_id ) FROM smc_class_hour AS h WHERE h.class_id = s.class_id ) AS planNum,
                ( SELECT count( sh.hourstudy_id ) FROM smc_student_hourstudy AS sh WHERE sh.student_id = s.student_id AND sh.class_id = s.class_id ) AS alreadyNum,
                ( SELECT b.coursebalance_figure FROM smc_student_coursebalance AS b WHERE b.course_id = c.course_id AND b.student_id = s.student_id ) AS price 
            FROM
                smc_student_study AS s
                LEFT JOIN smc_student AS st ON s.student_id = st.student_id
                LEFT JOIN smc_student_family AS f ON f.student_id = s.student_id
                LEFT JOIN smc_class AS c ON s.class_id = c.class_id
                LEFT JOIN smc_student_coursebalance as scb on scb.student_id=s.student_id and scb.course_id=c.course_id and scb.school_id=s.school_id
            WHERE
               {$datawhere} and s.class_id = '{$paramArray['class_id']}'
            ";
        $StudentList = $this->DataControl->selectClear($sql);

//        if($StudentList){
//            foreach($StudentList as &$val){
//                $val['restNum'] = $val['planNum'] - $val['alreadyNum'];
//            }
//
//        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(s.study_id)
            FROM
                smc_student_study AS s
                LEFT JOIN smc_student AS st ON s.student_id = st.student_id 
                left join smc_student_family as f on f.student_id = s.student_id
            WHERE
               {$datawhere} and s.class_id = '{$paramArray['class_id']}'");
        $allnums = $all_num[0][0];

        $field = array();
        $field["student_cnname"] = "学员名称";
        $field["student_branch"] = "学员编号";
        $field["family_mobile"] = "联系电话";
        $field["restNum"] = "剩余课次";
        $field["price"] = "课程余额";

        if ($StudentList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $StudentList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取班级人数列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取班级人数列表失败', 'result' => $result);
        }

        return $res;
    }

    //课表
    function TeachingList($paramArray)
    {
        $datawhere = '1';
        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] !== "") {
            $datawhere .= " and h.hour_way ={$paramArray['hour_way']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                h.hour_id,
                h.hour_way,
                h.hour_number,
                h.hour_day as hour_formerday,
                h.hour_starttime,
                h.hour_endtime,
                h.hour_name,
                h.hour_ischecking,
                t.staffer_id,
                c.classroom_cnname,
                (select count(study_id) from smc_student_study as s where s.class_id = t.class_id and s.study_isreading = '1') as allNum,
                              (select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=h.hour_id) as commentNum,

                (select count(sh.hourstudy_id) from smc_student_hourstudy as sh where sh.hour_id = t.hour_id and sh.hourstudy_checkin = '1') as tureNum,         
              (SELECT count(cs.sturemark_id) FROM eas_classhour_sturemark AS cs WHERE cs.hour_id = h.hour_id ) as alreadyNum,
	            (select GROUP_CONCAT(sturemark_id) from eas_classhour_sturemark as s where s.hour_id = t.hour_id ) as id,
	            (select class_fullnums from smc_class as cl where cl.class_id = t.class_id ) as class_fullnums
            FROM
                smc_class_hour_teaching AS t
                LEFT JOIN smc_class_hour AS h ON t.hour_id = h.hour_id 
                left join smc_classroom as c on c.classroom_id = h.classroom_id
          
            WHERE
                {$datawhere} 
                AND t.class_id = '{$paramArray['class_id']}' and t.staffer_id = '{$paramArray['staffer_id']}' and h.hour_ischecking > '-1' order by hour_formerday,hour_starttime ASC
            Limit {$pagestart},{$num}";

//        and t.staffer_id = '{$paramArray['staffer_id']}'

        $TeachingList = $this->DataControl->selectClear($sql);

        if($TeachingList){
            $status=array('0'=>'实体课','1'=>'线上课');
            $statuss=array('0'=>'未上课','1'=>'已上课');
            foreach($TeachingList as &$val){
                $val['ismyhour'] = ($val['staffer_id'] == $paramArray['staffer_id'])?1:0;
                $val['hour_way']=$status[$val['hour_way']];
                $val['teaching_ischecking']=$statuss[$val['hour_ischecking']];
                $val['hour_formertimes']=$val['hour_starttime'].'-'.$val['hour_endtime'];
                $val['score']=$val['commentNum'].'/'.$val['allNum'];

            }

        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(t.teaching_id)
            FROM
                smc_class_hour_teaching AS t
                LEFT JOIN smc_class_hour AS h ON t.hour_id = h.hour_id 
                left join smc_classroom as c on c.classroom_id = h.classroom_id
            WHERE
                {$datawhere} 
                AND t.class_id = '{$paramArray['class_id']}' and t.staffer_id = '{$paramArray['staffer_id']}' and h.hour_ischecking > '-1'");
//        and t.staffer_id = '{$paramArray['staffer_id']}'
        $allnums = $all_num[0][0];

        $field = array();
        $field["hour_way"] = "课程类型";
        $field["hour_formerday"] = "日期";
        $field["hour_formertimes"] = "时间";
        $field["classroom_cnname"] = "教室";
        $field["hour_name"] = "lesson";
        $field["allNum"] = "总人数";
        $field["tureNum"] = "上课人数";
        $field["teaching_ischecking"] = "是否已上课";

        if ($TeachingList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $TeachingList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取课表列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取课表列表失败', 'result' => $result);
        }

        return $res;
    }

    //班级教师
    function ClassTeacher($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
         SELECT
            s.staffer_cnname,
            s.staffer_enname,
            s.staffer_mobile,
            s.staffer_sex,
            te.teachtype_name as teach_type,
            t.teaching_type
        FROM
            smc_class_hour_teaching AS t left join smc_staffer as s on t.staffer_id = s.staffer_id
            left join smc_code_teachtype as te on te.teachtype_code = t.teachtype_code
        WHERE
             t.class_id = '{$paramArray['class_id']}' and t.staffer_id <> '' 
             group by t.staffer_id
            Limit {$pagestart},{$num}";
        $TeachingList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(t.teach_id)
            FROM
                smc_class_teach AS t left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE t.class_id = '{$paramArray['class_id']}'");
        $allnums = $all_num[0][0];

        $field = array();
        $field["staffer_cnname"] = "教师名称";
        $field["staffer_mobile"] = "手机号";
        $field["staffer_sex"] = "性别";
        $field["teach_type"] = "教师类型";

        if ($TeachingList) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $TeachingList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取班级教师成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取班级教师失败', 'result' => $result);
        }

        return $res;
    }


    //通讯录列表
    function TimList($paramArray)
    {
        $datawhere = " 1";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%')";
        }

        $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = t.class_id and ss.study_isreading = '1') as classNum
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id        
	            LEFT JOIN smc_student_study AS s ON t.class_id = s.class_id
	            LEFT JOIN smc_student AS st ON s.student_id = st.student_id 
               	left join smc_course as co on co.course_id = c.course_id
	            left join smc_code_coursetype as p on p.coursetype_id = co.coursetype_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['staffer_id']}' and class_status = '1' and c.school_id = '{$paramArray['school_id']}' AND p.coursetype_isopenclass = '0' and s.study_isreading = '1'
                group by c.class_id
	        ORDER BY co.course_inclasstype ASC";
        $ClassList = $this->DataControl->selectClear($sql);

        $student = $this->DataControl->selectClear("
            SELECT
                s.class_id,
                st.student_id,
                st.student_cnname,
                st.student_enname,
                st.student_branch,
                st.student_sex,
                st.student_img
            FROM
                smc_student_study AS s
                LEFT JOIN smc_student AS st ON s.student_id = st.student_id
            WHERE
                {$datawhere} 
                AND s.study_isreading = '1' and s.school_id = '{$paramArray['school_id']}'");
        $son = array();
        foreach ($ClassList as $k => &$v) {
            $son[$k]['class_cnname'] = $v['class_cnname'];
            $son[$k]['class_id'] = $v['class_id'];
            foreach ($student as $key => $value) {
                if($value['class_id'] == $v['class_id']){
                    $son[$k]['children'][$key]['student_cnname'] = $value['student_cnname'].$value['student_enname'];
                    $son[$k]['children'][$key]['student_enname'] = $value['student_enname'];
                    $son[$k]['children'][$key]['student_img'] = $value['student_img'];
                    $son[$k]['children'][$key]['student_id'] = $value['student_id'];
                    $son[$k]['children'][$key]['student_sex'] = $value['student_sex'];
                    if(!$value['student_img']){
                        if($value['student_sex'] == '女'){
                            $son[$k]['children'][$key]['student_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191704x550337680.png';
                        }else{
                            $son[$k]['children'][$key]['student_img'] = 'https://pic.kedingdang.com/schoolmanage/202101191704x965822906.png';
                        }
                    }
                }

            }

        }

        if ($ClassList) {
            $res = array('error' => '0', 'errortip' => '获取通讯录列表成功', 'result' => $son);
        } else {
            $res = array('error' => '1', 'errortip' => '暂无通讯录信息', 'result' => array());
        }

        return $res;
    }




}