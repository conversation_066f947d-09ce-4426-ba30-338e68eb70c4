<?php

namespace Model\Easx;

Class AppraiseModel extends modelTpl
{
    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 课堂评价
     */
    function getAppraiseList($paramArray){
        $datawhere = "hs.student_id='{$paramArray['student_id']}'";

        if(isset($request['hour_way']) && $request['hour_way']!='' && $request['hour_way']!='不限'){
            $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
        }

        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and ch.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and ch.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and ch.hour_day >= '{$week['nowweek_start']}' and ch.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and ch.hour_day <= CURDATE() and ch.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and ch.hour_day <= CURDATE() and ch.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and ch.hour_day >= '{$month[0]}' and ch.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and ch.hour_day >= '{$lastmonth[0]}' and ch.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !==''){
            $datawhere .= " and ch.hour_day >= '{$paramArray['starttime']}'";
        }
        if(isset($paramArray['endtime']) && $paramArray['endtime'] !==''){
            $datawhere .= " and ch.hour_day <= '{$paramArray['endtime']}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT hs.hourstudy_id,hs.hourstudy_checkin,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,c.class_cnname,c.class_enname,s.school_cnname,cl.classroom_cnname,
                (SELECT sf.staffer_cnname 
                  FROM smc_class_hour_teaching as ht 
                  LEFT JOIN smc_staffer as sf ON sf.staffer_id=ht.staffer_id 
                  WHERE ht.class_id=hs.class_id AND ht.hour_id=hs.hour_id AND ht.teaching_type='0'
                ) as staffer_cnname,
                (SELECT sh.hourcomment_score FROM eas_student_hourcomment as sh WHERE sh.student_id=hs.student_id AND sh.hour_id=hs.hour_id) as hourcomment_score,
                (SELECT ROUND(AVG(sr.sturemarkstar_score), 1) FROM eas_classhour_sturemark as cs 
                  LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id 
                  WHERE cs.student_id=hs.student_id AND cs.hour_id=hs.hour_id AND cs.class_id=hs.class_id
                ) as score_num
                FROM smc_student_hourstudy as hs
                LEFT JOIN smc_class_hour as ch ON ch.hour_id=hs.hour_id
                LEFT JOIN smc_class as c ON c.class_id=hs.class_id
                LEFT JOIN smc_classroom as cl ON cl.classroom_id=ch.classroom_id
                LEFT JOIN smc_school as s ON s.school_id=c.school_id
                WHERE {$datawhere} ORDER BY ch.hour_day DESC LIMIT {$pagestart},{$num}";

        $datalist = $this->DataControl->selectClear($sql);
        $status = array('0'=>'未上课','1'=>'已上课');
        if($datalist){
            foreach($datalist as &$v){
                $v['hourstudy_checkin_name'] = $status[$v['hourstudy_checkin']];
            }
        }else{
            $datalist = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(hs.hourstudy_id) as num
                                                      FROM smc_student_hourstudy as hs
                                                      LEFT JOIN smc_class_hour as ch ON ch.hour_id=hs.hour_id
                                                      LEFT JOIN smc_class as c ON c.class_id=hs.class_id
                                                      LEFT JOIN smc_classroom as cl ON cl.classroom_id=ch.classroom_id
                                                      LEFT JOIN smc_school as s ON s.school_id=c.school_id
                                                      WHERE {$datawhere}");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 课堂评价详情
     */
    function AppraiseDteail($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT hs.hourstudy_checkin,hs.student_id,c.class_id,c.class_cnname,c.class_enname,s.school_cnname,cl.classroom_cnname,sf.staffer_id,sf.staffer_cnname,sf.staffer_mobile,ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_content,ch.hour_ischecking
                                                   FROM smc_student_hourstudy as hs
                                                   LEFT JOIN smc_class_hour as ch ON ch.hour_id=hs.hour_id
                                                   LEFT JOIN smc_class as c ON c.class_id=hs.class_id
                                                   LEFT JOIN smc_classroom as cl ON cl.classroom_id=ch.classroom_id
                                                   LEFT JOIN smc_school as s ON s.school_id=c.school_id
                                                   LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id=hs.class_id AND ht.hour_id=hs.hour_id AND ht.teaching_type='0'
                                                   LEFT JOIN smc_staffer as sf ON sf.staffer_id=ht.staffer_id
                                                   WHERE hs.hourstudy_id='{$paramArray['hourstudy_id']}'");
        $status = array('0'=>'未上课','1'=>'已上课');
        $dataOne['hourstudy_checkin_name'] = $status[$dataOne['hourstudy_checkin']];

        $staffer = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname,staffer_img","staffer_id='{$dataOne['staffer_id']}'");
        $student = $this->DataControl->getFieldOne("smc_student","student_cnname,student_img","student_id='{$dataOne['student_id']}'");
        $staffer['student_cnname'] = $student['student_cnname'];
        $staffer['student_img'] = $student['student_img'];
        $score_num = $this->DataControl->selectClear("SELECT ROUND(AVG(sr.sturemarkstar_score), 1) as score_num 
                                                        FROM eas_classhour_sturemark as cs
                                                        LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                                                        WHERE cs.student_id='{$dataOne['student_id']}' AND cs.hour_id='{$dataOne['hour_id']}' AND cs.class_id='{$dataOne['class_id']}'");
        $staffer['score_num'] = $score_num[0]['score_num'];

        $staffer['sturemark'] = $this->DataControl->selectClear("SELECT cs.sturemark_comment,cs.sturemark_picturejson,sr.sturemarkstar_name,sr.sturemarkstar_score 
                                                                     FROM eas_classhour_sturemark as cs
                                                                     LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                                                                     WHERE cs.student_id='{$dataOne['student_id']}' AND cs.hour_id='{$dataOne['hour_id']}' AND cs.class_id='{$dataOne['class_id']}' and cs.sturemark_status =0 ");

        $appraise = $this->DataControl->getFieldOne("eas_student_hourcomment","hourcomment_content,hourcomment_score","student_id='{$dataOne['student_id']}' and hour_id='{$dataOne['hour_id']}'");
        if(!$appraise){
            $appraise = array();
        }

        $data = array();
        $data['list'] = $dataOne;
        $data['staffer'] = $staffer;
        $data['appraise'] = $appraise;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 添加课堂评价
     */
    function AddAppraise($paramArray){
        $data = array();
        $data['hour_id'] = $paramArray['hour_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['hourcomment_content'] = addslashes($paramArray['hourcomment_content']);
        $data['hourcomment_score'] = $paramArray['hourcomment_score'];
        $data['hourcomment_anonymous'] = $paramArray['hourcomment_anonymous'];
        $data['hourcomment_createtime'] = time();
        if ($this->DataControl->insertData('eas_student_hourcomment', $data)) {
            $res = array('error' => 0, 'errortip' => '添加课堂评价成功');
        } else {
            $res = array('error' => 1, 'errortip' => '添加课堂评价失败');
        }
        return $res;
    }

}