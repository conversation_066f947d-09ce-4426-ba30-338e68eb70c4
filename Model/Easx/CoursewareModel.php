<?php

namespace Model\Easx;

Class CoursewareModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            if(!$this->setPublic($publicarray)){
                return false;
            }
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'], $publicarray['re_postbe_id'])) {
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id, $re_postbe_id=0)
    {

        if($re_postbe_id>0){
            $this->stafferOne = $this->DataControl->selectOne("SELECT
                                                                      s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_mobile,s.account_class,sp.postpart_isteregulator,sp.postpart_isbeike
                                                                   FROM
                                                                      smc_staffer as s
                                                                   LEFT JOIN
                                                                      gmc_staffer_postbe as p ON p.staffer_id = s.staffer_id
                                                                   LEFT JOIN
                                                                      smc_school_postpart as sp ON sp.postpart_id = p.postpart_id
                                                                   WHERE
                                                                      s.company_id='{$this->company_id}' AND s.staffer_id = '{$staffer_id}' AND p.postbe_id = '{$re_postbe_id}'");
            if(!$this->stafferOne['postpart_isbeike']){
                $this->error = true;
                $this->errortip = "无趣备课访问权限，请联系管理员开启";
                return false;
            }
        }else{
            $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        }

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }


    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 获取账号资料
     */
    function getUserlist($paramArray){
        $sql="
            SELECT
                s.staffer_branch,
                s.staffer_bakpass as staffer_pass,
                s.staffer_mobile,
                s.staffer_email
            FROM
                smc_staffer AS s 
            WHERE
                s.staffer_id = '{$paramArray['staffer_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_branch"] = "账号";
        $field["staffer_bakpass"] = "密码";
        $field["staffer_mobile"] = "手机号";
        $field["staffer_email"] = "邮箱";

        $result = array();
        if($stafferDetail){
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取账号资料成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取账号资料失败', 'result' => $result);
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 修改密码
     */
    function updatePassAction($paramArray){
        $data = array();
        if($paramArray['staffer_pass1'] == $paramArray['staffer_pass2']){
            $data['staffer_pass'] = md5($paramArray['staffer_pass1']);
            $data['staffer_bakpass'] = $paramArray['staffer_pass1'];
            $data['staffer_updatetime'] = time();
        }else{
            ajax_return(array('error' => 1,'errortip' => "两次输入的密码不相同！"));
        }

        $field = array();
        $field["staffer_pass"] = "md5密码";
        $field["staffer_bakpass"] = "密码备注";
        if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['staffer_id']}'",$data) ) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改密码成功", 'result' => $result);
//            $this->addSmcWorkLog($paramArray['company_id'],$paramArray['school_id'],$paramArray['staffer_id'],"修改密码",'修改密码',dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '修改密码失败', 'result' => $result);
        }
        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 修改个人信息
     */
    function updateStafferInfoAction($paramArray)
    {
        $data = array();
        if(isset($paramArray['staffer_img']) && $paramArray['staffer_img'] !== ''){
            $data['staffer_img'] = $paramArray['staffer_img'];
            $data['staffer_updatetime'] = time();
        }else{
            $res = array('error' => '1', 'errortip' => '头像必须传入');
            return $res;
        }
        if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['staffer_id']}'", $data)) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "个人信息修改成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '个人信息修改失败', 'result' => $result);
        }

        return $res;
    }

    //下拉 -- 校园
    function getSchoolList($paramArray)
    {
        $datawhere = "s.company_id = '{$paramArray['company_id']}'";
        if(isset($paramArray['school_id']) && $paramArray['school_id'] !== ''){
            $datawhere .= " and s.school_id = '{$paramArray['school_id']}'";
        }

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and t.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            } else {
                $datawhere .= " and t.staffer_id = '{$this->staffer_id}'";
            }
        }

        $datalist = $this->DataControl->selectClear("SELECT s.school_id,s.school_cnname
                                                       FROM smc_class_hour_teaching as t
                                                       LEFT JOIN smc_class as c ON c.class_id = t.class_id
                                                       LEFT JOIN smc_school as s ON s.school_id = c.school_id
                                                       WHERE {$datawhere} AND s.school_isclose = 0 GROUP BY s.school_id");

        return $datalist;
    }

    //下拉 -- 课程别
    function getCourseList($paramArray)
    {
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}' and sc.course_inclasstype = '0' and cl.classcode_isbeike = '1'";//and course_isuseeas =1

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and t.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            } else {
                $datawhere .= " and t.staffer_id = '{$this->staffer_id}'";
            }
        }

        $sql = "SELECT sc.course_id,sc.course_cnname,sc.course_branch
                FROM smc_class_hour_teaching as t
                LEFT JOIN smc_class as c ON c.class_id = t.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                WHERE {$datawhere} AND (c.class_status = '0' or c.class_status = '1') AND c.class_type = '0' AND sc.course_status <> '-1' GROUP BY sc.course_id";

        $datalist = $this->DataControl->selectClear($sql);
        if ($datalist) {
            foreach ($datalist as &$vale) {
                $vale['course_cnname'] = $vale['course_cnname'] . '(' . $vale['course_branch'] . ')';
            }
        }

        return $datalist;
    }

    //下拉 -- 教师
    function getTeacherList($paramArray)
    {
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}' and (c.class_status = '0' or c.class_status = '1') and c.class_type = '0' and cl.classcode_isbeike = '1' and th.teachhour_isbeike = '1'";

        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== ''){
            $datawhere .= " and h.hour_day >= '{$paramArray['starttime']}'";
        }
        if(isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and h.hour_day <= '{$paramArray['endtime']}'";
        }

        $sql = "SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname
                FROM smc_class_hour_teaching as ht
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                LEFT JOIN eas_teachhour as th ON th.company_id = sc.company_id AND th.classcode_branch = sc.course_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',h.hour_lessontimes)
                WHERE {$datawhere} and sf.staffer_cnname IS NOT NULL GROUP BY sf.staffer_id";

        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$v){
                if($v['staffer_enname'] && $v['staffer_enname'] != ''){
                    $v['staffer_cnname'] = $v['staffer_cnname'] . '-' . $v['staffer_enname'];
                }
            }
        }

        return $datalist;
    }

    //下拉 -- 教室
    function getClassroomList($paramArray)
    {
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}' and (c.class_status = '0' or c.class_status = '1') and c.class_type = '0' and cl.classcode_isbeike = '1'";

        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$paramArray['class_id']}'";
        }

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')";
                }
            } else {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$this->staffer_id}')";
            }
        }

        $sql = "SELECT sc.classroom_id,sc.classroom_cnname
                FROM smc_class as c
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_classroom as sc ON sc.classroom_id = h.classroom_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                WHERE {$datawhere} AND sc.classroom_status = '1'
                GROUP BY sc.classroom_id";

        $datalist = $this->DataControl->selectClear($sql);

        return $datalist;
    }

    //下拉 -- 班级
    function getClass($paramArray)
    {
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}' and (c.class_status = '0' or c.class_status = '1') and c.class_type = '0' and cl.classcode_isbeike = '1'";

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')";
                }
            } else {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$this->staffer_id}')";
            }
        }

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname
                FROM smc_class as c
                LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                WHERE {$datawhere}";

        $datalist = $this->DataControl->selectClear($sql);

        return $datalist;
    }

    //下拉 -- 班组
    function getCoursetypeList($paramArray)
    {
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}' and c.class_type = '0' and cl.classcode_isbeike = '1'";//and course_isuseeas =1

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and t.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            } else {
                $datawhere .= " and t.staffer_id = '{$this->staffer_id}'";
            }
        }

        $sql = "SELECT ct.coursetype_id,ct.coursetype_cnname,ct.coursetype_branch
                FROM smc_class_hour_teaching as t
                LEFT JOIN smc_class as c ON c.class_id = t.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = sc.coursetype_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                WHERE {$datawhere} AND (c.class_status = '0' or c.class_status = '1') AND sc.course_status <> '-1' GROUP BY ct.coursetype_id";

        $datalist = $this->DataControl->selectClear($sql);
        if ($datalist) {
            foreach ($datalist as &$vale) {
                $vale['course_cnname'] = $vale['course_cnname'] . '(' . $vale['course_branch'] . ')';
            }
        }

        return $datalist;
    }

    //下拉 -- 班种
    function getCoursecatList($paramArray)
    {
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}' and c.class_type = '0' and cl.classcode_isbeike = '1'";//and course_isuseeas =1

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and t.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            } else {
                $datawhere .= " and t.staffer_id = '{$this->staffer_id}'";
            }
        }

        $sql = "SELECT cc.coursecat_id,cc.coursecat_cnname,cc.coursecat_branch
                FROM smc_class_hour_teaching as t
                LEFT JOIN smc_class as c ON c.class_id = t.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = sc.coursecat_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = sc.course_branch and cl.company_id = sc.company_id
                WHERE {$datawhere} AND (c.class_status = '0' or c.class_status = '1') AND sc.course_status <> '-1' GROUP BY cc.coursecat_id";

        $datalist = $this->DataControl->selectClear($sql);
        if ($datalist) {
            foreach ($datalist as &$vale) {
                $vale['course_cnname'] = $vale['course_cnname'] . '(' . $vale['course_branch'] . ')';
            }
        }

        return $datalist;
    }


    /**
     * @param $paramArray
     * @return array
     * 课件 -- 获取课件信息
     */
    function getClassList($paramArray){
        $datawhere = "cs.company_id = '{$this->company_id}' and (cs.class_status = '0' or cs.class_status = '1') and cs.class_type = '0' and co.course_inclasstype = '0'";
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        if(isset($request['starttime']) && $request['starttime'] !== ''){
            $datawhere .= " and cs.class_stdate >= '{$request['starttime']}'";
        }
        if(isset($request['endtime']) && $request['endtime'] !== ''){
            $datawhere .= " and cs.class_enddate <= '{$request['endtime']}'";
        }
        if(isset($paramArray['class_status']) && $paramArray['class_status'] !== ''){
            $datawhere .= " and cs.class_status='{$paramArray['class_status']}'";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] !== ''){
            $datawhere .= " and s.school_id='{$paramArray['school_id']}'";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !== ''){
            $datawhere .= " and co.course_id='{$paramArray['course_id']}'";
        }
        $having = "1=1";
        if(isset($paramArray['classroom_id']) && $paramArray['classroom_id'] !== ''){
            $having .= " and classroom_cnname is not NUll";
            $datawhere .= " and ch.classroom_id = '{$paramArray['classroom_id']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%')";
        }
        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and cs.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
                }else{
                    $datawhere .= " and cs.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
                }
            } else {
                //一定是教师自己的课表
                $datawhere .= " and cs.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
            }
        }else{
            if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                $datawhere .= " and cs.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
            } else {
                $datawhere .= " and cs.class_id = '0'";
            }
        }

        $sql = "select ht.class_id, cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,co.course_cnname,s.school_shortname as school_cnname,cs.class_fullnums,co.course_branch,ht.hour_lessontimes,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as staffer_cnname,
	            (SELECT p.prepare_createtime FROM eas_prepare as p WHERE p.teacher_branch=sf.staffer_branch and p.class_branch = cs.class_branch ORDER BY p.prepare_createtime DESC limit 0,1 ) as prepare_createtime,
                ( SELECT COUNT(ch.hour_id)
                    FROM
                        smc_class_hour as ch
                    LEFT JOIN
                        smc_class as c ON c.class_id = ch.class_id
                    LEFT JOIN
                        smc_course as sc ON sc.course_id = c.course_id
                    LEFT JOIN
                        eas_classcode as cl ON cl.classcode_branch = sc.course_branch AND cl.company_id = sc.company_id
                    LEFT JOIN
                        eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',ch.hour_lessontimes)
                    WHERE
                        ch.class_id = cs.class_id AND ch.hour_ischecking = '1' AND ch.hour_iswarming = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                ) as hour_num,
                ( SELECT COUNT(ch.hour_id)
                    FROM
                        smc_class_hour as ch
                    LEFT JOIN
                        smc_class as c ON c.class_id = ch.class_id
                    LEFT JOIN
                        smc_course as sc ON sc.course_id = c.course_id
                    LEFT JOIN
                        eas_classcode as cl ON cl.classcode_branch = sc.course_branch AND cl.company_id = sc.company_id
                    LEFT JOIN
                        eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',ch.hour_lessontimes)
                    WHERE
                        ch.class_id = cs.class_id AND ch.hour_ischecking <> '-1' AND ch.hour_iswarming = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                ) as class_hournums,
                ( SELECT COUNT(tc.teaching_id)
                    FROM
                      smc_class_hour_teaching as tc
                    LEFT JOIN
                      smc_class as c ON c.class_id = tc.class_id
                    LEFT JOIN
                      smc_course as sc ON sc.course_id = c.course_id
                    LEFT JOIN
                        smc_class_hour AS h ON h.hour_id = tc.hour_id
                    LEFT JOIN
                      eas_classcode as cl ON cl.classcode_branch = sc.course_branch AND cl.company_id = sc.company_id
                    LEFT JOIN
                      eas_teachhour as th ON th.company_id = sc.company_id AND th.classcode_branch = sc.course_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',tc.hour_lessontimes)
                    WHERE
                      tc.class_id = cs.class_id AND tc.staffer_id = ht.staffer_id AND tc.teaching_type = '0' AND h.hour_iswarming = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                      AND tc.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = ht.staffer_id)
                ) as prepare_allnum,
                ( SELECT COUNT(DISTINCT p.teachhour_branch)
                    FROM
                    	smc_class_hour_teaching AS tc
                    LEFT JOIN
                        smc_class AS c ON c.class_id = tc.class_id
                    LEFT JOIN
                        smc_course AS sc ON sc.course_id = c.course_id
                    LEFT JOIN
                        smc_staffer AS stf ON stf.staffer_id = tc.staffer_id
                    LEFT JOIN
                        smc_class_hour AS h ON h.hour_id = tc.hour_id
                    LEFT JOIN
                        eas_prepare as p ON p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch
                    LEFT JOIN
                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                    LEFT JOIN
                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                    WHERE
                        tc.class_id = cs.class_id AND tc.staffer_id = ht.staffer_id AND tc.teaching_type = '0' AND h.hour_iswarming = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1' AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                    AND (SELECT COUNT(p.prepare_id)
                        FROM
                            eas_prepare as p
                        LEFT JOIN
                            eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                        LEFT JOIN
                            eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                        WHERE
                            p.company_id = cs.company_id AND p.class_branch = cs.class_branch AND p.teacher_branch = sf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                         = (SELECT COUNT(tp.teachplan_id)
                        FROM
                            eas_teachhour_teachplan as tp
                        LEFT JOIN
                          eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                        LEFT JOIN
                            eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                        WHERE
                            tp.company_id = cs.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1')
                ) AS prepare_num,
                ( SELECT COUNT(study_isreading) FROM smc_student_study as sy WHERE sy.class_id = ht.class_id and sy.study_isreading = 1) as study_num,
                (
                    SELECT group_concat(DISTINCT cr.classroom_cnname) 
                    FROM smc_class_hour AS h 
                    LEFT JOIN smc_classroom as cr ON cr.classroom_id=h.classroom_id 
                    WHERE h.class_id=cs.class_id
                ) as classroom_cnname
                from smc_class_hour_teaching as ht 
                left join smc_staffer as sf ON  sf.staffer_id = ht.staffer_id
                left join smc_class_hour as ch ON ch.hour_id = ht.hour_id
                left join smc_class as cs ON cs.class_id = ch.class_id 
                left join smc_course as co ON co.course_id = cs.course_id 
                left join smc_school as s ON s.school_id = cs.school_id
                left join eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                where {$datawhere} and ht.staffer_id > 0 and cc.classcode_isbeike = '1'
                group by ht.class_id 
                HAVING {$having}
                Limit {$pagestart},{$num}";
        $classList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array('0'=>'待开班','1'=>'进行中','-1'=>'已结束'));
        if($classList){
            foreach($classList as &$v){
                if($this->stafferOne['postpart_isteregulator'] == '1'){
                    $hourList = $this->DataControl->selectClear("SELECT
                                                                      tc.staffer_id
                                                                   FROM
                                                                      smc_class_hour_teaching as tc
                                                                   LEFT JOIN
                                                                      smc_class as c ON c.class_id = tc.class_id
                                                                   LEFT JOIN
                                                                      smc_course as sc ON sc.course_id = c.course_id
                                                                   LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = sc.course_branch AND cl.company_id = sc.company_id
                                                                   LEFT JOIN
                                                                      eas_teachhour as th ON th.company_id = sc.company_id AND th.classcode_branch = sc.course_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',tc.hour_lessontimes)
                                                                   WHERE
                                                                      tc.class_id = '{$v['class_id']}' AND tc.teaching_type = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'");
                    if($hourList){
                        foreach($hourList as $val){
                            if($val['staffer_id'] == $this->staffer_id){
                                $v['is_beike'] = true;
                            }
                        }
                    }
                }

                $v['class_status_name'] = $status[$v['class_status']];
                $v['class_hour_num'] = $v['hour_num'] . '/' . $v['class_hournums'];
                $v['class_preparenums'] = $v['prepare_num'] . '/' . $v['prepare_allnum'];
                $v['class_fullnums'] = $v['study_num'] . '/' . $v['class_fullnums'];
                if($v['prepare_createtime']){
                    $v['prepare_createtime'] = date("Y-m-d H:i:s",$v['prepare_createtime']);
                }
            }
        }else{
            $classList = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectClear(" 
                    select ht.class_id,
                    (select group_concat( DISTINCT cm.classroom_cnname) from smc_classroom as cm where cm.classroom_id = ch.classroom_id ) as classroom_cnname
                    from smc_class_hour_teaching as ht 
                    left join smc_staffer as sf ON  sf.staffer_id = ht.staffer_id
                    left join smc_class_hour as ch ON ch.hour_id = ht.hour_id
                    left join smc_class as cs ON cs.class_id = ch.class_id 
                    left join smc_course as co ON co.course_id = cs.course_id 
                    left join smc_school as s ON s.school_id = cs.school_id
                    left join eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                    where {$datawhere} and ht.staffer_id > 0 and cc.classcode_isbeike = '1'
                    group by ht.class_id
                    HAVING {$having}
				");
            if (is_array($all_num)) {
                $data['allnums'] = count($all_num);
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $classList;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 备课 -- 获取课时
     */
    function getClasshour($paramArray){
        $classOne = $this->DataControl->getFieldOne("smc_class","class_cnname","class_id='{$paramArray['class_id']}'");
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $datawhere = "c.company_id = '{$this->company_id}' AND c.class_id = '{$paramArray['class_id']}' AND (c.class_status = '0' OR c.class_status = '1') and c.class_type = '0' AND sc.course_inclasstype = '0'";

        $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$this->staffer_id}')
                     and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$this->staffer_id}')";

        $sql =  "SELECT
                      h.hour_id,h.class_id,h.hour_lessontimes,h.hour_name,h.hour_ischecking,h.hour_day,sc.course_branch,
                      (SELECT COUNT(tp.teachplan_id) FROM eas_teachhour_teachplan as tp WHERE tp.company_id = c.company_id AND tp.classcode_branch = sc.course_branch AND tp.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes )) as prepare_allnum,
                      (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0 AND p.prepare_status = '1') as prepare_num
                 FROM
                      smc_class as c
                 LEFT JOIN
                      smc_class_hour as h ON h.class_id = c.class_id
                 LEFT JOIN
                      smc_class_hour_teaching AS ht ON ht.class_id = c.class_id AND ht.hour_id = h.hour_id
                 LEFT JOIN
                      smc_course as sc ON sc.course_id = c.course_id
                 LEFT JOIN
                      eas_classcode as cl ON cl.classcode_branch = sc.course_branch AND cl.company_id = sc.company_id
                 LEFT JOIN
                      eas_teachhour as th ON th.company_id = sc.company_id AND th.classcode_branch = sc.course_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',h.hour_lessontimes)
                 WHERE
                      {$datawhere} AND ht.staffer_id = '{$this->staffer_id}' AND h.hour_ischecking <> '-1' AND h.hour_iswarming = '0' AND ht.teaching_type = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                 ORDER BY
                      h.hour_ischecking DESC,h.hour_id ASC
                 LIMIT {$pagestart},{$num}";
        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$v){
                if($v['prepare_allnum'] && $v['prepare_allnum'] == $v['prepare_num']){
					$v['prepare_status'] = '1';
                    $v['prepare_status_name'] = '已备课';
                }else{
                    $v['prepare_status'] = '0';
                    $v['prepare_status_name'] = '未备课';
                }
                if($v['hour_ischecking'] == '0'){
                    $v['hour_ischecking_name'] = '未上课';
                }elseif($v['hour_ischecking'] == '1'){
                    $v['hour_ischecking_name'] = '已上课';
                }
                $v['is_beike'] = true;
            }
        }else{
            $datalist = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT
                                                              COUNT(h.hour_id) as num
                                                         FROM
                                                              smc_class as c
                                                         LEFT JOIN
                                                              smc_class_hour as h ON h.class_id = c.class_id
                                                         LEFT JOIN
                                                              smc_class_hour_teaching AS ht ON ht.class_id = c.class_id AND ht.hour_id = h.hour_id
                                                         LEFT JOIN
                                                              smc_course as sc ON sc.course_id = c.course_id
                                                         LEFT JOIN
                                                              eas_classcode as cl ON cl.classcode_branch = sc.course_branch AND cl.company_id = sc.company_id
                                                         LEFT JOIN
                                                              eas_teachhour as th ON th.company_id = sc.company_id AND th.classcode_branch = sc.course_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',h.hour_lessontimes)
                                                         WHERE
                                                              {$datawhere} AND ht.staffer_id = '{$this->staffer_id}' AND h.hour_ischecking <> '-1' AND h.hour_iswarming = '0' AND ht.teaching_type = '0' AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;
        $data['classname'] = $classOne['class_cnname'];

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 备课 -- 添加/修改批注
     */
    function PersonPostilApi($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT prepare_id,prepare_postil FROM eas_prepare WHERE prepare_id = '{$paramArray['prepare_id']}'");
        if($dataOne){
            $data = array();
            $data['prepare_postil'] = $paramArray['prepare_postil'];
            $data['prepare_updatetime'] = time();
            if($this->DataControl->updateData("eas_prepare","prepare_id='{$paramArray['prepare_id']}'",$data)){
                $res = array('error' => 0, 'errortip' => '提交成功');
            }else{
                $res = array('error' => 1, 'errortip' => '提交失败');
            }
        }else{
            $res = array('error' => 1, 'errortip' => '暂未创建教案备课信息');
        }

        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 备课 -- 添加个人教学作品
     */
    function AddTeachplanApi($paramArray)
    {
        $teachhour_branch = $paramArray['course_branch'].'-'.$paramArray['hour_lessontimes'];
        $class = $this->DataControl->getFieldOne("smc_class","class_branch","class_id='{$paramArray['class_id']}'");
        $teachplan = $this->DataControl->selectOne("SELECT t.*,p.prepare_wordcontent,p.class_branch,p.prepare_postil FROM eas_teachhour_teachplan as t LEFT JOIN eas_prepare as p ON p.company_id=t.company_id AND p.class_branch='{$class['class_branch']}' AND p.teachhour_branch='{$teachhour_branch}' AND p.teachplan_id = t.teachplan_id AND p.teacher_branch='{$this->stafferOne['staffer_branch']}' WHERE t.teachplan_id = '{$paramArray['teachplan_id']}'");
        if($this->DataControl->getOne("eas_teachhour_tempworks","company_id='{$teachplan['company_id']}' and classcode_branch='{$teachplan['classcode_branch']}' and teachhour_branch='{$teachplan['teachhour_branch']}' and teacher_branch='{$this->stafferOne['staffer_branch']}' and tempworks_status=0")){
            $res = array('error' => 1, 'errortip' => '已有未审核的教学作品，审核后才能上传');
            return $res;
        }
        $data = array();
        $data['company_id'] = $teachplan['company_id'];
        $data['classcode_branch'] = $teachplan['classcode_branch'];
        $data['teachhour_branch'] = $teachplan['teachhour_branch'];
        $data['teacher_branch'] = $this->stafferOne['staffer_branch'];
        $data['tempworks_name'] = $paramArray['tempworks_name'];
        $data['tempworks_videoname'] = $paramArray['tempworks_videoname'];
        $data['tempworks_videourl'] = $paramArray['tempworks_videourl'];
        $data['tempworks_class'] = $teachplan['teachplan_class'];
        if ($teachplan['teachplan_class'] == '0') {
            $data['tempworks_fileurl'] = $teachplan['teachplan_fileurl'];
            if($teachplan['prepare_wordcontent']){
                $data['tempworks_wordcontent'] = addslashes($teachplan['prepare_wordcontent']);
            }else{
                $data['tempworks_wordcontent'] = addslashes($teachplan['teachplan_wordcontent']);
            }
        } else {
            $data['tempworks_imgurl'] = $teachplan['teachplan_imgurl'];
        }
        $data['tempworks_postil'] = $teachplan['teachplan_postil'];
        $data['tempworks_matters'] = $teachplan['teachplan_matters'];
        $data['tempworks_createtime'] = time();
        $dataid = $this->DataControl->insertData('eas_teachhour_tempworks', $data);
        if ($dataid) {
            if($paramArray['teachplan_id']){
                if(!$this->DataControl->getOne("eas_prepare","teacher_branch='{$this->stafferOne['staffer_branch']}' and tempworks_id='{$dataid}'")){
                    $array = array();
                    $array['company_id'] = $teachplan['company_id'];
                    $array['classcode_branch'] = $teachplan['classcode_branch'];
                    $array['teacher_branch'] = $this->stafferOne['staffer_branch'];
                    $array['class_branch'] = $teachplan['class_branch'];
                    $array['teachhour_branch'] = $teachplan['teachhour_branch'];
                    $array['tempworks_id'] = $dataid;
                    $array['prepare_postil'] = $teachplan['prepare_postil'];
                    $array['prepare_status'] = '0';
                    $array['prepare_createtime'] = time();
                    $this->DataControl->insertData("eas_prepare", $array);
                }

                $teachpics = $this->DataControl->selectClear("SELECT * FROM eas_teachhour_teachpics WHERE teachplan_id = '{$paramArray['teachplan_id']}'");
                if($teachpics){
                    foreach($teachpics as $v){
                        $list = array();
                        $list['company_id'] = $v['company_id'];
                        $list['tempworks_id'] = $dataid;
                        $list['teachpics_name'] = $v['teachpics_name'];
                        $list['teachpics_url'] = $v['teachpics_url'];
                        $list['teachpics_sort'] = $v['teachpics_sort'];
                        $list['teachpics_createtime'] = time();
                        $listid = $this->DataControl->insertData("eas_teachhour_teachpics", $list);
                        if($listid){
                            $pics = $this->DataControl->getOne("eas_prepare_teachpics","company_id='{$v['company_id']}' AND teachpics_id='{$v['teachpics_id']}'");
                            if($pics){
                                $info = array();
                                $info['company_id'] = $pics['company_id'];
                                $info['teachpics_id'] = $listid;
                                $info['prepare_isemphasis'] = $pics['prepare_isemphasis'];
                                $info['prepare_isnokeep'] = $pics['prepare_isnokeep'];
                                $this->DataControl->insertData("eas_prepare_teachpics", $info);
                            }
                        }
                    }
                }
            }
            $res = array('error' => '0', 'errortip' => "添加优秀教学作品成功");
        } else {
            $res = array('error' => '1', 'errortip' => '添加优秀教学作品失败');
        }

        return $res;
    }

    function getCurl($url){
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    /**
     * @param $paramArray
     * @return array
     * 备课 -- 获取教案信息
     */
    function getLessonPlan($paramArray){
        $teachhour_branch = $paramArray['course_branch'].'-'.$paramArray['hour_lessontimes'];

        $classname = $this->DataControl->selectOne("SELECT c.class_cnname,co.course_branch,co.course_branch as classcode_branch,
                                                      (SELECT ch.hour_name FROM smc_class_hour as ch WHERE ch.class_id = c.class_id AND ch.hour_lessontimes = '{$paramArray['hour_lessontimes']}' LIMIT 1) as hour_name,
                                                      (SELECT cd.classcode_isopen FROM eas_classcode AS cd WHERE cd.company_id = c.company_id AND cd.classcode_branch = co.course_branch) as classcode_isopen
                                                      FROM smc_class as c
                                                      LEFT JOIN smc_course as co ON co.course_id=c.course_id
                                                      WHERE c.class_id='{$paramArray['class_id']}'");
        $classname['teachhour_branch'] = $teachhour_branch;

        $teachplan = $this->DataControl->selectClear("SELECT t.teachplan_id,t.teachplan_name,t.teachplan_class,t.teachplan_postil,t.teachplan_matters,t.teachplan_coverimg,t.teachplan_fileurl,t.teachplan_videourl,t.teachplan_videocovering,t.teachplan_wordcontent,ep.prepare_id,ep.prepare_postil,ep.prepare_status,ep.prepare_watchtime,ep.prepare_watchfinish,
                                                      (
                                                        SELECT tp.teachpics_url
                                                        FROM eas_teachhour_teachpics as tp
                                                        WHERE tp.teachplan_id=t.teachplan_id
                                                        ORDER BY tp.teachpics_id ASC LIMIT 0,1
                                                      ) as teachpics_url,
                                                      (
                                                        SELECT COUNT(tp.teachpics_id) as num
                                                        FROM eas_teachhour_teachpics as tp
                                                        WHERE tp.teachplan_id=t.teachplan_id
                                                      ) as teachpics_num,
                                                      (
                                                        SELECT ec.classcode_isopen
                                                        FROM eas_classcode as ec
                                                        WHERE ec.company_id = t.company_id AND ec.classcode_branch=t.classcode_branch
                                                      ) as classcode_isopen
                                                      FROM eas_teachhour_teachplan AS t
                                                      LEFT JOIN eas_prepare as ep ON ep.teachplan_id=t.teachplan_id AND ep.teacher_branch='{$this->stafferOne['staffer_branch']}'
                                                      WHERE t.company_id = '{$this->company_id}' AND t.teachhour_branch='{$teachhour_branch}' GROUP BY t.teachplan_id");
//        and t.teachplan_class='{$paramArray['teachplan_class']}'
        if($teachplan){
            foreach($teachplan as &$v) {
                if(!$v['prepare_id']){
                    $v['prepare_id'] = "";
                }
                if(!$v['prepare_postil']){
                    $v['prepare_postil'] = "";
                }
                if($v['prepare_status'] == ''){
                    $v['prepare_status'] = 0;
                }
                if($v['prepare_watchtime'] == ''){
                    $v['prepare_watchtime'] = 0;
                }
                if($v['prepare_watchfinish'] == ''){
                    $v['prepare_watchfinish'] = 0;
                }
                if ($v['teachplan_class'] == '1') {
                    $v['is_img'] = true;
                } else {
                    $v['is_img'] = false;
                }
                $collect_status = $this->DataControl->getFieldOne("eas_teachhour_teachplan_collect","collect_status","staffer_id='{$this->staffer_id}' and teachplan_id='{$v['teachplan_id']}'");
                if($collect_status['collect_status'] == 1){
                    $v['is_collect'] = true;
                }else{
                    $v['is_collect'] = false;
                }
                $prepare = $this->DataControl->getOne("eas_prepare","teachplan_id='{$v['teachplan_id']}' and teacher_branch='{$this->stafferOne['staffer_branch']}'");
                if($prepare){
                    $v['is_cunzai'] = true;
                }else{
                    $v['is_cunzai'] = false;
                }
            }
        }else{
            $teachplan = array();
        }

        $temppostil = $this->DataControl->selectClear("SELECT
                                                            w.tempworks_id,w.tempworks_name,w.tempworks_videourl,w.tempworks_videourl,w.tempworks_createtime,s.staffer_cnname,s.staffer_img,s.staffer_sex,
                                                            (
                                                              SELECT COUNT(wc.staffer_id)
                                                              FROM eas_teachhour_watch as wc
                                                              WHERE wc.tempworks_id = w.tempworks_id
                                                            ) as watch_num,
                                                            (
                                                              SELECT COUNT(p.staffer_id)
                                                              FROM eas_teachhour_praise as p
                                                              WHERE p.tempworks_id = w.tempworks_id
                                                            ) as praise_num
                                                         FROM
                                                            eas_teachhour_tempworks as w
                                                         LEFT JOIN
                                                            smc_staffer as s ON s.staffer_branch = w.teacher_branch
                                                         WHERE
                                                            w.company_id = '{$this->company_id}' AND w.teachhour_branch = '{$teachhour_branch}' AND w.tempworks_status = '1'
                                                         LIMIT 2");
        if($temppostil){
            foreach($temppostil as &$v) {
                $v['tempworks_createtime'] = date("Y-m-d H:i",$v['tempworks_createtime']);
                if($this->DataControl->getOne("eas_teachhour_praise","tempworks_id='{$v['tempworks_id']}' and staffer_id='{$this->staffer_id}'")){
                    $v['is_zan'] = true;
                }else{
                    $v['is_zan'] = false;
                }
            }
        }else{
            $temppostil = array();
        }

        $data = array();
        $data['classname'] = $classname;
        $data['list'] = $teachplan;
        $data['temppostil'] = $temppostil;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 备课 -- 在线编辑教案信息页面
     */
    function EditLessonPlan($paramArray)
    {
        $teachhour_branch = $paramArray['course_branch'].'-'.$paramArray['hour_lessontimes'];

        $classname = $this->DataControl->selectOne("SELECT c.class_cnname,c.class_branch,co.course_branch,co.course_branch as classcode_branch,
                                                      (SELECT ch.hour_name FROM smc_class_hour as ch WHERE ch.class_id = c.class_id AND ch.hour_lessontimes = '{$paramArray['hour_lessontimes']}' LIMIT 1) as hour_name
                                                      FROM smc_class as c
                                                      LEFT JOIN smc_course as co ON co.course_id=c.course_id
                                                      WHERE c.class_id='{$paramArray['class_id']}'");
        $classname['teachhour_branch'] = $teachhour_branch;

        $datalist = $this->DataControl->selectOne("SELECT t.teachplan_id,t.teachplan_wordcontent,t.teachplan_videourl,t.teachplan_postil,p.prepare_id,p.prepare_watchfinish,p.prepare_wordcontent,p.prepare_watchtime
                                                     FROM eas_teachhour_teachplan AS t
                                                     LEFT JOIN eas_prepare as p ON p.company_id = t.company_id AND p.class_branch = '{$classname['class_branch']}' AND p.teachhour_branch = t.teachhour_branch AND p.teachplan_id = t.teachplan_id AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}'
                                                     WHERE t.teachplan_id='{$paramArray['teachplan_id']}'");

        $tempvideo = $this->DataControl->selectClear("SELECT
                                                            w.tempworks_id,w.tempworks_name,w.tempworks_videourl,w.tempworks_videourl,w.tempworks_createtime,s.staffer_cnname,s.staffer_img,s.staffer_sex,
                                                            (
                                                              SELECT COUNT(wc.staffer_id)
                                                              FROM eas_teachhour_watch as wc
                                                              WHERE wc.tempworks_id = w.tempworks_id
                                                            ) as watch_num,
                                                            (
                                                              SELECT COUNT(p.staffer_id)
                                                              FROM eas_teachhour_praise as p
                                                              WHERE p.tempworks_id = w.tempworks_id
                                                            ) as praise_num
                                                         FROM
                                                            eas_teachhour_tempworks as w
                                                         LEFT JOIN
                                                            smc_staffer as s ON s.staffer_branch = w.teacher_branch
                                                         WHERE
                                                            w.company_id = '{$this->company_id}' AND w.teachhour_branch = '{$teachhour_branch}' AND w.tempworks_status = '1'
                                                         LIMIT 2");
        if($tempvideo){
            foreach($tempvideo as &$v) {
                $v['tempworks_createtime'] = date("Y-m-d H:i",$v['tempworks_createtime']);
            }
        }else{
            $tempvideo = array();
        }

        $data = array();
        $data['list'] = $datalist;
        $data['info'] = $classname;
        $data['tempvideo'] = $tempvideo;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 备课 -- 在线编辑教案信息操作
     */
    function EditLessonPlanApi($paramArray){
        $data = array();
        $data['prepare_wordcontent'] = $paramArray['prepare_wordcontent'];
        $data['prepare_updatetime'] = time();

        if($this->DataControl->updateData("eas_prepare","prepare_id='{$paramArray['prepare_id']}'",$data)){
            $res = array('error' => 0, 'errortip' => '在线编辑教案信息成功');
        }else{
            $res = array('error' => 1, 'errortip' => '在线编辑教案信息失败');
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 查看课程 -- 教案备课信息创建
     */
    function AddLessonPlanInfo($paramArray){
        $teachhour_branch = $paramArray['course_branch'].'-'.$paramArray['hour_lessontimes'];
        $class = $this->DataControl->getFieldOne("smc_class","class_branch","class_id='{$paramArray['class_id']}'");
        $learning = $this->DataControl->getFieldOne("eas_prepare","prepare_id","company_id='{$paramArray['company_id']}' and class_branch='{$class['class_branch']}' and teachhour_branch='{$teachhour_branch}' and teacher_branch='{$this->stafferOne['staffer_branch']}' and teachplan_id='{$paramArray['teachplan_id']}'");
        $result = array();
        if($learning){
            $result['prepare_id'] = $learning['prepare_id'];
            $res = array('error' => 1, 'errortip' => '已创建过教案备课信息', 'result' => $result);
            return $res;
        }
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['classcode_branch'] = $paramArray['classcode_branch'];
        $data['teacher_branch'] = $this->stafferOne['staffer_branch'];
        $data['class_branch'] = $class['class_branch'];
        $data['teachhour_branch'] = $paramArray['course_branch'].'-'.$paramArray['hour_lessontimes'];
        $data['teachplan_id'] = $paramArray['teachplan_id'];
        $data['prepare_status'] = '0';
        $data['prepare_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_prepare", $data);
        if($dataid){
            $result['prepare_id'] = $dataid;
            $res = array('error' => 0, 'errortip' => '教案备课信息创建成功', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '教案备课信息创建失败', 'result' => $result);
        }

        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 备课 -- 备案进程
     */
    function Process($paramArray){
        $data = array();
        $data['prepare_status'] = $paramArray['prepare_status'];
        $learning = $this->DataControl->getFieldOne("eas_prepare","prepare_watchtime","prepare_id='{$paramArray['prepare_id']}'");
        if($paramArray['prepare_watchtime'] > $learning['prepare_watchtime']){
            $data['prepare_watchtime'] = $paramArray['prepare_watchtime'];
            $data['prepare_watchfinish'] = $paramArray['prepare_watchfinish'];
        }
        $tip = "";
        if($paramArray['prepare_status'] == '1'){
            $data['prepare_finishtime'] = time();
            $tip = "已完成";
        }elseif($paramArray['prepare_status'] == '0'){
            $tip = "进行中";
        }
        if($this->DataControl->updateData("eas_prepare", "prepare_id='{$paramArray['prepare_id']}'", $data)){
            $res = array('error' => 0, 'errortip' => '教案备课' . $tip);
        }else{
            $res = array('error' => 1, 'errortip' => '教案备课失败');
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 课件 -- 获取预览批注/预览教案
     */
    function getCourseware($paramArray){
        $datawhere = "";
        if($paramArray['code'] == '1'){
            $datawhere = ",pt.prepare_isemphasis,pt.prepare_isnokeep";
        }
        $dataList = $this->DataControl->selectOne("SELECT t.teachplan_name,t.teachplan_coverimg,t.teachplan_postil,t.teachplan_matters,t.teachplan_videourl,c.class_cnname,c.class_enname,c.class_stdate,c.class_timestr,p.prepare_postil,
                                                  (SELECT sf.staffer_cnname FROM smc_staffer as sf WHERE sf.staffer_branch=p.teacher_branch) as staffer_cnname
                                                  FROM eas_teachhour_teachplan as t 
                                                  LEFT JOIN eas_prepare as p ON p.teachhour_branch=t.teachhour_branch AND p.teachplan_id = t.teachplan_id AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}'
                                                  LEFT JOIN smc_class as c ON c.class_branch=p.class_branch
                                                  WHERE t.teachplan_id='{$paramArray['teachplan_id']}'");

        $teachpics = $this->DataControl->selectClear("SELECT t.teachplan_name,tp.teachpics_id,tp.teachpics_url,tp.teachpics_sort {$datawhere}
                                                    FROM eas_teachhour_teachplan as t 
                                                    LEFT JOIN eas_teachhour_teachpics as tp ON tp.teachplan_id=t.teachplan_id 
                                                    LEFT JOIN eas_prepare_teachpics as pt ON pt.teachpics_id=tp.teachpics_id 
                                                    WHERE t.teachplan_id='{$paramArray['teachplan_id']}'");

        if($paramArray['code'] == '1'){
            if($teachpics){
                foreach($teachpics as &$v){
                    if($v['prepare_isemphasis']){
                        $v['prepare_isemphasis_name'] = '【重难点】';
                    }
                    if($v['prepare_isnokeep'] == '1'){
                        $v['prepare_isnokeep_name'] = '【不讲】';
                    }
                }
            }
        }

        $data = array();
        $data['list'] = $dataList;
        $data['teachpics'] = $teachpics;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 课件 -- 获取教学图片
     */
    function getTeachpics($paramArray){
        $sql = "SELECT t.teachplan_name,tt.teachpics_id,tt.teachpics_url,tt.teachpics_sort 
                FROM eas_teachhour_teachpics as tt
                LEFT JOIN eas_teachhour_teachplan as t ON t.teachplan_id=tt.teachplan_id
                WHERE tt.company_id='{$this->company_id}' AND tt.teachplan_id='{$paramArray['teachplan_id']}'";
        $teacpics = $this->DataControl->selectClear($sql);
        if(!$teacpics){
            $teacpics = array();
        }

        return $teacpics;
    }

    /**
     * @param $paramArray
     * @return array
     * 课件 -- 获取其他优秀教学作品
     */
    function getExcellentPostilVideo($paramArray){
        $datawhere = "1";

        if(isset($paramArray['order_praise']) && $paramArray['order_praise'] == '1'){
            $order = "ORDER BY praise_num DESC";
        }elseif(isset($paramArray['order_praise']) && $paramArray['order_praise'] == '2'){
            $order = "ORDER BY praise_num ASC";
        }
        if(isset($paramArray['order_watch']) && $paramArray['order_watch'] == '1'){
            $order = "ORDER BY watch_num DESC";
        }elseif(isset($paramArray['order_watch']) && $paramArray['order_watch'] == '2'){
            $order = "ORDER BY watch_num ASC";
        }
        if(isset($paramArray['order_time']) && $paramArray['order_time'] == '1'){
            $order = "ORDER BY w.tempworks_examtime DESC";
        }
        if(isset($paramArray['keyword']) && $paramArray['keyword'] !== ''){
            $datawhere .= " and (s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $Perfect_sql = "SELECT
                            w.tempworks_id,w.teachhour_branch,w.tempworks_status,w.tempworks_name,w.tempworks_videourl,w.tempworks_videourl,w.tempworks_createtime,s.staffer_cnname,s.staffer_img,s.staffer_sex,
                            FROM_UNIXTIME(w.tempworks_createtime,'%Y-%m-%d %H:%i:%s') AS tempworks_createtime,
                            (
                              SELECT COUNT(wc.staffer_id) as num
                              FROM eas_teachhour_watch as wc
                              WHERE wc.tempworks_id = w.tempworks_id
                            ) as watch_num,
                            (
                              SELECT COUNT(p.staffer_id) as num
                              FROM eas_teachhour_praise as p
                              WHERE p.tempworks_id = w.tempworks_id
                            ) as praise_num
                        FROM
                            eas_teachhour_tempworks as w
                        LEFT JOIN
                            smc_staffer as s ON s.staffer_branch = w.teacher_branch
                        WHERE {$datawhere} AND w.teachhour_branch='{$paramArray['teachhour_branch']}' AND w.tempworks_status = '1'
                        {$order} LIMIT {$pagestart},{$num} ";
        $PerfectOne = $this->DataControl->selectOne($Perfect_sql);
        $PerfectOne['isShowAll'] = false;
        if($this->DataControl->getOne("eas_teachhour_praise","tempworks_id='{$PerfectOne['tempworks_id']}' and staffer_id='{$this->staffer_id}'")){
            $PerfectOne['is_zan'] = true;
        }else{
            $PerfectOne['is_zan'] = false;
        }
        $Perfect = $this->DataControl->selectClear($Perfect_sql);
        if($Perfect){
            foreach($Perfect as &$v){
                $v['isShowAll'] = false;
                if($this->DataControl->getOne("eas_teachhour_praise","tempworks_id='{$v['tempworks_id']}' and staffer_id='{$this->staffer_id}'")){
                    $v['is_zan'] = true;
                }else{
                    $v['is_zan'] = false;
                }
            }
        }else{
            $Perfect = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] !== '') {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(w.tempworks_id) as num
                                                        FROM
                                                            eas_teachhour_tempworks as w
                                                        LEFT JOIN
                                                            smc_staffer as s ON s.staffer_branch = w.teacher_branch
                                                        WHERE {$datawhere} AND w.teachhour_branch = '{$paramArray['teachhour_branch']}' AND w.tempworks_status = '1'");
            if ($all_num) {
                $data['allnum'] = $all_num['num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $branch = explode("-", $paramArray['teachhour_branch']);
        $hour_name = $this->DataControl->selectOne("SELECT c.class_cnname,h.hour_name
                                                      FROM smc_class_hour as h
                                                      LEFT JOIN smc_class as c ON c.class_id = h.class_id
                                                      LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                      WHERE co.course_branch='{$branch[0]}' AND h.hour_lessontimes='{$branch[1]}'");
        $data['perfectOne'] = $PerfectOne;
        $data['perfect'] = $Perfect;
        $data['hour_name'] = $hour_name;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 课件 -- 上传个人视频
     */
    function UploadVideoApi($paramArray){
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['tempvideo_status'] = '0';
        $data['classcode_branch'] = $paramArray['classcode_branch'];
        $data['teacher_branch'] = $this->stafferOne['staffer_branch'];
        $data['teachhour_branch'] = $paramArray['teachhour_branch'];
        $data['tempvideo_name'] = $paramArray['tempvideo_name'];
        $data['tempvideo_author'] = $paramArray['tempvideo_author'];
        $data['tempvideo_coverimg'] = $paramArray['tempvideo_coverimg'];
        $data['tempvideo_videourl'] = $paramArray['tempvideo_videourl'];
        $data['tempvideo_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_teachhour_tempvideo",$data);

        $result = array();
        if($dataid){
            $res = array('error' => 0, 'errortip' => '上传成功', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '上传失败', 'result' => $result);
        }

        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 课件 -- 获取优质教学作品
     */
    function getExcellentPostilApi($paramArray)
    {
        $sql = "SELECT w.*,sf.staffer_cnname,sf.staffer_img,
                (SELECT p.prepare_postil FROM eas_prepare as p WHERE p.teacher_branch = w.teacher_branch AND p.tempworks_id = w.tempworks_id) as prepare_postil,
                (SELECT COUNT(wc.staffer_id) FROM eas_teachhour_watch as wc WHERE wc.tempworks_id = w.tempworks_id) as watch_num,
                (SELECT COUNT(p.staffer_id) FROM eas_teachhour_praise as p WHERE p.tempworks_id = w.tempworks_id) as praise_num
                FROM eas_teachhour_tempworks as w
                LEFT JOIN smc_staffer as sf ON sf.staffer_branch = w.teacher_branch
                WHERE w.tempworks_id = '{$paramArray['tempworks_id']}'";
        $tempworks = $this->DataControl->selectOne($sql);
        if($tempworks){
            $teachpics = $this->DataControl->selectClear("SELECT teachpics_id,teachpics_name,teachpics_url,teachpics_sort FROM eas_teachhour_teachpics WHERE tempworks_id = '{$tempworks['tempworks_id']}'");
            if($teachpics){
                $tempworks['img_list'] = $teachpics;
            }else{
                $tempworks['img_list'] = array();
            }
            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已通过', '-1' => '已驳回'));
            $tempworks['tempworks_status_name'] = $status[$tempworks['tempworks_status']];
            $tempworks['tempworks_createtime'] = date("Y-m-d H:i", $tempworks['tempworks_createtime']);
            if($this->DataControl->getOne("eas_teachhour_watch","tempworks_id='{$paramArray['tempworks_id']}' and staffer_id='{$this->staffer_id}'")){
                $tempworks['is_watch'] = true;
            }else{
                $tempworks['is_watch'] = false;
            }
            if($this->DataControl->getOne("eas_teachhour_praise","tempworks_id='{$paramArray['tempworks_id']}' and staffer_id='{$this->staffer_id}'")){
                $tempworks['is_praise'] = true;
            }else{
                $tempworks['is_praise'] = false;
            }
        }

        $data = array();
        $branch = explode("-", $paramArray['teachhour_branch']);
        $hour_name = $this->DataControl->selectOne("SELECT h.hour_name FROM smc_class_hour as h LEFT JOIN smc_course as c ON c.course_id = h.course_id WHERE c.course_branch = '{$branch[0]}' AND h.hour_lessontimes = '{$branch[1]}'");
        $data['list'] = $tempworks;
        $data['hour_name'] = $hour_name;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 课件 -- 个人收藏
     */
    function AddCollect($paramArray){
        if($this->DataControl->getOne("eas_teachhour_teachplan_collect","teachplan_id='{$paramArray['teachplan_id']}' and staffer_id='{$this->staffer_id}'")){
            $res = array('error' => 1, 'errortip' => '已收藏');
        }else{
            $data = array();
            $data['teachplan_id'] = $paramArray['teachplan_id'];
            $data['staffer_id'] = $this->staffer_id;
            $dataid = $this->DataControl->insertData("eas_teachhour_teachplan_collect",$data);
            if($dataid){
                $res = array('error' => 0, 'errortip' => '收藏信息创建成功');
            }else{
                $res = array('error' => 1, 'errortip' => '收藏信息创建失败');
            }
        }

        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 课件 -- 添加/取消收藏
     */
    function AddCancelCollect($paramArray){

        $data = array();
        $data['collect_status'] = $paramArray['collect_status'];
        $dataid = $this->DataControl->updateData("eas_teachhour_teachplan_collect","teachplan_id='{$paramArray['teachplan_id']}' and staffer_id='{$this->staffer_id}'",$data);
        if($paramArray['collect_status'] == '1'){
            $collect = '收藏';
        }else{
            $collect = '取消收藏';
        }

        $result = array();
        if($dataid){
            $res = array('error' => 0, 'errortip' => $collect.'成功', 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => $collect.'失败', 'result' => $result);
        }

        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 课件 -- 批注/视频 点赞/取消点赞
     */
    function AddCancelPraise($paramArray){
        if($paramArray['tempvideo_id']){
            $id = $paramArray['tempvideo_id'];
            $temp_id = 'tempvideo_id';
        }
        if($paramArray['tempworks_id']){
            $id = $paramArray['tempworks_id'];
            $temp_id = 'tempworks_id';
        }
        if($paramArray['code'] == '0'){
            $dataid = $this->DataControl->delData("eas_teachhour_praise","{$temp_id}={$id} and staffer_id='{$this->staffer_id}'");
        }else {
            $data = array();
            $data['tempvideo_id'] = $paramArray['tempvideo_id'];
            $data['tempworks_id'] = $paramArray['tempworks_id'];
            $data['staffer_id'] = $this->staffer_id;
            $dataid = $this->DataControl->insertData("eas_teachhour_praise", $data);
        }
        if($paramArray['code'] == '0'){
            $collect = '取消点赞';
        }else{
            $collect = '点赞';
        }

        if ($dataid) {
            $res = array('error' => 0, 'errortip' => $collect .'成功');
        } else {
            $res = array('error' => 1, 'errortip' => $collect .'失败');
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 课件 -- 观看视频
     */
    function AddCancelWatch($paramArray){
        $data = array();
        $data['tempvideo_id'] = $paramArray['tempvideo_id'];
        $data['tempworks_id'] = $paramArray['tempworks_id'];
        $data['staffer_id'] = $this->staffer_id;
        $dataid = $this->DataControl->insertData("eas_teachhour_watch", $data);

        if ($dataid) {
            $res = array('error' => 0, 'errortip' => '观看成功');
        } else {
            $res = array('error' => 1, 'errortip' => '观看失败');
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 个性化教案 -- 教案明细
     */
    function PersonLessonPlan($paramArray){
        $datawhere = "1";
        if(isset($paramArray['teachpics_sort']) && $paramArray['teachpics_sort'] !== ''){
            $datawhere = $paramArray['teachpics_sort'];
        }

        $teachplan = $this->DataControl->selectOne("SELECT t.teachplan_id,t.teachplan_name,t.teachplan_postil,t.teachplan_matters,t.teachplan_coverimg,
                                                    (SELECT p.prepare_postil FROM eas_prepare as p WHERE p.teacher_branch='{$this->stafferOne['staffer_branch']}' AND p.teachplan_id=t.teachplan_id) as prepare_postil,
                                                    (SELECT tt.teachpics_url FROM eas_teachhour_teachpics as tt WHERE tt.teachplan_id=t.teachplan_id AND tt.teachpics_sort={$datawhere}) as teachpics_url
                                                    FROM eas_teachhour_teachplan as t 
                                                    WHERE t.teachplan_id='{$paramArray['teachplan_id']}'");

        $data = $this->DataControl->getFieldOne("eas_teachhour_teachplan_collect","collect_status","staffer_id='{$this->staffer_id}' and teachplan_id='{$teachplan['teachplan_id']}'");
        if($data['collect_status'] == 1){
            $teachplan['is_collect'] = true;
        }else{
            $teachplan['is_collect'] = false;
        }

        $teachpics = $this->DataControl->selectClear("SELECT t.teachpics_id,t.teachpics_sort,pt.prepare_isemphasis,pt.prepare_isnokeep 
                                                      FROM eas_teachhour_teachpics as t
                                                      LEFT JOIN eas_prepare_teachpics as pt ON pt.teachpics_id=t.teachpics_id
                                                      WHERE t.teachplan_id='{$paramArray['teachplan_id']}'");
        if($teachpics){
            foreach($teachpics as &$v){
                if($v['prepare_isemphasis'] == '1'){
                    $v['prepare_isemphasis_name'] = '重难点';
                }else{
                    $v['prepare_isemphasis_name'] = '普通';
                }
                if($v['prepare_isnokeep'] == '1'){
                    $v['prepare_isnokeep_name'] = '不讲';
                }else{
                    $v['prepare_isnokeep_name'] = '需讲';
                }
            }
        }
        $data = array();
        $data['teachplan'] = $teachplan;
        $data['teachpics'] = $teachpics;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 个性化教案 -- 添加备课标记
     */
    function PrepareMarkApi($paramArray)
    {
        $data = array();
        $data['prepare_isemphasis'] = $paramArray['prepare_isemphasis'];
        $data['prepare_isnokeep'] = $paramArray['prepare_isnokeep'];
        if($paramArray['prepare_isemphasis'] == '1' || $paramArray['prepare_isnokeep'] == '1'){
            $collect = '标记';
        }else{
            $collect = '取消标记';
        }

        $result = array();
        if($this->DataControl->updateData("eas_prepare_teachpics","teachpics_id='{$paramArray['teachpics_id']}'",$data)){
            $res = array('error' => 0, 'errortip' => "{$collect}成功", 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => "{$collect}失败", 'result' => $result);
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 课程资料 -- 获取课程资料信息
     */
    function CourseMaterials($paramArray){
        $teachhour_branch = $paramArray['course_branch'].'-'.$paramArray['hour_lessontimes'];

        $word = $this->DataControl->selectClear("SELECT tempfiles_id,tempfiles_name,tempfiles_url,tempfiles_size FROM eas_teachhour_tempfiles WHERE teachhour_branch='{$teachhour_branch}' AND company_id='{$this->company_id}' AND tempfiles_class='0'");
        if($word){
            $wordnum = count($word);
            foreach($word as &$v){
                $v['Chakan'] = "https://idocv.kidcastle.com.cn/view/url?url=".$v['tempfiles_url'];
            }
        }else{
            $word = array();
            $wordnum = 0;
        }

        $mp3 = $this->DataControl->selectClear("SELECT tempfiles_id,tempfiles_name,tempfiles_url,tempfiles_size FROM eas_teachhour_tempfiles WHERE teachhour_branch='{$teachhour_branch}' AND company_id='{$this->company_id}' AND tempfiles_class='1'");
        if($mp3){
            $mp3num = count($mp3);
            foreach($mp3 as &$v){
                $v['Chakan'] = "https://idocv.kidcastle.com.cn/view/url?url=".$v['tempfiles_url'];
            }
        }else{
            $mp3 = array();
            $mp3num = 0;
        }

        $ppt = $this->DataControl->selectClear("SELECT tempfiles_id,tempfiles_name,tempfiles_url,tempfiles_size FROM eas_teachhour_tempfiles WHERE teachhour_branch='{$teachhour_branch}' AND company_id='{$this->company_id}' AND tempfiles_class='2'");
        if($ppt){
            $pptnum = count($ppt);
            foreach($ppt as &$v){
                $v['Chakan'] = "https://idocv.kidcastle.com.cn/view/url?url=".$v['tempfiles_url'];
            }
        }else{
            $ppt = array();
            $pptnum = 0;
        }

        $data = array();
        $data['wordnum'] = $wordnum;
        $data['mp3num'] = $mp3num;
        $data['pptnum'] = $pptnum;
        $data['word'] = $word;
        $data['mp3'] = $mp3;
        $data['ppt'] = $ppt;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 报表 -- 班级统计报表
     */
    function SchoolReport($paramArray)
    {
        $datawhere = "c.company_id = '{$this->company_id}' and c.school_id = '{$paramArray['school_id']}' and c.class_type = '0' and co.course_inclasstype = '0'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== '' && isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and ((c.class_stdate >= '{$paramArray['starttime']}' and c.class_stdate <= '{$paramArray['endtime']}') or (c.class_enddate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate <= '{$paramArray['starttime']}' and c.class_enddate >= '{$paramArray['endtime']}'))";
        }

        if(isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== ''){
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if(isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== ''){
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !== ''){
            $datawhere .= " and c.course_id = '{$paramArray['course_id']}'";
        }

        if ($paramArray['account_class'] == 0) {
            if ($this->stafferOne['postpart_isteregulator'] != '1') {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$this->staffer_id}')";
            }
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT
                    c.class_id,c.class_cnname,c.class_enname,c.class_branch,
                    (SELECT COUNT(th.teachhour_id)
                        FROM smc_course as co
                        LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id AND cc.classcode_branch = co.course_branch
                        LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch
                        WHERE co.course_id = c.course_id AND co.course_inclasstype = '0' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                    ) as class_hournums,
                    (SELECT COUNT(ch.hour_id) FROM smc_class_hour as ch WHERE ch.class_id = c.class_id AND ch.hour_ischecking = '1') as hour_num,
                    (SELECT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '0' LIMIT 1) as staffer_cnname,
                    (SELECT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '1' LIMIT 1) as fu_staffer_cnname
                FROM
                    smc_class as c
                LEFT JOIN
                    smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN
                    smc_course as co ON co.course_id = c.course_id
                LEFT JOIN
                    eas_classcode as cc ON cc.company_id = co.company_id AND cc.classcode_branch = co.course_branch
                LEFT JOIN
                    eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                WHERE
                    {$datawhere} AND c.class_status = '1' AND h.hour_iswarming = '0' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                GROUP BY
                    c.class_id
                LIMIT {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);
        if($dataList){
            foreach($dataList as &$v){
                $not_prepare_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as num
                                                                    FROM smc_class AS c
                                                                    LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                                    LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                                    LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                                    LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                                    LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                                    LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                                    WHERE c.class_id = '{$v['class_id']}' AND h.hour_iswarming = '0' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                                                                    AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                                    AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");
                $v['not_prepare_num'] = $not_prepare_num['num'];

                $prepare_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.teachhour_branch) as num
                                                                FROM smc_class AS c
                                                                LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                                LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                                LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                                LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                                LEFT JOIN eas_prepare as p ON p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch
                                                                LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                                WHERE c.class_id = '{$v['class_id']}' AND h.hour_iswarming = '0' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                                                                AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1')");
                $v['prepare_num'] = $prepare_num['num'];

                $delay_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as num
                                                              FROM smc_class AS c
                                                              LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                              LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                              LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                              LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                              LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                              LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                              WHERE c.class_id = '{$v['class_id']}' AND h.hour_iswarming = '0' AND h.hour_ischecking = '1' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                                                              AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch  AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                              AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");
                $v['delay_num'] = $delay_num['num'];
            }
        }

        $data = array();
        if(isset($paramArray['is_count']) && $paramArray['is_count'] !== ''){
            $all_nums = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.class_id) as num
                                                          FROM
                                                              smc_class as c
                                                          LEFT JOIN
                                                              smc_class_hour as h ON h.class_id = c.class_id
                                                          LEFT JOIN
                                                              smc_course as co ON co.course_id = c.course_id
                                                          LEFT JOIN
                                                              eas_classcode as cc ON cc.company_id = co.company_id AND cc.classcode_branch = co.course_branch
                                                          LEFT JOIN
                                                              eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
										                  WHERE {$datawhere} AND c.class_status = '1' AND h.hour_iswarming = '0' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'");
            if($all_nums){
                $data['allnums'] = $all_nums['num'];
            }else{
                $data['allnums'] = '0';
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $dataList;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 报表 -- 教师统计报表
     */
    function StafferReport($paramArray)
    {
        $datawhere = "c.company_id = '{$this->company_id}' and c.school_id = '{$paramArray['school_id']}' and (c.class_status='0' or c.class_status='1') and c.class_type = '0'";// and co.course_inclasstype = '0'
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sf.staffer_cnname like '%{$paramArray['keyword']}%' or sf.staffer_enname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '%{$paramArray['keyword']}%')";
        }
        $datewhere = "c.class_type = '0'";
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== '' && isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and ((c.class_stdate >= '{$paramArray['starttime']}' and c.class_stdate <= '{$paramArray['endtime']}') or (c.class_enddate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate <= '{$paramArray['starttime']}' and c.class_enddate >= '{$paramArray['endtime']}'))";
            $datewhere .= " and h.hour_day >= '{$paramArray['starttime']}' and h.hour_day <= '{$paramArray['endtime']}'";
        }

        if ($paramArray['account_class'] == 0) {
            if ($this->stafferOne['postpart_isteregulator'] != '1') {
                $datawhere .= " and sf.staffer_id = '{$this->staffer_id}'";
            }
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT
                    sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,sf.staffer_branch
                FROM
                    smc_class AS c
                LEFT JOIN
                    smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                LEFT JOIN
                    smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN
                    smc_staffer AS sf ON sf.staffer_id = ht.staffer_id
                WHERE
                    {$datawhere} AND sf.staffer_leave = '0'
                GROUP BY
                    sf.staffer_id
                LIMIT {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);
        if($dataList){
            foreach($dataList as &$v){
                $class_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.class_id ) as class_num
                                                              FROM smc_class_hour_teaching AS ht
                                                              LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                              LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                              LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                              LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                              WHERE (c.class_status = '0' or c.class_status = '1') AND ht.teaching_type = '0' AND ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND cc.classcode_isbeike = '1' AND {$datewhere}");
                $v['class_num'] = $class_num['class_num'];

                $hour_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as hour_num
                                                            FROM smc_class_hour_teaching AS ht
                                                            LEFT JOIN smc_class AS c ON c.class_id = ht.class_id
                                                            LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                            LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                            LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                            LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                            LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                            WHERE ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND {$datewhere}
                                                            AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                            AND (SELECT COUNT(p.prepare_id)
                                                            FROM
                                                                eas_prepare as p
                                                            LEFT JOIN
                                                                eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                            LEFT JOIN
                                                                eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                            WHERE
                                                                p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                             = (SELECT COUNT(tp.teachplan_id)
                                                            FROM
                                                                eas_teachhour_teachplan as tp
                                                            LEFT JOIN
                                                              eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                            LEFT JOIN
                                                                eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                            WHERE
                                                                tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");
                $v['hour_num'] = $hour_num['hour_num'];

                $prepare_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.teachhour_branch) as prepare_num
                                                                FROM smc_class_hour_teaching AS ht
                                                                LEFT JOIN smc_class AS c ON c.class_id = ht.class_id
                                                                LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                                LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                                LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                                LEFT JOIN eas_prepare as p ON p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch
                                                                LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes) AND {$datewhere}
                                                                WHERE ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                                                                AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1')");
                $v['prepare_num'] = $prepare_num['prepare_num'];

                $delay_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as delay_num
                                                              FROM smc_class_hour_teaching AS ht
                                                              LEFT JOIN smc_class AS c ON c.class_id = ht.class_id
                                                              LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                              LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                              LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                              LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                              LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                              WHERE ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND h.hour_ischecking = '1' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND {$datewhere}
                                                              AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch  AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                              AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");
                $v['delay_num'] = $delay_num['delay_num'];
            }
        }else{
            $dataList = array();
        }

        $data = array();
        if(isset($paramArray['is_count']) && $paramArray['is_count'] !== ''){
            $all_nums = $this->DataControl->selectOne("SELECT COUNT(q.staffer_id) as num FROM 
                                                         (SELECT sf.staffer_id
                                                         FROM smc_class AS c
                                                         LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id = c.class_id
                                                         LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                         LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id
                                                         WHERE {$datawhere} AND sf.staffer_leave='0' GROUP BY sf.staffer_id) as q");
            if($all_nums){
                $data['allnums'] = $all_nums['num'];
            }else{
                $data['allnums'] = '0';
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $dataList;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 报表 -- 班级备课详情
     */
    function ClassHourTotal($paramArray)
    {
        $datawhere = "c.company_id = '{$this->company_id}' and (c.class_status='0' or c.class_status='1') and c.class_type = '0' and sc.course_inclasstype = '0'";

        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== ''){
            $datawhere .= " and h.hour_day >= '{$paramArray['starttime']}'";
        }
        if(isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and h.hour_day <= '{$paramArray['endtime']}'";
        }
        $having = "1=1";
        if(isset($paramArray['prepare_status']) && $paramArray['prepare_status'] == '0'){
            $having .= " and (prepare_allnum = 0 or prepare_allnum <> prepare_num)";
        }elseif(isset($paramArray['prepare_status']) && $paramArray['prepare_status'] == '1'){
            $having .= " and prepare_allnum > 0 and prepare_allnum = prepare_num";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch,sc.course_cnname,sc.course_branch,h.hour_name,h.hour_ischecking,h.hour_day,h.hour_starttime,h.hour_endtime,
                (SELECT GROUP_CONCAT(DISTINCT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) )) FROM smc_class_hour_teaching as ht LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id WHERE ht.class_id = c.class_id AND ht.teaching_type = '0') as staffer_cnname,
                (SELECT GROUP_CONCAT(DISTINCT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) )) FROM smc_class_hour_teaching as ht LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id WHERE ht.class_id = c.class_id AND ht.teaching_type = '1') as fu_staffer_cnname,
                (SELECT GROUP_CONCAT(DISTINCT cr.classroom_cnname) FROM smc_classroom as cr LEFT JOIN smc_class_hour as h ON cr.classroom_id = h.classroom_id AND cr.classroom_status = '1' WHERE h.class_id = c.class_id) as classroom_cnname,
                (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0) as prepare_allnum,
                (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0 AND p.prepare_status = '1') as prepare_num
                FROM smc_class as c
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN eas_classcode as cc ON cc.company_id = sc.company_id and cc.classcode_branch = sc.course_branch
                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',h.hour_lessontimes)
                WHERE {$datawhere} AND c.class_id = '{$paramArray['class_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                GROUP BY h.hour_id
                HAVING {$having}
                LIMIT {$pagestart},{$num}";

        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$v){
                $v['hour_time'] = $v['hour_day'] .' '. $v['hour_starttime'] .'-'. $v['hour_endtime'];
                if($v['prepare_allnum'] && $v['prepare_allnum'] == $v['prepare_num']){
                    $v['prepare_status'] = '1';
                    $v['prepare_status_name'] = '已备课';
                }else{
                    $v['prepare_status'] = '0';
                    $v['prepare_status_name'] = '未备课';
                }
                if($v['hour_ischecking'] == '0'){
                    $v['hour_ischecking_name'] = '未上课';
                }elseif($v['hour_ischecking'] == '1'){
                    $v['hour_ischecking_name'] = '已上课';
                }
            }
        }else{
            $datalist = array();
        }
        $data = array();
        if(isset($paramArray['is_count']) && $paramArray['is_count'] !== ''){
            $all_nums = $this->DataControl->selectOne("SELECT COUNT(q.class_id) as num FROM 
                                                        (SELECT c.class_id,
                                                        (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0) as prepare_allnum,
                                                        (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0 AND p.prepare_status = '1') as prepare_num
                                                        FROM smc_class as c
                                                        LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                        LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                                                        LEFT JOIN eas_classcode as cc ON cc.company_id = sc.company_id and cc.classcode_branch = sc.course_branch
                                                        LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',h.hour_lessontimes)
                                                        WHERE {$datawhere} AND c.class_id = '{$paramArray['class_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                                                        GROUP BY h.hour_id HAVING {$having}) as q");
            if($all_nums){
                $data['allnums'] = $all_nums['num'];
            }else{
                $data['allnums'] = '0';
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        $sql = "SELECT
                    c.class_id,c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,
                    (SELECT group_concat(DISTINCT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '0') as staffer_cnname,
                    (SELECT group_concat(DISTINCT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '1') as fu_staffer_cnname
                FROM
                    smc_class as c
                LEFT JOIN
                    smc_course as co ON co.course_id = c.course_id
                WHERE
                    c.class_id = '{$paramArray['class_id']}'";
        $data['staffer_list'] = $this->DataControl->selectOne($sql);

        return $data;

    }


    /**
     * @param $paramArray
     * @return array
     * 报表 -- 班级统计报表导出
     */
    function SchoolExport($paramArray)
    {
        $datawhere = "c.company_id = '{$this->company_id}' and c.school_id = '{$paramArray['school_id']}' and c.class_type = '0' and co.course_inclasstype = '0'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== '' && isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and ((c.class_stdate >= '{$paramArray['starttime']}' and c.class_stdate <= '{$paramArray['endtime']}') or (c.class_enddate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate <= '{$paramArray['starttime']}' and c.class_enddate >= '{$paramArray['endtime']}'))";
        }

        if(isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== ''){
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if(isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== ''){
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !== ''){
            $datawhere .= " and c.course_id = '{$paramArray['course_id']}'";
        }

        if ($paramArray['account_class'] == 0) {
            if ($this->stafferOne['postpart_isteregulator'] != '1') {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_status = '0' AND tc.staffer_id = '{$this->staffer_id}')";
            }
        }

        $sql = "SELECT
                    c.class_id,c.class_cnname,c.class_enname,c.class_branch,
                    (SELECT COUNT(th.teachhour_id)
                        FROM smc_course as co
                        LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id AND cc.classcode_branch = co.course_branch
                        LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch
                        WHERE co.course_id = c.course_id AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                    ) as class_hournums,
                    (SELECT COUNT(ch.hour_id) FROM smc_class_hour as ch WHERE ch.class_id = c.class_id AND ch.hour_ischecking = '1') as hour_num,
                    (SELECT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '0' LIMIT 1) as staffer_cnname,
                    (SELECT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '1' LIMIT 1) as fu_staffer_cnname
                FROM
                    smc_class as c
                LEFT JOIN
                    smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN
                    smc_course as co ON co.course_id = c.course_id
                LEFT JOIN
                    eas_classcode as cc ON cc.company_id = co.company_id AND cc.classcode_branch = co.course_branch
                LEFT JOIN
                    eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                WHERE
                    {$datawhere} AND c.class_status = '1' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                GROUP BY
                    c.class_id";
        $dataList = $this->DataControl->selectClear($sql);

        $result = array();
        if($dataList){
            $outexceldate = array();
            foreach($dataList as $datavar){
                $not_prepare_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as num
                                                                    FROM smc_class AS c
                                                                    LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                                    LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                                    LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                                    LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                                    LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                                    LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                                    WHERE c.class_id = '{$datavar['class_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND h.hour_day >= CURDATE()
                                                                    AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                                    AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");

                $prepare_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.teachhour_branch) as num
                                                                FROM smc_class AS c
                                                                LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                                LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                                LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                                LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                                LEFT JOIN eas_prepare as p ON p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch
                                                                LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                                WHERE c.class_id = '{$datavar['class_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                                                                AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1')");

                $delay_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as num
                                                              FROM smc_class AS c
                                                              LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                              LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                              LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                              LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                              LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                              LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                              WHERE c.class_id = '{$datavar['class_id']}' AND h.hour_ischecking = '1' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                                                              AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch  AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                              AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");

                $datearray = array();

                $datearray['class_cnname'] = $datavar['class_cnname'];//班级名称

                $datearray['class_enname'] = $datavar['class_enname'];//班级别称

                $datearray['class_branch'] = $datavar['class_branch'];//班级编号

                $datearray['staffer_cnname'] = $datavar['staffer_cnname'];//主教老师

                $datearray['fu_staffer_cnname'] = $datavar['fu_staffer_cnname'];//助教老师

                $datearray['hour_num'] = $datavar['hour_num'];//已上课时数

                $datearray['class_hournums'] = $datavar['class_hournums'];//总备课时数

                $datearray['not_prepare_num'] = $not_prepare_num['num'];//待备课时数

                $datearray['prepare_num'] = $prepare_num['num'];//已备课时数

                $datearray['delay_num'] = $delay_num['num'];//延迟备课时数

                $outexceldate[] = $datearray;
            }

            $header = array("班级名称", "班级别称", "班级编号", "主教老师", "助教老师", "已上课时数", "总备课时数", "待备课时数", "已备课时数", "延迟备课时数");

            $getfileds = array("class_cnname","class_enname","class_branch","staffer_cnname","fu_staffer_cnname","hour_num","class_hournums","not_prepare_num","prepare_num","delay_num");

            $exportFile = "班级统计报表信息导出".date("Ymd").".xlsx";
            query_to_excel($header,$outexceldate,$getfileds,$exportFile);

            $res = array('error' => 0,'errortip' => "导出完毕!", 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 报表 -- 教师统计报表导出
     */
    function StafferExport($paramArray)
    {
        $datawhere = "c.company_id = '{$this->company_id}' and c.school_id = '{$paramArray['school_id']}' and (c.class_status='0' or c.class_status='1') and c.class_type = '0'";
        if(isset($paramArray['keyword']) && $paramArray['keyword'] !== ''){
            $datawhere .= " and (sf.staffer_cnname like '{$paramArray['keyword']}' or sf.staffer_enname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '{$paramArray['keyword']}')";
        }
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== ''){
            $datawhere .= " and h.hour_day >= '{$paramArray['starttime']}'";
        }
        if(isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and h.hour_day <= '{$paramArray['endtime']}'";
        }
        if ($paramArray['account_class'] == 0) {
            if ($this->stafferOne['postpart_isteregulator'] != '1') {
                $datawhere .= " and sf.staffer_id = '{$this->staffer_id}'";
            }
        }

        $sql = "SELECT
                    sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,sf.staffer_branch
                FROM
                    smc_class AS c
                LEFT JOIN
                    smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                LEFT JOIN
                    smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN
                    smc_staffer AS sf ON sf.staffer_id = ht.staffer_id
                WHERE
                    {$datawhere} AND sf.staffer_leave = '0'
                GROUP BY
                    sf.staffer_id";
        $dataList = $this->DataControl->selectClear($sql);
        if($dataList){
            foreach($dataList as &$v){
                $class_num = $this->DataControl->selectOne("SELECT COUNT( c.class_id ) as class_num
                                                              FROM smc_class_teach AS t
                                                              LEFT JOIN smc_class as c ON c.class_id = t.class_id
                                                              WHERE (c.class_status = '0' or c.class_status = '1') AND t.teach_status = '0' AND t.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}'");
                $v['class_num'] = $class_num['class_num'];

                $hour_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as hour_num
                                                            FROM smc_class_hour_teaching AS ht
                                                            LEFT JOIN smc_class AS c ON c.class_id = ht.class_id
                                                            LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                            LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                            LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                            LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                            LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                            WHERE ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND h.hour_day >= CURDATE()
                                                            AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                            AND (SELECT COUNT(p.prepare_id)
                                                            FROM
                                                                eas_prepare as p
                                                            LEFT JOIN
                                                                eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                            LEFT JOIN
                                                                eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                            WHERE
                                                                p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                             = (SELECT COUNT(tp.teachplan_id)
                                                            FROM
                                                                eas_teachhour_teachplan as tp
                                                            LEFT JOIN
                                                              eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                            LEFT JOIN
                                                                eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                            WHERE
                                                                tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");
                $v['hour_num'] = $hour_num['hour_num'];

                $prepare_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.teachhour_branch) as prepare_num
                                                                FROM smc_class_hour_teaching AS ht
                                                                LEFT JOIN smc_class AS c ON c.class_id = ht.class_id
                                                                LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                                LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                                LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                                LEFT JOIN eas_prepare as p ON p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch
                                                                LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                                WHERE ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                                                                AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1')");
                $v['prepare_num'] = $prepare_num['prepare_num'];

                $delay_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as delay_num
                                                              FROM smc_class_hour_teaching AS ht
                                                              LEFT JOIN smc_class AS c ON c.class_id = ht.class_id
                                                              LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                              LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                              LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                              LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                                                              LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                              WHERE ht.staffer_id = '{$v['staffer_id']}' AND c.school_id = '{$paramArray['school_id']}' AND h.hour_ischecking = '1' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                                                              AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch  AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                              AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");
                $v['delay_num'] = $delay_num['delay_num'];
            }
        }else{
            $dataList = array();
        }

        $result = array();
        if($dataList){
            $outexceldate = array();
            foreach($dataList as $datavar){
                $datearray = array();

                $datearray['staffer_cnname'] = $datavar['staffer_cnname'];//教师名称

                $datearray['staffer_enname'] = $datavar['staffer_enname'];//英文名称

                $datearray['staffer_branch'] = $datavar['staffer_branch'];//教师编号

                $datearray['class_num'] = $datavar['class_num'];//带课班级数

                $datearray['hour_num'] = $datavar['hour_num'];//待备课时数

                $datearray['prepare_num'] = $datavar['prepare_num'];//已备课时数

                $datearray['delay_num'] = $datavar['delay_num'];//延迟备课时数

                $outexceldate[] = $datearray;
            }

            $header = array("教师名称","英文名称","教师编号","需备课班级数","待备课时数","已备课时数","延迟备课时数");

            $getfileds = array("staffer_cnname","staffer_enname","staffer_branch","class_num","hour_num","prepare_num","delay_num");

            $exportFile = "教师统计报表信息导出".date("Ymd").".xlsx";
            query_to_excel($header,$outexceldate,$getfileds,$exportFile);

            $res = array('error' => 0,'errortip' => "导出完毕!", 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        return $res;
    }


    /**
     * @param $paramArray
     * @return array
     * 报表 -- 班级备课信息导出
     */
    function ClassHour($paramArray)
    {
        $datawhere = "c.company_id= '{$this->company_id}' and (c.class_status='0' or c.class_status='1')  and c.class_type = '0' and sc.course_inclasstype = '0'";

        $having = "1=1";
        if(isset($paramArray['prepare_status']) && $paramArray['prepare_status'] == '0'){
            $having .= " and (prepare_allnum = 0 or prepare_allnum <> prepare_num)";
        }elseif(isset($paramArray['prepare_status']) && $paramArray['prepare_status'] == '1'){
            $having .= " and prepare_allnum > 0 and prepare_allnum = prepare_num";
        }
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== ''){
            $datawhere .= " and h.hour_day >= '{$paramArray['starttime']}'";
        }
        if(isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and h.hour_day <= '{$paramArray['endtime']}'";
        }

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch,sc.course_cnname,sc.course_branch,h.hour_name,h.hour_ischecking,h.hour_day,h.hour_starttime,h.hour_endtime,
                (SELECT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '0') as staffer_cnname,
                (SELECT concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '1') as fu_staffer_cnname,
                (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0) as prepare_allnum,
                (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( sc.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = '{$this->stafferOne['staffer_branch']}' AND p.teachplan_id > 0 AND p.prepare_status = '1') as prepare_num
                FROM smc_class as c
                LEFT JOIN smc_class_hour as h ON h.hour_id = c.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN eas_classcode as cc ON cc.company_id = sc.company_id and cc.classcode_branch = sc.course_branch
                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(sc.course_branch,'-',h.hour_lessontimes)
                WHERE {$datawhere} AND c.class_id = '{$paramArray['class_id']}' AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'
                GROUP BY h.hour_id
                HAVING {$having}";
        $dataList = $this->DataControl->selectClear($sql);

        $result = array();
        if($dataList){
            $outexceldate = array();
            foreach($dataList as $datavar){
                $datearray = array();
                $datearray['class_id'] = $datavar['class_id'];//班级ID

                $datearray['class_cnname'] = $datavar['class_cnname'];//班级名称

                $datearray['staffer_cnname'] = $datavar['staffer_cnname'];//主教老师

                $datearray['fu_staffer_cnname'] = $datavar['fu_staffer_cnname'];//助教老师

                $datearray['hour_name'] = $datavar['hour_name'];//课时名称

                $datearray['hour_time'] = $datavar['hour_time'];//上课日期

                if($datavar['prepare_allnum'] && $datavar['prepare_allnum'] == $datavar['prepare_num']){//备课状态
                    $datavar['prepare_status_name'] = '已备课';
                }else{
                    $datavar['prepare_status_name'] = '未备课';
                }

                if($datavar['hour_ischecking'] == '0'){ //上课状态
                    $datearray['hour_ischecking_name'] = '未上课';
                }elseif($datavar['hour_ischecking'] == '1'){
                    $datearray['hour_ischecking_name'] = '已上课';
                }

                $datearray['prepare_postil'] = $datavar['prepare_postil'];//课时批注

                $outexceldate[] = $datearray;
            }

            $header = array("班级ID","班级名称","主教老师","助教老师","课时名称","上课日期","备课状态","上课状态");

            $getfileds = array("class_id","class_cnname","staffer_cnname","fu_staffer_cnname","hour_name","hour_time","prepare_status_name","hour_ischecking_name");

            $exportFile = "班级备课信息导出".date("Ymd").".xlsx";
            query_to_excel($header,$outexceldate,$getfileds,$exportFile);

            $res = array('error' => 0,'errortip' => "导出完毕!", 'result' => $result);
        }else{
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 在学课程
     */
    function InlearnCourse($paramArray){
        $datawhere = "l.company_id='{$this->company_id}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (ec.course_name like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['course_type']) && $paramArray['course_type'] !== ''){
            $datawhere .= " and ec.course_type='{$paramArray['course_type']}'";
        }

        //一定是教师自己的课表
        $datawhere .= " and l.teacher_branch ='{$this->stafferOne['staffer_branch']}'";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT ec.course_id,ec.course_name,ec.course_img,
                (SELECT COUNT(t.trainhour_id) FROM eas_course_chapter as cp LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id WHERE cp.course_id=ec.course_id) as trainhour_num,
                (
                    SELECT COUNT(el.learning_id) 
                    FROM eas_learning as el 
                    WHERE el.teacher_branch=l.teacher_branch AND el.course_id=ec.course_id AND el.learning_status='2'
                ) as com_num
                FROM eas_learning as l 
                LEFT JOIN eas_course as ec ON ec.course_id=l.course_id
                WHERE {$datawhere} GROUP BY ec.course_id LIMIT {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);
        if($dataList){
            foreach($dataList as &$v){
                $v['finish_ratio'] = $v['com_num'] .'/'. $v['trainhour_num'];
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_nums = $this->DataControl->selectOne("SELECT COUNT(DISTINCT ec.course_id) as num FROM eas_learning as l
                                                         LEFT JOIN eas_course as ec ON ec.course_id=l.course_id
                                                         WHERE {$datawhere}");
            if($all_nums){
                $data['allnums'] = $all_nums['num'];
            }else{
                $data['allnums'] = 0;
            }
        }
        $data['list'] = $dataList;

        return $data;
    }



    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 优秀批注
     */
    function getExcellentPostil($paramArray){
        $datawhere = "t.company_id = '{$this->company_id}'";
        if ($paramArray['code'] == '1') {
            $order = ",praise_num DESC";
        } elseif ($paramArray['code'] == '2') {
            $order = ",watch_num DESC";
        } elseif ($paramArray['code'] == '3') {
            $order = ",t.temppostil_examtime DESC";
        }

        //一定是教师自己的批注
        $datawhere .= " and t.teacher_branch ='{$this->stafferOne['staffer_branch']}'";

        if(isset($paramArray['keyword']) && $paramArray['keyword'] !== ''){
            $datawhere .= " and (t.temppostil_author like '%{$paramArray['keyword']}%')";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $Perfect_sql = "SELECT t.temppostil_id,t.teachhour_branch,t.temppostil_status,t.temppostil_tilte,t.temppostil_details,t.temppostil_author,f.staffer_cnname,f.staffer_img,
                        FROM_UNIXTIME(t.temppostil_createtime,'%Y-%m-%d %H:%i:%s') AS temppostil_createtime,
                        (
                          SELECT COUNT(w.staffer_id)
                          FROM eas_teachhour_watch as w
                          WHERE w.temppostil_id = t.temppostil_id
                        ) as watch_num,
                        (
                          SELECT COUNT(p.staffer_id)
                          FROM eas_teachhour_praise as p
                          WHERE p.temppostil_id = t.temppostil_id
                        ) as praise_num
                        FROM eas_teachhour_temppostil as t
                        LEFT JOIN smc_staffer as f ON f.staffer_branch = t.teacher_branch
                        WHERE {$datawhere} AND t.teachhour_branch='{$paramArray['teachhour_branch']}' AND t.temppostil_status = '1' AND t.temppostil_isPerfect='1'
                        ORDER BY t.temppostil_isPerfect DESC {$order} LIMIT {$pagestart},{$num} ";

        $Perfect = $this->DataControl->selectClear($Perfect_sql);
        if($Perfect){
            foreach($Perfect as &$v){
                if($this->DataControl->getOne("eas_teachhour_praise","temppostil_id='{$v['temppostil_id']}' and staffer_id='{$this->staffer_id}'")){
                    $v['is_zan'] = true;
                }else{
                    $v['is_zan'] = false;
                }
            }
        }else{
            $Perfect = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] !== '') {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(t.temppostil_id) as num
                                                        FROM eas_teachhour_temppostil as t
                                                        WHERE {$datawhere} AND t.teachhour_branch='{$paramArray['teachhour_branch']}' AND t.temppostil_status = '1' AND t.temppostil_isPerfect='1'");
            if ($all_num) {
                $data['allnum'] = $all_num['num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $Perfect;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 我的批注
     */
    function getPostil($paramArray){
        $datawhere = " cs.company_id='{$this->company_id}' and (cs.class_status = '0' OR cs.class_status = '1')";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%' or co.course_branch like '%{$paramArray['keyword']}%')";
        }

//        if ($this->stafferOne['account_class'] == 1) {
//            $str_school_id = $this->getStafferAllSchool();
//            $datawhere .= " and s.school_id in ({$str_school_id})";
//        } else {
//            $datawhere .= " and  sf.staffer_id ='{$this->staffer_id}'";
//        }

        $datawhere .= " and p.teacher_branch ='{$this->stafferOne['staffer_branch']}'";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT cs.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,co.course_cnname,s.school_cnname,cs.class_fullnums,co.course_id,co.course_branch,
                (SELECT COUNT(study_isreading) FROM smc_student_study as sy WHERE sy.class_id = cs.class_id AND sy.study_isreading = 1) as study_num
                FROM eas_prepare as p
                LEFT JOIN smc_class as cs ON cs.class_branch = p.class_branch 
                LEFT JOIN smc_course as co ON co.course_id = cs.course_id 
                LEFT JOIN smc_school as s ON s.school_id = cs.school_id
                LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id and cc.classcode_branch = co.course_branch
                WHERE {$datawhere} AND cc.classcode_isbeike = '1' AND p.prepare_postil <> ''
                GROUP BY cs.class_id
                Limit {$pagestart},{$num}";
        $classList = $this->DataControl->selectClear($sql);
        if($classList){
            foreach($classList as &$v){
                $v['class_fullnums'] = $v['study_num'] . '/' . $v['class_fullnums'];
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_nums = $this->DataControl->selectOne("SELECT COUNT(q.class_id) as num FROM
                                                        (SELECT cs.class_id
                                                        FROM eas_prepare as p
                                                        LEFT JOIN smc_class as cs ON cs.class_branch = p.class_branch
                                                        LEFT JOIN smc_course as co ON co.course_id = cs.course_id
                                                        LEFT JOIN smc_school as s ON s.school_id = cs.school_id
                                                        WHERE {$datawhere} AND p.prepare_postil IS NOT NULL GROUP BY cs.class_id) as q");
            if($all_nums){
                $data['allnums'] = $all_nums['num'];
            }else{
                $data['allnums'] = 0;
            }
        }
        $data['list'] = $classList;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 查看批注
     */
    function SeePostil($paramArray){
        $datawhere = "c.class_id='{$paramArray['class_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (ch.hour_name like '%{$paramArray['keyword']}%')";
        }

        $sql = "SELECT c.class_branch,co.course_cnname,co.course_branch,ch.hour_name,ch.hour_lessontimes
                FROM smc_class as c
                LEFT JOIN smc_class_hour as ch ON ch.class_id=c.class_id
                LEFT JOIN smc_course as co ON co.course_id=c.course_id
                LEFT JOIN eas_classcode as cc ON cc.company_id = co.company_id AND cc.classcode_branch = co.course_branch
                LEFT JOIN eas_teachhour as th ON th.company_id = cc.company_id AND th.classcode_branch = cc.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',ch.hour_lessontimes)
                WHERE {$datawhere} AND cc.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'";
        $hoursList = $this->DataControl->selectClear($sql);
        if($hoursList){
            $hourList = array();
            foreach($hoursList as &$val){
                $prepare = $this->DataControl->selectClear("SELECT t.teachplan_id,t.teachplan_name,p.prepare_id,p.prepare_postil
                                                              FROM eas_prepare as p
                                                              LEFT JOIN eas_teachhour_teachplan as t ON t.teachplan_id=p.teachplan_id
                                                              WHERE p.teacher_branch='{$this->stafferOne['staffer_branch']}' AND p.class_branch='{$val['class_branch']}'
                                                              AND p.teachhour_branch=CONCAT('{$val['course_branch']}','-','{$val['hour_lessontimes']}') AND t.teachplan_id > 0 AND p.prepare_postil <> ''");
                if($prepare){
                    $val['prepare'] = $prepare;
                    $hourList[] = $val;
                }
            }
        }else{
            $hoursList = array();
            $hourList = array();
        }

        $data = array();
        if($paramArray['code'] == '0'){
            $data['is_edit'] = false;
        }elseif($paramArray['code'] == '1'){
            $data['is_edit'] = true;
        }

        $course = $this->DataControl->getFieldOne("smc_course","course_cnname","course_id='{$paramArray['course_id']}'");

        $classinfo = array();
        $list = $this->DataControl->selectOne("SELECT c.class_cnname,ch.hour_day,ch.hour_starttime,ch.hour_endtime FROM smc_class as c LEFT JOIN smc_class_hour as ch ON ch.class_id=c.class_id WHERE c.class_id='{$paramArray['class_id']}' ORDER BY ch.hour_lessontimes ASC LIMIT 1");
        $classinfo['class_cnname'] = $list['class_cnname'];
        $classinfo['staffer_cnname'] = $this->stafferOne['staffer_cnname'];
        $classinfo['classtime'] = $list['hour_day'] .' '. $list['hour_starttime'] .'-'. $list['hour_endtime'];

        if($paramArray['code'] == '0'){
            $data['list'] = $hourList;
        }elseif($paramArray['code'] == '1'){
            $data['list'] = $hoursList;
        }
        $data['course_cnname'] = $course['course_cnname'];
        $data['class_info'] = $classinfo;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 查看单个批注
     */
    function SeeOnePostil($paramArray){
        $dataOne = $this->DataControl->getFieldOne("eas_prepare","prepare_id,prepare_postil","prepare_id='{$paramArray['prepare_id']}'");

        return $dataOne;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 查看笔记
     */
    function LookNote($paramArray){
        $datawhere = "l.teacher_branch='{$this->stafferOne['staffer_branch']}' AND co.stage_id='{$paramArray['stage_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (co.course_name like '%{$paramArray['keyword']}%')";
        }

        $sql = "SELECT l.learning_id,l.learning_note,co.course_id,co.course_name,cp.chapter_id,cp.chapter_name,t.trainhour_id,t.trainhour_name
                FROM eas_learning as l
                LEFT JOIN eas_course as co ON co.course_id=l.course_id
                LEFT JOIN eas_course_chapter as cp ON cp.course_id=co.course_id
                LEFT JOIN eas_course_trainhour as t ON t.chapter_id=cp.chapter_id
                WHERE {$datawhere} GROUP BY l.learning_id";
        $noteList = $this->DataControl->selectClear($sql);


        $data = array();
        if($paramArray['code'] == '0'){
            $data['is_edit'] = false;
        }elseif($paramArray['code'] == '1'){
            $data['is_edit'] = true;
        }

        $stage = $this->DataControl->getFieldOne("eas_career_stage","stage_cnname","stage_id='{$paramArray['stage_id']}'");

        $data['list'] = $noteList;
        $data['stage'] = $stage['stage_cnname'];

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 查看单个笔记
     */
    function LookOneNote($paramArray){
        $dataOne = $this->DataControl->getFieldOne("eas_learning","learning_id,learning_note","learning_id='{$paramArray['learning_id']}'");

        return $dataOne;
    }


    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 我的视频
     */
    function getVideo($paramArray){
        $datawhere = " w.company_id = '{$this->company_id}' and w.teacher_branch = '{$this->stafferOne['staffer_branch']}' ";

        if (isset($paramArray['tempworks_status']) && $paramArray['tempworks_status'] !== '') {
            $datawhere .= " and w.tempworks_status = '{$paramArray['tempworks_status']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    w.tempworks_id,w.tempworks_status,w.tempworks_videoname,w.tempworks_videourl,w.tempworks_rejectreason,FROM_UNIXTIME( w.tempworks_createtime, '%Y-%m-%d %H:%i' ) as tempworks_createtime
                FROM
                    eas_teachhour_tempworks as w
                WHERE
                    {$datawhere}
                Limit {$pagestart},{$num} ";
        $classList = $this->DataControl->selectClear($sql);
        if ($classList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已驳回"));
            foreach ($classList as &$val) {
                $val['tempworks_status_name'] = $status[$val['tempworks_status']];
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_nums = $this->DataControl->selectOne("SELECT COUNT(w.tempworks_id) as num FROM eas_teachhour_tempworks as w WHERE {$datawhere}");
            if($all_nums){
                $data['allnums'] = $all_nums['num'];
            }else{
                $data['allnums'] = 0;
            }
        }
        $data['list'] = $classList;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 个人中心 -- 我的收藏
     */
    function getCollection($paramArray){
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if($paramArray['code'] == 1){
            $datawhere = "cs.company_id='{$this->company_id}' and s.school_id='{$paramArray['school_id']}' and (cs.class_status = '0' OR cs.class_status = '1') and co.course_id > 0  and tc.staffer_id ='{$this->staffer_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
                $datawhere .= " and (ch.hour_name like '%{$paramArray['keyword']}%')";
            }

            $sql = "SELECT tc.teachplan_id,cs.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,co.course_id,co.course_cnname,s.school_cnname,co.course_branch,t.teachhour_branch,
	            (
                    SELECT group_concat(DISTINCT sf.staffer_cnname) 
                    FROM eas_teachhour_teachplan_collect AS cl
                    LEFT JOIN smc_staffer as sf ON sf.staffer_id=cl.staffer_id 
                    WHERE cl.teachplan_id=t.teachplan_id
                ) as staffer_cnname
                FROM eas_teachhour_teachplan_collect AS tc
                LEFT JOIN eas_teachhour_teachplan as t ON t.teachplan_id=tc.teachplan_id
                LEFT JOIN smc_course AS co ON co.course_branch = t.classcode_branch 
                LEFT JOIN smc_class AS cs ON cs.course_id = co.course_id
                LEFT JOIN smc_school AS s ON s.school_id = cs.school_id 
                WHERE {$datawhere} AND tc.collect_status='1'
                GROUP BY co.course_id
                Limit {$pagestart},{$num}    
                ";

            $data = array();
            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $all_nums = $this->DataControl->selectOne("SELECT COUNT(q.teachplan_id) as num FROM 
                                                        (SELECT tc.teachplan_id
                                                        FROM eas_teachhour_teachplan_collect AS tc
                                                        LEFT JOIN eas_teachhour_teachplan as t ON t.teachplan_id=tc.teachplan_id
                                                        LEFT JOIN smc_course AS co ON co.course_branch = t.classcode_branch 
                                                        LEFT JOIN smc_class AS cs ON cs.course_id = co.course_id
                                                        LEFT JOIN smc_school AS s ON s.school_id = cs.school_id 
                                                        WHERE {$datawhere} AND tc.collect_status='1'
                                                        GROUP BY co.course_id) as q");
                if($all_nums){
                    $data['allnums'] = $all_nums['num'];
                }else{
                    $data['allnums'] = 0;
                }
            }else{
                $data['allnums'] = 0;
            }
        }elseif($paramArray['code'] == 2){
            $datawhere = "co.company_id='{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
                $datawhere .= " and (co.course_name like '%{$paramArray['keyword']}%')";
            }

            $datawhere .= " and c.teacher_branch ='{$this->stafferOne['staffer_branch']}'";
            $sql = "SELECT co.course_id,co.course_name,co.course_img,
                    (SELECT COUNT(DISTINCT l.teacher_branch) FROM eas_learning as l WHERE l.course_id=co.course_id) as learn_num
                    FROM eas_course_collect AS c
                    LEFT JOIN eas_course AS co ON co.course_id = c.course_id 
                    WHERE {$datawhere} AND c.collect_status = '1'
                    Limit {$pagestart},{$num}";

            $data = array();
            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $all_nums = $this->DataControl->selectOne("SELECT COUNT(co.course_id) as num
                                                            FROM eas_course_collect AS c
                                                            LEFT JOIN eas_course AS co ON co.course_id = c.course_id 
                                                            WHERE {$datawhere} AND c.collect_status = '1'");
                if($all_nums){
                    $data['allnums'] = $all_nums['num'];
                }else{
                    $data['allnums'] = 0;
                }
            }else{
                $data['allnums'] = 0;
            }
        }

        $classList = $this->DataControl->selectClear($sql);
        if($classList){
            foreach($classList as &$v){
                if($v['teachhour_branch']){
                    $arr = explode('-',$v['teachhour_branch']);
                    $v['hour_lessontimes'] = $arr[1];
                }
            }
        }else{
            $classList = array();
        }

        $data['list'] = $classList;

        return $data;
    }
}