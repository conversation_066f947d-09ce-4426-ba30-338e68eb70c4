<?php

namespace Model\Easx;

class EducationModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            if (!$this->setPublic($publicarray)) {
                return false;
            }
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'], $publicarray['re_postbe_id'])) {
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }


    function verdictStaffer($staffer_id, $re_postbe_id = 0)
    {

        if ($re_postbe_id > 0) {
            $this->stafferOne = $this->DataControl->selectOne("SELECT
                                                                      s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_mobile,s.account_class,sp.postpart_isteregulator,sp.postpart_isregister
                                                                   FROM
                                                                      smc_staffer as s
                                                                   LEFT JOIN
                                                                      gmc_staffer_postbe as p ON p.staffer_id = s.staffer_id
                                                                   LEFT JOIN
                                                                      smc_school_postpart as sp ON sp.postpart_id = p.postpart_id
                                                                   WHERE
                                                                      s.company_id='{$this->company_id}' AND s.staffer_id = '{$staffer_id}' AND p.postbe_id = '{$re_postbe_id}'");
            if (!$this->stafferOne['postpart_isregister']) {
                $this->error = true;
                $this->errortip = "无小循环登记访问权限，请联系管理员开启";
                return false;
            }
        } else {
            $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        }

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }


    /**
     *  获取教师所带的班级
     * author: ling
     * 对应接口文档 0001
     */
    function getStaClassList($paramArray)
    {
        $datawhere = "cs.company_id='{$this->company_id}' and cs.school_id = '{$paramArray['school_id']}' and cs.class_status <> '-2' and cs.class_type = '0' and cl.classcode_isregister = 1 and ct.teach_status = '0'";// and ct.teach_type = 0

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_enname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%' or co.course_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= "  and ch.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== '') {
            $datawhere .= "  and cs.class_status = '{$paramArray['class_status']}'";
        }
        if (isset($paramArray['re_school_id']) && $paramArray['re_school_id'] !== '') {
            $datawhere .= "  and cs.school_id = '{$paramArray['re_school_id']}'";
        }
        $hvaing = '1=1';
        if (isset($paramArray['classroom_id']) && $paramArray['classroom_id'] !== '') {
            $hvaing .= " and classroom_cnname is not NULL ";
            $datawhere .= "  and ch.classroom_id = '{$paramArray['classroom_id']}'";
        }

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and ct.staffer_id = '{$paramArray['staffer_id']}' ";
            } else {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= "  and ct.staffer_id = '{$paramArray['re_staffer_id']}'";
                } else {
                    $datawhere .= "  and ct.staffer_id = '{$paramArray['staffer_id']}'";
                }
            }
        } else {
            if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                $datawhere .= "  and ct.staffer_id = '{$paramArray['re_staffer_id']}'";
            } else {
                $datawhere .= " and cs.class_id = '0'";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $today = date("Y-m-d");

        $sql = "select ct.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,cs.class_type,co.course_cnname,s.school_shortname as school_cnname,class_fullnums,co.course_branch,
        
            (select group_concat( DISTINCT s.staffer_cnname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_cnname,
            (select group_concat( DISTINCT s.staffer_enname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_enname,
            (select count(study_isreading) from smc_student_study as sy where sy.class_id = ht.class_id and sy.study_isreading = 1) as study_num,
            (select group_concat( DISTINCT cm.classroom_cnname) from smc_classroom as cm,smc_class_lessonplan AS l where cm.classroom_id = l.classroom_id AND l.class_id = ht.class_id ) as classroom_cnname,
            (select count(ch.hour_id)
                from smc_class_hour as ch
                left join smc_class as c ON c.class_id = ch.class_id
                left join smc_course as co ON co.course_id = c.course_id
                left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id=co.company_id
                left join eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',ch.hour_lessontimes)
                where ch.class_id = cs.class_id and ch.hour_iswarming = 0 and ch.hour_ischecking = 1 and ch.hour_isfree = 0 and cl.classcode_isregister = 1 and th.teachhour_isregister = 1 and ch.hour_day <= '{$today}' 
            ) as hour_checkingnum,
            (select count(sch.hour_id)
                from smc_class_hour as sch
                left join smc_class as c ON c.class_id = sch.class_id
                left join smc_course as co ON co.course_id = c.course_id
                left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id=co.company_id
                left join eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',sch.hour_lessontimes)
                where sch.class_id = cs.class_id and sch.hour_iswarming = 0 and sch.hour_ischecking <> -1 and cl.classcode_isregister = 1 and th.teachhour_isregister = 1
            ) as hour_allnum,
            (select count( DISTINCT  sh.hour_branch) 
                from eas_student_hour as sh 
                left join smc_class AS c ON c.class_id = sh.class_id
                left join smc_course AS co ON co.course_id = c.course_id
                left join eas_classcode AS cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                left join eas_teachhour AS th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = REPLACE(sh.hour_branch, '_', '-')
                where sh.class_id = cs.class_id and cl.classcode_isregister = 1 and th.teachhour_isregister = 1
            ) as  hour_branch_num
            from smc_class_teach as ct 
	        left join smc_class_hour_teaching AS ht on ct.class_id = ht.class_id
            left join smc_class_hour as ch ON ch.hour_id = ht.hour_id and ht.class_id = ch.class_id
            left join smc_class as cs ON cs.class_id = ch.class_id 
            left join smc_course as co ON co.course_id = cs.course_id 
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id=co.company_id
            left join smc_school as s ON s.school_id = cs.school_id
            left join smc_staffer as sf ON  sf.staffer_id = ct.staffer_id
            where {$datawhere}
            group by ct.class_id 
            HAVING {$hvaing} 
            order by co.course_branch DESC
            Limit {$pagestart},{$num}";

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $allnum = $this->DataControl->selectClear("select ct.class_id,
                    (select group_concat( DISTINCT cm.classroom_cnname) from smc_classroom as cm,smc_class_lessonplan AS l where cm.classroom_id = l.classroom_id AND l.class_id = ct.class_id ) as classroom_cnname
                    from smc_class_teach as ct 
	                left join smc_class_hour_teaching AS ht on ct.class_id = ht.class_id
                    left join smc_class_hour as ch ON ch.hour_id = ht.hour_id and ht.class_id = ch.class_id
                    left join smc_class as cs ON cs.class_id = ch.class_id 
                    left join smc_course as co ON co.course_id = cs.course_id
                    left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id=co.company_id 
                    left join smc_school as s ON s.school_id = cs.school_id
                    where {$datawhere}
                    group by ct.class_id
                     HAVING {$hvaing}
                  ");

            if ($allnum) {
                $data['allnum'] = count($allnum);
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $classList = array();
        } else {
            $arr_status = array('0' => '待开班', '1' => '进行中', '-1' => '已结束');
            foreach ($classList as $key => $value) {
                $classList[$key]['class_status_name'] = $arr_status[$value['class_status']];
                $classList[$key]['status'] = false;
                $classList[$key]['student_nums'] = $value['study_num'] . '/' . $value['class_fullnums'];
                $classList[$key]['class_hour_num'] = $value['hour_checkingnum'] . '/' . $value['hour_allnum'];
                $classList[$key]['hour_diffnum'] = ($value['hour_checkingnum'] - $value['hour_branch_num']) < 0 ? 0 : ($value['hour_checkingnum'] - $value['hour_branch_num']);
                $hourlist = $this->DataControl->selectClear("SELECT h.hour_day, CONCAT(o.course_branch,'_',h.hour_lessontimes) as hour_branch FROM smc_class_hour AS h,smc_class AS c,smc_course AS o WHERE c.class_id = h.class_id AND c.course_id = o.course_id and h.hour_iswarming = '0' and h.hour_ischecking = '1' and c.class_id = '{$value['class_id']}'");
                $num = 0;
                if ($hourlist) {
                    foreach ($hourlist as $val) {
                        $notregister_num = $this->DataControl->selectOne("SELECT
                                                                                COUNT(hour_testscore) as num
                                                                            FROM
                                                                                smc_student_study AS sd
                                                                            LEFT JOIN
                                                                                eas_student_hour AS sh ON sh.student_id = sd.student_id AND sh.class_id = sd.class_id AND sh.hour_branch = '{$val['hour_branch']}'
                                                                            WHERE
                                                                                sd.study_beginday <= '{$val['hour_day']}' AND ( sd.study_isreading = '1' OR sd.study_endday > '{$val['hour_day']}' ) AND sd.class_id = '{$value['class_id']}'");
                        if (!$notregister_num['num']) {
                            $num += 1;
                        }
                    }
                }
                $classList[$key]['notregister_num'] = $num;
            }
        }

        $data['list'] = $classList;
        return $data;
    }

    /**
     * 填写分数
     * author: ling
     * 对应接口文档 0001
     */
    function fillStuWorkScoreAction($request)
    {

        if (!intval($request['class_id'])) {
            $this->error = '1';
            $this->errortip = '请选择班级!';
            $this->backData = array();
            return false;
        }
        if (!intval($request['student_id'])) {
            $this->error = '1';
            $this->errortip = '请选择学员!';
            $this->backData = array();
            return false;
        }
        if (!intval($request['hour_id'])) {
            $this->error = '1';
            $this->errortip = '请选择课时!';
            $this->backData = array();
            return false;
        } else {
            $teachHour = $this->DataControl->selectOne("select  ch.hour_id,ch.hour_lessontimes,hour_name,ce.course_branch from smc_class_hour as ch,smc_course as ce where  ch.course_id = ce.course_id and ch.hour_id ='{$request['hour_id']}'  limit 0,1");
            if (!$teachHour) {
                $this->error = '1';
                $this->errortip = '没有可用的课时!';
                $this->backData = array();
                return false;
            }
        }
        if (!$this->DataControl->getFieldOne("smc_class_hour", "hour_ischecking", "hour_id = '{$request['hour_id']}' and hour_ischecking = '1'")) {
            $this->error = '1';
            $this->errortip = '未考勤课时暂不可登记~';
            $this->backData = array();
            return false;
        }

        $hour_branch = $teachHour['course_branch'] . '_' . $teachHour['hour_lessontimes'];
        $hourOne = $this->DataControl->getFieldOne("eas_student_hour", "student_id", "student_id ='{$request['student_id']}' and class_id='{$request['class_id']}'and hour_branch = '{$hour_branch}'");

        if (!$hourOne) {
            $data = array();
            $data['student_id'] = $request['student_id'];
            $data['class_id'] = $request['class_id'];
            $data['hour_branch'] = $teachHour['course_branch'] . '_' . $teachHour['hour_lessontimes'];
            $data['hour_name'] = $teachHour['hour_name'];
            if (isset($request['hour_testscore']) && $request['hour_testscore'] != "") {
                $data['hour_testscore'] = $request['hour_testscore'];
            }
            if (isset($request['hour_homeworkscores']) && $request['hour_homeworkscores'] != "") {
                $data['hour_homeworkscores'] = $request['hour_homeworkscores'];
            }
            //线下作业
            if (isset($request['hour_homework']) && $request['hour_homework'] != "") {
                $data['hour_homework'] = $request['hour_homework'];
            }
            if (isset($request['hour_netstatus']) && $request['hour_netstatus'] !== "") {
                $data['hour_netstatus'] = $request['hour_netstatus'];
            }
            if (isset($request['hour_netscores']) && $request['hour_netscores'] != "") {
                $data['hour_netscores'] = $request['hour_netscores'];
            }
            if (isset($request['hour_refresher']) && $request['hour_refresher'] !== "") {
                $data['hour_refresher'] = $request['hour_refresher'];
            }
            if (isset($request['hour_bookcheck']) && $request['hour_bookcheck'] !== "") {
                $data['hour_bookcheck'] = $request['hour_bookcheck'];
            }
            if (isset($request['hour_resitscore']) && $request['hour_resitscore'] !== "") {
                $data['hour_resitscore'] = $request['hour_resitscore'];
            }


            $data['hour_createtime'] = time();
            $data['hour_updatatime'] = time();
            $this->DataControl->insertData('eas_student_hour', $data);
            $this->error = '0';
            $this->errortip = '操作成功';
            $this->backData = $data;
            return true;

        } else {
            $data = array();
            $data['hour_updatatime'] = time();
            if (isset($request['hour_testscore']) && $request['hour_testscore'] != "") {
                $data['hour_testscore'] = $request['hour_testscore'];
            }
            if (isset($request['hour_homeworkscores']) && $request['hour_homeworkscores'] != "") {
                $data['hour_homeworkscores'] = $request['hour_homeworkscores'];
            }
            //线下作业
            if (isset($request['hour_homework']) && $request['hour_homework'] != "") {
                $data['hour_homework'] = $request['hour_homework'];
            }
            if (isset($request['hour_netstatus']) && $request['hour_netstatus'] !== "") {
                $data['hour_netstatus'] = $request['hour_netstatus'];
            }
            if (isset($request['hour_netscores']) && $request['hour_netscores'] != "") {
                $data['hour_netscores'] = $request['hour_netscores'];
            }
            if (isset($request['hour_refresher']) && $request['hour_refresher'] !== "") {
                $data['hour_refresher'] = $request['hour_refresher'];
            }
            if (isset($request['hour_bookcheck']) && $request['hour_bookcheck'] !== "") {
                $data['hour_bookcheck'] = $request['hour_bookcheck'];
            }
            if (isset($request['hour_resitscore']) && $request['hour_resitscore'] !== "") {
                $data['hour_resitscore'] = $request['hour_resitscore'];
            }

            if ($this->DataControl->updateData('eas_student_hour', "student_id ='{$request['student_id']}' and class_id='{$request['class_id']}'and hour_branch = '{$hour_branch}'", $data)) {
                $this->error = '0';
                $this->errortip = '操作成功';
                $this->backData = $data;
                return true;
            } else {
                $this->error = '1';
                $this->errortip = '更新失败';
                $this->backData = array();
                return false;
            }
        }
    }

    /**
     *  一键已参与  网课一键参与  补课一键无补课  小书检核一键已完成  线下作业一键已完成
     * author: ling
     * 对应接口文档 0001
     */
    function fillClassHourNetstatus($request)
    {
        if ($request['hour_netstatus'] <> 1 && $request['hour_refresher'] <> 0 && $request['hour_bookcheck'] !== '' && $request['hour_homework'] <> 1) {
            $this->error = 1;
            $this->errortip = '请选择需要登记项目';
            return false;
        }
        $datawhere = '1';
        $hourOne = $this->DataControl->selectOne("SELECT h.hour_day, h.hour_name , CONCAT( o.course_branch,'_',h.hour_lessontimes) as hour_branch FROM smc_class_hour AS h, smc_class AS c, smc_course AS o WHERE c.class_id = h.class_id AND c.course_id = o.course_id and c.class_id = '{$request['class_id']}'and h.hour_id='{$request['hour_id']}'");
        if (!$hourOne) {
            $this->error = 1;
            $this->errortip = '请选择课时';
            return false;
        } else {
            $datawhere .= " and  sd.study_beginday <= '{$hourOne['hour_day']}'  and (sd.study_isreading = '1' OR sd.study_endday > '{$hourOne['hour_day']}')  ";
        }
        if (!$this->DataControl->getFieldOne("smc_class_hour", "hour_ischecking", "hour_id = '{$request['hour_id']}' and hour_ischecking = '1'")) {
            $this->error = '1';
            $this->errortip = '未考勤课时暂不可登记~';
            $this->backData = array();
            return false;
        }
        $sql = "select s.student_id
            from smc_student_study AS sd
            left join smc_student as s ON s.student_id= sd.student_id
            where {$datawhere} and sd.class_id ='{$request['class_id']}' ";
        $dataList = $this->DataControl->selectClear($sql);
        $this->DataControl->begintransaction();
        if ($dataList) {
            $request_data = $request;
            foreach ($dataList as &$dataOne) {
                $request_data['student_id'] = $dataOne['student_id'];
                $bool = $this->fillStuWorkScoreAction($request_data);
                if (!$bool) {
                    $this->error = 1;
                    $this->errortip = '设置失败';
                    $this->DataControl->rollback();
                    return false;
                }
            }
            $this->error = 0;
            $this->errortip = '设置成功';
            $this->DataControl->commit();
            return true;
        } else {
            $this->error = 1;
            $this->errortip = '不存在参与学员';
            $this->DataControl->rollback();
            return false;
        }
    }


    /**
     * 获取班级的课时
     * author: ling
     * 对应接口文档 0001
     */
    function getClassHourListApi($request)
    {
        $today = date("Y-m-d");
        $sql = "SELECT th.hour_id,th.hour_name,th.hour_day,hour_ischecking,c.class_cnname,c.class_enname,
	            (select count(hs.hour_id) from smc_student_hourstudy as hs where hs.hour_id = th.hour_id and hs.hourstudy_checkin = '1') as checknum,
	            (select count(sh.student_id) from eas_student_hour as sh where sh.hour_name = th.hour_name and sh.class_id = th.class_id) as branchnum
                FROM smc_class_hour as th
                LEFT JOIN smc_class as c On th.class_id = c.class_id
                LEFT JOIN smc_course as  s On s.course_id = c.course_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch =s.course_branch and cl.company_id =s.company_id
                LEFT JOIN eas_teachhour as tc ON  tc.classcode_branch = s.course_branch  and  tc.company_id=s.company_id and tc.teachhour_branch =  concat(s.course_branch,'-',th.hour_lessontimes)
                WHERE  c.class_id = '{$request['class_id']}' AND th.hour_iswarming = 0 AND  th.hour_ischecking = 1 and th.hour_day <='{$today}' AND cl.classcode_isregister =1
                AND tc.teachhour_isregister = 1 AND th.hour_iswarming = 0 AND th.hour_isfree = 0
                ORDER BY th.hour_lessontimes DESC";

        $before_hourList = $this->DataControl->selectClear($sql);
        if (!$before_hourList) {
            $before_hourList = array();
        }
        $sql = "SELECT th.hour_id,th.hour_name,th.hour_day,hour_ischecking,c.class_cnname,c.class_enname,
                (select count(hs.hour_id) from smc_student_hourstudy as hs where hs.hour_id = th.hour_id and hs.hourstudy_checkin = '1') as checknum,
                (select count(sh.student_id) from eas_student_hour as sh where sh.hour_name = th.hour_name and sh.class_id = th.class_id) as branchnum
                FROM smc_class_hour as th
                LEFT JOIN smc_class as c On th.class_id = c.class_id
                LEFT JOIN smc_course as  s On s.course_id = c.course_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch =s.course_branch and cl.company_id =s.company_id
                LEFT JOIN  eas_teachhour as tc ON  tc.classcode_branch = s.course_branch  and  tc.company_id=s.company_id and tc.teachhour_branch =  concat(s.course_branch,'-',th.hour_lessontimes)
                WHERE  c.class_id = '{$request['class_id']}' AND th.hour_iswarming = 0 AND  th.hour_ischecking = 0 AND cl.classcode_isregister =1
                AND tc.teachhour_isregister = 1 AND th.hour_iswarming = 0 AND th.hour_isfree = 0
                ORDER BY th.hour_lessontimes ASC";

        $after_hourList = $this->DataControl->selectClear($sql);
        if (!$after_hourList) {
            $after_hourList = array();
        }
        $hourList = array_merge($before_hourList, $after_hourList);

        if ($hourList) {
            foreach ($hourList as $key => $value) {
                $hourList[$key]['class_cnname'] = $value['class_cnname'] . " ({$value['class_enname']})";
                $hourList[$key]['hour_name'] = $value['hour_name'] . " ({$value['hour_day']})";
                $hourList[$key]['hour_ischecking_name'] = $value['hour_ischecking'] == 0 ? '未开始' : '已结束';
                $hourList[$key]['active'] = false;
                if ($value['checknum'] <= $value['branchnum']) {
                    $hourList[$key]['register'] = '1';
                } else {
                    $hourList[$key]['register'] = '0';
                }
            }
        } else {
            $hourList = array();
        }

        $data['list'] = $hourList;
        return $data;
    }


    /**
     * 获取班级的在读人员
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function getStudyStudentListApi($request)
    {
        $hourOne = $this->DataControl->selectOne("SELECT h.hour_day, h.hour_name , CONCAT( o.course_branch,'_',h.hour_lessontimes) as hour_branch FROM smc_class_hour AS h, smc_class AS c, smc_course AS o WHERE c.class_id = h.class_id AND c.course_id = o.course_id and c.class_id = '{$request['class_id']}'and h.hour_id='{$request['hour_id']}'");

        $datawhere = '1 and s.student_id > 0';
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%')";
        }

        if (isset($request['hour_netstatus']) && $request['hour_netstatus'] != '') {
            $datawhere .= " and sh.hour_netstatus = '{$request['hour_netstatus']}'";
        }

        if (isset($request['hour_refresher']) && $request['hour_refresher'] != '') {
            $datawhere .= " and sh.hour_refresher = '{$request['hour_refresher']}'";
        }

        if (isset($request['teachhour_scoretype']) && $request['teachhour_scoretype'] != '') {
            $datawhere .= " and tc.teachhour_scoretype = '{$request['teachhour_scoretype']}'";
        }

        if (isset($request['teachhour_onscoretype']) && $request['teachhour_onscoretype'] != '') {
            $datawhere .= " and tc.teachhour_onscoretype = '{$request['teachhour_onscoretype']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '30';
        }
        $pagestart = ($page - 1) * $num;


        $hour_day = date("Y-m-d");

        if ($hourOne) {
            $datawhere .= " and  hs.hour_id='{$request['hour_id']}'  ";
        } else {
            $datawhere .= " and  sd.study_beginday <= '{$hour_day}'  and (sd.study_isreading = '1' OR sd.study_endday > '{$hour_day}')  ";
        }

        $sql = "select s.student_id,s.student_enname,s.student_cnname,s.student_img,s.student_sex,s.student_branch,student_birthday,hour_testscore,hour_homework,hour_homeworkscores,
            hour_resitscore,hour_netstatus,hour_netscores,hour_refresher,hour_bookcheck,sd.study_beginday,hs.hourstudy_checkin,tc.teachhour_scoretype,tc.teachhour_onscoretype,
            (select count(track_id) from smc_student_track as ca where ca.student_id = sd.student_id and ca.school_id=sd.school_id) as track_num,
            (select track_day from smc_student_track as ca where ca.student_id = hs.student_id order by track_day  DESC limit 0,1 ) as track_day
            from smc_student_hourstudy as hs
            left join smc_student_study as sd ON sd.student_id = hs.student_id and sd.class_id = hs.class_id
            left join smc_student as s ON s.student_id = hs.student_id
            left join eas_student_hour as sh ON sh.student_id = hs.student_id and sh.class_id = hs.class_id and sh.hour_branch = '{$hourOne['hour_branch']}'
            left join smc_class as c ON c.class_id = hs.class_id
            left join smc_course as co ON co.course_id = c.course_id
            left join eas_teachhour as tc ON tc.company_id = co.company_id and tc.classcode_branch = co.course_branch and tc.teachhour_branch =  REPLACE('{$hourOne['hour_branch']}', '_', '-')
            where {$datawhere} and hs.class_id ='{$request['class_id']}'
            group by s.student_id
            limit {$pagestart},{$num}
            ";


        $allnum = $this->DataControl->selectOne("
                select  count(distinct s.student_id) as all_num
                from smc_student_hourstudy as hs
                left join smc_student_study as sd ON sd.student_id = hs.student_id and sd.class_id = hs.class_id
                left join smc_student as s ON s.student_id = hs.student_id
                left join eas_student_hour as sh ON sh.student_id = hs.student_id and sh.class_id = hs.class_id and sh.hour_branch = '{$hourOne['hour_branch']}'
                left join smc_class as c ON c.class_id = hs.class_id
                left join smc_course as co ON co.course_id = c.course_id
                left join eas_teachhour as tc ON tc.company_id = co.company_id and tc.classcode_branch = co.course_branch and tc.teachhour_branch =  REPLACE('{$hourOne['hour_branch']}', '_', '-')
                where {$datawhere} and hs.class_id ='{$request['class_id']}'
                ");


        if ($allnum) {
            $data['allnum'] = $allnum['all_num'];
        } else {
            $data['allnum'] = 0;
        }

        $studyList = $this->DataControl->selectClear($sql);
        $arr_refresher = array('0' => '无补课', '1' => '已补课', '-1' => '未补课');
        $arr_bookcheck = array('0' => '未完成', '1' => '已完成', '' => '未完成');
        $arr_netstatus = array('1' => '已参与', '-1' => '未参与');
        $arr_homework = array('0' => '请选择线下作业状态', '1' => '已完成', '-1' => '未完成');
        $arr_check = array('0' => '缺勤', '1' => '出勤');
        if (!$studyList) {
            $studyList = array();
        } else {
            foreach ($studyList as $key => $value) {
                if ($value['hourstudy_checkin']) {
                    $value['hour_refresher'] = 0;
                }
                $studyList[$key]['hour_refresher_name'] = $arr_refresher[$value['hour_refresher']];
                $studyList[$key]['hour_bookcheck_name'] = $arr_bookcheck[$value['hour_bookcheck']];
                $studyList[$key]['hour_netstatus_name'] = $arr_netstatus[$value['hour_netstatus']];
                $studyList[$key]['hour_homework_name'] = $arr_homework[$value['hour_homework']];
                $studyList[$key]['hour_check_name'] = $arr_check[$value['hourstudy_checkin']];
                if ($value['hour_testscore'] == '' || $value['hour_testscore'] >= '80') {
                    $studyList[$key]['hour_resitscore'] = '--';
                }
            }
        }

        $data['list'] = $studyList;

        return $data;
    }


    /**
     * 获取班级的在读人员 -- 电访管理
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function getStudentCatitrackListApi($request)
    {
        if ($request['hourList']) {
            $arr = array();
            $hour_list = json_decode(stripslashes($request['hourList']), true);
            foreach ($hour_list as $v) {
                $arr[] = $v['hour_id'];
            }
            $hourId = implode(",", $arr);
        } else {
            $hourId = 0;
        }

        $hourList = $this->DataControl->selectClear("SELECT h.hour_day, h.hour_name , CONCAT( o.course_branch,'_',h.hour_lessontimes) as hour_branch FROM smc_class_hour AS h, smc_class AS c, smc_course AS o WHERE c.class_id = h.class_id AND c.course_id = o.course_id and c.class_id = '{$request['class_id']}'and h.hour_id IN ({$hourId})");

        $datawhere = " sd.class_id = '{$request['class_id']}' and sd.study_isreading = 1 and s.student_id > 0";
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%')";
        }

        $hvaing = "1=1";
        if (isset($request['catitrack_class']) && $request['catitrack_class'] == '0') {
            $hvaing .= " and track_nums = '0'";
        } elseif (isset($request['catitrack_class']) && $request['catitrack_class'] == '1') {
            $hvaing .= " and track_nums <> '0'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '30';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['study_day']) && $request['study_day'] !== "") {
            $hour_day = GetTheMonth($request['study_day']);
        } else {
            $hour_day = GetTheMonth(date("Y-m-d"));
        }

        if ($hourList) {
            $hourfirst = current($hourList);
            $hourend = end($hourList);
            $datawhere .= " and  sd.study_beginday <= '{$hourfirst['hour_day']}'  and (sd.study_isreading = '1' OR sd.study_endday > '{$hourend['hour_day']}')  ";
        } else {
            $datawhere .= " and  sd.study_beginday <= '{$hour_day[1]}'  and (sd.study_isreading = '1' OR sd.study_endday > '{$hour_day[0]}')  ";
        }

        $trackwheres = " ca.student_id = sd.student_id AND ca.class_id = sd.class_id ";
        if (isset($request['day']) && $request['day'] !== "") {
            $trackwheres .= " and ca.track_day like '%{$request['day']}%'";
        }

        $sql = "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex,s.student_birthday,sd.study_beginday,
                (SELECT sf.family_mobile FROM smc_student_family AS sf WHERE sf.student_id = s.student_id AND sf.family_isdefault = 1 LIMIT 1) as family_mobile,
                (SELECT COUNT(ca.track_id) FROM smc_class as c,smc_course as co,smc_student_track as ca WHERE co.course_id = c.course_id and ca.coursecat_id = co.coursecat_id and {$trackwheres}) as track_nums
                FROM smc_student_study AS sd
                LEFT JOIN smc_student as s ON s.student_id = sd.student_id
                WHERE {$datawhere}
                HAVING {$hvaing}
                limit {$pagestart},{$num}";

        $allnum = $this->DataControl->selectOne("SELECT COUNT(s.student_id) as all_num,
                                                   (SELECT COUNT(ca.track_id) FROM smc_class as c,smc_course as co,smc_student_track as ca
                                                   WHERE co.course_id = c.course_id and ca.coursecat_id = co.coursecat_id and {$trackwheres}
                                                   ) as track_nums
                                                   FROM smc_student_study AS sd
                                                   LEFT JOIN smc_student as s ON s.student_id= sd.student_id
                                                   WHERE {$datawhere} HAVING {$hvaing}");

        if ($allnum) {
            $data['allnum'] = $allnum['all_num'];
        } else {
            $data['allnum'] = 0;
        }

        $studyList = $this->DataControl->selectClear($sql);
        if (!$studyList) {
            $studyList = array();
        } else {
            foreach ($studyList as &$val) {
                if ($val['track_nums']) {
                    $val['track_class'] = '已电访';
                } else {
                    $val['track_class'] = '未电访';
                }
            }
        }

        $intervallist = $this->DataControl->selectClear("SELECT i.interval_name,i.interval_scope FROM smc_class as c LEFT JOIN eas_course_interval as i ON i.course_id = c.course_id WHERE c.class_id = '{$request['class_id']}'");
        if ($intervallist) {
            foreach ($intervallist as &$v) {
                $interval_scope = json_decode($v['interval_scope'], true);
                $v['interval_scope_name'] = $this->ChinaseNum($interval_scope[0]['hour_lessontimes']);
            }
        } else {
            $intervallist = array();
        }

        $classOne = $this->DataControl->selectOne("SELECT c.class_cnname,c.class_enname,c.class_branch,co.coursetype_id,co.coursecat_id,ct.coursetype_cnname,cc.coursecat_cnname
                                                       FROM smc_class as c
                                                       LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                       LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                                                       LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = co.coursecat_id
                                                       WHERE c.class_id = '{$request['class_id']}' LIMIT 1");

        $data['list'] = $studyList;
        $data['interval'] = $intervallist;
        $data['class'] = $classOne;

        return $data;
    }

    // 数字转文字
    function ChinaseNum($num)
    {
        $char = array("零", "一", "二", "三", "四", "五", "六", "七", "八", "九");
        $dw = array("", "十", "百", "千", "万", "亿", "兆");
        $retval = "";
        $proZero = false;
        for ($i = 0; $i < strlen($num); $i++) {
            if ($i > 0) $temp = (int)(($num % pow(10, $i + 1)) / pow(10, $i));
            else $temp = (int)($num % pow(10, 1));

            if ($proZero == true && $temp == 0) continue;

            if ($temp == 0) $proZero = true;
            else $proZero = false;

            if ($proZero) {
                if ($retval == "") continue;
                $retval = $char[$temp] . $retval;
            } else $retval = $char[$temp] . $dw[$i] . $retval;
        }
        if ($retval == "一十") {
            $retval = "十";
        }
        return $retval;
    }


    /**
     * 获取沟通对象
     * author: ling
     * 对应接口文档 0001
     */
    function getCatitrackCodeApi()
    {
        $object = $this->DataControl->selectClear("select object_code,object_name from  crm_code_object  where 1=1  ");
        return $object;
    }

    /**
     * 新增学员的访谈记录
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function addStuCatitrackAction($request)
    {
        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['track_from'] = '1';
        $data['school_id'] = $request['school_id'];
        $data['class_id'] = $request['class_id'];
        $data['track_linktype'] = $request['commode_id'];
        $data['student_id'] = $request['student_id'];
        $classOne = $this->DataControl->selectOne("SELECT u.coursetype_id,u.coursecat_id
FROM smc_class AS c,smc_course AS u WHERE c.course_id = u.course_id AND c.class_id = '{$request['class_id']}' limit 0,1");
        if (!$classOne) {
            $this->error = '1';
            $this->errortip = '请确认班级信息存在！';
            return false;
        }
        $data['coursetype_id'] = $classOne['coursetype_id'];
        $data['coursecat_id'] = $classOne['coursecat_id'];
        $data['tracktype_id'] = $request['tracktype_id'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['track_code'] = $request['catitrack_code'];
        $tracktypeOne = $this->DataControl->selectOne("SELECT c.tracktype_name
FROM smc_code_tracktype AS c WHERE c.tracktype_id = '{$request['tracktype_id']}' limit 0,1");
        $data['track_classname'] = $tracktypeOne['tracktype_name'];
        $data['result_id'] = $request['trackresult_id'];
        $data['track_day'] = $request['catitrack_day'];
        $data['track_note'] = $request['catitrack_note'];
        $data['track_followutime'] = $request['catitrack_followutime'];
        $data['track_createtime'] = time();
        if ($this->DataControl->insertData("smc_student_track", $data)) {
            $this->DataControl->query("update smc_code_tracktype set tracktype_use = tracktype_use + 1 where tracktype_id='{$request['tracktype_id']}'");
            $this->error = '0';
            $this->errortip = '新增教务电访信息成功';
            return true;
        } else {
            $this->error = '1';
            $this->errortip = '新增教务电访信息失败';
            return false;
        }
    }

    /**
     * 获取访谈记录
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return mixed
     */
    function getCatitrackApi($request)
    {
        $datawhere = "sc.school_id = '{$request['school_id']}'";
        $class = $this->DataControl->selectOne("SELECT co.coursetype_id FROM smc_class as c LEFT JOIN smc_course as co ON co.course_id = c.course_id WHERE c.class_id = '{$request['class_id']}' LIMIT 1");
        if ($class && $class['coursetype_id'] > 0) {
            $datawhere .= " and sc.coursetype_id = '{$class['coursetype_id']}' ";
        }
        if (isset($request['tracktype_id']) && $request['tracktype_id'] !== '') {
            $datawhere .= " and sc.tracktype_id = '{$request['tracktype_id']}'";
        }
        if (isset($request['commode_id']) && $request['commode_id'] !== '') {
            $datawhere .= " and sc.track_linktype = '{$request['commode_id']}'";
        }
        if (isset($request['startime']) && $request['startime'] !== '') {
            $datawhere .= " and sc.track_day >= '{$request['startime']}' ";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and sc.track_day <= '{$request['endtime']}' ";
        }
        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and sc.student_id = '{$request['student_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like  '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sf.staffer_cnname like  '%{$request['keyword']}%')  ";
        }

        $sql = "select sf.staffer_cnname,sf.staffer_enname,sc.track_from,sc.track_note,sc.track_day,sc.track_createtime,
                (SELECT t.tracktype_name FROM smc_code_tracktype as t WHERE t.tracktype_id = sc.tracktype_id) as tracktype_name,
                (SELECT c.commode_name FROM crm_code_commode as c WHERE c.commode_id = sc.track_linktype) as commode_name,
                (SELECT o.object_name FROM crm_code_object AS o WHERE o.object_code = sc.track_code) as object_name,
                (SELECT r.trackresult_name FROM smc_code_trackresult AS r WHERE r.trackresult_id = sc.result_id) as trackresult_name,
                (SELECT c.class_cnname FROM smc_class AS c WHERE c.class_id = sc.class_id) as class_cnname,
                (SELECT p.post_name FROM gmc_company_post AS p, gmc_staffer_postbe AS b
                WHERE p.post_id = b.post_id AND b.staffer_id = sf.staffer_id AND b.school_id = sc.school_id ORDER BY b.postbe_ismianjob DESC LIMIT 1) AS post_name
                from smc_student_track as sc
                left join smc_student as s ON s.student_id = sc.student_id
                left join smc_staffer as sf ON sf.staffer_id = sc.staffer_id
                where {$datawhere}
                order by track_createtime DESC
                ";

        $studyList = $this->DataControl->selectClear($sql);
        if ($studyList) {
            foreach ($studyList as &$v) {
                if ($v['track_from']) {
                    $v['track_from'] = '教务登记';
                } else {
                    $v['track_from'] = '校务登记';
                }
                if (!$v['class_cnname']) {
                    $v['class_cnname'] = '--';
                }
                $v['track_createtime'] = date("Y-m-d H:i:s", $v['track_createtime']);
                if ($v['staffer_enname']) {
                    $v['staffer_cnname'] = $v['staffer_cnname'] . '-' . $v['staffer_enname'];
                }
            }
        } else {
            $studyList = array();
        }
        $data = array();
        $allnum = $this->DataControl->selectOne("select count(sc.track_id) as allnum
                                                     from smc_student_track as sc
                                                     left join smc_student as s ON s.student_id = sc.student_id
                                                     left join smc_staffer as sf ON sf.staffer_id = sc.staffer_id
                                                     where {$datawhere}");
        if ($allnum) {
            $data['allnum'] = $allnum['allnum'];
        } else {
            $data['allnum'] = 0;
        }

        $hourOne = $this->DataControl->selectOne("
            select ch.hour_name,c.class_cnname
            from smc_class_hour as ch
            left join smc_class as c ON c.class_id = ch.class_id
            where ch.hour_id = '{$request['hour_id']}'
        ");
        if (!$hourOne) {
            $hourOne = array();
        }

        $catitrack = $this->DataControl->selectOne("SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,c.track_day
                                                        FROM smc_student_study AS sd
                                                        LEFT JOIN smc_student as s ON s.student_id=sd.student_id
                                                        LEFT JOIN smc_student_track as c ON c.student_id=s.student_id
                                                        WHERE s.student_id='{$request['student_id']}' AND sd.school_id ='{$request['school_id']}' ORDER BY c.track_day DESC");
        if (!$catitrack) {
            $catitrack = array();
        }

        $data['info'] = $hourOne;
        $data['list'] = $studyList;
        $data['catitrack'] = $catitrack;
        return $data;
    }


    /**
     * 获取电访沟通模板
     * author: ling
     * 对应接口文档 0001
     */
    function getComtempApi($paramArray)
    {
        $sql = "SELECT
                    comtemp_id,
                    comtemp_content
                FROM
                    eas_code_comtemp
                WHERE
                    comtemp_status = '1' AND company_id = '{$paramArray['company_id']}'";
        $template = $this->DataControl->selectClear($sql);
        if (!$template) {
            $template = array();
        }

        return $template;
    }


    /**
     * 获取班级的在读人员 -- 小学成绩登记
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function getStudentScoreApi($request)
    {
        if ($request['hourList']) {
            $arr = array();
            $hour_list = json_decode(stripslashes($request['hourList']), true);
            foreach ($hour_list as $v) {
                $arr[] = $v['hour_id'];
            }
            $hourId = implode(",", $arr);
        } else {
            $hourId = 0;
        }

        $hourList = $this->DataControl->selectClear("SELECT h.hour_day, h.hour_name, CONCAT( o.course_branch,'_',h.hour_lessontimes) as hour_branch FROM smc_class_hour AS h, smc_class AS c, smc_course AS o WHERE c.class_id = h.class_id AND c.course_id = o.course_id and c.class_id = '{$request['class_id']}'and h.hour_id IN ({$hourId})");

        $datawhere = '1 and s.student_id > 0';
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%')";
        }

        $hvaing = "1=1";
        if (isset($request['score_class']) && $request['score_class'] == '0') {
            $hvaing .= " and score_num = '0'";
        } elseif (isset($request['score_class']) && $request['score_class'] == '1') {
            $hvaing .= " and score_num <> '0'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '30';
        }
        $pagestart = ($page - 1) * $num;

        $hour_day = date("Y-m-d");

        if ($hourList) {
            $hourfirst = current($hourList);
            $hourend = end($hourList);
            $datawhere .= " and  sd.study_beginday <= '{$hourfirst['hour_day']}'  and (sd.study_isreading = '1' OR sd.study_endday > '{$hourend['hour_day']}')  ";
        } else {
            $datawhere .= " and  sd.study_beginday <= '{$hour_day}'  and (sd.study_isreading = '1' OR sd.study_endday > '{$hour_day}')  ";
        }

        $datawheres = " 1 ";

        if (isset($request['month']) && $request['month'] !== "") {
            $datawheres .= " and FROM_UNIXTIME(sc.score_createtime, '%Y-%m') = '{$request['month']}'";
        } else {
            $month = date('Y-m');
            $datawheres .= " and FROM_UNIXTIME(sc.score_createtime, '%Y-%m') = '{$month}'";
        }

        $sql = "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex,s.student_birthday,sd.study_beginday,
                (SELECT COUNT(sc.score_id) FROM smc_student_score as sc WHERE {$datawheres} and sc.student_id = sd.student_id AND sc.class_id = sd.class_id) as score_num
                FROM smc_student_study AS sd
                LEFT JOIN smc_student as s ON s.student_id = sd.student_id
                WHERE {$datawhere} AND sd.class_id = '{$request['class_id']}'
                HAVING {$hvaing}
                limit {$pagestart},{$num}
                ";

        $allnum = $this->DataControl->selectOne("SELECT COUNT(s.student_id) as all_num,
                                                   (SELECT COUNT(sc.score_id) FROM smc_student_score as sc WHERE {$datawheres} and sc.student_id = sd.student_id AND sc.class_id = sd.class_id) as score_num
                                                   FROM smc_student_study AS sd
                                                   LEFT JOIN smc_student as s ON s.student_id= sd.student_id
                                                   WHERE {$datawhere} AND sd.class_id = '{$request['class_id']}' HAVING {$hvaing}");

        if ($allnum) {
            $data['allnum'] = $allnum['all_num'];
        } else {
            $data['allnum'] = 0;
        }

        $studyList = $this->DataControl->selectClear($sql);
        if (!$studyList) {
            $studyList = array();
        } else {
            foreach ($studyList as &$val) {
                if ($val['score_num']) {
                    $val['score_class'] = '已登记';
                } else {
                    $val['score_class'] = '未登记';
                }
            }
        }

        $data['list'] = $studyList;

        return $data;
    }

    /**
     * 获取历史成绩
     * author: wgh
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function getScoresApi($request)
    {
        $datawhere = "sc.class_id = '{$request['class_id']}' and sc.student_id = '{$request['student_id']}'";
        if (isset($request['score_year']) && $request['score_year'] !== '') {
            $datawhere .= " and sc.score_year = '{$request['score_year']}' ";
        }
        if (isset($request['score_term']) && $request['score_term'] !== '') {
            $datawhere .= " and sc.score_term = '{$request['score_term']}' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '100';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "
            select sc.score_id,sc.score_grade,sc.score_term,sc.score_tutoring,sc.score_kidschool,sc.score_year,FROM_UNIXTIME(sc.score_createtime) as score_createtime,sf.staffer_cnname,sf.staffer_enname,sf.staffer_img
            from smc_student_score as sc
            left join smc_staffer as sf ON sf.staffer_id = sc.staffer_id
            where {$datawhere} order by sc.score_createtime DESC
            LIMIT {$pagestart},{$num}
            ";

        $studyList = $this->DataControl->selectClear($sql);
        if ($studyList) {
            foreach ($studyList as &$v) {
                $v['score_grade_name'] = $this->ChinaseNum($v['score_grade']) . '年级';
                if ($v['score_term'] == 1) {
                    $v['score_term_name'] = '上学期期中';
                } elseif ($v['score_term'] == 2) {
                    $v['score_term_name'] = '上学期期末';
                } elseif ($v['score_term'] == 3) {
                    $v['score_term_name'] = '下学期期中';
                } elseif ($v['score_term'] == 4) {
                    $v['score_term_name'] = '下学期期末';
                }
                $v['score_year'] = $v['score_year'] . '年';
            }
        } else {
            $studyList = array();
        }
        $data = array();
        $allnum = $this->DataControl->selectOne("
            select count(sc.score_id) as allnum
            from smc_student_score as sc
            left join smc_staffer as sf ON sf.staffer_id = sc.staffer_id
            where {$datawhere}");
        if ($allnum) {
            $data['allnum'] = $allnum['allnum'];
        } else {
            $data['allnum'] = 0;
        }

        $catitrack = $this->DataControl->selectOne("SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,FROM_UNIXTIME(sc.score_createtime) as score_createtime
                                                       FROM smc_student_study AS sd
                                                       LEFT JOIN smc_student as s ON s.student_id=sd.student_id
                                                       LEFT JOIN smc_student_score as sc ON sc.student_id=s.student_id and sc.class_id=sd.class_id
                                                       WHERE s.student_id = '{$request['student_id']}' AND sd.school_id = '{$request['school_id']}'
                                                       ORDER BY sc.score_createtime DESC");
        if ($catitrack) {
            if (!$catitrack['score_createtime']) {
                $catitrack['score_createtime'] = '--';
            }
        } else {
            $catitrack = array();
        }

        $data['list'] = $studyList;
        $data['catitrack'] = $catitrack;
        return $data;
    }

    /**
     * 添加成绩
     * author: wgh
     * 对应接口文档 0001
     * @param $request
     */
    function addStuScoreAction($request)
    {
        $where = "class_id = '{$request['class_id']}' and student_id = '{$request['student_id']}' and score_grade = '{$request['score_grade']}' and score_term = '{$request['score_term']}' and score_year = '{$request['score_year']}'";
        if ($this->DataControl->getFieldOne("smc_student_score", "score_id", $where)) {
            $this->error = 1;
            $this->errortip = '不可重复登记';
            return false;
        }

        $data = array();
        $data['class_id'] = $request['class_id'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['student_id'] = $request['student_id'];
        $data['score_grade'] = $request['score_grade'];
        $data['score_term'] = $request['score_term'];
        $data['score_tutoring'] = $request['score_tutoring'];
        $data['score_kidschool'] = $request['score_kidschool'];
        $data['score_year'] = $request['score_year'];
        $data['score_createtime'] = time();
        if ($this->DataControl->insertData('smc_student_score', $data)) {
            $this->error = '0';
            $this->errortip = '操作成功';
            return true;
        } else {
            $this->error = '1';
            $this->errortip = '操作失败';
            return false;
        }
    }

    /**
     * 编辑成绩
     * author: wgh
     * 对应接口文档 0001
     * @param $request
     */
    function editStuScoreAction($request)
    {
        $data = array();
        $data['score_tutoring'] = $request['score_tutoring'];
        $data['score_kidschool'] = $request['score_kidschool'];
        if ($this->DataControl->updateData("smc_student_score", "score_id = '{$request['score_id']}'", $data)) {
            $this->error = '0';
            $this->errortip = '操作成功';
            return true;
        } else {
            $this->error = '1';
            $this->errortip = '操作失败';
            return false;
        }
    }


    /**
     *  班级统计报表
     * author: ling
     * 对应接口文档 0001
     */
    function getStaClassReport($paramArray)
    {
        $datawhere = "cs.company_id='{$this->company_id}' and cs.class_type = '0' and cs.class_status <> '-2' and cl.classcode_isregister =1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_enname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= "  and co.course_id = '{$paramArray['course_id']}'";
        }

        //添加班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= "  and cs.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
            $datawhere .= " and  cs.class_id in (select DISTINCT cht.class_id from smc_class_teach as cht where cht.staffer_id='{$paramArray['re_staffer_id']}' and cht.teach_status = '0' and cht.teach_type = '0')";
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== '') {
            $datawhere .= "  and cs.class_status = '{$paramArray['class_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select  cp.postpart_isteregulator from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and exists (select ht.staffer_id from smc_class_teach as ht where ht.class_id = cs.class_id and ht.staffer_id = '{$this->staffer_id}' and ht.teach_status = '0' and ht.teach_type = '0') ";
            }
        }

        $pagestart = ($page - 1) * $num;

        $today = date("Y-m-d");

        $sql = "select cs.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,sl.school_shortname,sl.school_branch,co.course_branch,co.course_cnname,
              (select group_concat( DISTINCT s.staffer_cnname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_cnname,
              (select group_concat( DISTINCT s.staffer_enname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_enname,
              (select group_concat( DISTINCT s.staffer_branch) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_branch,
            (select count(s.study_id) from smc_student_study as s where s.class_id = cs.class_id and s.study_isreading =1) as  study_num,
            (select count(h.hour_id)
                from smc_class_hour as h
                left join smc_class as sc on sc.class_id = h.class_id
                left join smc_course as co ON co.course_id = sc.course_id
                left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                where h.class_id = cs.class_id and h.hour_iswarming = 0 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
            ) as hour_num,
            (select count(h.hour_id)
                from smc_class_hour as h
                left join smc_class as sc on sc.class_id = h.class_id
                left join smc_course as co ON co.course_id = sc.course_id
                left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                where h.class_id = cs.class_id and hour_ischecking = 1 and h.hour_iswarming = 0 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
            ) as hour_finishnum
            from  smc_class as cs 
            left join smc_course as co ON cs.course_id=co.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            left join smc_school as sl ON sl.school_id = cs.school_id
            where {$datawhere} 
            order by  (case when class_status = 1 then 1 when class_status = -1 then 2 when class_status = 0 then 3 END ) ASC 
            Limit {$pagestart},{$num}";


        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束'));
            foreach ($dataList as $key => $value) {
                $dataList[$key]['class_status_name'] = $status[$value['class_status']];
                $dataList[$key]['hour_progress'] = $value['hour_finishnum'] . '/' . $value['hour_num'];
            }
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("select count(DISTINCT cs.class_id) as all_num    from  smc_class as cs
            left join smc_course as co ON cs.course_id=co.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            left join smc_school as sl ON sl.school_id = cs.school_id
            where {$datawhere}");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }


    /**
     * 平常时期电访执行统计表
     * author: qu
     */
    function getCatitrackReport($paramArray)
    {
        $datawhere = "cs.company_id='{$this->company_id}' and cs.class_type = '0' and cs.class_status <> '-2' and cl.classcode_isregister =1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_enname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= "  and co.course_id = '{$paramArray['course_id']}'";
        }

        //添加班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= "  and cs.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
            $datawhere .= " and  cs.class_id in (select DISTINCT cht.class_id from smc_class_hour_teaching as cht where cht.staffer_id='{$paramArray['re_staffer_id']}' )";
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== '') {
            $datawhere .= "  and cs.class_status = '{$paramArray['class_status']}'";
        }
        if ($paramArray['class_status'] == '-1') {
            $datawhere .= " and cs.class_enddate like '%{$paramArray['day']}%'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $datawheres = " ca.school_id = cs.school_id AND ca.class_id = cs.class_id ";

        if (isset($paramArray['day']) && $paramArray['day'] !== "") {
            $datawheres .= " and ca.track_day like '%{$paramArray['day']}%'";
        }

        $firstday = $paramArray['day'] . '-01';
        $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select  cp.postpart_isteregulator from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and exists (select ht.staffer_id from smc_class_teach as ht where ht.class_id = cs.class_id and ht.teach_type = '0' and ht.teach_status = '0' and ht.staffer_id = '{$this->staffer_id}') ";
            }
        }

        $sql = "select cs.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,sl.school_shortname,sl.school_branch,co.course_branch,co.course_cnname,
            (select group_concat( DISTINCT s.staffer_cnname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and teach_status = '0' and teach_type = '0') as  staffer_cnname,
            (select group_concat( DISTINCT s.staffer_enname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and teach_status = '0' and teach_type = '0') as  staffer_enname,
            (select group_concat( DISTINCT s.staffer_branch) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and teach_status = '0' and teach_type = '0') as staffer_branch,
            (select count(s.student_id) from smc_student_study as s where s.class_id = cs.class_id and s.study_isreading = '1' and ((study_beginday >= '{$firstday}' AND study_beginday <= '{$lastday}') OR (study_beginday <= '{$firstday}' AND study_endday >= '{$lastday}') OR (study_endday >= '{$firstday}' AND study_endday <= '{$lastday}'))) as study_num,
            (SELECT COUNT(DISTINCT ca.student_id) FROM smc_student_track AS ca LEFT JOIN smc_student_study AS s ON s.student_id = ca.student_id AND s.class_id = ca.class_id and s.study_isreading = '1' WHERE {$datawheres} AND ((study_beginday >= '{$firstday}' AND study_beginday <= '{$lastday}') OR (study_beginday <= '{$firstday}' AND study_endday >= '{$lastday}') OR (study_endday >= '{$firstday}' AND study_endday <= '{$lastday}'))) AS track_num
            from smc_class as cs 
            left join smc_course as co ON cs.course_id = co.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            left join smc_school as sl ON sl.school_id = cs.school_id
            where {$datawhere} 
            order by (case when class_status = 1 then 1 when class_status = -1 then 2 when class_status = 0 then 3 END) ASC 
            Limit {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束'));
            foreach ($dataList as $key => $value) {
                $dataList[$key]['class_status_name'] = $status[$value['class_status']];
                $dataList[$key]['rate'] = sprintf("%.2f", $value['track_num'] / $value['study_num'] * 100) . '%';
            }
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("select count(DISTINCT cs.class_id) as all_num from smc_class as cs
            left join smc_course as co ON cs.course_id = co.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            left join smc_school as sl ON sl.school_id = cs.school_id
            where {$datawhere}");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }

    /**
     * 校园平常时期电访执行统计表
     * author: qu
     */
    function getCatitrackScReport($paramArray)
    {
        $datawhere = "s.company_id='{$this->company_id}' and s.school_isclose = '0' and s.school_istest = '0' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['dataequity']) && $paramArray['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $datawhere .= " and s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        } else {
            $datawhere .= " and s.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id']) {
            $orgaize_datawhere = " and s.school_id in (select 
            og.school_id from gmc_company_organizeschool as og
            left join  gmc_company_organize as  g On og.organize_id = g.organize_id
             where g.organizeclass_id='{$paramArray['organizeclass_id']}' ) ";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id']) {
            $orgaize_datawhere = " and (s.school_id in (select og.school_id from gmc_company_organizeschool as og,gmc_company_organize as g where g.organize_id = og.organize_id and g.father_id='{$paramArray['organize_id']}' ) or  s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['organize_id']}' ) ) ";
        }
        if (isset($paramArray['sec_organize_id']) && $paramArray['sec_organize_id']) {
            $orgaize_datawhere = " and s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['sec_organize_id']}' ) ";
        }
        $datawhere .= $orgaize_datawhere;

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }

        $datawheres = " 1 ";

        if (isset($paramArray['month']) && $paramArray['month'] !== "") {
            $datawheres .= " and ca.track_day like '%{$paramArray['month']}%'";
        }

        $coursetypewhere = " and 1";

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $coursetypewhere .= " and co.coursetype_id='{$paramArray['coursetype_id']}'";
        }

        $firstday = $paramArray['month'] . '-01';
        $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));

        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.school_cnname,
                s.school_branch,
	            d.district_cnname,
                (
                SELECT
                    count( DISTINCT ( c.class_id )) 
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch AND cl.company_id = co.company_id 
                WHERE
                    c.school_id = s.school_id 
                     {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND (
                       (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                    OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                    OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                     )
                ) AS class_num,
                (
                SELECT
                    count( DISTINCT ( ss.student_id )) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch AND cl.company_id = co.company_id
                WHERE
                    ss.school_id = s.school_id 
                     {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND ss.study_isreading = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND (
                       (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                    OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                    OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                     )
                     AND (
                        ( study_beginday >= '{$firstday}' AND study_beginday <= '{$lastday}' ) 
                     OR ( study_beginday <= '{$firstday}' AND study_endday >= '{$lastday}' ) 
                     OR ( study_endday >= '{$firstday}' AND study_endday <= '{$lastday}' ) 
                      ) 
                ) AS stu_num,
                (
                SELECT
                    COUNT(DISTINCT(ca.student_id)) 
                FROM
                    smc_student_track AS ca
                    LEFT JOIN smc_student_study AS sd ON sd.student_id = ca.student_id and sd.class_id = ca.class_id
                    LEFT JOIN smc_class as c on c.class_id = ca.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch AND cl.company_id = co.company_id
                WHERE
                  {$datawheres}  
                  {$coursetypewhere}
                  AND s.school_id = ca.school_id 
                  AND cl.classcode_isregister = '1' 
                  AND sd.study_isreading = '1' 
                  AND c.class_status > '-2' 
                  AND c.class_status <> '0' 
                  AND c.class_type = '0' 
                  AND (
                     (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                  OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                  OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                   )
                ) AS track_num
            FROM
                smc_school AS s
                left join gmc_company_district as d on s.district_id = d.district_id
            WHERE
                {$datawhere}
            GROUP BY
                s.school_id
            ";

        $dataList = $this->DataControl->selectClear($sql. " Limit {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as $key => $value) {
                $dataList[$key]['rate'] = sprintf("%.2f", $value['track_num'] / $value['stu_num'] * 100) . '%';
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['stu_num'] = $dateexcelvar['stu_num'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];

                    $datearray['rate'] = sprintf("%.2f", $dateexcelvar['track_num'] / $dateexcelvar['stu_num'] * 100) . '%';

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('所属区域', '校区名称', '校区编号', '班级数量', '学员数', '已电访学员数', '电访率'));
            $excelfileds = array('district_cnname', 'school_cnname', 'school_branch', 'class_num', 'stu_num', 'track_num', 'rate');

            $tem_name = $this->LgStringSwitch('校园平常时期电访执行统计表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        }

        $allnum = $this->DataControl->selectClear($sqls);
        if ($allnum) {
            $data['allnum'] = count($allnum);
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }


    /**
     * 小学成绩登记统计报表
     * author: wgh
     * 对应接口文档 0001
     */
    function getStaScore($paramArray)
    {
        $datawhere = "cs.company_id='{$this->company_id}' and cs.class_type = '0' and cs.class_status <> '-2' and cl.classcode_isregister =1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_enname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= "  and co.course_id = '{$paramArray['course_id']}'";
        }

        //添加班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= "  and cs.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
            $datawhere .= " and  cs.class_id in (select DISTINCT cht.class_id from smc_class_teach as cht where cht.staffer_id='{$paramArray['re_staffer_id']}' and cht.teach_status = '0' and cht.teach_type = '0')";
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== '') {
            $datawhere .= "  and cs.class_status = '{$paramArray['class_status']}'";
        }
        if ($paramArray['class_status'] == '-1') {
            $datawhere .= " and cs.class_enddate like '%{$paramArray['day']}%'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select  cp.postpart_isteregulator from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and exists (select ht.staffer_id from smc_class_teach as ht where ht.class_id = cs.class_id and ht.staffer_id = '{$this->staffer_id}' and ht.teach_status = '0' and ht.teach_type = '0') ";
            }
        }

        $datawheres = " 1 ";

        if (isset($paramArray['day']) && $paramArray['day'] !== "") {
            $datawheres .= " and FROM_UNIXTIME(sc.score_createtime, '%Y-%m') = '{$paramArray['day']}'";
        }

        $today = GetTheMonth($paramArray['day']);
        $firstday = $today[0];
        $lastday = $today[1];

        $sql = "select cs.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,sl.school_shortname,sl.school_branch,co.course_branch,co.course_cnname,
                (select group_concat( DISTINCT s.staffer_cnname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_cnname,
                (select group_concat( DISTINCT s.staffer_enname) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_enname,
                (select group_concat( DISTINCT s.staffer_branch) from smc_class_teach as ht,smc_staffer as s where ht.staffer_id =s.staffer_id and ht.class_id=cs.class_id and ht.teach_type = '0' and ht.teach_status = '0') as  staffer_branch,
                (select count(DISTINCT s.study_id) from smc_student_study as s where s.class_id = cs.class_id and ((study_beginday >= '{$firstday}' AND study_beginday <= '{$lastday}') OR (study_beginday <= '{$firstday}' AND study_endday >= '{$lastday}') OR(study_endday >= '{$firstday}' AND study_endday <= '{$lastday}'))) as study_num,
                (select count(DISTINCT s.study_id) from smc_student_study as s left join smc_student_score as sc on sc.class_id = s.class_id and sc.student_id = s.student_id where {$datawheres} and s.class_id = cs.class_id and ((study_beginday >= '{$firstday}' AND study_beginday <= '{$lastday}') OR (study_beginday <= '{$firstday}' AND study_endday >= '{$lastday}') OR(study_endday >= '{$firstday}' AND study_endday <= '{$lastday}')) and sc.score_id > 0) as  study_registernum
                from  smc_class as cs 
                left join smc_course as co ON cs.course_id=co.course_id
                left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                left join smc_school as sl ON sl.school_id = cs.school_id
                where {$datawhere} 
                order by  (case when class_status = 1 then 1 when class_status = -1 then 2 when class_status = 0 then 3 END ) ASC 
                Limit {$pagestart},{$num}";


        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束'));
            foreach ($dataList as $key => $value) {
                $dataList[$key]['class_status_name'] = $status[$value['class_status']];
                $dataList[$key]['register_rate'] = sprintf("%.2f", $value['study_registernum'] / $value['study_num'] * 100) . '%';
            }
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("select count(DISTINCT cs.class_id) as all_num
            from smc_class as cs
            left join smc_course as co ON cs.course_id=co.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            left join smc_school as sl ON sl.school_id = cs.school_id
            where {$datawhere}");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }

    /**
     * 校园小学成绩登记统计表
     * author: wgh
     */
    function getScoreReport($paramArray)
    {
        $datawhere = "s.company_id='{$this->company_id}' and s.school_isclose = '0' and s.school_istest = '0' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['dataequity']) && $paramArray['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $datawhere .= " and s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        } else {
            $datawhere .= " and s.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id']) {
            $orgaize_datawhere = " and s.school_id in (select 
            og.school_id from gmc_company_organizeschool as og
            left join  gmc_company_organize as  g On og.organize_id = g.organize_id
             where g.organizeclass_id='{$paramArray['organizeclass_id']}' ) ";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id']) {
            $orgaize_datawhere = " and (s.school_id in (select og.school_id from gmc_company_organizeschool as og,gmc_company_organize as g where g.organize_id = og.organize_id and g.father_id='{$paramArray['organize_id']}' ) or  s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['organize_id']}' ) ) ";
        }
        if (isset($paramArray['sec_organize_id']) && $paramArray['sec_organize_id']) {
            $orgaize_datawhere = " and s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['sec_organize_id']}' ) ";
        }
        $datawhere .= $orgaize_datawhere;

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $datawheres = " 1 ";

        if (isset($paramArray['month']) && $paramArray['month'] !== "") {
            $datawheres .= " and FROM_UNIXTIME(sc.score_createtime, '%Y-%m') = '{$paramArray['month']}'";
        }

        $firstday = $paramArray['month'] . '-01';
        $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));

        $coursetypewhere = " and 1";

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $coursetypewhere .= " and co.coursetype_id='{$paramArray['coursetype_id']}'";
        }


        $sql = "
            SELECT
                s.school_id,
                s.school_cnname,
                s.school_branch,
                (
                SELECT
                    count(
                    DISTINCT ( c.class_id )) 
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                    AND cl.company_id = co.company_id 
                WHERE
                    c.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND (
                       (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                    OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                    OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                     )
                ) AS class_num,
                (
                SELECT
                    count(
                    DISTINCT ( ss.student_id )) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                WHERE
                    ss.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND (
                       (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                    OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                    OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                     )
                ) AS stu_num,
                (
                SELECT
                    COUNT(DISTINCT(ss.student_id)) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                    LEFT JOIN smc_student_score as sc on sc.class_id = ss.class_id and sc.student_id = ss.student_id
                WHERE
                  {$datawheres} and 
                  s.school_id = ss.school_id 
                    {$coursetypewhere}
                  AND cl.classcode_isregister = '1' 
                  AND c.class_status > '-2' 
                  AND c.class_status <> '0' 
                  AND c.class_type = '0' 
                  AND sc.score_id > '0' 
                  AND (
                     (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                  OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                  OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                   )
                ) AS study_registernum
            FROM
                smc_school AS s
            WHERE
                {$datawhere}
            GROUP BY
                s.school_id
            Limit {$pagestart},{$num}";


        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as $key => $value) {
                $dataList[$key]['register_rate'] = sprintf("%.2f", $value['study_registernum'] / $value['stu_num'] * 100) . '%';
            }
        }

        $sqls = "
            SELECT
               s.school_cnname,
               s.school_branch,
                (
                SELECT
                    count(
                    DISTINCT ( c.class_id )) 
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                    AND cl.company_id = co.company_id 
                WHERE
                    c.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND (
                       (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                    OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                    OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                     )
                ) AS class_num,
                (
                SELECT
                    count(
                    DISTINCT ( ss.student_id )) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                WHERE
                    ss.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND (
                       (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                    OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                    OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                     )
                ) AS stu_num,
                (
                SELECT
                    COUNT(DISTINCT(ss.student_id)) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                    LEFT JOIN smc_student_score as sc on sc.class_id = ss.class_id and sc.student_id = ss.student_id
                WHERE
                  {$datawheres} and 
                  s.school_id = ss.school_id 
                    {$coursetypewhere}
                  AND cl.classcode_isregister = '1' 
                  AND c.class_status > '-2' 
                  AND c.class_status <> '0' 
                  AND c.class_type = '0' 
                  AND sc.score_id > '0' 
                  AND (
                     (class_stdate >= '{$firstday}' AND class_stdate <= '{$lastday}' ) 
                  OR (class_stdate <= '{$firstday}' AND class_enddate >= '{$lastday}' )
                  OR (class_enddate >= '{$firstday}' AND class_enddate <= '{$lastday}')
                   )
                ) AS study_registernum
            FROM
                smc_school AS s
            WHERE
                {$datawhere}
            GROUP BY
                s.school_id";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sqls);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['stu_num'] = $dateexcelvar['stu_num'];
                    $datearray['study_registernum'] = $dateexcelvar['study_registernum'];

                    $datearray['register_rate'] = sprintf("%.2f", $dateexcelvar['study_registernum'] / $dateexcelvar['stu_num'] * 100) . '%';

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '班级数量', '学员数', '已登记学员数', '登记率'));
            $excelfileds = array('school_cnname', 'school_branch', 'class_num', 'stu_num', 'study_registernum', 'register_rate');

            $tem_name = $this->LgStringSwitch('校园小学成绩登记统计表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        }

        $allnum = $this->DataControl->selectClear($sqls);
        if ($allnum) {
            $data['allnum'] = count($allnum);
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }


    /**
     * 校园小学成绩登记详情统计表
     * author: wgh
     * 对应接口文档 0001
     */
    function getScoreReportDetail($paramArray)
    {
        $datawhere = "cs.company_id='{$this->company_id}' and cs.class_type = '0' and cs.class_status <> '-2' and cl.classcode_isregister =1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_enname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= "  and co.course_id = '{$paramArray['course_id']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= "  and cs.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== '') {
            $datawhere .= "  and cs.class_id = '{$paramArray['class_id']}'";
        }

        if (isset($paramArray['score_term']) && $paramArray['score_term'] !== '') {
            $datawhere .= "  and sc.score_term = '{$paramArray['score_term']}'";
        }

        if (isset($paramArray['day']) && $paramArray['day'] !== "") {
            $datawhere .= " and FROM_UNIXTIME(sc.score_createtime, '%Y-%m') = '{$paramArray['day']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select  cp.postpart_isteregulator from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and exists (select ht.staffer_id from smc_class_teach as ht where ht.class_id = cs.class_id and ht.staffer_id = '{$this->staffer_id}' and ht.teach_status = '0' and ht.teach_type = '0') ";
            }
        }

        $sql = "select cs.class_id,cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,sl.school_shortname as school_cnname,sl.school_branch,co.course_branch,co.course_cnname,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,sd.study_beginday,
                sc.score_year,sc.score_grade,sc.score_term,sc.score_tutoring,sc.score_kidschool,sc.score_createtime
                from smc_student_score as sc
                left join smc_class as cs ON cs.class_id=sc.class_id
                left join smc_course as co ON cs.course_id=co.course_id
                left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                left join smc_school as sl ON sl.school_id = cs.school_id
                left join smc_student as st ON st.student_id = sc.student_id
                left join smc_student_study as sd ON sd.student_id = sc.student_id and sd.class_id = sc.class_id
                where {$datawhere}
                order by sc.score_createtime desc 
                Limit {$pagestart},{$num}";


        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$value) {
                $value['score_year'] = $value['score_year'] . '年';
                $value['score_grade_name'] = $this->ChinaseNum($value['score_grade']) . '年级';
                if ($value['score_term'] == 1) {
                    $value['score_term_name'] = '上学期期中';
                } elseif ($value['score_term'] == 2) {
                    $value['score_term_name'] = '上学期期末';
                } elseif ($value['score_term'] == 3) {
                    $value['score_term_name'] = '下学期期中';
                } elseif ($value['score_term'] == 4) {
                    $value['score_term_name'] = '下学期期末';
                }
                $value['score_createtime'] = date("Y-m-d", $value['score_createtime']);
            }
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("select count(DISTINCT sc.score_id) as all_num
            from smc_student_score as sc
            left join smc_class as cs ON cs.class_id=sc.class_id
            left join smc_course as co ON cs.course_id=co.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            left join smc_school as sl ON sl.school_id = cs.school_id
            where {$datawhere}");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }

    /**
     * 校园班平统计表
     * author: wgh
     */
    function getScClassAverage($paramArray)
    {
        $datawhere = "s.company_id='{$this->company_id}' and s.school_isclose = '0' and s.school_istest = '0' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['dataequity']) && $paramArray['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $datawhere .= " and s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        } else {
            $datawhere .= " and s.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id']) {
            $orgaize_datawhere = " and s.school_id in (select 
            og.school_id from gmc_company_organizeschool as og
            left join  gmc_company_organize as  g On og.organize_id = g.organize_id
             where g.organizeclass_id='{$paramArray['organizeclass_id']}' ) ";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id']) {
            $orgaize_datawhere = " and (s.school_id in (select og.school_id from gmc_company_organizeschool as og,gmc_company_organize as g where g.organize_id = og.organize_id and g.father_id='{$paramArray['organize_id']}' ) or  s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['organize_id']}' ) ) ";
        }
        if (isset($paramArray['sec_organize_id']) && $paramArray['sec_organize_id']) {
            $orgaize_datawhere = " and s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['sec_organize_id']}' ) ";
        }
        $datawhere .= $orgaize_datawhere;

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $datawheres = " 1 ";

        if (isset($paramArray['day']) && $paramArray['day'] !== "") {
            $datawheres .= " and class_enddate >= '{$paramArray['day']}'";
        } else {
            $paramArray['day'] = date('Y-m-d');

        }

        $coursetypewhere = " and 1";
        $classinfowhere = " and 1";

        $coursetypeCnname = '';
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $coursetypewhere .= " and co.coursetype_id='{$paramArray['coursetype_id']}'";
            $classinfowhere .= " and y.coursetype_id='{$paramArray['coursetype_id']}'";

            $cousettypeOne = $this->DataControl->selectOne("select coursetype_cnname from smc_code_coursetype where company_id = '{$this->company_id}' and coursetype_id = '{$paramArray['coursetype_id']}'");
            if ($cousettypeOne) {
                $coursetypeCnname = $cousettypeOne['coursetype_cnname'];
            }
        }


        $sql = "
            SELECT
                s.school_id,
                s.school_cnname,
                (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname,
                s.school_branch,
                (SELECT
                    count(DISTINCT ( c.class_id )) 
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                    AND cl.company_id = co.company_id 
                WHERE
                    c.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND {$datawheres}
                ) AS class_num,
                (SELECT
                    count(DISTINCT ( ss.student_id )) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                WHERE
                    ss.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    and ss.study_beginday<='{$paramArray['day']}'         
                    and ss.study_endday>='{$paramArray['day']}'         
                    AND {$datawheres}
                ) AS stu_num
                ,(select group_concat(x.class_enname) from smc_class x,smc_course y 
                where x.course_id=y.course_id {$classinfowhere} and x.class_status=-1  and x.school_id=s.school_id
                and x.class_enddate>=DATE_SUB('{$paramArray['day']}', INTERVAL 15 DAY)) as endclass_info
            FROM
                smc_school AS s
            WHERE
                {$datawhere}
            GROUP BY
                s.school_id
            Limit {$pagestart},{$num}";


        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['class_averagenum'] = ($value['stu_num'] / $value['class_num']) ? round($value['stu_num'] / $value['class_num'], 2) : 0;
            }
        } else {
            $dataList = array();
        }

        $sqls = "
            SELECT
                s.school_cnname,
                s.school_branch,
                (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname,
                (SELECT
                    count(DISTINCT ( c.class_id )) 
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                    AND cl.company_id = co.company_id 
                WHERE
                    c.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    AND {$datawheres}
                ) AS class_num,
                (SELECT
                    count(DISTINCT ( ss.student_id )) 
                FROM
                    smc_student_study AS ss
                    left join smc_class as c on c.class_id = ss.class_id
                    LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                    LEFT JOIN eas_classcode AS cl ON cl.classcode_branch = co.course_branch 
                WHERE
                    ss.school_id = s.school_id 
                    {$coursetypewhere}
                    AND cl.classcode_isregister = '1' 
                    AND c.class_status > '-2' 
                    AND c.class_status <> '0' 
                    AND c.class_type = '0' 
                    and ss.study_beginday<='{$paramArray['day']}'         
                    and ss.study_endday>='{$paramArray['day']}'   
                    AND {$datawheres}
                ) AS stu_num
                ,(select group_concat(x.class_enname) from smc_class x,smc_course y 
                where x.course_id=y.course_id {$classinfowhere} and x.class_status=-1 and x.school_id=s.school_id
                and x.class_enddate>=DATE_SUB('{$paramArray['day']}', INTERVAL 15 DAY)) as endclass_info
            FROM
                smc_school AS s
            WHERE
                {$datawhere}
            GROUP BY
                s.school_id";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sqls);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['coursetype_cnname'] = ($coursetypeCnname != '') ? $coursetypeCnname : '--';
                    $datearray['stu_num'] = $dateexcelvar['stu_num'];
                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['class_averagenum'] = ($dateexcelvar['stu_num'] / $dateexcelvar['class_num']) ? round($dateexcelvar['stu_num'] / $dateexcelvar['class_num'], 2) : 0;
                    $datearray['endclass_info'] = $dateexcelvar['endclass_info'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '所属区域', '班组', '在读总人数', '班级数量', '班级平均人数', '15天内结班情况'));
            $excelfileds = array('school_cnname', 'school_branch', 'district_cnname', 'coursetype_cnname', 'stu_num', 'class_num', 'class_averagenum', 'endclass_info');

            $tem_name = $this->LgStringSwitch('校园班平统计表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        }

        $allnum = $this->DataControl->selectClear($sqls);
        if ($allnum) {
            $data['allnum'] = count($allnum);
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }


    /**
     * 校园班平教师统计表
     * author: wgh
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function scClassTeaacher($paramArray)
    {
        $datawhere = " cl.company_id='{$paramArray['company_id']}' and cl.class_type = '0' and ec.classcode_isregister = '1' and htg.teach_type = '0' and htg.teach_status = '0'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sf.staffer_cnname like '%{$paramArray['keyword']}%' or sf.staffer_enname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '%{$paramArray['keyword']}%' )";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " and cl.school_id = '{$paramArray['school_id']}'";
        }

        $study_where = "ss.school_id = s.school_id";
        //截止时间
        if (isset($paramArray['end_time']) && $paramArray['end_time'] != '') {
            $study_where .= " and ss.study_beginday <= '{$paramArray['end_time']}' and ss.study_endday >= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and htg.staffer_id = '{$paramArray['staffer_id']}' ";
            }
        }

        $sql = "select s.school_id,s.school_shortname as school_cnname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,staffer_branch,sf.staffer_id,
            (select count(DISTINCT ht.class_id) 
                from smc_class_teach as ht 
                left join smc_class as sc on sc.class_id = ht.class_id
                left join smc_school as ss on ss.school_id = sc.school_id
                left join smc_course as co ON co.course_id = sc.course_id
                left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                where ht.staffer_id = sf.staffer_id and ss.school_id=cl.school_id and sc.class_status = '1' and sc.class_type = '0' and ec.classcode_isregister = '1' and ht.teach_type = '0' and ht.teach_status = '0'
            )  as teaching_num,
            (SELECT
                count(DISTINCT ss.student_id)
            FROM
                smc_student_study AS ss
                left join smc_class_teach as ht on ht.class_id = ss.class_id
                left join smc_class as c on c.class_id = ss.class_id
                left join smc_course AS co ON c.course_id = co.course_id
                left join eas_classcode AS cl ON cl.classcode_branch = co.course_branch AND cl.company_id = co.company_id 
            WHERE
                {$study_where}
                AND ht.staffer_id = sf.staffer_id
                AND ss.study_isreading = '1'
                AND ht.teach_type = '0'
                AND ht.teach_status = '0'
                AND cl.classcode_isregister = '1' 
                AND c.class_status > '-2' 
                AND c.class_status <> '0' 
                AND c.class_type = '0'
            ) AS stu_num
            from smc_class_teach as htg
            left join smc_class as cl On htg.class_id = cl.class_id
            left join smc_staffer as sf ON sf.staffer_id = htg.staffer_id
            left join smc_school as s ON s.school_id = cl.school_id
            left join smc_course as co ON co.course_id = cl.course_id
            left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
            where {$datawhere}   and sf.staffer_id > 0 group by s.school_id,sf.staffer_id
            Limit {$pagestart},{$num}
        ";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['class_averagenum'] = ($value['stu_num'] / $value['teaching_num']) ? round($value['stu_num'] / $value['teaching_num'], 2) : 0;
            }
        } else {
            $dataList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("select count(q.school_id) as all_num from
            ( select s.school_id
            from smc_class_teach as htg
            left join smc_class as cl On htg.class_id = cl.class_id
            left join smc_staffer as sf ON sf.staffer_id = htg.staffer_id
            left join smc_school as s ON s.school_id = cl.school_id
            left join smc_course as co ON co.course_id = cl.course_id
            left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
            where {$datawhere} and sf.staffer_id > 0 group by s.school_id,sf.staffer_id) as q
            ");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }
        $data['list'] = $dataList;

        return $data;

    }


    /**
     *  课时统计
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return mixed
     */
    function getClassHourReport($request)
    {
        $arr_hour_homework = array(0 => '无作业', 1 => '已完成', '-1' => '未完成');
        $arr_hour_netstatus = array('0' => '待参与', '1' => '已参与', '-1' => '未参与');
        $arr_bookcheck = array('1' => '已完成', '0' => '未完成');
        $info = $this->DataControl->selectOne(
            "select cs.class_cnname,cs.class_enname,cs.class_branch,sl.school_shortname as school_cnname,
                  (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=cs.class_id and ch.hour_iswarming =0 ) as hour_allnum,
                  (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=cs.class_id and ch.hour_ischecking =1 and ch.hour_iswarming =0) as hour_finishnum
                  from smc_class as cs 
                  left join smc_school as sl ON sl.school_id = cs.school_id
                  where cs.class_id='{$request['class_id']}' limit 0,1");
        $data['info'] = $info == array() ? array() : $info;
        $hourOne = $this->DataControl->selectOne("SELECT h.hour_name , CONCAT( o.course_branch,'_',h.hour_lessontimes) as hour_branch 
            FROM smc_class_hour AS h
            LEFT JOIN smc_class AS c on c.class_id = h.class_id
            LEFT JOIN smc_course AS o ON  c.course_id = o.course_id 
            WHERE c.class_id = '{$request['class_id']}'and h.hour_id='{$request['hour_id']}'");
        $hour_day = date("Y-m-d");
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['hour_id']) {
            $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_sex,s.student_branch,student_birthday,hour_testscore,hour_homeworkscores,sh.hour_homework,hour_netstatus,hour_netscores,hour_refresher,sh.hour_name,s.student_branch,cl.class_branch,sh.hour_branch,sd.class_id,sh.hour_bookcheck
            from smc_student_study AS sd
            left join smc_class as cl ON cl.class_id=sd.class_id
            left join smc_student as s ON s.student_id= sd.student_id
            left join eas_student_hour as sh ON sh.student_id = sd.student_id and sh.class_id = sd.class_id and sh.hour_branch = '{$hourOne['hour_branch']}'
            left join smc_student_hourstudy as hy On hy.class_id=cl.class_id and sd.student_id=hy.student_id 
            where {$datawhere} and s.student_id > 0 and hy.hour_id='{$request['hour_id']}'
            limit {$pagestart},{$num}
            ";
        } else {
            $hourwhere = "1";
            if (isset($request['starttime']) && $request['starttime']) {
                $hourwhere .= " and h.hour_day >= '{$request['starttime']}'";
            }
            if (isset($request['endtime']) && $request['endtime']) {
                $hourwhere .= " and h.hour_day <= '{$request['endtime']}'";
            } else {
                $request['endtime'] = date("Y-m-d");
            }

            $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_sex,s.student_branch,student_birthday,cl.class_branch,'' as hour_homework,''as hour_netstatus,'' as hour_netscores,'' as hour_bookcheck,
            (select count(sh.student_id) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and hour_netstatus =1 and  {$hourwhere}) as hour_netstatus_num,
            (select count(sh.student_id) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where  h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and (hour_netstatus =1 or hour_netstatus = -1) and {$hourwhere}) as hour_netstatus_allnum,
            (select avg(sh.hour_netscores) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and hour_netstatus =1 and  {$hourwhere}) as hour_netstatus_scores,
            (select avg(sh.hour_testscore) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and  {$hourwhere}) as hour_test_scores,
             (select count(sh.student_id) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and hour_bookcheck =1 and  {$hourwhere}) as  hour_bookcheck_num,
            (select count(sh.student_id) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where  h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and {$hourwhere}) as  hour_bookcheck_allnum,
             (select count(sh.student_id) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where  h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and sh.hour_homework =1 and {$hourwhere}) as  hour_homework_num,
               (select count(sh.student_id) from eas_student_hour as sh,smc_class_hour as h,smc_course as co where  h.class_id=sh.class_id and co.course_id=h.course_id and concat(co.course_branch,'_',h.hour_lessontimes)=sh.hour_branch and sh.class_id=sd.class_id and sh.student_id =sd.student_id and (sh.hour_homework =1 or sh.hour_homework =-1 ) and {$hourwhere}) as  hour_homework_allnum
            from smc_student_study AS sd
            left join smc_class as  cl ON cl.class_id=sd.class_id
            left join smc_student as s ON s.student_id= sd.student_id
            where {$datawhere} and s.student_id > 0 and sd.study_beginday<= '{$request['endtime']}' and
            ((sd.class_id ='{$request['class_id']}' and sd.study_isreading ='1' and  sd.study_beginday <='{$hour_day}') or (sd.class_id='{$request['class_id']}' and sd.study_endday >='{$hour_day}' and sd.study_beginday <='{$hour_day}'))
            limit {$pagestart},{$num}";
        }
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$dataOne) {
                $params = array();
                $params['student_branch'] = $dataOne['student_branch'];
                $params['class_branch'] = $dataOne['class_branch'];
                if ($request['hour_id']) {
                    $params['hour_branch'] = $hourOne['hour_branch'];
                }
                $params['starttime'] = $request['start_time'];
                $params['endtime'] = $request['end_time'];


                $schoolRate = request_by_curl("https://stuapi.kidcastle.cn/Api/getStuClassTaskByHour", dataEncode($params), "GET");
                if ($schoolRate) {
                    $app_schoolRate = json_decode($schoolRate, true);
                    if (!$app_schoolRate['result'] || !is_array($app_schoolRate['result'])) {
                        $app_schoolRateList = array();
                    } else {
                        $app_schoolRateList = $app_schoolRate['result'][0];
                    }
                }
                $dataOne['examine_score'] = $app_schoolRateList['examine_score'];

                $dataOne['taskitem_score'] = $app_schoolRateList['taskitem_score'];
                $dataOne['up_taskitem_rate'] = $app_schoolRateList['up_taskitem_rate'];
                //     $dataOne['under_taskitem_rate'] = $app_schoolRateList['under_taskitem_rate'];
                $dataOne['under_taskitem_rate'] = $dataOne['hour_homework_allnum'] > 0 ? round($dataOne['hour_homework_num'] / $dataOne['hour_homework_allnum'], 4) * 100 : 0;
                $dataOne['video_taskitem_rate'] = $app_schoolRateList['video_taskitem_rate'];
                $dataOne['taskitem_status_name'] = $app_schoolRateList['taskitem_status_name'];
                $dataOne['classhour_memberstar'] = $app_schoolRateList['classhour_memberstar'] + 0;
                $dataOne['avg_audio_taskitem_score'] = $app_schoolRateList['avg_audio_taskitem_score'] + 0;
                $dataOne['test_taskitem_rate'] = $app_schoolRateList['test_taskitem_rate'];
                $dataOne['avg_test_score'] = $app_schoolRateList['avg_test_score'];
                $dataOne['textbook_review_rate'] = $app_schoolRateList['textbook_review_rate'];
                $dataOne['hour_homework_name'] = $dataOne['hour_homework'] == '' ? '--' : $arr_hour_homework[$dataOne['hour_homework']];
                $dataOne['hour_netstatus_name'] = $dataOne['hour_netstatus'] != false ? $arr_hour_netstatus[$dataOne['hour_netstatus']] : '--';
                $dataOne['hour_netstatus_rate'] = $dataOne['hour_netstatus_allnum'] > 0 ? round($dataOne['hour_netstatus_num'] / $dataOne['hour_netstatus_allnum'], 4) * 100 : 0;
                $dataOne['hour_bookcheck_rate'] = $dataOne['hour_bookcheck_allnum'] > 0 ? round($dataOne['hour_bookcheck_num'] / $dataOne['hour_bookcheck_allnum'], 4) * 100 : 0;
                $dataOne['hour_netstatus_scores'] = $dataOne['hour_netstatus_scores'] == false ? '0' : round($dataOne['hour_netstatus_scores'], 4);
                $dataOne['hour_test_scores'] = $dataOne['hour_test_scores'] == false ? '0' : round($dataOne['hour_test_scores'], 4);
                $dataOne['review_status_name'] = $dataOne['hour_bookcheck'] == '' ? '--' : $arr_bookcheck[$dataOne['hour_bookcheck']];
            }
        }
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$value) {
                $value['hour_netscores'] = intval($value['hour_netscores']);
                $value['hour_testscore'] = intval($value['hour_testscore']);
                $value['hour_homeworkscores'] = intval($value['hour_homeworkscores']);
                $value['classhour_memberstar'] = round($value['classhour_memberstar'], 1);
            }
        }

        if (isset($request['is_export']) && $request['is_export'] !== '') {
            $outexceldate = array();
            if ($dataList) {
                $outexceldate = array();
                foreach ($dataList as $dateexcelvar) {
                    if ($request['hour_id']) {
                        $datearray = array();
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];
                        $datearray['student_sex'] = $dateexcelvar['student_sex'];
                        $datearray['up_taskitem_rate'] = $dateexcelvar['up_taskitem_rate'];
                        $datearray['taskitem_status_name'] = $dateexcelvar['taskitem_status_name'];
                        $datearray['examine_score'] = $dateexcelvar['examine_score'];
                        $datearray['review_status_name'] = $dateexcelvar['review_status_name'];
                        $datearray['taskitem_score'] = $dateexcelvar['taskitem_score'];
                        $datearray['hour_netstatus_name'] = $dateexcelvar['hour_netstatus_name'];
                        $datearray['hour_netscores'] = $dateexcelvar['hour_netscores'];
                        $datearray['classhour_memberstar'] = $dateexcelvar['classhour_memberstar'];
                        $datearray['hour_homework_name'] = $dateexcelvar['hour_homework_name'];
                        $outexceldate[] = $datearray;
                    } else {
                        $datearray = array();
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];
                        $datearray['student_sex'] = $dateexcelvar['student_sex'];
                        $datearray['up_taskitem_rate'] = $dateexcelvar['up_taskitem_rate'];
                        $datearray['video_taskitem_rate'] = $dateexcelvar['video_taskitem_rate'];
                        $datearray['avg_test_score'] = $dateexcelvar['avg_test_score'];
                        $datearray['hour_bookcheck_rate'] = $dateexcelvar['hour_bookcheck_rate'];
                        $datearray['avg_audio_taskitem_score'] = $dateexcelvar['avg_audio_taskitem_score'];
                        $datearray['hour_netstatus_rate'] = $dateexcelvar['hour_netstatus_rate'];
                        $datearray['hour_netstatus_scores'] = $dateexcelvar['hour_netstatus_scores'];
                        $datearray['hour_test_scores'] = $dateexcelvar['hour_test_scores'];
                        $datearray['classhour_memberstar'] = $dateexcelvar['classhour_memberstar'];
                        $datearray['under_taskitem_rate'] = $dateexcelvar['under_taskitem_rate'];
                        $outexceldate[] = $datearray;
                    }
                }
            }
            if ($request['hour_id']) {
                $excelheader = array('学员中文名', '学员英文名', '学员编号', '性别', '线上任务', '录播任务', '线上检测分数', 'mini book检核状况', '配音平均分数', '网课参与状况', '网课检测分数', '家长满意度', '线下作业完成状况', '线下成绩平局分数');
                $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'student_sex', 'up_taskitem_rate', 'taskitem_status_name', 'examine_score', 'review_status_name', 'taskitem_score', 'hour_netstatus_name', 'hour_netscores', 'classhour_memberstar', 'hour_homework_name', 'hour_test_scores');
            } else {
                $excelheader = array('学员中文名', '学员英文名', '学员编号', "性别", "线上任务平均完成率", '录播任务平均完成率', '线上检测平均分数', 'mini book平均完成率', '配音平均分数', '网课平均参与率', '网课检测平均分数', '家长平均满意度', '线下作业平均任务完成率', '线下成绩平局分数');
                $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'student_sex', 'up_taskitem_rate', 'video_taskitem_rate', 'avg_test_score', 'hour_bookcheck_rate', 'avg_audio_taskitem_score', 'hour_netstatus_rate', 'hour_netstatus_scores', 'classhour_memberstar', 'under_taskitem_rate', 'hour_test_scores');
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $info['school_cnname'] . $info['class_cnname'] . $hourOne['hour_name'] . "检核状况.xlsx");
        }

        if (isset($request['is_count']) && $request['is_count']) {
            if ($request['hour_id']) {
                $allnum = $this->DataControl->selectOne("select count(sd.study_id) as all_num from smc_student_study AS sd
            left join smc_class as cl ON cl.class_id=sd.class_id
            left join smc_student as s ON s.student_id= sd.student_id
            left join eas_student_hour as sh ON sh.student_id = sd.student_id and sh.class_id = sd.class_id and sh.hour_branch = '{$hourOne['hour_branch']}'
            left join smc_student_hourstudy as hy On hy.class_id=cl.class_id and sd.student_id=hy.student_id 
            where {$datawhere} and s.student_id > 0 and hy.hour_id='{$request['hour_id']}'");
            } else {
                $allnum = $this->DataControl->selectOne("
                select count(s.student_id)  as all_num 
                from smc_student_study AS sd
                left join smc_student as s ON s.student_id= sd.student_id
                left join eas_student_hour as sh ON sh.student_id = sd.student_id and sh.class_id = sd.class_id and sh.hour_branch = '{$hourOne['hour_branch']}'
                where {$datawhere} and (sd.class_id ='{$request['class_id']}' and sd.study_isreading ='1' and  sd.study_beginday <='{$hour_day}') or (sd.class_id='{$request['class_id']}' and sd.study_endday >'{$hour_day}' and sd.study_beginday <='{$hour_day}')
            
            ");
            }
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $dataList;

        return $data;
    }

    /**
     * 班级统计报表 - 电访记录
     * author: ling
     * 对应接口文档 0001
     */
    function getStuCatitrack($paramArray)
    {
        $info = $this->DataControl->selectOne(
            "select cs.class_cnname,cs.class_enname,sl.school_shortname as school_cnname,
                  (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=cs.class_id ) as hour_allnum,
                  (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=cs.class_id and ch.hour_ischecking =1) as hour_finishnum
                  from smc_class as cs 
                  left join smc_school as sl ON sl.school_id = cs.school_id
                  where  cs.class_id='{$paramArray['class_id']}' limit 0,1");

        $data['info'] = $info == array() ? array() : $info;

        $datawhere = "c.class_id='{$paramArray['class_id']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= "  and c.track_day >= '{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= "  and c.track_day <= '{$paramArray['endtime']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            select s.student_cnname,s.student_enname,s.student_branch,s.student_sex,c.track_day,b.object_name,c.track_note,sf.staffer_cnname,FROM_UNIXTIME(c.track_createtime) as track_createtime
            from smc_student_track as c 
            left join smc_student as s On s.student_id = c.student_id
            left join smc_student_study as ss ON s.student_id = ss.student_id AND ss.class_id = c.class_id
            left join crm_code_object as b ON b.object_code = c.track_code
            left join smc_staffer as sf ON sf.staffer_id =c.staffer_id
            where {$datawhere} ";


        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['track_day'] = $dateexcelvar['track_day'];
                    $datearray['object_name'] = $dateexcelvar['object_name'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("学员中文名", "学员英文名", "学员编号", "性别", "电访日期", "沟通对象", "沟通内容", "提交人", "提交时间");
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'student_sex', 'track_day', 'object_name', 'track_note', 'staffer_cnname', 'track_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $info['school_cnname'] . $info['class_cnname'] . "学员电访记录.xlsx");
            exit;
        } else {
            $sql .= " Limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
        }

        if (!$dataList) {
            $dataList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("
                select count(c.track_id)  as all_num  
                from smc_student_track as c 
                left join smc_student as s On s.student_id = c.student_id
                left join smc_student_study as ss ON s.student_id = ss.student_id AND ss.class_id = c.class_id
                left join crm_code_object as b ON b.object_code = c.track_code
                left join smc_staffer as sf ON sf.staffer_id =c.staffer_id
                where {$datawhere}  
            ");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }
        $data['list'] = $dataList;

        return $data;
    }

    /**
     * 班级统计报表 - 考试成绩统计
     * author: wgh
     * 对应接口文档 0001
     */
    function getExamScoreStatis($paramArray)
    {
        $info = $this->DataControl->selectOne(
            "select cs.class_cnname,cs.class_enname,sl.school_shortname as school_cnname,
                 (select count(h.hour_id)
                    from smc_class_hour as h
                    left join smc_class as sc on sc.class_id = h.class_id
                    left join smc_course as co ON co.course_id = sc.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = cs.class_id and h.hour_iswarming = 0 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                 ) as hour_allnum,
                 (select count(h.hour_id)
                    from smc_class_hour as h
                    left join smc_class as sc on sc.class_id = h.class_id
                    left join smc_course as co ON co.course_id = sc.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = cs.class_id and hour_ischecking = 1 and h.hour_iswarming = 0 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                 ) as hour_finishnum
                 from smc_class as cs 
                 left join smc_school as sl ON sl.school_id = cs.school_id
                 where  cs.class_id='{$paramArray['class_id']}' limit 0,1");

        $data['info'] = $info == array() ? array() : $info;

        $datawhere = "sd.class_id = '{$paramArray['class_id']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= "  and c.catitrack_day >= '{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= "  and c.catitrack_day <= '{$paramArray['endtime']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,sd.class_id,sd.study_beginday
                from smc_student_study as sd
                left join smc_student as s On s.student_id = sd.student_id
                where {$datawhere}";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $hour_list = $this->DataControl->selectClear("select h.hour_name,h.hour_lessontimes,sh.hour_testscore,sh.hour_netscores
                    from smc_student_hourstudy as hs 
                    left join smc_class AS c on c.class_id = hs.class_id
                    left join smc_course AS co ON  co.course_id = c.course_id
                    left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                    left join smc_class_hour AS h ON  h.hour_id = hs.hour_id
                    left join eas_student_hour as sh ON sh.student_id = hs.student_id and sh.class_id = hs.class_id and sh.hour_branch = concat(co.course_branch,'_',h.hour_lessontimes) 
                    left join eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where hs.class_id = '{$dateexcelvar['class_id']}' AND hs.student_id = '{$dateexcelvar['student_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and cl.classcode_isregister = 1 and th.teachhour_isregister = 1 and th.teachhour_isscore = 1 and th.teachhour_scoretype <> ''");
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    if ($hour_list) {
                        foreach ($hour_list as $item) {
                            $datearray['hour_testscore' . $item['hour_lessontimes']] = $item['hour_testscore'] ? $item['hour_testscore'] : '--';
                            $datearray['hour_netscores' . $item['hour_lessontimes']] = $item['hour_netscores'] ? $item['hour_netscores'] : '--';
                        }
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $hourList = $this->DataControl->selectClear("
            SELECT h.hour_name,h.hour_lessontimes,th.teachhour_isscore,th.teachhour_scoretype,th.teachhour_isonscore,th.teachhour_onscoretype
            FROM smc_class_hour AS h
            LEFT JOIN smc_class AS c on c.class_id = h.class_id
            LEFT JOIN smc_course AS co ON  co.course_id = c.course_id 
            LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
            LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
            WHERE c.class_id = '{$paramArray['class_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and cl.classcode_isregister = 1 and th.teachhour_isregister = 1");

            $excelheader = array('学员中文名', '学员英文名', '学员编号', '性别', '入班日期');
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'student_sex', 'study_beginday');
            if ($hourList) {
                foreach ($hourList as $value) {
                    if ($value['teachhour_isscore'] == 1 && $value['teachhour_scoretype']) {
                        $name = $value['hour_name'] . ' ' . $value['teachhour_scoretype'] . '分数';
                        array_push($excelheader, $name);
                        $string = 'hour_testscore' . $value['hour_lessontimes'];
                        array_push($excelfileds, $string);
                    }
                    if ($value['teachhour_isonscore'] == 1 && $value['teachhour_onscoretype']) {
                        $name = $value['hour_name'] . ' ' . $value['teachhour_onscoretype'] . '分数';
                        array_push($excelheader, $name);
                        $string = 'hour_netscores' . $value['hour_lessontimes'];
                        array_push($excelfileds, $string);
                    }
                }
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $info['school_cnname'] . $info['class_cnname'] . "考试成绩统计.xlsx");
            exit;
        } else {
            $sql .= " Limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
        }

        if ($dataList) {
            foreach ($dataList as &$value) {
                $hour_list = $this->DataControl->selectClear("select h.hour_lessontimes,sh.hour_testscore,sh.hour_netscores
                from smc_student_hourstudy as hs 
                left join smc_class AS c on c.class_id = hs.class_id
                left join smc_course AS co ON  co.course_id = c.course_id
                left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                left join smc_class_hour AS h ON  h.hour_id = hs.hour_id
                left join eas_student_hour as sh ON sh.student_id = hs.student_id and sh.class_id = hs.class_id and sh.hour_branch = concat(co.course_branch,'_',h.hour_lessontimes) 
                left join eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                where hs.class_id = '{$value['class_id']}' AND hs.student_id = '{$value['student_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and cl.classcode_isregister = 1 and th.teachhour_isregister = 1 and th.teachhour_isscore = 1 and th.teachhour_scoretype <> ''");
                if ($hour_list) {
                    foreach ($hour_list as $item) {
                        $value['hour_testscore' . $item['hour_lessontimes']] = $item['hour_testscore'] ? $item['hour_testscore'] : '--';
                        $value['hour_netscores' . $item['hour_lessontimes']] = $item['hour_netscores'] ? $item['hour_netscores'] : '--';
                    }
                }
            }
        } else {
            $dataList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("
                select count(sd.student_id)  as all_num  
                from smc_student_study as sd
                left join smc_student as s On s.student_id = sd.student_id
                where {$datawhere}  
            ");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }
        $data['list'] = $dataList;

        return $data;
    }


    /**
     * 教师统计
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return mixed
     */
    function schoolTeaacher($paramArray)
    {
        $datawhere = " cl.company_id='{$paramArray['company_id']}' and cl.class_type = '0' and ec.classcode_isregister = '1' and sf.staffer_leave =  0 and ( cl.class_status = '0' OR cl.class_status = '1' ) ";// and htg.teach_type = '0' and htg.teach_status = '0'

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sf.staffer_cnname like '%{$paramArray['keyword']}%' or sf.staffer_enname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '%{$paramArray['keyword']}%' )";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " and cl.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '' && isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= "  and ((cl.class_stdate >= '{$paramArray['starttime']}' and cl.class_stdate <= '{$paramArray['endtime']}')
                        or (cl.class_enddate >= '{$paramArray['starttime']}' and cl.class_enddate <= '{$paramArray['endtime']}')
                        or (cl.class_stdate >= '{$paramArray['starttime']}' and cl.class_enddate <= '{$paramArray['endtime']}')
                        or (cl.class_stdate <= '{$paramArray['starttime']}' and cl.class_enddate >= '{$paramArray['endtime']}'))";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and htg.staffer_id = '{$paramArray['staffer_id']}' ";
            }
        }

        $sql = "select s.school_shortname as school_cnname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,staffer_branch,sf.staffer_id,s.school_id,count(cl.class_id) as hour_num,
            (select count(DISTINCT ht.class_id) 
                from smc_class_teach as ht 
                left join smc_class as sc on sc.class_id = ht.class_id
                left join smc_school as ss on ss.school_id = sc.school_id
                left join smc_course as co ON co.course_id = sc.course_id
                left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                where ht.staffer_id = sf.staffer_id and ss.school_id=cl.school_id and sc.class_status = '1' and sc.class_type = '0' and ec.classcode_isregister = '1' and ht.teach_type = '0' and ht.teach_status = '0'
            )  as teaching_num
            from smc_class_teach as htg
            left join smc_class as cl On htg.class_id = cl.class_id
            left join smc_staffer as sf ON sf.staffer_id = htg.staffer_id
            left join smc_school as s ON s.school_id = cl.school_id
            left join smc_course as co ON co.course_id = cl.course_id
            left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
            where {$datawhere}   and sf.staffer_id > 0 group by s.school_id,sf.staffer_id
             Limit {$pagestart},{$num}
        ";
        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count']) {
            $allnum = $this->DataControl->selectOne("select count(q.school_id) as all_num from
            ( select s.school_id
            from smc_class_teach as htg
            left join smc_class as cl On htg.class_id = cl.class_id
            left join smc_staffer as sf ON sf.staffer_id = htg.staffer_id
            left join smc_school as s ON s.school_id = cl.school_id
            left join smc_course as co ON co.course_id = cl.course_id
            left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
            where {$datawhere} and sf.staffer_id > 0 group by s.school_id,sf.staffer_id) as q
            ");
            if ($allnum) {
                $data['allnum'] = $allnum['all_num'];
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }
        $data['list'] = $dataList;

        return $data;

    }

    /**
     * 教务工作报表
     * author: ling
     * 对应接口文档 0001
     */
    function getEasWorkReport($paramArray)
    {
        $datawhere = " c.company_id='{$paramArray['company_id']}' and c.class_status = '1' and c.class_type = '0' and cl.classcode_isregister =1 and ct.teach_type = '0' and ct.teach_status = '0'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%' )";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= " and c.course_id='{$paramArray['course_id']}'";
        }
        //补充了班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= " and co.coursetype_id='{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['re_school_id']) && $paramArray['re_school_id'] !== '') {
            $datawhere .= " and c.school_id='{$paramArray['re_school_id']}'";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '' && isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= " and ((c.class_stdate >= '{$paramArray['starttime']}' and c.class_stdate <= '{$paramArray['endtime']}') or (c.class_enddate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate >= '{$paramArray['starttime']}' and c.class_enddate <= '{$paramArray['endtime']}') or (c.class_stdate <= '{$paramArray['starttime']}' and c.class_enddate >= '{$paramArray['endtime']}'))";
        }

        $Having = "1=1";
        $teachwhere = '1';
        if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
            $teachwhere = "sr.staffer_id ='{$paramArray['re_staffer_id']}'";
            $Having .= " and (mian_staffer_cnname is not NUll)  ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and ct.staffer_id = '{$paramArray['staffer_id']}' ";
            }
        }

        $sql = "select c.class_id,c.class_cnname,c.class_enname,c.class_branch,
                (select DISTINCT GROUP_CONCAT(sr.staffer_cnname) from smc_class_teach as th left join smc_staffer as sr on sr.staffer_id=th.staffer_id where th.class_id = c.class_id and th.teach_type = 0 and th.teach_status = 0 and {$teachwhere} limit 1) as mian_staffer_cnname,
                (select DISTINCT GROUP_CONCAT(sr.staffer_enname) from smc_class_teach as th left join smc_staffer as sr on sr.staffer_id=th.staffer_id where th.class_id = c.class_id and th.teach_type = 0 and th.teach_status = 0 and sr.staffer_enname <> '' and {$teachwhere} limit 1) as mian_staffer_enname
                from smc_class_teach as ct 
	            left join smc_class_hour_teaching AS htg on ct.class_id = htg.class_id
                left join smc_class as c On htg.class_id = c.class_id
                left join smc_course as co On co.course_id = c.course_id
                left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                where {$datawhere}
                group by c.class_id
                having {$Having}
                limit {$pagestart},{$num}
               ";


        $classnum = $this->DataControl->selectOne("select count(q.class_id) as all_num from (select c.class_id,
            (select DISTINCT GROUP_CONCAT(sr.staffer_cnname,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END ) ) from smc_class_teach as th,smc_staffer as sr  where th.staffer_id = sr.staffer_id and th.class_id = c.class_id and th.teach_type = 0 and th.teach_status = 0 and {$teachwhere} limit 1) as mian_staffer_cnname,
            (select DISTINCT GROUP_CONCAT(sr.staffer_cnname,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END ) ) from smc_class_teach as th,smc_staffer as sr  where th.staffer_id = sr.staffer_id and th.class_id = c.class_id and th.teach_type = 1 and th.teach_status = 0 and sr.staffer_enname <> '' and {$teachwhere} limit 1) as fu_staffer_cnname
            from smc_class_teach as ct 
	        left join smc_class_hour_teaching AS htg on ct.class_id = htg.class_id
            left join smc_class as c On htg.class_id = c.class_id
            left join smc_course as co On co.course_id = c.course_id
            left join eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id 
            where {$datawhere} group by c.class_id having {$Having}) as q");
        if (!$classnum) {
            $all_num = 0;
        } else {
            $all_num = $classnum['all_num'];
        }
        $teachClassList = $this->DataControl->selectClear($sql);
        if ($teachClassList) {
            foreach ($teachClassList as $key => &$classOne) {
                $hour_ckeckingnum = $this->DataControl->selectOne("select count(h.hour_id) as num
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = '{$classOne['class_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and CONCAT(h.hour_day,' ',h.hour_endtime) <= NOW() and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                    LIMIT 1 ");
                $hour_diffnum = $this->DataControl->selectOne("select count(h.hour_id) as num
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = '{$classOne['class_id']}' and h.hour_iswarming = 0 and h.hour_ischecking = 1 and CONCAT(h.hour_day,' ',h.hour_endtime) <= NOW() and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                    AND h.hour_lessontimes NOT IN ( SELECT substring_index( sh.hour_branch, '_', '-1' ) FROM eas_student_hour AS sh WHERE sh.class_id = h.class_id)
                    LIMIT 1 ");
                $hour_ckeckingallnum = $this->DataControl->selectOne("select count(h.hour_id) as num
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = '{$classOne['class_id']}'  and  h.hour_iswarming = 0 and h.hour_ischecking <> -1 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                    LIMIT 1 ");
                $hour_branch_num = $this->DataControl->selectOne("SELECT count( DISTINCT sh.hour_branch ) as num
                    FROM smc_class_hour AS h
                    LEFT JOIN smc_course AS co ON co.course_id = h.course_id
                    LEFT JOIN eas_classcode AS ec ON ec.classcode_branch = co.course_branch AND ec.company_id = co.company_id
                    LEFT JOIN eas_teachhour AS th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) 
                    LEFT JOIN eas_student_hour AS sh ON sh.class_id = h.class_id and substring_index(sh.hour_branch,'_','-1')=h.hour_lessontimes
                    WHERE h.class_id = '{$classOne['class_id']}' AND h.hour_iswarming = 0 and h.hour_ischecking = 1 AND CONCAT( h.hour_day, ' ', h.hour_endtime ) <= NOW() AND ec.classcode_isregister = '1' AND th.teachhour_isregister = '1' 
                    LIMIT 1 ");
                $hour_list = $this->DataControl->selectOne("SELECT sh.hour_name,sh.hour_updatatime,sh.hour_createtime
                    FROM smc_class_hour AS h
                    LEFT JOIN smc_course AS co ON co.course_id = h.course_id
                    LEFT JOIN eas_classcode AS ec ON ec.classcode_branch = co.course_branch AND ec.company_id = co.company_id
                    LEFT JOIN eas_teachhour AS th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                    LEFT JOIN eas_student_hour AS sh ON sh.class_id = h.class_id AND substring_index( sh.hour_branch, '_', '-1' ) = h.hour_lessontimes 
                    WHERE h.class_id = '{$classOne['class_id']}' AND h.hour_iswarming = 0 AND h.hour_ischecking = 1 AND CONCAT( h.hour_day, ' ', h.hour_endtime ) <= NOW() AND ec.classcode_isregister = '1' AND th.teachhour_isregister = '1' 
                    ORDER BY sh.hour_createtime DESC 
                    LIMIT 1 ");
                $hour_day = $this->DataControl->selectOne("SELECT h.hour_day
                    FROM smc_class_hour AS h
                    LEFT JOIN smc_course AS co ON co.course_id = h.course_id
                    LEFT JOIN eas_classcode AS ec ON ec.classcode_branch = co.course_branch AND ec.company_id = co.company_id
                    LEFT JOIN eas_teachhour AS th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                    WHERE h.class_id = '{$classOne['class_id']}' AND h.hour_iswarming = 0 AND h.hour_ischecking = 1 AND CONCAT( h.hour_day, ' ', h.hour_endtime ) <= NOW() AND ec.classcode_isregister = '1' AND th.teachhour_isregister = '1' 
                    ORDER BY h.hour_day DESC 
                    LIMIT 1 ");
                if ($hour_list['hour_name']) {
                    $classOne['hour_name'] = $hour_list['hour_name'];
                    if ($hour_list['hour_updatatime']) {
                        $classOne['hour_updatatime'] = date("Y-m-d", $hour_list['hour_updatatime']);
                    } else {
                        $classOne['hour_updatatime'] = date("Y-m-d", $hour_list['hour_createtime']);
                    }
                } else {
                    $classOne['hour_name'] = '';
                    $classOne['hour_updatatime'] = '--';
                }
                $classOne['hour_day'] = $hour_day['hour_day'] ? $hour_day['hour_day'] : '--';
                $classOne['hour_num'] = $hour_ckeckingnum['num'] . '/' . $hour_ckeckingallnum['num'];
                $classOne['hour_diffnum'] = $hour_diffnum['num'];
                $classOne['hour_branch_num'] = $hour_branch_num['num'];
            }
        } else {
            $teachClassList = array();
        }
        $data = array();
        $data['allnum'] = $all_num;
        $data['list'] = $teachClassList;
        return $data;
    }


    /**
     * 班级教务明细
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     */
    function getEasWorkOne($paramArray)
    {
        $sql = "
                select c.class_id,c.class_cnname,c.class_enname,c.class_branch,l.school_cnname,
                (select DISTINCT concat(sr.staffer_cnname,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END ) ) from smc_class_teach as th left join smc_staffer as sr on sr.staffer_id=th.staffer_id where th.class_id = c.class_id and th.teach_type = 0 and th.teach_status = 0 limit 1) as mian_staffer_cnname,
                (select DISTINCT concat(sr.staffer_cnname,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END ) ) from smc_class_teach as th left join smc_staffer as sr on sr.staffer_id=th.staffer_id where th.class_id = c.class_id and th.teach_type = 1 and th.teach_status = 0 limit 1) as fu_staffer_cnname,
                (select count(h.hour_id)
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = c.class_id and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and CONCAT(h.hour_day,' ',h.hour_endtime) <= NOW() and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                )  as  hour_ckeckingnum,
                (select count(h.hour_id)
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = c.class_id and h.hour_iswarming = 0 and h.hour_ischecking = 1 and CONCAT(h.hour_day,' ',h.hour_endtime) <= NOW() and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                    AND h.hour_lessontimes NOT IN ( SELECT substring_index( sh.hour_branch, '_', '-1' ) FROM eas_student_hour AS sh WHERE sh.class_id = h.class_id)
                )  as  hour_diffnum,
                (select count(h.hour_id)
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id = c.class_id and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1'
                )  as  hour_ckeckingallnum,
                ( SELECT count( DISTINCT sh.hour_branch ) 
                FROM smc_class_hour AS h
                LEFT JOIN smc_course AS co ON co.course_id = h.course_id
                LEFT JOIN eas_classcode AS ec ON ec.classcode_branch = co.course_branch AND ec.company_id = co.company_id
                LEFT JOIN eas_teachhour AS th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) 
                LEFT JOIN eas_student_hour AS sh ON sh.class_id = h.class_id and substring_index(sh.hour_branch,'_','-1')=h.hour_lessontimes
                WHERE h.class_id = c.class_id AND h.hour_iswarming = 0 and h.hour_ischecking = 1 AND CONCAT( h.hour_day, ' ', h.hour_endtime ) <= NOW() AND ec.classcode_isregister = '1' AND th.teachhour_isregister = '1' 
                ) AS hour_branch_num
                from smc_class as c 
                left join smc_school as l ON c.school_id = l.school_id
                where c.class_id ='{$paramArray['class_id']}'";
        $classOne = $this->DataControl->selectOne($sql);
        if (!$classOne) {
            $classOne = array();
        }

        $data = array();
        $data['info'] = $classOne;
        $datawhere = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (h.hour_name like '%{$paramArray['keyword']}%'  )";
        }
        if (isset($paramArray['hour_ischecking']) && $paramArray['hour_ischecking'] !== '') {
            $today = date("Y-m-d");
            if ($paramArray['hour_ischecking'] == 1) {
                $datawhere .= " and h.hour_ischecking='{$paramArray['hour_ischecking']}'";
            } elseif ($paramArray['hour_ischecking'] == 0) {
                $datawhere .= " and h.hour_ischecking='0' and  h.hour_day <='{$today}'";
            } elseif ($paramArray['hour_ischecking'] == 2) {
                $datawhere .= " and h.hour_ischecking='0' and  h.hour_day >'{$today}'";
            }
        }
        $having = '1=1';
        if (isset($paramArray['is_register']) && $paramArray['is_register'] !== '') {
            if ($paramArray['is_register'] == 0) {
                $having .= " and is_register > 0  ";
            } elseif ($paramArray['is_register'] == 1) {
                $having .= " and is_register  =  0  ";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $list_sql = "select h.hour_id,h.class_id,h.hour_name,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_ischecking,
                    (select count(sh.student_id) from  eas_student_hour as sh where sh.class_id = h.class_id  and substring_index(sh.hour_branch,'_','-1')=h.hour_lessontimes ) as is_register,
                    (select group_concat(concat(sf.staffer_cnname ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) ) from smc_class_hour_teaching as t,smc_staffer as sf where sf.staffer_id=t.staffer_id and t.hour_id=h.hour_id and t.teaching_type = '0' and  sf.staffer_id > 0) as mian_staffer_cnname,
                    (select group_concat(concat(sf.staffer_cnname ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) ) from smc_class_hour_teaching as t,smc_staffer as sf where sf.staffer_id=t.staffer_id and t.hour_id=h.hour_id and t.teaching_type = '1' and  sf.staffer_id > 0) as fu_staffer_cnname
                    from smc_class_hour as h
                    left join smc_course as co ON co.course_id = h.course_id
                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                    where h.class_id='{$paramArray['class_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1' and {$datawhere}
                    having {$having}
                    order by h.hour_lessontimes ASC
                    ";

        $hourList = $this->DataControl->selectClear($list_sql . "limit {$pagestart},{$num}");
        if ($hourList) {
            foreach ($hourList as $key => &$hourOne) {
                $hourOne['is_register_name'] = $hourOne['is_register'] > 0 ? '已登记' : '未登记';
                $hourOne['is_checking_name'] = $hourOne['hour_ischecking'] == 1 ? '已上课' : '待上课';
                $hourOne['hour_time'] = $hourOne['hour_day'] . ' ' . $hourOne['hour_starttime'] . '-' . $hourOne['hour_endtime'];
            }
        } else {
            $hourList = array();
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($list_sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['hour_name'] = $dateexcelvar['hour_name'];//课时名称
                    $datearray['hour_time'] = $dateexcelvar['hour_day'] . ' ' . $dateexcelvar['hour_starttime'] . '-' . $dateexcelvar['hour_endtime'];//上课时间
                    $datearray['mian_staffer_cnname'] = $dateexcelvar['mian_staffer_cnname'];//主教教师
                    $datearray['fu_staffer_cnname'] = $dateexcelvar['fu_staffer_cnname'];//助教教师
                    $datearray['is_checking_name'] = $dateexcelvar['hour_ischecking'] == 1 ? '已上课' : '待上课';//上课状态
                    $datearray['is_register_name'] = $dateexcelvar['is_register'] > 0 ? '已登记' : '未登记';//登记状态
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array('课时名称', '上课时间', '主教教师', '助教教师', '上课状态', '登记状态');
            $excelfileds = array('hour_name', 'hour_time', 'mian_staffer_cnname', 'fu_staffer_cnname', 'is_checking_name', 'is_register_name');

            $fielname = "{$classOne['school_cnname']}{$classOne['class_cnname']}教务工作明细";
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $allnums = $this->DataControl->selectOne("select count(q.hour_id) as num from
                                                    (select h.hour_id,
                                                    (select count(sh.student_id) from  eas_student_hour as sh where sh.class_id = h.class_id  and substring_index(sh.hour_branch,'_','-1')=h.hour_lessontimes ) as is_register
                                                    from smc_class_hour as h
                                                    left join smc_course as co ON co.course_id = h.course_id
                                                    left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
                                                    left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                    where h.class_id='{$paramArray['class_id']}' and h.hour_iswarming = 0 and h.hour_ischecking <> -1 and ec.classcode_isregister = '1' and th.teachhour_isregister = '1' and {$datawhere}
                                                    having {$having}) as q");

        $data['list'] = $hourList;
        $data['allnums'] = $allnums['num'];
        return $data;
    }


    /**
     * 获取课时检核详情
     * author: ling
     * 对应接口文档 0001
     */
    function getHourcheckOne($reqeust)
    {

        if (!$reqeust['hour_id']) {
            $this->error = '1';
            $this->errortip = '请选择课时';
        }

        $hourOne = $this->DataControl->selectOne("
            select ch.hour_lessontimes,c.course_branch ,cs.class_cnname,cs.class_enname,ch.hour_name,ch.class_id,cs.class_branch,cs.class_id
            from smc_class_hour as ch,smc_course as c ,smc_class as cs 
            where ch.hour_id='{$reqeust['hour_id']}' and c.course_id = ch.course_id and cs.class_id =ch.class_id limit 0,1
        ");

        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_branch,student_id", "student_id='{$reqeust['student_id']}'");
        $hour_branch = $hourOne['course_branch'] . '_' . $hourOne['hour_lessontimes'];


        $params = "student_branch={$studentOne['student_branch']}&class_branch={$hourOne['class_branch']}&hour_branch={$hour_branch}";

        $re_stuadata = request_by_curl("https://stuapi.kidcastle.cn/Api/getStuStudySituation", $params, "GET", array());


        $arr_stuadata = json_decode($re_stuadata, true);
        $stuadata = $arr_stuadata['result'];
        if ($stuadata) {
            if ($stuappOne = $this->DataControl->getOne("eas_stuapp_hour", "student_id='{$studentOne['student_id']}' and class_id='{$hourOne['class_id']}'and hour_branch='{$hour_branch}'")) {
                if ((time() - $stuappOne['hour_updatatime']) > 1 * 60) {
                    $stuapp_data = array();
                    $stuapp_data['classhour_comment'] = addslashes($stuadata['classhour_comment']);
                    $stuapp_data['classhour_note'] = addslashes($stuadata['classhour_note']);
                    $stuapp_data['task_allnums'] = $stuadata['taskitem_finishnum'];
                    $stuapp_data['task_finhishnums'] = $stuadata['taskitem_finishnum'];
                    $stuapp_data['task_score'] = $stuadata['examine_score'];
                    $stuapp_data['radio_allnum'] = $stuadata['radio_allnum'];
                    $stuapp_data['radio_num'] = $stuadata['radio_num'];
                    $stuapp_data['video_num'] = $stuadata['video_num'];
                    $stuapp_data['all_video_num'] = $stuadata['all_video_num'];
                    $stuapp_data['classhour_memberstar'] = $stuadata['classhour_memberstar'];
                    $stuapp_data['radio_rate'] = $stuadata['radio_allnum'] ? ($stuadata['radio_num'] / $stuadata['radio_allnum']) : 0;
                    $stuapp_data['hour_updatatime'] = time();
                    $this->DataControl->updateData('eas_stuapp_hour', "student_id='{$studentOne['student_id']}' and class_id='{$hourOne['class_id']}'and hour_branch='{$hour_branch}'", $stuapp_data);

                }

            } else {
                $stuapp_data = array();
                $stuapp_data['classhour_comment'] = addslashes($stuadata['classhour_comment']);
                $stuapp_data['student_id'] = $studentOne['student_id'];
                $stuapp_data['hour_branch'] = $hour_branch;
                $stuapp_data['class_id'] = $hourOne['class_id'];
                $stuapp_data['classhour_note'] = addslashes($stuadata['classhour_note']);
                $stuapp_data['task_allnums'] = $stuadata['taskitem_finishnum'];
                $stuapp_data['task_finhishnums'] = $stuadata['taskitem_finishnum'];
                $stuapp_data['task_score'] = $stuadata['examine_score'];
                $stuapp_data['radio_allnum'] = $stuadata['radio_allnum'];
                $stuapp_data['radio_num'] = $stuadata['radio_num'];
                $stuapp_data['video_num'] = $stuadata['video_num'];
                $stuapp_data['all_video_num'] = $stuadata['all_video_num'];
                $stuapp_data['classhour_memberstar'] = $stuadata['classhour_memberstar'];
                $stuapp_data['radio_rate'] = $stuadata['radio_allnum'] ? ($stuadata['radio_num'] / $stuadata['radio_allnum']) : 0;
                $stuapp_data['hour_updatatime'] = time();
                $stuapp_data['hour_createtime'] = time();

                $this->DataControl->insertData('eas_stuapp_hour', $stuapp_data);
            }

        }


        $studentHourOne = $this->DataControl->selectOne("
            select sh.hour_testscore,sh.hour_homeworkscores,sh.hour_netscores,sh.hour_netstatus,sh.hour_bookcheck,sh.hour_refresher
            from eas_student_hour as sh where sh.hour_branch ='{$hour_branch}' and sh.student_id ='{$reqeust['student_id']}' and class_id='{$hourOne['class_id']}' limit 0,1 ");

//       0无补课1已补课 -1 未补课',
        $arr_netstatus = array('0' => '待参与', '1' => '已参与', '-1' => '未参与');
        $arr_refresher = array('0' => '无补课', '1' => '已补课', '-1' => '未补课');
        $arr_bookcheck = array('0' => '未完成', '1' => '已完成');
        $data = array();
        $data['info']['class_cnname'] = $hourOne['class_cnname'] . '(' . $hourOne['class_enname'] . ')';
        $data['info']['hour_name'] = $hourOne['hour_name'];
        $data['info']['student_cnname'] = $studentOne['student_cnname'];

//        线下
        $data['list']['hour_testscore'] = $studentHourOne['hour_testscore'];
        $data['list']['hour_homeworkscores'] = $studentHourOne['hour_homeworkscores'];
        $data['list']['hour_netscores'] = $studentHourOne['hour_netscores'];
        $data['list']['hour_netstatus_name'] = $arr_netstatus[$studentHourOne['hour_netstatus']];
        $data['list']['hour_bookcheck_name'] = $arr_bookcheck[$studentHourOne['hour_bookcheck']];
        $data['list']['hour_refresher_name'] = $arr_refresher[$studentHourOne['hour_refresher']];

        $stuappOne = $this->DataControl->selectOne("select * from eas_stuapp_hour  as sh where sh.hour_branch ='{$hour_branch}' and sh.student_id ='{$reqeust['student_id']}' and class_id='{$hourOne['class_id']}'   ");

        //线上


        if ($stuappOne['task_allnums']) {
            $data['data']['finish_rate'] = (($stuappOne['task_finhishnums'] / $stuappOne['task_allnums']) * 100);
        } else {
            $data['data']['finish_rate'] = '0';
        }
        $data['data']['finish_num'] = intval($stuappOne['task_finhishnums']);
        $data['data']['all_num'] = intval($stuappOne['task_allnums']);
        $data['data']['video_course'] = intval($stuappOne['video_num']);
        $data['data']['check_score'] = $stuappOne['task_score'];
        $data['data']['hour_mark'] = $stuappOne['classhour_comment'];
        $data['data']['hour_member_note'] = $stuappOne['classhour_note'];
        $data['data']['radio_allnum'] = intval($stuappOne['radio_allnum']);
        $data['data']['radio_finishnum'] = intval($stuappOne['radio_num']);
        $data['data']['radio_rate'] = $stuappOne['radio_rate'];
        $data['data']['report_star'] = intval($stuappOne['classhour_memberstar']);

        return $data;
    }


    /**
     * 获取职工任职所有的校园
     * author: ling
     * 对应接口文档 0001
     */
    function getStafferAllSchool($postbe_id)
    {
        $postOne = $this->DataControl->selectOne("select  postrole_id,postpart_id from  gmc_staffer_postbe as p where p.postbe_id ='{$postbe_id}' ");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id='{$this->staffer_id}'");

        if ($stafferOne['account_class'] == 0) {
            if ($postOne['postrole_id'] == 0) {
                $postList = $this->DataControl->selectClear("select school_id from  gmc_staffer_postbe as p  where p.postbe_id ='{$postbe_id}'");

                if (!$postList) {
                    $arr_all_school = array();
                } else {
                    $arr_all_school = array_column($postList, 'school_id');
                }

//                var_dump($arr_all_school);
            } else {
                $post_sql = "select co.school_id
            from  gmc_staffer_postbe as sp 
            left join gmc_company_organizeschool as co ON co.organize_id =sp.organize_id
            where sp.postbe_id = '{$postbe_id}' and sp.school_id = '0'";
                $com_postList = $this->DataControl->selectClear($post_sql);

                if (!$com_postList) {
                    $arr_all_school = array();
                } else {
                    $arr_all_school = array_column($com_postList, 'school_id');
                }
                if (!$arr_all_school) {
                    $arr_all_school = array();
                }
                //var_dump($arr_all_school);
            }
        } else {
            $arr_school = $this->DataControl->selectClear("
                select s.school_id
                from smc_school as s 
                where s.company_id = '{$this->company_id}' 
            ");
            $arr_all_school = array_column($arr_school, 'school_id');
        }

        $str_school_id = trim(implode(',', $arr_all_school), ',');

        if (!$str_school_id) {
            $str_school_id = 0;
        }

        return $str_school_id;
    }


    /**
     * 我的班级 学员管理
     * author: wgh
     * 对应接口文档 134
     */
    function getStudentList($request)
    {
        $sql = "SELECT
                    c.class_id,c.course_id,c.class_cnname,c.class_enname,c.class_branch,c.class_stdate,c.class_enddate,co.course_cnname,co.course_branch,
                    (SELECT COUNT(cl.class_id) FROM smc_class as cl WHERE cl.father_id = c.class_id) as sub_class_num,
                    (SELECT GROUP_CONCAT(DISTINCT sf.staffer_cnname) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '0') as staffer_cnname,
                    (SELECT GROUP_CONCAT(DISTINCT sf.staffer_cnname) FROM smc_class_teach as t LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id WHERE t.class_id = c.class_id AND t.teach_type = '1') as fu_staffer_cnname,
                    (SELECT GROUP_CONCAT(DISTINCT cr.classroom_cnname) FROM smc_classroom as cr LEFT JOIN smc_class_hour as h ON cr.classroom_id = h.classroom_id AND cr.classroom_status = '1' WHERE h.class_id = c.class_id) as classroom_cnname
                FROM
                    smc_class as c
                LEFT JOIN
                    smc_course as co ON co.course_id = c.course_id
                WHERE
                    c.class_id = '{$request['class_id']}'";
        $classOne = $this->DataControl->selectOne($sql);

        $datawhere = "ss.company_id = '{$request['company_id']}' and ss.school_id = '{$request['school_id']}' and ss.class_id = '{$request['class_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }
        $having = "1=1";
        if (isset($request['student_status']) && $request['student_status'] == '1') {
            $having .= " and fu_class_cnname IS NOT NULL";
        } elseif (isset($request['student_status']) && $request['student_status'] == '0') {
            $having .= " and fu_class_cnname IS NULL";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sqls = "SELECT
                      s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,ss.study_beginday,scb.coursebalance_time,
                      (SELECT sf.family_mobile FROM smc_student_family AS sf WHERE sf.student_id = s.student_id AND sf.family_isdefault = 1 LIMIT 1) as family_mobile,
                      (SELECT cl.class_id FROM smc_student_study AS sd LEFT JOIN smc_class as cl ON cl.class_id = sd.class_id AND cl.class_type = 1 WHERE sd.student_id = ss.student_id AND cl.father_id = ss.class_id AND sd.study_isreading = '1' LIMIT 1) as to_class_id,
                      (SELECT cl.class_cnname FROM smc_student_study AS sd LEFT JOIN smc_class as cl ON cl.class_id = sd.class_id AND cl.class_type = 1 WHERE sd.student_id = ss.student_id AND cl.father_id = ss.class_id AND sd.study_isreading = '1' LIMIT 1) as fu_class_cnname
                  FROM
                      smc_student_study as ss
                  LEFT JOIN
                      smc_student as s ON s.student_id = ss.student_id
                  LEFT JOIN
                      smc_class as c ON c.class_id = ss.class_id
                  LEFT JOIN
                      smc_student_coursebalance as scb ON scb.student_id = ss.student_id AND scb.course_id = c.course_id AND scb.school_id = ss.school_id
                  WHERE
                      {$datawhere} AND ss.study_isreading = '1'
                  HAVING {$having}
                  LIMIT {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sqls);
        if ($dataList) {
            foreach ($dataList as &$v) {
                if ($v['fu_class_cnname']) {
                    $v['student_status'] = '已分班';
                } else {
                    $v['fu_class_cnname'] = '--';
                    $v['student_status'] = '待分班';
                }
            }
        } else {
            $dataList = array();
        }
        $data = array();

        $allnum = $this->DataControl->selectOne("SELECT
                                                        COUNT(s.student_id) as all_num,
                                                        (SELECT cl.class_cnname FROM smc_student_study AS sd LEFT JOIN smc_class as cl ON cl.class_id = sd.class_id AND cl.class_type = 1 WHERE sd.student_id = ss.student_id AND cl.father_id = ss.class_id AND sd.study_isreading = '1' LIMIT 1) as fu_class_cnname
                                                    FROM
                                                        smc_student_study as ss
                                                    LEFT JOIN
                                                        smc_student as s ON s.student_id = ss.student_id
                                                    WHERE
                                                        {$datawhere} AND ss.study_isreading = '1'
                                                    HAVING {$having}");
        if ($allnum) {
            $data['allnum'] = $allnum['all_num'];
        } else {
            $data['allnum'] = 0;
        }

        $data['classOne'] = $classOne;
        $data['list'] = $dataList;

        return $data;
    }


    /**
     * 学员管理 学员分班操作
     * author: wgh
     * 对应接口文档 136
     */
    function separateClassAction($request)
    {
        $Model = new \Model\Smc\TransactionModel($request);
        $student_list = json_decode(stripslashes($request['student_list']), true);
        if (!is_array($student_list) || count($student_list) < 1) {
            $this->error = 1;
            $this->errortip = '请选择学员';
            return false;
        }
        if (!isset($request['class_id']) || $request['class_id'] == '') {
            $this->error = 1;
            $this->errortip = '请选择分班班级';
            return false;
        }

        $sql = "SELECT
                    c.class_fullnums,co.course_limitnum,
                    (SELECT COUNT(ss.study_id) FROM smc_student_study as ss WHERE ss.class_id=c.class_id AND ss.study_isreading = 1) as num
                FROM
                    smc_class as c
                LEFT JOIN
                    smc_course as co ON co.course_id = c.course_id
                WHERE
                    c.class_id = '{$request['class_id']}'";
        $classOne = $this->DataControl->selectOne($sql);
        if ($classOne['course_limitnum'] == 1) {
            if ((count($student_list) + $classOne['num']) > $classOne['class_fullnums']) {
                $this->error = 1;
                $this->errortip = "入班人数已超过限定人数";
                return false;
            }
        }

        foreach ($student_list as $v) {
            $study_beginday = date("Y-m-d");
            $Model->entryClass($v['student_id'], '', $request['class_id'], $study_beginday);
        }

        return true;
    }


    /**
     * 学员管理 学员转班操作
     * author: wgh
     * 对应接口文档 137
     */
    function toSeparateClassAction($request)
    {
        $Model = new \Model\Smc\TransactionModel($request);
        $student_list = json_decode(stripslashes($request['student_list']), true);
        if (!is_array($student_list) || count($student_list) < 1) {
            $this->error = 1;
            $this->errortip = '请选择学员';
            return false;
        }
        if (!isset($request['to_class_id']) || $request['to_class_id'] == '') {
            $this->error = 1;
            $this->errortip = '请选择转班班级';
            return false;
        }

        foreach ($student_list as $val) {
            $Model->outClass($val['student_id'], $request['class_id'], '2');

            $Model->transferClass($val['student_id'], $request['class_id'], $request['to_class_id']);
        }

        return true;
    }

}