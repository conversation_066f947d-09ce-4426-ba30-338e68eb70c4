<?php

namespace Model\Easx;

Class TimetableModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }


    /**
     * 去上课跳转第三方地址,免密登陆
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool|string
     */
    function LineThreeTeUrl($request)
    {
        $lineroomsOne = $this->DataControl->selectOne("select l.* from smc_linerooms as l where l.hour_id='{$request['hour_id']}'");
        if (!$lineroomsOne) {

            $this->error = true;
            $this->errortip = "不存在云教室数据";
            return false;
        }
        if ($lineroomsOne['linerooms_threenumber'] == '') {
            $LineClassModel = new \Model\Smc\LineClassModel($request);
            $threenumber = $LineClassModel->CreateLineThreenumber($lineroomsOne['linerooms_id']);
            if ($threenumber) {
                $lineroomsOne['linerooms_threenumber'] = $threenumber['threenumber'];
                $lineroomsOne['linerooms_chairmanpwd'] = $threenumber['chairmanpwd'];
                $lineroomsOne['linerooms_confuserpwd'] = $threenumber['confuserpwd'];
            } else {
                $this->error = true;
                $this->errortip = $LineClassModel->errortip;
                return false;
            }
        }
        $teachOne = $this->DataControl->selectOne("select teaching_type from smc_class_hour_teaching where hour_id='{$request['hour_id']}' and staffer_id='{$this->stafferOne['staffer_id']}' ");


        $url = "https://global.talk-cloud.net/WebAPI/entry/?";
        $postdata = array();
        $postdata['domain'] = 'shjdbjm';
        $postdata['serial'] = $lineroomsOne['linerooms_threenumber'];
        $postdata['username'] = $this->stafferOne['staffer_enname'] == '' ? $this->stafferOne['staffer_cnname'] : $this->stafferOne['staffer_enname'];
        if ($teachOne['teaching_type'] == 0) {
            $postdata['usertype'] = '0';
            $postdata['userpassword'] = bin2hex(@mcrypt_encrypt(MCRYPT_RIJNDAEL_128, '4j7UDVpdHXgosq9z', $lineroomsOne['linerooms_chairmanpwd'], MCRYPT_MODE_ECB));
        } else {

            $postdata['usertype'] = '1';
            $postdata['userpassword'] = bin2hex(@mcrypt_encrypt(MCRYPT_RIJNDAEL_128, '4j7UDVpdHXgosq9z', $lineroomsOne['linerooms_assistantpwd'], MCRYPT_MODE_ECB));
        }
        $postdata['ts'] = time();
        $postdata['auth'] = MD5("4j7UDVpdHXgosq9z{$postdata['ts']}{$postdata['serial']}{$postdata['usertype']}");
        $postdata['extradata'] = "mohism";
        $postdata['jumpurl'] = "https://easxapi.kedingdang.com/";
        $postUrl = $url . dataEncode($postdata);
        return $postUrl;
    }

    function LineThreeRecordPlayback($request)
    {
        $lineOne = $this->DataControl->selectOne("select linerooms_id,linerooms_starttime,linerooms_endtime from smc_linerooms where  hour_id= '{$request['hour_id']}'");
        $hourOne = $this->DataControl->selectOne("select hour_id,hour_day from smc_class_hour where hour_id='{$request['hour_id']}' ");
        $Model = new \Model\Smc\LineClassModel();
        $starttime = strtotime($hourOne['hour_day']);
        $endtime = strtotime($hourOne['hour_day']) + 24 * 60 * 60 - 1;
        $dataList = $Model->GetLineThreeRecord($lineOne['linerooms_id'], $starttime, $endtime);
        return $dataList;
    }

    function monthList($request)
    {
        $datawhere = " 1 ";

        $starttime = strtotime($request['month'] . '-01');
        $endtime = strtotime(date('Y-m-t', $starttime));

        if (isset($request['hour_way']) && $request['hour_way'] != '') {
            $datawhere .= " and ch.hour_way='{$request['hour_way']}'";
        }
        $time1 = $starttime;
        $time2 = $endtime;
        $monarr = array();
        while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
            $monarr[] = date('Y-m-d', $time1);

            $time1 = strtotime("+1 day", $time1);
        }
        if (!$monarr) {
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }
        $tem_array = array();
        foreach ($monarr as $val) {
            $sql = "select cht.teaching_id
                  from smc_class_hour_teaching as cht
                  left join smc_class_hour as ch on ch.hour_id=cht.hour_id
                  left join smc_class as cl on cl.class_id = ch.class_id
                  left join smc_school as sc on sc.school_id = cl.school_id
                  where {$datawhere} and ch.hour_day='{$val}' and cht.staffer_id='{$request['staffer_id']}' and cht.teaching_isdel='0' and sc.school_id = '{$request['school_id']}'
                  ";

            $data = array();
            $data['year'] = date("Y", strtotime($val));
            $data['month'] = date("m", strtotime($val));
            $data['day'] = date("d", strtotime($val));
            if ($this->DataControl->selectOne($sql)) {
                $data['status'] = 1;
            } else {
                $data['status'] = 0;
            }
            $tem_array[] = $data;
        }

        return $tem_array;

    }

    function studentTimetable($request)
    {
        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['hour_way']) && $request['hour_way'] != '') {
            $datawhere .= " and ch.hour_way='{$request['hour_way']}'";
        }

        if (isset($request['hour_ischecking']) && $request['hour_ischecking'] != '') {
            $datawhere .= " and ch.hour_ischecking='{$request['hour_ischecking']}'";
        }

        if (isset($request['fixedtime']) && $request['fixedtime'] != '') {
            $datawhere .= " and ch.hour_day='{$request['fixedtime']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and ch.hour_day>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and ch.hour_day<='{$request['endtime']}'";
        }

        $sql = "select ch.hour_id,ch.hour_day,c.class_id,c.class_cnname,c.class_enname,sch.school_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_way,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,ch.hour_name,ch.hour_number,c.course_id
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hourstudy_id) from smc_student_hourstudy as ho where ho.hour_id=ch.hour_id and hourstudy_checkin = '1' ) as tureNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum,
              (SELECT count(cs.sturemark_id) FROM eas_classhour_sturemark AS cs WHERE cs.hour_id = ch.hour_id ) as alreadyNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0' and c.school_id = '{$request['school_id']}' and c.class_status > '-2' 
              GROUP BY ch.hour_id 
              order by ch.hour_day ASC,ch.hour_starttime asc ";
        $sql .= " limit {$pagestart},{$num}";

        $hourList = $this->DataControl->selectClear($sql);

        if (!$hourList) {
            $this->error = true;
            $this->errortip = "暂无课表信息";
            return false;
        }

        $status = array('0' => '未上课', '1' => '已上课', '-1' => '已取消');
        $way = array('0' => '实体课', '1' => '线上课');
        $week = array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日');

        foreach ($hourList as &$hourOne) {
            $hourOne['hour_ischecking_name'] = $status[$hourOne['hour_ischecking']];
            $hourOne['hour_way_name'] = $way[$hourOne['hour_way']];
            $hourOne['week'] = $week[date('w', strtotime($hourOne['hour_day']))];
            $hourOne['hour_evaluate'] = 1 / 20;
            $hourOne['score'] = $hourOne['commentNum'] . '/' . $hourOne['stuNum'];
            $sql = "select cc.coursetype_isname from smc_course as sc left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id where sc.course_id='{$hourOne['course_id']}'";
            $typeOne = $this->DataControl->selectOne($sql);
            if ($typeOne['coursetype_isname'] == 0) {
                $hourOne['class_cnname'] = $hourOne['class_enname'];
            }
        }

        $data = array();
        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0' and c.school_id = '{$request['school_id']}' and c.class_status > '-2'
              GROUP BY ch.hour_id 
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $hourList;
        return $data;
    }

    function isHourApi($request)
    {
        $dateList = json_decode(stripslashes($request['date']), true);
        $hourList = array();
        foreach ($dateList as $k => $item) {
            $datawhere = " 1 ";
            if (isset($request['hour_way']) && $request['hour_way'] != '') {
                $datawhere .= " and ch.hour_way='{$request['hour_way']}'";
            }
            if (isset($request['hour_ischecking']) && $request['hour_ischecking'] != '') {
                $datawhere .= " and ch.hour_ischecking='{$request['hour_ischecking']}'";
            }
            $datawhere .= " and ch.hour_day='{$item['date']}'";

            $sql = "select ch.hour_day
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0' and c.school_id = '{$request['school_id']}' and c.class_status > '-2' 
              GROUP BY ch.hour_id 
              order by ch.hour_day ASC,ch.hour_starttime asc ";

            $a = $this->DataControl->selectClear($sql);

            if ($a) {
                $hourList[$k]['status'] = '1';
            } else {
                $hourList[$k]['status'] = '0';
            }
            $hourList[$k]['hour_day'] = $item['date'];
        }
        return $hourList;

    }

    function hourItem($request)
    {

        $sql = "
            SELECT
                c.class_cnname,
                c.class_enname,
                c.course_id,
                sch.school_cnname,
                cl.classroom_cnname,
                c.class_fullnums,
                ch.hour_day,
                ch.hour_starttime,
                ch.hour_endtime,
                ch.hour_lessontimes,
                c.class_fullnums,
                ch.hour_content,
                ch.hour_ischecking,
                ch.hour_way,
                ch.hour_number,
                ch.hour_name,
                ( SELECT count( ss.student_id ) FROM smc_student_study AS ss WHERE ss.class_id = c.class_id AND ss.study_isreading = '1' ) AS stuNum,
                ( SELECT count( ho.hourstudy_id ) FROM smc_student_hourstudy AS ho WHERE ho.hour_id = ch.hour_id AND hourstudy_checkin = '1' ) AS tureNum ,
                st.staffer_cnname as staffer_assistant,
                stt.staffer_cnname as staffer_cnname
            FROM
                smc_class_hour AS ch
                LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
                LEFT JOIN smc_classroom AS cl ON cl.classroom_id = ch.classroom_id
                LEFT JOIN smc_class_hour_teaching AS ht ON ht.hour_id = ch.hour_id and ht.teaching_type = '1'
                AND ht.teaching_isdel = '0'
                LEFT JOIN smc_staffer AS st ON st.staffer_id = ht.staffer_id 	
                LEFT JOIN smc_class_hour_teaching AS htt ON htt.hour_id = ch.hour_id and htt.teaching_type = '0'
                AND htt.teaching_isdel = '0'
                LEFT JOIN smc_staffer AS stt ON stt.staffer_id = htt.staffer_id 
                LEFT JOIN smc_school AS sch ON sch.school_id = c.school_id
              where ch.hour_id='{$request['hour_id']}'
              ";
        $hourOne = $this->DataControl->selectOne($sql);

        if (!$hourOne) {
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $hourOne['staffer_cnname'] = $hourOne['staffer_cnname'] ? $hourOne['staffer_cnname'] : '暂无';
        $hourOne['staffer_assistant'] = $hourOne['staffer_assistant'] ? $hourOne['staffer_assistant'] : '暂无';

        $sql = "select cc.coursetype_isname from smc_course as sc left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id where sc.course_id='{$hourOne['course_id']}'";
        $typeOne = $this->DataControl->selectOne($sql);
        if ($typeOne['coursetype_isname'] == 0) {
            $hourOne['class_cnname'] = $hourOne['class_enname'];
        }

        $status = array('0' => '未上课', '1' => '已上课', '-1' => '已取消');
        $way = array('0' => '实体课', '1' => '线上课');

        $hourOne['hour_ischecking_name'] = $status[$hourOne['hour_ischecking']];
        $hourOne['hour_way_name'] = $way[$hourOne['hour_way']];

        return $hourOne;

    }

    function hourStuRemark($request)
    {

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_img,s.student_sex,sh.hourcomment_id,sh.hourcomment_content,sh.hourcomment_level,hourcomment_anonymous
              from eas_student_hourcomment as sh
              left join smc_student as s on s.student_id=sh.student_id
              where sh.hour_id='{$request['hour_id']}'
              order by sh.hourcomment_createtime desc
              ";
        $commentList = $this->DataControl->selectClear($sql);
        if ($commentList) {
            foreach ($commentList as &$commentVar) {
                if ($commentVar['hourcomment_anonymous'] == '1') {
                    $commentVar['student_cnname'] = '匿名';
                    $commentVar['student_enname'] = '匿名';
                    $commentVar['student_img'] = '';
                }
                $noun = $this->DataControl->selectClear("select noun_word from eas_student_hourcomment_noun_apply as a left join eas_student_hourcomment_noun as n on a.noun_id = n.noun_id where a.hourcomment_id = '{$commentVar['hourcomment_id']}'");
                $commentVar['noun'] = $noun;
            }
        }

        if (!$commentList) {
            $this->error = true;
            $this->errortip = "无学员评论";
            return false;
        }

        $data = array();
        $data['commentList'] = $commentList;

        return $commentList;
    }

    function stuMonthList($request)
    {

        $starttime = strtotime($request['month'] . '-01');
        $endtime = strtotime(date('Y-m-t', $starttime));

        $time1 = $starttime;
        $time2 = $endtime;
        $monarr = array();
        while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
            $monarr[] = date('Y-m-d', $time1);

            $time1 = strtotime("+1 day", $time1);
        }
        if (!$monarr) {
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }
        $tem_array = array();
        foreach ($monarr as $val) {
            $sql = "select ch.hour_id
                  from smc_class_hour as ch
                  left join smc_student_study as ss on ss.class_id=ch.class_id
                  where ch.hour_day='{$val}' and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
                  order by ch.hour_starttime asc
                  ";

            $data = array();
            $data['year'] = date("Y", strtotime($val));
            $data['month'] = date("m", strtotime($val));
            $data['day'] = date("d", strtotime($val));
            if ($this->DataControl->selectOne($sql)) {
                $data['status'] = 1;
            } else {
                $data['status'] = 0;
            }
            $tem_array[] = $data;
        }

        return $tem_array;

    }

    function hourContent($request)
    {
        $hourOne = $this->DataControl->selectOne("select ch.hour_content from smc_class_hour as ch where ch.hour_id='{$request['hour_id']}' limit 0,1");
        if (!$hourOne) {
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        return $hourOne;
    }

    function updateHourContent($request)
    {

        $data = array();
        $data['hour_content'] = $request['hour_content'];
        $data['hour_updatatime'] = time();
        if ($this->DataControl->updateData("smc_class_hour", "hour_id='{$request['hour_id']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据错误";
            return false;
        }

    }

    function stuTimetableList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['fixedtime']) && $request['fixedtime'] != '') {
            $datawhere .= " and ch.hour_day='{$request['fixedtime']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and ch.hour_day>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and ch.hour_day<='{$request['endtime']}'";
        }

        $sql = "select ch.hour_id,ch.hour_day,c.class_cnname,sch.school_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes
              ,st.staffer_cnname
              ,(select AVG(stu.sturemarkstar_score) from eas_classhour_sturemark as cs left join eas_classhour_sturemarkstar as stu on stu.sturemark_id=cs.sturemark_id where cs.hour_id=ch.hour_id and cs.student_id='{$request['student_id']}' limit 0,1) as sturemarkstar_score
              ,(select sh.hourcomment_score from eas_student_hourcomment as sh where sh.hour_id=ch.hour_id and sh.student_id='{$request['student_id']}' limit 0,1) as hourcomment_score
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum
              ,(select sh.hourstudy_checkin from smc_student_hourstudy as sh where sh.class_id=c.class_id and sh.hour_id=ch.hour_id and sh.student_id='{$request['student_id']}' limit 0,1) as hourstudy_checkin
              from smc_class_hour as ch
              left join smc_student_study as ss on ss.class_id=ch.class_id
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
              order by ch.hour_starttime asc ";

        $sql .= " limit {$pagestart},{$num}";

        $hourList = $this->DataControl->selectClear($sql);

        if (!$hourList) {
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $status = array('0' => '未上课', '1' => '已上课', '-1' => '已取消');
        $checkin = array('0' => '异常', '1' => '签到');
        $week = array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日');

        foreach ($hourList as &$hourOne) {
            $hourOne['hour_ischecking_name'] = $status[$hourOne['hour_ischecking']];
            if (!$hourOne['hourstudy_checkin'] && $hourOne['hourstudy_checkin'] != '0') {
                $hourOne['hourstudy_checkin_name'] = '未上课';
            } else {
                $hourOne['hourstudy_checkin_name'] = $checkin[$hourOne['hourstudy_checkin']];
            }

            $hourOne['week'] = $week[date('w', strtotime($hourOne['hour_day']))];
        }

        $data = array();
        $count_sql = "select ch.hour_id
              from smc_class_hour as ch
              left join smc_student_study as ss on ss.class_id=ch.class_id
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ss.student_id='{$request['student_id']}' and ss.study_isreading='1' and ch.hour_ischecking<>'-1'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $hourList;

        return $data;

    }


    function stuHourItem($request)
    {

        $sql = "select c.class_id,c.class_cnname,sch.school_cnname,st.staffer_cnname,cl.classroom_cnname,c.class_fullnums,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,c.class_fullnums,ch.hour_content,ch.hour_way,ch.hour_number
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              where ch.hour_id='{$request['hour_id']}'
              ";
        $hourOne = $this->DataControl->selectOne($sql);

        if (!$hourOne) {
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $status = array('0' => '待考勤', '1' => '已考勤', '-1' => '已取消');
        $way = array('0' => '实体课', '1' => '线上课');

        $hourOne['hour_ischecking_name'] = $status[$hourOne['hour_ischecking']];
        $hourOne['hour_way_name'] = $way[$hourOne['hour_way']];

        $sql = "select sr.sturemarkstar_name,sr.sturemarkstar_score
              from eas_classhour_sturemark as cs
              left join eas_classhour_sturemarkstar as sr on sr.sturemark_id=cs.sturemark_id
              where cs.student_id='{$request['student_id']}' and cs.hour_id='{$request['hour_id']}'
              ";

        $tempList = $this->DataControl->selectClear($sql);

        if (!$tempList) {
            $tempList = array();
        }

        $sql = "select cs.sturemark_comment,cs.sturemark_picturejson,stu.homeworkstu_score
              from eas_classhour_sturemark as cs
              left join eas_homeworkstu as stu on stu.hour_id=cs.hour_id and stu.student_id=cs.student_id
              where cs.student_id='{$request['student_id']}' and cs.hour_id='{$request['hour_id']}'";

        $info = $this->DataControl->selectOne($sql);

        if (!$info) {
            $info = array();
        }

        $sql = "select sh.hourcomment_content,sh.hourcomment_score
              from eas_student_hourcomment as sh
              where sh.student_id='{$request['student_id']}' and sh.hour_id='{$request['hour_id']}'";

        $stuCommentInfo = $this->DataControl->selectOne($sql);

        $score_num = $this->DataControl->selectClear("SELECT ROUND(AVG(sr.sturemarkstar_score), 1) as score_num 
                                                        FROM eas_classhour_sturemark as cs
                                                        LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                                                        WHERE cs.student_id='{$request['student_id']}' AND cs.hour_id='{$request['hour_id']}' AND cs.class_id='{$hourOne['class_id']}'");

        $score = $score_num[0]['score_num'];

        if (!$stuCommentInfo) {
            $stuCommentInfo = array();
        }

        $data = array();
        $data['hourOne'] = $hourOne;
        $data['tempList'] = $tempList;
        $data['info'] = $info;
        $data['stuCommentInfo'] = $stuCommentInfo;
        $data['score'] = $score;

        return $data;

    }

    function commentHour($request)
    {
        $data = array();
        $data['hour_id'] = $request['hour_id'];
        $data['student_id'] = $request['student_id'];
        $data['student_id'] = $request['student_id'];
        $data['hourcomment_content'] = $request['hourcomment_content'];
        $data['hourcomment_score'] = $request['hourcomment_score'];
        $data['hourcomment_createtime'] = time();

        if ($this->DataControl->insertData("eas_student_hourcomment", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function getDate($startdate, $enddate)
    {
        $stimestamp = strtotime($startdate);
        $etimestamp = strtotime($enddate);
        $days = ($etimestamp - $stimestamp) / 86400 + 1;
        $date = array();
        for ($i = 0; $i < $days; $i++) {
            $date[] = date('Y-m-d', $stimestamp + (86400 * $i));
        }
        return $date;
    }

    //pc
    function studentTimetableDay($request)
    {
        $datawhere = "c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and ht.staffer_id='{$request['staffer_id']}' and c.class_status = '1' and ch.hour_ischecking <> '-1' and ht.teaching_isdel='0' and ec.classcode_isregister = '1' AND th.teachhour_isregister = '1'";
        if (isset($request['fixedtime']) && $request['fixedtime'] != '') {
            $datawhere .= " and ch.hour_day='{$request['fixedtime']}'";
        }

        if (isset($request['monthtime']) && $request['monthtime'] != '') {
            $request['starttime'] = date('Y-m-01', strtotime($request['monthtime']));;
            $request['endtime'] = date('Y-m-t', strtotime($request['monthtime']));;
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        }

        $sql = "select ch.hour_id,ch.hour_day,ch.hour_way,ch.hour_number,c.class_id,c.class_cnname,co.course_branch,sch.school_cnname,cl.classroom_cnname,cl.classroom_branch,c.class_fullnums,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,c.class_enname
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum
              ,(select c.coursecat_branch FROM smc_course as s LEFT JOIN smc_code_coursecat as c ON c.coursecat_id = s.coursecat_id WHERE s.course_id = c.course_id ) as coursecat_branch
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co ON co.course_id = c.course_id
              left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
              left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',ch.hour_lessontimes)
              where {$datawhere}
              group by ch.hour_id
              order by ch.hour_starttime asc ";

        $hourList = $this->DataControl->selectClear($sql);



        if ($hourList) {
            $status = array('0' => '待考勤', '1' => '已考勤', '-1' => '已取消');
            $week = array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日');

            foreach ($hourList as &$hourOne) {
                $hourOne['hour_ischecking_name'] = $status[$hourOne['hour_ischecking']];
                $hourOne['week'] = $week[date('w', $hourOne['hour_day'])];
                $hourOne['hour_evaluate'] = 1 / 20;
                if ($hourOne['hour_way'] == 1) {
                    $hourOne['classroom_cnname'] = $hourOne['classroom_branch'] = $hourOne['hour_number'];
                }
            }
        }

        if ($hourList) {
            //上午
            $ams = strtotime("00:00");
            $ame = strtotime("11:59");
            //下午
            $pms = strtotime("12:00");
            $pme = strtotime("17:59");
            //晚上
            $mms = strtotime("18:00");
            $mme = strtotime("23:59");

            $dataamlist = array();
            $datapmlist = array();
            $datammlist = array();
            foreach ($hourList as $One) {
//                $One['hour_starttime']=str_pad($One['hour_starttime'],5,0,STR_PAD_LEFT);
                $starttime = strtotime($One['hour_starttime']);
                if ($starttime >= $ams && $starttime < $ame) {
                    $dataamlist[] = $One;
                } elseif ($starttime >= $pms && $starttime < $pme) {
                    $datapmlist[] = $One;
                } elseif ($starttime >= $mms && $starttime < $mme) {
                    $datammlist[] = $One;
                }
            }
        }

        $datalist = array();
        $datalist['0']['name'] = "上午";
        $datalist['0']['list'] = $dataamlist;
        $datalist['1']['name'] = "下午";
        $datalist['1']['list'] = $datapmlist;
        $datalist['2']['name'] = "晚上";
        $datalist['2']['list'] = $datammlist;

        if ($request['datatype'] == '1') {
            $hourList = $datalist;
        } elseif ($request['datatype'] == '2') {
            $dataarray = array();
            $field = array();
            $weekname = array("周一", "周二", "周三", "周四", "周五", "周六", "周日");

            $stimestamp = strtotime($request['starttime']);
            $etimestamp = strtotime($request['endtime']);
            // 计算日期段内有多少天
            $days = ($etimestamp - $stimestamp) / 86400 + 1;
            // 保存每天日期
            $date = array();
            for ($i = 0; $i < $days; $i++) {
                $dateOne = date('Y-m-d', $stimestamp + (86400 * $i));
                $date[] = $dateOne;

                //表头
                $field[$i] = $weekname[$i] . "（" . $dateOne . "）";
            }
            //上午

            $dataOne = array();
            if ($datalist['0']['list']) {
                foreach ($datalist['0']['list'] as $k => $datavar) {
                    if ($date) {
                        foreach ($date as $key => $datevar) {
                            if ($datavar['hour_day'] == $datevar) {
                                $dataOne[$key]['week'][] = $datavar;
                            } else {
                                $dataOne[$key]['week']['-1'] = '';
                            }
                        }
                    }
                }
                $dataOne = $this->get_arr($dataOne);
                $dataarray['0']['name'] = "上午";
                $dataarray['0']['list'] = array_values($dataOne);
            } else {
                $dataarray['0']['name'] = "上午";
                $dataarray['0']['list'] = array();
            }

            //下午
            $dataOne = array();
            if ($datalist['1']['list']) {
                foreach ($datalist['1']['list'] as $k => $datavar) {
                    if ($date) {
                        foreach ($date as $key => $datevar) {
                            if ($datavar['hour_day'] == $datevar) {
                                $dataOne[$key]['week'][] = $datavar;
                            } else {
                                $dataOne[$key]['week']['-1'] = '';
                            }
                        }
                    }
                }
                $dataOne = $this->get_arr($dataOne);

                $dataarray['1']['name'] = "下午";
                $dataarray['1']['list'] = array_values($dataOne);
            } else {
                $dataarray['1']['name'] = "下午";
                $dataarray['1']['list'] = array();
            }

            //晚上
            $dataOne = array();
            if ($datalist['2']['list']) {
                foreach ($datalist['2']['list'] as $k => $datavar) {
                    if ($date) {
                        foreach ($date as $key => $datevar) {
                            if ($datavar['hour_day'] == $datevar) {
                                $dataOne[$key]['week'][] = $datavar;
                            } else {
                                $dataOne[$key]['week']['-1'] = '';
                            }
                        }
                    }
                }
                $dataOne = $this->get_arr($dataOne);

                $dataarray['2']['name'] = "晚上";
                $dataarray['2']['list'] = array_values($dataOne);
            } else {
                $dataarray['2']['name'] = "晚上";
                $dataarray['2']['list'] = array();
            }

            $hourList = $dataarray;

        } elseif ($request['datatype'] == '3') {
            $WORK_DAY = [
                0 => ['en' => 'Sunday', 'cn' => '周日', 'tn' => 'G'],
                1 => ['en' => 'Monday', 'cn' => '周一', 'tn' => 'A'],
                2 => ['en' => 'Tuesday', 'cn' => '周二', 'tn' => 'B'],
                3 => ['en' => 'Wednesday', 'cn' => '周三', 'tn' => 'C'],
                4 => ['en' => 'Thursday', 'cn' => '周四', 'tn' => 'D'],
                5 => ['en' => 'Friday', 'cn' => '周五', 'tn' => 'E'],
                6 => ['en' => 'Saturday', 'cn' => '周六', 'tn' => 'F'],
                7 => ['en' => 'Sunday', 'cn' => '周日', 'tn' => 'G']
            ];

            $start = $request['starttime'];
            $end = $request['endtime'];

            $weekstart = date("w", strtotime($start));
            $weekend = date("w", strtotime($end));

            $start_num = $weekstart;
            $end_num = 6 - $weekend;

            $starttime = date("Y-m-d", strtotime('-' . $start_num . ' day', strtotime($start)));
            $endtime = date("Y-m-d", strtotime('+' . $end_num . ' day', strtotime($end)));

            $dateList = $this->getDate($starttime, $endtime);
            $list = array();
            foreach ($dateList as $date) {
                if ($hourList) {
                    foreach ($hourList as $meetingOne) {
                        if ($meetingOne['hour_day'] == $date) {
                            if ($date < $start || $date > $end) {
                                if ($date < $start) {
                                    $week = '1';
                                } else {
                                    $week = ceil((date("d", strtotime($end)) + $start_num) / 7);
                                }

                            } else {
                                $week = ceil((date("d", strtotime($date)) + $start_num) / 7);
                            }
                            $list[$week][$WORK_DAY[date("w", strtotime($date))]['tn'] . '_1'][] = $meetingOne;
                        }
                    }
                }
                if ($date < $start || $date > $end) {
                    if ($date < $start) {
                        $week = '1';
                    } else {
                        $week = ceil((date("d", strtotime($end)) + $start_num) / 7);
                    }
                } else {
                    $week = ceil((date("d", strtotime($date)) + $start_num) / 7);
                }
                if (!$list[$week][$WORK_DAY[date("w", strtotime($date))]['tn'] . '_1']) {
                    $data = array();
                    $data['hour_day'] = $date;
                    $list[$week][$WORK_DAY[date("w", strtotime($date))]['tn'] . '_1'][] = $data;
                }
            }

            foreach ($list as &$one) {
                ksort($one);
            }
            $hourList = $list;
        }

        $data = array();
        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co ON co.course_id = c.course_id
              left join eas_classcode as ec ON ec.classcode_branch = co.course_branch and ec.company_id = co.company_id
              left join eas_teachhour as th ON th.company_id = ec.company_id AND th.classcode_branch = ec.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',ch.hour_lessontimes)
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['field'] = $field;
        $data['list'] = $hourList;


        return $data;
    }


    function timetableOne($request)
    {
        $datawhere = " c.class_id = '{$request['class_id']}'  ";

        $sql = "select c.class_id,c.class_branch,c.class_cnname,c.class_enname,c.class_fullnums,c.course_id,c.class_stdate,r.course_cnname,r.course_branch,s.school_branch,s.school_shortname,
              (SELECT COUNT(s.study_isreading) FROM smc_student_study as s WHERE s.class_id = c.class_id and s.study_isreading = 1 ) as studentnum
              from smc_class as c 
              LEFT JOIN smc_school as s ON s.school_id = c.school_id 
              LEFT JOIN smc_course as r ON r.course_id = c.course_id 
              where {$datawhere} ";

        $classOne = $this->DataControl->selectOne($sql);

        //主教师
        $teacherlist = $this->DataControl->selectClear("select s.staffer_cnname,s.staffer_enname
                        from smc_class_teach as c 
                        left join smc_staffer as s ON s.staffer_id = c.staffer_id 
                        where c.teach_status = '0' and c.teach_type = '0' and c.class_id = '{$classOne['class_id']}' ");
        $teacherstr = '';
        if ($teacherlist) {
            $teacherArray = array();
            foreach ($teacherlist as $teacherlistVar) {
                if ($teacherlistVar['staffer_enname']) {
                    $teacherArray[] = $teacherlistVar['staffer_cnname'] . '-' . $teacherlistVar['staffer_enname'];
                } else {
                    $teacherArray[] = $teacherlistVar['staffer_cnname'];
                }
            }
            $teacherstr = implode(",", $teacherArray);
        }
        $classOne['teachername'] = $teacherstr;
        //助教师
        $teacherlist = $this->DataControl->selectClear("select s.staffer_cnname,s.staffer_enname
                        from smc_class_teach as c
                        left join smc_staffer as s ON s.staffer_id = c.staffer_id
                        where c.teach_status = '0' and c.teach_type = '1' and c.class_id = '{$classOne['class_id']}' ");
        $teacherstr = '';
        if ($teacherlist) {
            $teacherArray = array();
            foreach ($teacherlist as $teacherlistVar) {
                if ($teacherlistVar['staffer_enname']) {
                    $teacherArray[] = $teacherlistVar['staffer_cnname'] . '-' . $teacherlistVar['staffer_enname'];
                } else {
                    $teacherArray[] = $teacherlistVar['staffer_cnname'];
                }
            }
            $teacherstr = implode(",", $teacherArray);
        }
        $classOne['fu_teachername'] = $teacherstr;
        //教室
        $classroomlist = $this->DataControl->selectClear("select r.classroom_cnname
                        from smc_class_hour as c 
                        left join smc_classroom as r ON r.classroom_id = c.classroom_id 
                        where c.class_id = '{$classOne['class_id']}' and c.classroom_id <> 0
                        GROUP BY r.classroom_cnname ");
        $classroomstr = '';
        if ($classroomlist) {
            $classroomArray = array();
            foreach ($classroomlist as $classroomlistVar) {
                $classroomArray[] = $classroomlistVar['classroom_cnname'];
            }
            $classroomstr = implode(",", $classroomArray);
        }
        $classOne['classroomname'] = $classroomstr;
        //班级课时
        $classhourlist = $this->DataControl->selectClear("select c.hour_id,c.hour_name,c.hour_lessontimes,c.hour_ischecking
                        from smc_class_hour as c 
                        where c.class_id = '{$classOne['class_id']}' and c.hour_ischecking > '-1'
                        ORDER BY c.hour_lessontimes ASC");
        if (is_array($classhourlist)) {
            foreach ($classhourlist as &$classhourvar) {
                if ($classhourvar['hour_ischecking'] == '1') {
                    $classhourvar['hour_ischeckingname'] = '已结束';
                } elseif ($classhourvar['hour_ischecking'] == '0') {
                    $classhourvar['hour_ischeckingname'] = '未开始';
                }
            }
        }
        $classOne['classhourlist'] = $classhourlist;

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        return $classOne;

    }

    /**
     *  教务报表-教师课表
     * author: ling
     * 对应接口文档 0001
     */
    function getTeacTimetable($request)
    {

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;
        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' )";

        } elseif (isset($request['re_school_id']) and $request['re_school_id'] !== "") {
            $where .= " and c.school_id ='{$request['re_school_id']}'";
        } elseif (isset($request['school_id']) and $request['school_id'] !== "") {
            $where .= " and c.school_id ='{$request['school_id']}'";
        } else {
            return false;
        }

        if (isset($request['classroom_id']) and $request['classroom_id'] !== "") {
            $where .= " and ch.classroom_id = '{$request['classroom_id']}' ";
        }

        $datawhere = '1';
        if (isset($request['hour_way']) and $request['hour_way'] !== "") {
            $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";

        }

        if ($request['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $where .= " and s.staffer_id = '{$request['staffer_id']}' ";
            }
        }

        $sql = "select t.staffer_id,
                concat(s.staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )  ) as staffer_cnname
                from smc_staffer as s
                left JOIN smc_class_hour_teaching as t  ON t.staffer_id  = s.staffer_id and t.teaching_type =0
                left JOIN smc_class_hour as ch  ON ch.class_id = t.class_id and t.hour_id = ch.hour_id
                left JOIN smc_class as c ON c.class_id  = t.class_id
                left JOIN smc_classroom as cl ON cl.classroom_id  = ch.classroom_id
                left join smc_school as sch on sch.school_id=c.school_id
                where ch.hour_day >='{$startday}' and ch.hour_day <= '{$endday}' and {$where} and s.staffer_leave = 0 and c.class_status <> '-2'  and s.company_id = '{$request['company_id']}'
                group by  t.staffer_id ";

        $arr_staffer = $this->DataControl->selectClear($sql);

        if ($arr_staffer) {
            $arr_staffer_id = array_column($arr_staffer, 'staffer_id');
            $str_staffer_id = trim(implode(',', $arr_staffer_id), ',');
            $arr_staffercnname = array_column($arr_staffer, 'staffer_cnname', 'staffer_id');

        } else {
            return array();
        }


        $sql = " select  s.staffer_id,s.staffer_cnname,c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number
		 from  smc_staffer as s
		 LEFT  JOIN  smc_class_hour_teaching as t  On s.staffer_id = t.staffer_id and  t.teaching_type =0
		 LEFT JOIN smc_class_hour AS ch ON  ch.hour_id = t.hour_id
		 LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		 left join smc_course as co on co.course_id=c.course_id
         left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
		 where ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where}  and c.class_status <> '-2' and t.staffer_id in ($str_staffer_id) and {$datawhere} and s.company_id = '{$request['company_id']}'
		";

        $weekList = $this->DataControl->selectClear($sql);
        if ($weekList) {
            foreach ($weekList as &$val) {
                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = "云教室";
                    $val['classroom_cnname'] = $val['classroom_brnach'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $val['hour_way'] == 0 ? "实体课" : "线上课";
                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }
            }
        }


        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = array("日", "一", "二", "三", "四", "五", "六");
        $data = array();
        if ($weekList) {

//			只检测未上课的课时冲突
//			if (isset($request['conflict']) && $request['conflict'] == 1) {
//				foreach($weekList as $key => $value){
//					if($value['hour_ischecking'] == 0){
//						$dataWeekList1[$key] =  $value;
//
//				}else{
//						$dataWeekList2[$key.'a'] =  $value;
//					 }
//				}
//				$dataWeekList1 = $this->checkClassroomConflict($dataWeekList1);
//				$dataWeekList1 = $this->checkStafferConflict($dataWeekList1);
//
//				$weekList =array_values(array_merge($dataWeekList1, $dataWeekList2));
//			}

//			全局检测冲突
            if (isset($request['conflict']) && $request['conflict'] == 1) {

                $weekList = $this->checkClassroomConflict($weekList);

                $weekList = $this->checkStafferConflict($weekList);
//
            }

            foreach ($weekList as $key => &$val) {

                foreach ($arr_staffer_id as $k => $v) {
                    $data[$v]['staffer_cnname']['staffer_cnname'] = $arr_staffercnname[$v];

                    if ($val['staffer_id'] == $v) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $datevalue . " 周" . $weekarray[$week];
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];


                            if ($val['hour_day'] == $datevalue) {

                                $data[$v][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    }
                }
            }
        }
        $data = $this->get_arr($data);

        return $data;

    }


    /**
     *  教务报表-教室课表
     * author: ling
     * 对应接口文档 0001
     */
    function roomTimeTable($request)
    {
        if (!isset($request['classroom_id']) || $request['classroom_id'] == "") {
            $request['classroom_id'] = 0;
        }

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }


        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = date("H:i", strtotime($request['starttime']));
        } else {
            $starttime = "06:00";
        }

        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = date("H:i", strtotime($request['endtime']));
        } else {
            $endtime = "23:59";
        }

        if (isset($request['lengthtime']) && $request['lengthtime'] !== "") {
            $lengthtime = intval($request['lengthtime']);
        } else {
            $lengthtime = '60';
        }

//		$time_length = "60 minute";
//		$time_quantum = date('H:i',strtotime( " + $time_length",strtotime($starttime)));

        $time_quantum = array();

        for ($i = strtotime($starttime); $i <= strtotime($endtime); $i = $i + 60 * $lengthtime) {

            $str_endtime = date("H:i", $i + 60 * $lengthtime);

            if ($str_endtime == "00:00") {
                $str_endtime = $endtime;
            }
            $time_quantum[] = date("H:i", $i) . '-' . $str_endtime;
        }

        $time_quantum = array_unique($time_quantum);

        $where = '1';
        if ($request['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $where .= " and s.staffer_id = '{$request['staffer_id']}' ";
            }
            $str_school_id = $this->getStafferAllSchool($request['re_postbe_id']);
            $where .= " and c.school_id in ({$str_school_id})";
        }

        $sql = "select c.class_id,class_branch,c.class_cnname,ch.hour_id, s.staffer_cnname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,t.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number
            from  smc_classroom AS cl
			LEFT JOIN smc_class_hour AS ch ON cl.classroom_id = ch.classroom_id
			LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
			LEFT JOIN smc_course AS co ON co.course_id = c.course_id
			LEFT JOIN smc_class_hour_teaching AS t ON t.hour_id = ch.hour_id and  t.teaching_type =0
			LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
            where {$where}  AND ch.hour_day >='{$startday}'  and ch.hour_day <='{$endday}' and cl.classroom_id ='{$request['classroom_id']}' and co.course_inclasstype <>'2' and c.class_status <> '-2' and c.company_id='{$request['company_id']}'
    		";

        $weekList = $this->DataControl->selectClear($sql);


        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = array("日", "一", "二", "三", "四", "五", "六");
        $data = array();
        if ($weekList) {

            foreach ($weekList as $key => &$val) {
                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = "云教室";
                    $val['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $val['hour_way'] == 0 ? "实体课" : "线上课";

                foreach ($time_quantum as $k => $v) {
                    $data[$k]['time_quantum']['time_quantum'] = $v;

                    $iend = $weekList[$key]['hour_endtime'];
                    $istart = $weekList[$key]['hour_starttime'];
                    $jstart = substr($v, 0, 5);
                    $jend = substr($v, 6, 5);

                    if (!($iend < $jstart) && !($jend < $istart)) {

                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $datevalue . " 周" . $weekarray[$week];
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];

                            if ($val['hour_day'] == $datevalue) {
                                $data[$k][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$k][$enweekarray[$week]] = array();
                            }
                        }
                    } else {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $datevalue . " 周" . $weekarray[$week];
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                        }

                    }

                }
            }
        }
//		$data = $this->get_arr($data);
        return $data;
    }

    /**
     *  教务报表-教师详情课表
     * author: ling
     * 对应接口文档 0001
     */
    function stafferTimeTable($request)
    {
        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d');
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }


        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = date("H:i", strtotime($request['starttime']));
        } else {
            $starttime = "06:00";
        }

        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = date("H:i", strtotime($request['endtime']));
        } else {
            $endtime = "23:59";
        }

        if (isset($request['lengthtime']) && $request['lengthtime'] !== "") {
            $lengthtime = intval($request['lengthtime']);
        } else {
            $lengthtime = '60';
        }


//		$time_length = "60 minute";
//		$time_quantum = date('H:i',strtotime( " + $time_length",strtotime($starttime)));

        $time_quantum = array();

        for ($i = strtotime($starttime); $i <= strtotime($endtime); $i = $i + 60 * $lengthtime) {

            $str_endtime = date("H:i", $i + 60 * $lengthtime);

            if ($str_endtime == "00:00") {
                $str_endtime = $endtime;
            }
            $time_quantum[] = date("H:i", $i) . '-' . $str_endtime;
        }

        $time_quantum = array_unique($time_quantum);

        $where = '1';
        $datawhere = '1';
        if (isset($request['hour_way']) && $request['hour_way'] !== "") {
            $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
        }
        if (!isset($request['re_staffer_id']) || $request['re_staffer_id'] == "") {
            $request['re_staffer_id'] = 0;
        }
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $where .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' )";
        } elseif (isset($request['re_school_id']) && $request['re_school_id'] !== "") {
            $where .= " and c.school_id = '{$request['re_school_id']}'";
        } elseif (isset($request['school_id']) && $request['school_id'] !== "") {
            $where .= " and c.school_id = '{$request['school_id']}'";
        } else {
            return false;
        }


        if ($request['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $where .= " and s.staffer_id = '{$request['staffer_id']}' ";
            }
            $str_school_id = $this->getStafferAllSchool($request['re_postbe_id']);
            $where .= " and c.school_id in ({$str_school_id})";
        }

        $sql = " select s.staffer_id,s.staffer_cnname,c.class_id,c.class_cnname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number
		 from  smc_staffer as s
		 LEFT  JOIN  smc_class_hour_teaching as t  On s.staffer_id = t.staffer_id and  t.teaching_type =0
		 LEFT JOIN smc_class_hour AS ch ON  ch.hour_id = t.hour_id
		 LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		 left join smc_course as co on co.course_id=c.course_id
         left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
		 where ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where}  and co.course_inclasstype <>'2' and c.class_status <> '-2' and {$datawhere} and s.company_id='{$request['company_id']}'
		";

        $weekList = $this->DataControl->selectClear($sql);

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = array("日", "一", "二", "三", "四", "五", "六");
        $data = array();
        if ($weekList) {


            foreach ($weekList as $key => &$val) {
                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = "云教室";
                    $val['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $val['hour_way'] == 0 ? "实体课" : "线上课";

                foreach ($time_quantum as $k => $v) {
                    $data[$k]['time_quantum']['time_quantum'] = $v;
                    $iend = $weekList[$key]['hour_endtime'];
                    $istart = $weekList[$key]['hour_starttime'];
                    $jstart = substr($v, 0, 5);
                    $jend = substr($v, 6, 5);

                    if (!($iend < $jstart) && !($jend < $istart)) {

                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $datevalue . " 周" . $weekarray[$week];
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];

                            if ($val['hour_day'] == $datevalue) {

                                $data[$k][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$k][$enweekarray[$week]] = array();
                            }
                        }
                    } else {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $datevalue . " 周" . $weekarray[$week];
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                        }

                    }
                }
            }
        }

//		$data = $this->get_arr($data);

        return $data;

    }


    /**
     * 教务报表-教师空闲时间检索
     */

    function getStafferTeachingTime($request)
    {

        $where = '1';
        $datawhere = '1';
        if (isset($request['hour_day']) and $request['hour_day'] !== "") {
            $where .= " and ch.hour_day ='{$request['hour_day']}'";
        } else {
            $request['hour_day'] = date('Y-m-d');
            $where .= " and ch.hour_day ='{$request['hour_day']}'";
        }

        $star_monthday = date("Y-m", strtotime($request['hour_day'])) . '-01';
        $end_monthday = date("Y-m-d", strtotime("$star_monthday +1 month -1 day"));
        $star_month = date("m", strtotime($star_monthday));
        $datawhere .= " and  chr.hour_day>='{$star_monthday}' and   chr.hour_day<='{$end_monthday}'";
        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = date("H:i", strtotime($request['starttime']));
        } else {
            $starttime = "06:00";
        }

        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = date("H:i", strtotime($request['endtime']));
        } else {
            $endtime = "23:59";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $where .= " and (sf.staffer_cnname like '%{$request['keyword']}%' or sf.staffer_enname like '%{$request['keyword']}%' or sf.staffer_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['com_postbe_id']) && $request['com_postbe_id'] !== "") {
            $where .= " and sp.post_id ='{$request['com_postbe_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        if ($request['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$request['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0 
             ");

            if (!$postbe && !$compostbe) {
                $where .= " and sf.staffer_id = '{$request['staffer_id']}' ";
            }
            $str_school_id = $this->getStafferAllSchool($request['re_postbe_id']);
            $where .= " and cl.school_id in ({$str_school_id})";
        }

        $sql = "select s.school_cnname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,staffer_branch,sf.staffer_id,s.school_id,count(cl.class_id) as hour_num,count(htg.hour_id) as hour_numing,
            (select count(DISTINCT ht.class_id) 
                from smc_class_hour_teaching as ht 
                left join smc_class as sc on sc.class_id = ht.class_id
                left join smc_class_hour as chr ON ht.hour_id = chr.hour_id
                where ht.staffer_id = sf.staffer_id and sc.school_id=cl.school_id
                and {$datawhere} 
            )  as teaching_classnum,
             (select count(DISTINCT ht.hour_id) 
                from smc_class_hour_teaching as ht 
                left join smc_class as sc on sc.class_id = ht.class_id
                left join smc_class_hour as chr ON ht.hour_id = chr.hour_id
                where ht.staffer_id = sf.staffer_id and sc.school_id=cl.school_id
                 and {$datawhere} 
            )  as teaching_hournum
            from smc_class_hour_teaching as htg
            left join smc_class_hour as  ch ON ch.hour_id = htg.hour_id
            left join smc_class as cl On htg.class_id = cl.class_id
            left join smc_staffer as sf ON sf.staffer_id = htg.staffer_id
            left join smc_school as s ON s.school_id = cl.school_id
            left join gmc_staffer_postbe as sp ON sp.staffer_id = htg.staffer_id and sp.school_id=cl.school_id
            where {$where}  and 
             ( ('{$endtime}' <=ch.hour_starttime) or (ch.hour_endtime <= '{$starttime}'))
               and sf.staffer_id > 0
             and cl.company_id ='{$request['company_id']}'
             group by s.school_id,sf.staffer_id  
             order by  s.school_id DESC
           
        ";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['teaching_classnum'] = $dateexcelvar['teaching_classnum'];
                    $datearray['teaching_hournum'] = $dateexcelvar['teaching_hournum'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("教师中文名", "教师英文名", '教师编号', $star_month."月份带班数", $star_month."月份课时数", "所属校区", "校区编号");
            $excelfileds = array('staffer_cnname', 'staffer_enname', 'staffer_branch', 'teaching_classnum', 'teaching_hournum', 'school_cnname', 'school_branch');

            $tem_name = '教师空闲时段检索表.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $dataList = array();
            }
            $countsql = "select htg.teaching_id
            from smc_class_hour_teaching as htg
            left join smc_class_hour as  ch ON ch.hour_id = htg.hour_id
            left join smc_class as cl On htg.class_id = cl.class_id
            left join smc_staffer as sf ON sf.staffer_id = htg.staffer_id
            left join smc_school as s ON s.school_id = cl.school_id
            left join gmc_staffer_postbe as sp ON sp.staffer_id = htg.staffer_id and sp.school_id=cl.school_id
            where {$where}  and 
               ( ('{$endtime}' <=ch.hour_starttime) or (ch.hour_endtime <= '{$starttime}'))
               and sf.staffer_id > 0
             and cl.company_id ='{$request['company_id']}'
             group by s.school_id,sf.staffer_id ";
            $countList = $this->DataControl->selectClear($countsql);
            if ($countList) {
                $allnum = count($countList);
            } else {
                $allnum = 0;
            }
            $result = array();
            $result['allnum'] = $allnum;
            $result['list'] = $dataList;
            $result['month'] = $star_month;

            return $result;

        }

    }


    /**
     * 获取教室下拉列表
     * author: ling
     * 对应接口文档 0001
     */
    function getClassroomList($request)
    {
        $where = 1;
        if ($request['account_class'] == 0) {
            $str_school_id = $this->getStafferAllSchool($request['re_postbe_id']);
            $where .= " and c.school_id in ({$str_school_id})";
        }
        $sql = "
                select c.classroom_cnname,c.classroom_id,c.classroom_branch from smc_classroom as c 
                where  c.company_id='{$request['company_id']}' and  {$where}
        ";
        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    function get_arr($arr)
    {
        foreach ($arr as $k => $v) {
            if (is_array($arr[$k])) {
                $arr[$k] = $this->get_arr($arr[$k]);
            } else {
                if ($k == -1) {
                    unset($arr[$k]);
                }
            }
        }
        return $arr;
    }

    /**
     * @param $weekList
     * @return mixed
     * 检测教室冲突
     */
    function checkClassroomConflict($weekList)
    {

        $week_date = date('Y-m-d');
        if ($weekList) {
            $weekList = array_values($weekList);


            //检查冲突  //时间冲突
            if (count($weekList) > 1) {
                for ($i = 0; $i < count($weekList); $i++) {
                    for ($j = $i + 1; $j < count($weekList); $j++) {

                        if (($weekList[$i]['hour_day'] == $weekList[$j]['hour_day']) && ($weekList[$i]['classroom_id'] == $weekList[$j]['classroom_id'])) {

                            $iend = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_endtime'];
                            $istart = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_starttime'];
                            $jend = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_endtime'];
                            $jstart = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_starttime'];

                            $iend = date('Y-m-d H:i:s', strtotime($iend));
                            $istart = date('Y-m-d H:i:s', strtotime($istart));
                            $jend = date('Y-m-d H:i:s', strtotime($jend));
                            $jstart = date('Y-m-d H:i:s', strtotime($jstart));

                            if (!($iend <= $jstart) && !($jend <= $istart) && $weekList[$i]['hour_ischecking'] == 0 && $weekList[$j]['hour_ischecking'] == 0) {

                                $weekList[$i]['hour_ischecking'] = -2;
                                $weekList[$j]['hour_ischecking'] = -2;
                                $weekList[$j]['classroom_confict'] = -2;
                                $weekList[$i]['classroom_confict'] = -2;
                            }
                        }
                    }
                }
            }
        }
        return $weekList;
    }

    /**
     * @param $weekList
     * @return mixed
     * 检测教师冲突
     */
    function checkStafferConflict($weekList)
    {

        $week_date = date('Y-m-d');
        if ($weekList) {
            //检查冲突  //时间冲突
            if (count($weekList) > 1) {
                for ($i = 0; $i < count($weekList); $i++) {
                    for ($j = $i + 1; $j < count($weekList); $j++) {
                        if (($weekList[$i]['hour_day'] == $weekList[$j]['hour_day']) && ($weekList[$i]['staffer_id'] == $weekList[$j]['staffer_id'])) {
                            $iend = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_endtime'];
                            $istart = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_starttime'];
                            $jend = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_endtime'];
                            $jstart = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_starttime'];

                            $iend = date('Y-m-d H:i:s', strtotime($iend));
                            $istart = date('Y-m-d H:i:s', strtotime($istart));
                            $jend = date('Y-m-d H:i:s', strtotime($jend));
                            $jstart = date('Y-m-d H:i:s', strtotime($jstart));
                            if (!($iend <= $jstart) && !($jend <= $istart) && $weekList[$i]['hour_ischecking'] == 0 && $weekList[$j]['hour_ischecking'] == 0) {
                                $weekList[$i]['hour_ischecking'] = -2;
                                $weekList[$j]['hour_ischecking'] = -2;
                                $weekList[$j]['staffer_confict'] = -2;
                                $weekList[$i]['staffer_confict'] = -2;
                            }
                        }
                    }
                }
            }
        }
        return $weekList;
    }


    /**
     * 获取职工任职所有的校园
     * author: ling
     * 对应接口文档 0001
     */
    function getStafferAllSchool($postbe_id)
    {
        $sql = "
            select  postrole_id,postpart_id
            from  gmc_staffer_postbe as p 
            where p.postbe_id ='{$postbe_id}' ";

        $postOne = $this->DataControl->selectOne($sql);

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id='{$this->staffer_id}'");

        if ($stafferOne['account_class'] == 0) {
            if ($postOne['postrole_id'] == 0) {
                $postList = $this->DataControl->selectClear("  select school_id
            from  gmc_staffer_postbe as p 
            where p.postbe_id ='{$postbe_id}'");

                if (!$postList) {
                    $arr_all_school = array();
                } else {
                    $arr_all_school = array_column($postList, 'school_id');
                }

//                var_dump($arr_all_school);
            } else {
                $post_sql = "
            select co.school_id
            from  gmc_staffer_postbe as sp 
            left join gmc_company_organizeschool as co ON co.organize_id =sp.organize_id
            where sp.postbe_id = '{$postbe_id}' and sp.school_id = '0'
        ";
                $com_postList = $this->DataControl->selectClear($post_sql);

                if (!$com_postList) {
                    $arr_all_school = array();
                } else {
                    $arr_all_school = array_column($com_postList, 'school_id');
                }
                if (!$arr_all_school) {
                    $arr_all_school = array();
                }
                //var_dump($arr_all_school);
            }
        } else {
            $arr_school = $this->DataControl->selectClear("
                select s.school_id
                from smc_school as s 
                where s.company_id = '{$this->company_id}' 
            ");
            $arr_all_school = array_column($arr_school, 'school_id');
        }

        $str_school_id = trim(implode(',', $arr_all_school), ',');

        if (!$str_school_id) {
            $str_school_id = 0;
        }

        return $str_school_id;
    }

}
