<?php
/**
 * 客诉-话术
 */

namespace Model\Easx;


class UcsSpeechcraftModel extends UcsCommonModel
{
    /**
     * 话术列表 -zjc
     * @return bool
     */
    function speechcraftList($paramArray)
    {

        if(!isset($paramArray['speechcraft_belongs']) || $paramArray['feedbacktype_belongs']='')
        {
            $this->error = 1;
            $this->errortip = "参数缺失:话术所属";
            $this->result = array();
            return false;
        }

        $dataWhere=" s.company_id='{$paramArray['company_id']}' AND s.speechcraft_status=1 ";
        if($paramArray['speechcraft_belongs']==2)//个人
        {
            $dataWhere .=" AND s.staffer_id='{$paramArray['staffer_id']}' ";
        }else{
            $dataWhere .=" AND s.staffer_id=0 ";
        }

        //分类ID
        if(!empty($paramArray['feedbacktype_id']))
        {
            $dataWhere .=" AND s.feedbacktype_id='{$paramArray['feedbacktype_id']}' ";
        }
        //主题ID
        if(!empty($paramArray['feedbacktheme_id']))
        {
            $dataWhere .=" AND s.feedbacktheme_id='{$paramArray['feedbacktheme_id']}' ";
        }
        //话术名称
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $dataWhere .= " AND (s.speechcraft_name like '%{$paramArray['keyword']}%' )";
        }

        //分页
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $dataList = $this->DataControl->selectClear("SELECT s.speechcraft_id,s.speechcraft_name,s.speechcraft_content,s.speechcraft_createtime,s.feedbacktype_id,s.feedbacktheme_id,ct.feedbacktype_name,cf.feedbacktheme_name  
        FROM ucs_speechcraft s 
        LEFT JOIN ucs_code_feedbacktype AS ct ON s.feedbacktype_id=ct.feedbacktype_id
        LEFT JOIN ucs_code_feedbacktheme AS cf ON s.feedbacktheme_id=cf.feedbacktheme_id
        WHERE {$dataWhere}
        LIMIT {$pagestart},{$num} 
        ");
        if(!empty($dataList))
        {
            foreach ($dataList as $key =>$value)
            {
                $dataList[$key]['speechcraft_createtime'] = date('Y-m-d H:i:s',$value['speechcraft_createtime']);
            }

        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT COUNT(s.speechcraft_id) as datanum 
        FROM ucs_speechcraft s 
        LEFT JOIN ucs_code_feedbacktype AS ct ON s.feedbacktype_id=ct.feedbacktype_id
        LEFT JOIN ucs_code_feedbacktheme AS cf ON s.feedbacktheme_id=cf.feedbacktheme_id
        WHERE {$dataWhere}
        ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "话术列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无话术";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 话术查看单个 -zjc
     * @param $paramArray
     * @return bool
     */
    function speechcraftDetail($paramArray)
    {
        if(!isset($paramArray['speechcraft_id']) || $paramArray['speechcraft_id']=='')
        {
            $this->error = 1;
            $this->errortip = "参数缺失:ID";
            $this->result = array();
            return false;
        }
        $dataList = $this->DataControl->selectOne("SELECT s.speechcraft_id,s.speechcraft_name,s.speechcraft_content,s.speechcraft_createtime,ct.feedbacktype_name,cf.feedbacktheme_name
FROM ucs_speechcraft s
LEFT JOIN ucs_code_feedbacktype AS ct ON s.feedbacktype_id=ct.feedbacktype_id
LEFT JOIN ucs_code_feedbacktheme AS cf ON s.feedbacktheme_id=cf.feedbacktheme_id
WHERE speechcraft_id='{$paramArray['speechcraft_id']}' ");

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "单条话术获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "未找到该话术!";
            $this->result = array();
            return false;
        }
    }

    /**
     * 新增话术 -zjc
     * @param $paramArray
     * @return bool
     */
    function speechcraftadd($paramArray)
    {

        if(empty($paramArray['feedbacktype_id']) || empty($paramArray['feedbacktheme_id']) ||empty($paramArray['speechcraft_name']) ||empty($paramArray['speechcraft_content']) )
        {
            $this->error = 1;
            $this->errortip = "必要参数缺失";
            $this->result = array();
            return false;
        }

        $insertData['company_id']=$this->company_id;
        $insertData['staffer_id']=$this->staffer_id;
        $insertData['feedbacktype_id']=$paramArray['feedbacktype_id'];
        $insertData['feedbacktheme_id']=$paramArray['feedbacktheme_id'];
        $insertData['speechcraft_name']=$paramArray['speechcraft_name'];
        $insertData['speechcraft_content']=$paramArray['speechcraft_content'];
        $insertData['speechcraft_status']=1;
        $insertData['speechcraft_createtime']=time();
        $insert_id = $this->DataControl->insertData("ucs_speechcraft",$insertData);

        if($insert_id>0){
            $insertData['speechcraft_createtime'] = date('Y-m-d H:i:s',$insertData['speechcraft_createtime']);
            $this->error = 0;
            $this->errortip = "新增成功!";
            $this->result = $insertData;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $paramArray;
            return false;
        }
    }

    /**
     * 话术 新增/编辑提交 -zjc
     */
    function speechcraftSubmit($paramArray)
    {
        if(empty($paramArray['speechcraft_id']) || empty($paramArray['feedbacktype_id']) || empty($paramArray['feedbacktheme_id']) ||empty($paramArray['speechcraft_name']) ||empty($paramArray['speechcraft_content']) )
        {
            $this->error = 1;
            $this->errortip = "必要参数缺失";
            $this->result = array();
            return false;
        }
        $updateData['company_id']=$this->company_id;
        $updateData['staffer_id']=$this->staffer_id;
        $updateData['feedbacktype_id']=$paramArray['feedbacktype_id'];
        $updateData['feedbacktheme_id']=$paramArray['feedbacktheme_id'];
        $updateData['speechcraft_name']=$paramArray['speechcraft_name'];
        $updateData['speechcraft_content']=$paramArray['speechcraft_content'];
        $updateData['speechcraft_status']=0;
        $updateData['speechcraft_createtime']=time();
        $updateResult = $this->DataControl->updateData("ucs_speechcraft"," speechcraft_id='{$paramArray['speechcraft_id']}' ",$updateData);

        if($updateResult!=false){
            $updateData['speechcraft_createtime'] = date('Y-m-d H:i:s',$updateData['speechcraft_createtime']);
            $this->error = 0;
            $this->errortip = "更新成功!";
            $this->result = $updateData;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "操作失败了:(";
            $this->result = $updateData;
            return false;
        }
    }

    /**
     * 话术 删除 -zjc
     */
    function speechcraftDelete($paramArray)
    {
        if(!isset($paramArray['speechcraft_id']) || $paramArray['speechcraft_id']=='')
        {
            $this->error = 1;
            $this->errortip = "参数缺失:ID";
            $this->result = array();
            return false;
        }

        //查询该话术
        $stafferData = $this->DataControl->selectOne("SELECT staffer_id 
FROM ucs_speechcraft 
WHERE speechcraft_id='{$paramArray['speechcraft_id']}' 
        ");

        if($stafferData['staffer_id']==$this->staffer_id)
        {
            $delresult = $this->DataControl->delData("ucs_speechcraft","speechcraft_id='{$paramArray['speechcraft_id']}' ");
            if($delresult!=false)
            {
                $this->error = 0;
                $this->errortip = "删除成功!";
                $this->result = array();
                return false;
            }else{
                $this->error = 1;
                $this->errortip = "稍后再试试!";
                $this->result = array();
                return false;
            }

        }else{
            $this->error = 1;
            $this->errortip = "越权操作!";
            $this->result = array();
            return false;
        }

    }


}