<?php

namespace Model\Easx;


Class HomeModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

//    function __construct($publicarray)
//    {
//        parent::__construct();
//        if (is_array($publicarray)) {
//            $this->setPublic($publicarray);
//            $this->publicarray = $publicarray;
//        }
//    }
//



    //首页统计
    function TotalStatistics($paramArray)
    {
        $sql = "
            SELECT
                count( class_id ) AS classNum 
            FROM
                smc_class AS c 
            WHERE
                c.school_id = '{$paramArray['school_id']}' 
                AND c.class_status = '1' 
                AND c.class_stdate <= '{$paramArray['end_time']}' AND c.class_enddate >= '{$paramArray['start_time']}'";
        $classNum = $this->DataControl->selectOne($sql);

        $sql = "
            SELECT
                count(h.hour_id) as hourNum
            FROM
                smc_class_hour AS h
                LEFT JOIN smc_class AS c ON h.class_id = c.class_id
                where c.school_id = '{$paramArray['school_id']}' and h.hour_day >= '{$paramArray['start_time']}' and h.hour_day <= '{$paramArray['end_time']}'";
        $hourNum = $this->DataControl->selectOne($sql);

        $sql = "
            SELECT
                count(h.hour_id) as alreadyNum
            FROM
                smc_class_hour AS h
                LEFT JOIN smc_class AS c ON h.class_id = c.class_id
                where c.school_id = '{$paramArray['school_id']}' and h.hour_day >= '{$paramArray['start_time']}' and h.hour_day <= '{$paramArray['end_time']}' and h.hour_ischecking = '1'";
        $alreadyNum = $this->DataControl->selectOne($sql);

        $sql = "
            SELECT
                count(h.hourstudy_id) as stuNum
            FROM
                smc_student_hourstudy AS h
                LEFT JOIN smc_class AS c ON h.class_id = c.class_id 
            WHERE
                c.school_id = '{$paramArray['school_id']}'
                AND c.class_stdate <= '{$paramArray['end_time']}' AND c.class_enddate >= '{$paramArray['start_time']}'";
        $stuNum = $this->DataControl->selectOne($sql);

        $sql = "
            SELECT
                count(h.hourstudy_id) as dutyNum
            FROM
                smc_student_hourstudy AS h
                LEFT JOIN smc_class AS c ON h.class_id = c.class_id 
            WHERE
                c.school_id = '{$paramArray['school_id']}'
                AND c.class_stdate <= '{$paramArray['end_time']}' AND c.class_enddate >= '{$paramArray['start_time']}' AND h.hourstudy_checkin = '1'";
        $dutyNum = $this->DataControl->selectOne($sql);

        $evaluate = $dutyNum['dutyNum'];

        $sql = "
            SELECT
                count( cs.sturemark_id ) AS remarkNum 
            FROM
                eas_classhour_sturemark as cs 
                left join smc_class_hour AS h on cs.hour_id = h.hour_id
                LEFT JOIN smc_class AS c ON h.class_id = c.class_id 
            WHERE
                c.school_id = '{$paramArray['school_id']}' 
                AND h.hour_day >= '{$paramArray['start_time']}' 
                AND h.hour_day <= '{$paramArray['end_time']}'";
        $remarkNum = $this->DataControl->selectOne($sql);

        $result = array();
        $result['classNum'] = $classNum['classNum'];
        $result['hourNum'] = $hourNum['hourNum'];
        $result['alreadyNum'] = $alreadyNum['alreadyNum'];
        $result['stuNum'] = $stuNum['stuNum'];
        $result['dutyNum'] = $dutyNum['dutyNum'];
        $result['evaluate'] = $evaluate;
        $result['remarkNum'] = $remarkNum['remarkNum'];


        $field = array();
        $field["classNum"] = "进行班级数";
        $field["hourNum"] = "总课时数";
        $field["alreadyNum"] = "已上课时数";
        $field["stuNum"] = "应到学员人数";
        $field["dutyNum"] = "实到学员人数";
        $field["evaluate"] = "应评价人数";
        $field["remarkNum"] = "已评价人数";

        if ($alreadyNum) {
            $res = array('error' => '0', 'errortip' => '获取个人资料成功', 'result' => $result, 'field' => $field);
        } else {
            $res = array('error' => '1', 'errortip' => '获取个人资料失败', 'result' => $result, 'field' => $field);
        }

        return $res;
    }

    //首页代办项列表
    function AgentList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $data = array();
        $data['all_num'] = 0;
        $data['list'] = array();

        return $data;

        $sql = "
            SELECT
                cl.class_id,
                cl.class_cnname,
                s.school_cnname,
                h.hour_starttime,
                h.hour_endtime,
                h.hour_day
            FROM
                smc_student_hourstudy AS sh
                LEFT JOIN smc_class AS cl ON cl.class_id = sh.class_id
                LEFT JOIN smc_school AS s ON cl.school_id = s.school_id 
                left join smc_class_hour as h on sh.hour_id = h.hour_id
            WHERE
                {$datawhere}
                AND cl.school_id = '{$paramArray['school_id']}' 
                AND student_id NOT IN ( SELECT student_id FROM eas_classhour_sturemark AS cs WHERE cs.student_id = sh.student_id AND cs.hour_id = sh.hour_id )
            Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);
        if($NoticeDetail){
            foreach($NoticeDetail as &$val){
                $val['date'] = $val['hour_day'].' '.$val['hour_starttime'].'-'.$val['hour_endtime'];
                $val['type'] = '课程待评价';
                $val['status'] = '待评价';
                $val['name'] = $val['class_cnname'].$val['hour_day'].$val.' '.$val['hour_starttime'].'-'.$val['hour_starttime'];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*) as a
            FROM
                (SELECT
                cl.class_id,
                cl.class_cnname,
                s.school_cnname,
                h.hour_starttime,
                h.hour_endtime,
                h.hour_day
            FROM
                smc_student_hourstudy AS sh
                LEFT JOIN smc_class AS cl ON cl.class_id = sh.class_id
                LEFT JOIN smc_school AS s ON cl.school_id = s.school_id 
                left join smc_class_hour as h on sh.hour_id = h.hour_id
            WHERE
                {$datawhere}
                AND cl.school_id = '{$paramArray['school_id']}' 
                AND student_id NOT IN ( SELECT student_id FROM eas_classhour_sturemark AS cs WHERE cs.student_id = sh.student_id AND cs.hour_id = sh.hour_id )) as b");
        $allnums = $all_num[0]['a'];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }


    /**
     *  获取教师所带的班级
     * author: ling
     * 对应接口文档 0001
     */
    function getStaClassList($paramArray)
    {
        $datawhere = "cs.company_id='{$paramArray['company_id']}' and cs.school_id = '{$paramArray['school_id']}' and s.school_istest = '0' and cs.class_status <> '-2' and cs.class_status <> '-1' and co.course_id > 0";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$paramArray['keyword']}%' or cs.class_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%' or co.course_branch like '%{$paramArray['keyword']}%')";
        }
        //班级类型  0父班1子班
        if (isset($paramArray['class_type']) && $paramArray['class_type'] != '') {
            $datawhere .= " and cs.class_type = '{$paramArray['class_type']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= " and ch.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== '') {
            $datawhere .= " and cs.class_status = '{$paramArray['class_status']}'";
        }
        if (isset($paramArray['re_school_id']) && $paramArray['re_school_id'] !== '') {
            $datawhere .= " and cs.school_id = '{$paramArray['re_school_id']}'";
        }
        if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
            $datawhere .= " and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
        }
        $hvaing = '1=1';
        if (isset($paramArray['classroom_id']) && $paramArray['classroom_id'] !== '') {
            $hvaing .= " and classroom_cnname is not NULL ";
            $datawhere .= " and ch.classroom_id = '{$paramArray['classroom_id']}'";
        }

        if ($paramArray['account_class'] == 0) {
            $postbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join smc_school_postpart as  cp ON  cp.postpart_id = sp.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            $compostbe = $this->DataControl->selectOne("select cp.postpart_isteregulator
             from gmc_staffer_postbe as sp
             left join gmc_company_postrole as  cpl ON  sp.postrole_id = cpl.postrole_id
             left join smc_school_postpart as  cp ON  cp.postpart_id = cpl.postpart_id
             where sp.postbe_id = '{$paramArray['re_postbe_id']}' and  cp.postpart_isteregulator = 1 and cp.postpart_id >0
             ");

            if (!$postbe && !$compostbe) {
                $datawhere .= " and ht.staffer_id = '{$paramArray['staffer_id']}' ";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id = cs.class_id and sch.hour_iswarming = 0 and sch.hour_ischecking = 1  ) as hour_checkingnum,
        $sql = "select ht.class_id, cs.class_cnname,cs.class_enname,cs.class_branch,cs.class_status,cs.class_type,co.course_cnname,s.school_shortname as school_cnname,class_fullnums,co.course_branch,
            (select count(study_isreading) from smc_student_study as sy where sy.class_id = ht.class_id and sy.study_isreading = 1) as study_num,
            (select group_concat( DISTINCT cm.classroom_cnname) from smc_classroom as cm,smc_class_lessonplan AS l where cm.classroom_id = l.classroom_id AND l.class_id = ht.class_id ) as classroom_cnname,
            (select count(cht.teaching_id) from smc_class_hour_teaching as cht where cht.class_id = cs.class_id and cht.staffer_id = ht.staffer_id and cht.teaching_ischecking = 1  ) as hour_checkingnum,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id = cs.class_id and sch.hour_iswarming = 0 and sch.hour_ischecking <> -1  ) as hour_allnum,
            (select count( DISTINCT  sh.hour_branch) from eas_student_hour as sh where sh.class_id = cs.class_id   ) as  hour_branch_num
            from  smc_class_hour_teaching as ht
            left join smc_class_hour as ch ON ch.hour_id = ht.hour_id and ht.class_id = ch.class_id
            left join smc_class as cs ON cs.class_id = ch.class_id
            left join smc_course as co ON co.course_id = cs.course_id
            left join smc_school as s ON s.school_id = cs.school_id
            where {$datawhere}
            group by ht.class_id
            HAVING {$hvaing}
            order by co.course_branch DESC
            Limit {$pagestart},{$num}";

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $allnum = $this->DataControl->selectClear("select ht.class_id,
                    (select group_concat( DISTINCT cm.classroom_cnname) from smc_classroom as cm,smc_class_lessonplan AS l where cm.classroom_id = l.classroom_id AND l.class_id = ht.class_id ) as classroom_cnname
                    from smc_class_hour_teaching as ht
                    left join smc_class_hour as ch ON ch.hour_id = ht.hour_id and ht.class_id = ch.class_id
                    left join smc_class as cs ON cs.class_id = ch.class_id
                    left join smc_course as co ON co.course_id = cs.course_id
                    left join smc_school as s ON s.school_id = cs.school_id
                    where {$datawhere}
                    group by ht.class_id
                     HAVING {$hvaing}
                  ");

            if ($allnum) {
                $data['allnum'] = count($allnum);
            } else {
                $data['allnum'] = 0;
            }
        } else {
            $data['allnum'] = 0;
        }

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $classList = array();
        } else {
            $arr_status = array('0' => '待开班', '1' => '进行中', '-1' => '已结束');
            $class_type = array('0' => '父班', '1' => '子班');
            foreach ($classList as $key => $value) {
                $classList[$key]['class_typename'] = $class_type[$value['class_type']];
                $classList[$key]['class_status_name'] = $arr_status[$value['class_status']];
                $classList[$key]['status'] = false;
                $classList[$key]['student_nums'] = $value['study_num'] . '/' . $value['class_fullnums'];
                $classList[$key]['class_hour_num'] = $value['hour_checkingnum'] . '/' . $value['hour_allnum'];
                $classList[$key]['hour_diffnum'] = ($value['hour_checkingnum'] - $value['hour_branch_num']) < 0 ? 0 : ($value['hour_checkingnum'] - $value['hour_branch_num']);
                $hourlist = $this->DataControl->selectClear("SELECT h.hour_day, CONCAT( o.course_branch,'_',h.hour_lessontimes) as hour_branch FROM smc_class_hour AS h,smc_class AS c,smc_course AS o WHERE c.class_id = h.class_id AND c.course_id = o.course_id and h.hour_ischecking = '1' and c.class_id = '{$value['class_id']}'");
                $num = 0;
                if ($hourlist) {
                    foreach ($hourlist as $val) {
                        $notregister_num = $this->DataControl->selectOne("SELECT
                                                                                COUNT(hour_testscore) as num
                                                                            FROM
                                                                                smc_student_study AS sd
                                                                            LEFT JOIN
                                                                                eas_student_hour AS sh ON sh.student_id = sd.student_id AND sh.class_id = sd.class_id AND sh.hour_branch = '{$val['hour_branch']}'
                                                                            WHERE
                                                                                sd.study_beginday <= '{$val['hour_day']}' AND ( sd.study_isreading = '1' OR sd.study_endday > '{$val['hour_day']}' ) AND sd.class_id = '{$value['class_id']}'");
                        if (!$notregister_num['num']) {
                            $num += 1;
                        }
                    }
                }
                $classList[$key]['notregister_num'] = $num;
            }
        }

        $data['list'] = $classList;
        return $data;
    }

}