<?php

namespace Model\Easx;

Class EvaluateModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 班级统计列表
     */
    function EvaluateList($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}'";
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and h.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and h.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and h.hour_day >= '{$week['nowweek_start']}' and h.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and h.hour_day >= '{$month[0]}' and h.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and h.hour_day >= '{$lastmonth[0]}' and h.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !==''){
            $datawhere .= " and co.course_id = '{$paramArray['course_id']}'";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] !==''){
            $datawhere .= " and c.school_id = '{$paramArray['school_id']}'";
        }
        if(isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !==''){
            $datawhere .= " and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
        }else{
            if($paramArray['account_class'] !== '1'){
                $datawhere .= " and ht.staffer_id='{$this->staffer_id}'";
            }
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as staffer_cnname,
                (SELECT COUNT(t.teaching_id) FROM smc_class_hour_teaching as t WHERE t.class_id=ht.class_id) as hour_num,
                (SELECT COUNT(sh.hourstudy_id) FROM smc_student_hourstudy as sh WHERE sh.hourstudy_checkin='1' and sh.class_id=c.class_id) as hourstudy_num,
                (SELECT COUNT( q.sturemark_id ) FROM
                    ( SELECT sm.sturemark_id, sm.staffer_id, sm.class_id FROM eas_classhour_sturemark AS sm GROUP BY sm.student_id ) AS q WHERE q.class_id = ht.class_id
                ) AS evaluate_num,
                (SELECT COUNT(hc.hourcomment_id) FROM smc_class_hour as ch LEFT JOIN eas_student_hourcomment AS hc ON hc.hour_id = ch.hour_id WHERE ch.class_id=c.class_id ) AS return_num
                FROM smc_class_hour_teaching AS ht
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id
                WHERE {$datawhere} AND c.class_status <> '-2' GROUP BY c.class_id";debug($sql);die;

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['class_id'] = $dateexcelvar['class_id'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['hour_num'] = $dateexcelvar['hour_num'];
                    $datearray['hourstudy_num'] = $dateexcelvar['hourstudy_num'];
                    $datearray['evaluate_num'] = $dateexcelvar['evaluate_num'];
                    $datearray['return_num'] = $dateexcelvar['return_num'];
                    if($dateexcelvar['hourstudy_num']){
                        $datearray['evaluate_rate'] = round(($dateexcelvar['evaluate_num'] / $dateexcelvar['hourstudy_num']) * 100, 2) . '%';
                        $datearray['return_rate'] = round(($dateexcelvar['return_num'] / $dateexcelvar['hourstudy_num']) * 100, 2) . '%';
                    }else{
                        $datearray['evaluate_rate'] = '0%';
                        $datearray['return_rate'] = '0%';
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("班级ID", "班级中文名", "班级别名", "班级编号", "教师", "上课次数", "出勤人数", "评价人数", "评价率", "回评人数", "回评率");
            $excelfileds = array('class_id', 'class_cnname', 'class_enname', 'class_branch', 'staffer_cnname', 'hour_num', 'hourstudy_num', 'evaluate_num', 'evaluate_rate', 'return_num', 'return_rate');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "班级统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    if($v['hourstudy_num']){
                        $v['evaluate_rate'] = round(($v['evaluate_num'] / $v['hourstudy_num']) * 100, 2) . '%';
                        $v['return_rate'] = round(($v['return_num'] / $v['hourstudy_num']) * 100, 2) . '%';
                    }else{
                        $v['evaluate_rate'] = '0%';
                        $v['return_rate'] = '0%';
                    }
                }
            }else{
                $datalist = array();
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.class_id) as num FROM 
                                                        (SELECT c.class_id
                                                        FROM smc_class_hour_teaching AS ht
                                                        LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                        LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                        LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                        WHERE {$datawhere} AND c.class_status <> '-2' GROUP BY c.class_id) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 教师统计列表
     */
    function StafferList($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}'";
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and h.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and h.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and h.hour_day >= '{$week['nowweek_start']}' and h.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and h.hour_day >= '{$month[0]}' and h.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and h.hour_day >= '{$lastmonth[0]}' and h.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !==''){
            $datawhere .= " and co.course_id = '{$paramArray['course_id']}'";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] !==''){
            $datawhere .= " and c.school_id = '{$paramArray['school_id']}'";
        }
        if(isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !==''){
            $datawhere .= " and sf.staffer_id = '{$paramArray['re_staffer_id']}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        if(isset($paramArray['class_id']) && $paramArray['class_id'] !== ''){
            $sql = "SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,h.hour_day,h.hour_starttime,h.hour_endtime,
                (SELECT COUNT(t.teaching_id) FROM smc_class_hour_teaching as t WHERE t.hour_id=h.hour_id) as hour_num,
                (SELECT COUNT(DISTINCT sh.student_id) FROM smc_student_hourstudy AS sh WHERE sh.hourstudy_checkin = '1' AND sh.hour_id = ht.hour_id) AS hourstudy_num,
                ( SELECT COUNT( sm.sturemark_id ) FROM eas_classhour_sturemark AS sm WHERE sm.hour_id = ht.hour_id ) AS evaluate_num,
                (SELECT COUNT(hc.hourcomment_id) FROM eas_student_hourcomment as hc WHERE hc.hour_id=ht.hour_id) as return_num
                FROM smc_staffer as sf
                LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                WHERE c.class_id='{$paramArray['class_id']}'";
        } elseif (isset($paramArray['rs_staffer_id']) && $paramArray['rs_staffer_id'] !=='') {
            $sql = "SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,h.hour_day,h.hour_starttime,h.hour_endtime,
                (SELECT COUNT(t.teaching_id) FROM smc_class_hour_teaching as t WHERE t.hour_id=h.hour_id) as hour_num,
                (SELECT COUNT(DISTINCT sh.student_id) FROM smc_student_hourstudy AS sh WHERE sh.hourstudy_checkin = '1' AND sh.hour_id = ht.hour_id) AS hourstudy_num,
                ( SELECT COUNT( sm.sturemark_id ) FROM eas_classhour_sturemark AS sm WHERE sm.hour_id = ht.hour_id ) AS evaluate_num,
                (SELECT COUNT(hc.hourcomment_id) FROM eas_student_hourcomment as hc WHERE hc.hour_id=ht.hour_id) as return_num
                FROM smc_staffer as sf
                LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                WHERE sf.staffer_id='{$paramArray['rs_staffer_id']}'";
        }else{
            $sql = "SELECT sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,h.hour_day,h.hour_starttime,h.hour_endtime,
                (SELECT COUNT(t.teaching_id) FROM smc_class_hour_teaching as t WHERE t.staffer_id = sf.staffer_id) as hour_num,
                (SELECT COUNT(DISTINCT sh.student_id ) FROM smc_class_hour_teaching AS ct LEFT JOIN smc_student_hourstudy AS sh ON sh.hour_id = ct.hour_id WHERE sh.hourstudy_checkin = '1' AND ct.staffer_id = sf.staffer_id) AS hourstudy_num,
                (SELECT COUNT( sm.student_id ) FROM eas_classhour_sturemark AS sm WHERE sm.staffer_id = sf.staffer_id) AS evaluate_num,
                (SELECT
                    COUNT( hc.student_id )
                FROM
                    smc_class_teach AS ct
                    LEFT JOIN smc_class_hour AS ch ON ch.class_id = ct.class_id
                    LEFT JOIN eas_student_hourcomment AS hc ON hc.hour_id = ch.hour_id
                WHERE
                    ct.staffer_id = sf.staffer_id
                ) AS return_num
                FROM smc_staffer as sf
                LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                WHERE {$datawhere} GROUP BY sf.staffer_id";
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['staffer_id'] = $dateexcelvar['staffer_id'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['hour_num'] = $dateexcelvar['hour_num'];
                    $datearray['hourstudy_num'] = $dateexcelvar['hourstudy_num'];
                    $datearray['evaluate_num'] = $dateexcelvar['evaluate_num'];
                    $datearray['return_num'] = $dateexcelvar['return_num'];
                    if($dateexcelvar['hourstudy_num']){
                        $datearray['evaluate_rate'] = round(($dateexcelvar['evaluate_num'] / $dateexcelvar['hourstudy_num']) * 100, 2) . '%';
                        $datearray['return_rate'] = round(($dateexcelvar['return_num'] / $dateexcelvar['hourstudy_num']) * 100, 2) . '%';
                    }else{
                        $datearray['evaluate_rate'] = '0%';
                        $datearray['return_rate'] = '0%';
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("教师ID", "教师中文名", "教师英文名", "上课次数", "出勤人数", "评价人数", "评价率", "回评人数", "回评率");
            $excelfileds = array('staffer_id', 'staffer_cnname', 'staffer_enname', 'hour_num', 'hourstudy_num', 'evaluate_num', 'evaluate_rate', 'return_num', 'return_rate');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "教师统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    if($v['hourstudy_num']){
                        $v['evaluate_rate'] = round(($v['evaluate_num'] / $v['hourstudy_num']) * 100, 2) . '%';
                        $v['return_rate'] = round(($v['return_num'] / $v['hourstudy_num']) * 100, 2) . '%';
                    }else{
                        $v['evaluate_rate'] = '0%';
                        $v['return_rate'] = '0%';
                    }
                    if((isset($paramArray['class_id']) && $paramArray['class_id'] !=='') || (isset($paramArray['rs_staffer_id']) && $paramArray['rs_staffer_id'] !=='')){
                        $v['hour_time'] = $v['hour_day'] .' '. $v['hour_starttime'] .'-'. $v['hour_endtime'];
                    }
                }
            }else{
                $datalist = array();
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            if(isset($paramArray['class_id']) && $paramArray['class_id'] !== ''){
                $all_num = $this->DataControl->selectOne("SELECT COUNT(sf.staffer_id) as num FROM smc_staffer as sf
                                                            LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                                                            LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                            LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                            LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                                                            WHERE c.class_id='{$paramArray['class_id']}'");
            } elseif (isset($paramArray['rs_staffer_id']) && $paramArray['rs_staffer_id'] !=='') {
                $all_num = $this->DataControl->selectOne("SELECT COUNT(sf.staffer_id) as num
                                                            FROM smc_staffer as sf
                                                            LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                                                            LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                            LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                            LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                                                            WHERE sf.staffer_id='{$paramArray['rs_staffer_id']}'");
            }else{
                $all_num = $this->DataControl->selectOne("SELECT COUNT(q.staffer_id) as num FROM
                                                            (SELECT sf.staffer_id
                                                            FROM smc_staffer as sf
                                                            LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                                                            LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                            LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                            LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                                                            WHERE {$datawhere} GROUP BY sf.staffer_id) as q");
            }
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 学员统计列表
     */
    function StudentList($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}' and sd.student_id IS NOT NULL and st.study_isreading = '1'";
        if($paramArray['account_class'] !== '1'){
            $datawhere .= " and ht.staffer_id='{$this->staffer_id}'";
        }
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and h.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and h.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and h.hour_day >= '{$week['nowweek_start']}' and h.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and h.hour_day >= '{$month[0]}' and h.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and h.hour_day >= '{$lastmonth[0]}' and h.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sd.student_cnname like '%{$paramArray['keyword']}%' or sd.student_enname like '%{$paramArray['keyword']}%' or sd.student_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !==''){
            $datawhere .= " and sc.course_id = '{$paramArray['course_id']}'";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] !==''){
            $datawhere .= " and c.school_id = '{$paramArray['school_id']}'";
        }
        if(isset($paramArray['class_id']) && $paramArray['class_id'] !==''){
            $datawhere .= " and ht.class_id = '{$paramArray['class_id']}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT sd.student_id,sd.student_cnname,sd.student_enname,sd.student_branch,c.class_cnname,c.class_enname,sc.course_cnname,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_content,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as staffer_cnname,
                (SELECT ROUND(AVG(sr.sturemarkstar_score), 1)
                  FROM eas_classhour_sturemark as cs
                  LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                  WHERE cs.hour_id = ht.hour_id AND cs.class_id = ht.class_id AND cs.student_id = sd.student_id
                ) as score_num
                FROM smc_class_hour_teaching as ht 
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_course as sc ON sc.course_id = c.course_id
                LEFT JOIN smc_student_study as st ON st.class_id = ht.class_id
                LEFT JOIN smc_student as sd ON sd.student_id = st.student_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id
                WHERE {$datawhere} GROUP BY sd.student_id";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['hour_time'] = $dateexcelvar['hour_day'] .' '. $dateexcelvar['hour_starttime'] .'-'. $dateexcelvar['hour_endtime'];
                    $datearray['hour_content'] = $dateexcelvar['hour_content'];
                    $datearray['score_num'] = $dateexcelvar['score_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("学员ID", "学员中文名", "学员英文名", "学员编号", "班级", "课程别", "上课教师", "上课时间", "上课内容", "学习意愿");
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'course_cnname', 'staffer_cnname', 'hour_time', 'hour_content', 'score_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "学员统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    $v['hour_time'] = $v['hour_day'] .' '. $v['hour_starttime'] .'-'. $v['hour_endtime'];
                    $v['staffer_appraise'] = '';
                    if($v['staffer_enname']){
                        $v['staffer_cnname'] = $v['staffer_cnname'] . '-' . $v['staffer_enname'];
                    }
                }
            }else{
                $datalist = array();
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM 
                                                        (SELECT sd.student_id
                                                        FROM smc_class_hour_teaching as ht
                                                        LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                        LEFT JOIN smc_course as sc ON sc.course_id=c.course_id
                                                        LEFT JOIN smc_student_study as st ON st.class_id = ht.class_id
                                                        LEFT JOIN smc_student as sd ON sd.student_id = st.student_id
                                                        LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                                                        WHERE {$datawhere} GROUP BY sd.student_id) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 查看评价状况
     */
    function CevaluateState($paramArray){
        if (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '0') {
            $sql = "SELECT
                        st.student_id,
                        st.student_img,
                        st.student_cnname,
                        st.student_sex
                    FROM
                        smc_student_hourstudy AS h
                        LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                    WHERE
                        h.class_id = '{$paramArray['class_id']}'
                        AND h.hourstudy_checkin = '1'
                        AND h.student_id IN ( SELECT sm.student_id FROM eas_classhour_sturemark AS sm WHERE sm.class_id = h.class_id  )
                    GROUP BY
                        st.student_id ";
        } elseif (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '1') {
            $sql = "SELECT
                        st.student_id,
                        st.student_img,
                        st.student_cnname,
                        st.student_sex
                    FROM
                        smc_student_hourstudy AS h
                        LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                    WHERE
                        h.class_id = '{$paramArray['class_id']}'
                        AND h.hourstudy_checkin = '1'
                        AND h.student_id IN ( SELECT hc.student_id FROM smc_class_hour AS ch LEFT JOIN eas_student_hourcomment AS hc ON hc.hour_id = ch.hour_id WHERE ch.class_id =  h.class_id )
                    GROUP BY
                        st.student_id";
        } elseif (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '2') {
            $sql = "SELECT
                        st.student_id,
                        st.student_img,
                        st.student_cnname,
                        st.student_sex
                   FROM
                        smc_student_hourstudy AS h
                        LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                   WHERE
                        h.class_id = '{$paramArray['class_id']}'
                        AND h.hourstudy_checkin = '1'
                   GROUP BY
                        st.student_id";
        }
        $attendance = $this->DataControl->selectClear($sql);
        if(!$attendance){
            $attendance = array();
        }

        $evaluate_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM
                                                        (SELECT
                                                            st.student_id
                                                        FROM
                                                            smc_student_hourstudy AS h
                                                            LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                                                        WHERE
                                                            h.class_id = '{$paramArray['class_id']}'
                                                            AND h.hourstudy_checkin = '1'
                                                            AND h.student_id IN ( SELECT sm.student_id FROM eas_classhour_sturemark AS sm WHERE sm.class_id = h.class_id  )
                                                        GROUP BY
                                                            st.student_id) as q");

        $return_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM
                                                       (SELECT
                                                            st.student_id
                                                       FROM
                                                            smc_student_hourstudy AS h
                                                            LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                                                       WHERE
                                                            h.class_id = '{$paramArray['class_id']}'
                                                            AND h.hourstudy_checkin = '1'
                                                            AND h.student_id IN ( SELECT hc.student_id FROM smc_class_hour AS ch LEFT JOIN eas_student_hourcomment AS hc ON hc.hour_id = ch.hour_id WHERE ch.class_id =  h.class_id )
                                                       GROUP BY
                                                            st.student_id) as q");

        $attendance_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM
                                                           (SELECT
                                                                st.student_id
                                                           FROM
                                                                smc_student_hourstudy AS h
                                                                LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                                                           WHERE
                                                                h.class_id = '{$paramArray['class_id']}'
                                                                AND h.hourstudy_checkin = '1'
                                                           GROUP BY
                                                                st.student_id) as q");

        $data = array();
        $data['list'] = $attendance;
        $data['evaluate_num'] = $evaluate_num['num'];
        $data['return_num'] = $return_num['num'];
        $data['attendance_num'] = $attendance_num['num'];

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 查看评价状况
     */
    function SevaluateState($paramArray){
        if (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '1') {
            $sql = "SELECT
                        st.student_id,
                        st.student_img,
                        st.student_cnname,
                        st.student_sex
                    FROM
                        smc_class_teach AS ct
                        LEFT JOIN smc_student_hourstudy AS h ON h.class_id = ct.class_id
                        LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                    WHERE
                        ct.staffer_id = '{$paramArray['re_staffer_id']}'
                        AND h.hourstudy_checkin = '1'
                        AND h.student_id IN ( SELECT sm.student_id FROM eas_classhour_sturemark AS sm WHERE sm.hour_id = h.hour_id AND sm.staffer_id = ct.staffer_id )";
        } elseif (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '0') {
            $sql = "SELECT
                        st.student_id,
                        st.student_img,
                        st.student_cnname,
                        st.student_sex
                    FROM
                        smc_class_teach AS ct
                        LEFT JOIN smc_student_hourstudy AS h ON h.class_id = ct.class_id
                        LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                    WHERE
                        ct.staffer_id = '{$paramArray['re_staffer_id']}'
                        AND h.hourstudy_checkin = '1'
                        AND h.student_id IN ( SELECT hc.student_id FROM eas_student_hourcomment AS hc WHERE hc.hour_id = h.hour_id )";
        } elseif (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '2') {
            $sql = "SELECT
                        st.student_id,
                        st.student_img,
                        st.student_cnname,
                        st.student_sex
                   FROM
                        smc_class_teach AS ct
                        LEFT JOIN smc_student_hourstudy AS h ON h.class_id = ct.class_id
                        LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                   WHERE
                        ct.staffer_id = '{$paramArray['re_staffer_id']}'
                        AND h.hourstudy_checkin = '1'
                   GROUP BY
                        st.student_id";
        }
        $attendance = $this->DataControl->selectClear($sql);
        if(!$attendance){
            $attendance = array();
        }

        $evaluate_num = $this->DataControl->selectOne("SELECT
                                                            COUNT(st.student_id) as num
                                                        FROM
                                                            smc_class_teach AS ct
                                                            LEFT JOIN smc_student_hourstudy AS h ON h.class_id = ct.class_id
                                                            LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                                                        WHERE
                                                            ct.staffer_id = '{$paramArray['re_staffer_id']}'
                                                            AND h.hourstudy_checkin = '1'
                                                            AND h.student_id IN ( SELECT sm.student_id FROM eas_classhour_sturemark AS sm WHERE sm.hour_id = h.hour_id AND sm.staffer_id = ct.staffer_id)");

        $return_num = $this->DataControl->selectOne("SELECT
                                                            COUNT(st.student_id) as num
                                                       FROM
                                                            smc_class_teach AS ct
                                                            LEFT JOIN smc_student_hourstudy AS h ON h.class_id = ct.class_id
                                                            LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                                                       WHERE
                                                            ct.staffer_id = '{$paramArray['re_staffer_id']}'
                                                            AND h.hourstudy_checkin = '1'
                                                            AND h.student_id IN ( SELECT hc.student_id FROM eas_student_hourcomment AS hc WHERE hc.hour_id = h.hour_id )");

        $attendance_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM
                                                           (SELECT
                                                                st.student_id
                                                           FROM
                                                                smc_class_teach AS ct
                                                                LEFT JOIN smc_student_hourstudy AS h ON h.class_id = ct.class_id
                                                                LEFT JOIN smc_student AS st ON st.student_id = h.student_id
                                                           WHERE
                                                                ct.staffer_id = '{$paramArray['re_staffer_id']}'
                                                                AND h.hourstudy_checkin = '1'
                                                           GROUP BY
                                                                st.student_id) as q");

        $data = array();
        $data['list'] = $attendance;
        $data['evaluate_num'] = $evaluate_num['num'];
        $data['return_num'] = $return_num['num'];
        $data['attendance_num'] = $attendance_num['num'];

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 查看评价明细
     */
    function EvaluateDetail($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT s.student_id,s.student_cnname,s.student_img,c.class_id,c.class_cnname,h.hour_id,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_content,
                                                   concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as staffer_cnname
                                                   FROM smc_student as s
                                                   LEFT JOIN smc_student_hourstudy as hs ON hs.student_id = s.student_id
                                                   LEFT JOIN smc_class as c ON c.class_id = hs.class_id
                                                   LEFT JOIN smc_class_hour as h ON h.hour_id = hs.hour_id
                                                   LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id = hs.class_id AND ht.hour_id = h.hour_id
                                                   LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id
                                                   WHERE s.student_id='{$paramArray['student_id']}'");
        if($dataOne){
            $dataOne['hour_time'] = $dataOne['hour_day'] .' '. $dataOne['hour_starttime'] .'-'. $dataOne['hour_endtime'];
        }else{
            $dataOne = array();
        }

        $staffer = $this->DataControl->selectClear("SELECT sr.sturemarkstar_name,sr.sturemarkstar_score,cs.sturemark_comment,cs.sturemark_picturejson
                                                     FROM eas_classhour_sturemark as cs
                                                     LEFT JOIN eas_classhour_sturemarkstar as sr ON sr.sturemark_id=cs.sturemark_id
                                                     WHERE cs.student_id='{$dataOne['student_id']}' AND cs.hour_id='{$dataOne['hour_id']}' AND cs.class_id='{$dataOne['class_id']}'");

        $data = array();
        $data['list'] = $dataOne;
        $data['staffer'] = $staffer;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 评价查询
     */
    function EvaluateQuery($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}' and sc.school_istest = '0'";
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and h.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and h.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and h.hour_day >= '{$week['nowweek_start']}' and h.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and h.hour_day >= '{$month[0]}' and h.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and h.hour_day >= '{$lastmonth[0]}' and h.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_enname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%' or f.family_mobile like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['class_id']) && $paramArray['class_id'] !==''){
            $datawhere .= " and ht.class_id = '{$paramArray['class_id']}'";
        }
        if(isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !==''){
            $datawhere .= " and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
        }else{
            if($paramArray['account_class'] !== '1'){
                $datawhere .= " and ht.staffer_id='{$this->staffer_id}'";
            }
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT hc.hourcomment_id,hc.hourcomment_score,hc.hourcomment_content,hc.hourcomment_level,hc.hourcomment_anonymous,hc.hourcomment_createtime,st.student_cnname,st.student_enname,st.student_branch,c.class_cnname,c.class_enname,h.hour_day,h.hour_starttime,h.hour_endtime,f.family_mobile,
                (SELECT sf.staffer_cnname FROM smc_staffer as sf WHERE sf.staffer_id=ht.staffer_id) as staffer_cnname,
                (SELECT GROUP_CONCAT(n.noun_word) FROM eas_student_hourcomment_noun_apply as a LEFT JOIN eas_student_hourcomment_noun as n ON n.noun_id = a.noun_id WHERE a.hourcomment_id = hc.hourcomment_id) as noun_word
                FROM eas_student_hourcomment as hc
                LEFT JOIN smc_student_hourstudy as hs ON hs.student_id=hc.student_id
                LEFT JOIN smc_student as st ON st.student_id=hc.student_id
                LEFT JOIN smc_student_family as f ON f.student_id=st.student_id
                LEFT JOIN smc_class as c ON c.class_id = hs.class_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = hc.hour_id
                LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id = hs.class_id
                LEFT JOIN smc_school as sc ON sc.school_id = c.school_id
                WHERE {$datawhere} AND hs.hourstudy_checkin='1' GROUP BY hc.hourcomment_id";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['hourcomment_id'] = $dateexcelvar['hourcomment_id'];
                    $datearray['hour_time'] = $dateexcelvar['hour_day'] .' '. $dateexcelvar['hour_starttime'] .'-'. $dateexcelvar['hour_endtime'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['hourcomment_score'] = $dateexcelvar['hourcomment_score'];
                    $datearray['hourcomment_content'] = $dateexcelvar['hourcomment_content'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['family_mobile'] = $dateexcelvar['family_mobile'];
                    $datearray['hourcomment_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['hourcomment_createtime']);
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("评价ID", "上课时间", "班级中文名", "班级别名", "教师", "评分", "评价内容", "学员中文名", "学员英文名", "学员编号", "主要联系手机", "创建时间");
            $excelfileds = array('hourcomment_id', 'hour_time', 'class_cnname','class_enname', 'staffer_cnname', 'hourcomment_score', 'hourcomment_content', 'student_cnname', 'student_enname', 'student_branch', 'family_mobile', 'hourcomment_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "评价统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    if($v['hourcomment_level'] == '1'){
                        $v['hourcomment_level_name'] = '一般';
                    }elseif($v['hourcomment_level'] == '2'){
                        $v['hourcomment_level_name'] = '不满意';
                    }else{
                        $v['hourcomment_level_name'] = '非常满意';
                    }
                    if($v['hourcomment_anonymous']){
                        $v['hourcomment_anonymous_name'] = '是';
                    }else{
                        $v['hourcomment_anonymous_name'] = '否';
                    }
                    $v['hour_time'] = $v['hour_day'] .' '. $v['hour_starttime'] .'-'. $v['hour_endtime'];
                    $v['hourcomment_createtime'] = date("Y-m-d H:i:s", $v['hourcomment_createtime']);
                }
            }else{
                $datalist = array();
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.hourcomment_id) as num FROM 
                                                    (SELECT hc.hourcomment_id
                                                    FROM eas_student_hourcomment as hc
                                                    LEFT JOIN smc_student_hourstudy as hs ON hs.student_id=hc.student_id
                                                    LEFT JOIN smc_student as st ON st.student_id=hc.student_id
                                                    LEFT JOIN smc_student_family as f ON f.student_id=st.student_id
                                                    LEFT JOIN smc_class as c ON c.class_id = hs.class_id
                                                    LEFT JOIN smc_class_hour as h ON h.hour_id = hc.hour_id
                                                    LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id = hs.class_id
                                                    LEFT JOIN smc_school as sc ON sc.school_id = c.school_id
                                                    WHERE {$datawhere} AND hs.hourstudy_checkin='1' GROUP BY hc.hourcomment_id) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 评价统计
     */
    function ScoreTotal($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}'";
        if(isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !==''){
            $datawhere .= " and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
        }else{
            if($paramArray['account_class'] !== '1'){
                $datawhere .= " and ht.staffer_id='{$this->staffer_id}'";

                if(isset($paramArray['school_id']) && $paramArray['school_id'] !==''){
                    $datawhere .= " and c.school_id='{$paramArray['school_id']}'";
                }
            }
        }
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and h.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and h.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and h.hour_day >= '{$week['nowweek_start']}' and h.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and h.hour_day >= '{$month[0]}' and h.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and h.hour_day >= '{$lastmonth[0]}' and h.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT c.class_cnname,c.class_enname,h.hour_day,h.hour_starttime,h.hour_endtime,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as staffer_cnname,
                concat(f.staffer_cnname,(CASE WHEN ifnull( f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', f.staffer_enname ) END ) ) as fu_staffer_cnname,
                (SELECT COUNT(q.hourstudy_id) FROM
                    (SELECT sh.hourstudy_id,sh.class_id
                    FROM smc_student_hourstudy as sh
                    WHERE sh.hourstudy_checkin='1'
                    GROUP BY sh.student_id
                    ) as q WHERE q.class_id=hs.class_id
                ) as hourstudy_num,
                (SELECT COUNT(sh.hourcomment_id) FROM smc_class_hour AS ch LEFT JOIN eas_student_hourcomment AS sh ON sh.hour_id = ch.hour_id WHERE ch.class_id = hs.class_id) AS hourcomment_num,
                (SELECT COUNT(sh.hourcomment_id) FROM smc_class_hour AS ch LEFT JOIN eas_student_hourcomment AS sh ON sh.hour_id = ch.hour_id WHERE ch.class_id = hs.class_id AND sh.hourcomment_level = '0') AS hourcomment_satisfied_num,
                (SELECT COUNT(sh.hourcomment_id) FROM smc_class_hour AS ch LEFT JOIN eas_student_hourcomment AS sh ON sh.hour_id = ch.hour_id WHERE ch.class_id = hs.class_id AND sh.hourcomment_level = '1') AS hourcomment_soso_num,
                (SELECT COUNT(sh.hourcomment_id) FROM smc_class_hour AS ch LEFT JOIN eas_student_hourcomment AS sh ON sh.hour_id = ch.hour_id WHERE ch.class_id = hs.class_id AND sh.hourcomment_level = '2') AS hourcomment_dissatisfied_num
                FROM eas_student_hourcomment as hc
                LEFT JOIN smc_student_hourstudy as hs ON hs.student_id = hc.student_id
                LEFT JOIN smc_class as c ON c.class_id = hs.class_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = hc.hour_id
                LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id = hs.class_id AND ht.hour_id = h.hour_id
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id
                LEFT JOIN smc_staffer as f ON f.staffer_id = ht.staffer_id
                WHERE {$datawhere} AND hs.hourstudy_checkin='1' GROUP BY hc.hour_id ORDER BY hc.hourcomment_id";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['hour_time'] = $dateexcelvar['hour_day'] .' '. $dateexcelvar['hour_starttime'] .'-'. $dateexcelvar['hour_endtime'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['fu_staffer_cnname'] = $dateexcelvar['fu_staffer_cnname'];
                    $datearray['hourstudy_num'] = $dateexcelvar['hourstudy_num'];
                    $datearray['hourcomment_num'] = $dateexcelvar['hourcomment_num'];
                    $datearray['hourcomment_satisfied_num'] = $dateexcelvar['hourcomment_satisfied_num'];
                    $datearray['hourcomment_soso_num'] = $dateexcelvar['hourcomment_soso_num'];
                    $datearray['hourcomment_dissatisfied_num'] = $dateexcelvar['hourcomment_dissatisfied_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("上课时间", "班级中文名", "班级别名", "主教老师", "助教老师", "出勤人次", "评价人数", "非常满意", "一般", "不满意");
            $excelfileds = array('hour_time', 'class_cnname', 'class_enname', 'staffer_cnname','fu_staffer_cnname', 'hourstudy_num', 'hourcomment_num', 'hourcomment_satisfied_num', 'hourcomment_soso_num', 'hourcomment_dissatisfied_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "评价统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    $v['hour_time'] = $v['hour_day'] .' '. $v['hour_starttime'] .'-'. $v['hour_endtime'];
                }
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.hourcomment_id) as num FROM
                                                        (SELECT hc.hourcomment_id
                                                        FROM eas_student_hourcomment as hc
                                                        LEFT JOIN smc_student_hourstudy as hs ON hs.student_id = hc.student_id
                                                        LEFT JOIN smc_class as c ON c.class_id = hs.class_id
                                                        LEFT JOIN smc_class_hour as h ON h.hour_id = hc.hour_id
                                                        LEFT JOIN smc_class_hour_teaching as ht ON ht.class_id = hs.class_id
                                                        WHERE {$datawhere} AND hs.hourstudy_checkin='1' GROUP BY hc.hour_id) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 出勤学员评价详情
     */
    function Attendance($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or st.student_cnname like '%{$paramArray['keyword']}%' or st.student_enname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%' or hc.hourcomment_content like '%{$paramArray['keyword']}%')";
        }
        $having = "1=1";
        if (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '0') {
            $having .= " and is_evaluate IS NUll";
        }elseif (isset($paramArray['is_evaluate']) && $paramArray['is_evaluate'] == '1') {
            $having .= " and is_evaluate IS NOT NUll";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT st.student_id,st.student_cnname,st.student_enname,st.student_branch,hc.hourcomment_score,hc.hourcomment_content,h.hour_day,h.hour_starttime,h.hour_endtime,c.class_cnname,c.class_enname,
                (SELECT hc.student_id FROM eas_student_hourcomment AS hc WHERE hc.hour_id = h.hour_id AND hc.student_id = hs.student_id) as is_evaluate
                FROM smc_class_hour_teaching as ht 
                LEFT JOIN smc_student_hourstudy as hs ON hs.class_id=ht.class_id AND hs.hour_id = ht.hour_id
                LEFT JOIN smc_student as st ON st.student_id=hs.student_id
                LEFT JOIN eas_student_hourcomment as hc ON hc.student_id=st.student_id
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = hs.hour_id
                WHERE {$datawhere} AND ht.staffer_id='{$this->staffer_id}' AND hs.hourstudy_checkin='1' GROUP BY st.student_id HAVING {$having} ORDER BY st.student_id ASC";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['hour_time'] = $dateexcelvar['hour_day'] .' '. $dateexcelvar['hour_starttime'] .'-'. $dateexcelvar['hour_endtime'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['hourcomment_score'] = $dateexcelvar['hourcomment_score'];
                    $datearray['hourcomment_content'] = $dateexcelvar['hourcomment_content'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("学员ID", "班级", "上课时间", "学员中文名", "学员英文名", "学员编号", "评分", "评价内容");
            $excelfileds = array('student_id', 'class_cnname', 'hour_time','student_cnname', 'student_enname', 'student_branch', 'hourcomment_score', 'hourcomment_content');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "出勤学员评价统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    $v['hour_time'] = $v['hour_day'] .' '. $v['hour_starttime'] .'-'. $v['hour_endtime'];
                }
            }else{
                $datalist = array();
            }
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM 
                                                    (SELECT st.student_id,
                                                    (SELECT sm.sturemark_id FROM eas_classhour_sturemark as sm WHERE sm.student_id=st.student_id AND sm.hour_id=h.hour_id) as is_evaluate
                                                    FROM smc_class_hour_teaching as ht 
                                                    LEFT JOIN smc_student_hourstudy as hs ON hs.class_id=ht.class_id AND hs.hour_id = ht.hour_id
                                                    LEFT JOIN smc_student as st ON st.student_id=hs.student_id
                                                    LEFT JOIN eas_student_hourcomment as hc ON hc.student_id=st.student_id
                                                    LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                    LEFT JOIN smc_class_hour as h ON h.hour_id = hc.hour_id
                                                    WHERE {$datawhere} AND ht.staffer_id='{$this->staffer_id}' AND hs.hourstudy_checkin='1' GROUP BY st.student_id HAVING {$having}) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 教师+班级评价统计
     */
    function ScoreTotalTwo($paramArray){
        $datawhere = "c.company_id = '{$this->company_id}'";
        if(isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !==''){
            $datawhere .= " and sf.staffer_id = '{$paramArray['re_staffer_id']}'";
        }else{
            if($paramArray['account_class'] !== '1'){
                $datawhere .= " and sf.staffer_id='{$this->staffer_id}'";

                if(isset($paramArray['school_id']) && $paramArray['school_id'] !==''){
                    $datawhere .= " and c.school_id='{$paramArray['school_id']}'";
                }
            }
        }
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and h.hour_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and h.hour_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and h.hour_day >= '{$week['nowweek_start']}' and h.hour_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and h.hour_day <= CURDATE() and h.hour_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and h.hour_day >= '{$month[0]}' and h.hour_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and h.hour_day >= '{$lastmonth[0]}' and h.hour_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT sf.staffer_id,c.class_cnname,c.class_enname,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as staffer_cnname,
                (SELECT COUNT(q.hourstudy_id) FROM 
                  (SELECT sh.hourstudy_id,sh.class_id
                  FROM smc_student_hourstudy as sh 
                  WHERE sh.hourstudy_checkin='1'
                  GROUP BY sh.student_id
                  ) as q WHERE q.class_id=ht.class_id
                ) as hourstudy_num,
                (SELECT COUNT(q.hourstudy_id) FROM
                  (SELECT sh.hourstudy_id,sh.class_id
                  FROM smc_student_hourstudy as sh
                  GROUP BY sh.student_id
                  ) as q WHERE q.class_id=ht.class_id
                ) as study_num,
                (SELECT COUNT(hc.hourcomment_id)
                    FROM smc_student_hourstudy AS h
                    LEFT JOIN eas_student_hourcomment AS hc ON hc.student_id = h.student_id AND hc.hour_id = h.hour_id
                    WHERE h.class_id = ht.class_id AND h.hourstudy_checkin = '1' AND hc.hourcomment_id IS NOT NULL
                ) AS evaluate_num,
                (SELECT ROUND(AVG(hc.hourcomment_score), 1)
                  FROM smc_student_hourstudy AS hs
                  LEFT JOIN eas_student_hourcomment AS hc ON hc.student_id = hs.student_id AND hc.hour_id = hs.hour_id
                  WHERE hs.class_id = c.class_id
                ) as average_score
                FROM  smc_staffer as sf
                LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                WHERE {$datawhere} AND c.class_id > 0 AND c.class_status <> '-2' GROUP BY c.class_id";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['staffer_id'] = $dateexcelvar['staffer_id'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['hourstudy_num'] = $dateexcelvar['hourstudy_num'];
                    $datearray['evaluate_num'] = $dateexcelvar['evaluate_num'];
                    if($dateexcelvar['hourstudy_num']){
                        $datearray['evaluate_rate'] = sprintf("%.4f",$dateexcelvar['evaluate_num'] / $dateexcelvar['study_num']) * 100 . '%';
                    }else{
                        $datearray['evaluate_rate'] = '0%';
                    }
                    $datearray['average_score'] = $dateexcelvar['average_score'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("教师ID", "教师中文名", "教师英文名", "出勤人数", "收到评价数", "评价率", "平均分");
            $excelfileds = array('staffer_id', 'staffer_cnname', 'staffer_enname', 'hourstudy_num','evaluate_num', 'evaluate_rate', 'average_score');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "教师得分统计报表导出.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if($datalist){
                foreach($datalist as &$v){
                    if($v['hourstudy_num']){
                        $v['evaluate_rate'] = sprintf("%.4f",$v['evaluate_num'] / $v['study_num']) * 100 . '%';
                    }else{
                        $v['evaluate_rate'] = '0%';
                    }
                }
            }
        }


        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.staffer_id) as num FROM
                                                    (SELECT sf.staffer_id
                                                    FROM smc_staffer as sf
                                                    LEFT JOIN smc_class_hour_teaching as ht ON ht.staffer_id = sf.staffer_id
                                                    LEFT JOIN smc_class_hour as h ON h.hour_id = ht.hour_id
                                                    LEFT JOIN smc_class as c ON c.class_id = ht.class_id
                                                    WHERE {$datawhere} AND c.class_id > 0 GROUP BY c.class_id) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 沟通管理
     */
    function getStudyStudentList($paramArray)
    {
        $datawhere = '1 and s.student_id > 0 and sd.study_isreading = 1';
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== "") {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '30';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_sex,s.student_branch,s.student_birthday,
                (select count(ca.track_id) from smc_student_track as ca where ca.student_id =sd.student_id and ca.school_id=sd.school_id) as track_num,
                (select ca.track_day from smc_student_track as ca where ca.student_id =sd.student_id and ca.school_id=sd.school_id order by track_day DESC limit 0,1 ) as track_day
                from smc_student_study AS sd
                left join smc_student as s ON s.student_id= sd.student_id
                where {$datawhere} and sd.class_id ='{$paramArray['class_id']}'
                limit {$pagestart},{$num}";
        $studyList = $this->DataControl->selectClear($sql);
        if(!$studyList){
            $studyList = array();
        }

        $data = array();
        if(isset($paramArray['is_count']) && $paramArray['is_count'] !== ""){
            $allnum = $this->DataControl->selectOne("select count(s.student_id) as num
                                                       from smc_student_study AS sd
                                                       left join smc_student as s ON s.student_id= sd.student_id
                                                       where {$datawhere} and sd.class_id ='{$paramArray['class_id']}' ");
            if ($allnum) {
                $data['allnum'] = $allnum['num'];
            } else {
                $data['allnum'] = 0;
            }
        }else{
            $data['allnum'] = 0;
        }

        $data['list'] = $studyList;

        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 获取沟通记录
     */
    function getCatitrackApi($request)
    {
        $datawhere = "sc.school_id ='{$request['school_id']}'";
        $class = $this->DataControl->selectOne("SELECT co.coursetype_id FROM smc_class as c LEFT JOIN smc_course as co ON co.course_id = c.course_id WHERE c.class_id = '{$request['class_id']}' LIMIT 1");
        if ($class && $class['coursetype_id'] > 0) {
            $datawhere .= " and sc.coursetype_id = '{$class['coursetype_id']}' ";
        }
        if (isset($request['tracktype_id']) && $request['tracktype_id'] !== '') {
            $datawhere .= " and sc.tracktype_id = '{$request['tracktype_id']}'";
        }
        if (isset($request['commode_id']) && $request['commode_id'] !== '') {
            $datawhere .= " and sc.track_linktype = '{$request['commode_id']}'";
        }
        if (isset($request['startime']) && $request['startime'] !== '') {
            $datawhere .= " and sc.track_day >= '{$request['startime']}' ";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and sc.track_day <= '{$request['endtime']}' ";
        }
        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and sc.student_id = '{$request['student_id']}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like  '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sf.staffer_cnname like  '%{$request['keyword']}%')  ";
        }

        $sql = "select sf.staffer_cnname,sf.staffer_enname,sc.track_from,sc.track_day,sc.track_note,sc.track_createtime,
                (SELECT t.tracktype_name FROM smc_code_tracktype as t WHERE t.tracktype_id = sc.tracktype_id) as tracktype_name,
                (SELECT c.commode_name FROM crm_code_commode as c WHERE c.commode_id=sc.track_linktype) as commode_name,
                (SELECT co.object_name FROM crm_code_object as co WHERE co.object_code=sc.track_code) as object_name,
                (SELECT r.trackresult_name FROM smc_code_trackresult AS r WHERE r.trackresult_id = sc.result_id) as trackresult_name,
                (SELECT c.class_cnname FROM smc_class AS c WHERE c.class_id = sc.class_id) as class_cnname,
                (SELECT p.post_name FROM gmc_company_post AS p, gmc_staffer_postbe AS b
                WHERE p.post_id = b.post_id AND b.staffer_id = sf.staffer_id AND b.school_id = sc.school_id ORDER BY b.postbe_ismianjob DESC LIMIT 1) AS post_name
                from smc_student_track as sc
                left join smc_student as s ON s.student_id = sc.student_id
                left join smc_staffer as sf ON sf.staffer_id = sc.staffer_id
                where {$datawhere}
                order by sc.track_createtime DESC";

        $studyList = $this->DataControl->selectClear($sql);
        if ($studyList) {
            foreach ($studyList as &$v) {
                if ($v['track_from']) {
                    $v['track_from'] = '教务登记';
                } else {
                    $v['track_from'] = '校务登记';
                }
                $v['track_createtime'] = date("Y-m-d H:i:s", $v['track_createtime']);
                if ($v['staffer_enname']) {
                    $v['staffer_cnname'] = $v['staffer_cnname'] . '-' . $v['staffer_enname'];
                }
            }
        } else {
            $studyList = array();
        }
        $data = array();
        $allnum = $this->DataControl->selectOne("select count(sc.track_id) as  allnum
                                                    from smc_student_track as sc
                                                    left join smc_student as s ON s.student_id = sc.student_id
                                                    left join smc_staffer as sf ON sf.staffer_id = sc.staffer_id
                                                    where {$datawhere}");
        if ($allnum) {
            $data['allnum'] = $allnum['allnum'];
        } else {
            $data['allnum'] = 0;
        }

        $hourOne = $this->DataControl->selectOne("
            select ch.hour_name,c.class_cnname
            from smc_class_hour as ch
            left join smc_class as c ON c.class_id = ch.class_id
            where ch.hour_id = '{$request['hour_id']}'
        ");
        if (!$hourOne) {
            $hourOne = array();
        }

        $catitrack = $this->DataControl->selectOne("SELECT s.student_id,s.student_cnname,s.student_sex,s.student_img,c.track_day
                                                       FROM smc_student_study AS sd
                                                       LEFT JOIN smc_student as s ON s.student_id=sd.student_id
                                                       LEFT JOIN smc_student_track as c ON c.student_id=s.student_id
                                                       WHERE s.student_id='{$request['student_id']}' AND sd.school_id ='{$request['school_id']}' ORDER BY c.track_day DESC");

        $data['info'] = $hourOne;
        $data['list'] = $studyList;
        $data['catitrack'] = $catitrack;
        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 新增访谈记录
     */
    function addStuCatitrackAction($request)
    {
        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['track_from'] = '1';
        $data['school_id'] = $request['school_id'];
        $data['class_id'] = $request['class_id'];
        $data['track_linktype'] = $request['commode_id'];
        $data['student_id'] = $request['student_id'];
        $classOne = $this->DataControl->selectOne("SELECT u.coursetype_id,u.coursecat_id
FROM smc_class AS c,smc_course AS u WHERE c.course_id = u.course_id AND c.class_id = '{$request['class_id']}' limit 0,1");
        if(!$classOne){
            $this->error = '1';
            $this->errortip = '请确认班级信息存在！';
            return false;
        }
        $data['coursetype_id'] = $classOne['coursetype_id'];
        $data['coursecat_id'] = $classOne['coursecat_id'];
        $data['tracktype_id'] = $request['tracktype_id'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['track_code'] = $request['catitrack_code'];
        $tracktypeOne = $this->DataControl->selectOne("SELECT c.tracktype_name
FROM smc_code_tracktype AS c WHERE c.tracktype_id = '{$request['tracktype_id']}' limit 0,1");
        $data['track_classname'] = $tracktypeOne['tracktype_name'];
        $data['result_id'] = $request['trackresult_id'];
        $data['track_day'] = $request['catitrack_day'];
        $data['track_note'] = $request['catitrack_note'];
        $data['track_followutime'] = $request['catitrack_followutime'];
        $data['track_createtime'] = time();
        if ($this->DataControl->insertData("smc_student_track", $data)) {
            $this->DataControl->query("update smc_code_tracktype set tracktype_use = tracktype_use + 1 where tracktype_id='{$request['tracktype_id']}'");
            $this->error = '0';
            $this->errortip = '新增教务电访信息成功';
            return true;
        } else {
            $this->error = '1';
            $this->errortip = '新增教务电访信息失败';
            return false;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 获取沟通模板
     */
    function getTemplateApi($paramArray)
    {
        $template = $this->DataControl->selectClear("SELECT comtemp_id,comtemp_title,comtemp_content FROM eas_code_comtemp WHERE company_id='{$paramArray['company_id']}'");
        if (!$template) {
            $template = array();
        }

        return $template;
    }

    /**
     * 家长评价教师 家长评价教师学校列表 - 后台PC
     * 作者: wgh
     * @param $request
     * @return array
     */
    function EvaluateSchListPC($request){
        $sql = "SELECT
                    s.school_id,
                    s.school_cnname
                FROM
                    eas_student_hourcomment AS hc
	                LEFT JOIN smc_class_hour AS h ON h.hour_id = hc.hour_id
                    LEFT JOIN smc_class AS c ON c.class_id = h.class_id
                    LEFT JOIN smc_school AS s ON s.school_id = c.school_id
                WHERE
                    c.school_id <> ''
                GROUP BY
                    c.school_id";

        $teaList=$this->DataControl->selectClear($sql);

        if(!$teaList){
            $this->error = true;
            $this->errortip = "无学校数据";
            return false;
        }

        $data['list']=$teaList;
        return $data;
    }

    /**
     * 家长评价教师 家长评价教师班级列表 - 后台PC
     * 作者: wgh
     * @param $request
     * @return array
     */
    function EvaluateClassListPC($request){
        $datawhere = " 1 ";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

//        $sql = "SELECT
//                    c.class_id,
//                    c.class_branch,
//                    c.class_cnname,
//                    c.class_enname
//                FROM
//                    eas_student_hourcomment AS hc
//	                LEFT JOIN smc_class_hour AS h ON h.hour_id = hc.hour_id
//                    LEFT JOIN smc_class AS c ON c.class_id = h.class_id
//                WHERE
//                    {$datawhere} AND c.class_id IS NOT NULL
//                GROUP BY
//                    c.class_id";
        $sql = "SELECT
                    c.class_id,
                    c.class_branch,
                    c.class_cnname,
                    c.class_enname
                FROM
                    smc_class AS c
                WHERE
                    {$datawhere}";

        $workList = $this->DataControl->selectClear($sql);

        if(!$workList){
            $this->error = true;
            $this->errortip = "无班级数据";
            return false;
        }

        $data['list'] = $workList;
        return $data;
    }

    /**
     * 家长评价教师 家长评价教师教师列表 - 后台PC
     * 作者: wgh
     * @param $request
     * @return array
     */
    function EvaluateTeaListPC($request){
        $datawhere=" 1 ";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$request['class_id']}'";
        }

//        $sql = "SELECT
//                    c.class_id,
//                    s.staffer_id,
//                    s.staffer_branch,
//                    s.staffer_cnname,
//                    s.staffer_enname
//                FROM
//                    eas_student_hourcomment AS hc
//                    LEFT JOIN smc_class_hour AS h ON h.hour_id = hc.hour_id
//                    LEFT JOIN smc_class AS c ON c.class_id = h.class_id
//                    LEFT JOIN smc_class_teach AS ct ON ct.class_id = c.class_id
//                    LEFT JOIN smc_staffer AS s ON s.staffer_id = ct.staffer_id
//                WHERE
//                    {$datawhere} AND s.staffer_id IS NOT NULL
//                GROUP BY
//                    s.staffer_id";

        $sql = "SELECT
                    c.class_id,
                    s.staffer_id,
                    s.staffer_branch,
                    s.staffer_cnname,
                    s.staffer_enname
                FROM
                    smc_class AS c
                    LEFT JOIN smc_class_teach AS ct ON ct.class_id = c.class_id
                    LEFT JOIN smc_staffer AS s ON s.staffer_id = ct.staffer_id
                WHERE
                    {$datawhere} AND s.staffer_id IS NOT NULL
                GROUP BY
                    s.staffer_id";

        $teaList=$this->DataControl->selectClear($sql);

        if(!$teaList){
            $this->error = true;
            $this->errortip = "无教师数据";
            return false;
        }else{
            foreach ($teaList as &$var) {
                if($var['staffer_enname'] && $var['staffer_enname'] != ''){
                    $var['staffer_cnname'] = $var['staffer_cnname'] . '-' . $var['staffer_enname'] ;
                }
            }
        }

        $data['list']=$teaList;
        return $data;

    }
}