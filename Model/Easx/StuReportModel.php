<?php

namespace Model\Easx;


Class StuReportModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }


    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }


    /**
     *  查询学校的的任务完成率
     * author: ling
     * 对应接口文档 0001
     */
    function getSchoolTaskRate($paramArray, $from = 0)
    {
        $datawhere = "school_type =1";
        if (isset($paramArray['re_school_id']) && $paramArray['re_school_id'] !== '') {
            $datawhere .= " and  s.school_id = '{$paramArray['re_school_id']}'";
        }
        if (isset($paramArray['dataequity']) && $paramArray['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $datawhere .= " and s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }else{
            $datawhere .= " and s.school_id = '{$paramArray['school_id']}'";
        }
        if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id']) {
            $orgaize_datawhere = " and s.school_id in (select 
            og.school_id from gmc_company_organizeschool as og
            left join  gmc_company_organize as  g On og.organize_id = g.organize_id
             where g.organizeclass_id='{$paramArray['organizeclass_id']}' ) ";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id']) {
            $orgaize_datawhere = " and (s.school_id in (select og.school_id from gmc_company_organizeschool as og,gmc_company_organize as g where g.organize_id = og.organize_id and g.father_id='{$paramArray['organize_id']}' ) or  s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['organize_id']}' ) ) ";
        }
        if (isset($paramArray['sec_organize_id']) && $paramArray['sec_organize_id']) {
            $orgaize_datawhere = " and s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['sec_organize_id']}' ) ";
        }
        $datawhere .= $orgaize_datawhere;
        $hour_where = '1';
        if (isset($paramArray['month']) && $paramArray['month']) {
            $paramArray['start_time'] = date("Y-m-01", strtotime($paramArray['month']) . '-01');
            $paramArray['end_time'] = date("Y-m-t", strtotime($paramArray['month']) . '-01');
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $hour_where .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $hour_where .= " and ch.hour_day >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $hour_where .= " and ch.hour_day <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname  like '%{$paramArray['keyword']}%' or s.school_branch  like '%{$paramArray['keyword']}%'  or s.school_shortname  like '%{$paramArray['keyword']}%' )  ";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $array_school = $this->getStafferAllSchool($paramArray['re_postbe_id']);
        $str_school_id = $array_school['str_school_id'];

        if ($array_school['str_school_branch']) {
            $taskData = array();
            $taskData['str_school_branch'] = $array_school['str_school_branch'];
            $taskData['starttime'] = $paramArray['start_time'];
            $taskData['endtime'] = $paramArray['end_time'];
        }
        if ($from == 1) {
            $datawhere = " s.company_id='{$paramArray['company_id']}'";
        }
        $datawhere .= " and s.school_id in ({$str_school_id})";
        $sql = "select s.school_shortname as school_cnname,s.school_enname,s.school_branch, s.school_id
                    from smc_school as s 
                    where {$datawhere}  and  s.school_istest =0 
                    order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
                ";



        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $key=> $dateexcelvar) {
                    $countOne = $this->DataControl->selectOne("
                    select 
                      (select count(student_id)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id
                and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and {$hour_where} ) as  all_hour_student_num,
                 (select count(student_id) 
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and hour_bookcheck=1 and {$hour_where} ) as  is_book_student_num,
                    (select count(student_id) 
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and (hour_netstatus=1 or hour_netstatus =-1 ) and {$hour_where} ) as  net_student_allnum,
                     (select count(student_id) 
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and hour_netstatus=1 and {$hour_where} ) as  is_net_student_num,
                    (select avg(hour_netscores) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and hour_netstatus = 1 and c.school_id = s.school_id   and {$hour_where}) as  avg_school_netscorses,
                    (select avg(hour_testscore) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and {$hour_where}) as  avg_school_testscore,
                    (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and h.hour_homework=1  and {$hour_where}) as homework_num ,
                    (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and (h.hour_homework=1 or h.hour_homework =-1)  and {$hour_where}) as all_homework_num         
                    from smc_school as s where s.school_id =  {$dateexcelvar['school_id']}
                    limit 0,1
                ");
                    $dateexcelarray[$key]['all_hour_student_num'] = $countOne['all_hour_student_num'];
                    $dateexcelarray[$key]['is_book_student_num'] = $countOne['is_book_student_num'];
                    $dateexcelarray[$key]['net_student_allnum'] = $countOne['net_student_allnum'];
                    $dateexcelarray[$key]['is_net_student_num'] = $countOne['is_net_student_num'];
                    $dateexcelarray[$key]['avg_school_netscorses'] = $countOne['avg_school_netscorses'];
                    $dateexcelarray[$key]['avg_school_testscore'] = $countOne['avg_school_testscore'];
                    $dateexcelarray[$key]['homework_num'] = $countOne['homework_num'];
                    $dateexcelarray[$key]['all_homework_num'] = $countOne['all_homework_num'];



                    $taskData = array();
                    $taskData['str_school_branch'] = $dateexcelvar['school_branch'];
                    $taskData['starttime'] = $paramArray['start_time'];
                    $taskData['endtime'] = $paramArray['end_time'];
                    $schoolRate = request_by_curl("https://stuapi.kidcastle.cn/Api/getSchRateBySchBranch", dataEncode($taskData), "GET");
                    if ($schoolRate) {
                        $app_schoolRate = json_decode($schoolRate, true);
                        if (!$app_schoolRate['result'] || !is_array($app_schoolRate['result'])) {
                            $app_schoolRateList = array();
                        } else {
                            $app_schoolRateList = $app_schoolRate['result'][0];
                        }
                    }
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['up_taskitem_rate'] = $app_schoolRateList['up_taskitem_rate'] + 0;
                    $datearray['video_taskitem_rate'] = $app_schoolRateList['video_taskitem_rate'] + 0;
                    $datearray['avg_test_score'] = $app_schoolRateList['avg_test_score'] + 0;
                    $datearray['textbook_review_rate'] = $countOne['all_hour_student_num'] > 0 ? round($countOne['is_book_student_num'] / $countOne['all_hour_student_num'], 4) * 100 : 0;
                    $datearray['avg_audio_taskitem_score'] = $app_schoolRateList['avg_audio_taskitem_score'] + 0;
                    $datearray['net_school_rate'] = $countOne['net_student_allnum'] > 0 ? round($countOne['is_net_student_num'] / $countOne['net_student_allnum'], 4) * 100 : 0;
                    $datearray['avg_school_netscorses'] = $countOne['avg_school_netscorses'] == false ? '0' : round($countOne['avg_school_netscorses'], 2);
                    $datearray['classhour_memberstar'] = $app_schoolRateList['classhour_memberstar'] + 0;
                    $datearray['under_taskitem_rate'] = $countOne['all_homework_num'] > 0 ? round($countOne['homework_num'] / $countOne['all_homework_num'], 4) * 100 : 0;
                    $datearray['avg_school_testscore'] = $countOne['avg_school_testscore'] == false ? '0' : round($countOne['avg_school_testscore'], 2);
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array('校区名称', '校区编号', '线上任务平均完成率', '录播任务平均完成率', '线上检测平均分数', 'mini book平均完成率', '配音平均分数', "网课平均参与率", '网课检测平均分数', '家长平均满意度', '线下作业平均完成率', '线下成绩平均分数');
            $excelfileds = array('school_cnname', 'school_branch', 'up_taskitem_rate', 'video_taskitem_rate', 'avg_test_score', 'textbook_review_rate', 'avg_audio_taskitem_score', 'net_school_rate', 'avg_school_netscorses', 'classhour_memberstar', 'under_taskitem_rate', 'avg_school_testscore');
            if ($paramArray['language_type'] == 'tw') {
                $Model = new \Model\jianfanModel();
                $fielname = $Model->gb2312_big5("中心基地总表");
                $excelheader = $Model->array_gb2312_big5($excelheader);
            } else {
                $fielname = "中心基地总表";
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$paramArray['start_time']}-{$paramArray['end_time']}.xlsx");
            exit;
        }
        $sql .= " limit {$pagestart},{$num}";

        $schoolList = $this->DataControl->selectClear($sql);
        $rateList = array();
        $avg_data = array();
        $avg_data['up_taskitem_rate'] = 0;
        $avg_data['video_taskitem_rate'] = 0;
        $avg_data['avg_test_score'] = 0;
        $avg_data['textbook_review_rate'] = 0;
        $avg_data['avg_audio_taskitem_score'] = 0;
        $avg_data['net_school_rate'] = 0;
        $avg_data['test_taskitem_rate'] = 0;
        $avg_data['under_taskitem_rate'] = 0;
        $avg_data['all_num'] = 0;

        if ($schoolList) {
            foreach ($schoolList as $key => $schoolOne) {

                $countOne = $this->DataControl->selectOne("
                    select 
                      (select count(student_id)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id
                and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and {$hour_where} ) as  all_hour_student_num,
                 (select count(student_id) 
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and hour_bookcheck=1 and {$hour_where} ) as  is_book_student_num,
                    (select count(student_id) 
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and (hour_netstatus=1 or hour_netstatus =-1 ) and {$hour_where} ) as  net_student_allnum,
                     (select count(student_id) 
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and hour_netstatus=1 and {$hour_where} ) as  is_net_student_num,
                    (select avg(hour_netscores) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and hour_netstatus = 1 and c.school_id = s.school_id   and {$hour_where}) as  avg_school_netscorses,
                    (select avg(hour_testscore) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id   and {$hour_where}) as  avg_school_testscore,
                    (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and h.hour_homework=1  and {$hour_where}) as homework_num ,
                    (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as  c 
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id 
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and (h.hour_homework=1 or h.hour_homework =-1)  and {$hour_where}) as all_homework_num         
                    from smc_school as s where s.school_id =  {$schoolOne['school_id']}
                    limit 0,1
                ");
                $schoolList[$key]['all_hour_student_num'] = $countOne['all_hour_student_num'];
                $schoolList[$key]['is_book_student_num'] = $countOne['is_book_student_num'];
                $schoolList[$key]['net_student_allnum'] = $countOne['net_student_allnum'];
                $schoolList[$key]['is_net_student_num'] = $countOne['is_net_student_num'];
                $schoolList[$key]['avg_school_netscorses'] = $countOne['avg_school_netscorses'];
                $schoolList[$key]['avg_school_testscore'] = $countOne['avg_school_testscore'];
                $schoolList[$key]['homework_num'] = $countOne['homework_num'];
                $schoolList[$key]['all_homework_num'] = $countOne['all_homework_num'];

                $taskData = array();
                $taskData['str_school_branch'] = $schoolOne['school_branch'];
                $taskData['starttime'] = $paramArray['start_time'];
                $taskData['endtime'] = $paramArray['end_time'];
                $schoolRate = request_by_curl("https://stuapi.kidcastle.cn/Api/getSchRateBySchBranch", dataEncode($taskData), "GET");
                if ($schoolRate) {
                    $app_schoolRate = json_decode($schoolRate, true);
                    if (!$app_schoolRate['result'] || !is_array($app_schoolRate['result'])) {
                        $app_schoolRateList = array();
                    } else {
                        $app_schoolRateList = $app_schoolRate['result'][0];
                    }
                }
                $data = array();
                $data['school_id'] = $schoolOne['school_id'];
                $data['school_cnname'] = $schoolOne['school_cnname'];
                $data['school_branch'] = $schoolOne['school_branch'];
                $data['textbook_review_rate'] = $countOne['all_hour_student_num'] > 0 ? round($countOne['is_book_student_num'] / $countOne['all_hour_student_num'], 4) * 100 : 0;


                $data['up_taskitem_rate'] = $app_schoolRateList['up_taskitem_rate'] + 0;
                $data['under_taskitem_rate'] = $countOne['all_homework_num'] > 0 ? round($countOne['homework_num'] / $countOne['all_homework_num'], 4) * 100 : 0;
                $data['video_taskitem_rate'] = $app_schoolRateList['video_taskitem_rate'] + 0;
                $data['classhour_memberstar'] = $app_schoolRateList['classhour_memberstar'] + 0;
                $data['avg_audio_taskitem_score'] = $app_schoolRateList['avg_audio_taskitem_score'] + 0;
                $data['avg_test_score'] = $app_schoolRateList['avg_test_score'] + 0;
                $data['net_school_rate'] = $countOne['net_student_allnum'] > 0 ? round($countOne['is_net_student_num'] / $countOne['net_student_allnum'], 4) * 100 : 0;
                $data['avg_school_netscorses'] = $countOne['avg_school_netscorses'] == false ? '0' : round($countOne['avg_school_netscorses'], 2);
                $data['avg_school_testscore'] = $countOne['avg_school_testscore'] == false ? '0' : round($countOne['avg_school_testscore'], 2);
                $avg_data['up_taskitem_rate'] += $data['up_taskitem_rate'];
                $avg_data['video_taskitem_rate'] += $data['video_taskitem_rate'];
                $avg_data['avg_test_score'] += $data['avg_test_score'];
                $avg_data['textbook_review_rate'] += $data['textbook_review_rate'];
                $avg_data['avg_audio_taskitem_score'] += $data['avg_audio_taskitem_score'];
                $avg_data['net_school_rate'] += $data['net_school_rate'];
                $avg_data['avg_school_netscorses'] += $data['avg_school_netscorses'];
                $avg_data['avg_school_testscore'] += $data['avg_school_testscore'];
                $avg_data['classhour_memberstar'] += $data['classhour_memberstar'];
                $avg_data['under_taskitem_rate'] += $data['under_taskitem_rate'];
                $avg_data['all_num']++;
                $rateList[] = $data;
            }
        }
        $result = array();

        $allnum = $this->DataControl->selectOne("select count(s.school_id) as  num from smc_school as s  where {$datawhere} and  s.school_istest =0  ");
        if ($allnum) {
            $result['all_num'] = $allnum['num'];
        } else {
            $result['all_num'] = 0;
        }
        $result['list'] = $rateList;
        $avg_alldata = array();
        $avg_alldata[] = $avg_data;
        $result['avg_all_num'] = $avg_alldata;
        return $result;
    }


    /**
     *  获取全国的学校的平均分
     *  author: ling
     * 对应接口文档 0001
     */

    function getCompanyTaskRate($paramArray)
    {
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $paramArray['month'] = date("Y-m", strtotime($paramArray['start_time']));
        }
        $month_where = '1';
        $hour_where = '1';
        if (isset($paramArray['month']) && $paramArray['month']) {
            $month_where .= " and tw.taskview_month='{$paramArray['month']}'";
            $hour_where .= " and DATE_FORMAT(ch.hour_day,'%Y-%m') = '{$paramArray['month']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $hour_where .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        $orgaize_datawhere = '1';
        if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id']) {
            $orgaize_datawhere = "  1 and s.school_id in (select 
            og.school_id from gmc_company_organizeschool as og
            left join  gmc_company_organize as  g On og.organize_id = g.organize_id
             where g.organizeclass_id='{$paramArray['organizeclass_id']}' ) ";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id']) {
            $orgaize_datawhere = "  1 and (s.school_id in (select og.school_id from gmc_company_organizeschool as og,gmc_company_organize as g where g.organize_id = og.organize_id and g.father_id='{$paramArray['organize_id']}' ) or  s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['organize_id']}' ) ) ";
        }
        if (isset($paramArray['re_school_id']) && $paramArray['re_school_id']) {
            $paramArray['sec_organize_id'] = $paramArray['re_school_id'];
        }
        if (isset($paramArray['sec_organize_id']) && $paramArray['sec_organize_id']) {
            $orgaize_datawhere = "  1 and s.school_id in (select og.school_id from gmc_company_organizeschool as og where og.organize_id='{$paramArray['sec_organize_id']}' ) ";
        }

        $sql = "select company_id,
         (select avg(taskview_up_rate) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where} and {$orgaize_datawhere} and taskview_up_rate >0  ) as up_taskitem_rate,
         (select avg(taskview_video_rate) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where} and {$orgaize_datawhere} and taskview_up_rate >0  ) as video_taskitem_rate,
         (select avg(taskview_avg_testscore) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where} and {$orgaize_datawhere} and taskview_up_rate >0  ) as avg_test_score,
         (select avg(taskview_avg_audio_score) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where} and {$orgaize_datawhere} and taskview_up_rate >0  ) as avg_audio_taskitem_score,
           (select avg(taskview_classhour_memberstar) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where} and {$orgaize_datawhere} and taskview_up_rate >0  ) as classhour_memberstar,
          (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and {$orgaize_datawhere} ) as student_allnum,
          (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and {$orgaize_datawhere} and hour_bookcheck =1 ) as student_bookchecknum,
          (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and {$orgaize_datawhere} and (hour_netstatus =1 or  hour_netstatus =-1) ) as all_hour_student_num,
            (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and {$orgaize_datawhere} and hour_netstatus =1 ) as is_net_student_num,
             (select avg(h.hour_netscores) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id  and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and  {$orgaize_datawhere} and hour_netstatus =1 ) as avg_school_netscorses,
             (select avg(h.hour_testscore) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id  and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and  {$orgaize_datawhere} ) as avg_school_testscore,
              (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id  and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and  {$orgaize_datawhere} and (h.hour_homework=1 or h.hour_homework =-1) ) as hour_homework_allnum,
              (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and  {$orgaize_datawhere} and h.hour_homework=1  ) as hour_homework_num
        from gmc_company as y 
        where y.company_id='{$paramArray['company_id']}' Limit 0,1
       ";
        $dataOne = $this->DataControl->selectOne($sql);
        if ($dataOne) {
            $data[1]['school_cnname'] = '所选区域';
            $data[1]['up_taskitem_rate'] = round($dataOne['up_taskitem_rate'], 2);
            $data[1]['video_taskitem_rate'] = round($dataOne['video_taskitem_rate'], 2);
            $data[1]['avg_test_score'] = round($dataOne['avg_test_score'], 2);
            $data[1]['textbook_review_rate'] = $dataOne['student_allnum'] > 0 ? round($dataOne['student_bookchecknum'] / $dataOne['student_allnum'], 4) * 100 : 0;
            $data[1]['avg_audio_taskitem_score'] = round($dataOne['avg_audio_taskitem_score'], 2);
            $data[1]['net_school_rate'] = $dataOne['all_hour_student_num'] > 0 ? round($dataOne['is_net_student_num'] / $dataOne['all_hour_student_num'], 4) * 100 : 0;
            $data[1]['avg_school_netscorses'] = round($dataOne['avg_school_netscorses'], 2);
            $data[1]['avg_school_testscore'] = round($dataOne['avg_school_testscore'], 2);
            $data[1]['classhour_memberstar'] = round($dataOne['classhour_memberstar'], 2);
            $data[1]['under_taskitem_rate'] = $dataOne['hour_homework_allnum'] > 0 ? round($dataOne['hour_homework_num'] / $dataOne['hour_homework_allnum'], 4) * 100 : 0;
        }

        $sql = "select company_id,
         (select avg(taskview_up_rate) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where} and taskview_up_rate >0  ) as up_taskitem_rate,
         (select avg(taskview_video_rate) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where}  and taskview_up_rate >0  ) as video_taskitem_rate,
         (select avg(taskview_avg_testscore) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where}  and taskview_up_rate >0  ) as avg_test_score,
         (select avg(taskview_avg_audio_score) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where}  and taskview_up_rate >0  ) as avg_audio_taskitem_score,
           (select avg(taskview_classhour_memberstar) from eas_school_stuapp_taskview as tw,smc_school as s where s.school_id=tw.school_id and s.company_id=y.company_id and {$month_where}  and taskview_up_rate >0  ) as classhour_memberstar,
          (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}  ) as student_allnum,
          (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}  and hour_bookcheck =1 ) as student_bookchecknum,
          (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}  and (hour_netstatus =1 or  hour_netstatus =-1) ) as all_hour_student_num,
            (select count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}  and hour_netstatus =1 ) as is_net_student_num,
             (select avg(h.hour_netscores) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id  and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where} and hour_netstatus =1 ) as avg_school_netscorses,
             (select avg(h.hour_testscore) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id  and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}) as avg_school_testscore,
              (select  count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id  and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}  and (h.hour_homework=1 or h.hour_homework =-1) ) as hour_homework_allnum,
              (select  count(h.student_id) from eas_student_hour as h,smc_class_hour as ch,smc_course as co,smc_class as c,smc_school as s where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes) and c.school_id = s.school_id and s.company_id=y.company_id and {$hour_where}  and h.hour_homework=1  ) as hour_homework_num
        from gmc_company as y
        where y.company_id='{$paramArray['company_id']}' Limit 0,1
       ";

        $dataOne = $this->DataControl->selectOne($sql);
        if ($dataOne) {
            $data[0]['school_cnname'] = '全国';
            $data[0]['up_taskitem_rate'] = round($dataOne['up_taskitem_rate'], 2);
            $data[0]['video_taskitem_rate'] = round($dataOne['video_taskitem_rate'], 2);
            $data[0]['avg_test_score'] = round($dataOne['avg_test_score'], 2);
            $data[0]['textbook_review_rate'] = $dataOne['student_allnum'] > 0 ? round($dataOne['student_bookchecknum'] / $dataOne['student_allnum'], 4) * 100 : 0;
            $data[0]['avg_audio_taskitem_score'] = round($dataOne['avg_audio_taskitem_score'], 2);
            $data[0]['net_school_rate'] = $dataOne['all_hour_student_num'] > 0 ? round($dataOne['is_net_student_num'] / $dataOne['all_hour_student_num'], 4) * 100 : 0;
            $data[0]['avg_school_netscorses'] = round($dataOne['avg_school_netscorses'], 2);
            $data[0]['avg_school_testscore'] = round($dataOne['avg_school_testscore'], 2);
            $data[0]['classhour_memberstar'] = round($dataOne['classhour_memberstar'], 2);
            $data[0]['under_taskitem_rate'] = $dataOne['hour_homework_allnum'] > 0 ? round($dataOne['hour_homework_num'] / $dataOne['hour_homework_allnum'], 4) * 100 : 0;
        }
        $data = array_values($data);
        return $data;
    }
    /**
     * 查询班级的任务完成率
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     */
    function getClassTaskRate($paramArray)
    {
        $datawhere = "1 and s.class_type = '0'";
        if (isset($paramArray['re_school_id']) && $paramArray['re_school_id'] !== '') {
            $datawhere .= " and s.school_id='{$paramArray['re_school_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== '') {
            $datawhere .= " and cs.coursecat_id='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= " and cs.course_id='{$paramArray['course_id']}'";
        }
        $hour_where = '1';

        if (isset($paramArray['month']) && $paramArray['month']) {
            $paramArray['start_time'] = date("Y-m-01", strtotime($paramArray['month'] . '-01'));
            $paramArray['end_time'] = date("Y-m-t", strtotime($paramArray['month'] . '-01'));
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $hour_where .= " and ch.hour_day >= '{$paramArray['start_time']}'";

        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $hour_where .= " and ch.hour_day <= '{$paramArray['end_time']}'";
            $datawhere .= " and (s.class_enddate >= '{$paramArray['end_time']}' or s.class_enddate='' ) ";
            $datawhere .= " and s.class_stdate <= '{$paramArray['end_time']}'";
        }
//        if (isset($paramArray['month']) && $paramArray['month']) {
//            $hour_where .= " and DATE_FORMAT(ch.hour_day,'%Y-%m') = '{$paramArray['month']}'";
//        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.class_cnname  like '%{$paramArray['keyword']}%' or s.class_enname like '%{$paramArray['keyword']}%' or s.class_branch  like '%{$paramArray['keyword']}%' )  ";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.class_cnname,s.class_branch ,s.class_enname,l.school_branch,l.school_shortname,cs.course_cnname,ct.coursecat_cnname,
                (select count(student_id)
                    from eas_student_hour as h,
                    smc_class_hour as ch ,
                    smc_course as co,
                    smc_class as c
                    where
                    co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id
                    and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                    and c.class_id = s.class_id and {$hour_where} ) as  all_hour_student_num,
                (select  count(student_id)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                    and hour_netstatus = 1  and c.class_id = s.class_id  and {$hour_where}) as  is_net_student_num,
                (select  count(student_id)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                    and hour_bookcheck = 1  and c.class_id = s.class_id  and {$hour_where}) as  is_book_student_num,
                (select avg(hour_netscores)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                    and hour_netstatus = 1  and c.class_id = s.class_id  and {$hour_where}) as  avg_school_netscorses,
                (select avg(hour_testscore)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                     and c.class_id = s.class_id  and {$hour_where}) as  avg_school_homeworkscores,
                     (select count(h.student_id)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                    and hour_homework = 1  and c.class_id = s.class_id  and {$hour_where}) as  hour_homework_num,
                    (select count(h.student_id)
                    from eas_student_hour as h,smc_class_hour as ch ,smc_course as co,smc_class as c
                    where co.course_id = ch.course_id and  c.class_id=ch.class_id and ch.class_id=h.class_id and h.hour_branch = CONCAT(co.course_branch,'_',ch.hour_lessontimes)
                    and (h.hour_homework = 1 or h.hour_homework = -1 )  and c.class_id = s.class_id  and {$hour_where}) as  all_hour_homework_num
                    
        from smc_class as s,smc_course as cs,eas_classcode as co,smc_school as l ,smc_code_coursecat as ct
        where s.course_id=cs.course_id and cs.course_branch = co.classcode_branch and co.company_id=cs.company_id and cs.coursecat_id=ct.coursecat_id and
        co.company_id='{$this->company_id}' and {$datawhere} and co.classcode_isregister =1 and s.school_id=l.school_id and s.class_status <> '-2'
        order by s.class_id DESC 
        limit {$pagestart},{$num}
                ";
        $rateList = array();
        $classList = $this->DataControl->selectClear($sql);
        if ($classList) {
            foreach ($classList as $key => $classOne) {
                $taskData = array();
                $taskData['str_class_branch'] = $classOne['class_branch'];
                $taskData['starttime'] = $paramArray['start_time'];
                $taskData['endtime'] = $paramArray['end_time'];
                $classRate = request_by_curl("https://stuapi.kidcastle.cn/Api/getClassTaskByClassBranch", dataEncode($taskData), "GET");
                if ($classRate) {
                    $app_classRate = json_decode($classRate, true);
                    if (!$app_classRate['result'] || !is_array($app_classRate['result'])) {
                        $app_classRateList = array();
                    } else {
                        $app_classRateList = $app_classRate['result'][0];
                    }
                }

                $data = array();
                $data['school_shortname'] = $classOne['school_shortname'];
                $data['school_branch'] = $classOne['school_branch'];
                $data['class_cnname'] = $classOne['class_cnname'];
                $data['class_enname'] = $classOne['class_enname'];
                $data['class_branch'] = $classOne['class_branch'];
                $data['coursecat_cnname'] = $classOne['coursecat_cnname'];
                $data['course_cnname'] = $classOne['course_cnname'];
                $data['up_taskitem_rate'] = $app_classRateList['up_taskitem_rate'] + 0;
                $data['textbook_review_rate'] = $classOne['all_hour_student_num'] > 0 ? round($classOne['is_net_student_num'] / $classOne['all_hour_student_num'], 4) * 100 : 0;
                $data['under_taskitem_rate'] = $classOne['all_hour_homework_num'] > 0 ? round($classOne['hour_homework_num'] / $classOne['all_hour_homework_num'], 4) * 100 : 0;
                $data['video_taskitem_rate'] = $app_classRateList['video_taskitem_rate'] + 0;
                $data['classhour_memberstar'] = $app_classRateList['classhour_memberstar'] + 0;
                $data['avg_audio_taskitem_score'] = $app_classRateList['avg_audio_taskitem_score'] + 0;
                $data['avg_test_score'] = $app_classRateList['avg_test_score'] + 0;
                $data['net_school_rate'] = $classOne['all_hour_student_num'] > 0 ? round($classOne['is_net_student_num'] / $classOne['all_hour_student_num'], 4) * 100 : 0;
                $data['avg_school_netscorses'] = $classOne['avg_school_netscorses'] == false ? '0' : round($classOne['avg_school_netscorses'], 2);
                $data['avg_school_homeworkscores'] = $classOne['avg_school_homeworkscores'] == false ? '0' : round($classOne['avg_school_homeworkscores'], 2);
                $rateList[] = $data;
            }
        }

        $result = array();
        $allnum = $this->DataControl->selectOne("select count(s.class_id)  as all_num  from smc_class as s, smc_course as cs,eas_classcode as co where s.course_id=cs.course_id 
                    and cs.course_branch = co.classcode_branch and co.company_id=cs.company_id and co.company_id='{$this->company_id}' and {$datawhere} and co.classcode_isregister =1  and s.class_status <> '-2' ");
        if ($allnum) {
            $result['all_num'] = $allnum['all_num'];
        } else {
            $result['all_num'] = 0;
        }
        $result['list'] = $rateList;
        return $result;
    }


    /**
     *   获取集团组织架构模式
     * author: ling
     * 对应接口文档 0001
     */

    function getComOrganizeClass($request)
    {
        $datawhere = '1';

        $array_school = $this->getStafferAllSchool($request['re_postbe_id']);
        $str_school_id = $array_school['str_school_id'];
        $datawhere .= " and ol.school_id in ({$str_school_id})";

        $dataList = $this->DataControl->selectClear("select
        DISTINCT
       os.organizeclass_name ,os.organizeclass_id
        from gmc_code_organizeclass as os
        left join  gmc_company_organize as oe ON os.organizeclass_id = oe.organizeclass_id
        left join gmc_company_organizeschool as ol ON oe.organize_id = ol.organize_id
        where  {$datawhere} and os.company_id='{$this->company_id}'");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;

    }

    /**
     * 获取集团组织
     * author: ling
     * 对应接口文档 0001
     */
    function getComOrganize($reqeust)
    {
        $datawhere = '1';
        if (isset($reqeust['re_organize_id']) && $reqeust['re_organize_id'] !== '') {
            $datawhere .= " and father_id='{$reqeust['re_organize_id']}' ";
        } else {
            $datawhere .= " and father_id='0' ";
        }
        if (isset($reqeust['organizeclass_id']) && $reqeust['organizeclass_id']) {
            $datawhere .= " and oe.organizeclass_id = '{$reqeust['organizeclass_id']}'";
        }

//        $array_school = $this->getStafferAllSchool($reqeust['re_postbe_id']);
//        $str_school_id = $array_school['str_school_id'];
//        $datawhere .= " and ol.school_id in ({$str_school_id})";


        $dataList = $this->DataControl->selectClear("select  DISTINCT
    oe.organize_id,oe.organize_cnname
    from gmc_company_organize as oe
    left join gmc_company_organizeschool as ol ON oe.organize_id = ol.organize_id
    where  {$datawhere} and company_id='{$this->company_id}'");


        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }


    /**
     * 获取组织下的学校
     * author: ling
     * 对应接口文档 0001
     * @param $reqeust
     */
    function getOrgnizeSchool($reqeust)
    {

        $datawhere = '1';
        if (isset($reqeust['organize_id']) && $reqeust['organize_id']) {
            $datawhere .= " and co.organize_id='{$reqeust['organize_id']}' ";
        }
        $array_school = $this->getStafferAllSchool($reqeust['re_postbe_id']);
        $str_school_id = $array_school['str_school_id'];
        $datawhere .= " and sc.school_id in ({$str_school_id})";

        $dataList = $this->DataControl->selectClear("
            select sc.school_id,sc.school_cnname
            from gmc_company_organizeschool as os
            left join gmc_company_organize as co ON os.organize_id = co.organize_id
            left join smc_school as sc On sc.school_id = os.school_id and co.company_id = sc.company_id
            where co.company_id = '{$this->company_id}' and  {$datawhere} ");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }


    /**
     *  获取职工任职所有的校园
     * author: ling
     * 对应接口文档 0001
     * @param $postbe_id
     * @return array
     */
    private function getStafferAllSchool($postbe_id)
    {
        $sql = "
            select  postrole_id,postpart_id
            from  gmc_staffer_postbe as p
            where p.postbe_id ='{$postbe_id}' ";

        $postOne = $this->DataControl->selectOne($sql);

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id='{$this->staffer_id}'");

        if ($stafferOne['account_class'] == 0 && $postbe_id <> '-1') {
            if ($postOne['postrole_id'] == 0) {
                $postList = $this->DataControl->selectClear("  select p.school_id,sc.school_branch
            from  gmc_staffer_postbe as p
            left join  smc_school as sc ON  sc.school_id = p.school_id
            where p.postbe_id ='{$postbe_id}' and sc.school_isclose =0 
               order by p.school_id DESC
            ");

                if (!$postList) {
                    $arr_all_school = array();
                } else {
                    $arr_all_school = array_column($postList, 'school_id');
                    $arr_all_school_branch = array_column($postList, 'school_branch');
                }
            } else {
                $post_sql = "
            select co.school_id,sc.school_branch
            from  gmc_staffer_postbe as sp
            left join gmc_company_organizeschool as co ON co.organize_id =sp.organize_id
               left join  smc_school as sc ON  sc.school_id = co.school_id
            where sp.postbe_id = '{$postbe_id}' and sp.school_id = '0' and sc.school_isclose =0 
              order by co.school_id DESC
            ";
                $com_postList = $this->DataControl->selectClear($post_sql);

                if (!$com_postList) {
                    $arr_all_school = array();
                } else {
                    $arr_all_school = array_column($com_postList, 'school_id');
                    $arr_all_school_branch = array_column($com_postList, 'school_branch');
                }
                if (!$arr_all_school) {
                    $arr_all_school = array();
                }
            }
        } else {
            $arr_school = $this->DataControl->selectClear("
                select s.school_id,s.school_branch
                from smc_school as s
                where s.company_id = '{$this->company_id}' and s.school_isclose =0 
                order by s.school_id DESC
            ");
            $arr_all_school = array_column($arr_school, 'school_id');
            $arr_all_school_branch = array_column($arr_school, 'school_branch');
        }

        $str_school_id = trim(implode(',', $arr_all_school), ',');
        if (!$str_school_id) {
            $str_school_id = 0;
        }

        $str_school_branch = "";
        if ($arr_all_school_branch) {
            foreach ($arr_all_school_branch as $branchOne) {
                $str_school_branch .= "'$branchOne',";
            }
        }
        $str_school_branch = trim($str_school_branch, ',');
        $result = array();
        $result['str_school_id'] = $str_school_id;
        $result['str_school_branch'] = $str_school_branch;
        return $result;
    }

    /**
     * 校总表-获取班种
     * author: ling
     * 对应接口文档 0001
     */
    function getCoursecat($request)
    {
        $dataList = $this->DataControl->selectClear("select DISTINCT ca.coursecat_id,ca.coursecat_branch,ca.coursecat_cnname 
            from smc_code_coursecat as ca 
            left join eas_classcode as cd On cd.catcode_branch =ca.coursecat_branch and ca.company_id=cd.company_id
            where ca.company_id='{$request['company_id']}' and cd.classcode_isregister =1 ");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    function getCourse($request)
    {
        $datawhere = '1';
        if (isset($request['coursecat_id']) && $request['coursecat_id']) {
            $datawhere .= " and co.coursecat_id='{$request['coursecat_id']}'";
        }
        $dataList = $this->DataControl->selectClear("select DISTINCT co.course_cnname,co.course_branch,co.course_id from smc_course as co
            left join eas_classcode as cd ON cd.classcode_branch=co.course_branch and cd.company_id=co.company_id
            where co.company_id='{$request['company_id']}' and cd.classcode_isregister =1 and {$datawhere}  ");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }
}