<?php

namespace Model\Easx;

Class HomeworkModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();


    function homeworkList($request)
    {

        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.homework_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and h.homework_createtime <= '{$endtime}'";
        }

        $sql = "
            SELECT
                h.homework_id,
                h.homework_title,
                h.homework_createtime,
                h.homework_isrefer,
                h.homework_content,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '3' or hk.homeworkstu_status = '1' or hk.homeworkstu_status = '4')) AS readNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '1')) AS uncommentNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '0' or hk.homeworkstu_status = '2' or hk.homeworkstu_status = '3')) AS unsendNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '1' or hk.homeworkstu_status = '4')) AS submitNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '6' or hk.homeworkstu_status = '5')) AS backNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '2')) AS unreadNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '4') ) AS commentNum,
                ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id ) AS allNum,
                hh.hour_day,
                hh.hour_starttime,
                hh.hour_endtime,
                hh.hour_name
            FROM
                eas_homework AS h
                LEFT JOIN eas_homeworkstu AS s ON h.homework_id = s.homework_id 
                left join smc_class as c on c.class_id = s.class_id
                left join smc_class_hour as hh on hh.hour_id = s.hour_id
            WHERE
                {$datawhere} and h.staffer_id = '{$request['staffer_id']}' and c.school_id = '{$request['school_id']}'
            GROUP BY
                h.homework_id
	        order by h.homework_id DESC
              ";

        $sql .= " limit {$pagestart},{$num}";

        $workList = $this->DataControl->selectClear($sql);

        if (!$workList) {
            $this->error = true;
            $this->errortip = "暂未发布任何作业信息";
            return false;
        }

        foreach ($workList as &$workOne) {

            $workOne['homework_createtime'] = date("m-d H:i", $workOne['homework_createtime']);
            $workOne['hour_day'] = date("m-d", strtotime($workOne['hour_day']));

            $sql = "select c.class_cnname,count(hk.student_id) as stuNum
                  from eas_homeworkstu as hk
                  left join smc_class as c on c.class_id=hk.class_id
                  where hk.homework_id='{$workOne['homework_id']}'
                  group by hk.class_id
                  order by hk.class_id asc
                  ";

            $classList = $this->DataControl->selectClear($sql);
            if ($classList) {
                $workOne['classInfo'] = $classList;
            } else {
                $workOne['classInfo'] = array();
            }

            if($workOne['homework_isrefer'] == '0'){
                if($workOne['readNum'] > '0'){
                    $workOne['status'] = '0';
                }else{
                    $workOne['status'] = '1';
                }
            }

            if($workOne['homework_isrefer'] == '1'){
                if($workOne['submitNum'] > '0' || $workOne['commentNum'] > '0' || $workOne['backNum'] > '0'){
                    $workOne['status'] = '0';
                }else{
                    $workOne['status'] = '1';
                }
            }
        }

        $data = array();
        $count_sql = "select h.homework_id
              from eas_homework as h
              where {$datawhere} and h.staffer_id='{$request['staffer_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $workList;

        return $data;

    }

    function homeworkItem($request)
    {
        $sql = "select h.homework_title,h.homework_content,h.homework_mediajson,h.homework_radiourl,h.homework_isrefer,s.staffer_id,s.staffer_cnname,h.homework_createtime
              from eas_homework as h
              left join smc_staffer as s on s.staffer_id=h.staffer_id
              where h.homework_id='{$request['homework_id']}'";

        $workOne = $this->DataControl->selectOne($sql);

        if (!$workOne) {
            $this->error = true;
            $this->errortip = "无相关作业";
            return false;
        }

        $workOne['homework_createtime'] = date("Y-m-d H:i:s", $workOne['homework_createtime']);
        $sql = "select c.class_id,c.class_cnname,count(hk.student_id) as stuNum
                  from eas_homeworkstu as hk
                  left join smc_class as c on c.class_id=hk.class_id
                  where hk.homework_id='{$request['homework_id']}'
                  group by hk.class_id
                  order by hk.class_id asc
                  ";

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $classList = array();
        }

        foreach ($classList as &$classOne) {

            $sql = "select hk.student_id,s.student_cnname,s.student_enname,student_sex,student_img,hk.homeworkstu_status,hk.homeworkdo_id
                  from eas_homeworkstu as hk
                  left join smc_student as s on s.student_id=hk.student_id
                  where hk.homework_id='{$request['homework_id']}' and hk.class_id='{$classOne['class_id']}'
                  group by hk.student_id
                  order by hk.student_id asc
                  ";
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $studentList = array();
            }
            $classOne['studentList'] = $studentList;

        }

        $workOne['classInfo'] = $classList;


        return $workOne;
    }

    function homeworkReceiveInfo($request)
    {

        $datawhere = ' ';
        if (isset($request['hour_id']) && $request['hour_id'] != '') {
            $datawhere = " and hk.hour_id='{$request['hour_id']}' ";
        }

        $sql = "select hk.hour_id,c.class_id,c.class_cnname,count(hk.student_id) as stuNum
                  from eas_homeworkstu as hk
                  left join smc_class as c on c.class_id=hk.class_id
                  where hk.homework_id='{$request['homework_id']}'
                  group by hk.class_id
                  order by hk.class_id asc
                  ";

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无相关接收信息";
            return false;
        }

        foreach ($classList as &$classOne) {
            $datawhere = ' ';
            if ($classOne['hour_id'] > '0') {
                $datawhere = " and hk.hour_id='{$classOne['hour_id']}' ";
            }
            $sql = "select hk.student_id,s.student_cnname
                  from eas_homeworkstu as hk
                  left join smc_student as s on s.student_id=hk.student_id
                  where hk.homework_id='{$request['homework_id']}' and hk.class_id='{$classOne['class_id']}' {$datawhere}
                  group by hk.student_id
                  order by hk.student_id asc
                  ";
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $studentList = array();
            }
            $classOne['studentList'] = $studentList;

        }

        return $classList;

    }

    function homeworkStuList($request)
    {
        $datawhere = " 1 ";

        if ($request['homeworkstu_status'] == '0') {
            $sql = "select s.student_id,s.student_cnname,s.student_img,s.student_sex,h.homeworkstu_status,h.homeworkstu_score
              from eas_homeworkstu as h
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}' and (h.homeworkstu_status='0' or h.homeworkstu_status='2' or  h.homeworkstu_status='3')";
        } elseif ($request['homeworkstu_status'] == '1') {
            $sql = "select s.student_id,s.student_cnname,s.student_img,s.student_sex,h.homeworkstu_status,h.homeworkstu_score
              from eas_homeworkstu as h
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}' and (h.homeworkstu_status='1' or h.homeworkstu_status='4')";
        } else {
            $sql = "select s.student_id,s.student_cnname,s.student_img,s.student_sex,h.homeworkstu_status,h.homeworkstu_score
              from eas_homeworkstu as h
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'";
        }

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        $status = array('0' => '待完成', '1' => '待评价', '2' => '未读', '3' => '已读', '4' => '已评分');
        foreach ($studentList as &$studentOne) {
            if ($studentOne['homeworkstu_status'] == '4') {
                $studentOne['status_name'] = $studentOne['homeworkstu_score'];
            } else {
                $studentOne['status_name'] = $status[$studentOne['homeworkstu_status']];
            }
        }

        $data = array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $studentList;

        return $data;

    }

    function homeworkStuOne($request)
    {

        $sql = "select h.homework_id,s.student_cnname,s.student_sex,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl
              ,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime,s.student_img
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              where h.homework_id='{$request['homework_id']}' and h.student_id='{$request['student_id']}'";
        $studentOne = $this->DataControl->selectOne($sql);

        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }
        if ($studentOne['homeworkstu_sendtime']) {
            $studentOne['homeworkstu_sendtime'] = date("Y-m-d H:i", $studentOne['homeworkstu_sendtime']);
        } else {
            $studentOne['homeworkstu_sendtime'] = '未提交';
        }

        return $studentOne;
    }

    function getTempType($request)
    {

        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and h.homeworktemp_title like '%{$request['keyword']}%'";
        }

        $sql = "select h.homeworktemp_id,h.homeworktemp_title,h.homeworktemp_remark from eas_code_homeworktemp as h
              where {$datawhere} and h.company_id='{$request['company_id']}' and h.homeworktemp_type_id='{$request['homeworktemp_type_id']}'
              group by h.homeworktemp_id
              order by h.homeworktemp_id asc
              ";

        $sql .= " limit {$pagestart},{$num}";
        $tempList = $this->DataControl->selectClear($sql);
        if (!$tempList) {
            $this->error = true;
            $this->errortip = "无作业模板";
            return false;
        }


        $data = array();
        $count_sql = "select h.homeworktemp_id from eas_code_homeworktemp as h
              where {$datawhere} and h.company_id='{$request['company_id']}' and h.homeworktemp_type_id='{$request['homeworktemp_type_id']}'
              group by h.homeworktemp_id
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $tempList;

        return $data;

    }

    function getHomeTemp($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cr.homeworktemp_title like '%{$request['keyword']}%' or cr.homeworktemp_remark like '%{$request['keyword']}%')";
        }
        $temp = array();
        $sql = "select  cr.*,crt.homeworktemp_type_name from  eas_code_homeworktemp  as cr 
                left join eas_code_homeworktemp_type as crt ON cr.homeworktemp_type_id = crt.homeworktemp_type_id
                where cr.company_id='{$request['company_id']}' and cr.homeworktemp_status =1 and {$datawhere} and  crt.homeworktemp_type_status =1    ";

        $dataList = $this->DataControl->selectClear($sql);


        if (!$dataList) {
            $temp = array();
        } else
            foreach ($dataList as $key => $value) {
                $temp[$value['homeworktemp_type_id']]['title'] = $value['homeworktemp_type_name'];
                $temp[$value['homeworktemp_type_id']]['list'][] = $value;


            }

        $data = array();
        $data['list'] = $temp;
        return $data;
    }

    function stuHomeworkList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and h.student_id = '{$request['student_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }
        $sql = "select s.student_cnname,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl
              ,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'";

        $sql .= " limit {$pagestart},{$num}";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        foreach ($studentList as &$studentOne) {
            if ($studentOne['homeworkstu_sendtime']) {
                $studentOne['homeworkstu_sendtime'] = date("Y-m-d H:i", $studentOne['homeworkstu_sendtime']);
            } else {
                $studentOne['homeworkstu_sendtime'] = '未提交';
            }
        }

        $data = array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              where {$datawhere} and h.homework_id='{$request['homework_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $studentList;

        return $data;
    }

    function submitComment($request)
    {

        $data = array();
        $data['homeworkstu_score'] = $request['homeworkstu_score'];
        if($request['correct'] == '1'){
            $data['homeworkstu_status'] = 6;
        }else{
            $data['homeworkstu_status'] = 4;
        }
        $data['homeworkstu_comment'] = $request['homeworkstu_comment'];
        $data['homeworkstu_commentmedia'] = $request['homeworkstu_commentmedia'];
        $data['homeworkstu_commentradiourl'] = $request['homeworkstu_commentradiourl'];
        $data['homeworkstu_commenttime'] = time();

        if ($this->DataControl->updateData("eas_homeworkstu", "homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'", $data)) {
            $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$request['student_id']}'");

            $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$request['student_id']}'");

            foreach ($parenter as &$value) {
                $staffer_cnname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$request['staffer_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$request['company_id']}' and masterplate_name = '作业批阅提醒'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '作业批阅提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $student_cnname['student_cnname'] . '学员您好，' . $staffer_cnname['staffer_cnname'] . '教师已批阅您的作业，请注意查看哦~';
                $b = $student_cnname['student_cnname'];
                $c = $request['class_cnname'];
                $d = date('m月d日 H:i', time());
                $e = '您的作业分数为' . $request['homeworkstu_score'] . '，点击这里查看详情';
                $f = "https://scptc.kedingdang.com/HomeWork/JobDetails?homework_id={$request['homework_id']}&homeworkstu_status=4&isSubmit=1&cid={$request['company_id']}&s_id={$request['student_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($value['parenter_id'], $request['student_id']);
                $wxteModel->TeScore($a, $b, $c, $d, $e, $f, $wxid);
            }
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function hourList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $datawhere .= " and ch.hour_day = '{$request['fixedtime']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        }


        $sql = "select ch.hour_day,c.class_cnname,c.class_enname,sch.school_cnname,c.class_fullnums,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,ch.class_id,ch.hour_id,ch.hour_name
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(sh.homework_id) from eas_homeworkstu as sh where sh.hour_id=ch.hour_id) as workNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as x on c.course_id=x.course_id
              left join smc_code_coursetype as y on x.coursetype_id=y.coursetype_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0' and (c.class_status = '1' or c.class_status = '-1')  AND ch.hour_ischecking - coursetype_ischeckwork >= '0' and c.school_id = '{$request['school_id']}'
              order by ch.hour_day DESC
              ";

        $sql .= " limit {$pagestart},{$num}";

        $hourList = $this->DataControl->selectClear($sql);

        if (!$hourList) {
            $this->error = true;
            $this->errortip = "无课时数据";
            return false;
        }

        $data = array();
        $count_sql = "select ch.hour_id
              ,(select count(sh.homework_id) from eas_homeworkstu as sh where sh.hour_id=ch.hour_id) as workNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$request['staffer_id']}' and ht.teaching_isdel='0' and c.class_status>='0' and ch.hour_ischecking='1' and c.school_id = '{$request['school_id']}'
              HAVING workNum='0'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $hourList;

        return $data;

    }

    function classStuList($request)
    {

        $sql = "select ss.class_id,ss.student_id,s.student_cnname,s.student_sex,s.student_img 
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where ss.class_id in ({$request['class_ids']}) and ss.study_isreading='1'";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        $data = array();
        $count_sql = "select ss.class_id
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where ss.class_id in ({$request['class_ids']}) and ss.study_isreading='1'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $studentList;

        return $data;

    }

    function submitHomework($request)
    {

        $list = json_decode(stripslashes($request['list']), true);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }


        $data = array();
        $data['homework_title'] = $request['homework_title'];
        $data['homework_content'] = $request['homework_content'];
//        $data['homework_mediajson']=$request['homework_mediajson']?str_replace("\\/", "/", json_encode($request['homework_mediajson'],JSON_UNESCAPED_UNICODE)):'';
        $data['homework_mediajson'] = $request['homework_mediajson'];
        $data['homework_radiourl'] = $request['homework_radiourl'] ? $request['homework_radiourl'] : '';
        $data['staffer_id'] = $request['staffer_id'];
        $data['homework_isrefer'] = $request['homework_isrefer'];
        $data['hour_id'] = $request['hour_id'];
        $data['class_id'] = $request['class_id'];
        $data['homework_createtime'] = time();
        $homework_id = $this->DataControl->insertData("eas_homework", $data);

        foreach ($list as $val) {
            $data = array();
            $data['homework_id'] = $homework_id;
            $data['class_id'] = $val['class_id'];
            $data['student_id'] = $val['student_id'];
            $data['hour_id'] = $val['hour_id']; //做PC的时候添加的
            $data['homeworkstu_createtime'] = time();
            $data['homeworkstu_status'] = '2';
            $this->DataControl->insertData("eas_homeworkstu", $data);

//            $parenter_id = $this->DataControl->selectOne("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$val['student_id']}'");

            $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$val['student_id']}'");

            foreach ($parenter as &$value) {
                $staffer_cnnname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$request['staffer_id']}'");
                $status = $this->DataControl->getFieldOne("eas_homeworkstu", "homeworkstu_status,homeworkstu_sendtime", "homework_id = '{$homework_id}' and student_id = '{$val['student_id']}'");
                if ($status['homeworkstu_sendtime'] == '0') {
                    $isSubmit = '0';
                } else {
                    $isSubmit = '1';
                }

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$request['company_id']}' and masterplate_name = '作业提醒'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '作业提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $staffer_cnnname['staffer_cnname'] . '教师布置新的作业了，请及时完成哦~';
                $b = $val['class_cnname'];
                $c = $request['homework_title'];

                $str = str_replace(array("\r\n", "\r", "\n"), "", $request['homework_content']);
                $len = 50;
                $d = $this->str($str,$len);

                $e = '点击这里查看作业详情';
                $f = "https://scptc.kedingdang.com/HomeWork/JobDetails?homework_id={$homework_id}&homeworkstu_status={$status['homeworkstu_status']}&isSubmit={$isSubmit}&cid={$request['company_id']}&s_id={$val['student_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($value['parenter_id'], $val['student_id']);
                $wxteModel->SendHomework($a, $b, $c, $d, $e, $f, $wxid);
            }

        }

        return true;
    }

    function str($str='',$len=0){
        //检查参数
        if(!is_string($str) || !is_int($len)){
            return '';
        }
        $length = strlen($str);
        if($length <= 0 ){
            return '';
        }
        if($len>=$length){
            return $str;
        }
        //初始化，统计字符串的个数，
        $count = 0;
        for($i=0;$i<$length;$i++){
            //达到个数跳出循环，$i即为要截取的长度
            if($count == $len){
                break;
            }
            $count++;
            //ord函数是获取字符串的ASCII编码，大于等于十六进制0x80的字符串即为中文字符串
            if(ord($str{$i}) >= 0x80){
                $i +=2;//中文编码的字符串的长度再加2
            }
        }
        //如果要截取的个数超过了字符串的总个数，那么我们返回全部字符串，不带省略号
        if($len > $count){
            return $str;
        }else{
            return substr($str,0,$i).'...';
        }
    }

    //编辑作业
    function updateHomework($request)
    {
        $data = array();
        $data['homework_title'] = $request['homework_title'];
        $data['homework_content'] = $request['homework_content'];
        $data['homework_mediajson'] = $request['homework_mediajson'];
        $data['homework_radiourl'] = $request['homework_radiourl'] ? $request['homework_radiourl'] : '';
        $data['homework_updatetime'] = time();
        $this->DataControl->updateData("eas_homework","homework_id = '{$request['homework_id']}'",$data);

        return true;
    }

    //删除作业
    function delHomework($request)
    {
        $this->DataControl->delData("eas_homework","homework_id = '{$request['homework_id']}'");

        return true;

    }

    function cutSubstr($str, $len)
    {
        if (strlen($str) > $len) {
            $str = substr($str, 0, $len) . '...';
        }
        return $str;
    }

    function getStuHomeworkList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and h.student_id = '{$request['student_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }

        if (isset($request['homework_id']) && $request['homework_id'] !== '') {
            $datawhere .= " and h.homework_id = '{$request['homework_id']}'";
        }
        $sql = "select s.student_cnname,h.homeworkstu_status,h.homeworkstu_score,ho.homework_title,h.homeworkstu_content,h.homeworkstu_mediajson,h.homeworkstu_radiourl
              ,h.homeworkstu_comment,h.homeworkstu_commentmedia,h.homeworkstu_commentradiourl,h.homeworkstu_sendtime,st.staffer_cnname
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              left join smc_staffer as st on st.staffer_id=ho.staffer_id
              where {$datawhere}
              order by h.homeworkstu_createtime desc
              ";
        $sql .= " limit {$pagestart},{$num}";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        $status = array('0' => '待完成', '1' => '已提交', '2' => '未读', '3' => '已读', '4' => '已评分');
        foreach ($studentList as &$studentOne) {
            if ($studentOne['homeworkstu_sendtime']) {
                $studentOne['homeworkstu_sendtime'] = date("Y-m-d H:i", $studentOne['homeworkstu_sendtime']);
            } else {
                $studentOne['homeworkstu_sendtime'] = '未提交';
            }
            $studentOne['homeworkstu_status_name'] = $status[$studentOne['homeworkstu_status']];
        }

        $data = array();
        $count_sql = "select s.student_id
              from eas_homeworkstu as h
              left join eas_homework as ho on ho.homework_id=h.homework_id
              left join smc_student as s on s.student_id=h.student_id
              left join smc_staffer as st on st.staffer_id=ho.staffer_id
              where {$datawhere}
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $studentList;

        return $data;

    }

    function handHomework($request)
    {
        $data = array();
        $data['homeworkstu_content'] = $request['homeworkstu_content'];
        $data['homeworkstu_mediajson'] = $request['homeworkstu_mediajson'];
        $data['homeworkstu_radiourl'] = $request['homeworkstu_radiourl'];
        $data['homeworkstu_sendtime'] = time();
        $data['homeworkstu_status'] = 1;

        if ($this->DataControl->updateData("eas_homeworkstu", "homework_id='{$request['homework_id']}' and student_id='{$request['student_id']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }

    }


    /**
     * 作业查询 管理列表 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkListPC($request)
    {

        $datawhere = " 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and h.homework_title like '%{$request['keyword']}%'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.homework_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and h.homework_createtime <= '{$endtime}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$request['class_id']}'";
        }

        if (isset($request['from_staffer_id']) && $request['from_staffer_id'] != '') {
            $datawhere .= " and h.staffer_id = '{$request['from_staffer_id']}'";
        }

        $sql = " select h.homework_id,h.homework_title,h.homework_createtime,c.class_id,c.class_cnname,c.class_enname,s.staffer_cnname,s.staffer_enname
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and (hk.homeworkstu_status='3' or hk.homeworkstu_status='4' or hk.homeworkstu_status='1') and hk.class_id=c.class_id) as readNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and (hk.homeworkstu_status='1' or hk.homeworkstu_status='4') and hk.class_id=c.class_id) as submitNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.homeworkstu_comment<>'' and hk.class_id=c.class_id) as commentNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.class_id=c.class_id) as allNum
              from eas_homework as h
              left join eas_homeworkstu as hk on hk.homework_id=h.homework_id 
              left join smc_class as c on c.class_id=hk.class_id 
              left join smc_staffer as s on s.staffer_id=h.staffer_id
              where {$datawhere}
              GROUP BY h.homework_id,c.class_id
              ORDER BY h.homework_id DESC
               ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            //班级对应的教师
            foreach ($dateexcelarray as &$workOne) {
                $workOne['homework_createtime'] = date("Y-m-d H:i", $workOne['homework_createtime']);

                //教师
                $teacherlist = $this->DataControl->selectClear("select s.staffer_cnname
                        from smc_class_teach as c 
                        left join smc_staffer as s ON s.staffer_id = c.staffer_id 
                        where c.teach_status = '0' and c.class_id = '{$workOne['class_id']}' ");
                $teacherstr = '';
                if ($teacherlist) {
                    $teacherArray = array();
                    foreach ($teacherlist as $teacherlistVar) {
                        if($teacherlistVar['staffer_enname']){
                            $teacherArray[] = $teacherlistVar['staffer_cnname'] . '-' . $teacherlistVar['staffer_enname'];
                        }else{
                            $teacherArray[] = $teacherlistVar['staffer_cnname'];
                        }
                    }
                    $teacherstr = implode(",", $teacherArray);
                }
                $workOne['teachername'] = $teacherstr;
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['homework_id'] = $dateexcelvar['homework_id'];//作业ID
                    $datearray['homework_title'] = $dateexcelvar['homework_title'];//作业标题
                    $datearray['homework_createtime'] = $dateexcelvar['homework_createtime'];//创建时间
                    $datearray['class_id'] = $dateexcelvar['class_id'];//班级ID
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];//班级中文名
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];//班级别名
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];//作业布置教师
                    $datearray['readNum'] = $dateexcelvar['readNum'];//阅读人数
                    $datearray['submitNum'] = $dateexcelvar['submitNum'];//提交人数
                    $datearray['commentNum'] = $dateexcelvar['commentNum'];//评论人数
                    $datearray['allNum'] = $dateexcelvar['allNum'];//全部人数
//                    $datearray['teachername'] = $dateexcelvar['teachername'];//班级教师
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = array('作业ID', '作业标题', '创建时间', '班级ID', '班级中文名', '班级别名', '作业布置教师', '阅读人数', '提交人数', '评论人数', '全部人数');//
            $excelfileds = array('homework_id', 'homework_title', 'homework_createtime', 'class_id', 'class_cnname', 'class_enname', 'staffer_cnname', 'readNum', 'submitNum', 'channel_name', 'commentNum', 'allNum');

            $fielname = "作业信息";
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}.xlsx");
            exit;
        }

        $workList = $this->DataControl->selectClear($sql." limit {$pagestart},{$num}");

        if (!$workList) {
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        //班级对应的教师
        foreach ($workList as &$workOne) {
            $workOne['homework_createtime'] = date("Y-m-d H:i", $workOne['homework_createtime']);
            if($workOne['staffer_enname']){
                $workOne['staffer_cnname'] = $workOne['staffer_cnname'] . '-' . $workOne['staffer_enname'];
            }

            //教师
            $teacherlist = $this->DataControl->selectClear("select s.staffer_cnname,s.staffer_enname
                        from smc_class_teach as c 
                        left join smc_staffer as s ON s.staffer_id = c.staffer_id 
                        where c.teach_status = '0' and c.class_id = '{$workOne['class_id']}' ");
            $teacherstr = '';
            if ($teacherlist) {
                $teacherArray = array();
                foreach ($teacherlist as $teacherlistVar) {
                    if($teacherlistVar['staffer_enname']){
                        $teacherArray[] = $teacherlistVar['staffer_cnname'] . '-' . $teacherlistVar['staffer_enname'];
                    }else{
                        $teacherArray[] = $teacherlistVar['staffer_cnname'];
                    }
                }
                $teacherstr = implode(",", $teacherArray);
            }
            $workOne['teachername'] = $teacherstr;
        }

        if ($workList) {
            $workLists = $this->DataControl->selectClear($sql);
            $allnum = count($workLists);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $workList;

        return $data;

    }

    /**
     * 作业查询 管理列表 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkClassListPC($request)
    {
        $datawhere = " 1 ";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and p.school_id = '{$request['school_id']}'";
        }
        $sql = "select c.class_id,c.class_branch,c.class_cnname,c.class_enname 
              FROM eas_homeworkstu as h 
              LEFT JOIN smc_class as c ON c.class_id = h.class_id
              LEFT JOIN eas_homework as w ON w.homework_id = h.homework_id 
              LEFT JOIN gmc_staffer_postbe as p ON p.staffer_id = w.staffer_id 
              WHERE {$datawhere}
              GROUP BY h.class_id ";

        $workList = $this->DataControl->selectClear($sql);

        if (!$workList) {
            $this->error = true;
            $this->errortip = "无班级数据";
            return false;
        }

        $data['list'] = $workList;
        return $data;

    }

    /**
     * 作业查询 作业教师列表 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkSchListPC($request)
    {
        $sql = "select l.school_id,s.school_cnname 
              FROM eas_homework as h 
              LEFT JOIN eas_homeworkstu as w ON w.homework_id = h.homework_id 
              LEFT JOIN smc_class as l ON l.class_id = w.class_id 
              LEFT JOIN smc_school as s On s.school_id = l.school_id 
              WHERE l.school_id <> '' 
              GROUP BY l.school_id";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无学校数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    //已读未读学员
    function homeworkDone($request)
    {
        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex
            FROM
                eas_homeworkstu AS h
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                where homework_id = '{$request['homework_id']}' and homeworkstu_status = '{$request['homeworkstu_status']}'";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无学生数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    //需要提交作业学生完成情况(待评，已评)
    function homeworkReferApi($request)
    {
        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                h.homeworkstu_score,
                h.homeworkdo_id,
                FROM_UNIXTIME( h.homeworkstu_sendtime, '%m.%d %H:%i' ) AS homeworkstu_sendtime,
                FROM_UNIXTIME( h.homeworkstu_commenttime, '%m.%d %H:%i' ) AS homeworkstu_commenttime
            FROM
                eas_homeworkstu AS h
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                where homework_id = '{$request['homework_id']}' and homeworkstu_status = '{$request['homeworkstu_status']}'";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无学生数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    //需要提交作业学生完成情况(打回)
    function homeworkBackApi($request)
    {
        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                h.homeworkstu_status
            FROM
                eas_homeworkstu AS h
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                where homework_id = '{$request['homework_id']}' and (homeworkstu_status = '5' or homeworkstu_status = '6')";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无学生数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    //需要提交作业学生完成情况(未交)
    function homeworkUnsendApi($request)
    {
        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                h.homeworkstu_status
            FROM
                eas_homeworkstu AS h
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                where homework_id = '{$request['homework_id']}' and (homeworkstu_status = '2' or homeworkstu_status = '3')";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无学生数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    //查看作业
    function HomeworkDetailApi($request)
    {
        $sql = "
            SELECT 
                e.homework_title,
                e.homework_content,
                h.homeworkstu_comment,
                e.homework_mediajson,
                e.homework_radiourl,
                h.homeworkstu_content,
                h.homeworkstu_mediajson,
                h.homeworkstu_radiourl,
                h.homeworkstu_commentmedia,
                h.homeworkstu_score,
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex,
                FROM_UNIXTIME( h.homeworkstu_commenttime, '%m.%d %H:%i' ) AS homeworkstu_commenttime,
                FROM_UNIXTIME( e.homework_createtime, '%m.%d %H:%i' ) AS homework_createtime,
                FROM_UNIXTIME( h.homeworkstu_sendtime, '%m.%d %H:%i' ) AS homeworkstu_sendtime
            FROM
                eas_homeworkstu AS h
                LEFT JOIN eas_homework AS e ON h.homework_id = e.homework_id
                LEFT JOIN smc_student AS s ON h.student_id = s.student_id
                where e.homework_id = '{$request['homework_id']}' and h.student_id = '{$request['student_id']}'";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        $data['list'] = $teaList;
        return $data;

    }

    /**
     * 作业查询 作业教师列表 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkTeaListPC($request)
    {
        $datawhere = " 1 ";
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and w.class_id = '{$request['class_id']}'";
        }

        $sql = "select h.staffer_id,s.staffer_branch,s.staffer_cnname,s.staffer_enname 
              FROM eas_homework as h 
              LEFT JOIN smc_staffer as s ON s.staffer_id = h.staffer_id 
              LEFT JOIN eas_homeworkstu as w ON w.homework_id = h.homework_id 
              WHERE {$datawhere}
              GROUP BY h.staffer_id";

        $teaList = $this->DataControl->selectClear($sql);

        if (!$teaList) {
            $this->error = true;
            $this->errortip = "无教师数据";
            return false;
        } else {
            foreach ($teaList as &$var) {
                if($var['staffer_enname'] && $var['staffer_enname'] != ''){
                    $var['staffer_cnname'] = $var['staffer_cnname'] . '-' . $var['staffer_enname'] ;
                }
            }
        }

        $data['list'] = $teaList;
        return $data;

    }


    /**
     * 作业查询 作业完成状态 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkStuStatePC($request)
    {
        $sql = "SELECT count(s.student_id) as allnum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and (hk.homeworkstu_status='1' or hk.homeworkstu_status='3' or hk.homeworkstu_status='4')) as readNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.homeworkstu_status='2') as noreadNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and (hk.homeworkstu_status='1' or hk.homeworkstu_status='4')) as submitNum
              ,(select count(hk.student_id) from eas_homeworkstu as hk where hk.homework_id=h.homework_id and hk.homeworkstu_comment<>'') as commentNum
                from eas_homework AS h
                LEFT JOIN eas_homeworkstu AS s ON h.homework_id = s.homework_id
                WHERE h.homework_id='{$request['homework_id']}' and s.class_id='{$request['class_id']}' ";

        $StuStateNum = $this->DataControl->selectClear($sql);

        //已读
        $readlist = $this->DataControl->selectClear("SELECT h.student_id,s.student_cnname,s.student_img,s.student_sex 
                    from eas_homeworkstu as h 
                    LEFT JOIN smc_student as s ON s.student_id = h.student_id
                    WHERE h.homework_id='{$request['homework_id']}' and h.class_id='{$request['class_id']}' and (h.homeworkstu_status='1' or h.homeworkstu_status='3' or h.homeworkstu_status='4') ");
        if ($readlist) {
            foreach ($readlist as &$v) {
                $v['stustate'] = '已读';
            }
        } else {
            $readlist = array();
        }
        //未读
        $noreadlist = $this->DataControl->selectClear("SELECT h.student_id,s.student_cnname,s.student_img,s.student_sex 
                      from eas_homeworkstu as h 
                      LEFT JOIN smc_student as s ON s.student_id = h.student_id
                      WHERE h.homework_id='{$request['homework_id']}' and h.class_id='{$request['class_id']}' and h.homeworkstu_status='2' ");
        if ($noreadlist) {
            foreach ($noreadlist as &$v) {
                $v['stustate'] = '未读';
            }
        } else {
            $noreadlist = array();
        }
        //已提交
        $submitlist = $this->DataControl->selectClear("SELECT h.student_id,s.student_cnname,s.student_img,s.student_sex 
                      from eas_homeworkstu as h 
                      LEFT JOIN smc_student as s ON s.student_id = h.student_id
                      WHERE h.homework_id='{$request['homework_id']}' and h.class_id='{$request['class_id']}' and (h.homeworkstu_status='1' or h.homeworkstu_status='4') ");
        if ($submitlist) {
            foreach ($submitlist as &$v) {
                $v['stustate'] = '已交';
            }
        } else {
            $submitlist = array();
        }
        //已评价
        $commentlist = $this->DataControl->selectClear("SELECT h.student_id,s.student_cnname,s.student_img,s.student_sex 
                        from eas_homeworkstu as h 
                        LEFT JOIN smc_student as s ON s.student_id = h.student_id
                        WHERE h.homework_id='{$request['homework_id']}' and h.class_id='{$request['class_id']}' and h.homeworkstu_comment<>'' ");
        if ($commentlist) {
            foreach ($commentlist as &$v) {
                $v['stustate'] = '已评';
            }
        } else {
            $commentlist = array();
        }
        //全部
        $alllist = $this->DataControl->selectClear("SELECT h.homeworkstu_status,h.student_id,s.student_cnname,s.student_img,s.student_sex
                    from eas_homeworkstu as h 
                    LEFT JOIN smc_student as s ON s.student_id = h.student_id
                    WHERE h.homework_id='{$request['homework_id']}' and h.class_id='{$request['class_id']}' ");
        if (is_array($alllist)) {
            foreach ($alllist as &$allvar) {
                $stuOne = $this->DataControl->selectOne("select h.homeworkstu_status,h.homeworkstu_isread from eas_homeworkstu as h WHERE h.homework_id='{$request['homework_id']}' and h.class_id='{$request['class_id']}' and h.student_id='{$request['student_id']}' ");
                if ($allvar['homeworkstu_status'] == '2') {
                    $allvar['stustate'] = '未读';
                } elseif ($allvar['homeworkstu_status'] == '1' || $allvar['homeworkstu_status'] == '3' || $allvar['homeworkstu_status'] == '4') {
                    $allvar['stustate'] = '已读';
                }
            }
        }

        $datalist = array();
        $datalist['readlist'] = $readlist;
        $datalist['noreadlist'] = $noreadlist;
        $datalist['submitlist'] = $submitlist;
        $datalist['commentlist'] = $commentlist;
        $datalist['alllist'] = $alllist;

        $data['list'] = $datalist;
        $data['num'] = $StuStateNum;
        return $data;

    }

    /**
     * 作业统计 按班级 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkClassStatis($request)
    {

        $datawhere = " 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and c.class_cnname like '%{$request['keyword']}%'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and s.homeworkstu_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and s.homeworkstu_createtime <= '{$endtime}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k WHERE k.class_id = c.class_id) as stunum
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k WHERE k.class_id = c.class_id and (k.homeworkstu_status ='1' or k.homeworkstu_status ='3' or k.homeworkstu_status ='4')) as readnum
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k WHERE k.class_id = c.class_id and (k.homeworkstu_status='1' or k.homeworkstu_status ='4')) as submitNum
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k WHERE k.class_id = c.class_id and k.homeworkstu_comment<>'') as commentnum
                FROM eas_homework AS h
                LEFT JOIN eas_homeworkstu AS s ON s.homework_id = h.homework_id
                LEFT JOIN smc_class as c ON c.class_id = s.class_id
                WHERE {$datawhere} and c.class_id IS NOT NULL
                GROUP BY s.class_id LIMIT {$pagestart},{$num}";
        $classList = $this->DataControl->selectClear($sql);

        if (is_array($classList)) {
            foreach ($classList as &$classVar) {
                //班级作业数
                $homeworklist = $this->DataControl->selectClear("select k.homework_id from eas_homeworkstu as k WHERE k.class_id='{$classVar['class_id']}' GROUP BY k.homework_id");
                if ($homeworklist) {
                    $homeworknum = count($homeworklist);
                } else {
                    $homeworknum = '0';
                }
                $classVar['homeworknum'] = $homeworknum;
                //阅读率
                $classVar['readrate'] = sprintf("%.2f", $classVar['readnum'] / $classVar['stunum']) * 100 . '%';
                //提交率
                $classVar['submitrate'] = sprintf("%.2f", $classVar['submitNum'] / $classVar['stunum']) * 100 . '%';
                //评价率
                $classVar['commentrate'] = sprintf("%.2f", $classVar['commentnum'] / $classVar['stunum']) * 100 . '%';

            }
        }

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无班级数据";
            return false;
        }

        if ($classList) {
            $allnum = count($classList);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $classList;
        return $data;
    }

    /**
     * 作业统计 按班级 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkTeacherStatis($request)
    {

        $datawhere = " 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and h.homework_createtime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and h.homework_createtime <= '{$endtime}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and h.class_id = '{$request['class_id']}'";
        }

        $sql = "SELECT h.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k LEFT JOIN eas_homework as wk ON wk.homework_id = k.homework_id LEFT JOIN smc_class AS c ON c.class_id = k.class_id WHERE wk.staffer_id=h.staffer_id AND c.school_id = '{$request['school_id']}' ) as stunum
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k LEFT JOIN eas_homework as wk ON wk.homework_id = k.homework_id LEFT JOIN smc_class AS c ON c.class_id = k.class_id WHERE wk.staffer_id=h.staffer_id AND c.school_id = '{$request['school_id']}' and (k.homeworkstu_status ='1' or k.homeworkstu_status ='3' or k.homeworkstu_status ='4')) as readnum
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k LEFT JOIN eas_homework as wk ON wk.homework_id = k.homework_id LEFT JOIN smc_class AS c ON c.class_id = k.class_id WHERE wk.staffer_id=h.staffer_id AND c.school_id = '{$request['school_id']}' and (k.homeworkstu_status='1' or k.homeworkstu_status ='4')) as submitNum
                ,(SELECT COUNT(k.student_id) FROM eas_homeworkstu as k LEFT JOIN eas_homework as wk ON wk.homework_id = k.homework_id LEFT JOIN smc_class AS c ON c.class_id = k.class_id WHERE wk.staffer_id=h.staffer_id AND c.school_id = '{$request['school_id']}' and k.homeworkstu_comment<>'') as commentnum
                FROM eas_homework AS h
                LEFT JOIN eas_homeworkstu as w ON w.homework_id = h.homework_id
                LEFT JOIN smc_staffer as s ON s.staffer_id = h.staffer_id
                LEFT JOIN smc_class as c ON c.class_id = w.class_id
                WHERE {$datawhere} and c.school_id = '{$request['school_id']}'
                GROUP BY h.staffer_id LIMIT {$pagestart},{$num}";
        $classList = $this->DataControl->selectClear($sql);

        if (is_array($classList)) {
            foreach ($classList as &$classVar) {
                //班级作业数
                $homeworklist = $this->DataControl->selectClear("select h.homework_id
                                from eas_homework as h
                                LEFT JOIN eas_homeworkstu as w ON w.homework_id = h.homework_id
                                LEFT JOIN smc_class as c ON c.class_id = w.class_id
                                WHERE h.staffer_id='{$classVar['staffer_id']}' and c.school_id = '{$request['school_id']}' GROUP BY h.homework_id");
                if ($homeworklist) {
                    $homeworknum = count($homeworklist);
                } else {
                    $homeworknum = '0';
                }
                $classVar['homeworknum'] = $homeworknum;
                //阅读率
                $classVar['readrate'] = sprintf("%.2f", $classVar['readnum'] / $classVar['stunum']) * 100 . '%';
                //提交率
                $classVar['submitrate'] = sprintf("%.2f", $classVar['submitNum'] / $classVar['stunum']) * 100 . '%';
                //评价率
                $classVar['commentrate'] = sprintf("%.2f", $classVar['commentnum'] / $classVar['stunum']) * 100 . '%';

            }
        }

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无班级数据";
            return false;
        }

        if ($classList) {
            $allnum = count($classList);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $classList;
        return $data;
    }


    /**
     * 作业统计 按班级 - 后台PC
     * 作者: 97
     * @param $request
     * @return array
     */
    function homeworkAllList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and s.class_id = '{$request['class_id']}'";
        }

        $sql = "SELECT
                    r.hour_day,r.hour_starttime,r.hour_endtime,
                    h.homework_id,
                    h.homework_title,
                    h.homework_createtime,
                    ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id AND (hk.homeworkstu_status = '3' or hk.homeworkstu_status = '1' or hk.homeworkstu_status = '4')) AS readnum,
                    ( SELECT count( hk.student_id ) FROM eas_homeworkstu AS hk WHERE hk.homework_id = h.homework_id ) AS stunum
                FROM
                    eas_homework AS h
                    LEFT JOIN eas_homeworkstu AS s ON h.homework_id = s.homework_id
                    LEFT JOIN smc_class_hour as r ON r.hour_id = s.hour_id
                WHERE
                    {$datawhere}
                GROUP BY
                    h.homework_id";
        $sql .= " limit {$pagestart},{$num}";
        $workList = $this->DataControl->selectClear($sql);
        if (is_array($workList)) {
            foreach ($workList as &$workVar) {
                $workVar['hour_day'] = $workVar['hour_day'] . ' ' . $workVar['hour_starttime'];
                $workVar['homework_createtime'] = date("Y-m-d H:i", $workVar['homework_createtime']);
                $workVar['hourread'] = $workVar['readnum'] . '/' . $workVar['stunum'];
            }
        }

        if (!$workList) {
            $this->error = true;
            $this->errortip = "无作业数据";
            return false;
        }

        if ($workList) {
            $allnum = count($workList);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $workList;
        return $data;
    }

    /**
     * 发布作业  删除一个作业
     * 作者: 97
     * @param $request
     * @return array
     */
    function delHomeworkOne($request)
    {
        $istrue = $this->DataControl->delData("eas_homework", "homework_id = '{$request['homework_id']}' and staffer_id = '{$request['staffer_id']}' ");
        if ($istrue) {
            $this->DataControl->delData("eas_homeworkstu", "homework_id = '{$request['homework_id']}'");
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }

    /**
     * 发布作业  删除一个作业接受人
     * 作者: 97
     * @param $request
     * @return array
     */
    function delHomeworkReceiveOne($request)
    {
        $istrue = $this->DataControl->delData("eas_homeworkstu", "homework_id = '{$request['homework_id']}' and class_id = '{$request['class_id']}'  and student_id = '{$request['student_id']}' ");
        if ($istrue) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }

    //打回订正
    function HomeBackApi($request)
    {
        $data = array();
        $data['homeworkstu_status'] = '5';
        $data['homeworkstu_commentmedia'] = $request['homeworkstu_commentmedia'];
        $data['homeworkstu_commentradiourl'] = $request['homeworkstu_commentradiourl'];
        $data['homeworkstu_comment'] = $request['homeworkstu_comment'];
        $data['homeworkstu_commenttime'] = time();
        $data['homeworkstu_score'] = $request['homeworkstu_score'];
        if ($this->DataControl->updateData("eas_homeworkstu","homework_id = '{$request['homework_id']}' and student_id = '{$request['student_id']}'",$data)) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }
}