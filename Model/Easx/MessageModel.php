<?php

namespace Model\Easx;


Class MessageModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

//    function __construct($publicarray)
//    {
//        parent::__construct();
//        if (is_array($publicarray)) {
//            $this->setPublic($publicarray);
//            $this->publicarray = $publicarray;
//        }
//    }

    //班级通知列表
    function ClassMessage($paramArray)
    {
        $datawhere = "m.school_id = '{$paramArray['school_id']}' and m.staffer_id = '{$paramArray['staffer_id']}' and m.message_type = '0'";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                m.message_id,
                m.message_title,
                m.message_content,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id) as allnum,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id and ms.message_isread = 1) as readnum,
                s.staffer_cnname
           FROM
                eas_class_message AS m left join smc_staffer as s on m.staffer_id = s.staffer_id
           WHERE {$datawhere} order by m.message_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("SELECT COUNT(m.message_id) FROM eas_class_message AS m WHERE {$datawhere}");
        $allnums = $all_num[0][0];

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["allnum"] = "总人数";
        $field["readnum"] = "已读人数";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取班级通知成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无班级通知，快来发布吧~', 'result' => $result);
        }

        return $res;
    }

    //查看阅读情况
    function ReadSituation($paramArray)
    {
        $result = array();

        $readnum = $this->DataControl->selectClear("select count(ms.messagestu_id) as readnum from eas_class_messagestu as ms where ms.message_id = '{$paramArray['message_id']}' and ms.message_isread = '1'");
        $result['readnum'] = $readnum[0]['readnum'];

        $allnum = $this->DataControl->selectClear("select count(ms.messagestu_id) as allnum from eas_class_messagestu as ms where ms.message_id = '{$paramArray['message_id']}'");
        $result['allnum'] = $allnum[0]['allnum'];

        $result['unreadnum'] = $result['allnum'] - $result['readnum'];

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if($paramArray['type'] == '0'){
            $sql = "
            SELECT
                s.student_cnname,
                s.student_enname,
                s.student_sex,
                s.student_img,
                ms.message_isread 
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}'";
            $ReadDetail = $this->DataControl->selectClear($sql);


            $all_num = $this->DataControl->select("
            SELECT
                COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}'");
            $allnums = $all_num[0][0];
        }elseif($paramArray['type'] == '2'){
            $sql = "
            SELECT
                s.student_cnname,
                s.student_enname,
                s.student_sex,
                s.student_img,
                ms.message_isread 
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}' and ms.message_isread = '0'";
            $ReadDetail = $this->DataControl->selectClear($sql);


            $all_num = $this->DataControl->select("
            SELECT
                COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}' and ms.message_isread = '0'");
            $allnums = $all_num[0][0];
        }elseif($paramArray['type'] == '1'){
            $sql = "
            SELECT
                s.student_cnname,
                s.student_enname,
                s.student_sex,
                s.student_img,
                ms.message_isread 
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}' and ms.message_isread = '1'";
            $ReadDetail = $this->DataControl->selectClear($sql);


            $all_num = $this->DataControl->select("
            SELECT
                COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}' and ms.message_isread = '1'");
            $allnums = $all_num[0][0];
        }else{
            $sql = "
            SELECT
                s.student_cnname,
                s.student_enname,
                s.student_sex,
                s.student_img,
                ms.message_isread 
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}'
            Limit {$pagestart},{$num}";
            $ReadDetail = $this->DataControl->selectClear($sql);


            $all_num = $this->DataControl->select("
            SELECT
                COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN smc_student AS s ON ms.student_id = s.student_id
            where ms.message_id = '{$paramArray['message_id']}'");
            $allnums = $all_num[0][0];
        }



        $result['data'] = $ReadDetail;
        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => '查看阅读情况成功', 'result' => $result);

        return $res;
    }

    //选择接收人班级
    function ChoiceSelecterClass($paramArray)
    {
        $datawhere = "c.school_id = '{$paramArray['school_id']}'  AND c.class_status = '1' AND t.staffer_id = '{$paramArray['staffer_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' )";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                c.class_enname,
                (select count(*) from smc_student_study as s WHERE s.study_isreading = '1' and s.class_id = c.class_id) as allnum
            FROM
                smc_class AS c left join smc_class_teach as t on c.class_id = t.class_id 
            WHERE
                {$datawhere}
            Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select(" SELECT COUNT(c.class_id) FROM smc_class AS c left join smc_class_teach as t on c.class_id = t.class_id WHERE {$datawhere} ");
        $allnums = $all_num[0][0];

        if ($NoticeDetail) {
            $result = array();
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取选择接收人班级成功', 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $result["all_num"] = '0';
            $res = array('error' => '1', 'errortip' => '获取选择接收人班级失败', 'result' => $result);
        }

        return $res;
    }

    //选择接收人学员
    function ChoiceSelecterStudent($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_img,
                s.student_sex
            FROM
                smc_student_study AS ss
                LEFT JOIN smc_student AS s ON s.student_id = ss.student_id 
            WHERE
                ss.class_id = '{$paramArray['class_id']}' 
                AND ss.study_isreading = '1'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(ss.study_id)
            FROM
                smc_student_study AS ss
                LEFT JOIN smc_student AS s ON s.student_id = ss.student_id 
            WHERE
                ss.class_id = '{$paramArray['class_id']}' 
                AND ss.study_isreading = '1'");
        $allnums = $all_num[0][0];


        $class_name = $this->DataControl->getFieldOne("smc_class","class_cnname,class_enname","class_id = '{$paramArray['class_id']}'");

        if ($NoticeDetail) {
            $result = array();
            $result["data"] = $NoticeDetail;
            $result["class_cnname"] = $class_name['class_cnname'];
            $result["class_enname"] = $class_name['class_enname'];
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取选择接收人学员成功', 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取选择接收人学员失败', 'result' => $result);
        }

        return $res;
    }

    //发布通知
    function SendMessageAction($paramArray){
        $data = array();
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['message_title'] = $paramArray['message_title'];
        $data['message_content'] = $paramArray['message_content'];
        $data['message_radiourl'] = $paramArray['message_radiourl'];
        $data['message_mediajson'] = $paramArray['message_mediajson'];
        $data['message_type'] = $paramArray['message_type'];
        $data['message_createtime'] = time();
        $message_id = $this->DataControl->insertData('eas_class_message',$data);

        $data = array();
        $schoolList = json_decode(stripslashes($paramArray['student']),true);
        foreach ($schoolList as $item) {
            $data['class_id'] = $item['class_id'];
            $data['student_id'] = $item['student_id'];
            $data['message_id'] = $message_id;
            $data['message_isread'] = '0';
            $data['message_createtime'] = time();
            $this->DataControl->insertData('eas_class_messagestu',$data);

            $student_cnname = $this->DataControl->getFieldOne("smc_student","student_cnname","student_id = '{$item['student_id']}'");
            $parenter = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_id from smc_student_family as f left join smc_parenter as p on f.parenter_id = p.parenter_id WHERE f.student_id  = '{$item['student_id']}'");

            foreach ($parenter as &$val) {
                $staffer_cnname = $this->DataControl->getFieldOne("smc_staffer","staffer_cnname","staffer_id = '{$paramArray['staffer_id']}'");
                $class_cnname = $this->DataControl->selectOne("select c.class_cnname from smc_class as c where c.class_id = '{$item['class_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '{$paramArray['company_id']}' and masterplate_name = '班级通知'");
                if($isset){
                    $wxid = $isset['masterplate_wxid'];
                }else{
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_wxid","company_id = '0' and masterplate_name = '班级通知'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $student_cnname['student_cnname'].'学员您好，您收到一条班级通知，请注意查看哦~';
                $b = $class_cnname['class_cnname'];
                $c = $staffer_cnname['staffer_cnname'];
                $d = date('m月d日 H:i',time());

                $str = str_replace(array("\r\n", "\r", "\n"), "", $paramArray['message_content']);
                $len = 50;
                $e = $this->str($str,$len);

                $f = '点击这里查看班级通知详情';
                $g = "https://scptc.kedingdang.com/ClassNotice/detailNotice?message_id={$message_id}&type=1&s_id={$item['student_id']}&cid={$paramArray['company_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'],$item['student_id']);
                $wxteModel->ClassMessage($a,$b,$c,$d,$e,$f,$g,$wxid);
            }

        }

        $res = array('error' => '0', 'errortip' => "发布通知成功", 'result' => array());

        return $res;
    }


    function str($str='',$len=0){
        //检查参数
        if(!is_string($str) || !is_int($len)){
            return '';
        }
        $length = strlen($str);
        if($length <= 0 ){
            return '';
        }
        if($len>=$length){
            return $str;
        }
        //初始化，统计字符串的个数，
        $count = 0;
        for($i=0;$i<$length;$i++){
            //达到个数跳出循环，$i即为要截取的长度
            if($count == $len){
                break;
            }
            $count++;
            //ord函数是获取字符串的ASCII编码，大于等于十六进制0x80的字符串即为中文字符串
            if(ord($str{$i}) >= 0x80){
                $i +=2;//中文编码的字符串的长度再加2
            }
        }
        //如果要截取的个数超过了字符串的总个数，那么我们返回全部字符串，不带省略号
        if($len > $count){
            return $str;
        }else{
            return substr($str,0,$i).'...';
        }
    }

    function cutSubstr($str, $len)
    {
        if (strlen($str) > $len) {
            $str = substr($str, 0, $len) . '...';
        }
        return $str;
    }

    //阅读通知
    function ReadMessageAction($paramArray){
        if($paramArray['type'] == '1'){
            $data = array();
            $data['message_isread'] = '1';
            $data['message_readtime'] = time();
            $this->DataControl->updateData('eas_class_messagestu',"student_id = '{$paramArray['student_id']}' and message_id = '{$paramArray['message_id']}'",$data);
        }else{
            $data = array();
            $data['message_id'] = $paramArray['message_id'];
            $data['student_id'] = $paramArray['student_id'];
            $data['message_isread'] = '1';
            $data['message_readtime'] = time();
            $data['message_createtime'] = time();
            $this->DataControl->insertData('eas_class_messagestu',$data);
        }

        $res = array('error' => '0', 'errortip' => "阅读通知成功", 'result' => array());

        return $res;
    }

    //查看接收人
    function GetSelector($paramArray)
    {
        $result = array();

        $data1 = $this-> DataControl->selectClear("select c.class_id,c.class_cnname,c.class_enname from eas_class_messagestu as m left join smc_class as c on m.class_id = c.class_id where m.message_id = '{$paramArray['message_id']}' GROUP BY c.class_id");

        $data2 = $this-> DataControl->selectClear("select c.student_id,c.student_cnname,c.student_enname,c.student_img,c.student_sex,class_id from eas_class_messagestu as m left join smc_student as c on m.student_id = c.student_id where m.message_id = '{$paramArray['message_id']}'  GROUP BY c.student_id");

        $data = $this->tree($data1,$data2);

        $result['children'] = $data;

        $res = array('error' => '0', 'errortip' => "发布通知成功", 'result' => $result);

        return $res;
    }

    function tree($items1,$items2)
    {
        $son = array();
        if(is_array($items1)){
            foreach($items1 as $k=>&$v) {
                $son[$k]['class_cnname'] = $v['class_cnname'];
                $son[$k]['class_enname'] = $v['class_enname'];
                $son[$k]['class_id'] = $v['class_id'];
                foreach ($items2 as $key=>$value) {
                    if ($v['class_id'] == $value['class_id']) {
                        $son[$k]['children'][$key]['student_cnname'] = $value['student_cnname'];
                        $son[$k]['children'][$key]['student_enname'] = $value['student_enname'];
                        $son[$k]['children'][$key]['student_img'] = $value['student_img'];
                        $son[$k]['children'][$key]['student_id'] = $value['student_id'];
                        $son[$k]['children'][$key]['student_sex'] = $value['student_sex'];
                    }
                }
            }
        }
        return $son;
    }


    //修改密码
    function updatePassAction($paramArray){
        $data = array();

        if (!preg_match('/^[0-9_a-zA-Z]{6,18}$/i',$paramArray['staffer_pass1'])){
            ajax_return(array('error' => 1,'errortip' => "密码需要控制在6~18位！"));
        }

        if($paramArray['staffer_pass1'] == $paramArray['staffer_pass2']){
            $data['staffer_pass'] = md5($paramArray['staffer_pass1']);
            $data['staffer_bakpass'] = $paramArray['staffer_pass1'];
            $data['staffer_updatetime'] = time();
        }else{
            ajax_return(array('error' => 1,'errortip' => "两次输入的密码不相同！"));
        }


        $field = array();
        $field["staffer_pass"] = "md5密码";
        $field["staffer_bakpass"] = "密码备注";
        if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['staffer_id']}'",$data) ) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改密码成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '修改密码失败', 'result' => $result);
        }
        return $res;
    }

    //班级通知详情
    function MessageDetail($paramArray)
    {
        $sql = "
           SELECT
                m.message_id,
                m.message_type,
                m.message_title,
                m.message_object,
                m.message_content,
                m.message_radiourl,
                m.message_mediajson,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                s.staffer_cnname,
                s.staffer_enname,
                s.staffer_mobile,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id and ms.message_isread = 1) as readnum
           FROM
                eas_class_message AS m left join smc_staffer as s on m.staffer_id = s.staffer_id
           WHERE m.message_id = '{$paramArray['message_id']}'";
        $NoticeDetail = $this->DataControl->selectOne($sql);
        if($NoticeDetail){
            if($NoticeDetail['staffer_enname']){
                $NoticeDetail['staffer_cnname'] = $NoticeDetail['staffer_cnname'] . '-' . $NoticeDetail['staffer_enname'];
            }
            if($NoticeDetail['message_type']){
                $stu_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT st.student_id) AS num FROM smc_school AS s LEFT JOIN smc_student_study AS st ON st.school_id = s.school_id WHERE s.school_id = '{$paramArray['school_id']}'");
                $tech_nums = $this->DataControl->selectOne("SELECT COUNT(DISTINCT s.staffer_id) AS num FROM smc_staffer AS s LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id LEFT JOIN smc_staffer_info as i ON s.staffer_id = i.staffer_id WHERE p.school_id = '{$paramArray['school_id']}' AND s.staffer_leave = '0'");
                if($NoticeDetail['message_object'] == '0'){
                    $NoticeDetail['allnum'] = $stu_num['num'] + $tech_nums['num'];
                }elseif($NoticeDetail['message_object'] == '1'){
                    $NoticeDetail['allnum'] = $stu_num['num'];
                }elseif($NoticeDetail['message_object'] == '2'){
                    $NoticeDetail['allnum'] = $tech_nums['num'];
                }
            }else{
                $allnums = $this->DataControl->selectOne("select count(messagestu_id) as num from eas_class_messagestu where message_id = '{$NoticeDetail['message_id']}'");
                $NoticeDetail['allnum'] = $allnums['num'];
            }
        }else{
            $NoticeDetail = array();
        }

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["message_radiourl"] = "音频URL";
        $field["message_mediajson"] = "视屏或者图片json";
        $field["message_createtime"] = "通知时间";
        $field["staffer_cnname"] = "通知人";
        $field["staffer_mobile"] = "通知人联系方式";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取班级通知详情成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班级通知详情失败', 'result' => $result);
        }

        return $res;
    }

    //修改头像
    function updateImgAction($paramArray){
        $data = array();

        $data['staffer_img'] = $paramArray['staffer_img'];
        $data['staffer_updatetime'] = time();

        $field = array();
        $field["staffer_img"] = "头像";
        if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['staffer_id']}'",$data) ) {

            $dataone = array();
            $dataone['marketer_img'] = $paramArray['staffer_img'];
            $this->DataControl->updateData("crm_marketer","staffer_id = '{$paramArray['staffer_id']}'",$dataone);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改头像成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '修改头像失败', 'result' => $result);
        }
        return $res;
    }

    //根据student_id查学员名字
    function StudentCnname($paramArray)
    {
        $student_cnname = $this->DataControl->getFieldOne("smc_student","student_cnname,student_enname,student_sex,student_img","student_id = '{$paramArray['student_id']}'");

        $result["name"] = $student_cnname['student_cnname'];
        $result["enname"] = $student_cnname['student_enname'];
        $result["student_sex"] = $student_cnname['student_sex'];
        $result["student_img"] = $student_cnname['student_img'];
        $res = array('error' => '0', 'errortip' => '获取学员名字成功', 'result' => $result);

        return $res;
    }

    //个人通知列表
    function PersonnalMessage($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                ms.message_isread,
                m.message_title,
                m.message_content,
                m.message_id,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN eas_class_message AS m on ms.message_id = m.message_id
                where ms.student_id = '{$paramArray['student_id']}'
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(ms.messagestu_id)
            FROM
                eas_class_messagestu AS ms
                LEFT JOIN eas_class_message AS m on ms.message_id = m.message_id
                
                where ms.student_id = '{$paramArray['student_id']}'");
        $allnums = $all_num[0][0];

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["message_isread"] = "是否已读";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取个人通知列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取个人通知列表失败', 'result' => $result);
        }

        return $res;
    }

    //学校公告列表
    function SchoolMessage($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                m.message_title,
                m.message_id,
                m.message_content,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                s.staffer_cnname
            FROM
                eas_class_message AS m left join smc_staffer as s on s.staffer_id = m.staffer_id
            WHERE
                m.school_id = '{$paramArray['school_id']}' and m.message_type = '1' and (m.message_object = '0' or m.message_object = '2')
            ORDER BY m.message_createtime DESC    
            Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(m.message_id)
            FROM
                eas_class_message AS m
            WHERE
                m.school_id = '{$paramArray['school_id']}' and m.message_type = '1' and (m.message_object = '0' or m.message_object = '2')");
        $allnums = $all_num[0][0];

        $field = array();
        $field["message_title"] = "通知标题";
        $field["message_content"] = "通知内容";
        $field["message_createtime"] = "时间";
        $field["message_isread"] = "是否已读";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取学校公告列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取学校公告列表失败', 'result' => $result);
        }

        return $res;
    }




    //获取班级信息
    function GetClassInfo($paramArray)
    {
        $sql = "
            SELECT
                c.class_cnname,
                co.course_cnname,
                co.course_branch,
                s.school_cnname,
                c.class_fullnums,
                (select count(ss.study_id) from smc_student_study as ss where ss.class_id= c.class_id and ss.study_isreading = '1') as stuNum
            FROM
                smc_class AS c
                LEFT JOIN smc_course AS co ON c.course_id = co.course_id
                left join smc_school as s on c.school_id = s.school_id
            WHERE c.class_id = '{$paramArray['class_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["course_cnname"] = "课程别名称";
        $field["course_branch"] = "课程别编号";
        $field["school_cnname"] = "校园名称";
        $field["class_fullnums"] = "总人数";
        $field["stuNum"] = "在班人数";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取班级信息成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班级信息失败', 'result' => $result);
        }

        return $res;
    }

    //Pc班级通知列表
    function PcClassMessage($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (m.message_title like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $start_time = strtotime($paramArray['start_time'] . '00:00:00');
            $datawhere .= " and m.message_createtime >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $end_time = strtotime($paramArray['end_time'] . '23:59:59');
            $datawhere .= " and m.message_createtime <= '{$end_time}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                m.message_id,
                m.message_title,
                m.message_content,
                c.class_cnname,
                c.class_enname,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id) as allnum,
                (select count(ms.messagestu_id) from eas_class_messagestu as ms where ms.message_id = m.message_id and ms.message_isread = 1) as readnum,
                concat(s.staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname
           FROM
                eas_class_message AS m
            left join
                smc_staffer as s on m.staffer_id = s.staffer_id
            left join
                eas_class_messagestu as ms on ms.message_id = m.message_id
            left join
                smc_class as c on c.class_id = ms.class_id
           WHERE {$datawhere} and m.school_id = '{$paramArray['school_id']}' and m.message_type = '0' GROUP BY m.message_id order by m.message_createtime DESC";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] !== "") {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['message_id'] = $dateexcelvar['message_id'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['message_createtime'] = $dateexcelvar['message_createtime'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['message_title'] = $dateexcelvar['message_title'];
                    $datearray['num'] = $dateexcelvar['readnum'].'/'.$dateexcelvar['allnum'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = array("通知ID", "班级中文名", "班级别名", "时间", "教师", "通知标题", "已读人数");
            $excelfileds = array('message_id', 'class_cnname', 'class_enname', 'message_createtime', 'staffer_cnname', 'message_title', 'num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "通知管理报表导出.xlsx");
            exit;
        }else{
            $sql .= " LIMIT {$pagestart},{$num}";
            $NoticeDetail = $this->DataControl->selectClear($sql);
            if($NoticeDetail){
                foreach ($NoticeDetail as &$val){
                    $val['num'] = $val['readnum'].'/'.$val['allnum'];
                }
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(m.message_id)
            FROM
                eas_class_message AS m
            WHERE {$datawhere} and m.school_id = '{$paramArray['school_id']}' and m.message_type = '0' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }

    //Pc公告列表
    function PcNoticeMessage($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (m.message_title like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $start_time  = strtotime($paramArray['start_time']);
            $datawhere .= " and m.message_createtime >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $end_time  = strtotime($paramArray['end_time'] . "23:59:59");
            $datawhere .= " and m.message_createtime <= '{$end_time}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                m.message_id,
                m.message_title,
                m.message_object,
                FROM_UNIXTIME( m.message_createtime, '%Y-%m-%d %H:%i' ) AS message_createtime,
                concat(s.staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname
           FROM
                eas_class_message AS m left join smc_staffer as s on m.staffer_id = s.staffer_id
           WHERE {$datawhere} and m.school_id = '{$paramArray['school_id']}' and m.message_type = '1'  order by m.message_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);
        if($NoticeDetail){
            foreach($NoticeDetail as &$v){
                if($v['message_object'] == '0'){
                    $v['message_object_name'] = '学员、老师';
                }elseif($v['message_object'] == '1'){
                    $v['message_object_name'] = '学员';
                }elseif($v['message_object'] == '2'){
                    $v['message_object_name'] = '老师';
                }
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(m.message_id)
            FROM
                eas_class_message AS m
            WHERE {$datawhere} and m.school_id = '{$paramArray['school_id']}' and m.message_type = '1' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }

    //删除通知/公告
    function delMessageAction($paramArray)
    {
        if ($this->DataControl->delData("eas_class_message", "message_id = '{$paramArray['message_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除失败', 'result' => $result);
        }
        return $res;
    }

    //编辑公告
    function updateMessageAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_class_message", "message_id", "message_id = '{$paramArray['message_id']}'");
        if ($activityOne) {
            $data = array();
            $data['message_title'] = $paramArray['message_title'];
            $data['message_content'] = $paramArray['message_content'];
            $data['message_object'] = $paramArray['message_object'];
            $data['message_mediajson'] = $paramArray['message_mediajson'];
            $data['message_updatetime'] = time();

            $field = array();
            $field['message_title'] = '标题';
            $field['message_content'] = '内容';

            if ($this->DataControl->updateData("eas_class_message", "message_id = '{$paramArray['message_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑公告成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑公告失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //发布公告
    function createMessageAction($paramArray){
        $data = array();
        $data['message_title'] = $paramArray['message_title'];
        $data['message_content'] = $paramArray['message_content'];
        $data['message_object'] = $paramArray['message_object'];
        $data['message_mediajson'] = $paramArray['message_mediajson'];
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['message_createtime'] = time();
        $data['message_type'] = '1';

        $field = array();
        $field['message_title'] = "课时时间分数名称";
        $field['message_content'] = "分钟数";

        if($this->DataControl->insertData('eas_class_message',$data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "发布公告成功", 'result' => $result);
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加课时时间设置失败', 'result' => $result);
        }
        return $res;
    }

    //教师下拉
    function MessageTeaListPCApi($request){
        $sql = "
            SELECT
                m.staffer_id,
                s.staffer_branch,
                s.staffer_cnname,
                s.staffer_enname 
            FROM
                eas_class_message AS m
                LEFT JOIN smc_staffer AS s ON s.staffer_id = m.staffer_id 
                WHERE m.school_id = '{$request['school_id']}'
            GROUP BY
                m.staffer_id";

        $teaList=$this->DataControl->selectClear($sql);

        if(!$teaList){
            $this->error = true;
            $this->errortip = "无教师数据";
            return false;
        }else{
            foreach ($teaList as &$var) {
                if($var['staffer_enname'] && $var['staffer_enname'] != ''){
                    $var['staffer_cnname'] = $var['staffer_cnname'] . '-' . $var['staffer_enname'] ;
                }
            }
        }

        $data['list']=$teaList;
        return $data;

    }

    //班级下拉
    function ClassApi($request){
        $sql = "
            SELECT 
                c.class_id,
                c.class_cnname
            FROM
                smc_class AS c
            WHERE c.class_id = '{$request['class_id']}'";

        $teaList=$this->DataControl->selectClear($sql);

        if(!$teaList){
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $data['list']=$teaList;
        return $data;

    }

    //发布公告
    function addCourseAction($paramArray){
        $data = array();
        $data['message_title'] = $paramArray['message_title'];
        $data['message_content'] = $paramArray['message_content'];
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['message_createtime'] = time();

        $field = array();
        $field['message_title'] = "通知标题";
        $field['message_content'] = "通知内容";

        if($this->DataControl->insertData('smc_course',$data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加课程成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团设置->课程相关设置",'添加课程',dataEncode($paramArray));

        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加课程失败', 'result' => $result);
        }
        return $res;
    }



}