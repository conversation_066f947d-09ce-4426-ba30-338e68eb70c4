<?php
/**
 * 客诉-微信模块
 */

namespace Model\Easx;


class UcsWechatModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    protected $code;
    protected $encryptedData;
    protected $iv;

    const appId = 'wx550be80d3f68edb1';
    const appSecret = '049263d2b71d977bc8186179dbbdd795';
    const nonceStr = 'kidcastle';
    const mch_id = '';
    const key = '';

    /**
     * 通过微信code换取用户信息
     * @param $paramArray
     * @return array|false|\mix|string
     */
    function wxLogin($paramArray)
    {
        $this->encryptedData = $paramArray['encryptedData'];
        $this->iv = $paramArray['iv'];

        $param = array(
            'appid' => self::appId,
            'secret' => self::appSecret,
            'grant_type' => "authorization_code",
            'js_code' =>$paramArray['code']
        );
        $getBackurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($param), "GET");
        $json_play = new \Webjson();
        $wxResult = $json_play->decode($getBackurl, "1");
//        $wxResult = json_decode($getBackurl, true);
        if (empty($wxResult)) {
            // 为什么以empty判断是否错误，这是根据微信返回
            // 这种情况通常是由于传入不合法的code
            $res = array('error' => 1, 'errortip' => "获取session_key及openID时异常，微信内部错误");
            ajax_return($res);

        }else{
            $loginFail = array_key_exists('errcode', $wxResult);
            if ($loginFail) {
                $res = array('error' => 1, 'errortip'=>'微信报错!','msg' => $wxResult['errmsg'],'errorCode'=>$wxResult['errcode']);
                ajax_return($res);

            }else{
                $mobileInfo = $this->getMobile($wxResult['session_key'],$this->encryptedData,$this->iv);
                $wxResult['phoneNumber'] = $mobileInfo['phoneNumber'];

                return $wxResult;
            }

        }

    }

    /**
     * 获取手机号
     * @param $session_key
     * @param $session_key
     * @return mixed
     */
    function getMobile($session_key,$encryptedData,$iv)
    {
        if(empty($session_key))
        {
            ajax_return(array('error' => 1, 'errortip'=>'获取session_key时异常,session_key为空!'));

        }

        include_once "Core/Tools/Wxcode/wxBizDataCrypt.php";

        $pc = new \WXBizDataCrypt(self::appId,$session_key);
        $errCode = $pc->decryptData($encryptedData,$iv,$data);
        if ($errCode == 0) {
            return json_decode($data,true);

        } else {
            ajax_return(array('error' => 1, 'errortip'=>'微信获取手机号错误!','errorCode'=>$errCode));

        }

    }

    //微信小程序二维码
    function getQRcodeApi($request)
    {
        $tokenOne = $this->DataControl->getFieldOne("ucs_weixin_token", "token_failuretime,token_string"
            , "token_site = '1' and token_type = '1' ", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::appId,
                'secret' => self::appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '1';
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'];
            $this->DataControl->insertData("ucs_weixin_token", $data);
            $access_token = $dataArray['access_token'];
        }

        $qcode = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={$access_token}";
        $scene = "company_id=".$request['company_id'];
        $pageStr = "pages/customer/index/main";
        $param = json_encode(array("scene" => $scene,"page" => $pageStr,"width" => '300'));

        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;

        return $strImg;
    }

	//跨界活动 -- 活动二维码
    function getWXQRcodeApi($request)
    {
        $tokenOnestr = request_by_curl("https://crmapi.kedingdang.com/Api/getNewWxToken",'',"POST",array());
        $tokenOne = json_decode($tokenOnestr,true);
        $tokenOne = $tokenOne['result'];
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::appId,
                'secret' => self::appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'];
            request_by_curl("https://crmapi.kedingdang.com/Api/addNewWxToken", dataEncode($data), "POST",array());

            $access_token = $dataArray['access_token'];
        }

        $qcode = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$access_token}";

        $pathStr = "pages/transboundary/customer/main?company_id=".$request['company_id']."&school_id=".$request['school_id']."&activity_id=".$request['activity_id'];

        $param = json_encode(array("path" => $pathStr, "width" => 1500));

        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");

        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;

        return $strImg;
    }

	//跨界活动 -- 商家二维码
    function getWXQRcodesApi()
    {
        $tokenOnestr = request_by_curl("https://crmapi.kedingdang.com/Api/getNewWxToken",'',"POST",array());
        $tokenOne = json_decode($tokenOnestr,true);
        $tokenOne = $tokenOne['result'];
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $paramarray = array(
                'appid' => self::appId,
                'secret' => self::appSecret,
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'];
            request_by_curl("https://crmapi.kedingdang.com/Api/addNewWxToken", dataEncode($data), "POST",array());

            $access_token = $dataArray['access_token'];
        }

        $qcode = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$access_token}";

        $pathStr = "pages/transboundary/business/main";

        $param = json_encode(array("path" => $pathStr, "width" => 1500));

        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");

        //生成图片 -- 加上头部 header
        $base64 = base64_encode($result);

        $strImg = "data:image/png" . ";base64," . $base64;

        return $strImg;
    }

    //把请求发送到微信服务器换取二维码 -- 微信小程序二维码
    function httpRequest($url, $data = '', $method = 'GET')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

}