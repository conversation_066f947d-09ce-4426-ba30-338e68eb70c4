<?php
/**
 * 客诉-公共模块(必要参数验证)
 */
namespace Model\Easx;


class UcsCommonModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $re_postbe_id = 0;//操作人
    public $publicArray = array();
    public $postbeisUcsuser;//是否有客诉权限 0:无 1:有
    public $postbeUcsuserLevel;//客诉权限级 1:校区客服 2:校长 3:集团客服 4:集团高管
    public $moduleOne = array();

    function __construct($publicArray = array())
    {
        parent::__construct();
        if (is_array($publicArray)) {
            $this->setPublic($publicArray);
            $this->publicarray = $publicArray;
            $this->company_id = $publicArray['company_id'];
            $this->school_id = $publicArray['school_id'];
            $this->staffer_id = $publicArray['staffer_id'];
            $this->re_postbe_id = $publicArray['re_postbe_id'];
            $this->getUcsPositionLevel($this->company_id, $this->school_id, $this->staffer_id, $this->re_postbe_id);
        }

    }

    /**
     * 自定义异常
     * @param $error
     * @param $errortip
     * @param array $result
     */
    function throwException($error,$errortip,$result=array())
    {
        $this->error = $error;
        $this->errortip = $errortip;
        $this->result = $result;
    }

    /**
     * 获取职工客诉最高权限 -zjc
     * @param int $company_id
     * @param int $school_id
     * @param int $staffer_id
     */
    function getUcsPositionLevel($company_id = 0, $school_id = 0, $staffer_id = 0, $re_postbe_id = 0)
    {
        //判断当前集团角色中 是否有客诉权限 及 权限级别 若有则为当前权限
        $companyPosition = $this->DataControl->selectOne("
SELECT postbe_isucsuser,postbe_ucsuserlevel 
FROM gmc_staffer_postbe 
WHERE postbe_id='{$re_postbe_id}'
");
        if (!empty($companyPosition)) {
            if ($companyPosition['postbe_isucsuser'] == 1) {
                if (in_array($companyPosition['postbe_ucsuserlevel'], array(1, 2, 3, 4))) {
                    $this->postbeisUcsuser = 1;
                    $this->postbeUcsuserLevel = $companyPosition['postbe_ucsuserlevel'];
                } else {
                    $this->postbeisUcsuser = 0;
                    $this->postbeUcsuserLevel = 0;
                }

            } else {
                //无集团客诉权限 查询学校角色中 是否有客诉权限 及 权限级别
//                $schoolPosition = $this->DataControl->selectOne("
//SELECT postbe_isucsuser,postbe_ucsuserlevel
//FROM gmc_staffer_postbe
//WHERE company_id=0 AND school_id='{$school_id}' AND staffer_id='{$staffer_id}'
//");
//                if($schoolPosition['postbe_isucsuser']==1)
//                {
//                    $this->postbeisUcsuser = 1;
//                    $this->postbeUcsuserLevel = $schoolPosition['postbe_ucsuserlevel'];
//                }else{
                $this->postbeisUcsuser = 0;
                $this->postbeUcsuserLevel = 0;
//                }

            }
        } else {
            if ($re_postbe_id == 0) {
                //超级管理员
                $this->postbeisUcsuser = 1;
                $this->postbeUcsuserLevel = 4;
            } else {
                $this->postbeisUcsuser = 0;
                $this->postbeUcsuserLevel = 0;
            }

        }


    }

    /**
     * 必要参数
     * @param $publicarray
     */
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = 1;
            $this->errortip = "企业ID必传1";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }

        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = 1;
            $this->errortip = "学校ID必须传入";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }

        if (isset($publicarray['staffer_id'])) {
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = 1;
            $this->errortip = "职员ID必须传入";
            $this->result = array();
            ajax_return(array('error' => $this->error, 'errortip' => $this->errortip, 'result' => $this->result));
        }


    }

    /**
     * 获取职员信息
     * @param $paramArray
     * @return array
     */
    function getStafferInfo($paramArray)
    {
        $stafferOne = $this->DataControl->selectOne("select s.staffer_id,s.account_class,s.staffer_cnname,s.staffer_enname,s.staffer_mobile,
s.staffer_branch,i.info_birthday as staffer_birthday,s.staffer_img,s.staffer_sex,
c.company_id,c.company_isaddstudent,c.company_logo,c.company_ismajor,c.company_isvoucher,c.company_isinvoice
from smc_staffer as s LEFT JOIN smc_staffer_info AS i ON s.staffer_id = i.staffer_id
LEFT JOIN gmc_company as c ON s.company_id=c.company_id where s.staffer_id = '{$paramArray['staffer_id']}' limit 0,1");

        $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id,organize_id,school_id,postbe_ucsuserlevel", "postbe_id = '{$paramArray['re_postbe_id']}'");


        if ($stafferOne['account_class'] == '0') {
            if (isset($paramArray['school_id']) && $paramArray['school_id']) {
                $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_isvoucher,s.school_cnname,s.school_shortname,s.school_shortname,s.school_branch,s.school_renewtime
,s.school_changestime,s.school_renewsttime,s.school_renewentime,s.school_changessttime,s.school_changesentime,s.school_istwocode,s.school_isscan FROM smc_school as s
WHERE s.school_id = '{$paramArray['school_id']}' limit 0,1");
            } else {
                $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_isvoucher,s.school_cnname,s.school_shortname,s.school_shortname,s.school_branch,s.school_renewtime
,s.school_changestime,s.school_renewsttime,s.school_renewentime,s.school_changessttime,s.school_changesentime,s.school_istwocode,s.school_isscan FROM smc_school as s
WHERE s.school_id in (
SELECT p.school_id FROM gmc_staffer_postbe AS p WHERE p.staffer_id = '{$stafferOne['staffer_id']}' AND p.school_id <> '0'
UNION ALL
SELECT p.school_id FROM gmc_company_organizeschool AS p, gmc_staffer_postbe AS b WHERE p.organize_id = b.organize_id AND b.staffer_id = '{$stafferOne['staffer_id']}' AND b.school_id = '0')
ORDER BY s.school_istemp DESC limit 0,1");
            }
        } else {
            $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_isvoucher,s.school_cnname,s.school_shortname,s.school_shortname,s.school_branch,s.school_renewtime
,s.school_changestime,s.school_renewsttime,s.school_renewentime,s.school_changessttime,s.school_changesentime,s.school_istwocode,s.school_isscan FROM smc_school as s
WHERE s.school_id = '{$paramArray['school_id']}' ORDER BY s.school_id ASC,s.school_istemp DESC limit 0,1");
        }
        $qrcodeurl = "https://ptc.kidcastle.com.cn/AddStudent?branch={$schoolOne['school_branch']}&schoolname={$schoolOne['school_shortname']}";
        $stafferOne['newstujoinqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($qrcodeurl));

        $result = array();
        if ($schoolOne) {
            $result = array();
            $result['staffer_id'] = $stafferOne['staffer_id'];
            $result['company_id'] = $stafferOne['company_id'];
            $result['school_id'] = $schoolOne['school_id'];
            $result['school_cnname'] = $schoolOne['school_shortname'];
            $result['school_renewtime'] = $schoolOne['school_renewtime'];
            $result['school_changestime'] = $schoolOne['school_changestime'];
            $result['school_isvoucher'] = $schoolOne['school_isvoucher'];
            $result['company_isaddstudent'] = $stafferOne['company_isaddstudent'];
            $result['company_ismajor'] = $stafferOne['company_ismajor'];
            $result['company_isvoucher'] = $stafferOne['company_isvoucher'];
            $result['company_isinvoice'] = $stafferOne['company_isinvoice'];
            $result['school_renewsttime'] = $schoolOne['school_renewsttime'];
            $result['school_renewentime'] = $schoolOne['school_renewentime'];
            $result['school_changessttime'] = $schoolOne['school_changessttime'];
            $result['school_changessttime'] = $schoolOne['school_changessttime'];
            $result['school_isscan'] = $schoolOne['school_isscan'];
            $result['school_istwocode'] = $schoolOne['school_istwocode'];
            $result['school_branch'] = $schoolOne['school_branch'];
            $result['today'] = date("Y-m-d", time());
            $result['newstujoinqrcode'] = $stafferOne['newstujoinqrcode'];//20205月暂用新生录入二维码

            $stafferArray = $this->DataControl->selectClear("SELECT c.post_name,p.postbe_ucsuserlevel from gmc_staffer_postbe as p left join gmc_company_post as c on p.post_id = c.post_id
where p.staffer_id = '{$paramArray['staffer_id']}'");
            if ($stafferArray) {
                foreach ($stafferArray as &$InfoOne) {
                    $InfoOne['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
                    $InfoOne['staffer_enname'] = $stafferOne['staffer_enname'];
                    $InfoOne['staffer_mobile'] = $stafferOne['staffer_mobile'];
                    $InfoOne['staffer_branch'] = $stafferOne['staffer_branch'];
                    $InfoOne['staffer_birthday'] = $stafferOne['staffer_birthday'];
                    $InfoOne['staffer_img'] = $stafferOne['staffer_img'];
                    $InfoOne['staffer_sex'] = $stafferOne['staffer_sex'];
                    $InfoOne['account_class'] = $stafferOne['account_class'];
                    $InfoOne['jituan'] = "集团";
                }
            } else {
                $data = array();
                $data['post_name'] = '';
                $data['postbe_ucsuserlevel'] = $postbeOne['postbe_ucsuserlevel'];
                $data['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
                $data['staffer_enname'] = $stafferOne['staffer_enname'];
                $data['staffer_mobile'] = $stafferOne['staffer_mobile'];
                $data['staffer_branch'] = $stafferOne['staffer_branch'];
                $data['staffer_birthday'] = $stafferOne['staffer_birthday'];
                $data['staffer_img'] = $stafferOne['staffer_img'];
                $data['staffer_sex'] = $stafferOne['staffer_sex'];
                $data['account_class'] = $stafferOne['account_class'];
                $data['xuexiao'] = "学校";

                $stafferArray[] = $data;
            }

            $result['stafferInfo'] = $stafferArray ? $stafferArray : array();

            if ($postbeOne['school_id'] == '0') {
                $school_id = $this->DataControl->getFieldOne("gmc_company_organizeschool", "school_id", "organize_id = '{$postbeOne['organize_id']}'");
                $sql = "SELECT
                    p.postbe_id,
                    p.postpart_id,
                    p.postbe_ucsuserlevel,
                    o.post_name,
                    o.post_istopjob,
                    (select school_shortname from smc_school WHERE school_id = '{$school_id['school_id']}') as school_cnname,
                    (select school_address from smc_school WHERE school_id = '{$school_id['school_id']}') as school_address,
                    (select school_id from smc_school WHERE school_id = '{$school_id['school_id']}') as school_id
                FROM gmc_staffer_postbe AS p
                    LEFT JOIN gmc_company_post AS o ON p.post_id = o.post_id
                    LEFT JOIN smc_school AS c ON p.school_id = c.school_id
                WHERE p.postbe_id = '{$paramArray['re_postbe_id']}'
                ORDER BY p.postbe_ismianjob DESC";
            } else {
                $sql = "SELECT p.postbe_id, p.postpart_id, p.postbe_ucsuserlevel, o.post_name, o.post_istopjob, c.school_id, c.school_shortname as  school_cnname, c.school_address
                FROM gmc_staffer_postbe AS p,gmc_company_post AS o,smc_school AS c
                WHERE p.post_id = o.post_id AND p.school_id = c.school_id AND p.postbe_id = '{$paramArray['re_postbe_id']}'
                ORDER BY p.postbe_ismianjob DESC";
            }

            $postpartArray = $this->DataControl->selectClear($sql);
            if (!$postpartArray) {
                $postpartArray = array();
            } else {
                foreach ($postpartArray as &$postpartOne) {
                    $postpartOne['staffer_cnname'] = $stafferOne['staffer_cnname'];
                    $postpartOne['staffer_enname'] = $stafferOne['staffer_enname'];
                    $postpartOne['staffer_mobile'] = $stafferOne['staffer_mobile'];
                    $postpartOne['staffer_branch'] = $stafferOne['staffer_branch'];
                    $postpartOne['staffer_birthday'] = $stafferOne['staffer_birthday'];
                    $postpartOne['staffer_img'] = $stafferOne['staffer_img'];
                    $postpartOne['staffer_sex'] = $stafferOne['staffer_sex'];
                    $postpartOne['account_class'] = $stafferOne['account_class'];
                }
            }
            if ($stafferOne['account_class'] == '1') {
                $temp = array();
                $temp['post_istopjob'] = 1;
                $postpartArray[] = $temp;
            }

            $result['postpart'] = $postpartArray;

            $contractOne = $this->getContract($this->company_id);

            if ($contractOne) {
                $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id=6");

                if (!$promoduleList) {
                    ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"), $this->companyOne['company_language']);
                }
                $datawhere = "1";
                if($postbeOne['postbe_ucsuserlevel'] == '1' || $postbeOne['postbe_ucsuserlevel'] == '3'){
                    $datawhere = "m.module_id <>'607'";
                }
                $sql = "select m.module_id,m.module_name,m.module_markurl,m.module_icon,m.father_id
                from imc_module as m 
                where m.product_id=6 and {$datawhere}
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                order by m.module_weight asc,m.module_id asc";
                $moduleList = $this->DataControl->selectClear($sql);

                $moduleArray = $this->trees($moduleList);

            } else {
                $datawhere = "1";
                if($postbeOne['postbe_ucsuserlevel'] == '1' || $postbeOne['postbe_ucsuserlevel'] == '3'){
                    $datawhere = "module_id <>'607'";
                }
                if ($paramArray['company_id'] == '1001') {
                    $moduleArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id
from imc_module where module_class = '5' and module_ismajor <= '{$stafferOne['company_ismajor']}' and {$datawhere} order by module_weight ASC");
                } elseif ($paramArray['company_id'] == '8888') {
                    $moduleArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id
from imc_module where module_class = '5' and module_ismajor <= '{$stafferOne['company_ismajor']}' and {$datawhere} and module_isshow = '1' order by module_weight ASC");
                } else {
                    $moduleArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id
from imc_module where module_class = '5' and module_ismajor <= '{$stafferOne['company_ismajor']}' and {$datawhere} and module_isshow = '1' and module_super = '0' order by module_weight ASC");
                }
                $moduleArray = $this->trees($moduleArray);
            }

            $result['children'] = $moduleArray;

        } else {
            $result['stafferInfo'] = array();
            $result['postpart'] = array();
            $result['children'] = array();

        }

//        $result['status'] = $status;
        $result['img'] = $stafferOne['company_logo'];
        $result['repairorder_message_num'] = "1";
        if ($result) {
            $this->error = 0;
            $this->errortip = "获取成功";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "学校信息未获取成功";
            $this->result = $result;
            return false;
        }
    }

    function getModuleList($request){

        $contractOne = $this->getContract($this->company_id);

        if (!$contractOne) {
            $this->getModuleListbak($request);
//            ajax_return(array('error' => 1, 'errortip' => "无可用合同!"),$this->companyOne['company_language']);
        }

        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$request['module_class']}'");

        if (!$promoduleList) {
            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"),$this->companyOne['company_language']);
        }

        $moduleArray = array_column($promoduleList, 'module_id');

        $sql = "select m.module_id as id,m.module_name as label,m.module_level,m.module_markurl,m.module_icon,m.father_id
                ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->company_id}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                where m.product_id='{$request['module_class']}'
                having status=1
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
        $moduleList = $this->DataControl->selectClear($sql);

        $tree=$this->getModuleTree($moduleList,$moduleArray,'id','module_markurl');

        $this->getArray($tree,'id','children');

        $module_id=array();

        if(isset($request['url']) && $request['url']!=''){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$request['url']}'");

            if($request['re_postbe_id']==0){

                if(!in_array($module['module_id'],$this->moduleOne)){
                    ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
                }

            }else{
                $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$request['re_postbe_id']}'");

                $sql="select u.module_id
                  from gmc_staffer_usermodule as u
                  where u.postrole_id ='{$postrole['postrole_id']}' and u.module_id ='{$module['module_id']}'
                  and u.module_id in (".implode(",",$this->moduleOne).")";

                $powerOne = $this->DataControl->selectOne($sql);
                if(!$powerOne){
                    ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
                }
            }

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];
        }

        $result = array();
        $result['children'] = $tree;

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);
    }

    function getPowerList($request){

        $contractOne = $this->getContract($this->company_id);

        if (!$contractOne) {
            $this->getPowerListbak($request);
//            ajax_return(array('error' => 1, 'errortip' => "无可用合同!"),$this->companyOne['company_language']);
        }

        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$request['module_class']}'");

        if (!$promoduleList) {
            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"),$this->companyOne['company_language']);
        }

        $moduleArray = array_column($promoduleList, 'module_id');

        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");

        $sql = "select m.module_id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id
                ,ifnull((select 1 from gmc_staffer_commonmodule as co where co.module_id=m.module_id and co.staffer_id='{$request['staffer_id']}'),0) as isselect
                ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->company_id}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join gmc_staffer_usermodule as u on u.module_id=m.module_id
                where m.product_id='{$request['module_class']}' and u.postrole_id = '{$postbeOne['postrole_id']}'
                having status=1
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
        $moduleList = $this->DataControl->selectClear($sql);

        $tree=$this->getModuleTree($moduleList,$moduleArray,'module_id','url');
        $this->getArray($tree,'module_id','children');

        $module_id=array();

        if(isset($request['url']) && $request['url']!=''){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$request['url']}'");

            if($request['re_postbe_id']==0){

                if(!in_array($module['module_id'],$this->moduleOne)){
                    ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
                }

            }else{
                $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$request['re_postbe_id']}'");

                $sql="select u.module_id
                  from gmc_staffer_usermodule as u
                  where u.postrole_id ='{$postrole['postrole_id']}' and u.module_id ='{$module['module_id']}'
                  and u.module_id in (".implode(",",$this->moduleOne).")";

                $powerOne = $this->DataControl->selectOne($sql);
                if(!$powerOne){
                    ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
                }
            }

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];
        }

        $result = array();
        $result['children'] = $tree;

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);
    }

    //机构管理 -- 首页
    function getModuleListbak($paramArray)
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module', 'module_name', "module_class = '{$paramArray['module_class']}'");
        $result['title'] = $module_name['module_name'];

        $major = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$paramArray['company_id']}'");
        if ($paramArray['company_id'] == '1001') {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
        } else {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_isshow = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
        }

        /* if($major['company_ismajor'] == '1'){
             if ($paramArray['company_id'] == '1001') {
                 $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' order by module_weight ASC");
             } else {
                 $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_isshow = '1' order by module_weight ASC");
             }
         }else{
             if ($paramArray['company_id'] == '1001') {
                 $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_ismajor = '0' order by module_weight ASC");
             } else {
                 $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_isshow = '1' and module_ismajor = '0' order by module_weight ASC");
             }
         }*/

        if($paramArray['url']){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$paramArray['url']}'");
            $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$paramArray['re_postbe_id']}'");
            $a = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postrole['postrole_id']}' and module_id = '{$module['module_id']}'");

            if(!$a && $paramArray['re_postbe_id'] != '0'){
                ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
            }

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];

            $data = $this->treesss($data);

            $result['children'] = $data;
        }else{
            $data = $this->tree($data);

            $result['children'] = $data;
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);
    }

    function getPowerListbak($paramArray)
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module', 'module_name', "module_class = '{$paramArray['module_class']}'");
        $result['title'] = $module_name['module_name'];

        $major = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$paramArray['company_id']}'");

        $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_ucsuserlevel", "postbe_id = '{$paramArray['re_postbe_id']}'");

        if($paramArray['re_postbe_id'] == '0'){
            if($paramArray['company_id'] == '1001'){
                $data = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_img from imc_module where module_class = '5' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
            }else{
                $data = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_img from imc_module where module_class = '5' and module_isshow = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
            }
        }else{
            $datawhere = "1";
            if($postbeOne['postbe_ucsuserlevel'] == '1' || $postbeOne['postbe_ucsuserlevel'] == '3'){
                $datawhere = "module_id <>'607'";
            }
            if ($paramArray['company_id'] == '1001') {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id
from imc_module where module_class = '5' and module_ismajor <= '{$major['company_ismajor']}' and {$datawhere} order by module_weight ASC");
            } else {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id
from imc_module where module_class = '5' and module_ismajor <= '{$major['company_ismajor']}' and {$datawhere} and module_isshow = '1' order by module_weight ASC");
            }
        }

        $data = $this->powerTree($data, $paramArray);

        $result['children'] = $data;

        if($paramArray['url']){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$paramArray['url']}' and module_class = '5'");

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];
        }
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);
    }

    function powerTree($items, $paramArray)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;

                    $a = $this->DataControl->getFieldOne("gmc_staffer_commonmodule","module_id","staffer_id = '{$paramArray['staffer_id']}' and module_id = '{$v['module_id']}'");
                    if($a){
                        $son[$k]['isselect'] = '1';
                    }else{
                        $son[$k]['isselect'] = '0';
                    }

                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['module_id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    $son[$k]['postrole_id'] = $v['postrole_id'];
                    $son[$k]['icon'] = $v['module_icon'];
                    $son[$k]['module_img'] = $v['module_img'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $a = $this->DataControl->getFieldOne("gmc_staffer_commonmodule","module_id","staffer_id = '{$paramArray['staffer_id']}' and module_id = '{$value['module_id']}'");
                            if($a){
                                $son[$k]['children'][$key]['isselect'] = '1';
                            }else{
                                $son[$k]['children'][$key]['isselect'] = '0';
                            }

                            $postrole_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$paramArray['postbe_id']}'");
                            $url = $this->DataControl->selectClear("SELECT
                                    m.module_markurl
                                FROM
                                    imc_module AS m
                                    LEFT JOIN gmc_staffer_usermodule AS u ON m.module_id = u.module_id
                                WHERE
                                    m.father_id = '{$value['module_id']}' and u.postrole_id = '{$postrole_id['postrole_id']}'
                                GROUP BY
                                    m.module_id");
                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['module_id'] = $value['module_id'];
                            $son[$k]['children'][$key]['postrole_id'] = $value['postrole_id'];
                            $son[$k]['children'][$key]['module_img'] = $value['module_img'];
                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $a = $this->DataControl->getFieldOne("gmc_staffer_commonmodule","module_id","staffer_id = '{$paramArray['staffer_id']}' and module_id = '{$values['module_id']}'");
                                    if($a){
                                        $son[$k]['children'][$key]['children'][$keys]['isselect'] = '1';
                                    }else{
                                        $son[$k]['children'][$key]['children'][$keys]['isselect'] = '0';
                                    }

                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_img'] = $values['module_img'];

                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }

    function getModuleTree($moduleList,$moduleArray,$field='id',$url='url'){
        $tree=$this->getTree($moduleList,$field,'father_id','children');

        if($tree){
            $mum=0;
            foreach($tree as $key=>$val){
                if (!in_array($val[$field], $moduleArray) || !isset($val['children'])) {
                    unset($tree[$key]);
                }else{
                    if(!$val[$url] || $val['module_level']==2){
                        $tree[$key][$url]=$val['children'][0][$url];
                    }
                    $tree[$key]['index']=$mum;

                    $m=0;
                    if($val['children']){
                        foreach($val['children'] as $k=>$v){
                            if (!in_array($v[$field], $moduleArray)) {
                                unset($tree[$key]['children'][$k]);
                            }else{
                                if(!$v[$url] || $v['module_level']==2){
                                    if($v['children'][0][$url] && isset($v['children'][0][$url])){
                                        $tree[$key]['children'][$k][$url]=$v['children'][0][$url];
                                    }
                                }
                                $tree[$key]['children'][$k]['activeIndex']=$mum.'-'.($m+1);
                                $m++;
                            }
                        }

                        $mum++;
                    }
                }
            }
        }

        return $tree;
    }

    function getArray($arr,$field,$child){
        foreach ($arr as $v) {
            if ($v[$field]) {
                $this->moduleOne[]=$v[$field];
                if($v[$child]){
                    $this->getArray($v[$child],$field,$child);
                }
            }
        }
    }

    function tree($items)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['label'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['module_markurl'] = $v['module_markurl'];
                    $son[$k]['module_icon'] = $v['module_icon'];
                    if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                        $son[$k]['disabled'] = true;
                    } else {
                        $son[$k]['disabled'] = false;
                    }
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $url = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
//                            $url = $this->DataControl->getFieldOne("imc_module","module_markurl","father_id = '{$value['module_id']}'");

                            $son[$k]['children'][$key]['label'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                                $son[$k]['children'][$key]['disabled'] = true;
                            } else {
                                $son[$k]['children'][$key]['disabled'] = false;
                            }
                            if ($url) {
                                $son[$k]['children'][$key]['module_markurl'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['module_markurl'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['module_icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['label'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_markurl'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_icon'] = $values['module_icon'];
                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }




    function treesss($items)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['module_id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    $son[$k]['icon'] = $v['module_icon'];
                    if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                        $son[$k]['disabled'] = true;
                    } else {
                        $son[$k]['disabled'] = false;
                    }
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $url = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
//                            $url = $this->DataControl->getFieldOne("imc_module","module_markurl","father_id = '{$value['module_id']}'");

                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['module_id'] = $value['module_id'];
                            if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                                $son[$k]['children'][$key]['disabled'] = true;
                            } else {
                                $son[$k]['children'][$key]['disabled'] = false;
                            }
                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }

    /**
     * 合同?
     * @param $company_id
     * @return false|mixed
     */
    function getContract($company_id)
    {
        $sql = "select sc.edition_id
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$company_id}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

        $contractOne = $this->DataControl->selectOne($sql);

        return $contractOne;
    }

    /**
     * @param $items
     * @return array
     */
    function trees($items)
    {
        $son = array();
        $count = 0;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
//                    $son[$k]['url'] = $v['module_markurl'].'?module_id='.$v['module_id'];
                    $son[$k]['icon'] = $v['module_icon'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $markurlOne = $this->DataControl->selectOne("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' order by module_weight ASC limit 0,1");
                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . $count;
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($markurlOne) {
                                $son[$k]['children'][$key]['url'] = $markurlOne['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];

                                }
                            }
                        }
                    }
                    $count = 0;
                }
            }
        }
        return $son;
    }

    /**
     * 处理方式列表 -zjc
     * $issensitive 是否敏感工单 0否 1是
     * @return bool
     */
    function processModeList($repairorder_status, $issensitive = 0)
    {
        //需要权限判断
//        if(!empty($company_id) && $company_id>0)//集团
//        {
//
//        }elseif ($company_id==0 && $school_id>0)//校区
//        {
//
//        }elseif ()
//        {
//
//        }
        $postbeisUcsuser = $this->postbeisUcsuser;//是否有客诉权限 0:无 1:有
        $postbeUcsuserLevel = $this->postbeUcsuserLevel;//客诉权限级 1:校区客服 2:校长 3:集团客服 4:集团高管

//        $dataList = array();
        //集团高管 集团客服 校长 校区客服

        //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案 4:已回访
        $dataList = array();
        $key = 0;
        $dataList[$key]['processmode_name'] = '查看';
        $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
        $dataList[$key]['frontend_methods'] = 'see';
        $key++;

        if($repairorder_status == 0){
            $dataList[$key]['processmode_name'] = '受理';
            $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
            $dataList[$key]['frontend_methods'] = 'acceptance';
            $key++;
            if($issensitive == '0') {
                $dataList[$key]['processmode_name'] = '删除';
                $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
                $dataList[$key]['frontend_methods'] = 'delete';
                $key++;
            }
        }
        if ($repairorder_status == 1 || $repairorder_status == 2) {
            $dataList[$key]['processmode_name'] = '修改';
            $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
            $dataList[$key]['frontend_methods'] = 'edit';
            $key++;

            if ($postbeUcsuserLevel == 2 || $postbeUcsuserLevel == 4) {
                $dataList[$key]['processmode_name'] = '催单';
                $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
                $dataList[$key]['frontend_methods'] = 'reminder';
                $key++;
            }
            if ($repairorder_status == 1) {
                $dataList[$key]['processmode_name'] = '跟进';
                $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
                $dataList[$key]['frontend_methods'] = 'follow';
                $key++;
            }
        }

        if ($repairorder_status == 2) {
            if($issensitive == '1'){
                if($postbeUcsuserLevel == 2 || $postbeUcsuserLevel == 4){
                    $dataList[$key]['processmode_name'] = '结案';
                    $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
                    $dataList[$key]['frontend_methods'] = 'caseClosed';
                    $key++;
                }
            }else{
                $dataList[$key]['processmode_name'] = '结案';
                $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
                $dataList[$key]['frontend_methods'] = 'caseClosed';
                $key++;
            }
        }

        if ($repairorder_status == 3) {
            $dataList[$key]['processmode_name'] = '回访';
            $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
            $dataList[$key]['frontend_methods'] = 'returnVisit';
            $key++;
            
            $dataList[$key]['processmode_name'] = '再跟进';
            $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
            $dataList[$key]['frontend_methods'] = 'nextFollow';
            $key++;
        }

        if ($repairorder_status == 4) {
            $dataList[$key]['processmode_name'] = '再跟进';
            $dataList[$key]['processmode_note'] = '该处理方式的简单说明';
            $dataList[$key]['frontend_methods'] = 'nextFollow';
        }

        return $dataList;
    }

    /**
     * 工单跟进种类
     * @param $paramArray repairorder_id
     * @return bool
     */
    function followupList($paramArray)
    {

        $dataList = [];
        $one = [
            'followup_type' => 1,
            'followup_name' => '投诉跟进',
            'followup_note' => "选择后输入跟进内容 工单处理状态变更为处理中",
            'followup_methods' => 'nextFollow'
        ];
        $two = [
            'followup_type' => 2,
            'followup_name' => '投诉已处理',
            'followup_note' => "选择后输入已处理内容 工单处理状态变更为已处理",
            'followup_methods' => 'nextFollow'
        ];
        $three = [
            'followup_type' => 3,
            'followup_name' => '内部流转',
            'followup_note' => "选择后出现转发组织、转发对象的操作控件 转发组织-默认显示登陆账号所属校区",
            'followup_methods' => 'nextFollow'
        ];
        $four = [
            'followup_type' => 4,
            'followup_name' => '上升至集团',
            'followup_note' => "选择后出现转发组织、转发对象的操作控件 转发组织-默认显示集团",
            'followup_methods' => 'nextFollow'
        ];
        $five = [
            'followup_type' => 5,
            'followup_name' => '下发至校区',
            'followup_note' => "选择后出现转发组织、转发对象的操作控件 转发组织-默认显示集团 转发对象-显示角色为集团高管的人员",
            'followup_methods' => 'nextFollow'
        ];
        $six = [
            'followup_type' => 6,
            'followup_name' => '上升为敏感事件',
            'followup_note' => "点击【上升为敏感事件】出现二次弹窗，点击【确定】则将该工单转为敏感工单 并停留在工单受理页面",
            'followup_methods' => 'nextFollow'
        ];
        $seven = [
            'followup_type' => 7,
            'followup_name' => '敏感事件处理',
            'followup_note' => "选择后出现处理人员选择控件，点击控件出现人员选择弹窗 集团高管角色在此列表可选人员为集团的客服（可多选） 校长角色在此列表可选人员为校区的客服（可多选）",
            'followup_methods' => 'nextFollow'
        ];

        //工单信息
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.company_id,r.school_id,r.repairorder_status,r.repairorder_issensitive,r.repairorder_nowposition
        FROM ucs_repairorder r
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}'
        ");

        array_push($dataList,$three);//内部流转
        //工单状态
        if($repairorderData['repairorder_status']==1 || $repairorderData['repairorder_status']==2)
        {
            array_push($dataList,$one);//投诉跟进
            array_push($dataList,$two);//投诉已处理

        }
        if($repairorderData['repairorder_issensitive']==0)
        {
            array_push($dataList,$six);//上升为敏感事件
        }

        if($repairorderData['repairorder_issensitive']==1 && in_array($this->postbeUcsuserLevel,array('2','4')) )
        {
            array_push($dataList,$seven);//敏感事件处理
        }

        //定位工单当前位置(集团\校区)
        if($repairorderData['repairorder_nowposition']==1)
        {
            array_push($dataList,$five);//下发至校区
        }elseif ($repairorderData['repairorder_nowposition']==2){
            array_push($dataList,$four);//上升至集团
        }else{
            array_push($dataList,$five);//下发至校区
            array_push($dataList,$four);//上升至集团
        }

        $result['datalist'] = is_array($dataList) ? $dataList : array();

        if ($dataList) {
            $this->error = 0;
            $this->errortip = "获取成功";
            $this->result = $result;
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "暂无数据";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 职员信息
     * @param $paramArray
     * @return false|mixed
     */
    function stafferData($paramArray=array())
    {
        //任职信息
        //集团职务 school_id = 0 说明是集团职务 school_id > 0 说明是校园职务
        $info = array();
        $info = $this->DataControl->selectOne("
        SELECT sp.postrole_id,sp.organize_id,sp.school_id,sp.post_id,sp.postrole_id,sp.postpart_id,sp.postbe_isucsuser,sp.postbe_ucsuserlevel,cp.post_name
        FROM gmc_staffer_postbe sp
         LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id
        WHERE sp.postbe_id='{$this->re_postbe_id}' AND sp.company_id='{$this->company_id}' AND sp.school_id='{$this->school_id}'
        ");
        $stafferInfo = $this->DataControl->selectOne("
        SELECT s.staffer_cnname,s.staffer_mobile
        FROM smc_staffer s
        WHERE s.staffer_id='{$this->staffer_id}'
        ");
        $info['staffer_cnname'] = $stafferInfo['staffer_cnname'];
        $info['staffer_mobile'] = $stafferInfo['staffer_mobile'];
        if(empty($info['post_name']))
        {
            $info['post_name'] = '管理员';
        }

        return $info;

    }

    /**
     * 根据某职员ID获取职员信息 -zjc
     * @param array $paramArray staffer_id
     * @return false|mixed
     */
    function getStafferData($paramArray)
    {
        //任职信息
        //集团职务 school_id = 0 说明是集团职务 school_id > 0 说明是校园职务
        $info = array();
        //集团身份
        $info1 = $this->DataControl->selectOne("
        SELECT sp.postrole_id,sp.organize_id,sp.school_id,sp.post_id,sp.postrole_id,sp.postpart_id,sp.postbe_isucsuser,sp.postbe_ucsuserlevel,cp.post_name
        FROM gmc_staffer_postbe sp
         LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id
        WHERE sp.company_id='{$this->company_id}' AND sp.school_id='0'
        ");
        //学校身份
        $info2 = $this->DataControl->selectOne("
        SELECT sp.postrole_id,sp.organize_id,sp.school_id,sp.post_id,sp.postrole_id,sp.postpart_id,sp.postbe_isucsuser,sp.postbe_ucsuserlevel,cp.post_name
        FROM gmc_staffer_postbe sp
         LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id
        WHERE sp.company_id='{$this->company_id}' AND sp.school_id='{$this->school_id}'
        ");
        $stafferInfo = $this->DataControl->selectOne("
        SELECT s.staffer_cnname,s.staffer_mobile
        FROM smc_staffer s
        WHERE s.staffer_id='{$paramArray['staffer_id']}'
        ");
        if(!empty($info1))
        {
            $info = $info1;
        }else{
            $info = $info2;
        }
        $info['staffer_cnname'] = $stafferInfo['staffer_cnname'];
        $info['staffer_mobile'] = $stafferInfo['staffer_mobile'];
        if(empty($info['post_name']))
        {
            $info['post_name'] = '超级管理员';
        }
        if(!empty($this->company_id) && empty($info['postbe_ucsuserlevel']))
        {
            $info['postbe_ucsuserlevel'] = 4;
        }
        if($info['school_id']==0)//集团身份
        {
            $info['identity'] = 1;
            $info['identity_name'] = "集团";
        }elseif ($info['school_id']>0){//学校身份
            $info['identity'] = 2;
            $info['identity_name'] = "学校";
        }else{//超管
            $info['identity'] = 0;
            $info['identity_name'] = "集团";
        }
        if($info['postbe_ucsuserlevel']==1)
        {
            $info['postbe_ucsuserlevel_name'] = "校区客服";
        }elseif($info['postbe_ucsuserlevel']==2){
            $info['postbe_ucsuserlevel_name'] = "校长";
        }elseif($info['postbe_ucsuserlevel']==3){
            $info['postbe_ucsuserlevel_name'] = "集团客服";
        }elseif($info['postbe_ucsuserlevel']==4){
            $info['postbe_ucsuserlevel_name'] = "集团高管";
        }else{
            $info['postbe_ucsuserlevel_name'] = "集团高管";
        }

        return $info;

    }

    /**
     * 根据学校ID获取学校信息
     * @param $paramArray
     */
    function getSchoolData($paramArray)
    {
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $schoolData = $this->DataControl->selectOne("
SELECT s.company_id,s.companies_id,s.district_id,s.school_id,s.school_cnname,s.school_province,s.school_city,s.school_area,s.school_address,s.school_phone
FROM smc_school s
WHERE s.school_id='{$paramArray['school_id']}'
");
        } else {
            $schoolData = array();
        }

        return $schoolData;
    }

    /**
     * 根据集团ID获取集团信息
     * @param $paramArray
     * @return array|false|mixed
     */
    function getCompanyData($paramArray)
    {
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $companyData = $this->DataControl->selectOne("
        SELECT c.company_id,c.company_shortname,c.company_cnname,c.company_logo,c.company_phone,c.company_ucsservicecont,c.company_address
        FROM gmc_company c
        WHERE c.company_id='{$paramArray['company_id']}'
        ");
        } else {
            $companyData = array();
        }

        return $companyData;


    }

    /**
     * 根据工单ID获取可见的 客诉角色数组
     * @param $paramArray repairorder_id
     */
    function getRepairorderFeedbackthemeRolelist($paramArray)
    {
        //获取工单ID对应的主题权限信息
        $data = $this->DataControl->selectOne("
        SELECT ft.feedbacktheme_rolelist
        FROM ucs_repairorder r
        LEFT JOIN ucs_code_feedbacktheme ft ON r.feedbacktheme_id=ft.feedbacktheme_id
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}'
        ");
        $roleArray = explode(',', $data['feedbacktheme_rolelist']);
        return $roleArray;

    }

    /**
     * 获取可以转发的组织
     * @param track_type 流转类型 4:上升至集团 5:下发至校区 3:内部流转
     * @param [keyword] 关键词
     * @return bool
     */
    function getForwardOrganizations($paramArray)
    {
        if ($this->school_id == 0)//集团  仅内部流转3和向下2
        {
            if ($paramArray['track_type'] == 4) {
                $this->error = 1;
                $this->errortip = "当前已在集团,不能再向上流转了";
                $this->result = array();
                return false;
            }

            //获取集团其他有客诉权限的员工列表
            if ($paramArray['track_type'] == 5)//下发校区  获取有客诉职员的职工列表及学校列表
            {
                $datawhere = "s.company_id='{$this->company_id}' AND s.school_isclose=0";
                if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                    $datawhere .= " AND (s.school_shortname like '%{$paramArray['keyword']}%' )";
                }

                $dataList = $this->DataControl->selectClear("
                SELECT s.school_id,s.school_shortname
                FROM smc_school s
                WHERE {$datawhere}
                ");

            } else if ($paramArray['track_type'] == 3) {//内部流转  获取有客诉权限的集团职员列表
                $dataList = $this->DataControl->selectClear("
                SELECT c.company_id,c.company_shortname
                FROM gmc_company c
                WHERE c.company_id='{$this->company_id}'
                ");

            } else {
                $dataList = array();
            }

        } else {//学校  仅内部流转3和向上1
            if ($paramArray['track_type'] == 5) {
                $this->error = 1;
                $this->errortip = "当前已在校区,不能再向下流转了";
                $this->result = array();
                return false;
            }
            if ($paramArray['track_type'] == 4)//向上流转  获取集团
            {
                $dataList = $this->DataControl->selectClear("
                SELECT c.company_id,c.company_cnname
                FROM gmc_staffer_postbe sp
                LEFT JOIN gmc_company c ON sp.company_id=c.company_id
                WHERE sp.company_id='{$this->company_id}' AND sp.school_id=0
                GROUP BY c.company_id
                ");


            } else if ($paramArray['track_type'] == 3) {//内部流转  学校信息
                $dataList = $this->DataControl->selectClear("
                SELECT s.school_id,s.school_shortname
                FROM gmc_staffer_postbe sp
                LEFT JOIN smc_school s ON sp.school_id=s.school_id
                WHERE sp.company_id='{$this->company_id}' AND sp.school_id='{$this->school_id}'
                GROUP BY s.school_id
                ");

            } else {
                $dataList = array();
            }

        }

        $result['datalist'] = is_array($dataList) ? $dataList : array();
        if ($dataList) {
            $this->error = 0;
            $this->errortip = "组织列表获取成功!";
            $this->result = $result;
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "没有数据:(";
            $this->result = array();
            return false;
        }

    }

    /**
     * 有客诉权限的职员列表
     * @param $paramArray type 类型 1:集团 2:学校
     * @param $paramArray forward_company_id 集团ID
     * @param $paramArray forward_school_id 学校ID
     * @return bool
     */
    function getUscStafferList($paramArray)
    {
        if($paramArray['type']==1)//集团客诉职员list
        {
            $dataList = $this->DataControl->selectClear("
            SELECT s.staffer_id,s.staffer_cnname,sp.postbe_ucsuserlevel
            FROM gmc_staffer_postbe sp
            LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
            WHERE sp.company_id='{$paramArray['forward_company_id']}' AND sp.school_id=0 AND sp.postbe_isucsuser=1 AND sp.postbe_ucsuserlevel IN(3,4) 
            GROUP BY s.staffer_id
            ");

        }elseif ($paramArray['type']==2){//学校
            $dataList = $this->DataControl->selectClear("
            SELECT s.staffer_id,s.staffer_cnname,sp.postbe_ucsuserlevel
            FROM gmc_staffer_postbe sp
            LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
            WHERE sp.company_id='{$paramArray['forward_company_id']}' AND sp.school_id='{$paramArray['forward_school_id']}' AND sp.postbe_isucsuser=1
            GROUP BY s.staffer_id
            ");

        }else{
            $this->error = 0;
            $this->errortip = "参数缺失:类型";
            $this->result = array();
            return false;
        }

        $result['datalist'] = is_array($dataList) ? $dataList : array();

        if ($dataList) {
            $this->error = 0;
            $this->errortip = "职员列表获取成功!";
            $this->result = $result;
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "没有数据:(";
            $this->result = array();
            return false;
        }

    }

    /**
     * 敏感工单可处理人员列表
     * @param $paramArray
     * @return bool
     */
    function getIssensitiveRepairorderStafferList($paramArray)
    {
        $list = $this->DataControl->selectClear("
        SELECT s.staffer_id,s.staffer_cnname,sp.postbe_ucsuserlevel
        FROM gmc_staffer_postbe sp
        LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
        WHERE sp.company_id='{$this->company_id}' AND sp.postbe_isucsuser=1 AND sp.postbe_ucsuserlevel IN('2','4')
        GROUP BY s.staffer_id
        ");
        if(!empty($list))
        {
            $dataList = array();
            foreach ($list as $key =>$value)
            {
                array_push($dataList,[
                    'key'=>$value['staffer_id'],
                    'label'=>$value['staffer_cnname'],
                    'disabled'=>false,
                    'ucsuserlevel'=>$value['postbe_ucsuserlevel']
                ]);
            }

        }
        $result['datalist'] = is_array($dataList) ? $dataList : array();

        if ($dataList) {
            $this->error = 0;
            $this->errortip = "敏感工单可处理职员列表获取成功!";
            $this->result = $result;
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "没有数据:(";
            $this->result = array();
            return false;
        }

    }


    /**
     * 获取组织对应的客诉成员[暂时不用]
     * @param $paramArray
     * @return bool
     */
    function getForwardMembers($paramArray)
    {
        if($this->school_id==0)//集团  仅内部流转和向下
        {
            if($paramArray['track_type']==1)
            {
                $this->error = 1;
                $this->errortip = "当前已在集团,不能再向上流转了";
                $this->result = array();
                return false;
            }
            //获取集团其他有客诉权限的员工列表
            if($paramArray['track_type']==2)//下发校区  获取有客诉职员的职工列表及学校列表
            {
                $dataArray = $this->DataControl->selectClear("
                SELECT s.staffer_id,s.staffer_cnname,sc.school_id,sc.school_shortname
                FROM gmc_staffer_postbe sp
                LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
                LEFT JOIN smc_school sc ON sp.school_id=sc.school_id
                WHERE sp.company_id='{$this->company_id}' AND sp.school_id>0 AND sp.postbe_isucsuser=1 AND sp.postbe_ucsuserlevel>2
                ");
                foreach ($dataArray as $key =>$value)
                {
                    $list[$value['school_id']]['school_id'] = $value['school_id'];
                    $list[$value['school_id']]['school_shortname'] = $value['school_shortname'];
                    $list[$value['school_id']]['info'][] = $value;
                }
                $dataList = array();
                foreach ($list as $key =>$value)
                {
                    array_push($dataList,$value);
                }

            }else if ($paramArray['track_type']==3){//内部流转  获取有客诉权限的集团职员列表
                $dataArray = $this->DataControl->selectClear("
                SELECT s.staffer_id,s.staffer_cnname,sp.school_id,c.company_id,c.company_cnname
                FROM gmc_staffer_postbe sp
                LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
                LEFT JOIN gmc_company c ON sp.company_id=c.company_id
                WHERE sp.company_id='{$this->company_id}' AND sp.school_id=0 AND sp.postbe_isucsuser=1 AND sp.postbe_ucsuserlevel>2
                ");
                foreach ($dataArray as $key =>$value)
                {
                    $list[$value['company_id']]['company_id'] = $value['company_id'];
                    $list[$value['company_id']]['company_cnname'] = $value['company_cnname'];
                    $list[$value['company_id']]['info'][] = $value;
                }
                $dataList = array();
                foreach ($list as $key =>$value)
                {
                    array_push($dataList,$value);
                }

            }else{
                $dataList = array();
            }

        }else{//学校  仅内部流转和向上
            if($paramArray['track_type']==2)
            {
                $this->error = 1;
                $this->errortip = "当前已在校区,不能再向下流转了";
                $this->result = array();
                return false;
            }
            if($paramArray['track_type']==1)//向上流转  获取有客诉职员的职工列表及学校列表
            {
                $dataArray = $this->DataControl->selectClear("
                SELECT s.staffer_id,s.staffer_cnname,sp.school_id,c.company_id,c.company_cnname
                FROM gmc_staffer_postbe sp
                LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
                LEFT JOIN gmc_company c ON sp.company_id=c.company_id
                WHERE sp.company_id='{$this->company_id}' AND sp.school_id=0 AND sp.postbe_isucsuser=1 AND sp.postbe_ucsuserlevel>0
                ");
                foreach ($dataArray as $key =>$value)
                {
                    $list[$value['company_id']]['company_id'] = $value['company_id'];
                    $list[$value['company_id']]['company_cnname'] = $value['company_cnname'];
                    $list[$value['company_id']]['info'][] = $value;
                }
                $dataList = array();
                foreach ($list as $key =>$value)
                {
                    array_push($dataList,$value);
                }

            }else if($paramArray['track_type']==3){//内部流转  获取有客诉权限的学校职员列表
                $dataArray = $this->DataControl->selectClear("
                SELECT s.staffer_id,s.staffer_cnname,sp.school_id,c.company_id,c.company_cnname
                FROM gmc_staffer_postbe sp
                LEFT JOIN smc_staffer s ON sp.staffer_id=s.staffer_id
                LEFT JOIN gmc_company c ON sp.company_id=c.company_id
                WHERE sp.company_id='{$this->company_id}' AND sp.school_id>0 AND sp.postbe_isucsuser=1 AND sp.postbe_ucsuserlevel>0
                ");
                foreach ($dataArray as $key =>$value)
                {
                    $list[$value['company_id']]['company_id'] = $value['company_id'];
                    $list[$value['company_id']]['company_cnname'] = $value['company_cnname'];
                    $list[$value['company_id']]['info'][] = $value;
                }
                $dataList = array();
                foreach ($list as $key =>$value)
                {
                    array_push($dataList,$value);
                }
            }else{
                $dataList = array();
            }

        }

        $result['datalist'] = is_array($dataList)?$dataList:array();
        if($dataList){
            $this->error = 0;
            $this->errortip = "列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "没有数据!";
            $this->result = array();
            return false;
        }

    }

    /**
     * 首页统计数据 -zjc
     * @param $paramArray [startdate]
     * @param $paramArray [enddate]
     * @return bool
     */
    function indexDataStatistics($paramArray)
    {
        $dataWhere = " ";
        if(!empty($paramArray['startdate']))
        {
            $starttime = strtotime($paramArray['startdate']);
            $dataWhere.=" AND r.repairorder_createtime>'{$starttime}' ";
        }
        if(!empty($paramArray['enddate']))
        {
            $endtime = strtotime($paramArray['enddate'])+86400-1;
            $dataWhere.=" AND r.repairorder_createtime<'{$endtime}' ";
        }
        //星级
        $repairorderLevel = $this->DataControl->selectOne("
SELECT
COUNT(case when r.repairorder_level=1 then 1 end) AS num1,
COUNT(case when r.repairorder_level=2 then 1 end) AS num2,
COUNT(case when r.repairorder_level=3 then 1 end) AS num3,
COUNT(case when r.repairorder_level=4 then 1 end) AS num4,
COUNT(case when r.repairorder_level=5 then 1 end) AS num5
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' {$dataWhere}
");

        //处理时长
        $repairorderTime = $this->DataControl->selectOne("
SELECT
COUNT(case when r.repairorder_updatetime-r.repairorder_createtime<86400 then 1 end) AS num1,
COUNT(case when 86400<r.repairorder_updatetime-r.repairorder_createtime<604800 then 1 end) AS num2,
COUNT(case when r.repairorder_updatetime-r.repairorder_createtime>604800 then 1 end) AS num3
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' AND r.repairorder_updatetime!=0 AND r.repairorder_status!='-2' {$dataWhere}
");

        //工单状态
        $repairorderStatus = $this->DataControl->selectOne("
SELECT
COUNT(case when r.repairorder_status=0 then 1 end) AS num0,
COUNT(case when r.repairorder_status=1 then 1 end) AS num1,
COUNT(case when r.repairorder_status=2 then 1 end) AS num2,
COUNT(case when r.repairorder_status=3 then 1 end) AS num3
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' AND r.repairorder_status!='-2' {$dataWhere}
");

        //类型
        $repairorderCatgory = $this->DataControl->selectOne("
SELECT
COUNT(case when r.feedbacktype_catgory=1 then 1 end) AS num1,
COUNT(case when r.feedbacktype_catgory=2 then 1 end) AS num2,
COUNT(case when r.feedbacktype_catgory=3 then 1 end) AS num3,
COUNT(case when r.feedbacktype_catgory=4 then 1 end) AS num4
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' AND r.repairorder_status!='-2' {$dataWhere}
");

        $dataList=[
            "data1"=>[
                "allList"=>[
                    [
                        "value"=>$repairorderLevel['num1'],
                        "name"=>"1星"
                    ],
                    [
                        "value"=>$repairorderLevel['num2'],
                        "name"=>"2星"
                    ],
                    [
                        "value"=>$repairorderLevel['num3'],
                        "name"=>"3星"
                    ],
                    [
                        "value"=>$repairorderLevel['num4'],
                        "name"=>"4星"
                    ],
                    [
                        "value"=>$repairorderLevel['num5'],
                        "name"=>"5星"
                    ],
                ],
                "legendData"=>[
                    [
                        "name"=>"1星",
                        "num"=>$repairorderLevel['num1'],
                        "color"=>"#68D288"
                    ],
                    [
                        "name"=>"2星",
                        "num"=>$repairorderLevel['num2'],
                        "color"=>"#58AEFF"
                    ],
                    [
                        "name"=>"3星",
                        "num"=>$repairorderLevel['num3'],
                        "color"=>"#8068FF"
                    ],
                    [
                        "name"=>"4星",
                        "num"=>$repairorderLevel['num4'],
                        "color"=>"#F9C626"
                    ],
                    [
                        "name"=>"5星",
                        "num"=>$repairorderLevel['num5'],
                        "color"=>"#F2637B"
                    ],

                ]

            ],
            "data2"=>[
                "allList"=>[
                    [
                        "value"=>$repairorderTime['num1'],
                        "name"=>"1天以内"
                    ],
                    [
                        "value"=>$repairorderTime['num2'],
                        "name"=>"1-7天"
                    ],
                    [
                        "value"=>$repairorderTime['num3'],
                        "name"=>"7天以上"
                    ]
                ],
                "legendData"=>[
                    [
                        "name"=>"1天以内",
                        "num"=>$repairorderTime['num1'],
                        "color"=>"#58AEFF"
                    ],
                    [
                        "name"=>"1-7天",
                        "num"=>$repairorderTime['num2'],
                        "color"=>"#8068FF"
                    ],
                    [
                        "name"=>"7天以上",
                        "num"=>$repairorderTime['num3'],
                        "color"=>"#F9C626"
                    ]

                ]

            ],
            "data3"=>[
                "allList"=>[
                    [
                        "value"=>$repairorderStatus['num0'],
                        "name"=>"待受理"
                    ],
                    [
                        "value"=>$repairorderStatus['num1'],
                        "name"=>"处理中"
                    ],
                    [
                        "value"=>$repairorderStatus['num2'],
                        "name"=>"已处理"
                    ],
                    [
                        "value"=>$repairorderStatus['num3'],
                        "name"=>"已完结"
                    ]
                ],
                "legendData"=>[
                    [
                        "name"=>"待受理",
                        "num"=>$repairorderStatus['num0'],
                        "color"=>"#58AEFF"
                    ],
                    [
                        "name"=>"处理中",
                        "num"=>$repairorderStatus['num1'],
                        "color"=>"#8068FF"
                    ],
                    [
                        "name"=>"已处理",
                        "num"=>$repairorderStatus['num2'],
                        "color"=>"#F9C626"
                    ],
                    [
                        "name"=>"已完结",
                        "num"=>$repairorderStatus['num3'],
                        "color"=>"#F2637B"
                    ],

                ]
            ],
            "data4"=>[
                "allList"=>[
                    [
                        "value"=>$repairorderCatgory['num3'],
                        "name"=>"表扬"
                    ],
                    [
                        "value"=>$repairorderCatgory['num2'],
                        "name"=>"建议"
                    ],
                    [
                        "value"=>$repairorderCatgory['num1'],
                        "name"=>"投诉"
                    ],
                    [
                        "value"=>$repairorderCatgory['num4'],
                        "name"=>"其他"
                    ]
                ],
                "legendData"=>[
                    [
                        "name"=>"表扬",
                        "num"=>$repairorderCatgory['num3'],
                        "color"=>"#58AEFF"
                    ],
                    [
                        "name"=>"建议",
                        "num"=>$repairorderCatgory['num2'],
                        "color"=>"#8068FF"
                    ],
                    [
                        "name"=>"投诉",
                        "num"=>$repairorderCatgory['num1'],
                        "color"=>"#F9C626"
                    ],
                    [
                        "name"=>"其他",
                        "num"=>$repairorderCatgory['num4'],
                        "color"=>"#F2637B"
                    ]

                ]
            ]
        ];
        //待处理
        $accepted_repairorder = $this->DataControl->selectOne("
SELECT COUNT(r.repairorder_id) AS num
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' AND r.repairorder_status=0
");

        //处理中
        $processing_repairorder= $this->DataControl->selectOne("
SELECT COUNT(r.repairorder_id) AS num
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' AND r.repairorder_status>0 AND r.repairorder_status<3
");

        //已结案
        $completed_repairorder= $this->DataControl->selectOne("
SELECT COUNT(r.repairorder_id) AS num
FROM ucs_repairorder r
WHERE r.company_id='{$this->company_id}' AND r.school_id='{$this->school_id}' AND r.repairorder_status=3
");

        $result['datalist'] = is_array($dataList)?$dataList:array();
        $result['accepted_repairorder_num'] = empty($accepted_repairorder['num'])?"0":$accepted_repairorder['num'];
        $result['processing_repairorder_num'] = empty($processing_repairorder['num'])?"0":$processing_repairorder['num'];
        $result['completed_repairorder_num'] = empty($completed_repairorder['num'])?"0":$completed_repairorder['num'];
        if($dataList){
            $this->error = 0;
            $this->errortip = "首页统计列表数据获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "没有数据!";
            $this->result = array();
            return false;
        }

    }

    /**
     * 工单权限校验
     * @param $paramArray repairorder_id 工单ID
     * @param $paramArray staffer_id 职员ID
     */
    function RepairorderPermissionsVerify($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}'
        ");
        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3')//结案 删除
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

    }

    /**
     * 结案模板
     */
    function settleRepairorderTemplateList($request)
    {
        $dataList=[
            [
                'content'=>"本人已处理完毕,结案"
            ],
            [
                'content'=>"已协调多部门处理完毕"
            ],
            [
                'content'=>"模板3"
            ],
            [
                'content'=>"模板4"
            ]
        ];
        $result['datalist'] = is_array($dataList) ? $dataList : array();

        if ($dataList) {
            $this->error = 0;
            $this->errortip = "获取成功";
            $this->result = $result;
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "暂无主题";
            $this->result = $result;
            return false;
        }

        return $dataList;
    }

    //$this->DataControl->begintransaction();
    //$this->DataControl->commit();
    //$this->DataControl->rollback();

    /**
     * 添加日程安排
     * @param $paramArray repairorder_id
     * @param $paramArray schedule_remindtime 提醒时间
     * @param $paramArray schedule_note 备注
     */
    function addRepairorderSchedule($paramArray)
    {
        $this->DataControl->insertData("ucs_schedule",[
            'company_id'=>$this->company_id,
            'school_id'=>$this->school_id,
            'staffer_id'=>$this->staffer_id,
            'repairorder_id'=>$paramArray['repairorder_id'],
            'schedule_remindtime'=>$paramArray['schedule_remindtime'],
            'schedule_note'=>"设置为今日跟进,上次跟进时间为".date('Y-m-d H:i:s',time()).",请尽快处理!",
            'schedule_createtime'=>time()
        ]);
    }

    /**
     * 绑定记录文件
     * @param $paramArray track_files_json 文件json
     * @param $paramArray track_id 跟踪记录ID
     */
    function addTrackFiles($paramArray)
    {
        $track_files_json = str_replace("\\","",$paramArray['track_files_json']);
        $track_files_array = json_decode($track_files_json,true);
        if(is_array($track_files_array))
        {
            foreach ($track_files_array as $key => $value)
            {
                $this->DataControl->updateData("ucs_upload_files","files_id='{$value['files_id']}' ",[
                    'track_id'=>$paramArray['track_id'],
                    'files_name'=>$value['files_name']
                ]);
            }
        }

    }

    /**
     * 获取工单当前跟进人
     * @param $paramArray repairorder_id
     * @return false|mixed
     */
    function getRepairorderNowTrackStafferId($paramArray)
    {
        $data = $this->DataControl->selectOne("
        SELECT rt.staffer_id
        FROM ucs_repairorder_track rt 
        WHERE rt.repairorder_id='{$paramArray['repairorder_id']}' AND rt.track_type IN(1,2,3,4) 
        ORDER BY rt.track_createtime DESC 
        LIMIT 1
        ");
        return $data;
    }

    /**
     * 获取工单当前流转状态
     * @param $paramArray
     * @return false|mixed
     */
    function getRepairorderNowTrackType($paramArray)
    {
        $data = $this->DataControl->selectOne("
        SELECT rt.track_type
        FROM ucs_repairorder_track rt 
        WHERE rt.repairorder_id='{$paramArray['repairorder_id']}' AND rt.track_type IN(1,2,3,4) 
        ORDER BY rt.track_createtime DESC 
        LIMIT 1
        ");
        return $data;
    }


}