<?php
/**
 * 公共
 */

namespace Model\Easx;
class UcsPublicModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    /**
     * 省份列表 -zjc
     * @param $paramArray
     * @return array|false
     */
    function getProvinceList($paramArray)
    {
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '1' and r.region_level='2' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $dataList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");

        $result['datalist'] = is_array($dataList)?$dataList:array();
        if($dataList){
            $this->error = 0;
            $this->errortip = "省份列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "没有数据!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 城市列表 -zjc
     * @param $paramArray region_id
     * @return array|false
     */
    function getCityList($paramArray)
    {

        if(!isset($paramArray['region_id'])||$paramArray['region_id']=='')
        {
            $this->error = 1;
            $this->errortip = "省份ID不能为空!";
            $this->result = array();
            return false;
        }
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '{$paramArray['region_id']}' and r.region_level='3' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $dataList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");
        $result['datalist'] = is_array($dataList)?$dataList:array();
        if($dataList){
            $this->error = 0;
            $this->errortip = "城市列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "没有数据!";
            $this->result = $result;
            return false;
        }
    }

    /**
     * 地区列表 -zjc
     * @param $paramArray region_id
     * @return array|false
     */
    function getAreaList($paramArray)
    {
        if(!isset($paramArray['region_id'])||$paramArray['region_id']=='')
        {
            $this->error = 1;
            $this->errortip = "市级ID不能为空!";
            $this->result = array();
            return false;
        }
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '{$paramArray['region_id']}' and r.region_level='4' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $dataList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");
        $result['datalist'] = is_array($dataList)?$dataList:array();
        if($dataList){
            $this->error = 0;
            $this->errortip = "区县列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 0;
            $this->errortip = "没有数据!";
            $this->result = $result;
            return false;
        }
    }

    /**
     * 学校列表
     * @param $paramArray
     * @return array
     */
    function getSchoolList($paramArray){
        if(empty($paramArray['company_id'])){
            $this->error = 1;
            $this->errortip = "集团ID必须传入!";
            $this->result = array();
            return false;
        }
        if(!isset($paramArray['school_id']) && empty($paramArray['school_province'])){
            $this->error = 1;
            $this->errortip = "请选择地区!";
            $this->result = array();
            return false;
        }
        $datawhere =" s.company_id = '{$paramArray['company_id']}'";
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%' )";//or c.company_cnname like '%{$paramArray['keyword']}%'
        }
        //区域
        if(isset($paramArray['district_id']) && $paramArray['district_id'] != ''){
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //省份
        if(isset($paramArray['school_province']) && $paramArray['school_province'] != ''){
            $datawhere .= " and s.school_province = '{$paramArray['school_province']}'";
        }
        //城市
        if(isset($paramArray['school_city']) && $paramArray['school_city'] != ''){
            $datawhere .= " and s.school_city = '{$paramArray['school_city']}'";
        }
        //城市
        if(isset($paramArray['school_cnname_initial']) && $paramArray['school_cnname_initial'] != ''){
            $datawhere .= " and s.school_cnname_initial = '{$paramArray['school_cnname_initial']}'";
        }
        //组织
        if(isset($paramArray['organize_id']) && $paramArray['organize_id'] != ''){
            $datawhere .= " and s.school_id in ( SELECT o.school_id FROM gmc_company_organizeschool as o where o.organize_id = '{$paramArray['organize_id']}')";
        }

        $schoolstring = "";
        if(isset($paramArray['staffer_id']) && $paramArray['staffer_id'] !== ''){
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,account_class,company_id", "staffer_id = '{$paramArray['staffer_id']}'");
            if($stafferOne['account_class'] == '0'){
                if(isset($paramArray['re_postbe_id']) && $paramArray['re_postbe_id'] !== ''){
                    $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "school_id,postrole_id,organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
                    if($postbeOne['school_id'] !== '0'){
                        $daonewhere = "p.staffer_id = '{$stafferOne['staffer_id']}' and p.postbe_status = '1' AND p.school_id <> '0'";
                        if(isset($paramArray['marketer_id']) && $paramArray['marketer_id'] !== ''){
                            $daonewhere .= " and p.postbe_iscrmuser = '1'";
                        }
                        $schoollist = $this->DataControl->selectClear("SELECT p.school_id FROM gmc_staffer_postbe AS p WHERE {$daonewhere}");
                    }else{
                        $daonewhere = "p.organize_id = b.organize_id AND b.staffer_id = '{$stafferOne['staffer_id']}' and b.postbe_status = '1' AND b.school_id = '0' and  b.organize_id = '{$postbeOne['organize_id']}'";
                        if(isset($paramArray['marketer_id']) && $paramArray['marketer_id'] !== ''){
                            $postroleOne = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscompanyuser", "postrole_id = '{$paramArray['postrole_id']}'");
                            if($postroleOne['postpart_iscompanyuser'] == '0'){
                                $daonewhere .= " and p.school_id = '0'";
                            }
                        }
                        $schoollist = $this->DataControl->selectClear("SELECT p.school_id FROM gmc_company_organizeschool AS p, gmc_staffer_postbe AS b WHERE {$daonewhere}");
                    }
                }else{
                    $schoollist = $this->DataControl->selectClear("SELECT p.school_id FROM gmc_staffer_postbe AS p WHERE p.staffer_id = '{$stafferOne['staffer_id']}' and p.postbe_status = '1' AND p.school_id <> '0' UNION ALL
SELECT p.school_id FROM gmc_company_organizeschool AS p, gmc_staffer_postbe AS b WHERE p.organize_id = b.organize_id AND b.staffer_id = '{$stafferOne['staffer_id']}' and b.postbe_status = '1' AND b.school_id = '0'");
                }
                if($schoollist){
                    $schoolArray = array();
                    foreach($schoollist as $schoollistVar){
                        $schoolArray[] = $schoollistVar['school_id'];
                    }
                    $schoolstring = implode(",",$schoolArray);
                    $datawhere .= " and s.school_id in ($schoolstring)  ";
                }else{
                    $datawhere .= " and s.school_id in (0)  ";
                }
            }
        }

        //首字母筛选
        $sqlfields = " s.school_cnname_initial ";
        $sql="SELECT  {$sqlfields}
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                WHERE {$datawhere}
                GROUP BY s.school_cnname_initial";
        $initial = $this->DataControl->selectClear($sql);
        $initialarray = array();
        if($initial) {
            foreach ($initial as $initialvar) {
                $initialarray[] = $initialvar['school_cnname_initial'];
            }
        }

        if(isset($paramArray['re_postbe_id'])){
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            if($postbeOne['postrole_id'] == '0'){
                $sqlfields = " s.school_id,s.company_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,c.company_cnname,s.school_address,s.school_phone  ";
                $sql="SELECT  {$sqlfields},p.postbe_id
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                left join gmc_staffer_postbe as p on p.school_id = s.school_id
                WHERE {$datawhere} and p.staffer_id = '{$paramArray['staffer_id']}' and school_isclose = 0 GROUP BY s.school_id ORDER BY s.school_sort DESC";
                $dataList = $this->DataControl->selectClear($sql);
            }else{
                $sqlfields = " s.school_id,s.company_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,c.company_cnname,s.school_address,s.school_phone  ";
                $sql="SELECT  {$sqlfields},(select postbe_id from gmc_staffer_postbe WHERE postbe_id = '{$paramArray['re_postbe_id']}') as postbe_id
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                WHERE {$datawhere} and school_isclose = 0 GROUP BY s.school_id ORDER BY s.school_sort DESC";
                $dataList = $this->DataControl->selectClear($sql);
            }
        }else{
            $sqlfields = " s.school_id,s.company_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,c.company_cnname,s.school_address,s.school_phone  ";
            $sql="SELECT  {$sqlfields}
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                WHERE {$datawhere} and school_isclose = 0 GROUP BY s.school_id ORDER BY s.school_sort DESC";
            $dataList = $this->DataControl->selectClear($sql);
        }

        //结构变更
        $dataarray = array();
//        if($dataList) {
//            foreach ($dataList as $key => $value) {
//                $dataarray[$value['company_id']]['company_id'] = $value['company_id'];
//                $dataarray[$value['company_id']]['company_cnname'] = $value['company_cnname'];
//                $dataarray[$value['company_id']]['list'][] = $value;
//            }
//        }

//        $data_list = array();
//        foreach ($dataarray as $key => $value)
//        {
//            array_push($data_list,$value);
//        }


//        if ($dataList['datalist'][$paramArray['company_id']])
//        {
//            foreach ($dataList['datalist'][$paramArray['company_id']] as &$val) {
//                if($paramArray['re_postbe_id'] == '0'){
//                    $status = '1';
//                }else{
//                    $a = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id,postrole_id,postbe_iscrmuser","postbe_id = '{$val['postbe_id']}'");
//                    if($a['school_id'] == '0'){
//                        $b = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_iscrmuser","postrole_id = '{$a['postrole_id']}'");
//                        if($b['postpart_iscrmuser'] == '1'){
//                            $status = '1';
//                        }else{
//                            $status = '0';
//                        }
//                    }else{
//                        if($a['postbe_iscrmuser'] == '1'){
//                            $status = '1';
//                        }else{
//                            $status = '0';
//                        }
//                    }
//                }
//                $val['status'] = $status;
//            }
//        }

        $result= array();
        $result['datalist'] = $dataList;
        $result['initial'] = $initialarray;
        $result['allid'] = $schoolstring;

        if($result){
            $this->error = 0;
            $this->errortip = "学校列表获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "fail";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 根据ID获取集团学校相关信息
     * @param $paramArray
     * @return bool
     */
    function getCompanySchoolInfo($paramArray)
    {

        if(empty($paramArray['company_id']))
        {
            $this->error = 1;
            $this->errortip = "集团ID必须传入!";
            $this->result = array();
            return false;
        }

        $datawhere=" c.company_id='{$paramArray['company_id']}' ";
        $companyData = $this->DataControl->selectOne("
        SELECT c.company_id,c.company_shortname,c.company_cnname,c.company_logo,c.company_phone,c.company_ucsservicecont 
        FROM gmc_company c 
        WHERE {$datawhere}
        ");

        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
            $schoolData = $this->DataControl->selectOne("
SELECT s.school_id,s.school_cnname,s.school_shortname  
FROM smc_school s 
WHERE s.school_id='{$paramArray['school_id']}' 
");
        }
        $companyData['school_data'] = is_array($schoolData)?$schoolData:array();

        $result=array();
        $result['datalist'] = is_array($companyData)?$companyData:array();

        if($companyData){
            $this->error = 0;
            $this->errortip = "信息获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "未查询到集团相关信息";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 查询是否有未结案的工单
     * @return bool
     */
    function getRepairorderRepeated($paramArray)
    {
        if(empty($paramArray['contactmobile']))
        {
            $this->error = 1;
            $this->errortip = "手机号不能为空!";
            $this->result = array();
            return false;
        }
        $two_hours_ago = time()-60*60*2;//2小时
        $two_hours_repairorder = $this->DataControl->selectOne("SELECT repairorder_pid
FROM ucs_repairorder 
WHERE repairorder_contactmobile='{$paramArray['contactmobile']}' AND repairorder_createtime>'{$two_hours_ago}' 
LIMIT 1
");
        if(!empty($two_hours_repairorder))
        {
            $this->error = 0;
            $this->errortip = "两小时内已提交过工单了!";
            $this->result = array();
            return true;
        }else{
            $whereData =" r.repairorder_contactmobile='{$paramArray['contactmobile']}' AND r.repairorder_status IN (0,1,2) ";
            $dataList = $this->DataControl->selectOne(" 
        SELECT r.repairorder_pid 
        FROM ucs_repairorder r 
        WHERE {$whereData} 
        ");
            if(empty($dataList))
            {
                $this->error = 0;
                $this->errortip = "没有未结案工单!";
                $this->result = array();
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "有未结案的工单!";
                $this->result = array();
                return true;
            }
        }



    }



}