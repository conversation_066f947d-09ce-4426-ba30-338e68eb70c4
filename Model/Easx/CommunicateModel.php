<?php

namespace Model\Easx;

Class CommunicateModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();
    public $publicarray = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 学员沟通管理
     */
    function getTotal($paramArray){
        $datawhere = "t.staffer_id='{$this->stafferOne['staffer_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%' or f.family_mobile like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and sc.track_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and sc.track_day <= '{$paramArray['end_time']}'";
        }
        if(isset($paramArray['class_id']) && $paramArray['class_id'] !==''){
            $datawhere .= " and ss.class_id = '{$paramArray['class_id']}'";
        }
        $having = "1=1";
        if($paramArray['code'] == 1){
            $having .= " and track_followutime <> '' ";
        }
        $orderby = '';
        if($paramArray['orderby'] == 2){
            $orderby .= " ,sc.track_day DESC";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT s.student_id,sc.track_day,s.student_cnname,s.student_enname,s.student_img,s.student_sex,f.family_mobile,ss.class_id,c.class_cnname,
                (SELECT sc.track_followutime FROM smc_student_track AS sc WHERE sc.school_id = c.school_id AND sc.student_id = s.student_id ORDER BY sc.track_createtime DESC LIMIT 1) AS track_followutime,
                (SELECT COUNT(ct.track_id) FROM smc_student_track as ct WHERE ct.school_id = c.school_id AND ct.student_id = sc.student_id) as track_num,
                (SELECT COUNT(ct.track_id) FROM smc_student_track as ct WHERE ct.school_id = c.school_id AND ct.student_id = sc.student_id AND ct.track_from = '0') as invalid_num,
                (SELECT COUNT(ct.track_id) FROM smc_student_track as ct WHERE ct.school_id = c.school_id AND ct.student_id = sc.student_id AND ct.track_from = '1') as valid_num
                FROM smc_class_hour_teaching as t
                LEFT JOIN smc_class as c ON c.class_id = t.class_id
                LEFT JOIN smc_student_study as ss ON ss.class_id = c.class_id
                LEFT JOIN smc_student as s ON s.student_id = ss.student_id
                LEFT JOIN smc_student_family as f ON f.student_id = s.student_id
                LEFT JOIN smc_student_track AS sc ON sc.school_id = c.school_id AND sc.student_id = s.student_id
                WHERE {$datawhere} AND ss.study_isreading = '1' AND c.class_status = '1' AND c.school_id = '{$paramArray['school_id']}' GROUP BY s.student_id HAVING {$having} ORDER BY sc.track_followutime DESC {$orderby} LIMIT {$pagestart},{$num}";
        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$v){
                $v['initials'] = $this->Initials($v['student_cnname']);
                if(!$v['track_followutime']){
                    $v['track_followutime'] = '待定';
                }
            }
            if($paramArray['orderby'] == 1){
                $sort_arr = [];
                foreach ($datalist as $val) {
                    $sort_arr[] = $val['initials'];
                }
                array_multisort($sort_arr, SORT_ASC, $datalist);
            }
        }else{
            $datalist = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM
                                                          (SELECT s.student_id,(SELECT sc.track_followutime FROM smc_student_track AS sc WHERE sc.school_id = c.school_id AND sc.student_id = s.student_id ORDER BY sc.track_createtime DESC LIMIT 1) AS track_followutime
                                                          FROM smc_class_hour_teaching as t
                                                          LEFT JOIN smc_class as c ON c.class_id=t.class_id
                                                          LEFT JOIN smc_student_study as ss ON ss.class_id = c.class_id
                                                          LEFT JOIN smc_student as s ON s.student_id = ss.student_id
                                                          LEFT JOIN smc_student_family as f ON f.student_id=s.student_id
                                                          LEFT JOIN smc_student_track AS sc ON sc.school_id = c.school_id AND sc.student_id = s.student_id
                                                          WHERE {$datawhere} AND ss.study_isreading='1' AND c.class_status='1'  AND c.school_id = '{$paramArray['school_id']}' GROUP BY s.student_id HAVING {$having}) as q");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;
        $await_num = $this->DataControl->selectOne("SELECT COUNT(q.student_id) as num FROM
                                                        (SELECT s.student_id,(SELECT sc.track_followutime FROM smc_student_track AS sc WHERE sc.school_id = c.school_id AND sc.student_id = s.student_id ORDER BY sc.track_createtime DESC LIMIT 1) AS track_followutime
                                                        FROM smc_class_hour_teaching as t
                                                        LEFT JOIN smc_class as c ON c.class_id=t.class_id
                                                        LEFT JOIN smc_student_study as ss ON ss.class_id = c.class_id
                                                        LEFT JOIN smc_student as s ON s.student_id = ss.student_id
                                                        LEFT JOIN smc_student_family as f ON f.student_id=s.student_id
                                                        LEFT JOIN smc_student_track AS sc ON sc.student_id = s.student_id
                                                        WHERE {$datawhere} and t.staffer_id='{$this->stafferOne['staffer_id']}' AND ss.study_isreading='1' AND c.class_status='1' AND c.school_id = '{$paramArray['school_id']}' and t.class_id = '{$paramArray['class_id']}' GROUP BY s.student_id HAVING track_followutime <> '') as q");
        $data['await_num'] = $await_num['num'];

        return $data;
    }

    //获取汉子首字母
    function Initials($str){
        $fchar = ord($str{0});
        if ($fchar >= ord('A') && $fchar <= ord('z')) {
            return strtoupper($str{0});
        }
        $s1 = iconv('UTF-8','gb2312',$str);
        $s2 = iconv('gb2312','UTF-8',$s1);
        $s = $s2 == $str ? $s1 : $str;
        $asc = ord($s{0}) * 256 + ord($s{1}) - 65536;

        if($asc >= -20319 && $asc <= -20284) return 'A';
        if($asc >= -20283 && $asc <= -19776) return 'B';
        if($asc >= -19775 && $asc <= -19219) return 'C';
        if($asc >= -19218 && $asc <= -18711) return 'D';
        if($asc >= -18710 && $asc <= -18527) return 'E';
        if($asc >= -18526 && $asc <= -18240) return 'F';
        if($asc >= -18239 && $asc <= -17923) return 'G';
        if($asc >= -17922 && $asc <= -17418) return 'H';
        if($asc >= -17417 && $asc <= -16475) return 'J';
        if($asc >= -16474 && $asc <= -16213) return 'K';
        if($asc >= -16212 && $asc <= -15641) return 'L';
        if($asc >= -15640 && $asc <= -15166) return 'M';
        if($asc >= -15165 && $asc <= -14923) return 'N';
        if($asc >= -14922 && $asc <= -14915) return 'O';
        if($asc >= -14914 && $asc <= -14631) return 'P';
        if($asc >= -14630 && $asc <= -14150) return 'Q';
        if($asc >= -14149 && $asc <= -14091) return 'R';
        if($asc >= -14090 && $asc <= -13319) return 'S';
        if($asc >= -13318 && $asc <= -12839) return 'T';
        if($asc >= -12838 && $asc <= -12557) return 'W';
        if($asc >= -12556 && $asc <= -11848) return 'X';
        if($asc >= -11847 && $asc <= -11056) return 'Y';
        if($asc >= -11055 && $asc <= -10247) return 'Z';
    }

    /**
     * @param $paramArray
     * @return array
     * 学员沟通记录
     */
    function getRecordList($paramArray){
        $datawhere = "s.student_id='{$paramArray['student_id']}' and sc.school_id='{$paramArray['school_id']}'";
        if (isset($paramArray['code']) && $paramArray['code'] == '1') {
            $datawhere .= " and sc.track_day = CURDATE()";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '2') {
            $month = date("Y-m-d", strtotime("- 1 days"));
            $datawhere .= " and sc.track_day = '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '3') {
            $week = GetWeekAll(date("Y-m-d"));
            $datawhere .= " and sc.track_day >= '{$week['nowweek_start']}' and sc.track_day <= '{$week['nowweek_end']}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '4') {
            $month = date("Y-m-d", strtotime("- 7 days"));
            $datawhere .= " and sc.track_day <= CURDATE() and sc.track_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '5') {
            $month = date("Y-m-d", strtotime("- 30 days"));
            $datawhere .= " and sc.track_day <= CURDATE() and sc.track_day >= '{$month}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '6') {
            $month = GetTheMonth(date("Y-m"));
            $datawhere .= " and sc.track_day >= '{$month[0]}' and sc.track_day <= '{$month[1]}'";
        } elseif (isset($paramArray['code']) && $paramArray['code'] == '7') {
            $month = GetMonth(date("Y-m-d"));
            $lastmonth = GetTheMonth($month);
            $datawhere .= " and sc.track_day >= '{$lastmonth[0]}' and sc.track_day <= '{$lastmonth[1]}'";
        }
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !==''){
            $datawhere .= " and sc.track_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !==''){
            $datawhere .= " and sc.track_day <= '{$paramArray['end_time']}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT sc.track_id,sc.track_from,sc.track_day,sc.track_followutime,sc.track_createtime,s.student_cnname,s.student_enname,s.student_img,s.student_sex,st.staffer_cnname,
                (SELECT r.trackresult_name FROM smc_code_trackresult AS r WHERE r.trackresult_id = sc.result_id) as trackresult_name,
                (SELECT cc.commode_name FROM crm_code_commode as cc WHERE cc.commode_id=sc.track_linktype) as commode_name,
                (SELECT co.object_name FROM crm_code_object as co WHERE co.object_code=sc.track_code) as object_name
                FROM smc_student_track as sc
                LEFT JOIN smc_student as s ON s.student_id = sc.student_id
                LEFT JOIN smc_staffer as st ON st.staffer_id = sc.staffer_id
                WHERE {$datawhere} ORDER BY sc.track_createtime DESC LIMIT {$pagestart},{$num}";
        $datalist = $this->DataControl->selectClear($sql);
        if($datalist){
            foreach($datalist as &$v){
                if(!$v['track_followutime']){
                    $v['track_followutime'] = '待定';
                }
                $v['track_createtime'] = date("Y-m-d H:i:s", $v['track_createtime']);
            }
        }else{
            $datalist = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(sc.track_id) as num
                                                      FROM smc_student_track as sc
                                                      LEFT JOIN smc_student as s ON s.student_id=sc.student_id
                                                      WHERE {$datawhere}");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }

        $data['list'] = $datalist;

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 添加沟通记录
     */
    function AddRecord($paramArray){
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['track_from'] = '1';
        $data['school_id'] = $paramArray['school_id'];
        $data['class_id'] = $paramArray['class_id'];
        $data['commode_id'] = $paramArray['commode_id'];
        $data['track_linktype'] = $paramArray['commode_id'];
        $data['student_id'] = $paramArray['student_id'];
        $classOne = $this->DataControl->selectOne("SELECT u.coursetype_id,u.coursecat_id
FROM smc_class AS c,smc_course AS u WHERE c.course_id = u.course_id AND c.class_id = '{$paramArray['class_id']}' limit 0,1");
        if(!$classOne){
            $this->error = '1';
            $this->errortip = '请确认班级信息存在！';
            return false;
        }
        $data['coursetype_id'] = $classOne['coursetype_id'];
        $data['coursecat_id'] = $classOne['coursecat_id'];
        $data['tracktype_id'] = $paramArray['tracktype_id'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['track_code'] = $paramArray['catitrack_code'];
        $tracktypeOne = $this->DataControl->selectOne("SELECT c.tracktype_name
FROM smc_code_tracktype AS c WHERE c.tracktype_id = '{$paramArray['tracktype_id']}' limit 0,1");
        if($tracktypeOne){
            $this->DataControl->query("update smc_code_tracktype set tracktype_use = tracktype_use + 1 where tracktype_id='{$paramArray['tracktype_id']}'");
            $data['track_classname'] = $tracktypeOne['tracktype_name'];
        }

        $data['result_id'] = $paramArray['trackresult_id'];
        $data['track_day'] = date("Y-m-d");
        $data['track_note'] = addslashes($paramArray['catitrack_note']);
        $data['track_picturejson'] = $paramArray['catitrack_picturejson'];
        $data['track_followutime'] = $paramArray['catitrack_followutime'];
        $data['track_createtime'] = time();
        if ($this->DataControl->insertData("smc_student_track", $data)) {
            $res = array('error' => 0, 'errortip' => '添加沟通记录成功');
        } else {
            $res = array('error' => 1, 'errortip' => '添加沟通记录失败');
        }
        return $res;
    }

    /**
     * @param $paramArray
     * @return array
     * 查看学员沟通记录
     */
    function getRecordOne($paramArray){
        $dataOne = $this->DataControl->selectOne("SELECT sc.track_id,sc.track_from,sc.track_day,sc.track_note,sc.track_followutime,sc.track_createtime,sc.track_picturejson,s.student_img,s.student_cnname,s.student_enname,s.student_sex,
                                                   (SELECT cc.commode_name FROM crm_code_commode as cc WHERE cc.commode_id=sc.track_linktype) as commode_name,
                                                   (SELECT co.object_name FROM crm_code_object as co WHERE co.object_code=sc.track_code) as object_name,
                                                   (SELECT sf.staffer_cnname FROM smc_staffer as sf WHERE sf.staffer_id=sc.staffer_id) as staffer_cnname,
                                                   (SELECT r.trackresult_name FROM smc_code_trackresult AS r WHERE r.trackresult_id=sc.result_id) as trackresult_name
                                                   FROM smc_student_track as sc
                                                   LEFT JOIN smc_student as s ON s.student_id = sc.student_id
                                                   WHERE sc.track_id='{$paramArray['track_id']}'");

        if($dataOne){
            if(!$dataOne['track_followutime']){
                $dataOne['track_followutime'] = '待定';
            }
        }else{
            $dataOne = array();
        }

        return $dataOne;
    }

    /**
     * @param $paramArray
     * @return array
     * 获取班级
     */
    function getClass($paramArray){
        $data = $this->DataControl->selectClear("SELECT c.class_id,c.class_cnname,c.class_enname
                                          FROM smc_class_hour_teaching as ht
                                          LEFT JOIN smc_class as c ON c.class_id=ht.class_id
                                          WHERE ht.staffer_id='{$this->staffer_id}' AND c.class_id>0 and c.school_id = '{$paramArray['school_id']}' GROUP BY c.class_id");
        if(!$data){
            $data = array();
        }

        return $data;
    }

    /**
     * @param $paramArray
     * @return array
     * 获取沟通模板
     */
    function getTemplateApi($paramArray)
    {
        $datawhere = "ct.comtemp_status = '1'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (ct.comtemp_code like '%{$paramArray['keyword']}%' or ct.comtemp_content like '%{$paramArray['keyword']}%')";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT * FROM eas_code_comtemp as ct WHERE {$datawhere} AND ct.company_id = '{$paramArray['company_id']}' LIMIT {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        }

        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] !== "") {
            $all_num = $this->DataControl->selectOne("SELECT COUNT(*) as num FROM eas_code_comtemp as ct WHERE {$datawhere} AND ct.company_id = '{$paramArray['company_id']}'");
            if($all_num){
                $data['allnums'] = $all_num['num'];
            }else{
                $data['allnums'] = 0;
            }
        }else{
            $data['allnums'] = '0';
        }
        $data['list'] = $dataList;

        return $data;
    }

}