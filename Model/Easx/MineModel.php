<?php

namespace Model\Easx;


Class MineModel extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";
    public $stafferOne = array();
    public $company_id = '';
    public $backData = array();
    public $staffer_id = '';

//    function __construct($publicarray)
//    {
//        parent::__construct();
//        if (is_array($publicarray)) {
//            $this->setPublic($publicarray);
//            $this->publicarray = $publicarray;
//        }
//    }

    //个人资料
    function PersonnalInfo($paramArray)
    {
        $sql = "
            SELECT
                s.*,
                f.family_mobile
            FROM
                smc_student AS s
            LEFT JOIN smc_student_family AS f ON f.student_id = s.student_id
            WHERE s.student_id = '{$paramArray['student_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        if(is_array($NoticeDetail)){
            foreach ($NoticeDetail as &$NoticeVar){
                $schoollist = $this->DataControl->selectClear("SELECT e.school_id,s.school_branch,s.school_cnname FROM smc_student_enrolled AS e 
                                                                LEFT JOIN smc_school as s ON s.school_id = e.school_id 
                                                                WHERE e.student_id = '{$NoticeVar['student_id']}' ");
                $schoolnum = '0';
                if($schoollist){
                    $schoolnum = count($schoollist);
                }else{
                    $schoolnum = 0;
                }
                $NoticeVar['schoollist'] = $schoollist;
                $NoticeVar['schoolnum'] = $schoolnum;
            }
        }

        $field = array();
        $field["student_cnname"] = "学员姓名";
        $field["student_sex"] = "性别";
        $field["student_img"] = "头像";
        $field["student_branch"] = "学员编号";
        $field["student_birthday"] = "出生日期";
        $field["family_mobile"] = "手机号码";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取个人资料成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取个人资料失败', 'result' => $result);
        }

        return $res;
    }

    //修改出生日期
    function ChangeBirthdayAction($paramArray){
        $data = array();
        $data['student_birthday'] = $paramArray['student_birthday'];
        $this->DataControl->updateData('smc_student',"student_id = '{$paramArray['student_id']}'",$data);

        $res = array('error' => '0', 'errortip' => "修改出生日期成功", 'result' => array());

        return $res;
    }

    //修改头像
    function ChangeImgAction($paramArray){
        $data = array();
        $data['student_img'] = $paramArray['student_img'];
        $this->DataControl->updateData('smc_student',"student_id = '{$paramArray['student_id']}'",$data);

        $res = array('error' => '0', 'errortip' => "修改头像成功", 'result' => array());

        return $res;
    }

    //查看家庭联系信息
    function FamilyInfo($paramArray)
    {
        $sql = "
            SELECT
                f.family_id,
                f.family_relation,
                p.parenter_cnname,
                f.family_mobile
            FROM
                smc_student_family AS f left join smc_parenter as p on f.family_mobile = p.parenter_mobile
            WHERE f.student_id = '{$paramArray['student_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["family_relation"] = "亲属关系";
        $field["parenter_cnname"] = "亲属姓名";
        $field["family_mobile"] = "手机号码";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '查看家庭联系信息成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看家庭联系信息失败', 'result' => $result);
        }

        return $res;
    }

    //反馈学校列表
    function ComplainSchool($paramArray)
    {
        $sql = "
            SELECT
                DISTINCT c.school_cnname ,c.school_id
            FROM
                smc_student_study AS s
                LEFT JOIN smc_school AS c ON s.school_id = c.school_id 
            WHERE
                s.student_id = '{$paramArray['student_id']}' and s.study_isreading = 1";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["school_cnname"] = "校区名称";
        $field["school_id"] = "学校id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈学校列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈学校列表失败', 'result' => $result);
        }

        return $res;
    }

    //反馈班级列表
    function ComplainClass($paramArray)
    {
        $sql = "
            SELECT 
                s.class_id,
                s.class_cnname
            FROM
                smc_class as s
            WHERE
                s.school_id = '{$paramArray['school_id']}' and class_status = '1'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["class_cnname"] = "班级名称";
        $field["class_id"] = "班级id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈班级列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈班级列表失败', 'result' => $result);
        }

        return $res;
    }

    //反馈教师列表
    function ComplainTeacher($paramArray)
    {
        $sql = "
            SELECT
                t.staffer_id,s.staffer_cnname
            FROM
                smc_class_teach as t left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                t.class_id = '{$paramArray['class_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_cnname"] = "教师名称";
        $field["staffer_id"] = "教师id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取反馈教师列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取反馈教师列表失败', 'result' => $result);
        }

        return $res;
    }

    //发布投诉
    function SendComplainAction($paramArray){
        $data = array();
        $data['complain_type'] = $paramArray['complain_type'];
        $data['school_id'] = $paramArray['school_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['class_id'] = $paramArray['class_id'];
        $data['complain_content'] = $paramArray['complain_content'];
        $data['complain_mediajson'] = $paramArray['complain_mediajson'];
        $data['complain_createtime'] = time();
        $this->DataControl->insertData('eas_mine_complain',$data);

        $res = array('error' => '0', 'errortip' => "发布投诉成功", 'result' => array());

        return $res;
    }

    //切换孩子列表
    function ChangeStudentList($paramArray)
    {
        $sql = "
            SELECT
                s.student_id,
                s.student_cnname,
                c.school_cnname,
                c.school_id
            FROM
                smc_student_family AS f
                LEFT JOIN smc_student AS s ON f.student_id = s.student_id 
                left join smc_student_study as ss on ss.student_id = f.student_id
                left join smc_school as c on ss.school_id = c.school_id
            WHERE
                family_mobile = '{$paramArray['mobile']}'
                GROUP BY f.student_id";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["student_cnname"] = "学员名称";
        $field["student_id"] = "学员id";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取切换孩子列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取切换孩子列表失败', 'result' => $result);
        }

        return $res;
    }

    //我的成长
    function MyGrowUp($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and m.message_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and m.message_createtime <= '{$paramArray['end_time']}'";
        }

        $field = array();
        $field["class_date"] = "课堂日期";
        $field["class_score"] = "课堂分数";
        $field["homework_date"] = "作业日期";
        $field["homework_score"] = "作业分数";

        $a = array();
        $a['class_date'] = ["12-16", "12-17", "12-18", "12-19", "12-20", "12-21", "12-22"];
        $a['class_score'] = [2.3, 3.5, 3.6, 2.8, 4, 0.5, 4.3];
        $a['homework_date'] = ["12-16", "12-17", "12-18", "12-19", "12-20", "12-21", "12-22"];
        $a['homework_score'] = [65, 75, 70, 80, 73, 93, 75];

        if ($a) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $a;
            $res = array('error' => '0', 'errortip' => '获取我的成长成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取我的成长失败', 'result' => $result);
        }

        return $res;
    }

    //上课统计
    function ClassStatistics($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sc.course_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if($paramArray['course_id']){
            $sql = "select sc.course_id,sc.course_cnname as class_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname,scp.pricinglog_buytimes as allClass,ssc.coursebalance_time as restClass,ssc.coursebalance_figure as allpay,scf.courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend
              ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc limit 0,1) as study_isreading
              from smc_student_coursebalance as ssc
              left join smc_student_coursebalance_pricinglog as scp On scp.student_id =ssc.student_id and  scp.course_id =ssc.course_id
              left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=scp.pricing_id
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=scp.pricing_id
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              where {$datawhere} and ssc.student_id='{$paramArray['student_id']}' and ssc.course_id = '{$paramArray['course_id']}'
              group by ssc.coursebalance_id
              order by ssc.coursebalance_createtime DESC
              limit {$pagestart},{$num}
              ";
        }else{
            $sql = "select sc.course_id,sc.course_cnname as class_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname,scp.pricinglog_buytimes as allClass,ssc.coursebalance_time as restClass,ssc.coursebalance_figure as allpay,scf.courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend
              ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc limit 0,1) as study_isreading
              from smc_student_coursebalance as ssc
              left join smc_student_coursebalance_pricinglog as scp On scp.student_id =ssc.student_id and  scp.course_id =ssc.course_id
              left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=scp.pricing_id
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=scp.pricing_id
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              where {$datawhere} and ssc.student_id='{$paramArray['student_id']}'
              group by ssc.coursebalance_id
              order by ssc.coursebalance_createtime DESC
              limit {$pagestart},{$num}
              ";
        }


        $NoticeDetail = $this->DataControl->selectClear($sql);

        foreach($NoticeDetail as &$value){
            $value['alreadyClass'] = $value['allClass'] - $value['restClass'];
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*) as a
            FROM
               (select sc.course_id,sc.course_cnname as class_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname,scp.pricinglog_buytimes as allClass,ssc.coursebalance_time as restClass,ssc.coursebalance_figure as allpay,scf.courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend
              ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc limit 0,1) as study_isreading
              from smc_student_coursebalance as ssc
              left join smc_student_coursebalance_pricinglog as scp On scp.student_id =ssc.student_id and  scp.course_id =ssc.course_id
              left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=scp.pricing_id
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=scp.pricing_id
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              where {$datawhere} and ssc.student_id='{$paramArray['student_id']}'
              group by ssc.coursebalance_id
              order by ssc.coursebalance_createtime DESC) as a");
        $allnums = $all_num[0]['a'];

        $field = array();
        $field["class_id"] = "课程id";
        $field["class_cnname"] = "课程名称";
        $field["allPay"] = "缴费总金额";
        $field["allClass"] = "总课时";
        $field["alreadyClass"] = "已上课时";
        $field["restClass"] = "剩余课时";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取上课统计成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取上课统计失败', 'result' => $result);
        }

        return $res;
    }

    //课消详情
    function ClassPayDetail($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and m.message_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and m.message_createtime <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                m.message_id
           FROM
                eas_class_message AS m
           WHERE {$datawhere}
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        foreach ($NoticeDetail as &$value) {
            $value['school_cnname'] = '测试幼儿园';
            $value['class_room'] = '教室12';
            $value['staffer_cnname'] = '修教师';
            $value['time'] = '12:00 - 1:00';
            $value['date'] = '12-04';
            $value['class_cnname'] = '兴趣班';
            $value['class_time'] = '33分钟';
            $value['price'] = '200';
        }


            $all_num = $this->DataControl->select("
            SELECT
               COUNT(m.message_id)
            FROM
                eas_class_message AS m
            WHERE {$datawhere}");
            $allnums = $all_num[0][0];

            $field = array();
            $field["school_cnname"] = "校区名称";
            $field["class_room"] = "教室";
            $field["staffer_cnname"] = "职工名称";
            $field["time"] = "上课时间";
            $field["date"] = "上课日期";
            $field["class_cnname"] = "课程名称";
            $field["class_time"] = "时常";
            $field["price"] = "金额";

            if ($NoticeDetail) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $NoticeDetail;
                $result["all_num"] = $allnums;
                $res = array('error' => '0', 'errortip' => '获取上课统计成功', 'result' => $result);
            } else {
                $result = array();
                $result["field"] = $field;
                $result["data"] = array();
                $result["all_num"] = 0;
                $res = array('error' => '1', 'errortip' => '获取上课统计失败', 'result' => $result);
            }

            return $res;
        }


    //投诉列表（学校）
    function ScComplainList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (ss.student_cnname like '%{$paramArray['keyword']}%' or ss.student_enname like '%{$paramArray['keyword']}%' or ss.student_branch like '%{$paramArray['keyword']}%' or f.family_mobile like '%{$paramArray['keyword']}%' )";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and c.complain_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and c.complain_createtime <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.complain_id,
                s.school_shortname as school_cnname,
                s.school_branch,
                c.complain_content,
                ss.student_cnname,
                ss.student_enname,
                ss.student_branch,
                f.family_mobile,
                FROM_UNIXTIME( c.complain_createtime, '%Y-%m-%d %H:%i' ) AS complain_createtime
            FROM
                eas_mine_complain AS c
                LEFT JOIN smc_school AS s ON c.school_id = s.school_id
                LEFT JOIN smc_student AS ss ON c.student_id = ss.student_id
                LEFT JOIN smc_student_family AS f ON c.student_id = f.student_id
           WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '0' order by c.complain_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.complain_id)
            FROM
                eas_mine_complain AS c
            LEFT JOIN
                smc_student AS ss ON c.student_id = ss.student_id
            LEFT JOIN
                smc_student_family AS f ON c.student_id = f.student_id
            WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '0' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }

    //投诉列表（教师）
    function TeaComplainList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (ss.student_cnname like '%{$paramArray['keyword']}%' or ss.student_enname like '%{$paramArray['keyword']}%' or ss.student_branch like '%{$paramArray['keyword']}%' or f.family_mobile like '%{$paramArray['keyword']}%' )";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and c.complain_createtime >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and c.complain_createtime <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.complain_id,
                s.school_cnname,
                st.staffer_enname,
                c.complain_content,
                ss.student_cnname,
                ss.student_enname,
                ss.student_branch,
                f.family_mobile,
                FROM_UNIXTIME( c.complain_createtime, '%Y-%m-%d %H:%i' ) AS complain_createtime,
                st.staffer_cnname,
                st.staffer_branch
            FROM
                eas_mine_complain AS c
                LEFT JOIN smc_school AS s ON c.school_id = s.school_id
                LEFT JOIN smc_student AS ss ON c.student_id = ss.student_id
                LEFT JOIN smc_student_family AS f ON c.student_id = f.student_id
                left join smc_staffer as st on st.staffer_id = c.staffer_id
           WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '1'  order by c.complain_createtime DESC
           Limit {$pagestart},{$num}";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.complain_id)
            FROM
                eas_mine_complain AS c
            LEFT JOIN
                smc_student AS ss ON c.student_id = ss.student_id
            LEFT JOIN
                smc_student_family AS f ON c.student_id = f.student_id
            WHERE {$datawhere} and c.school_id = '{$paramArray['school_id']}' and c.complain_type = '1' ");
        $allnums = $all_num[0][0];

        $data = array();
        $data['all_num'] = $allnums;
        $data['list'] = $NoticeDetail;

        return $data;
    }

    //投诉详情
    function ComplainDetail($paramArray)
    {
        $sql = "
            SELECT
                s.school_cnname,
                c.complain_content,
                ss.student_cnname,
                ss.student_img,
                FROM_UNIXTIME( c.complain_createtime, '%Y-%m-%d %H:%i' ) AS complain_createtime,
                c.complain_mediajson,
                st.staffer_cnname
            FROM
                eas_mine_complain AS c
                LEFT JOIN smc_school AS s ON c.school_id = s.school_id
                LEFT JOIN smc_student AS ss ON c.student_id = ss.student_id
                LEFT JOIN smc_student_family AS f ON c.student_id = f.student_id
                left join smc_staffer as st on st.staffer_id = c.staffer_id
           WHERE c.complain_id = '{$paramArray['complain_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);
        if($NoticeDetail){
            if ($paramArray['account_class'] == '0') {
                foreach($NoticeDetail as &$v){
                    $v['student_cnname'] = '***';
                    $v['student_img'] = '';
                }
            }
        }

        $field = array();
        $field["school_cnname"] = "学校";
        $field["complain_content"] = "投诉与建议";
        $field["student_cnname"] = "学员中文名";
        $field["student_img"] = "头像";
        $field["complain_createtime"] = "创建时间";
        $field["complain_mediajson"] = "视屏或者图片或者语音json";

        if ($NoticeDetail) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取投诉详情成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取投诉详情失败', 'result' => $result);
        }

        return $res;
    }

    function aView(){
        $this->getUserSig();
    }


}