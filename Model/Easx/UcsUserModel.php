<?php
/**
 * 客诉-C端用户
 */

namespace Model\Easx;


class UcsUserModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    /**
     * 根据微信data 创建或者查询用户信息 并返回
     */
    function getUser($paramArray)//openid,session_key
    {
//var_dump($paramArray);
//array(2) {
//  ["session_key"]=>
//  string(24) "Qp/YtsFB9oYn7u/6xxl+7A=="
//  ["openid"]=>
//  string(28) "ocbJO5ecMihCg4S6-VLzUQRBTGIA"
//}

        //查询openid
        $userInfo = $this->DataControl->selectOne("SELECT customer_id,customer_mobile,customer_nickname,customer_headportrait
FROM ucs_customer
WHERE customer_openid='{$paramArray['openid']}'
");

        if(!empty($userInfo))//已存在则返回用户信息
        {
//            $userInfo['is_newuser'] = 0;
            return $userInfo;

        }else{//不存在则创建并返回用户信息

            $id = $this->DataControl->insertData("ucs_customer",[
                'customer_openid' =>$paramArray['openid'],
                'customer_mobile' =>$paramArray['phoneNumber'],
                'customer_createtime' =>time()

            ]);
            if($id)
            {
                $newUserInfo = $this->DataControl->selectOne("SELECT customer_id,customer_mobile,customer_nickname,customer_headportrait 
FROM ucs_customer 
WHERE customer_id='{$id}' 
");
            }else{

                ajax_return(array('error' => 1, 'errortip'=>'添加失败!','result' =>array()));
            }

            return $newUserInfo;

        }

    }

}