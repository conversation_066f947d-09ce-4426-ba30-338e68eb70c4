<?php

namespace Model\Easx;

Class PrepareLessonsModel extends modelTpl{
    public $m;
    public $error = false;
    public $errortip = "";
    public $company_id = '';
    public $staffer_id = '';
    public $stafferOne = array();

    function __construct($publicarray)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            if(!$this->setPublic($publicarray)){
                return false;
            }
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'], $publicarray['re_postbe_id'])) {
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id, $re_postbe_id)
    {
        if($re_postbe_id) {
            $this->stafferOne = $this->DataControl->selectOne("SELECT
                                                                  s.staffer_id,s.staffer_cnname,s.staffer_branch,s.staffer_mobile,s.account_class,sp.postpart_isteregulator,sp.postpart_isbeike
                                                               FROM
                                                                  smc_staffer as s
                                                               LEFT JOIN
                                                                  gmc_staffer_postbe as p ON p.staffer_id = s.staffer_id
                                                               LEFT JOIN
                                                                  smc_school_postpart as sp ON sp.postpart_id = p.postpart_id
                                                               WHERE
                                                                  s.company_id='{$this->company_id}' AND s.staffer_id = '{$staffer_id}' AND p.postbe_id = '{$re_postbe_id}'");
            if(!$this->stafferOne['postpart_isbeike']){
                $this->error = true;
                $this->errortip = "无趣备课访问权限，请联系管理员开启";
                return false;
            }
        }else{
            $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_branch,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        }

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->staffer_id = $staffer_id;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 首页 -- 备课统计
     */
    function Statistic($paramArray)
    {
        $datawhere = " c.school_id = '{$paramArray['school_id']}' AND c.class_status = '1' and c.class_type = '0' and co.course_inclasstype = '0'";
        if(isset($paramArray['start_time']) && $paramArray['start_time'] !== ''){
            $datawhere .= " and h.hour_day >= '{$paramArray['start_time']}'";
        }
        if(isset($paramArray['end_time']) && $paramArray['end_time'] !== ''){
            $datawhere .= " and h.hour_day <= '{$paramArray['end_time']}'";
        }
        if($paramArray['account_class'] == 0){
            if($this->stafferOne['postpart_isteregulator'] == '1'){
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                            and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            }else{
                //一定是教师自己的课表
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$this->staffer_id}')
                        and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
            }
        }else{
            if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                            and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
            } else {
                $datawhere .= " and c.class_id = '0'";
            }
        }

        $waitfor = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as waitfor_num
                                                    FROM smc_class AS c
                                                    LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                    LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                    LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                    LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                    LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                    LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                    WHERE {$datawhere} and cl.classcode_isbeike = '1' and th.teachhour_isbeike = '1' AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                    AND (SELECT COUNT(p.prepare_id)
                                                    FROM
                                                        eas_prepare as p
                                                    LEFT JOIN
                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                    LEFT JOIN
                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                    WHERE
                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                     = (SELECT COUNT(tp.teachplan_id)
                                                    FROM
                                                        eas_teachhour_teachplan as tp
                                                    LEFT JOIN
                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                    LEFT JOIN
                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                    WHERE
                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))");

        $already = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.teachhour_branch) as already_num
                                                    FROM smc_class AS c
                                                    LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                    LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                    LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id
                                                    LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                    LEFT JOIN eas_prepare as p ON p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch
                                                    LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                    LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                    WHERE {$datawhere} and cl.classcode_isbeike = '1' and th.teachhour_isbeike = '1' AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes )
                                                    AND (SELECT COUNT(p.prepare_id)
                                                        FROM
                                                            eas_prepare as p
                                                        LEFT JOIN
                                                            eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                        LEFT JOIN
                                                            eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                        WHERE
                                                            p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                         = (SELECT COUNT(tp.teachplan_id)
                                                        FROM
                                                            eas_teachhour_teachplan as tp
                                                        LEFT JOIN
                                                          eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                        LEFT JOIN
                                                            eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                        WHERE
                                                            tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1')");

        $waitclass = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as waitclass_num
                                                      FROM smc_class AS c
                                                      LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                      LEFT JOIN smc_class_hour AS h ON h.class_id = c.class_id AND h.hour_ischecking='0'
                                                      LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                      LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                      LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                      WHERE {$datawhere} AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'");

        $afterclass = $this->DataControl->selectOne("SELECT COUNT(DISTINCT h.hour_id) as afterclass_num
                                                       FROM smc_class AS c
                                                       LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                       LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id AND h.hour_ischecking='1'
                                                       LEFT JOIN smc_course AS co ON co.course_id = c.course_id
                                                       LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch AND cl.company_id = co.company_id
                                                       LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                       WHERE {$datawhere} AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'");

        $data = array();
        $data['overviewList'][0]['label'] = '待备课时';
        $data['overviewList'][1]['label'] = '已备课时';
        $data['overviewList'][2]['label'] = '待上课时';
        $data['overviewList'][3]['label'] = '已上课时';
        $data['overviewList'][0]['value'] = $waitfor['waitfor_num'];
        $data['overviewList'][1]['value'] = $already['already_num'];
        $data['overviewList'][2]['value'] = $waitclass['waitclass_num'];
        $data['overviewList'][3]['value'] = $afterclass['afterclass_num'];
        return $data;
    }


    /**
     * @param $paramArray
     * @return array
     * 首页 -- 备课提醒
     */
    function LessonReminder($paramArray){
        $first_day = date("Y-m-d", strtotime("+ 14 day"));
        $datawhere = "c.school_id='{$paramArray['school_id']}' AND c.class_status = '1' and c.class_type = '0' and co.course_inclasstype = '0' and cl.classcode_isbeike = '1' and th.teachhour_isbeike = '1'";
        if($paramArray['code'] == '0'){
            $datawhere .= " and h.hour_day > CURDATE() and h.hour_day <= '{$first_day}'";
        }elseif($paramArray['code'] == '1'){
            $first_day = date("Y-m-d", strtotime("- 14 day"));
            $datawhere .= " and h.hour_day >= '{$first_day}' and h.hour_day <= CURDATE()";
        }
        if(isset($paramArray['class_id']) && $paramArray['class_id'] !== ''){
            $datawhere .= " and c.class_id='{$paramArray['class_id']}'";
        }
        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_hour_teaching AS tc WHERE tc.teaching_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                                and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            } else {
                //一定是教师自己的课表
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_hour_teaching AS tc WHERE tc.teaching_type = '0' AND tc.staffer_id = '{$this->staffer_id}')
                            and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
            }
        }else{
            if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_hour_teaching AS tc WHERE tc.teaching_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                                and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
            } else {
                $datawhere .= " and c.class_id = '0'";
            }
        }

        $reminds = array();
        if($paramArray['code'] == '0'){
            $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,co.course_branch,h.hour_id,h.hour_name,h.hour_day,h.hour_lessontimes
                   FROM smc_class as c
                   LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                   LEFT JOIN smc_course as co ON co.course_id = c.course_id
                   LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                   LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                   LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                   LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                   WHERE {$datawhere} AND h.hour_ischecking <> '-1' AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                            AND (SELECT COUNT(p.prepare_id)
                            FROM
                                eas_prepare as p
                            LEFT JOIN
                                eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                            LEFT JOIN
                                eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                            WHERE
                                p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                             = (SELECT COUNT(tp.teachplan_id)
                            FROM
                                eas_teachhour_teachplan as tp
                            LEFT JOIN
                              eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                            LEFT JOIN
                                eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                            WHERE
                                tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))
                     GROUP BY h.hour_id ORDER BY h.hour_day ASC,h.hour_name ASC";

            $dataList = $this->DataControl->selectClear($sql);
            if($dataList){
                foreach($dataList as $k => $v){
                    if($this->stafferOne['postpart_isteregulator'] == '1'){
                        $hour = $this->DataControl->getFieldOne("smc_class_hour_teaching","staffer_id","hour_id = '{$v['hour_id']}' AND teaching_type = '0'");
                        if($hour){
                            if($hour['staffer_id'] == $this->staffer_id){
                                $reminds[$k]['is_beike'] = true;
                            }else{
                                $reminds[$k]['is_beike'] = false;
                            }
                        }
                    }

                    $reminds[$k]['class_id'] = $v['class_id'];
                    $reminds[$k]['hour_id'] = $v['hour_id'];
                    $reminds[$k]['course_branch'] = $v['course_branch'];
                    $reminds[$k]['hour_lessontimes'] = $v['hour_lessontimes'];
                    $reminds[$k]['reminds'] = $v['class_cnname'].'('.$v['class_enname'].')'.$v['course_cnname'].$v['hour_name']."于".$v['hour_day'].'上课，请及时备课';
                }
            }
        }elseif($paramArray['code'] == '1'){
            $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,co.course_branch,h.hour_id,h.hour_name,h.hour_day,h.hour_lessontimes
                   FROM smc_class as c
                   LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                   LEFT JOIN smc_course as co ON co.course_id = c.course_id
                   LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                   LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                   LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                   LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                   WHERE {$datawhere} AND h.hour_ischecking = '1' AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                            AND (SELECT COUNT(p.prepare_id)
                            FROM
                                eas_prepare as p
                            LEFT JOIN
                                eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                            LEFT JOIN
                                eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                            WHERE
                                p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                             = (SELECT COUNT(tp.teachplan_id)
                            FROM
                                eas_teachhour_teachplan as tp
                            LEFT JOIN
                              eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                            LEFT JOIN
                                eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                            WHERE
                                tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))
                  GROUP BY h.hour_id ORDER BY h.hour_day ASC,h.hour_name ASC";

            $dataList = $this->DataControl->selectClear($sql);
            if($dataList){
                foreach($dataList as $k => $v){
                    if($this->stafferOne['postpart_isteregulator'] == '1'){
                        $hour = $this->DataControl->getFieldOne("smc_class_hour_teaching","staffer_id","hour_id = '{$v['hour_id']}' AND teaching_type = '0'");
                        if($hour){
                            if($hour['staffer_id'] == $this->staffer_id){
                                $reminds[$k]['is_beike'] = true;
                            }else{
                                $reminds[$k]['is_beike'] = false;
                            }
                        }
                    }

                    $reminds[$k]['class_id'] = $v['class_id'];
                    $reminds[$k]['hour_id'] = $v['hour_id'];
                    $reminds[$k]['course_branch'] = $v['course_branch'];
                    $reminds[$k]['hour_lessontimes'] = $v['hour_lessontimes'];
                    $reminds[$k]['reminds'] = $v['class_cnname'].'('.$v['class_enname'].')'.$v['course_cnname'].$v['hour_name']."已于".$v['hour_day'].'上课，请及时备课';
                }
            }
        }

        return $reminds;
    }



    /**
     * @param $paramArray
     * @return array
     * 首页 -- 我的课程表
     */
    function getTotal($paramArray){
        $date = getthemonth($paramArray['yearMonth']);

        $datawhere = "ch.hour_day >= '{$date[0]}' and ch.hour_day <= '{$date[1]}' and c.school_id='{$paramArray['school_id']}' AND c.class_status = '1' and c.class_type = '0' and co.course_inclasstype = '0' and cl.classcode_isbeike = '1' and th.teachhour_isbeike = '1'";

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                            and ch.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
                }else{
                    //一定是教师自己的课表
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$this->staffer_id}')
                        and ch.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
                }
            } else {
                //一定是教师自己的课表
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$this->staffer_id}')
                        and ch.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
            }
        }else{
            if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_teach AS tc WHERE tc.teach_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                            and ch.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
            } else {
                $datawhere .= " and c.class_id = '0'";
            }
        }

        $fieldsql = "c.class_id,c.class_branch,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,co.course_cnname,co.course_branch";

        $sql = "SELECT {$fieldsql},
                (SELECT cr.classroom_cnname FROM smc_classroom as cr WHERE cr.classroom_id=ch.classroom_id) as classroom_cnname,
                (SELECT COUNT(tp.teachplan_id) FROM eas_teachhour_teachplan as tp WHERE tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', ch.hour_lessontimes )) as prepare_allnum,
                (SELECT COUNT(p.prepare_id) FROM eas_prepare as p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', ch.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1') as prepare_num
                FROM smc_class as c
                LEFT JOIN smc_class_hour as ch ON ch.class_id = c.class_id AND ch.hour_ischecking <> '-1'
                LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                LEFT JOIN smc_course as co ON co.course_id = c.course_id
                LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',ch.hour_lessontimes)
                WHERE {$datawhere} GROUP BY ch.hour_id ORDER BY c.class_status ASC,c.class_timestr ASC,ch.hour_lessontimes ASC";
        $teachplan = $this->DataControl->selectClear($sql);

        $count = date('j', strtotime($date[1]));
        $mothListArray = array();
        for ($i = 1; $i <= $count; $i++) {
            if ($i < 10) {
                $i = '0' . $i;
            }
            $data = array();
            $data['year'] = date('Y', strtotime($date[0]));
            $data['month'] = date('m', strtotime($date[0]));
            $data['day'] = $i;
            $data['datetime'] = $data['year'] . "-" . $data['month'] . "-" . $data['day'];
            $data['week'] = date('w', strtotime($data['datetime']));

            array_push($mothListArray, $data);
        }
        $month_firstday = $mothListArray[0]['datetime'];

        $week = intval(date("w",strtotime($month_firstday)));
        if($week > 0){
            do{
                $last_data[] = date("Y-m-d",strtotime("$month_firstday - $week days"));
                $week--;
            }while($week > 0);
        }

        if ($teachplan) {
            foreach($teachplan as &$v){
                if($this->stafferOne['postpart_isteregulator'] == '1'){
                    $hour = $this->DataControl->getFieldOne("smc_class_hour_teaching","staffer_id","hour_id = '{$v['hour_id']}' AND teaching_type = '0'");
                    if($hour){
                        if($hour['staffer_id'] == $this->staffer_id){
                            $v['is_beike'] = true;
                        }else{
                            $v['is_beike'] = false;
                        }
                    }
                }

                if($v['prepare_allnum'] && $v['prepare_allnum'] == $v['prepare_num']){
                    $v['prepare_status'] = '1';
                }else{
                    $v['prepare_status'] = '0';
                }
                if($this->DataControl->getOne("smc_class_hour","hour_id='{$v['hour_id']}' and hour_ischecking='1'")){
                    $v['attend_status'] = '1';
                }else{
                    $v['attend_status'] = '0';
                }
            }

            $first_month = array();
            if($last_data){
                foreach($teachplan as &$v){
                    foreach($last_data as $key => $item) {
                        $first_month[$key]['day'] = date("d",strtotime($item));
                        $first_month[$key]['isPrevMonth'] = true;
                        if ($v['hour_day'] == $item) {
                            $first_month[$key]['list'][] = $v;
                        }
                    }
                }
            }

            $merge_month = array();
            foreach($teachplan as &$v){
                foreach($mothListArray as $key => $val) {
                    $merge_month[$key]['day'] =$val['day'];
                    if ($v['hour_day'] == $val['datetime']) {
                        $merge_month[$key]['list'][] = $v;
                    }
                }
            }
            if($last_data) {
                $merge_month = array_merge($first_month, $merge_month);
            }
        }else{
            $first_month = array();
            if($last_data) {
                foreach ($last_data as $key => $item) {
                    $first_month[$key]['day'] = date("d", strtotime($item));
                    $first_month[$key]['isPrevMonth'] = true;
                }
            }

            $merge_month = array();
            foreach($mothListArray as $key => $val) {
                $merge_month[$key]['day'] =$val['day'];
            }

            if($last_data) {
                $merge_month = array_merge($first_month, $merge_month);
            }
        }

        return $merge_month;
    }


    /**
     * @param $paramArray
     * @return array
     * 首页 -- 备课提醒
     */
    function getClassList($paramArray)
    {
        $datawhere = "c.school_id='{$paramArray['school_id']}' AND c.class_status = '1' and c.class_type = '0' and co.course_inclasstype = '0' and cl.classcode_isbeike = '1' and th.teachhour_isbeike = '1'";

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        if($paramArray['account_class'] == 0){
            if ($this->stafferOne['postpart_isteregulator'] == '1') {
                if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                    $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_hour_teaching AS tc WHERE tc.teaching_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                            and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
                }
            } else {
                //一定是教师自己的课表
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_hour_teaching AS tc WHERE tc.teaching_type = '0' AND tc.staffer_id = '{$this->staffer_id}')
                        and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$this->staffer_id}') and ht.staffer_id = '{$this->staffer_id}'";
            }
        }else{
            if (isset($paramArray['re_staffer_id']) && $paramArray['re_staffer_id'] !== '') {
                $datawhere .= " and c.class_id IN (SELECT tc.class_id FROM smc_class_hour_teaching AS tc WHERE tc.teaching_type = '0' AND tc.staffer_id = '{$paramArray['re_staffer_id']}')
                            and h.hour_id IN (SELECT t.hour_id FROM smc_class_hour_teaching AS t WHERE t.teaching_type = '0' AND t.staffer_id = '{$paramArray['re_staffer_id']}') and ht.staffer_id = '{$paramArray['re_staffer_id']}'";
            } else {
                $datawhere .= " and c.class_id = '0'";
            }
        }
        if(isset($paramArray['starttime']) && $paramArray['starttime'] !== ''){
            $datawhere .= " and h.hour_day >= '{$paramArray['starttime']}'";
        }
        if(isset($paramArray['endtime']) && $paramArray['endtime'] !== ''){
            $datawhere .= " and h.hour_day <= '{$paramArray['endtime']}'";
        }
        if(isset($paramArray['class_id']) && $paramArray['class_id'] !== ''){
            $datawhere .= " and c.class_id='{$paramArray['class_id']}'";
        }
        if(isset($paramArray['course_id']) && $paramArray['course_id'] !== ''){
            $datawhere .= " and co.course_id='{$paramArray['course_id']}'";
        }
        $data = array();
        if($paramArray['code'] == '0'){
            $first_day = date("Y-m-d", strtotime("+ 14 day"));
            $prepare = $this->DataControl->selectClear("SELECT c.class_id,c.class_cnname,c.class_branch,co.course_id,co.course_cnname,co.course_branch,h.hour_id,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_lessontimes,h.hour_ischecking,
                                                          CONCAT(stf.staffer_cnname,(CASE WHEN ifnull( stf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', stf.staffer_enname ) END ) ) as staffer_cnname
                                                          FROM smc_class as c
                                                          LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                          LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                          LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                          LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                          LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                          LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                          WHERE {$datawhere} AND h.hour_ischecking <> '-1' AND h.hour_day > CURDATE() and h.hour_day <= '{$first_day}' AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                                    AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))
                                                          GROUP BY h.hour_id ORDER BY h.hour_day ASC,h.hour_name ASC LIMIT {$pagestart},{$num}");
            if($prepare){
                foreach($prepare as $k => &$v){
                    if($this->stafferOne['postpart_isteregulator'] == '1'){
                        $hour = $this->DataControl->getFieldOne("smc_class_hour_teaching","staffer_id","hour_id = '{$v['hour_id']}' AND teaching_type = '0'");
                        if($hour){
                            if($hour['staffer_id'] == $this->staffer_id){
                                $v['is_beike'] = true;
                            }else{
                                $v['is_beike'] = false;
                            }
                        }
                    }

                    $v['class_time'] = $v['hour_day'].' '.$v['hour_starttime'].'-'.$v['hour_endtime'];
                    if($v['hour_ischecking']){
                        $v['attendclass_status_name'] = '已上课';
                    }else{
                        $v['attendclass_status_name'] = '待上课';
                    }
                }
            }else{
                $prepare = array();
            }
            $data = array();
            if(isset($paramArray['is_count']) && $paramArray['is_count'] !== ''){
                $all_nums = $this->DataControl->selectOne("SELECT COUNT(q.class_id) as num FROM
                                                             (SELECT c.class_id
                                                             FROM smc_class as c
                                                             LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                             LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                             LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                             LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                             LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                             LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                             WHERE {$datawhere} AND h.hour_ischecking <> '-1' AND h.hour_day > CURDATE() and h.hour_day <= '{$first_day}' AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN ( SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND p.teacher_branch = stf.staffer_branch AND p.teachplan_id > 0 AND p.prepare_status = '1'
                                                                    AND (SELECT COUNT(p.prepare_id)
                                                                    FROM
                                                                        eas_prepare as p
                                                                    LEFT JOIN
                                                                        eas_classcode as cl ON cl.classcode_branch = p.classcode_branch AND cl.company_id = p.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = p.company_id AND th.classcode_branch = p.classcode_branch AND th.teachhour_branch = p.teachhour_branch
                                                                    WHERE
                                                                        p.company_id = c.company_id AND p.class_branch = c.class_branch AND p.teacher_branch = stf.staffer_branch AND p.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1' AND p.teachplan_id > 0 AND p.prepare_status = '1')
                                                                     = (SELECT COUNT(tp.teachplan_id)
                                                                    FROM
                                                                        eas_teachhour_teachplan as tp
                                                                    LEFT JOIN
                                                                      eas_classcode as cl ON cl.classcode_branch = tp.classcode_branch AND cl.company_id = tp.company_id
                                                                    LEFT JOIN
                                                                        eas_teachhour as th ON th.company_id = tp.company_id AND th.classcode_branch = tp.classcode_branch AND th.teachhour_branch = tp.teachhour_branch
                                                                    WHERE
                                                                        tp.company_id = c.company_id AND tp.classcode_branch = co.course_branch AND tp.teachhour_branch = CONCAT( co.course_branch, '-', h.hour_lessontimes ) AND cl.classcode_isbeike = '1' AND th.teachhour_isbeike = '1'))
                                                             GROUP BY h.hour_id ORDER BY h.hour_day ASC,h.hour_name ASC) as q");
                if(is_array($all_nums)){
                    $data['allnums'] = $all_nums['num'];
                }else{
                    $data['allnums'] = 0;
                }
            }else{
                $data['allnums'] = 0;
            }
            $data['list'] = $prepare;

        }elseif($paramArray['code'] == '1'){
            $first_day = date("Y-m-d", strtotime("- 14 day"));
            $prepare = $this->DataControl->selectClear("SELECT c.class_id,c.class_cnname,c.class_branch,co.course_id,co.course_cnname,co.course_branch,h.hour_id,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_lessontimes,h.hour_ischecking,
                                                          CONCAT(stf.staffer_cnname,(CASE WHEN ifnull( stf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', stf.staffer_enname ) END ) ) as staffer_cnname
                                                           FROM smc_class as c
                                                           LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                           LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                           LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                           LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                           LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                           LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                           WHERE {$datawhere} AND h.hour_day >= '{$first_day}' AND h.hour_day <= CURDATE() AND h.hour_ischecking = '1'
                                                           AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN (SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.class_branch = c.class_branch AND p.prepare_status = '1')
                                                           GROUP BY h.hour_id ORDER BY h.hour_day ASC,h.hour_name ASC LIMIT {$pagestart},{$num}");
            if($prepare){
                foreach($prepare as $k => &$v){
                    if($this->stafferOne['postpart_isteregulator'] == '1'){
                        $hour = $this->DataControl->getFieldOne("smc_class_hour_teaching","staffer_id","hour_id = '{$v['hour_id']}' AND teaching_type = '0'");
                        if($hour){
                            if($hour['staffer_id'] == $this->staffer_id){
                                $v['is_beike'] = true;
                            }else{
                                $v['is_beike'] = false;
                            }
                        }
                    }

                    $v['class_time'] = $v['hour_day'].' '.$v['hour_starttime'].'-'.$v['hour_endtime'];
                    if($v['hour_ischecking']){
                        $v['attendclass_status_name'] = '已上课';
                    }else{
                        $v['attendclass_status_name'] = '待上课';
                    }
                }
            }else{
                $prepare = array();
            }
            $data = array();
            if(isset($paramArray['is_count']) && $paramArray['is_count'] !== ''){
                $all_nums = $this->DataControl->selectOne("SELECT COUNT(q.class_id) as num FROM
                                                             (SELECT c.class_id
                                                             FROM smc_class as c
                                                             LEFT JOIN smc_class_hour_teaching AS ht ON ht.class_id = c.class_id
                                                             LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                             LEFT JOIN smc_course as co ON co.course_id = c.course_id
                                                             LEFT JOIN smc_staffer AS stf ON stf.staffer_id = ht.staffer_id
                                                             LEFT JOIN eas_classcode as cl ON cl.classcode_branch = co.course_branch and cl.company_id = co.company_id
                                                             LEFT JOIN eas_teachhour as th ON th.company_id = cl.company_id AND th.classcode_branch = cl.classcode_branch AND th.teachhour_branch = CONCAT(co.course_branch,'-',h.hour_lessontimes)
                                                             WHERE {$datawhere} AND h.hour_day >= '{$first_day}' AND h.hour_day <= CURDATE() AND h.hour_ischecking = '1'
                                                             AND CONCAT( co.course_branch, '-', h.hour_lessontimes ) NOT IN (SELECT p.teachhour_branch FROM eas_prepare AS p WHERE p.class_branch = c.class_branch AND p.prepare_status = '1')
                                                             GROUP BY h.hour_id ORDER BY h.hour_day ASC,h.hour_name ASC) as q");
                if(is_array($all_nums)){
                    $data['allnums'] = $all_nums['num'];
                }else{
                    $data['allnums'] = 0;
                }
            }else{
                $data['allnums'] = 0;
            }
            $data['list'] = $prepare;
        }

        return $data;
    }

}