<?php
/**
 * 客诉-工单
 */

namespace Model\Easx;


class UcsRepairorderModel extends UcsCommonModel
{

    /**
     * 工单列表(案件库) -zjc
     * @param $paramArray
     * @return array|bool
     */
    function repairorderList($paramArray)
    {

        $datawhere=" ro.repairorder_status>=0 AND ro.company_id='{$this->company_id}' ";
//        $datawhere.=" AND find_in_set({$this->postbeUcsuserLevel},ft.feedbacktheme_rolelist) ";//对应角色可见  json格式 弃用
        $datawhere.=" AND ft.feedbacktheme_rolelist like '%{$this->postbeUcsuserLevel}%' ";//对应角色可见
        if($this->school_id!=0)
        {

            $datawhere.=" AND ro.school_id='{$this->school_id}'";
        }

        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " AND (ro.repairorder_pid like '%{$paramArray['keyword']}%' )";
        }
        //来源
        if(isset($paramArray['repairorder_from']) && $paramArray['repairorder_from'] != ''){
            $datawhere .= " AND (ro.repairorder_from = '{$paramArray['repairorder_from']}' )";
        }
        //类型
        if(isset($paramArray['feedbacktype_catgory']) && $paramArray['feedbacktype_catgory'] != ''){
            $datawhere .= " AND (ro.feedbacktype_catgory = '{$paramArray['feedbacktype_catgory']}' )";
        }
        //分类
        if(isset($paramArray['feedbacktype_id']) && $paramArray['feedbacktype_id'] != ''){
            $datawhere .= " AND (ro.feedbacktype_id = '{$paramArray['feedbacktype_id']}' )";
        }
        //主题
        if(isset($paramArray['feedbacktheme_id']) && $paramArray['feedbacktheme_id'] != ''){
            $datawhere .= " AND (ro.feedbacktheme_id = '{$paramArray['feedbacktheme_id']}' )";
        }
        //风险级别
        if(isset($paramArray['repairorder_level']) && $paramArray['repairorder_level'] != ''){
            $datawhere .= " AND (ro.repairorder_level = '{$paramArray['repairorder_level']}' )";
        }
        //校区ID
        if(isset($paramArray['repairorder_school_id']) && $paramArray['repairorder_school_id'] != ''){
            $datawhere .= " AND (ro.school_id = '{$paramArray['repairorder_school_id']}' )";
        }
        //工单状态
        if(isset($paramArray['repairorder_status']) && $paramArray['repairorder_status'] != ''){
            $datawhere .= " AND (ro.repairorder_status = '{$paramArray['repairorder_status']}' )";
        }
        //开始时间
        if(isset($paramArray['repairorder_start_date']) && $paramArray['repairorder_start_date'] != ''){
            $repairorder_start_time = strtotime($paramArray['repairorder_start_date']);
            $datawhere .= " AND (ro.repairorder_createtime > '{$repairorder_start_time}' )";
        }
        //结束时间
        if(isset($paramArray['repairorder_end_date']) && $paramArray['repairorder_end_date'] != ''){
            $repairorder_end_time = strtotime($paramArray['repairorder_end_date']);
            $datawhere .= " AND (ro.school_id < '{$repairorder_end_time}' )";
        }
        //分页
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $dataList = $this->DataControl->selectClear("
        SELECT ro.repairorder_id,ro.repairorder_pid,ro.feedbacktype_id,ro.feedbacktheme_id,ro.feedbacktype_catgory,ro.customer_id,ro.company_id,ro.school_id,ro.staffer_id,ro.repairorder_contactname,ro.repairorder_contactmobile,ro.repairorder_status,ro.repairorder_content,ro.repairorder_level,ro.repairorder_issensitive,ro.repairorder_from,ro.repairorder_fromnote,ro.create_staffer_id,ro.repairorder_updatetime,ro.repairorder_createtime,ro.repairorder_confirmtime,sc.school_shortname AS school_name,f.feedbacktype_name,ft.feedbacktheme_name,s.staffer_cnname AS first_staffer_name,
        (SELECT rt.track_type
FROM ucs_repairorder_track rt 
WHERE rt.repairorder_id=ro.repairorder_id AND rt.track_type IN(1,2,3,4) 
ORDER BY rt.track_createtime DESC 
LIMIT 1) AS track_type       
        FROM ucs_repairorder ro 
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id 
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id 
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id  
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id  
        WHERE {$datawhere}
        ORDER BY ro.repairorder_createtime DESC 
        LIMIT {$pagestart},{$num} 
        ");
        if(!empty($dataList))
        {
            foreach ($dataList as $key =>$value)
            {
                //工单的最新追踪信息
                if($value['repairorder_status']>0)
                {
                    $latestTrackData = $this->latestTrackData($value['repairorder_id']);
                    $dataList[$key]['staffer_name'] = $latestTrackData['staffer_name'];
                    $dataList[$key]['track_type_name'] = $latestTrackData['track_type_name'];
                }
                //最新流转状态
                if($value['track_type']==1)
                {
                    $dataList[$key]['track_type_name']="上升至集团";
                }elseif ($value['track_type']==2){
                    $dataList[$key]['track_type_name']="下发至校区";
                }elseif ($value['track_type']==3){
                    $dataList[$key]['track_type_name']="内部流转";
                }else{
                    $dataList[$key]['track_type_name']="";
                }
                //结案信息
                if(!empty($value['repairorder_confirmtime']))
                {
                    $dataList[$key]['repairorder_confirmdate'] = date('Y-m-d H:i:s',$value['repairorder_confirmtime']);
                    $confirmRepairorderDate = $this->confirmRepairorderDate($value['repairorder_id']);
                    $dataList[$key]['confirm_staffer_name'] = $confirmRepairorderDate['staffer_name'];
                }
                $dataList[$key]['repairorder_level'] = (int)$value['repairorder_level'];
                //来源
                if($value['repairorder_from']==1)
                {
                    $dataList[$key]['repairorder_from'] = "二维码";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "电话";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "公众号";
                }else{
                    $dataList[$key]['repairorder_from'] = "其他";
                }
                //工单类型
                if($value['repairorder_issensitive']==1)
                {
                    $dataList[$key]['repairorder_issensitive_name'] = "敏感工单";
                }else{
                    $dataList[$key]['repairorder_issensitive_name'] = "普通工单";
                }

                //类型 1:投诉 2:建议 3:表扬 4:其他
                if ($value['feedbacktype_catgory']==1) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '投诉';
                } elseif ($value['feedbacktype_catgory']==2) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '建议';
                } elseif ($value['feedbacktype_catgory']==3) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '表扬';
                } else {
                    $dataList[$key]['feedbacktype_catgory_name'] = '其他';
                }

                //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案
                if($value['repairorder_status']==0)
                {
                    $dataList[$key]['repairorder_status_name'] = '待受理';
                }elseif ($value['repairorder_status']==1)
                {
                    $dataList[$key]['repairorder_status_name'] = '处理中';
                }elseif ($value['repairorder_status']==2)
                {
                    $dataList[$key]['repairorder_status_name'] = '已处理';
                }elseif ($value['repairorder_status']==3)
                {
                    $dataList[$key]['repairorder_status_name'] = '已结案';
                }elseif ($value['repairorder_status']==4)
                {
                    $dataList[$key]['repairorder_status_name'] = '已回访';
                }else{
                    $dataList[$key]['repairorder_status_name'] = $dataList['repairorder_status'];
                }

                $dataList[$key]['repairorder_createdate'] = date('Y-m-d H:i:s',$value['repairorder_createtime']);
                if(!empty($value['repairorder_updatetime']))
                {
                    $dataList[$key]['repairorder_updatetime'] = date('Y-m-d H:i:s',$value['repairorder_updatetime']);
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis($value['repairorder_updatetime']-$value['repairorder_createtime']);
                }else{
                    $dataList[$key]['repairorder_updatetime'] = '';
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis(time()-$value['repairorder_createtime']);
                }
                $dataList[$key]['processmode_list'] = $this->processModeList($value['repairorder_status'], $value['repairorder_issensitive']);

            }

        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT COUNT(ro.repairorder_id) as datanum 
        FROM ucs_repairorder ro 
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id 
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id 
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id  
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id  
        WHERE {$datawhere}
        ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "案件库列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无案件信息可供查看哦";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 敏感工单列表
     * @param $paramArray
     * @return bool
     */
    function issensitiveRepairorderList($paramArray)
    {
        //权限校验
        if(!in_array($this->postbeUcsuserLevel,array('2','4')))
        {
            $this->error = 1;
            $this->errortip = "您没有权限查看该类信息";
            $this->result = array();
            return false;
        }
        $datawhere=" ro.repairorder_status>=0 AND ro.repairorder_issensitive=1";
        $datawhere.=" AND ft.feedbacktheme_rolelist like '%{$this->postbeUcsuserLevel}%' ";//对应角色可见
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " AND (ro.repairorder_pid like '%{$paramArray['keyword']}%' or ro.repairorder_contactmobile like '%{$paramArray['keyword']}%')";
        }
        //来源
        if(isset($paramArray['repairorder_from']) && $paramArray['repairorder_from'] != ''){
            $datawhere .= " AND (ro.repairorder_from = '{$paramArray['repairorder_from']}' )";
        }
        //类型
        if(isset($paramArray['feedbacktype_catgory']) && $paramArray['feedbacktype_catgory'] != ''){
            $datawhere .= " AND (ro.feedbacktype_catgory = '{$paramArray['feedbacktype_catgory']}' )";
        }
        //分类
        if(isset($paramArray['feedbacktype_id']) && $paramArray['feedbacktype_id'] != ''){
            $datawhere .= " AND (ro.feedbacktype_id = '{$paramArray['feedbacktype_id']}' )";
        }
        //主题
        if(isset($paramArray['feedbacktheme_id']) && $paramArray['feedbacktheme_id'] != ''){
            $datawhere .= " AND (ro.feedbacktheme_id = '{$paramArray['feedbacktheme_id']}' )";
        }
        //风险级别
        if(isset($paramArray['repairorder_level']) && $paramArray['repairorder_level'] != ''){
            $datawhere .= " AND (ro.repairorder_level = '{$paramArray['repairorder_level']}' )";
        }
        //校区ID
        if(isset($paramArray['repairorder_school_id']) && $paramArray['repairorder_school_id'] != ''){
            $datawhere .= " AND (ro.school_id = '{$paramArray['repairorder_school_id']}' )";
        }
        //工单状态
        if(isset($paramArray['repairorder_status']) && $paramArray['repairorder_status'] != ''){
            $datawhere .= " AND (ro.repairorder_status = '{$paramArray['repairorder_status']}' )";
        }
        //开始时间
        if(isset($paramArray['repairorder_start_date']) && $paramArray['repairorder_start_date'] != ''){
            $repairorder_start_time = strtotime($paramArray['repairorder_start_date']);
            $datawhere .= " AND (ro.repairorder_createtime > '{$repairorder_start_time}' )";
        }
        //结束时间
        if(isset($paramArray['repairorder_end_date']) && $paramArray['repairorder_end_date'] != ''){
            $repairorder_end_time = strtotime($paramArray['repairorder_end_date']);
            $datawhere .= " AND (ro.school_id < '{$repairorder_end_time}' )";
        }
        //分页
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $dataList = $this->DataControl->selectClear("
        SELECT ro.repairorder_id,ro.repairorder_pid,ro.feedbacktype_id,ro.feedbacktheme_id,ro.feedbacktype_catgory,ro.customer_id,ro.company_id,ro.school_id,ro.staffer_id,ro.repairorder_contactname,ro.repairorder_contactmobile,ro.repairorder_status,ro.repairorder_content,ro.repairorder_level,ro.repairorder_issensitive,ro.repairorder_from,ro.repairorder_fromnote,ro.create_staffer_id,ro.repairorder_updatetime,ro.repairorder_createtime,ro.repairorder_confirmtime,sc.school_shortname AS school_name,f.feedbacktype_name,ft.feedbacktheme_name,s.staffer_cnname AS first_staffer_name,c.company_shortname AS company_name,
        (SELECT rt.track_type
FROM ucs_repairorder_track rt 
WHERE rt.repairorder_id=ro.repairorder_id AND rt.track_type IN(1,2,3,4) 
ORDER BY rt.track_createtime DESC 
LIMIT 1) AS track_type  
        FROM ucs_repairorder ro 
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id 
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id 
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id 
        LEFT JOIN gmc_company c ON ro.company_id=c.company_id 
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id 
        WHERE {$datawhere}
        LIMIT {$pagestart},{$num} 
        ");
        if(!empty($dataList))
        {
            foreach ($dataList as $key =>$value)
            {
                //工单的最新追踪信息
                if($value['repairorder_status']>0)
                {
                    $latestTrackData = $this->latestTrackData($value['repairorder_id']);
                    $dataList[$key]['staffer_name'] = $latestTrackData['staffer_name'];
                    $dataList[$key]['track_type_name'] = $latestTrackData['track_type_name'];
                }
                //最新流转状态
                if($value['track_type']==1)
                {
                    $dataList[$key]['track_type_name']="上升至集团";
                }elseif ($value['track_type']==2){
                    $dataList[$key]['track_type_name']="下发至校区";
                }elseif ($value['track_type']==3){
                    $dataList[$key]['track_type_name']="内部流转";
                }else{
                    $dataList[$key]['track_type_name']="";
                }
                //结案信息
                if(!empty($value['repairorder_confirmtime']))
                {
                    $dataList[$key]['repairorder_confirmdate'] = date('Y-m-d H:i:s',$value['repairorder_confirmtime']);
                    $confirmRepairorderDate = $this->confirmRepairorderDate($value['repairorder_id']);
                    $dataList[$key]['confirm_staffer_name'] = $confirmRepairorderDate['staffer_name'];
                }
                $dataList[$key]['repairorder_level'] = (int)$value['repairorder_level'];
                //来源
                if($value['repairorder_from']==1)
                {
                    $dataList[$key]['repairorder_from'] = "二维码";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "电话";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "公众号";
                }else{
                    $dataList[$key]['repairorder_from'] = "其他";
                }
                //工单类型
                if($value['repairorder_issensitive']==1)
                {
                    $dataList[$key]['repairorder_issensitive_name'] = "敏感工单";
                    $dataList[$key]['first_staffer_name'] = "";
                }else{
                    $dataList[$key]['repairorder_issensitive_name'] = "普通工单";
                }

                //类型 1:投诉 2:建议 3:表扬 4:其他
                if ($value['feedbacktype_catgory']==1) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '投诉';
                } elseif ($value['feedbacktype_catgory']==2) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '建议';
                } elseif ($value['feedbacktype_catgory']==3) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '表扬';
                } else {
                    $dataList[$key]['feedbacktype_catgory_name'] = '其他';
                }

                //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案
                if($value['repairorder_status']==0)
                {
                    $dataList[$key]['repairorder_status_name'] = '待受理';
                }elseif ($value['repairorder_status']==1)
                {
                    $dataList[$key]['repairorder_status_name'] = '处理中';
                }elseif ($value['repairorder_status']==2)
                {
                    $dataList[$key]['repairorder_status_name'] = '已处理';
                }elseif ($value['repairorder_status']==3)
                {
                    $dataList[$key]['repairorder_status_name'] = '已结案';
                }elseif ($value['repairorder_status']==4) {
                    $dataList[$key]['repairorder_status_name'] = '已回访';
                }else{
                    $dataList[$key]['repairorder_status_name'] = $dataList['repairorder_status'];
                }

                $dataList[$key]['repairorder_createdate'] = date('Y-m-d H:i:s',$value['repairorder_createtime']);
                if(!empty($value['repairorder_updatetime']))
                {
                    $dataList[$key]['repairorder_updatetime'] = date('Y-m-d H:i:s',$value['repairorder_updatetime']);
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis($value['repairorder_updatetime']-$value['repairorder_createtime']);
                }else{
                    $dataList[$key]['repairorder_updatetime'] = '';
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis(time()-$value['repairorder_createtime']);
                }
                $dataList[$key]['processmode_list'] = $this->processModeList($value['repairorder_status'], $value['repairorder_issensitive']);

            }

        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT COUNT(ro.repairorder_id) as datanum 
        FROM ucs_repairorder ro 
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id 
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id 
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id 
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id
        WHERE {$datawhere}
        ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "敏感工单列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无敏感工单";
            $this->result = $result;
            return false;
        }


    }

    /**
     * 手机号历史工单
     * @param $paramArray
     * @return bool
     */
    function historyRepairorderList($paramArray)
    {
        if(!isset($paramArray['repairorder_mobile']) || $paramArray['repairorder_mobile']=='')
        {
            $this->error = 1;
            $this->errortip = "参数缺失:ID";
            $this->result = array();
            return false;
        }

        $datawhere = " ro.repairorder_contactmobile='{$paramArray['repairorder_mobile']}' ";
        $dataList = $this->DataControl->selectClear("
        SELECT ro.repairorder_id,ro.repairorder_pid,ro.feedbacktype_id,ro.feedbacktheme_id,ro.feedbacktype_catgory,ro.customer_id,ro.company_id,ro.school_id,ro.staffer_id,ro.repairorder_contactname,ro.repairorder_contactmobile,ro.repairorder_status,ro.repairorder_content,ro.repairorder_level,ro.repairorder_issensitive,ro.repairorder_from,ro.repairorder_fromnote,ro.create_staffer_id,ro.repairorder_updatetime,ro.repairorder_createtime,ro.repairorder_confirmtime,sc.school_shortname AS school_name,f.feedbacktype_name,ft.feedbacktheme_name,s.staffer_cnname AS first_staffer_name,c.company_shortname AS company_name 
        FROM ucs_repairorder ro 
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id 
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id 
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id 
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id 
        LEFT JOIN gmc_company c ON ro.company_id=c.company_id 
        WHERE {$datawhere}
        ");
        if(!empty($dataList))
        {
            foreach ($dataList as $key =>$value)
            {
                //工单的最新追踪信息
                if($value['repairorder_status']>0)
                {
                    $latestTrackData = $this->latestTrackData($value['repairorder_id']);
                    $dataList[$key]['staffer_name'] = $latestTrackData['staffer_name'];
                    $dataList[$key]['track_type_name'] = $latestTrackData['track_type_name'];
                }
                //结案信息
                if(!empty($value['repairorder_confirmtime']))
                {
                    $dataList[$key]['repairorder_confirmdate'] = date('Y-m-d H:i:s',$value['repairorder_confirmtime']);
                    $confirmRepairorderDate = $this->confirmRepairorderDate($value['repairorder_id']);
                    $dataList[$key]['confirm_staffer_name'] = $confirmRepairorderDate['staffer_name'];
                }
                $dataList[$key]['repairorder_level'] = (int)$value['repairorder_level'];
                //来源
                if($value['repairorder_from']==1)
                {
                    $dataList[$key]['repairorder_from'] = "二维码";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "电话";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "公众号";
                }else{
                    $dataList[$key]['repairorder_from'] = "其他";
                }
                //工单类型
                if($value['repairorder_issensitive']==1)
                {
                    $dataList[$key]['repairorder_issensitive_name'] = "敏感工单";
                }else{
                    $dataList[$key]['repairorder_issensitive_name'] = "普通工单";
                }

                //类型 1:投诉 2:建议 3:表扬 4:其他
                if ($value['feedbacktype_catgory']==1) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '投诉';
                } elseif ($value['feedbacktype_catgory']==2) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '建议';
                } elseif ($value['feedbacktype_catgory']==3) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '表扬';
                } else {
                    $dataList[$key]['feedbacktype_catgory_name'] = '其他';
                }

                //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案
                if($value['repairorder_status']==0)
                {
                    $dataList[$key]['repairorder_status_name'] = '待受理';
                }elseif ($value['repairorder_status']==1)
                {
                    $dataList[$key]['repairorder_status_name'] = '处理中';
                }elseif ($value['repairorder_status']==2)
                {
                    $dataList[$key]['repairorder_status_name'] = '已处理';
                }elseif ($value['repairorder_status']==3)
                {
                    $dataList[$key]['repairorder_status_name'] = '已结案';
                }elseif ($value['repairorder_status']==4)
                {
                    $dataList[$key]['repairorder_status_name'] = '已回访';
                }else{
                    $dataList[$key]['repairorder_status_name'] = $dataList['repairorder_status'];
                }

                $dataList[$key]['repairorder_createdate'] = date('Y-m-d H:i:s',$value['repairorder_createtime']);
                if(!empty($value['repairorder_updatetime']))
                {
                    $dataList[$key]['repairorder_updatetime'] = date('Y-m-d H:i:s',$value['repairorder_updatetime']);
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis($value['repairorder_updatetime']-$value['repairorder_createtime']);
                }else{
                    $dataList[$key]['repairorder_updatetime'] = '';
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis(time()-$value['repairorder_createtime']);
                }

            }

        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "历史工单列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无投诉信息";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 我的工单列表 -zjc
     * @param $paramArray
     * @return array
     */
    function myRepairorderList($paramArray)
    {

        $datawhere2 = " rt.staffer_id='{$this->staffer_id}' AND ro2.repairorder_status>=0 ";
        $datawhere1 = " ro1.company_id='{$this->company_id}' AND ro1.school_id='{$this->school_id}' AND ro1.repairorder_status=0 ";

        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere2 .= " AND (
            (ro2.repairorder_pid like '%{$paramArray['keyword']}%' ) OR 
            (ro2.repairorder_contactmobile like '%{$paramArray['keyword']}%' )
            )";
            $datawhere1 .= " AND (
            (ro1.repairorder_pid like '%{$paramArray['keyword']}%' ) OR 
            (ro1.repairorder_contactmobile like '%{$paramArray['keyword']}%' )
            ) ";
        }
        //来源
        if(isset($paramArray['repairorder_from']) && $paramArray['repairorder_from'] != ''){
            $datawhere2 .= " AND (ro2.repairorder_from = '{$paramArray['repairorder_from']}' )";
            $datawhere1 .= " AND (ro1.repairorder_from = '{$paramArray['repairorder_from']}' )";
        }
        //类型
        if(isset($paramArray['feedbacktype_catgory']) && $paramArray['feedbacktype_catgory'] != ''){
            $datawhere2 .= " AND (ro2.feedbacktype_catgory = '{$paramArray['feedbacktype_catgory']}' )";
            $datawhere1 .= " AND (ro1.feedbacktype_catgory = '{$paramArray['feedbacktype_catgory']}' )";
        }
        //分类
        if(isset($paramArray['feedbacktype_id']) && $paramArray['feedbacktype_id'] != ''){
            $datawhere2 .= " AND (ro2.feedbacktype_id = '{$paramArray['feedbacktype_id']}' )";
            $datawhere1 .= " AND (ro1.feedbacktype_id = '{$paramArray['feedbacktype_id']}' )";
        }
        //主题
        if(isset($paramArray['feedbacktheme_id']) && $paramArray['feedbacktheme_id'] != ''){
            $datawhere2 .= " AND (ro2.feedbacktheme_id = '{$paramArray['feedbacktheme_id']}' )";
            $datawhere1 .= " AND (ro1.feedbacktheme_id = '{$paramArray['feedbacktheme_id']}' )";
        }
        //风险级别
        if(isset($paramArray['repairorder_level']) && $paramArray['repairorder_level'] != ''){
            $datawhere2 .= " AND (ro2.repairorder_level = '{$paramArray['repairorder_level']}' )";
            $datawhere1 .= " AND (ro1.repairorder_level = '{$paramArray['repairorder_level']}' )";
        }
        //工单状态
        if(isset($paramArray['repairorder_status']) && $paramArray['repairorder_status'] != ''){
            $datawhere2 .= " AND (ro2.repairorder_status = '{$paramArray['repairorder_status']}' )";
            $datawhere1 .= " AND (ro1.repairorder_status = '{$paramArray['repairorder_status']}' )";
        }
        //校区ID
        if(isset($paramArray['repairorder_school_id']) && $paramArray['repairorder_school_id'] != ''){
            $datawhere2 .= " AND (ro2.school_id = '{$paramArray['repairorder_school_id']}' )";
            $datawhere1 .= " AND (ro1.school_id = '{$paramArray['repairorder_school_id']}' )";
        }
        //开始时间
        if(isset($paramArray['repairorder_start_date']) && $paramArray['repairorder_start_date'] != ''){
            $repairorder_start_time = strtotime($paramArray['repairorder_start_date']);
            $datawhere2 .= " AND (ro2.repairorder_createtime >= '{$repairorder_start_time}' )";
            $datawhere1 .= " AND (ro1.repairorder_createtime >= '{$repairorder_start_time}' )";
        }
        //结束时间
        if(isset($paramArray['repairorder_end_date']) && $paramArray['repairorder_end_date'] != ''){
            $repairorder_end_time = strtotime($paramArray['repairorder_end_date']." 23:59:59");
            $datawhere2 .= " AND (ro2.repairorder_createtime <= '{$repairorder_end_time}' )";
            $datawhere1 .= " AND (ro1.repairorder_createtime <= '{$repairorder_end_time}' )";
        }
        //分页
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $dataList = $this->DataControl->selectClear("
SELECT ro.repairorder_id,ro.repairorder_pid,ro.feedbacktype_id,ro.feedbacktheme_id,ro.feedbacktype_catgory,ro.customer_id,ro.company_id,ro.school_id,ro.staffer_id,ro.repairorder_contactname,ro.repairorder_contactmobile,ro.repairorder_status,ro.repairorder_content,ro.repairorder_level,ro.repairorder_issensitive,ro.repairorder_from,ro.repairorder_fromnote,ro.create_staffer_id,ro.repairorder_updatetime,ro.repairorder_createtime,ro.repairorder_confirmtime,sc.school_shortname AS school_name,f.feedbacktype_name,ft.feedbacktheme_name,s.staffer_cnname AS first_staffer_name,c.company_shortname AS company_name,
(SELECT rt.track_type
FROM ucs_repairorder_track rt 
WHERE rt.repairorder_id=ro.repairorder_id AND rt.track_type IN(1,2,3,4) 
ORDER BY rt.track_createtime DESC 
LIMIT 1) AS track_type
        FROM ucs_repairorder ro
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id
        LEFT JOIN gmc_company c ON ro.company_id=c.company_id 
        WHERE ro.repairorder_id IN (
            SELECT rt.repairorder_id
            FROM ucs_repairorder_track rt
            LEFT JOIN ucs_repairorder AS ro2 ON rt.repairorder_id=ro2.repairorder_id 
            WHERE {$datawhere2}
            UNION
            SELECT ro1.repairorder_id 
            FROM ucs_repairorder ro1 
            WHERE {$datawhere1}
            )
        ORDER BY ro.repairorder_createtime DESC
LIMIT {$pagestart},{$num} 
");

        if(!empty($dataList))
        {
            foreach ($dataList as $key =>$value)
            {
                //工单的最新追踪信息
                if($value['repairorder_status']>0)
                {
                    $latestTrackData = $this->latestTrackData($value['repairorder_id']);
                    $dataList[$key]['staffer_name'] = $latestTrackData['staffer_name'];
                }
                //最新流转状态
                if($value['track_type']==1)
                {
                    $dataList[$key]['track_type_name']="上升至集团";
                }elseif ($value['track_type']==2){
                    $dataList[$key]['track_type_name']="下发至校区";
                }elseif ($value['track_type']==3){
                    $dataList[$key]['track_type_name']="内部流转";
                }else{
                    $dataList[$key]['track_type_name']="";
                }
                //结案信息
                if(!empty($value['repairorder_confirmtime']))
                {
                    $dataList[$key]['repairorder_confirmdate'] = date('Y-m-d H:i:s',$value['repairorder_confirmtime']);
                    $confirmRepairorderDate = $this->confirmRepairorderDate($value['repairorder_id']);
                    $dataList[$key]['confirm_staffer_name'] = $confirmRepairorderDate['staffer_name'];
                }
                //星级
                $dataList[$key]['repairorder_level'] = (int)$value['repairorder_level'];
                //来源
                if($value['repairorder_from']==1)
                {
                    $dataList[$key]['repairorder_from'] = "二维码";
                }elseif ($value['repairorder_from']==2) {
                    $dataList[$key]['repairorder_from'] = "电话";
                }elseif ($value['repairorder_from']==3) {
                    $dataList[$key]['repairorder_from'] = "公众号";
                }else{
                    $dataList[$key]['repairorder_from'] = "其他";
                }
                //工单类型
                if($value['repairorder_issensitive']==1)
                {
                    $dataList[$key]['repairorder_issensitive_name'] = "敏感工单";
                    $dataList[$key]['first_staffer_name'] = "";
                }else{
                    $dataList[$key]['repairorder_issensitive_name'] = "普通工单";
                }

                //类型 1:投诉 2:建议 3:表扬 4:其他
                if ($value['feedbacktype_catgory']==1) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '投诉';
                } elseif ($value['feedbacktype_catgory']==2) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '建议';
                } elseif ($value['feedbacktype_catgory']==3) {
                    $dataList[$key]['feedbacktype_catgory_name'] = '表扬';
                } else {
                    $dataList[$key]['feedbacktype_catgory_name'] = '其他';
                }

                //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案 4:已回访
                if($value['repairorder_status']==0)
                {
                    $dataList[$key]['repairorder_status'] = '待受理';
                }elseif ($value['repairorder_status']==1)
                {
                    $dataList[$key]['repairorder_status'] = '处理中';
                }elseif ($value['repairorder_status']==2)
                {
                    $dataList[$key]['repairorder_status'] = '已处理';
                }elseif ($value['repairorder_status']==3)
                {
                    $dataList[$key]['repairorder_status'] = '已结案';
                }elseif ($value['repairorder_status']==4)
                {
                    $dataList[$key]['repairorder_status'] = '已回访';
                }else{
                    $dataList[$key]['repairorder_status'] = $dataList['repairorder_status'];
                }
                //是否回访
                if($value['repairorder_status']==4)
                {
                    $dataList[$key]['repairorder_isreturnvisit'] = "是";
                }else{
                    $dataList[$key]['repairorder_isreturnvisit'] = "否";
                }

                $dataList[$key]['repairorder_createdate'] = date('Y-m-d H:i:s',$value['repairorder_createtime']);
                //最后更新时间  累计时长=最后更新时间-创建时间
                if($value['repairorder_updatetime'])
                {
                    $dataList[$key]['repairorder_updatetime'] = date('Y-m-d H:i:s',$value['repairorder_updatetime']);
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis(time()-$value['repairorder_updatetime']);
                }else{
                    $dataList[$key]['repairorder_updatetime'] = '';
                    $dataList[$key]['repairorder_processtime'] = $this->timestampConversionHis(time()-$value['repairorder_createtime']);
                }

                //操作列表
                $dataList[$key]['processmode_list'] = $this->processModeList($value['repairorder_status'], $value['repairorder_issensitive']);
            }

        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sqlCount = "SELECT COUNT(ro.repairorder_id) as datanum
        FROM ucs_repairorder ro
        LEFT JOIN ucs_code_feedbacktheme ft ON ro.feedbacktheme_id=ft.feedbacktheme_id
        LEFT JOIN ucs_code_feedbacktype f ON ro.feedbacktype_id=f.feedbacktype_id
        LEFT JOIN smc_school sc ON ro.school_id=sc.school_id
        LEFT JOIN smc_staffer s ON ro.staffer_id=s.staffer_id
        LEFT JOIN gmc_company c ON ro.company_id=c.company_id
        WHERE ro.repairorder_id IN (
            SELECT rt.repairorder_id
            FROM ucs_repairorder_track rt
            LEFT JOIN ucs_repairorder AS ro2 ON rt.repairorder_id=ro2.repairorder_id 
            WHERE {$datawhere2}
            UNION
            SELECT ro1.repairorder_id
            FROM ucs_repairorder ro1
            WHERE {$datawhere1}
            )
        ";
            $count = $this->DataControl->selectOne($sqlCount);
            $count = $count['datanum']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "我的工单列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无投诉信息";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 创建工单 -zjc
     * @param $paramArray
     * @return bool
     */
    function addRepairorder($paramArray)
    {
        if(empty($paramArray['feedbacktype_id']) || empty($paramArray['feedbacktheme_id']) ||empty($paramArray['feedbacktype_catgory']) ||empty($paramArray['repairorder_contactname']) ||empty($paramArray['repairorder_contactmobile']) ||empty($paramArray['repairorder_content']) ||empty($paramArray['repairorder_from']) )
        {
            $this->error = 1;
            $this->errortip = "还有待完善的必填内容!";
            $this->result = array();
            return false;
        }
        if($paramArray['repairorder_from']==1 && empty($paramArray['customer_id']))//小程序类型的工单不能手动录入
        {
            $this->error = 1;
            $this->errortip = "此类工单不支持手动录入!";
            $this->result = array();
            return false;
        }
        if($paramArray['repairorder_from']==1){
            $this->submitTimeLimit($paramArray['repairorder_contactmobile']);
        }

//            $two_hours_ago = time()-60*60*2;//2小时
//            $dataList = $this->DataControl->selectOne("SELECT repairorder_pid
//FROM ucs_repairorder
//WHERE repairorder_contactmobile='{$paramArray['repairorder_contactmobile']}' AND repairorder_createtime>'{$two_hours_ago}'
//LIMIT 1
//");
        if(!empty($dataList))
        {
            $this->error = 2;
            $this->errortip = "您近期已提交工单，请耐心等待客服回复!";
            $this->result = array();
            return false;
        }

        $today_start_time=strtotime(date("Y-m-d",time()));//今天开始时间
        do{
            //工单编号
            $repairorder_pid = $this->setRepairorderPid();
        }while($this->DataControl->selectOne("SELECT repairorder_id FROM ucs_repairorder WHERE repairorder_createtime>'{$today_start_time}' AND repairorder_pid='{$repairorder_pid}' LIMIT 1 "));

        if(empty($paramArray['school_id']))//工单当前位置
        {
            $repairorder_nowposition = 1;
        }else{
            $repairorder_nowposition = 2;
        }
        $insertData['repairorder_pid']=$repairorder_pid;
        $insertData['feedbacktype_id']=$paramArray['feedbacktype_id'];
        $insertData['feedbacktheme_id']=$paramArray['feedbacktheme_id'];
        $insertData['feedbacktype_catgory']=$paramArray['feedbacktype_catgory'];
        $insertData['customer_id']=empty($paramArray['customer_id'])?0:$paramArray['customer_id'];
        $insertData['company_id']=$paramArray['company_id'];
        $insertData['school_id']=empty($paramArray['school_id'])?0:$paramArray['school_id'];
        $insertData['staffer_id']=0;
        $insertData['repairorder_contactname']=$paramArray['repairorder_contactname'];
        $insertData['repairorder_contactmobile']=$paramArray['repairorder_contactmobile'];
        $insertData['repairorder_content']=$paramArray['repairorder_content'];
        $insertData['repairorder_status']=0;
        $insertData['repairorder_level']=$paramArray['repairorder_level'];
        $insertData['repairorder_from']=$paramArray['repairorder_from'];
        $insertData['repairorder_fromnote']=empty($paramArray['repairorder_fromnote'])?'':$paramArray['repairorder_fromnote'];
        $insertData['create_staffer_id']=empty($paramArray['staffer_id'])?0:$paramArray['staffer_id'];
        $insertData['repairorder_createtime']=time();
        $insertData['repairorder_nowposition']=$repairorder_nowposition;
        $id = $this->DataControl->insertData("ucs_repairorder",$insertData);
        $result = array();
        $insertData['repairorder_createtime'] = date('Y-m-d H:i:s',$insertData['repairorder_createtime']);
        $result['datalist'] = $insertData;

        if($id>0){
            //写入跟踪记录
            $this->DataControl->insertData("ucs_repairorder_track",[
                'repairorder_id'=>$id,
                'track_name'=>"录入工单",
                'staffer_id'=>$this->staffer_id,
                'track_level'=>$paramArray['repairorder_level'],
                'track_content'=>$paramArray['repairorder_content'],
                'track_isurgent'=>0,
                'track_status'=>0,
                'track_type'=>0,
                'track_createtime'=>time(),
                'track_nexttime'=>0,
            ]);

            $this->error = 0;
            $this->errortip = "工单添加成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "工单提交失败!";
            $this->result = array();
            return false;
        }
    }

    /**
     * 工单提交时间限制
     * @param 手机号
     */
    function submitTimeLimit($phone_number)
    {
        $two_hours_ago = time()-60*60*2;//2小时
        $dataList = $this->DataControl->selectOne("SELECT repairorder_pid
FROM ucs_repairorder 
WHERE repairorder_contactmobile='{$phone_number}' AND repairorder_createtime>'{$two_hours_ago}' 
LIMIT 1
");
        if(!empty($dataList))
        {
            $this->error = 2;
            $this->errortip = "一个手机号码2小时内只能提交一次哦!";
            $this->result = array();
            return false;
        }

    }

    /**
     * 工单详情 -zjc
     * @param $paramArray
     * @return bool
     */
    function repairorderDetails($paramArray)
    {
        if(!isset($paramArray['repairorder_id']) || $paramArray['repairorder_id']=='')
        {
            $this->error = 1;
            $this->errortip = "参数缺失:工单ID";
            $this->result = array();
            return false;
        }

        //工单信息
        $dataList = $this->DataControl->selectOne(" 
        SELECT r.repairorder_id,r.repairorder_pid,r.feedbacktype_id,r.feedbacktheme_id,r.feedbacktype_catgory,r.company_id,r.school_id,r.staffer_id,r.repairorder_contactname,r.repairorder_contactmobile,r.repairorder_content,r.repairorder_status,r.repairorder_level,r.repairorder_issensitive,r.repairorder_from,r.repairorder_fromnote,r.create_staffer_id,r.repairorder_updatetime,r.repairorder_createtime,r.repairorder_confirmtime,cf.feedbacktype_name,ct.feedbacktheme_name   
        FROM ucs_repairorder r 
        LEFT JOIN ucs_code_feedbacktype cf ON r.feedbacktype_id=cf.feedbacktype_id 
        LEFT JOIN ucs_code_feedbacktheme ct ON r.feedbacktheme_id=ct.feedbacktheme_id 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        LIMIT 1 
        ");
        if(!empty($dataList))
        {
            //学校信息
            if(!empty($dataList['school_id']))
            {
                //学校信息
                $school_info = $this->getSchoolData(['school_id'=>$dataList['school_id']]);
                //省份名称
                if(!empty($school_info['school_province']))
                {
                    $school_province = $this->getRegionName($school_info['school_province']);
                    $school_info['school_province_name'] = $school_province['region_name'];
                }else{
                    $school_info['school_province_name'] = '';
                }
                //市名称
                if(!empty($school_info['school_city']))
                {
                    $school_city = $this->getRegionName($school_info['school_city']);
                    $school_info['school_city_name'] = $school_city['region_name'];
                }else{
                    $school_info['school_city_name'] = '';
                }

            }else{
                $school_info = array();
            }
            $dataList['school_info'] = $school_info;
            //职员信息
            $dataList['staffer_info'] = $this->stafferData();

            //类型 1:投诉 2:建议 3:表扬 4:其他
            if($dataList['feedbacktype_catgory']==1) {
                $dataList['feedbacktype_catgory_name'] = '投诉';
            }elseif ($dataList['feedbacktype_catgory']==2) {
                $dataList['feedbacktype_catgory_name'] = '建议';
            }elseif ($dataList['feedbacktype_catgory']==3) {
                $dataList['feedbacktype_catgory_name'] = '表扬';
            }else{
                $dataList['feedbacktype_catgory_name'] = '其他';
            }

            //来源
            if($dataList['repairorder_from']==1)
            {
                $dataList['repairorder_from_name'] = '二维码';
            }elseif ($dataList['repairorder_from']==2)
            {
                $dataList['repairorder_from_name'] = '电话';
            }elseif ($dataList['repairorder_from']==3)
            {
                $dataList['repairorder_from_name'] = '公众号';
            }else{
                $dataList['repairorder_from_name'] = $dataList['repairorder_fromnote'];
            }

            //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案
            if($dataList['repairorder_status']==0)
            {
                $dataList['repairorder_status_name'] = '待受理';
            }elseif ($dataList['repairorder_status']==1)
            {
                $dataList['repairorder_status_name'] = '处理中';
            }elseif ($dataList['repairorder_status']==2)
            {
                $dataList['repairorder_status_name'] = '已处理';
            }elseif ($dataList['repairorder_status']==3)
            {
                $dataList['repairorder_status_name'] = '已结案';
            }else{
                $dataList['repairorder_status_name'] = $dataList['repairorder_status'];
            }

            $dataList['repairorder_level'] = (int)$dataList['repairorder_level'];
            $dataList['repairorder_createtime'] = date('Y-m-d H:i:s',$dataList['repairorder_createtime']);

            //跟踪记录
            $trackList = $this->DataControl->selectClear("
SELECT rt.track_id,rt.repairorder_id,rt.track_name,rt.staffer_id,rt.track_level,rt.track_content,rt.track_isurgent,rt.track_status,rt.track_type,rt.track_createtime,rt.track_nexttime 
FROM ucs_repairorder_track rt 
WHERE rt.repairorder_id='{$paramArray['repairorder_id']}' 
ORDER BY rt.track_createtime DESC 
");
            if(!empty($trackList))
            {
                foreach ($trackList as $key =>$value)
                {
                    $trackList[$key]['track_createtime'] = date('Y-m-d H:i:s',$value['track_createtime']);
                    if(!empty($value['track_nexttime']))
                    {
                        $trackList[$key]['track_nexttime'] = date('Y-m-d H:i:s',$value['track_nexttime']);
                    }
                    $trackList[$key]['track_level'] = (int)$value['track_level'];
                    //状态0 -2:已删除 0:待受理 1:处理中 2:已处理 3:已结案
                    if($value['track_status']==0)
                    {
                        $trackList[$key]['track_status_name'] = '待受理';
                    }elseif ($value['track_status']==1)
                    {
                        $trackList[$key]['track_status_name'] = '处理中';
                    }elseif ($value['track_status']==2)
                    {
                        $trackList[$key]['track_status_name'] = '已处理';
                    }elseif ($value['track_status']==3)
                    {
                        $trackList[$key]['track_status_name'] = '已结案';
                    }else{
                        $trackList[$key]['track_status_name'] = $dataList['track_status'];
                    }
                    $paramGetStafferData['staffer_id'] =  $value['staffer_id'];
                    $trackStafferInfo = $this->getStafferData($paramGetStafferData);
//                    print_r($trackStafferInfo);
                    $trackList[$key]['staffer_belongs'] = $trackStafferInfo['identity_name'];
                    $trackList[$key]['staffer_title'] = $trackStafferInfo['postbe_ucsuserlevel_name'];


                    //文件信息
                    $filesList = $this->DataControl->selectClear("
SELECT f.track_id,f.files_type,f.files_name,f.files_url,f.files_md5 
FROM ucs_upload_files f 
WHERE f.track_id={$value['track_id']} 
");
                    $trackList[$key]['fileslist'] = is_array($filesList)?$filesList:array();
                }

            }
            $dataList['trackList'] = is_array($trackList)?$trackList:array();

        }

        $result = array();
        $result['datalist'] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "工单信息获成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "没有找到该工单的任何记录!";
            $this->result = $result;
            return false;
        }


    }

    /**
     * 编辑-提交工单 -zjc
     * @param $paramArray
     * @return bool
     */
    function submitRepairorder($paramArray)
    {
        if(empty($paramArray['repairorder_id']))
        {
            $this->error = 1;
            $this->errortip = "请选择要编辑的工单!";
            $this->result = array();
            return false;
        }
        if(empty($paramArray['feedbacktype_id']) || empty($paramArray['feedbacktheme_id']) || empty($paramArray['repairorder_contactname']) ||empty($paramArray['repairorder_contactmobile']) ||empty($paramArray['repairorder_content']) ||empty($paramArray['repairorder_from']) )
        {
            $this->error = 1;
            $this->errortip = "还有待完善的必填内容!";
            $this->result = array();
            return false;
        }
        if($paramArray['repairorder_from']==1 && empty($paramArray['customer_id']))//小程序类型的工单不能手动录入
        {
            $this->error = 1;
            $this->errortip = "此类工单不支持手动录入!";
            $this->result = array();
            return false;
        }

        $updateData['feedbacktype_id']=$paramArray['feedbacktype_id'];
        $updateData['feedbacktheme_id']=$paramArray['feedbacktheme_id'];
        $updateData['customer_id']=empty($paramArray['customer_id'])?0:$paramArray['customer_id'];
        $updateData['company_id']=$paramArray['company_id'];
        $updateData['school_id']=empty($paramArray['school_id'])?0:$paramArray['school_id'];
        $updateData['repairorder_contactname']=$paramArray['repairorder_contactname'];
        $updateData['repairorder_contactmobile']=$paramArray['repairorder_contactmobile'];
        $updateData['repairorder_content']=$paramArray['repairorder_content'];
        $updateData['repairorder_level']=$paramArray['repairorder_level'];
        $updateData['repairorder_from']=$paramArray['repairorder_from'];
        $updateData['repairorder_fromnote']=empty($paramArray['repairorder_fromnote'])?'':$paramArray['repairorder_fromnote'];
        $updateData['repairorder_updatetime']=time();
        $res = $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",$updateData);
        $result = array();
        $result['datalist'] = $updateData;

        if($res!=false){
            //写入跟踪记录
            $this->DataControl->insertData("ucs_repairorder_track",[
                'repairorder_id'=>$paramArray['repairorder_id'],
                'track_name'=>"编辑工单",
                'staffer_id'=>$this->staffer_id,
                'track_level'=>$paramArray['repairorder_level'],
                'track_content'=>$paramArray['repairorder_content'],
                'track_isurgent'=>0,
                'track_status'=>1,
                'track_type'=>0,
                'track_createtime'=>time(),
                'track_nexttime'=>0,
            ]);

            $this->error = 0;
            $this->errortip = "工单更新成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "工单更新失败!";
            $this->result = array();
            return false;
        }

    }

    /**
     * 删除工单 -zjc
     * @param $paramArray
     * @return bool
     */
    function deleteRepairorder($paramArray)
    {

        if(empty($paramArray['repairorder_id']))
        {
            $this->error = 1;
            $this->errortip = "参数缺失:工单ID!";
            $this->result = array();
            return false;
        }
        if(!in_array($this->postbeUcsuserLevel,array('2','4')))
        {
            $this->error = 1;
            $this->errortip = "没有操作权限";
            $this->result = array();
            return false;
        }

        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_issensitive,r.repairorder_status,
        (SELECT rt.track_type
        FROM ucs_repairorder_track rt 
        WHERE rt.repairorder_id='{$paramArray['repairorder_id']}' AND rt.track_type IN(1,2,3) 
        ORDER BY rt.track_createtime DESC 
        LIMIT 1) AS track_type
        FROM ucs_repairorder r  
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}'  ");
        if(empty($repairorderData))
        {
            $this->error = 1;
            $this->errortip = "没有找到该工单";
            $this->result = array();
            return false;
        }else{

            if($repairorderData['repairorder_issensitive']==1)
            {
                $this->error = 1;
                $this->errortip = "敏感工单不可删除";
                $this->result = array();
                return false;
            }
            if($repairorderData['repairorder_status']=='-2')
            {
                $this->error = 1;
                $this->errortip = "工单已删除,请勿重复操作!";
                $this->result = array();
                return false;
            }
            if(!empty($repairorderData['track_type']))
            {
                $this->error = 1;
                $this->errortip = "已存在流转状态的工单不可删除!";
                $this->result = array();
                return false;
            }

        }

        $res = $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>-2
        ]);

        $result=array();
        if($res!=false){
            $this->error = 0;
            $this->errortip = "删除操作成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "请再试试吧!";
            $this->result = $result;
            return false;
        }


    }

    /**
     * 受理订单 -zjc
     * @param $paramArray repairorder_id 单ID
     * @param $paramArray track_level 星级
     * @param $paramArray track_content 内容
     * @return bool
     */
    function processRepairorder($paramArray)
    {
        if(empty($paramArray['repairorder_id']) )
        {
            $this->error = 1;
            $this->errortip = "参数不全:repairorder_id!";
            $this->result = array();
            return false;
        }
        //查看工单的当前状态
        $repairorderInfo = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_level  
        FROM ucs_repairorder r
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderInfo['repairorder_status']!=0)
        {
            $this->error = 1;
            $this->errortip = "该工单已被受理,请刷新后再试!";
            $this->result = array();
            return false;
        }

        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
        }
        //可操作人(全部可选)

        $this->DataControl->begintransaction();
        //写入跟踪记录
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>"受理工单",
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$repairorderInfo['repairorder_level'],
            'track_isurgent'=>0,
            'track_status'=>1,
            'track_type'=>4,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime,
        ]);
        //写入跟踪权限
        $this->DataControl->insertData("ucs_repairorder_staffer",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'staffer_id'=>$this->staffer_id,
            'staffer_role'=>$this->postbeUcsuserLevel,
            'dispose_showing'=>1,
            'dispose_settleing'=>1,
            'dispose_tailing'=>1
        ]);
        if($track_id>0)
        {
            //写入跟踪记录log
            $this->DataControl->insertData("ucs_repairorder_trackstalog",[
                'track_id'=>$track_id,
                'staffer_id'=>$this->staffer_id
            ]);
            //更新工单状态
            $res= $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
                'staffer_id'=>$this->staffer_id,
                'repairorder_status'=>1,
                'repairorder_updatetime'=>time()
            ]);
            $this->DataControl->commit();
        }else{
            $this->DataControl->rollback();
        }

        $result=array();
        if($res!=false){
            $this->error = 0;
            $this->errortip = "已成功受理该工单!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "操作失败,刷新后重试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 催单 -zjc
     * @param $paramArray
     * @return bool
     */
    function setRush($paramArray)
    {
        if(empty($paramArray['repairorder_id']) )
        {
            $this->error = 1;
            $this->errortip = "参数不全:工单ID!";
            $this->result = array();
            return false;
        }
        //权限校验
        if(!in_array($this->postbeUcsuserLevel,array("2","4")))
        {
            $this->error = 1;
            $this->errortip = "您没有操作权限!";
            $this->result = array();
            return false;
        }

        //查看工单的当前状态
        $repairorderInfo = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_level,r.repairorder_pid  
        FROM ucs_repairorder r
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderInfo['repairorder_status']==0 || $repairorderInfo['repairorder_status']=='-2')
        {
            $this->error = 1;
            $this->errortip = "工单当前状态不可催单!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>"催单",
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$repairorderInfo['repairorder_level'],
            'track_content'=>"发起催单",
            'track_isurgent'=>1,
            'track_status'=>$repairorderInfo['repairorder_status'],
            'track_type'=>0,
            'track_createtime'=>time(),
            'track_nexttime'=>0,
        ]);

        //获取工单当前跟进人
        $nowStaffer = $this->getRepairorderNowTrackStafferId([
            'repairorder_id'=>$paramArray['repairorder_id']
        ]);
        //消息提醒
        $this->DataControl->insertData("ucs_repairorder_message",[
            'repairorder_pid'=>$repairorderInfo['repairorder_pid'],
            'staffer_id'=>$nowStaffer['staffer_id'],
            'message_content'=>"您有编号[".$repairorderInfo['repairorder_pid']."]的工单被催单,请尽快处理!",
            'message_status'=>0,
            'message_playname'=>"工单被催单",
            'message_createtime'=>time()
        ]);

        $result=array();
        if($track_id>0){
            $this->error = 0;
            $this->errortip = "催单成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 再跟进(结案后翻案) -zjc
     * @param $paramArray
     * @return bool
     */
    function reFollowUpRepairorder($paramArray)
    {
        if(empty($paramArray['repairorder_id']) )
        {
            $this->error = 1;
            $this->errortip = "参数不全:工单ID!";
            $this->result = array();
            return false;
        }
        //查看工单的当前状态
        $repairorderInfo = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_level 
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if(!in_array($repairorderInfo['repairorder_status'],array("3","4")))
        {
            $this->error = 1;
            $this->errortip = "该工单不是结案状态,不可再跟进!";
            $this->result = array();
            return false;
        }

        $this->DataControl->begintransaction();
        //写入跟踪记录
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>"结案工单再次跟进",
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$repairorderInfo['repairorder_level'],
            'track_content'=>"工单再次激活,等待受理",
            'track_isurgent'=>0,
            'track_status'=>0,
            'track_type'=>0,
            'track_createtime'=>time(),
            'track_nexttime'=>0,
        ]);
        if($track_id>0)
        {
            //更新工单状态
            $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
                'repairorder_status'=>0,
                'repairorder_updatetime'=>time()
            ]);
            $this->DataControl->commit();
        }else{
            $this->DataControl->rollback();
        }

        $result=array();
        if($track_id>0){
            $this->error = 0;
            $this->errortip = "工单状态已更新,可以再次跟进了!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "操作失败,刷新后重试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 上升敏感 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray followup_staffer_json  接受敏感工单的客服
     * @return bool\
     */
    function setIssensitive($paramArray)
    {
        if(empty($paramArray['repairorder_id']) || empty($paramArray['followup_staffer_json']))
        {
            $this->error = 1;
            $this->errortip = "必要参数缺失!";
            $this->result = array();
            return false;
        }
        //查询订单当前状态是否敏感
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_issensitive,r.repairorder_level,r.repairorder_pid   
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}'  
        ");

        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3')//结案 删除
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }
        if($repairorderData['repairorder_issensitive']==1)
        {
            $this->error = 1;
            $this->errortip = "已是敏感工单,无需重复操作!";
            $this->result = array();
            return false;
        }

        //开启事务
//        $this->DataControl->begintransaction();
        //写入跟踪记录
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id' =>$paramArray['repairorder_id'],
            'track_name' =>"上升敏感工单",
            'staffer_id' =>$this->staffer_id,
            'track_level' =>$paramArray['track_level'],
            'track_content' =>"已上升为敏感工单",
            'track_isurgent' =>0,
            'track_status' =>1,
            'track_createtime' =>time()
        ]);

        //更新权限
        $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' AND staffer_role IN('1','3')",[
            'dispose_showing'=>0,
            'dispose_settleing'=>0,
            'dispose_tailing'=>0

        ]);

        //指定人列表
        $followup_staffer_json = str_replace("\\","",$paramArray['followup_staffer_json']);
        $staffer_id_array = json_decode($followup_staffer_json,true);

        //判断指定人是否已在表中 在则更新  不在则 插入
        foreach($staffer_id_array as $key => $value)
        {
            $res = $this->DataControl->selectOne("
SELECT rs.staffer_id 
FROM ucs_repairorder_staffer rs
WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$value['key']}'
");
            if(empty($res))//插入
            {
                $this->DataControl->insertData("ucs_repairorder_staffer",[
                    'repairorder_id'=>$paramArray['repairorder_id'],
                    'staffer_id'=>$value['key'],
                    'staffer_role'=>$value['ucsuserlevel'],
                    'dispose_showing'=>1,
                    'dispose_settleing'=>1,
                    'dispose_tailing'=>1
                ]);
            }else{//更新
                $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' AND staffer_id='{$value['key']}' ",[
                    'staffer_role'=>$value['ucsuserlevel'],
                    'dispose_showing'=>1,
                    'dispose_settleing'=>1,
                    'dispose_tailing'=>1
                ]);
            }

            //消息提醒
            $this->DataControl->insertData("ucs_repairorder_message",[
                'repairorder_pid'=>$repairorderData['repairorder_pid'],
                'staffer_id'=>$value['key'],
                'message_content'=>"您有编号[".$repairorderData['repairorder_pid']."]的敏感工单转接,请尽快处理!",
                'message_status'=>0,
                'message_playname'=>"敏感工单转接",
                'message_createtime'=>time()
            ]);

        }
        $result=array();
        if($track_id>0)
        {
            //更新订单状态
            $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
                'repairorder_issensitive'=>1,
                'repairorder_updatetime'=>time()
            ]);

            $this->error = 0;
            $this->errortip = "操作成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 工单跟进 -zjc
     * @param $paramArray repairorder_id ID
     * @param $paramArray track_level 星级
     * @param $paramArray track_content 内容
     * @param $paramArray track_nextdate 日期
     * @param $paramArray [track_files_json] 文件
     * @return bool\
     */
    function followUpRepairorder($paramArray)
    {
        //查看工单状态(是否敏感)
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");

        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3')//结案 删除
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

        //权限校验
        if($repairorderData['repairorder_issensitive']==1)//敏感工单
        {
            if(!in_array($this->repairorder_issensitive,array('2','4')))//非校长,集团高管
            {
                //查询工单权限表
                $stafferInfo = $this->DataControl->selectOne("
                SELECT rs.staffer_role,rs.dispose_showing,rs.dispose_settleing,rs.dispose_tailing 
                FROM ucs_repairorder_staffer rs 
                WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$paramArray['staffer_id']}' 
                ");
                if(!empty($stafferInfo))
                {
                    if($stafferInfo['dispose_tailing']==0)
                    {
                        $this->error = 1;
                        $this->errortip = "没有该操作权限:(";
                        $this->result = array();
                        return false;
                    }
                }else{
                    $this->error = 1;
                    $this->errortip = "没有该操作权限!";
                    $this->result = array();
                    return false;
                }

            }

        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
            //添加日程安排
            $this->addRepairorderSchedule([
                'repairorder_id'=>$paramArray['repairorder_id'],
                'schedule_remindtime'=>$track_nexttime
            ]);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'工单跟进',
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>1,
            'track_type'=>4,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //查询并写入权限记录
        $stafferInfo= $this->DataControl->selectOne("
SELECT rs.staffer_role,rs.dispose_showing,rs.dispose_settleing,rs.dispose_tailing 
FROM ucs_repairorder_staffer rs 
WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$this->staffer_id}' 
        ");
        if(empty($stafferInfo))
        {
            $this->DataControl->insertData("ucs_repairorder_staffer",[
                'repairorder_id'=>$paramArray['repairorder_id'],
                'staffer_id'=>$this->staffer_id,
                'staffer_role'=>$this->postbeUcsuserLevel,
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }else{
            $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' AND staffer_id='{$this->staffer_id}'  ",[
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }
        //更新工单状态(时间)
        if($track_id>0)
        {
            $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
                'repairorder_level'=>$paramArray['track_level'],
                'repairorder_updatetime'=>time(),
                'repairorder_status'=>1
            ]);

            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }

        }

        $result=array();
        if($track_id>0){
            $this->error = 0;
            $this->errortip = "跟进成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 工单已处理 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray track_nextdate
     * @param $paramArray track_level
     * @param $paramArray track_content
     * @param $paramArray [track_files_json]
     * @return bool\
     */
    function processCompletedRepairorder($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3')//结案 删除
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

        //权限校验
        $stafferData = $this->DataControl->selectOne("
        SELECT rs.dispose_tailing 
        FROM ucs_repairorder_staffer rs 
        WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$this->staffer_id}' 
        ");
        if($stafferData['dispose_tailing']!=1)
        {
            $this->error = 1;
            $this->errortip = "没有操作权限!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
            //添加日程安排
            $this->addRepairorderSchedule([
                'repairorder_id'=>$paramArray['repairorder_id'],
                'schedule_remindtime'=>$track_nexttime
            ]);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'工单已处理',
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>2,
            'track_type'=>4,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //更新工单状态(时间)
        if($track_id>0)
        {
            $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
                'repairorder_status'=>2,
                'repairorder_updatetime'=>time(),
            ]);
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }

        }
        $result=array();
        if($track_id>0){
            $this->error = 0;
            $this->errortip = "工单已处理!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 内部流转工单 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray track_level
     * @param $paramArray track_content
     * @param $paramArray followup_staffer_id
     * @param $paramArray followup_staffer_ucsuserlevel
     * @param $paramArray [track_nextdate]
     * @param $paramArray [track_files_json]
     * @return bool\
     */
    function internalFlowRepairorder($paramArray)
    {
        //查看工单状态(非敏感)
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_pid,r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3' || $repairorderData['repairorder_issensitive']==1)//结案 删除 敏感
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
            //添加日程安排
            $this->addRepairorderSchedule([
                'repairorder_id'=>$paramArray['repairorder_id'],
                'schedule_remindtime'=>$track_nexttime
            ]);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'内部流转',
            'staffer_id'=>$paramArray['followup_staffer_id'],
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>1,
            'track_type'=>3,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //查询并写入权限记录
        $stafferInfo= $this->DataControl->selectOne("
SELECT rs.staffer_role,rs.dispose_showing,rs.dispose_settleing,rs.dispose_tailing 
FROM ucs_repairorder_staffer rs 
WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$paramArray['followup_staffer_id']}' 
        ");
        if(empty($stafferInfo))
        {
            $this->DataControl->insertData("ucs_repairorder_staffer",[
                'repairorder_id'=>$paramArray['repairorder_id'],
                'staffer_id'=>$paramArray['followup_staffer_id'],
                'staffer_role'=>$paramArray['followup_staffer_ucsuserlevel'],
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }else{
            $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' AND staffer_id='{$paramArray['followup_staffer_id']}'  ",[
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }

        //消息提醒
        $this->DataControl->insertData("ucs_repairorder_message",[
            'repairorder_pid'=>$repairorderData['repairorder_pid'],
            'staffer_id'=>$repairorderData['followup_staffer_id'],
            'message_content'=>"您有工单编号".$repairorderData['repairorder_pid']."的工单转接,请尽快处理",
            'message_status'=>0,
            'message_playname'=>"工单流转提醒",
            'message_createtime'=>time()
        ]);

        //更新工单状态(时间)
        $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>1,
            'repairorder_updatetime'=>time(),
        ]);
        $result=array();
        if($track_id>0){
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }

            $this->error = 0;
            $this->errortip = "内部流转成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 向上流转工单 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray followup_staffer_id
     * @param $paramArray track_level
     * @param $paramArray track_content
     * @param $paramArray followup_staffer_ucsuserlevel
     * @param $paramArray [track_files_json]
     * @return bool\
     */
    function upwardFlowRepairorder($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_pid,r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3' || $repairorderData['repairorder_issensitive']==1)//结案 删除 敏感
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'向上流转',
            'staffer_id'=>$paramArray['followup_staffer_id'],
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>1,
            'track_type'=>1,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //查询并写入权限记录
        $stafferInfo= $this->DataControl->selectOne("
SELECT rs.staffer_role,rs.dispose_showing,rs.dispose_settleing,rs.dispose_tailing 
FROM ucs_repairorder_staffer rs 
WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$paramArray['followup_staffer_id']}' 
        ");
        if(empty($stafferInfo))
        {
            $this->DataControl->insertData("ucs_repairorder_staffer",[
                'repairorder_id'=>$paramArray['repairorder_id'],
                'staffer_id'=>$paramArray['followup_staffer_id'],
                'staffer_role'=>$paramArray['followup_staffer_ucsuserlevel'],
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }else{
            $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' AND staffer_id='{$paramArray['followup_staffer_id']}'  ",[
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }

        //消息提醒
        $this->DataControl->insertData("ucs_repairorder_message",[
            'repairorder_pid'=>$repairorderData['repairorder_pid'],
            'staffer_id'=>$repairorderData['followup_staffer_id'],
            'message_content'=>"您有工单编号".$repairorderData['repairorder_pid']."的工单转接,请尽快处理",
            'message_status'=>0,
            'message_playname'=>"工单流转提醒",
            'message_createtime'=>time()
        ]);

        //更新工单状态(时间)
        $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>1,
            'repairorder_nowposition'=>1,
            'repairorder_updatetime'=>time(),
        ]);
        $result=array();
        if($track_id>0){
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }
            $this->error = 0;
            $this->errortip = "上升至集团成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 向下流转工单 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray followup_staffer_id
     * @param $paramArray followup_staffer_ucsuserlevel
     * @param $paramArray [track_files_json]
     * @return bool\
     */
    function downFlowRepairorder($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_pid,r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3' || $repairorderData['repairorder_issensitive']==1)//结案 删除 敏感
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'向下流转',
            'staffer_id'=>$paramArray['followup_staffer_id'],
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>1,
            'track_type'=>2,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //查询并写入权限记录
        $stafferInfo= $this->DataControl->selectOne("
SELECT rs.staffer_role,rs.dispose_showing,rs.dispose_settleing,rs.dispose_tailing 
FROM ucs_repairorder_staffer rs 
WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$paramArray['followup_staffer_id']}' 
        ");
        if(empty($stafferInfo))
        {
            $this->DataControl->insertData("ucs_repairorder_staffer",[
                'repairorder_id'=>$paramArray['repairorder_id'],
                'staffer_id'=>$paramArray['followup_staffer_id'],
                'staffer_role'=>$paramArray['followup_staffer_ucsuserlevel'],
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }else{
            $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' AND staffer_id='{$paramArray['followup_staffer_id']}'  ",[
                'dispose_showing'=>1,
                'dispose_settleing'=>0,
                'dispose_tailing'=>1
            ]);
        }

        //消息提醒
        $this->DataControl->insertData("ucs_repairorder_message",[
            'repairorder_pid'=>$repairorderData['repairorder_pid'],
            'staffer_id'=>$repairorderData['followup_staffer_id'],
            'message_content'=>"您有工单编号".$repairorderData['repairorder_pid']."的工单转接,请尽快处理",
            'message_status'=>0,
            'message_playname'=>"工单流转提醒",
            'message_createtime'=>time()
        ]);

        //更新工单状态(时间)
        $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>1,
            'repairorder_nowposition'=>0,
            'repairorder_updatetime'=>time(),
        ]);
        $result=array();
        if($track_id>0){
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }
            $this->error = 0;
            $this->errortip = "下发至校区成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }


    }

    /**
     * 处理敏感工单 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray [track_files_json]
     * @return bool\
     */
    function processIssensitiveRepairorder($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_pid,r.repairorder_status,r.repairorder_issensitive,r.staffer_id
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderData['repairorder_status']=='-2' || $repairorderData['repairorder_status']=='3')//结案 删除
        {
            $this->error = 1;
            $this->errortip = "当前工单状态已不可操作!";
            $this->result = array();
            return false;
        }

        //权限校验
        if($repairorderData['repairorder_issensitive']==1)//敏感工单
        {
            if(!in_array($this->repairorder_issensitive,array('2','4')))//非校长,集团高管
            {
                //查询工单权限表
                $stafferInfo = $this->DataControl->selectOne("
                SELECT rs.staffer_role,rs.dispose_showing,rs.dispose_settleing,rs.dispose_tailing 
                FROM ucs_repairorder_staffer rs 
                WHERE rs.repairorder_id='{$paramArray['repairorder_id']}' AND rs.staffer_id='{$paramArray['staffer_id']}' 
                ");
                if(!empty($stafferInfo))
                {
                    if($stafferInfo['dispose_tailing']==0)
                    {
                        $this->error = 1;
                        $this->errortip = "没有该操作权限:(";
                        $this->result = array();
                        return false;
                    }
                }else{
                    $this->error = 1;
                    $this->errortip = "没有该操作权限!";
                    $this->result = array();
                    return false;
                }

            }

        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
            //添加日程安排
            $this->addRepairorderSchedule([
                'repairorder_id'=>$paramArray['repairorder_id'],
                'schedule_remindtime'=>$track_nexttime
            ]);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'向下流转',
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>1,
            'track_type'=>2,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //更新工单状态(时间)
        $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>2,
            'repairorder_updatetime'=>time(),
        ]);
        $result=array();
        if($track_id>0){
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }
            $this->error = 0;
            $this->errortip = "操作成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 结案 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray track_level
     * @param $paramArray track_content
     * @param $paramArray [track_nextdate]
     * @param $paramArray [message_remind]
     * @param $paramArray [track_files_json]
     * @return bool
     */
    function closeRepairorder($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_issensitive,r.staffer_id 
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");
        if($repairorderData['staffer_id']!=$this->staffer_id)
        {
            $this->error = 1;
            $this->errortip = "您没有该工单的结案权限!";
            $this->result = array();
            return false;
        }
        if($repairorderData['repairorder_status']==3)
        {
            $this->error = 1;
            $this->errortip = "工单已结案,请勿重复操作!";
            $this->result = array();
            return false;
        }
        if(in_array($repairorderData['repairorder_status'],array("0","1","4")))
        {
            $this->error = 1;
            $this->errortip = "请根据工单流程规范操作!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        if(!empty($paramArray['track_nextdate']))
        {
            $track_nexttime = strtotime($paramArray['track_nextdate']);
            //添加日程安排
            $this->addRepairorderSchedule([
                'repairorder_id'=>$paramArray['repairorder_id'],
                'schedule_remindtime'=>$track_nexttime
            ]);
        }else{
            $track_nexttime = 0;
        }
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'结案',
            'staffer_id'=>$this->staffer_id,
            'track_level'=>$paramArray['track_level'],
            'track_content'=>$paramArray['track_content'],
            'track_status'=>3,
            'track_type'=>0,
            'track_createtime'=>time(),
            'track_nexttime'=>$track_nexttime
        ]);

        //更新权限表
        $this->DataControl->updateData("ucs_repairorder_staffer","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'dispose_tailing'=>0
        ]);

        //更新工单状态(时间)
        $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>3,
            'repairorder_confirmtime'=>time(),
            'repairorder_updatetime'=>time(),
        ]);
        $result=array();
        if($track_id>0){
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }
            $this->error = 0;
            $this->errortip = "工单已结案!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }

    }

    /**
     * 回访工单 -zjc
     * @param $paramArray repairorder_id
     * @param $paramArray track_content
     * @param $paramArray [track_files_json]
     * @return bool\
     */
    function visitRepairorder($paramArray)
    {
        //查看工单状态
        $repairorderData = $this->DataControl->selectOne("
        SELECT r.repairorder_status,r.repairorder_issensitive 
        FROM ucs_repairorder r 
        WHERE r.repairorder_id='{$paramArray['repairorder_id']}' 
        ");

        if($repairorderData['repairorder_status']!=3)
        {
            $this->error = 1;
            $this->errortip = "工单还没有结案,无法回访!";
            $this->result = array();
            return false;
        }

        //写入跟踪记录
        $track_id = $this->DataControl->insertData("ucs_repairorder_track",[
            'repairorder_id'=>$paramArray['repairorder_id'],
            'track_name'=>'回访',
            'staffer_id'=>$this->staffer_id,
            'track_level'=>0,
            'track_content'=>$paramArray['track_content'],
            'track_status'=>4,
            'track_type'=>0,
            'track_createtime'=>time(),
            'track_nexttime'=>0
        ]);

        //更新工单状态(时间)
        $this->DataControl->updateData("ucs_repairorder","repairorder_id='{$paramArray['repairorder_id']}' ",[
            'repairorder_status'=>4,
            'repairorder_updatetime'=>time(),
        ]);
        $result=array();
        if($track_id>0){
            //上传文件
            if(!empty($paramArray['track_files_json']))
            {
                $this->addTrackFiles([
                    'track_files_json'=>$paramArray['track_files_json'],
                    'track_id'=>$track_id
                ]);
            }
            $this->error = 0;
            $this->errortip = "操作成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "再试试!";
            $this->result = $result;
            return false;
        }


    }

    /**
     * 获取工单最新跟踪信息
     * @param $repairorder_id
     * @return false|mixed
     */
    function latestTrackData($repairorder_id)
    {
        $data = $this->DataControl->selectOne("
        SELECT rt.track_id,rt.staffer_id,rt.track_status,rt.track_type,s.staffer_cnname AS staffer_name 
        FROM ucs_repairorder_track rt 
        LEFT JOIN smc_staffer s ON rt.staffer_id=s.staffer_id 
        WHERE rt.repairorder_id='{$repairorder_id}' 
        ORDER BY rt.track_createtime DESC 
        LIMIT 1 
        ");
        if(!empty($data))
        {
            if($data['track_type']==1){
                $data['track_type_name'] = "上升至集团";
            }elseif ($data['track_type']==2){
                $data['track_type_name'] = "下发至校区";
            }elseif ($data['track_type']==3){
                $data['track_type_name'] = "内部流转";
            }elseif ($data['track_type']==4){
                $data['track_type_name'] = "跟进";
            }elseif ($data['track_type']==0){
                $data['track_type_name'] = "";
            }else{
                $data['track_type_name'] = $data['track_type'];
            }
        }

        return $data;
    }

    /**
     * 结案人信息
     * @param $repairorder_id
     * @return false|mixed
     */
    function confirmRepairorderDate($repairorder_id)
    {
        $data = $this->DataControl->selectOne("
        SELECT rt.track_id,rt.staffer_id,rt.track_status,rt.track_type,s.staffer_cnname AS staffer_name 
        FROM ucs_repairorder_track rt 
        LEFT JOIN smc_staffer s ON rt.staffer_id=s.staffer_id 
        WHERE rt.repairorder_id='{$repairorder_id}' AND rt.track_status=3 
        ORDER BY rt.track_createtime DESC 
        LIMIT 1 
        ");

        return $data;

    }





}