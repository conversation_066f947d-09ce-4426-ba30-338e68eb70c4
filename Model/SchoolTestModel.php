<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> <PERSON><PERSON> Qi
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model;

class SchoolTestModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $companyId = '1001';
    public $SchoolData;

    function __construct($companyId = '1001')
    {
        parent::__construct();
        $this->companyId = $companyId;
        $this->SchoolData = new \Dbsqlsrv();
    }


    function ImportStubalance(){
        $dateexcelarray = $this->SchoolData->findAll("SELECT TA.BRANCH,TB.BRANCH_CNAME,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG
,剩余金额=SUM(TA.PRE_AMT-TA.USED_AMT)
FROM
(
	SELECT B.BRANCH,B.STUD_NO
	,PRE_AMT
	,USED_AMT = (SELECT ISNULL(SUM(WOFF_AMT),0) FROM CM_ReceCr_PreUsed WHERE PRE_RECE_KEY_NO = A.PRE_RECE_KEY_NO AND PRE_DATA_SEQ = A.PRE_DATA_SEQ AND WOFF_DATE <= '20190531')
	FROM CM_ReceCr_PreMstr A,CM_ReceMstr B
	WHERE A.PRE_RECE_KEY_NO = B.RECE_KEY_NO
	AND B.BRANCH IN ('01425')
	AND A.PRE_DATE <= '20190531'
	AND B.STATUS = 1
)TA
LEFT JOIN SYS_BranchMstr TB ON TA.BRANCH=TB.BRANCH
LEFT JOIN CM_StudMstr TC ON TA.STUD_NO=TC.STUD_NO
WHERE TA.PRE_AMT-TA.USED_AMT>0
GROUP BY TA.BRANCH,TB.BRANCH_CNAME,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG
ORDER BY TA.BRANCH,TA.STUD_NO");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['剩余金额'] = $dateexcelvar['剩余金额'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","STUD_NO","STUD_CHI","STUD_ENG","LEFT_AMT");
        $excelfileds = array('BRANCH','BRANCH_CNAME','STUD_NO','STUD_CHI','STUD_ENG','剩余金额');
        query_to_excel($excelheader,$outexceldate,$excelfileds,"studentbalance.xlsx");
        exit;

    }


    function ImportStuCourseBalance(){

        $dateexcelarray = $this->SchoolData->findAll("SELECT TA.BRANCH,TB.BRANCH_CNAME
,TA.CLASS_NO,TD.CLASS_CHI,TD.CLASS_ENG
,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG
,TD.CODE_CLASS
,剩余课次=TIMES_TQTY-ISNULL(BACK_TQTY,0)-ISNULL(USED_TQTY,0)
,剩余金额=CR_CASH_AMT-ISNULL(BACK_TAMT,0)-ISNULL(SHARE_TAMT,0)
,退课单价=(CASE WHEN BACK_TQTY=0 THEN '' ELSE ROUND(BACK_TAMT/BACK_TQTY,2)END)
,耗课单价=(CASE WHEN USED_TQTY=0 THEN '' ELSE ROUND(SHARE_TAMT/USED_TQTY,2)END)
FROM
(
SELECT B.BRANCH
,B.CLASS_NO
,B.STUD_NO
,CR_CASH_AMT
,TIMES_TQTY = (SELECT ISNULL(SUM(TIMES_TQTY),0) FROM CM_ReceCr_TimesMstr WHERE CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,BACK_TAMT = (SELECT ISNULL(SUM(WOFF_AMT),0) FROM CM_ReceCr_TimesUsed WHERE WOFF_DATE<='20190531' AND CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,BACK_TQTY = (SELECT ISNULL(SUM(WOFF_TIMES_TQTY),0) FROM CM_ReceCr_TimesUsed WHERE WOFF_DATE<='20190531' AND CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,SHARE_TAMT = (SELECT ISNULL(SUM(SHARE_AMT),0) FROM CM_ReceCr_Share WHERE SHARE_MONTH<='201904' AND  CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,USED_TQTY = (SELECT  COUNT(CR_RECE_KEY_NO) FROM CM_ClassAtte_Stud WHERE DEDUCT_DATE<='20190531' AND CR_RECE_KEY_NO = A.CR_RECE_KEY_NO AND CR_DATA_SEQ =A.CR_DATA_SEQ)
 FROM CM_RECECR A,CM_ReceMstr B
 WHERE A.CR_RECE_KEY_NO = B.RECE_KEY_NO
 AND B.STATUS = 1
 AND B.BRANCH IN ('01425')
 AND B.RECE_END_DATE <= '20190531'
 AND A.CR_CODE_ACCT_GROUP = 'RECE'
 AND A.CODE_CHARGE='TIMES'
 --AND B.STUD_NO='2015102000420'
) AS TA
LEFT JOIN SYS_BranchMstr TB ON TA.BRANCH=TB.BRANCH
LEFT JOIN CM_StudMstr TC ON TA.STUD_NO=TC.STUD_NO
LEFT JOIN CM_ClassMstr TD ON TA.CLASS_NO=TD.CLASS_NO
WHERE CR_CASH_AMT > ISNULL(BACK_TAMT,0) + ISNULL(SHARE_TAMT,0)
ORDER BY TA.BRANCH,TA.CLASS_NO,TA.STUD_NO");
        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['CODE_CLASS'] = $dateexcelvar['CODE_CLASS'];
                $datearray['剩余课次'] = $dateexcelvar['剩余课次'];
                $datearray['剩余金额'] = $dateexcelvar['剩余金额'];
                $datearray['退课单价'] = $dateexcelvar['退课单价'];
                $datearray['耗课单价'] = $dateexcelvar['耗课单价'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","STUD_NO","STUD_CHI","STUD_ENG","课程别","剩余课次","剩余金额","退课单价","耗课单价");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG','STUD_NO',"STUD_CHI","STUD_ENG","CODE_CLASS","剩余课次","剩余金额","退课单价","耗课单价");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"stucoursebalance.xlsx");
        exit;

    }

    function ImportMangeprice(){

        $dateexcelarray = $this->SchoolData->findAll("SELECT TA.BRANCH,TB.BRANCH_CNAME
,TA.CLASS_NO,TD.CLASS_CHI,TD.CLASS_ENG
,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG
,TD.CODE_CLASS
,费用月份=CR_CODE_MONTH
,剩余金额=CR_CASH_AMT-ISNULL(BACK_TAMT,0)-ISNULL(SHARE_TAMT,0)
,金额类型=CODE_CHARGE
FROM
(
SELECT B.BRANCH
,B.CLASS_NO
,B.STUD_NO
,A.CR_CODE_MONTH
,A.CODE_CHARGE
,CR_CASH_AMT
,BACK_TAMT = (SELECT ISNULL(SUM(WOFF_AMT),0) FROM CM_RECEWOFF WHERE WOFF_DATE<='20190531' AND DR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND DR_DATA_SEQ = A.CR_DATA_SEQ)
,SHARE_TAMT = (SELECT ISNULL(SUM(SHARE_AMT),0) FROM CM_ReceCr_Share WHERE SHARE_MONTH<='201904' AND  CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
 FROM CM_RECECR A,CM_ReceMstr B
 WHERE A.CR_RECE_KEY_NO = B.RECE_KEY_NO
 AND B.STATUS = 1
 AND B.BRANCH IN ('01425')
 AND B.RECE_END_DATE <= '20190531'
 AND A.CR_CODE_ACCT_GROUP = 'RECE'
 AND A.CODE_CHARGE<>'TIMES'
) AS TA
LEFT JOIN SYS_BranchMstr TB ON TA.BRANCH=TB.BRANCH
LEFT JOIN CM_StudMstr TC ON TA.STUD_NO=TC.STUD_NO
LEFT JOIN CM_ClassMstr TD ON TA.CLASS_NO=TD.CLASS_NO
WHERE CR_CASH_AMT > ISNULL(BACK_TAMT,0) + ISNULL(SHARE_TAMT,0)
ORDER BY TA.BRANCH,TA.CLASS_NO,TA.STUD_NO,CR_CODE_MONTH");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['CODE_CLASS'] = $dateexcelvar['CODE_CLASS'];
                $datearray['MONTH'] = $dateexcelvar['费用月份'];
                $datearray['PRICE'] = $dateexcelvar['剩余金额'];
                $datearray['CLASSTYPE'] = $dateexcelvar['金额类型'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","STUD_NO","CODE_CLASS","MONTH","PRICE","CLASSTYPE");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','STUD_NO','CODE_CLASS','MONTH',"PRICE","CLASSTYPE");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"stumangeprice.xlsx");
        exit;

    }

    function ImportClass(){

        $dateexcelarray = $this->SchoolData->findAll("SELECT B.BRANCH,B.BRANCH_CNAME
,A.CLASS_NO,A.CLASS_CHI,A.CLASS_ENG
,A.CODE_CLASS  AS 课程别代码,C.CLASS_DESC AS 课程别名称
,A.ST_DATE,A.END_DATE
FROM CM_ClassMstr A
LEFT JOIN SYS_BRANCHMSTR B ON A.BRANCH=B.BRANCH
LEFT JOIN Code_Class C ON A.CODE_CLASS=C.CODE_CLASS
WHERE A.STATUS=1 AND A.CLASS_NUM>0
AND A.END_DATE>='20190531'
AND A.ST_DATE<='20190531'
AND B.BRANCH IN ('01425')
ORDER BY A.BRANCH,A.CLASS_NO");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['课程别代码'] = $dateexcelvar['课程别代码'];
                $datearray['课程别名称'] = $dateexcelvar['课程别名称'];
                $datearray['ST_DATE'] = $dateexcelvar['ST_DATE'];
                $datearray['END_DATE'] = $dateexcelvar['END_DATE'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","课程别代码","课程别名称","ST_DATE","END_DATE");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG','课程别代码',"课程别名称","ST_DATE","END_DATE");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"iptclass.xlsx");
        exit;

    }

    function ImportStudy(){
        $dateexcelarray = $this->SchoolData->findAll("SELECT BRANCH,BRANCH_CNAME
,CLASS_NO,CLASS_CHI,CLASS_ENG
,STUD_NO,STUD_CHI,STUD_ENG
,入班日期 FROM (
select B.BRANCH,B.BRANCH_CNAME
,S.CLASS_NO,A.CLASS_CHI,A.CLASS_ENG
,S.STUD_NO,C.STUD_CHI,C.STUD_ENG
,入班日期=(SELECT TOP 1 CHANGE_DATE FROM CM_Stud_ChangeDetl WHERE CLASS_NO=S.CLASS_NO AND STUD_NO=S.STUD_NO)
,状态=(SELECT CODE_CHANGE_STUD FROM DBO.Get_Stud_ClassStatus(A.BRANCH,S.CLASS_NO,S.STUD_NO,'20190531'))
FROM VIEW_CM_StudClass S
LEFT JOIN CM_ClassMstr A ON S.CLASS_NO=A.CLASS_NO
LEFT JOIN SYS_BRANCHMSTR B ON A.BRANCH=B.BRANCH
LEFT JOIN CM_STUDMSTR C ON S.STUD_NO=C.STUD_NO
WHERE A.STATUS=1 AND A.CLASS_NUM>0
AND A.END_DATE>='20190531' AND A.ST_DATE<='20190531'
AND B.BRANCH IN ('01425')
)TA
WHERE 状态 NOT IN(SELECT CODE_CHANGE_STUD FROM Code_Change_Stud WHERE ENABLE_CLASS='Y' AND ISNULL(CLASS_LOCK,'')='Y')
ORDER BY BRANCH, CLASS_NO,STUD_NO");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['入班日期'] = $dateexcelvar['入班日期'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","STUD_NO","STUD_CHI","STUD_ENG","入班日期");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG','STUD_NO',"STUD_CHI","STUD_ENG","入班日期");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"studentstudy.xlsx");
        exit;
    }


    function ImportStuArrears(){
        $dateexcelarray = $this->SchoolData->findAll("Select BRANCH,BRANCH_CNAME,CLASS_NO,CLASS_CHI, CLASS_ENG, CODE_CLASS,STUD_NO, STUD_CHI, STUD_ENG, CODE_CHARGE,
欠费课次 = (ATTE_COUNT - ISNULL(TIMES_QTY ,0) ),欠费金额=0
From (
Select B.BRANCH,F.BRANCH_CNAME,B.CLASS_NO,B.CLASS_CHI,B.CLASS_ENG,C.STUD_NO ,D.STUD_CHI ,D.STUD_ENG,A.CODE_CHARGE,B.CODE_CLASS,
BRANCH_FEE_AMT = (Select Max(TIMES_TAMT) From CM_BranchFees_Times Where CODE_CLASS = A.CODE_CLASS AND FEE_NAME=A.FEE_NAME) ,
B.CLASS_NUM ,
E.RECE_AMT ,
E.TIMES_QTY ,
ATTE_COUNT = (Select Count(*) From CM_ClassDesign_Time F  Where CLASS_NO = B.CLASS_NO  And FREE_YN = 'N'  And ACTV_YN = 'Y' And LESN_DATE < '20190600'
And IsNull((Select CLASS_LOCK From dbo.Get_Stud_ClassStatus(B.BRANCH,B.CLASS_NO,C.STUD_NO,F.LESN_DATE)),'') <> 'Y'
And IsNull((Select CHANGE_DATE From dbo.Get_Stud_ClassStatus(B.BRANCH,B.CLASS_NO,C.STUD_NO,F.LESN_DATE)),'') <> '' )
From CM_BranchFees A
Inner Join CM_ClassMstr B  On A.CODE_CLASS = B.CODE_CLASS
Inner Join CM_StudClass C  On C.CLASS_NO = B.CLASS_NO
Inner Join CM_StudMstr D  On C.STUD_NO = D.STUD_NO
Left Join ( Select D.CLASS_NO,D.STUD_NO,CODE_CHARGE,RECE_AMT = SUM(D.TIMES_TAMT-D.TIMES_BACK_TAMT),TIMES_QTY = SUM(D.TIMES_TQTY-D.TIMES_BACK_TQTY)
			From CM_ReceCr A
			LEFT JOIN VIEW_CM_ReceCr_TimesMstr AS D ON A.CR_RECE_KEY_NO = D.CR_RECE_KEY_NO AND A.CR_DATA_SEQ = D.CR_DATA_SEQ
			Where D.BRANCH  IN ('01425')
			AND D.CR_RECE_DATE<'20190600' And IsNull(D.CLASS_NO,'') <> ''And A.CODE_CHARGE = 'Times'
			Group By D.CLASS_NO,D.STUD_NO,CODE_CHARGE
) E  On C.CLASS_NO = E.CLASS_NO And C.STUD_NO = E.STUD_NO  And A.CODE_CHARGE = E.CODE_CHARGE
LEFT JOIN SYS_BranchMstr F ON B.BRANCH=F.BRANCH
Where A.MUST_BUY = 'Y'
AND EXISTS (Select 1 from CM_BranchFees_Branch where FEE_NAME=A.FEE_NAME and CODE_CLASS=A.CODE_CLASS and BRANCH=B.BRANCH)
And A.CODE_CHARGE = 'Times'
AND NOT EXISTS(SELECT 1 FROM CM_UnRece_Audit WHERE CLASS_NO = C.CLASS_NO AND STUD_NO = D.STUD_NO AND FEE_NAME = A.FEE_NAME AND ISNULL(CODE_CHARGE,'') = ISNULL(A.CODE_CHARGE,''))
And EXISTS(Select 1 From Code_Class Where CODE_CLASS = B.CODE_CLASS And CLASS_DEFAULT_NUM > 0)
And B.BRANCH  IN ('01425')
And B.ST_DATE <= '20190600'
And B.END_DATE >= '20180100'
) TA  Where ATTE_COUNT > ISNULL(TIMES_QTY  ,0)

Union

Select BRANCH,BRANCH_CNAME, CLASS_NO,CLASS_CHI, CLASS_ENG,CODE_CLASS, STUD_NO, STUD_CHI, STUD_ENG,CODE_CHARGE,
欠费课次 =0,欠费金额=BRANCH_FEE_AMT
From (
Select B.BRANCH,F.BRANCH_CNAME,A.FEE_NAME,A.CODE_CHARGE ,B.CLASS_NO,B.CLASS_CHI ,B.CLASS_ENG,C.STUD_NO ,D.STUD_CHI ,D.STUD_ENG,B.CODE_CLASS,
BRANCH_FEE_AMT = (Select Max(FEE_AMT) From CM_BranchFees Where CODE_CLASS = A.CODE_CLASS And CODE_CHARGE = A.CODE_CHARGE AND FEE_NAME=A.FEE_NAME),
CHANGE_STUD_DESC = (Select CHANGE_STUD_DESC From CODE_CHANGE_STUD Where CODE_CHANGE_STUD = (Select CODE_CHANGE_STUD From dbo.Get_Stud_ClassStatus(B.BRANCH, B.CLASS_NO, C.STUD_NO, B.END_DATE)))
From CM_BranchFees A
Inner Join CM_ClassMstr B  On A.CODE_CLASS = B.CODE_CLASS
Inner Join CM_StudClass C  On C.CLASS_NO = B.CLASS_NO
Inner Join CM_StudMstr D  On C.STUD_NO = D.STUD_NO
LEFT JOIN SYS_BranchMstr F ON B.BRANCH=F.BRANCH
Where A.MUST_BUY = 'Y'
AND EXISTS (Select 1 from CM_BranchFees_Branch where FEE_NAME=A.FEE_NAME and CODE_CLASS=A.CODE_CLASS and BRANCH=B.BRANCH)
And A.CODE_CHARGE = 'Sales'
AND NOT EXISTS(SELECT 1 FROM CM_UnRece_Audit WHERE CLASS_NO = C.CLASS_NO AND STUD_NO = D.STUD_NO AND FEE_NAME = A.FEE_NAME AND ISNULL(CODE_CHARGE,'') = ISNULL(A.CODE_CHARGE,''))
And EXISTS(Select 1 From Code_Class Where CODE_CLASS = B.CODE_CLASS And CLASS_DEFAULT_NUM > 0)
And B.BRANCH  IN ('01425')
And B.ST_DATE <= '20190600'
And B.END_DATE >= '20180100'
And Not Exists (Select CODE_CHARGE From CM_ReceCr  Where CODE_CHARGE = A.CODE_CHARGE
And Exists (Select RECE_KEY_NO From CM_ReceMstr  Where RECE_KEY_NO = CM_ReceCr.CR_RECE_KEY_NO  And BRANCH = B.BRANCH  And CLASS_NO = B.CLASS_NO  And STUD_NO = D.STUD_NO ))
) TA
WHERE dbo.FUN_Get_LastStudChange (CHANGE_STUD_DESC,STUD_NO,BRANCH,CLASS_NO )='Y'

Union

Select BRANCH,BRANCH_CNAME,CLASS_NO,CLASS_CHI,CLASS_ENG,CODE_CLASS, STUD_NO, STUD_CHI, STUD_ENG,  CODE_CHARGE,
欠费课次 =0,欠费金额=BRANCH_FEE_AMT
From (
Select b.branch,f.branch_cname,A.FEE_NAME,A.CODE_CHARGE ,B.CLASS_NO,B.CLASS_CHI ,C.STUD_NO ,D.STUD_CHI ,D.STUD_ENG ,B.CLASS_ENG,B.CODE_CLASS,
BRANCH_FEE_AMT = (Select Max(FEE_AMT) From CM_BranchFees Where CODE_CLASS = A.CODE_CLASS And CODE_CHARGE = A.CODE_CHARGE  AND FEE_NAME=A.FEE_NAME)
From CM_BranchFees A
Inner Join CM_ClassMstr B  On A.CODE_CLASS = B.CODE_CLASS
Inner Join CM_StudClass C  On C.CLASS_NO = B.CLASS_NO
Inner Join CM_StudMstr D  On C.STUD_NO = D.STUD_NO
LEFT JOIN SYS_BranchMstr F ON B.BRANCH=F.BRANCH
Where A.MUST_BUY = 'Y'
AND EXISTS (Select 1 from CM_BranchFees_Branch where FEE_NAME=A.FEE_NAME and CODE_CLASS=A.CODE_CLASS and BRANCH=B.BRANCH)
And A.CODE_CHARGE = 'Manage'
AND NOT EXISTS(SELECT 1 FROM CM_UnRece_Audit WHERE CLASS_NO = C.CLASS_NO AND STUD_NO = D.STUD_NO AND FEE_NAME = A.FEE_NAME AND ISNULL(CODE_CHARGE,'') = ISNULL(A.CODE_CHARGE,''))
And B.BRANCH  IN ('01425')
And B.ST_DATE <= '20190600'
And B.END_DATE >= '20180100'
And Not Exists (Select CODE_CHARGE From CM_ReceCr  Where CODE_CHARGE = A.CODE_CHARGE
And Exists (Select RECE_KEY_NO From CM_ReceMstr  Where RECE_KEY_NO = CM_ReceCr.CR_RECE_KEY_NO  And BRANCH = B.BRANCH  And CLASS_NO = B.CLASS_NO  And STUD_NO = D.STUD_NO ))
) TA
Order By BRANCH,CLASS_NO, STUD_NO
");


        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['课程别'] = $dateexcelvar['CODE_CLASS'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['欠费类别'] = $dateexcelvar['CODE_CHARGE'];
                $datearray['欠费课次'] = $dateexcelvar['欠费课次'];
                $datearray['欠费金额'] = $dateexcelvar['欠费金额'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","课程别","STUD_NO","STUD_CHI","STUD_ENG","欠费类别","欠费课次","欠费金额");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG',"课程别",'STUD_NO',"STUD_CHI","STUD_ENG","欠费类别","欠费课次","欠费金额");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"stuarrears.xlsx");
        exit;

    }



    function GuideClass(){
        $dateexcelarray = $this->SchoolData->findAll("SELECT B.BRANCH,B.BRANCH_CNAME
,A.CLASS_NO,A.CLASS_CHI,A.CLASS_ENG
,A.CODE_CLASS  AS 课程别代码,C.CLASS_DESC AS 课程别名称
,A.ST_DATE,A.END_DATE
FROM CM_ClassMstr A
LEFT JOIN SYS_BRANCHMSTR B ON A.BRANCH=B.BRANCH
LEFT JOIN Code_Class C ON A.CODE_CLASS=C.CODE_CLASS
WHERE A.STATUS=1
AND A.END_DATE>='20190531'
AND A.ST_DATE<='20190531'
AND B.BRANCH IN ('01425')
ORDER BY A.BRANCH,A.CLASS_NO");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['课程别代码'] = $dateexcelvar['课程别代码'];
                $datearray['课程别名称'] = $dateexcelvar['课程别名称'];
                $datearray['ST_DATE'] = $dateexcelvar['ST_DATE'];
                $datearray['END_DATE'] = $dateexcelvar['END_DATE'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","课程别代码","课程别名称","ST_DATE","END_DATE");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG','课程别代码',"课程别名称","ST_DATE","END_DATE");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"iptclass.xlsx");
        exit;

    }

    function GuideStudy(){
        $dateexcelarray = $this->SchoolData->findAll("SELECT BRANCH,BRANCH_CNAME
,CLASS_NO,CLASS_CHI,CLASS_ENG
,STUD_NO,STUD_CHI,STUD_ENG
,入班日期 FROM (
select B.BRANCH,B.BRANCH_CNAME
,S.CLASS_NO,A.CLASS_CHI,A.CLASS_ENG
,S.STUD_NO,C.STUD_CHI,C.STUD_ENG
,入班日期=(SELECT TOP 1 CHANGE_DATE FROM CM_Stud_ChangeDetl WHERE CLASS_NO=S.CLASS_NO AND STUD_NO=S.STUD_NO)
,状态=(SELECT CODE_CHANGE_STUD FROM DBO.Get_Stud_ClassStatus(A.BRANCH,S.CLASS_NO,S.STUD_NO,'20190531'))
FROM VIEW_CM_StudClass S
LEFT JOIN CM_ClassMstr A ON S.CLASS_NO=A.CLASS_NO
LEFT JOIN SYS_BRANCHMSTR B ON A.BRANCH=B.BRANCH
LEFT JOIN CM_STUDMSTR C ON S.STUD_NO=C.STUD_NO
WHERE A.STATUS=1 AND A.CLASS_NUM>0
AND A.END_DATE>='20190531' --AND A.ST_DATE<='20190531'
AND B.BRANCH IN ('01425')
)TA
WHERE 状态 NOT IN(SELECT CODE_CHANGE_STUD FROM Code_Change_Stud WHERE ENABLE_CLASS='Y' AND ISNULL(CLASS_LOCK,'')='Y')
ORDER BY BRANCH, CLASS_NO,STUD_NO");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['入班日期'] = $dateexcelvar['入班日期'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","STUD_NO","STUD_CHI","STUD_ENG","入班日期");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG','STUD_NO',"STUD_CHI","STUD_ENG","入班日期");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"studentstudy.xlsx");
        exit;
    }


    function GuideStuCourseBalance(){

        $dateexcelarray = $this->SchoolData->findAll("SELECT TA.BRANCH,TB.BRANCH_CNAME
,TA.CLASS_NO,TD.CLASS_CHI,TD.CLASS_ENG
,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG
,TD.CODE_CLASS,ta.code_charge
,剩余课次=TIMES_TQTY-ISNULL(BACK_TQTY,0)-ISNULL(USED_TQTY,0)
,剩余金额=CR_CASH_AMT-ISNULL(BACK_TAMT,0)-ISNULL(SHARE_TAMT,0)
--,退课单价=(CASE WHEN BACK_TQTY=0 THEN '' ELSE ROUND(BACK_TAMT/BACK_TQTY,2)END)
,耗课单价=(CASE WHEN USED_TQTY=0 THEN '' ELSE ROUND(SHARE_TAMT/USED_TQTY,2)END)
FROM
(
SELECT B.BRANCH
,B.CLASS_NO
,B.STUD_NO
,CR_CASH_AMT,a.code_charge
,TIMES_TQTY = (SELECT ISNULL(SUM(TIMES_TQTY),0) FROM CM_ReceCr_TimesMstr WHERE CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,BACK_TAMT = (SELECT ISNULL(SUM(WOFF_AMT),0) FROM CM_ReceCr_TimesUsed WHERE WOFF_DATE<='20190531' AND CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,BACK_TQTY = (SELECT ISNULL(SUM(WOFF_TIMES_TQTY),0) FROM CM_ReceCr_TimesUsed WHERE WOFF_DATE<='20190531' AND CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,SHARE_TAMT = (SELECT ISNULL(SUM(SHARE_AMT),0) FROM CM_ReceCr_Share WHERE SHARE_MONTH<='201905' AND  CR_RECE_KEY_NO=A.CR_RECE_KEY_NO AND CR_DATA_SEQ = A.CR_DATA_SEQ)
,USED_TQTY = (SELECT  COUNT(CR_RECE_KEY_NO) FROM CM_ClassAtte_Stud WHERE DEDUCT_DATE<='20190531' AND CR_RECE_KEY_NO = A.CR_RECE_KEY_NO AND CR_DATA_SEQ =A.CR_DATA_SEQ)
 FROM CM_RECECR A,CM_ReceMstr B
 WHERE A.CR_RECE_KEY_NO = B.RECE_KEY_NO
 AND B.STATUS = 1
 AND B.BRANCH IN  ('01425')
 AND B.RECE_END_DATE <= '20190531'
 AND A.CR_CODE_ACCT_GROUP = 'RECE'
 AND A.CODE_CHARGE='TIMES'
 --AND B.STUD_NO='2015102000420'
) AS TA
LEFT JOIN SYS_BranchMstr TB ON TA.BRANCH=TB.BRANCH
LEFT JOIN CM_StudMstr TC ON TA.STUD_NO=TC.STUD_NO
LEFT JOIN CM_ClassMstr TD ON TA.CLASS_NO=TD.CLASS_NO
WHERE CR_CASH_AMT > ISNULL(BACK_TAMT,0) + ISNULL(SHARE_TAMT,0)
ORDER BY TA.BRANCH,TA.CLASS_NO,TA.STUD_NO");
        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CLASS_ENG'] = $dateexcelvar['CLASS_ENG'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['CODE_CLASS'] = $dateexcelvar['CODE_CLASS'];
                $datearray['剩余课次'] = $dateexcelvar['剩余课次'];
                $datearray['剩余金额'] = $dateexcelvar['剩余金额'];
                $datearray['退课单价'] = $dateexcelvar['退课单价'];
                $datearray['耗课单价'] = $dateexcelvar['耗课单价'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CLASS_ENG","STUD_NO","STUD_CHI","STUD_ENG","课程别","剩余课次","剩余金额","退课单价","耗课单价");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO','CLASS_CHI','CLASS_ENG','STUD_NO',"STUD_CHI","STUD_ENG","CODE_CLASS","剩余课次","剩余金额","退课单价","耗课单价");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"stucoursebalance.xlsx");
        exit;

    }


    function CourseCatBalance(){
        $dateexcelarray = $this->SchoolData->findAll("SELECT TA.BRANCH,TB.BRANCH_CNAME,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG,ta.code_Charge,ta.code_Cat
,剩余金额=SUM(TA.PRE_AMT-TA.USED_AMT)
,剩余课次=SUM(PRE_TIMES-USED_TIMES)
FROM
(
 SELECT B.BRANCH,B.STUD_NO
 ,PRE_AMT,code_Charge,a.code_Cat
 ,USED_AMT = (SELECT ISNULL(SUM(WOFF_AMT),0) FROM CM_ReceCr_PreUsed WHERE PRE_RECE_KEY_NO = A.PRE_RECE_KEY_NO AND PRE_DATA_SEQ = A.PRE_DATA_SEQ AND WOFF_DATE <= '20190531')
 ,PRE_TIMES
 ,USED_TIMES = (SELECT ISNULL(SUM(WOFF_TIMES),0) FROM CM_ReceCr_PreUsed WHERE PRE_RECE_KEY_NO = A.PRE_RECE_KEY_NO AND PRE_DATA_SEQ = A.PRE_DATA_SEQ AND WOFF_DATE <= '20190531')
 FROM CM_ReceCr_PreMstr A,CM_ReceMstr B
 WHERE A.PRE_RECE_KEY_NO = B.RECE_KEY_NO
 AND B.BRANCH IN ('01425')
 AND A.PRE_DATE <= '20190531'
 --AND A.CODE_ACCT NOT IN('2203','220312','220391','220393')
 AND B.STATUS = 1
)TA
LEFT JOIN SYS_BranchMstr TB ON TA.BRANCH=TB.BRANCH
LEFT JOIN CM_StudMstr TC ON TA.STUD_NO=TC.STUD_NO
WHERE TA.PRE_AMT-TA.USED_AMT>0
GROUP BY TA.BRANCH,TB.BRANCH_CNAME,TA.STUD_NO,TC.STUD_CHI,TC.STUD_ENG ,ta.code_Charge,ta.code_Cat
ORDER BY TA.BRANCH,TA.STUD_NO");
        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['STUD_ENG'] = $dateexcelvar['STUD_ENG'];
                $datearray['code_Charge'] = $dateexcelvar['code_Charge'];
                $datearray['code_Cat'] = $dateexcelvar['code_Cat'];
                $datearray['剩余金额'] = $dateexcelvar['剩余金额'];
                $datearray['剩余课次'] = $dateexcelvar['剩余课次'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","STUD_NO","STUD_CHI","STUD_ENG","code_Charge","code_Cat","剩余金额","剩余课次");
        $excelfileds = array('BRANCH','BRANCH_CNAME','STUD_NO',"STUD_CHI","STUD_ENG","code_Charge","code_Cat","剩余金额","剩余课次");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"stucatbalance.xlsx");
        exit;
    }

    function GuideStuChange(){

        $dateexcelarray = $this->SchoolData->findAll("SELECT a.change_key_no,A.STUD_NO,STUD_CHI=(SELECT STUD_CHI FROM CM_STUDMSTR WHERE STUD_NO =A.STUD_NO)
,A.BRANCH,BRANCH_CNAME=(SELECT BRANCH_CNAME FROM SYS_BRANCHMSTR WHERE BRANCH=A.BRANCH)
,A.CLASS_NO,CLASS_CHI=(SELECT CLASS_CHI FROM CM_CLASSMSTR WHERE CLASS_NO=A.CLASS_NO),CODE_CLASS=(SELECT CODE_cLASS FROM CM_CLASSMSTR WHERE CLASS_NO=A.CLASS_NO)
,A.CHANGE_dATE,A.CODE_CHANGE_STUD,DESC_cHANGE_STUD=(SELECT CHANGE_STUD_dESC FROM CODE_CHANGE_STUD WHERE CODE_CHANGE_STUD=A.CODE_CHANGE_STUD)
FROM CM_STUD_CHANGEDETL A
LEFT JOIN CM_STUD_CHANGEMSTR B ON A.CHANGE_KEY_NO=B.CHANGE_KEY_NO
WHERE A.STUD_NO IN(
SELECT STUD_NO FROM (SELECT S.STUD_NO,
状态=(SELECT CODE_CHANGE_STUD FROM DBO.Get_Stud_ClassStatus(A.BRANCH,S.CLASS_NO,S.STUD_NO,'20190531'))
FROM VIEW_CM_StudClass S
LEFT JOIN CM_ClassMstr A ON S.CLASS_NO=A.CLASS_NO
LEFT JOIN SYS_BRANCHMSTR B ON A.BRANCH=B.BRANCH
LEFT JOIN CM_STUDMSTR C ON S.STUD_NO=C.STUD_NO
WHERE A.STATUS=1 AND A.CLASS_NUM>0
AND A.END_DATE>='20190531' --AND A.ST_DATE<='20190531'
AND B.BRANCH IN ('01425')
)TA
WHERE 状态 NOT IN(SELECT CODE_CHANGE_STUD FROM Code_Change_Stud WHERE ENABLE_CLASS='Y' AND ISNULL(CLASS_LOCK,'')='Y'))
AND ISNULL(A.CLASS_NO,'') IN(SELECT CLASS_NO FROM (
select S.CLASS_NO
,状态=(SELECT CODE_CHANGE_STUD FROM DBO.Get_Stud_ClassStatus(A.BRANCH,S.CLASS_NO,S.STUD_NO,'20190531'))
FROM VIEW_CM_StudClass S
LEFT JOIN CM_ClassMstr A ON S.CLASS_NO=A.CLASS_NO
LEFT JOIN SYS_BRANCHMSTR B ON A.BRANCH=B.BRANCH
LEFT JOIN CM_STUDMSTR C ON S.STUD_NO=C.STUD_NO
WHERE A.STATUS=1 AND A.CLASS_NUM>0
AND A.END_DATE>='20190531' --AND A.ST_DATE<='20190531'
AND B.BRANCH IN ('01425')
)TA
WHERE 状态 NOT IN(SELECT CODE_CHANGE_STUD FROM Code_Change_Stud WHERE ENABLE_CLASS='Y' AND ISNULL(CLASS_LOCK,'')='Y') UNION SELECT '')
AND A.CODE_CHANGE_STUD NOT IN('E01','E02')
and a.CHANGE_DATE<='20190531'
ORDER BY A.STUD_NO,A.CHANGE_DATE");
        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['BRANCH'] = $dateexcelvar['BRANCH'];
                $datearray['BRANCH_CNAME'] = $dateexcelvar['BRANCH_CNAME'];
                $datearray['CLASS_NO'] = $dateexcelvar['CLASS_NO'];
                $datearray['CLASS_CHI'] = $dateexcelvar['CLASS_CHI'];
                $datearray['CODE_CLASS'] = $dateexcelvar['CODE_CLASS'];
                $datearray['change_key_no'] = $dateexcelvar['change_key_no'];
                $datearray['STUD_NO'] = $dateexcelvar['STUD_NO'];
                $datearray['STUD_CHI'] = $dateexcelvar['STUD_CHI'];
                $datearray['CHANGE_dATE'] = $dateexcelvar['CHANGE_dATE'];//异动时间
                $datearray['CODE_CHANGE_STUD'] = $dateexcelvar['CODE_CHANGE_STUD'];//异动编号
                $datearray['DESC_cHANGE_STUD'] = $dateexcelvar['DESC_cHANGE_STUD'];//异动编号名称
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("BRANCH","BRANCH_CNAME","CLASS_NO","CLASS_CHI","CODE_CLASS","change_key_no","STUD_NO","STUD_CHI","CHANGE_dATE","异动编号","异动编号名称");
        $excelfileds = array('BRANCH','BRANCH_CNAME','CLASS_NO',"CLASS_CHI","CODE_CLASS","change_key_no","STUD_NO","STUD_CHI","CHANGE_dATE","CODE_CHANGE_STUD","DESC_cHANGE_STUD");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"stuchange.xlsx");
        exit;

    }

    function GuideStuCatBalance(){
        $dateexcelarray=$this->DataControl->selectClear("select es.*,s.student_cnname from smc_excel_simulation as es left join smc_student as s on s.student_branch=es.student_branch order by es.student_branch asc");
        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                $datearray['course_branch'] = $dateexcelvar['course_branch'];
                $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                $datearray['coursecatbalance_unitexpend'] = $dateexcelvar['coursecatbalance_unitexpend'];
                $datearray['coursecatbalance_unitrefund'] = $dateexcelvar['coursecatbalance_unitrefund'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("学员名","学员编号","班种编号","课程别","剩余课程金额","剩余课程次数","购买单价","退费单价");
        $excelfileds = array("student_cnname",'student_branch','coursecat_branch','course_branch',"coursebalance_figure","coursebalance_time","coursecatbalance_unitexpend","coursecatbalance_unitrefund");
        query_to_excel($excelheader,$outexceldate,$excelfileds,"模拟学员预收表.xlsx");
    }


    function simulationAdvance($student_branch,$coursecat_branch,$day){
        $dateexcelarray = $this->SchoolData->findOne("select top 1 CODE_CLASS
,TIMES_TQTY=(select sum(TIMES_TQTY) from CM_ReceCr_TimesMstr where class_no=ta.class_no)
from (
select b.CODE_CLASS ,a.class_no
,in_date=(select min(change_date) from CM_Stud_ChangeDetl where CLASS_NO=a.CLASS_NO and stud_no=a.STUD_NO)
from VIEW_CM_StudClass a
left join cm_classmstr b on a.class_no=b.CLASS_NO
left join Code_Class c on b.code_class=c.CODE_CLASS
where b.status=1
and c.CODE_CAT='{$coursecat_branch}'
and a.STUD_NO='{$student_branch}'
)ta
where in_date<='{$day}'
order by in_date desc");

        return $dateexcelarray;
    }



}