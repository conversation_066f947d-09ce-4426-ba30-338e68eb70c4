<?php

namespace Model\Crm;

class StudentActionModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证操作人信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->marketer_id = $marketer_id;
        }
    }

    /**
     * 分配记录,招募负责人表
     * author: ling
     * 对应接口文档 0001
     * @param $student_id
     * @param $principal_marketer_id
     * @param int $ismajor
     */
     function allotStudentAction($student_id, $principal_marketer_id, $ismajor = 0)
    {
        //判断有没有其他学校的负责人
        if($this->DataControl->selectOne("select principal_id from crm_student_principal where student_id = '{$student_id}' and school_id <> '{$this->school_id}' and principal_leave = '0' ")){
            $upData = array();
            $upData['principal_leave'] = 1;
            $upData['principal_updatatime'] = time();
            $this->DataControl->updateData("crm_student_principal", " student_id = '{$student_id}' and school_id <> '{$this->school_id}' and principal_leave = '0' ", $upData);
        }

        $allotData = array();
        $allotData['school_id'] = $this->school_id;
        $allotData['student_id'] = $student_id;
        $allotData['marketer_id'] = $this->marketer_id;
        $allotData['allot_marketer_id'] = $principal_marketer_id;
        $allotData['allotlog_status'] = 1;
        $allotData['allotlog_ismajor'] = $ismajor;
        $allotData['allotlog_createtime'] = time();
        $allotData['allotlog_note'] = "";
        $this->DataControl->insertData("crm_student_allotlog", $allotData);

        $principalData = array();
        $principalData['student_id'] = $student_id;
        $principalData['marketer_id'] = $principal_marketer_id;
        $principalData['school_id'] = $this->school_id;
        $principalData['principal_ismajor'] = $ismajor;
        $principalData['principal_createtime'] = time();
        $this->DataControl->insertData('crm_student_principal', $principalData);
        return true;
    }

    /**
     * 创建跟踪记录
     * author: ling
     * 对应接口文档 0001
     */
     function insertStudentTrackAction($student_id, $marketer_id = 0, $paramArray = array(),$from_school_id=0)
    {
        if($from_school_id > 0){//针对系统自动操作 非本校的数据
            $this->school_id = $from_school_id;
        }
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$marketer_id}'");
        $trackData = array();
        $trackData['student_id'] = $student_id;
        $trackData['school_id'] = $this->school_id;
        $trackData['marketer_id'] = $marketer_id;
        $trackData['marketer_name'] = $marketerOne['marketer_name'];
        $trackData['track_validinc'] = $paramArray['track_validinc'];
        $trackData['object_code'] = $paramArray['object_code'];
        $trackData['coursetype_id'] = $paramArray['coursetype_id'];
        $trackData['coursecat_id'] = $paramArray['coursecat_id'];
        $trackData['track_intention_level'] = $paramArray['track_intention_level'];
        $trackData['track_linktype'] = $paramArray['track_linktype'];
        $trackData['track_followmode'] = $paramArray['track_followmode'];
        $trackData['track_followuptype'] = $paramArray['track_followuptype'];
        $trackData['track_followuptime'] = $paramArray['track_followuptime'];
        $trackData['track_visitingtime'] = $paramArray['track_visitingtime'];
        $trackData['track_note'] = $paramArray['track_note'];
        $trackData['track_state'] = $paramArray['track_state'];
        $trackData['track_isschoolread'] = $paramArray['track_isschoolread'];
        $trackData['track_initiative'] = $paramArray['track_initiative'];
        $trackData['track_isactive'] = $paramArray['track_isactive'];
        $trackData['track_createtime'] = time();
        if ($track_id = $this->DataControl->insertData('crm_student_track', $trackData)) {
            if ($paramArray['track_isactive'] == 1) {
                $tracestatus = $this->DataControl->getFieldOne("crm_student", "student_tracestatus", "student_id='{$student_id}'");

                $studentData = array();
                if (isset($paramArray['track_intention_level']) && $paramArray['track_intention_level'] !== '') {
                    $studentData['student_intention_level'] = $paramArray['track_intention_level'];
                }

                if ($paramArray['invite_genre'] == '2' || $paramArray['invite_genre'] == '3') {
                    $studentData['student_tracestatus'] = 2;
                }else{
                    if ($tracestatus['student_tracestatus'] == 2 || $tracestatus['student_tracestatus'] == 3) {
                        $studentData['student_tracestatus'] = $tracestatus['student_tracestatus'];
                    } else {
                        if ($paramArray['track_followmode'] == 0 || $paramArray['track_followmode'] == 1 || $paramArray['track_followmode'] == 2 || $paramArray['track_followmode'] == 4) {
                            $studentData['student_tracestatus'] = 1;
                        }
                    }
//                    elseif ($paramArray['track_followmode'] == 1) {
//                        $studentData['student_tracestatus'] = 1;
//                    } elseif ($paramArray['track_followmode'] == 2) {
//                        $studentData['student_tracestatus'] = 3;
//                    } elseif ($paramArray['track_followmode'] == 4) {
//                        $studentData['student_tracestatus'] = 1;
//                    }
                }
                $this->DataControl->updateData("crm_student", "student_id='{$student_id}'", $studentData);
            }
            return $track_id;
        } else {
            return 0;
        }
    }

    /**
     *  取消负责记录,并更新student状态
     * author: ling
     * 对应接口文档 0001
     * @param $student_id
     * @param int $marketer_id
     * @param array $paramArray
     */
     function CancelStuTrackAction($student_id,$from_school_id=0)
    {
        if($from_school_id > 0){//针对系统自动操作 非本校的数据
            $this->school_id = $from_school_id;
        }
        $studentData = array();
        $studentData['student_tracestatus'] = '0';
        $studentData['student_distributionstatus'] = '0';
        $this->DataControl->updateData("crm_student", "student_id='{$student_id}'", $studentData);

        $principalData = array();
        $principalData['principal_leave'] = 1;
        $principalData['principal_updatatime'] = time();
        $this->DataControl->updateData("crm_student_principal", "student_id='{$student_id}' and principal_leave =0 and school_id='{$this->school_id}' ", $principalData);

        $allotData = array();
        $allotData['allotlog_status'] = 0;
        $allotData['allotlog_removetime'] = time();
        $this->DataControl->updateData('crm_student_allotlog', "student_id='{$student_id}' and allotlog_status =1 and school_id='{$this->school_id}'", $allotData);
    }


}

