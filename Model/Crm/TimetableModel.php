<?php


namespace Model\Crm;

class TimetableModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $isGmcPost = null;
    public $company_istrackcoursetype = 0;
    public $company_ismusttrackcoursetype = 0;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'], $publicarray['company_id']);
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist,company_istrackcoursetype,company_ismusttrackcoursetype", "company_id='{$publicarray['company_id']}'");
            $this->company_istrackcoursetype = $companyOne['company_istrackcoursetype'];
            $this->company_ismusttrackcoursetype = $companyOne['company_ismusttrackcoursetype'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证操作人信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");

        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->marketer_id = $marketer_id;
            return true;
        }
    }

    function inviteTimetable($request){

        if(!isset($request['month']) || $request['month']==''){
            $request['month']=date("Y-m");
        }

        $start=date("Y-m-",strtotime($request['month'])).'01';

        $end=date("Y-m-t",strtotime($request['month']));

        $weekstart=date("w",strtotime($start));
        $weekend=date("w",strtotime($end));

        $start_num=$weekstart-1;
        $end_num=7-$weekend;

        $starttime=date("Y-m-d",strtotime('-'.$start_num.' day',strtotime($start)));
        $endtime=date("Y-m-d",strtotime('+'.$end_num.' day',strtotime($end)));

        $enweekarray = array("Sunday","Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");

        $datawhere = " 1 ";
        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
        }

        if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}' ";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['course_id']) and $request['course_id'] !== "") {
            $datawhere .= " and co.course_id = '{$request['course_id']}' ";
        }

        if (isset($request['hour_way']) and $request['hour_way'] !== "") {
            $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
        }

        if (isset($request['school_id']) and $request['school_id'] !== "") {
            $datawhere .= " and c.school_id ='{$request['school_id']}'";
        }else{
            $this->error = true;
            $this->errortip = "请选择学校";
            return false;
        }


        $sql = " select c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype,c.class_appointnum,ca.coursecat_branch  
                ,if(co.course_inclasstype=3 and co.course_openclasstype=1,ifnull((select count(cb.audition_id) from view_crm_audition as cb where cb.audition_isvisit>=0 and cb.hour_id=(select h.hour_id from smc_class_hour as h where h.class_id=c.class_id and h.hour_ischecking <> '-1' order by h.hour_day asc,h.hour_lessontimes asc limit 0,1)),0),ifnull((select count(cb.audition_id) from view_crm_audition as cb where cb.hour_id=ch.hour_id and cb.audition_isvisit>=0),0)) as bookingNum
     from smc_class_hour AS ch
     LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
     left join smc_course as co on co.course_id=c.course_id
     left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
     left join smc_code_coursecat as ca on ca.coursecat_id=co.coursecat_id
     where {$datawhere}
     and ch.hour_ischecking <> '-1'
     and  ch.hour_day >='{$starttime}'  and ch.hour_day <= '{$endtime}' and c.class_status <> '-2' 
     order by ch.hour_day asc,ch.hour_starttime asc
    ";
        $hourList = $this->DataControl->selectClear($sql);

        $time_start = strtotime($starttime);
        $time_end = strtotime($endtime);
        $dateList = array();
        while ($time_start <= $time_end) {
            $dateList[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $data = array();
        if ($hourList) {
            foreach($dateList as $date){
                if($date<$start || $date>$end){
                    if($date<$start){
                        $weeknum='1';
                    }else{
                        $weeknum=ceil((date("d",strtotime($end))+$start_num+1)/7);
                    }
                }else{
                    $weeknum=ceil((date("d",strtotime($date))+$start_num)/7);
                }

                foreach ($hourList as $hourOne) {
                    if($date==$hourOne['hour_day']){
                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['day']=date('d',strtotime($date)).'日';
                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['month']=date('Y',strtotime($date)).'年'.date('m',strtotime($date)).'月'.date('d',strtotime($date));

                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['is_this_month']=date('Y-m',strtotime($date))==$request['month']?1:0;

                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['info'][]=$hourOne;
                    }else{
                        if(!isset($data[$weeknum][$enweekarray[date("w",strtotime($date))]]['info'])){
                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['day']=date('d',strtotime($date)).'日';
                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['month']=date('Y',strtotime($date)).'年'.date('m',strtotime($date)).'月'.date('d',strtotime($date));

                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['is_this_month']=date('Y-m',strtotime($date))==$request['month']?1:0;

                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['info']=array();
                        }
                    }
                }
            }
        }

        return $data;

    }


}