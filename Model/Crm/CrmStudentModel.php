<?php

namespace Model\Crm;


class CrmStudentModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $isGmcPost = null;
    public $company_istrackcoursetype = 0;
    public $company_ismusttrackcoursetype = 0;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'],$publicarray['company_id']);
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist,company_istrackcoursetype,company_ismusttrackcoursetype", "company_id='{$publicarray['company_id']}'");
            $this->company_istrackcoursetype = $companyOne['company_istrackcoursetype'];
            $this->company_ismusttrackcoursetype = $companyOne['company_ismusttrackcoursetype'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证操作人信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");

        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            $this->marketer_id = $marketer_id;
            return true;
        }
    }

    static $class_status = array(0 => "待开班", 1 => "进行中", -1 => "已结束");
    static $student_study = array(0 => "不在读", 1 => "在读", -1 => "已结束");

    /**
     *  更新CRMstudent
     * author: ling
     * 对应接口文档 0001
     */
    private function insertCRMStudent($student_id, $student_type = 0,$school_id = 0)
    {
        if($this->DataControl->selectOne(" select student_id from crm_student where student_id = '{$student_id}'  limit 0,1")){
            $studentData = array();
//            $studentData['student_tracestatus'] = 0;
            $studentData['student_type'] = $student_type;
            $studentData['school_id'] = $school_id;
            $this->DataControl->updateData("crm_student", "student_id='{$student_id}'", $studentData);
        }else {
            $crmStudentData = array();
            $crmStudentData['student_id'] = $student_id;
            $crmStudentData['student_distributionstatus'] = '0';
            $crmStudentData['student_tracestatus'] = '0';
            $crmStudentData['student_intention_level'] = '0';
            $crmStudentData['student_type'] = $student_type;
            $crmStudentData['school_id'] = $school_id;
            $crmStudentData['student_createtime'] = time();
            $crmStudentData['student_updatatime'] = time();
            $this->DataControl->insertData("crm_student", $crmStudentData);
        }
    }

    /**
     * 分配在籍学生
     * author: ling
     * 对应接口文档 0001
     */
    function crmAllotStudentAction($paramArray)
    {
        $paramArray['student_array'] = stripslashes($paramArray['student_array']);
        $StudentModel = new StudentActionModel($paramArray);
        $studentList = json_decode($paramArray['student_array'], 1);
        $allstu = is_array($studentList)?count($studentList):0;
        $scc = 0;
        $fail = 0;
        if (is_array($studentList) && count($studentList) > 0) {
            foreach ($studentList as $student_One) {
                $schoolidjson = $paramArray['school_id'];
                //跨校的人
                $enrollednum = 0;
                $enrolledarray = $this->DataControl->selectClear(" select school_id,student_id from smc_student_enrolled where enrolled_status in (0,1,3) and student_id = '{$student_One['student_id']}' ");
                if($enrolledarray){
                    $enrollednum = count($enrolledarray);
                }
                if($enrollednum > 2){//数据应该是有问题
                    $fail++;
                    $title = '该学生在多家学校就读';
                    continue;
                }elseif($enrollednum == 2){//跨校
                    $schoolidarray = [$enrolledarray[0]['school_id'],$enrolledarray[1]['school_id']];
                    if(!in_array($paramArray['school_id'], $schoolidarray)){
                        $fail++;
                        $title = '该学生不在本校就读';
                        //不在当前学校
                        continue;
                    }

                    $schoolidjson = $enrolledarray[0]['school_id'].','.$enrolledarray[1]['school_id'];
                    //是否是当前学校的分配
                    if ($this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$student_One['student_id']}' and principal_leave = 0 and school_id in ({$schoolidjson}) ")) {
                        $fail++;
                        $title = '该学生已在其他校被分配';
                        continue;
                    }
                    //名单是否已经创建
                    $studentONE = $this->DataControl->getFieldOne("crm_student", "student_distributionstatus,school_id", "student_id='{$student_One['student_id']}' ");
                    if ($studentONE && $studentONE['student_distributionstatus'] == 1 && in_array($studentONE['school_id'], $schoolidarray)) {
                        $fail++;
                        $title = '该学生已经被分配';
                        continue;
                    } else {
                        $this->insertCRMStudent($student_One['student_id'], $paramArray['student_type'],$paramArray['school_id']);
                    }
                }elseif($enrollednum == '1'){
                    if($paramArray['school_id'] != $enrolledarray[0]['school_id']){
                        $fail++;
                        $title = '该学生不在本校就读';
                        //不在当前学校
                        continue;
                    }
                    $schoolidjson = $enrolledarray[0]['school_id'];//当前所在学校ID
                    //是否是当前学校的分配
                    if ($this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$student_One['student_id']}' and principal_leave =0 and school_id = '{$schoolidjson}' ")) {
                        $fail++;
                        $title = '该学生已经被分配';
                        continue;
                    }
                    //名单是否已经创建
                    $studentONE = $this->DataControl->getFieldOne("crm_student", "school_id,student_distributionstatus", "student_id='{$student_One['student_id']}'  ");
                    if ($studentONE && $studentONE['student_distributionstatus'] == 1 && $studentONE['school_id'] == $paramArray['school_id'] ) {
                        $fail++;
                        $title = '该学生已经被分配';
                        continue;
                    } else {
                        $this->insertCRMStudent($student_One['student_id'], $paramArray['student_type'],$paramArray['school_id']);
                    }
                }else{
                    $this->insertCRMStudent($student_One['student_id'], $paramArray['student_type'],$paramArray['school_id']);
                }
//                if ($this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$paramArray['student_id']}' and principal_leave =0 ")) {
//                    continue;
//                }
//                $studentONE = $this->DataControl->getFieldOne("crm_student", "student_distributionstatus", "student_id='{$student_One['student_id']}'");
//                if ($studentONE && $studentONE['student_distributionstatus'] == 1) {
//                    continue;
//                } else {
//                    $this->insertCRMStudent($student_One['student_id'], $paramArray['student_type'],$paramArray['school_id']);
//                }

                //是否存在 其他校二次跟进的未确认 名单
                if ($this->DataControl->getFieldOne("crm_student_invite", "invite_id", "student_id='{$student_One['student_id']}' and invite_isvisit =0 and school_id not in ($schoolidjson) and company_id = '{$paramArray['company_id']}' ")) {

                    $upData = array();
                    $upData['invite_isvisit'] = -1;
                    $upData['invite_novisitreason'] = '校内招跟进发现存在流失校的柜询，系统做未确认操作';
                    $upData['invite_updatetime'] = time();
                    $this->DataControl->updateData("crm_student_invite", " student_id='{$student_One['student_id']}' and invite_isvisit =0 and school_id not in ($schoolidjson) and company_id = '{$paramArray['company_id']}' ", $upData);

                    $trackData = array();
                    $trackData['track_isactive'] = 0;
                    $trackData['track_note'] = "由系统自动确认柜询未到访，原因:校内招跟进发现存在流失校的柜询";
                    $trackData['track_state'] = 0;
                    $trackData['track_linktype'] = '取消柜询';
                    $StudentModel->insertStudentTrackAction($student_One['student_id'], 15, $trackData);

                } elseif ($this->DataControl->getFieldOne("crm_student_audition", "audition_id", "student_id='{$student_One['student_id']}' and  audition_isvisit =0 and school_id not in ($schoolidjson) and company_id = '{$paramArray['company_id']}' ")) {

                    $upData = array();
                    $upData['audition_isvisit'] = -1;
                    $upData['audition_novisitreason'] = '校内招跟进发现存在流失校的试听，系统做未确认操作';
                    $upData['audition_updatetime'] = time();
                    $this->DataControl->updateData("crm_student_audition", " student_id='{$student_One['student_id']}' and invite_isvisit =0 and school_id not in ($schoolidjson) and company_id = '{$paramArray['company_id']}' ", $upData);

                    $trackData = array();
                    $trackData['track_isactive'] = 0;
                    $trackData['track_note'] = "由系统自动确认试听未到访，原因:校内招跟进发现存在流失校的试听";
                    $trackData['track_state'] = 0;
                    $trackData['track_linktype'] = '取消试听';
                    $StudentModel->insertStudentTrackAction($student_One['student_id'], 15, $trackData);
                }

                //主负责人
                if ($paramArray['zhu_marketer_id']) {
                    $StudentModel->allotStudentAction($student_One['student_id'], $paramArray['zhu_marketer_id'], 1);
                    //副负责人
                    $fu_marketer_Array = json_decode(stripslashes($paramArray['fu_marketer_array']), 1);
                    if (is_array($fu_marketer_Array) && count($fu_marketer_Array) > 0) {
                        for ($i = 0; $i < count($fu_marketer_Array); $i++) {
                            $StudentModel->allotStudentAction($student_One['student_id'], $fu_marketer_Array[$i], 0);
                        }
                    }
                    $zhu_marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['zhu_marketer_id']}'");
                    $data = array();
                    $data['track_linktype'] = $this->LgStringSwitch("主管分配");
                    $data['track_note'] = $this->LgStringSwitch("由{$this->marketerOne['marketer_name']}进行分配,分配给主负责人{$zhu_marketerOne['marketer_name']}进行跟踪。");
                    if ($paramArray['zhu_marketer_id'] == $paramArray['marketer_id']) {
                        $data = array();
                        $data['track_linktype'] = $this->LgStringSwitch("自主跟进");
                        $data['track_note'] = $this->LgStringSwitch("由{$this->marketerOne['marketer_name']}进行自主跟进。");
                    }
                    $StudentModel->insertStudentTrackAction($student_One['student_id'], $this->marketer_id, $data);
                    $studentData = array();
                    $studentData['student_distributionstatus'] = 1;
                    $this->DataControl->updateData("crm_student", "student_id='{$student_One['student_id']}'", $studentData);
                }
                $scc++;
            }

            if($title != ''){
                $this->error = 1;
                $this->errortip = $title;
                return false;
            }else{
                if($allstu > 1) {
                    $title = '分配成功{$scc}个，失败{$fail}个';

                    $this->error = 0;
                    $this->errortip = $title;
                    return true;
                }else{
                    $this->error = 0;
                    $this->errortip =  '分配成功';
                    return true;
                }
            }
        } else {
            $this->error = 1;
            $this->errortip = '请选择学生';
            return false;
        }
    }

    /**
     * 跟进在籍学生
     * author: ling
     * 对应接口文档 0001
     */
    function crmTrackStudentAction($paramArray)
    {
        if (!$paramArray['student_id']) {
            $this->error = 1;
            $this->errortip = "请选择需要跟进的学生";
            return false;
        }

        $StudentModel = new StudentActionModel($paramArray);
        if (!$this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$paramArray['student_id']}' and marketer_id='{$paramArray['marketer_id']}' and principal_leave =0 ") && $paramArray['postbe_crmuserlevel'] <> 1) {
            $this->error = 1;
            $this->errortip = "非您的意向学生,无法取消";
            return false;
        }
        if ($paramArray['track_followmode'] == 2) {
            //每个客户在一个学校一个班组一天仅有一次预约类型
            if ($this->DataControl->selectOne("SELECT a.audition_id FROM view_crm_audition a
WHERE a.school_id = '{$paramArray['school_id']}' AND a.coursetype_id = '{$paramArray['coursetype_branch']}' AND a.client_id = '{$paramArray['student_id']}' AND a.audition_type = 'stu' AND a.audition_isvisit <> '-1' AND DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = DATE_FORMAT('{$paramArray['audition_visittime']}', '%Y-%m-%d') limit 0,1")) {
                $this->error = 1;
                $this->errortip = "该班组当天已邀约过试听";
                return false;
            }
        }
        $data = array();
        $data['track_followmode'] = $paramArray['track_followmode']; //跟进模式
        $data['track_linktype'] = $paramArray['track_linktype'];   //沟通方式.类型
        $data['object_code'] = $paramArray['object_code'];          //沟通对象
        $data['track_validinc'] = $paramArray['track_validinc'];      //是否有效
        $data['track_note'] = $paramArray['track_note'];
        $data['coursetype_id'] = $paramArray['coursetype_branch'];
        $data['coursecat_id'] = $paramArray['coursecat_id'];
        $data['track_intention_level'] = $paramArray['track_intention_level'];
        $data['track_followuptype'] = $paramArray['track_followuptype'];   //下次跟进类型
        if ($paramArray['track_followmode'] == 1) {
            $data['track_visitingtime'] = $paramArray['invite_visittime'];
        } elseif ($paramArray['track_followmode'] == 2) {
            $data['track_visitingtime'] = $paramArray['audition_visittime'];
        }
        if ($paramArray['track_followuptype'] == 1) {
            $data['track_followuptime'] = $paramArray['track_followuptime'];
        }
        $data['track_isactive'] = 1;
        $data['track_type'] = 0;
        $track_id = $StudentModel->insertStudentTrackAction($paramArray['student_id'], $paramArray['marketer_id'], $data);
        if ($paramArray['track_followmode'] == 1) {
            $inviteData = array();
            $inviteData['company_id'] = $this->company_id;
            $inviteData['school_id'] = $this->school_id;
            $inviteData['marketer_id'] = $this->marketer_id;
            $inviteData['receiver_name'] = $paramArray['receiver_name'];
            $inviteData['student_id'] = $paramArray['student_id'];
            $inviteData['track_id'] = $track_id;
            $inviteData['coursetype_id'] = $paramArray['coursetype_branch'];
            $inviteData['coursecat_id'] = $paramArray['coursecat_id'];
            $inviteData['invite_level'] = $paramArray['track_intention_level'];
            $inviteData['invite_genre'] = $paramArray['invite_genre'];
            $inviteData['invite_visittime'] = $paramArray['invite_visittime'];
            $inviteData['invite_createtime'] = time();
            if ($inviteData['invite_genre'] == '2' || $inviteData['invite_genre'] == '3') {
                $inviteData['invite_isvisit'] = 1;
            }
            $this->DataControl->insertData("crm_student_invite", $inviteData);

            if ($inviteData['invite_genre'] == '2' || $inviteData['invite_genre'] == '3') {
                $markerOne = $this->DataControl->selectOne("select marketer_name from  crm_marketer where marketer_id='{$paramArray['marketer_id']}' ");
                $dataTrack = array();
                $dataTrack['school_id'] = $paramArray['school_id'];
                $dataTrack['student_id'] = $paramArray['student_id'];
                $dataTrack['marketer_id'] = $paramArray['marketer_id'];
                $dataTrack['marketer_name'] = $markerOne['marketer_name'];
                $dataTrack['track_validinc'] = 1;
                $dataTrack['track_createtime'] = time();
                $dataTrack['track_linktype'] = $this->LgStringSwitch(":确认柜询");
                $dataTrack['track_note'] = $this->LgStringSwitch("系统确认:确认柜询成功");
                $this->DataControl->insertData("crm_student_track", $dataTrack);
            }
        } elseif ($paramArray['track_followmode'] == 2) {
            $auditionData = array();
            $auditionData['company_id'] = $this->company_id;
            $auditionData['school_id'] = $this->school_id;
            $auditionData['marketer_id'] = $this->marketer_id;
            $auditionData['receiver_name'] = $paramArray['receiver_name'];
            $auditionData['student_id'] = $paramArray['student_id'];
            $auditionData['track_id'] = $track_id;
            $auditionData['coursetype_id'] = $paramArray['coursetype_branch'];
            $auditionData['coursecat_id'] = $paramArray['coursecat_id'];
            $auditionData['course_id'] = $paramArray['course_id'];
            $auditionData['class_id'] = $paramArray['class_id'];
            $auditionData['class_cnname'] = $paramArray['class_cnname'];
            $auditionData['hour_id'] = $paramArray['hour_id'];
            $auditionData['audition_genre'] = $paramArray['audition_genre'];
            $auditionData['audition_visittime'] = $paramArray['audition_visittime'];
            $auditionData['audition_createtime'] = time();
            $this->DataControl->insertData("crm_student_audition", $auditionData);
        }

        $clientOne = $this->DataControl->selectOne("select from_client_id from smc_student where student_id = '{$paramArray['student_id']}'");


        //意向课程
        $Intentionarray = array();
        $Intentionarray['student_id'] = $paramArray['student_id'];
        $Intentionarray['coursetype_id'] = $paramArray['coursetype_branch'];
        $Intentionarray['coursecat_id'] = $paramArray['coursecat_id'];
        $Intentionarray['course_id'] = $paramArray['course_id'];
        $Intentionarray['intention_updatetime'] = time();
        if (!$this->DataControl->selectOne("SELECT l.intention_id FROM crm_student_intention l WHERE l.student_id = '{$paramArray['student_id']}' and l.coursetype_id = '{$Intentionarray['coursetype_id']}' and l.coursecat_id = '{$Intentionarray['coursecat_id']}' limit 0,1 ")) {
            $this->DataControl->insertData("crm_student_intention", $Intentionarray);
        }

        if ($paramArray['track_followuptype'] == 1) {
            $remind = array();
            $remind['company_id'] = $this->company_id;
            $remind['school_id'] = $this->school_id;
            $remind['marketer_id'] = $this->marketer_id;
            $remind['student_id'] = $paramArray['student_id'];
            $remind['remind_time'] = date("Y-m-d", strtotime("{$paramArray['track_followuptime']}"));
            $remind['remind_remark'] = $this->LgStringSwitch("系统:跟进提醒");
            $remind['remind_createtime'] = time();
            $this->DataControl->insertData('crm_student_remind', $remind);
        }
        if ($track_id) {
            $this->error = 0;
            $this->errortip = '跟进成功';
            return true;
        } else {
            $this->error = 1;
            $this->errortip = '跟进失败';
            return false;
        }
    }

    /**
     * 取消跟踪
     */
    function crmCancelStudentTrackAction($paramArray)
    {
        $paramArray['student_array'] = stripslashes($paramArray['student_array']);
        $StudentModel = new StudentActionModel($paramArray);
        $studentList = json_decode($paramArray['student_array'], 1);
        if (is_array($studentList) && count($studentList) > 0) {
            foreach ($studentList as $student_One) {
                if (!$this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$student_One['student_id']}' and marketer_id='{$paramArray['marketer_id']}' and principal_leave =0 ") && $paramArray['postbe_crmuserlevel'] <> 1) {
                    continue;
                }
                $invireOne = $this->DataControl->getFieldOne("crm_student_invite", "invite_id", "student_id='{$student_One['student_id']}' and invite_isvisit =0");
                $auditionOne = $this->DataControl->getFieldOne("crm_student_audition", "audition_id", "student_id='{$student_One['student_id']}' and  audition_isvisit =0");

                if (count($studentList) == 1) {
                    if ($invireOne) {
                        $this->error = 1;
                        $this->errortip = "该学生有柜询记录未确认";
                        return false;
                    } elseif ($auditionOne) {
                        $this->error = 1;
                        $this->errortip = "该学生有试听记录未确认";
                        return false;
                    }
                } else {
                    if ($invireOne || $auditionOne) {
                        continue;
                    }
                }

                $studentONE = $this->DataControl->getFieldOne("crm_student", "student_distributionstatus", "student_id='{$student_One['student_id']}'");
                if (!$studentONE) {
                    $this->insertCRMStudent($student_One['student_id'], $paramArray['student_type'],$paramArray['school_id']);
                }
                if ($studentONE && $studentONE['student_distributionstatus'] == 0) {
                    continue;
                } else {
                    $trackData = array();
                    $trackData['track_isactive'] = 1;
                    $trackData['track_note'] = "由{$this->marketerOne['marketer_name']}取消跟踪，原因:{$paramArray['cancel_reason']}";
                    $trackData['track_state'] = -1;
                    $trackData['track_linktype'] = '取消跟踪';
                    $StudentModel->insertStudentTrackAction($student_One['student_id'], $this->marketer_id, $trackData);
                }

                $StudentModel->CancelStuTrackAction($student_One['student_id']);
            }
            $this->error = 0;
            $this->errortip = "取消成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "请选择学生";
            return false;
        }
    }

    /**
     * 获取CRM学生信息
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function getCrmStudentOneApi($paramArray)
    {
        $sql = " 
            select t.student_id,t.student_crmtag,t.student_cnname,t.student_branch,student_enname,student_birthday,ct.student_intention_level,student_img,student_sex,p.parenter_mobile,t.student_idcard,b.student_balance,t.student_forwardprice,b.student_withholdbalance,
             (select k.track_createtime from crm_student_track as k where k.student_id = t.student_id order by k.track_id DESC limit 0,1  ) as track_createtime,
             (select count(k.track_id) from crm_student_track as k where k.student_id = t.student_id and k.track_isactive =1 ) as track_allnum,
             (select count(k.track_id) from crm_student_track as k where k.student_id = t.student_id and k.track_isactive =1 and k.track_followmode =0 ) as track_followmodenum,
             (select count(i.invite_id) from crm_student_invite as i where i.student_id = t.student_id and i.invite_isvisit =1 ) as invite_num,
             (select count(a.audition_id) from crm_student_audition as a where a.student_id = t.student_id and a.audition_isvisit =1 ) as audition_num,
             (select SUM(po.order_arrearageprice) from smc_payfee_order as po where po.student_id=t.student_id and po.company_id='{$paramArray['company_id']}' and po.school_id='{$paramArray['school_id']}' and (po.order_status between 1 and 3)) as order_arrearageprice
            from smc_student as t
            left join crm_student as ct ON ct.student_id = t.student_id
            left join smc_student_family as f ON t.student_id = f.student_id and f.family_isdefault =1
            left join smc_parenter as p ON p.parenter_id = f.parenter_id
            left join smc_student_balance as b ON b.student_id=t.student_id and b.school_id ='{$this->school_id}'
            where t.student_id='{$paramArray['student_id']}'
        ";
        $studentOne = $this->DataControl->selectOne($sql);
        if ($studentOne) {
            if ($this->isGmcPost == true) {
                $studentOne['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $studentOne['parenter_mobile']);
            }
            $studentOne['company_istrackcoursetype'] =  $this->company_istrackcoursetype;
            $studentOne['company_ismusttrackcoursetype'] =  $this->company_ismusttrackcoursetype;

            if ($studentOne['student_crmtag']) {
                $studentOne['student_crmtag'] = explode(',', $this->LgStringSwitch($studentOne['student_crmtag']));
            } else {
                $studentOne['student_crmtag'] = array();
            }
            $studentOne['student_intention_level'] = intval($studentOne['student_intention_level']);
            $studentOne['last_tracktime'] = $studentOne['track_createtime'] ? date('Y-m-d H:i:s', $studentOne['track_createtime']) : '--';
            $studentOne['order_paymentprice'] = sprintf("%01.2f", $studentOne['order_arrearageprice']);
            $studentOne['student_balance_all'] = sprintf("%01.2f", $studentOne['student_balance'] + $studentOne['student_withholdbalance']);  //账户余额
            //$studentOne['student_balance'];   //可退余额
        } else {
            $studentOne = array();
        }
        $familyList = $this->DataControl->selectClear("select p.parenter_cnname,p.parenter_mobile,family_isdefault,f.family_relation from smc_student_family as f,smc_parenter as p where f.parenter_id =p.parenter_id and f.student_id='{$paramArray['student_id']}' ");
        if (!$familyList) {
            $familyList = array();
        }else{
            foreach ($familyList as &$familyListVar){
                if ($this->isGmcPost == true) {
                    $familyListVar['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $familyListVar['parenter_mobile']);
                }
            }
        }

        $data['student'] = $studentOne;
        $data['family'] = $familyList;
        return $data;
    }

    /**
     * 获取CRM学生的课程信息
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function getCrmStuCourseList($paramArray)
    {
        $where = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $where .= "  and (co.course_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $where .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        $Having = '1=1';
        if (isset($paramArray['study_isreading']) && $paramArray['study_isreading'] !== '') {
            if ($paramArray['study_isreading'] == 0) {
                $Having .= "  and study_isreading is null";
            } else {
                $Having .= "  and study_isreading = '{$paramArray['study_isreading']}'";
            }
        }

        $sql = "select co.course_branch,co.course_cnname,t.agreement_cnname,pt.tuition_originalprice,pt.tuition_sellingprice,cb.coursebalance_time,cb.coursebalance_figure,cp.coursetype_branch,ct.coursecat_branch,
                (select sum(oc.ordercourse_buynums) from smc_payfee_order as o,smc_payfee_order_course as oc where o.order_pid =oc.order_pid and oc.course_id =co.course_id and o.student_id =cb.student_id and o.school_id = cb.school_id ) as  ordercourse_buynums,
                (CASE WHEN (SELECT d.study_isreading FROM smc_student_study AS d,smc_class AS s WHERE d.class_id = s.class_id AND d.student_id = cb.student_id AND s.course_id = co.course_id ORDER BY d.study_id DESC LIMIT 1 ) = '-1' THEN '-1' 
				WHEN (SELECT d.study_beginday FROM smc_student_study AS d,smc_class AS s WHERE d.class_id = s.class_id AND d.student_id = cb.student_id AND s.course_id = co.course_id ORDER BY d.study_id DESC LIMIT 1 ) <= CURDATE() 
				AND (SELECT s.class_stdate FROM smc_student_study AS d,smc_class AS s WHERE d.class_id = s.class_id AND d.student_id = cb.student_id AND s.course_id = co.course_id ORDER BY d.study_id DESC LIMIT 1 ) <= CURDATE() THEN '1' ELSE '0' END ) AS study_isreading 
                from smc_course as co 
                left join smc_student_coursebalance as cb ON  cb.course_id=co.course_id
                left join smc_fee_pricing as p ON p.pricing_id =cb.pricing_id 
                left join smc_fee_agreement as t ON t.agreement_id = p.agreement_id 
                left join smc_fee_pricing_tuition as pt ON pt.pricing_id =p.pricing_id
                left join smc_code_coursetype as cp ON cp.coursetype_id=co.coursetype_id
                left join smc_code_coursecat as  ct ON ct.coursecat_id=co.coursecat_id
                where cb.student_id='{$paramArray['student_id']}' and cb.school_id='{$paramArray['school_id']}' and {$where}
                group by co.course_id 
                having {$Having}
                ";


        $courseList = $this->DataControl->selectClear($sql);
        if (!$courseList) {
            $courseList = array();
        } else {
            foreach ($courseList as &$value) {
                if ($value['study_isreading'] == '') {
                    $value['study_isreading_name'] = '不在读';
                } else {
                    $value['study_isreading_name'] = self::$student_study[$value["study_isreading"]];;
                }
            }
        }
        $coursetypeList = $this->DataControl->selectClear(" 
                select ct.coursetype_cnname,ct.coursetype_id,ct.coursetype_branch
                from smc_student_coursebalance as s 
                left join smc_course as co ON co.course_id = s.course_id
                left join smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                where s.student_id='{$paramArray['student_id']}' and  s.school_id='{$this->school_id}'
                group by ct.coursetype_id
                 ");
        if (!$coursetypeList) {
            $coursetypeList = array();
        }
        $data = array();
        $data['courseList'] = $courseList;
        $data['coursetypeList'] = $coursetypeList;
        return $data;
    }

    /**
     * 获取CRM学生的班级
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function getCrmStuClass($paramArray)
    {
        $where = '1';
        $typewhere = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $where .= "  and (s.class_cnname like '%{$paramArray['keyword']}%' or s.class_enname like '%{$paramArray['keyword']}%' or s.class_branch like '%{$paramArray['keyword']}%' or co.course_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['study_isreading']) && $paramArray['study_isreading'] !== '') {
            $where .= "  and d.study_isreading = '{$paramArray['study_isreading']}'";
        }

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $where .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
            $typewhere .= "  and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== '') {
            $where .= "  and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }

        $sql = "select d.study_id,s.class_cnname,s.class_branch,s.class_enname,co.course_branch,co.course_cnname,s.class_status,d.study_beginday,d.study_endday,cp.coursetype_branch,cp.coursetype_cnname,ct.coursecat_branch,
                (CASE WHEN d.study_isreading = '-1' THEN '-1' WHEN d.study_beginday <= CURDATE() AND s.class_stdate <= CURDATE() THEN '1' ELSE '0' END) as study_isreading,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id=s.class_id and h.hour_ischecking <> -1) as hour_num,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id=s.class_id and h.hour_ischecking =1) as hour_checkingnum,
                (select count(hy.hourstudy_id) from smc_student_hourstudy as hy where hy.class_id=s.class_id and hy.student_id=d.student_id) as hourstudy_num
                from smc_student_study as d
                left join smc_class as s On d.class_id =s.class_id
                left join smc_course as co ON co.course_id=s.course_id
                left join smc_code_coursetype as cp ON cp.coursetype_id=co.coursetype_id
                left join smc_code_coursecat as ct ON ct.coursecat_id=co.coursecat_id
                where d.student_id='{$paramArray['student_id']}' and s.company_id='{$this->company_id}' and {$where}
                group by s.class_id 
                ";

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $classList = array();
        } else {
            foreach ($classList as &$classOne) {
                $classOne['class_status_name'] = self::$class_status[$classOne['class_status']];
                $classOne['study_isreading_name'] = self::$student_study[$classOne["study_isreading"]];
                $classOne['study_during'] = $classOne['study_beginday'] . '~' . ($classOne['study_endday'] ? $classOne['study_endday'] : '--');
            }
        }

        $coursetypeList = $this->DataControl->selectClear(" 
                select ct.coursetype_cnname,ct.coursetype_id,ct.coursetype_branch
                from smc_student_study as d 
                left join smc_class as s On d.class_id =s.class_id
                left join smc_course as co ON co.course_id = s.course_id
                left join smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                where d.student_id='{$paramArray['student_id']}' and s.company_id='{$this->company_id}' 
                group by ct.coursetype_id
                 ");
        if (!$coursetypeList) {
            $coursetypeList = array();
        }

        $coursecatList = $this->DataControl->selectClear(" 
                select ct.coursecat_cnname,ct.coursecat_id,ct.coursecat_branch
                from smc_student_study as d 
                left join smc_class as s On d.class_id =s.class_id
                left join smc_course as co ON co.course_id = s.course_id
                left join smc_code_coursecat as ct ON ct.coursecat_id =co.coursecat_id
                where d.student_id='{$paramArray['student_id']}' and s.company_id='{$this->company_id}'and {$typewhere}
                group by ct.coursecat_id
                 ");
        if (!$coursecatList) {
            $coursecatList = array();
        }
        $data = array();
        $data['classList'] = $classList;
        $data['coursetypeList'] = $coursetypeList;
        $data['coursecatList'] = $coursecatList;
        return $data;

    }

    /**
     * 获取学生的跟进信息
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function getStudentTrackList($paramArray)
    {
        $datawhere = '1';
        if (isset($paramArray['starttime']) && $paramArray['starttime']) {
            $starttime = strtotime($paramArray['starttime']);
            $datawhere .= " and t.track_createtime >='{$starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime']) {
            $endtime = strtotime($paramArray['endtime']) + 3600 * 24 - 1;
            $datawhere .= " and  t.track_createtime <='{$endtime}'";
        }
//        0普通回访1邀约2视听-1取消跟踪
        $track_followmode_array = array(0 => '普通回访', 1 => '柜询', 2 => '试听',4 => '转介绍跟踪', '-1' => '取消跟踪');

        $sqlfield = "m.marketer_name,f.staffer_enname,t.track_id,c.student_img,t.track_linktype,t.track_followmode,t.track_followuptime,t.track_note,track_visitingtime,from_unixtime(t.track_createtime, '%Y-%m-%d %H:%i:%s') as  track_createtime ,c.student_sex,t.school_id,t.track_id,t.track_isactive,t.track_validinc,ty.coursetype_cnname,t.track_intention_level";
        $studentTrackList = $this->DataControl->selectClear("
            select {$sqlfield},
            (select ca.class_cnname from crm_student_audition as ca where ca.track_id = t.track_id limit 0,1) as class_cnname,
            (select sl.school_shortname from smc_school as sl  where sl.school_id=t.school_id limit 0,1) as school_cnname,
            (select co.object_name from crm_code_object as co  where co.object_code =t.object_code limit 0,1 )  as object_name,
            (select s.staffer_sex from smc_staffer as s  where s.staffer_id=m.staffer_id) as staffer_sex,
            (select s.staffer_img from smc_staffer as s  where s.staffer_id=m.staffer_id) as marketer_img
            from crm_student_track as t
            LEFT JOIN  smc_student  as c ON c.student_id=t.student_id
            left JOIN crm_marketer as m ON m.marketer_id = t.marketer_id
            left JOIN smc_staffer as f On f.staffer_id=m.staffer_id
            left join smc_code_coursetype as ty ON t.coursetype_id = ty.coursetype_id
            where  t.student_id= '{$paramArray['student_id']}' and {$datawhere}
            Order by  t.track_createtime Desc Limit 0,30");

        if (!$studentTrackList) {
            $studentTrackList = array();
        } else {
            foreach ($studentTrackList as &$trackOne) {
                $trackOne['track_followmode_name'] = $track_followmode_array[$trackOne['track_followmode']];
                $trackOne['object_name'] = $trackOne['object_name'] == '' ? '--' : $trackOne['object_name'];
            }
        }
        $data = array();
        $data['list'] = $studentTrackList;
        return $data;
    }

    /**
     *  获取学生的柜询记录
     * author: ling
     * 对应接口文档 0001
     * @param $student_id
     * @return array|bool
     */
    function getStudentInviteList($reqeust)
    {
        $studentInviteList = $this->DataControl->selectClear("
			select  i.invite_genre,i.invite_isvisit,i.invite_level,i.invite_visittime,i.invite_novisitreason
			from crm_student_invite  as i where i.student_id='{$reqeust['student_id']}'  and i.school_id='{$this->school_id}'
			 Order by i.invite_createtime Desc
			 ");

        if ($studentInviteList) {
            $genrearray = $this->LgArraySwitch(array('0'=>'邀约-普通柜询','1'=>'邀约-能力测试','2'=>'来访-推带到访','3'=>'来访-主动到访'));//0普通柜询1能力测试 2推带到访 3 主动到访

            foreach ($studentInviteList as $key => $value) {
                $studentInviteList[$key]['invite_isvisitstatus'] = $value['invite_isvisit'];
                if ($value['invite_isvisit'] == '-1') {
                    $studentInviteList[$key]['invite_isvisit_name'] = $this->LgStringSwitch("否");
                } elseif ($value['invite_isvisit'] == '1') {
                    $studentInviteList[$key]['invite_isvisit_name'] = $this->LgStringSwitch("是");
                } else {
                    $studentInviteList[$key]['invite_isvisit_name'] = $this->LgStringSwitch("待确认");
                }
                $studentInviteList[$key]['invite_genre'] = $genrearray[$value['invite_genre']];
//                if ($value['invite_genre'] == 0) {
//                    $studentInviteList[$key]['invite_genre'] = $this->LgStringSwitch("普通柜询");
//                } else {
//                    $studentInviteList[$key]['invite_genre'] = $this->LgStringSwitch("能力测试");
//                }

                $studentInviteList[$key]['invite_level'] = intval($value['invite_level']);
            }
            $list = $studentInviteList;
        } else {
            $list = array();
        }
        return $list;
    }

    /**
     * 获取学生的试听记录
     * author: ling
     * 对应接口文档 0001
     * @param $student_id
     * @return array|bool
     */
    function getStudentAuditionList($request)
    {
        $studentIntentionList = $this->DataControl->selectClear("select
 			o.course_cnname,o.course_branch,a.class_cnname,c.class_branch,a.audition_visittime,a.audition_isvisit,audition_novisitreason as invite_novisitreason,t.track_intention_level
			from  crm_student_audition as a
			left join  smc_class as c ON c.class_id = a.class_id
			LEFT JOIN smc_course AS o ON o.course_id = a.course_id
			LEFT JOIN crm_student_track as t On t.track_id =a.track_id
			where a.student_id='{$request['student_id']}' and a.school_id='{$this->school_id}'  order by a.audition_createtime  DESC ");

        if ($studentIntentionList) {
            foreach ($studentIntentionList as $key => $value) {
                $studentIntentionList[$key]['audition_isvisit_status'] = $value['audition_isvisit'];
                if ($value['audition_isvisit'] == '-1') {
                    $studentIntentionList[$key]['audition_isvisit_name'] = $this->LgStringSwitch('否');
                } elseif ($value['audition_isvisit'] == '1') {
                    $studentIntentionList[$key]['audition_isvisit_name'] = $this->LgStringSwitch('是');
                } else {
                    $studentIntentionList[$key]['audition_isvisit_name'] = $this->LgStringSwitch('待确认');
                }
                if (empty($value['course_cnname'])) {
                    $studentIntentionList[$key]['course_cnname'] = '--';
                }
            }
            $list = $studentIntentionList;
        } else {
            $list = array();
        }
        return $list;
    }

    //学校班级列表
    function getSchollClass($paramArray){
        $sql = " select class_id,company_id,school_id,school_branch,class_cnname,class_enname from smc_class where company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and class_type = '0' ";
        $datalist = $this->DataControl->selectClear($sql);
        $result = array();
        $result['list'] = $datalist;
        return $result;
    }
    //学校班级列表 --- 进行中的班级  并且过滤掉公开课
    function getSchollClassing($paramArray){
        $sql = " select s.class_id,s.company_id,s.school_id,s.school_branch,s.class_cnname,s.class_enname 
         from smc_class as s 
         left join smc_course as c ON s.course_id = c.course_id  
         where s.company_id = '{$paramArray['company_id']}' and s.school_id = '{$paramArray['school_id']}' and s.class_type = '0' and s.class_status = '1'
         and c.course_inclasstype <> '3'";
        $datalist = $this->DataControl->selectClear($sql);
        $result = array();
        $result['list'] = $datalist;
        return $result;
    }

    /**
     * 获取在籍学员列表
     * author: ling
     * 对应接口文档 0001
     */
    function getCrmStudyStudentList($paramArray)
    {
        $datawhere = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword']) {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%'  or st.student_enname like '%{$paramArray['keyword']}%' or p.parenter_cnname like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_student_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=st.student_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        $having = '1=1';
        $uncoursetypewhere = 'co.coursetype_id >0';
        // 非在读班组
        if (isset($paramArray['unstudy_coursetype_id']) && $paramArray['unstudy_coursetype_id']) {
            $uncoursetypewhere .= " and co.coursetype_id  = '{$paramArray['unstudy_coursetype_id']}'";
            $having .= " and unstudy_coursetype_id is  Null";
        }

        //在读班组
        $coursetypewhere = '1';
        if (isset($paramArray['study_coursetype_id']) && $paramArray['study_coursetype_id']) {
            $coursetypewhere .= " and t.coursetype_id='{$paramArray['study_coursetype_id']}'";
            $having .= " and study_coursetype_cnname is not Null";
        }
        if (isset($paramArray['study_coursecat_id']) && $paramArray['study_coursecat_id']) {
            $coursetypewhere .= " and co.coursecat_id='{$paramArray['study_coursecat_id']}'";
            $having .= " and study_coursetype_cnname is not Null";
        }
        //在读班级
        $classwhere = '1';
        if (isset($paramArray['study_class_id']) && $paramArray['study_class_id']) {
            $classwhere .= " and c.class_id='{$paramArray['study_class_id']}'";
            $having .= " and study_class_cnname is not Null";
        }
        if (isset($paramArray['p'])) {
            $p = $paramArray['p'];
        } else {
            $p = 1;
        }

        if (isset($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = 10;
        }
        $page = ($p - 1) * $num;

        $sql = "
            SELECT e.student_id,st.student_crmtag,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,st.student_birthday,p.parenter_mobile,p.parenter_cnname, '0' as student_type,p.parenter_email,
            (SELECT group_concat(DISTINCT t.coursetype_cnname) from smc_student_study as d,smc_class as c,smc_course as co,smc_code_coursetype as t where d.class_id=c.class_id and c.course_id =co.course_id
             and co.coursetype_id=t.coursetype_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and d.student_id = e.student_id and c.school_id = e.school_id and {$coursetypewhere} )  as  study_coursetype_cnname, 
            (SELECT group_concat(DISTINCT cc.coursecat_cnname) from smc_student_study as d,smc_class as c,smc_course as co,smc_code_coursetype as t,smc_code_coursecat as cc where d.class_id=c.class_id and c.course_id =co.course_id and co.coursetype_id=t.coursetype_id and co.coursecat_id=cc.coursecat_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and d.student_id = e.student_id and c.school_id = e.school_id and {$coursetypewhere} )  as  study_coursecat_cnname, 
            (SELECT group_concat(DISTINCT c.class_cnname) from smc_student_study as d,smc_class as c where d.class_id=c.class_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and {$classwhere}) as study_class_cnname, 
            (SELECT co.coursetype_id from smc_student_study as d,smc_class as c,smc_course as co where d.class_id=c.class_id and c.course_id=co.course_id and d.study_isreading =1
             and d.student_id = e.student_id and c.school_id = e.school_id and {$uncoursetypewhere}  limit 0,1) as  unstudy_coursetype_id,
            (select GROUP_CONCAT(oo.coursecat_branch) FROM crm_student_intention as ii LEFT JOIN smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=st.student_id ) as course_cnname 
            FROM smc_student_enrolled AS e
            LEFT JOIN smc_student_study as sy On sy.student_id =e.student_id
            LEFT JOIN smc_student as st on st.student_id=e.student_id
            LEFT JOIN smc_student_family as f On f.student_id=st.student_id and f.family_isdefault =1
            LEFT JOIN smc_parenter as p ON p.parenter_id =f.parenter_id
            LEFT JOIN crm_student as ct ON ct.student_id =e.student_id 
            LEFT JOIN smc_class as cl ON sy.class_id = cl.class_id 
            WHERE {$datawhere} and e.school_id='{$this->school_id}' and e.student_id > 0 and e.enrolled_status in (0,1,3)
            AND e.student_id not in (select rt.student_id from crm_student as rt where rt.student_type = 0 and rt.student_distributionstatus = 1 and rt.school_id = '{$this->school_id}')
            GROUP BY e.school_id, e.student_id 
            HAVING {$having} 
            ";
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");
            
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_crmtag'] = $this->LgStringSwitch($dateexcelvar['student_crmtag']);
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['study_coursetype_cnname'] = $dateexcelvar['study_coursetype_cnname'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
//                    if($staffone['account_class'] == '1'){
//                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
//                    }else {
                        $datearray['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['parenter_mobile']);
//                    }
                    $datearray['parenter_email'] = $dateexcelvar['parenter_email'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '标签', '学员编号', '在读班组', '性别', '出生日期', '主要联系人', '主要联系电话', 'Email'));
            $excelfileds = array('student_cnname', 'student_enname', 'student_crmtag', 'student_branch', 'study_coursetype_cnname', 'student_sex', 'student_birthday', 'parenter_cnname', 'parenter_mobile', 'parenter_email' );
            $fielname = $this->LgStringSwitch("在籍学员名单");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }else{
            $sql .= "  limit {$page},{$num} ";
            $datalist = $this->DataControl->selectClear($sql);
        }

        if (!$datalist) {
            $datalist = array();
        }else{
            foreach ($datalist as &$value){
                if ($value['student_crmtag']) {
                    $value['student_crmtag'] = explode(',', $this->LgStringSwitch($value['student_crmtag']));
                } else {
                    $value['student_crmtag'] = array();
                }
                if(!is_null($value['course_cnname'])){
                    $value['course_cnname'] = explode(',', $value['course_cnname']);
                }else{
                    $value['course_cnname'] = array();
                }

                if($this->isGmcPost == true){
                    $value['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['parenter_mobile']);
                }
            }
        }

        $data_numList = $this->DataControl->selectClear("  SELECT e.student_id,
            (SELECT group_concat(DISTINCT t.coursetype_cnname) from smc_student_study as d,smc_class as c,smc_course as co,smc_code_coursetype as t where d.class_id=c.class_id and c.course_id =co.course_id and co.coursetype_id=t.coursetype_id
             and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and d.student_id = e.student_id and c.school_id = e.school_id and {$coursetypewhere} )  as  study_coursetype_cnname,
            (SELECT group_concat(DISTINCT c.class_cnname) from smc_student_study as d,smc_class as c where d.class_id=c.class_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and {$classwhere}) as study_class_cnname, 
            (SELECT co.coursetype_id from smc_student_study as d,smc_class as c,smc_course as co where d.class_id=c.class_id and c.course_id=co.course_id and d.study_isreading =1
             and d.student_id = e.student_id and c.school_id = e.school_id and {$uncoursetypewhere}  limit 0,1) as  unstudy_coursetype_id   
            FROM smc_student_enrolled as e
            LEFT JOIN smc_student_study as sy On sy.student_id =e.student_id
            LEFT JOIN smc_student as st on st.student_id=e.student_id
            LEFT JOIN smc_student_family as f On f.student_id=st.student_id and f.family_isdefault =1
            LEFT JOIN smc_parenter as p ON p.parenter_id =f.parenter_id
            LEFT JOIN crm_student as ct ON ct.student_id =e.student_id
            LEFT JOIN smc_class as cl ON sy.class_id = cl.class_id 
            WHERE {$datawhere} and e.school_id='{$this->school_id}' and e.student_id > 0 and e.enrolled_status in (0,1,3) 
            AND e.student_id not in (select student_id from crm_student as rt where rt.student_type = 0 and student_distributionstatus = 1)
            GROUP BY e.school_id, e.student_id 
            HAVING {$having}");

        $allnum = is_array($data_numList) ? count($data_numList) : 0;

        $result = array();
        $result['list'] = $datalist;
        $result['all_num'] = $allnum;
        return $result;
    }

    /**
     * 获取在籍学员列表
     * author: 97 复制
     * 对应接口文档 0001
     */
    function getCrmStudentExpandList($paramArray)
    {
        $datawhere = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword']) {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%'  or st.student_enname like '%{$paramArray['keyword']}%' or p.parenter_cnname like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_student_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=st.student_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        $having = '1=1';
        $uncoursetypewhere = 'co.coursetype_id >0';
        // 非在读班组
        if (isset($paramArray['unstudy_coursetype_id']) && $paramArray['unstudy_coursetype_id']) {
            $uncoursetypewhere .= " and co.coursetype_id  = '{$paramArray['unstudy_coursetype_id']}'";
            $having .= " and unstudy_coursetype_id is  Null";
        }

        //在读班组
        $coursetypewhere = '1';
        if (isset($paramArray['study_coursetype_id']) && $paramArray['study_coursetype_id']) {
            $coursetypewhere .= " and t.coursetype_id='{$paramArray['study_coursetype_id']}'";
            $having .= " and study_coursetype_cnname is not Null";
        }
        //在读班级
        $classwhere = '1';
        if (isset($paramArray['study_class_id']) && $paramArray['study_class_id']) {
            $classwhere .= " and c.class_id='{$paramArray['study_class_id']}'";
            $having .= " and study_class_cnname is not Null";
        }
        if (isset($paramArray['p'])) {
            $p = $paramArray['p'];
        } else {
            $p = 1;
        }

        if (isset($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = 10;
        }
        $page = ($p - 1) * $num;

        $sql = "
            SELECT e.student_id,st.student_crmtag,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,st.student_birthday,p.parenter_mobile,p.parenter_cnname, '0' as student_type,p.parenter_email,
            (SELECT group_concat(DISTINCT t.coursetype_cnname) from smc_student_study as d,smc_class as c,smc_course as co,smc_code_coursetype as t where d.class_id=c.class_id and c.course_id =co.course_id
             and co.coursetype_id=t.coursetype_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and d.student_id = e.student_id and c.school_id = e.school_id and {$coursetypewhere} )  as  study_coursetype_cnname, 
            (SELECT group_concat(DISTINCT c.class_cnname) from smc_student_study as d,smc_class as c where d.class_id=c.class_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and {$classwhere}) as study_class_cnname, 
            (SELECT co.coursetype_id from smc_student_study as d,smc_class as c,smc_course as co where d.class_id=c.class_id and c.course_id=co.course_id and d.study_isreading =1
             and d.student_id = e.student_id and c.school_id = e.school_id and {$uncoursetypewhere}  limit 0,1) as  unstudy_coursetype_id,
            (select GROUP_CONCAT(oo.coursecat_branch) FROM crm_student_intention as ii LEFT JOIN smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=st.student_id ) as course_cnname 
            FROM smc_student_enrolled AS e
            LEFT JOIN smc_student_study as sy On sy.student_id =e.student_id
            LEFT JOIN smc_student as st on st.student_id=e.student_id
            LEFT JOIN smc_student_family as f On f.student_id=st.student_id and f.family_isdefault =1
            LEFT JOIN smc_parenter as p ON p.parenter_id =f.parenter_id
            LEFT JOIN crm_student as ct ON ct.student_id =e.student_id 
            LEFT JOIN crm_student_expand_tips as et ON ct.student_id = et.student_id 
            WHERE {$datawhere} and e.school_id='{$this->school_id}' and e.student_id > 0  and e.enrolled_status in (0,1,3)
            AND e.student_id not in (select student_id from crm_student as rt where rt.student_type = 0 and student_distributionstatus = 1) 
            and et.student_id > 0 and ct.student_distributionstatus = 0 and et.school_id = e.school_id 
            GROUP BY e.school_id, e.student_id 
            HAVING {$having} 
            ";
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_crmtag'] = $this->LgStringSwitch($dateexcelvar['student_crmtag']);
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['study_coursetype_cnname'] = $dateexcelvar['study_coursetype_cnname'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
//                    if($staffone['account_class'] == '1'){
//                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
//                    }else {
                        $datearray['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['parenter_mobile']);
//                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '标签', '学员编号', '在读班组', '性别', '出生日期', '主要联系人', '主要联系电话'));
            $excelfileds = array('student_cnname', 'student_enname', 'student_crmtag', 'student_branch', 'study_coursetype_cnname', 'student_sex', 'student_birthday', 'parenter_cnname', 'parenter_mobile' );
            $fielname = $this->LgStringSwitch("在籍学员名单");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }else{
            $sql .= "  limit {$page},{$num} ";
            $datalist = $this->DataControl->selectClear($sql);
        }

        if (!$datalist) {
            $datalist = array();
        }else{
            foreach ($datalist as &$value){
                if ($value['student_crmtag']) {
                    $value['student_crmtag'] = explode(',', $this->LgStringSwitch($value['student_crmtag']));
                } else {
                    $value['student_crmtag'] = array();
                }
                if(!is_null($value['course_cnname'])){
                    $value['course_cnname'] = explode(',', $value['course_cnname']);
                }else{
                    $value['course_cnname'] = array();
                }

                if($this->isGmcPost == true){
                    $value['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['parenter_mobile']);
                }
            }
        }

        $data_numList = $this->DataControl->selectClear("  SELECT e.student_id,
            (SELECT group_concat(DISTINCT t.coursetype_cnname) from smc_student_study as d,smc_class as c,smc_course as co,smc_code_coursetype as t where d.class_id=c.class_id and c.course_id =co.course_id and co.coursetype_id=t.coursetype_id
             and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and d.student_id = e.student_id and c.school_id = e.school_id and {$coursetypewhere} )  as  study_coursetype_cnname,
            (SELECT group_concat(DISTINCT c.class_cnname) from smc_student_study as d,smc_class as c where d.class_id=c.class_id and d.study_isreading =1 and d.student_id = e.student_id and d.school_id=e.school_id and {$classwhere}) as study_class_cnname, 
            (SELECT co.coursetype_id from smc_student_study as d,smc_class as c,smc_course as co where d.class_id=c.class_id and c.course_id=co.course_id and d.study_isreading =1
             and d.student_id = e.student_id and c.school_id = e.school_id and {$uncoursetypewhere}  limit 0,1) as  unstudy_coursetype_id   
            FROM smc_student_enrolled as e
            LEFT JOIN smc_student_study as sy On sy.student_id =e.student_id
            LEFT JOIN smc_student as st on st.student_id=e.student_id
            LEFT JOIN smc_student_family as f On f.student_id=st.student_id and f.family_isdefault =1
            LEFT JOIN smc_parenter as p ON p.parenter_id =f.parenter_id
            LEFT JOIN crm_student as ct ON ct.student_id =e.student_id
            LEFT JOIN crm_student_expand_tips as et ON ct.student_id = et.student_id 
            WHERE {$datawhere} and e.school_id='{$this->school_id}' and e.student_id > 0 and e.enrolled_status in (0,1,3)
            AND e.student_id not in (select student_id from crm_student as rt where rt.student_type = 0 and student_distributionstatus = 1)
            and et.student_id > 0 and ct.student_distributionstatus = 0 
            GROUP BY e.school_id, e.student_id 
            HAVING {$having}");

        $allnum = is_array($data_numList) ? count($data_numList) : 0;

        $result = array();
        $result['list'] = $datalist;
        $result['all_num'] = $allnum;
        return $result;
    }

    /**
     * CRM在籍流失学员
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function getCrmLossStudentList($paramArray)
    {
        $datawhere = "scl.stuchange_code in ('C02') and scl.school_id='{$paramArray['school_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword']) {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%' or p.parenter_cnname like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_student_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=s.student_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }

        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and scl.changelog_day<='{$paramArray['end_time']}'";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and scl.changelog_day>='{$paramArray['start_time']}'";
        }
        if (isset($paramArray['p'])) {
            $p = $paramArray['p'];
        } else {
            $p = 1;
        }

        if (isset($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = 10;
        }
        $page = ($p - 1) * $num;

        $sql = "select s.student_id,s.student_crmtag,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_birthday,p.parenter_mobile,p.parenter_cnname,scl.changelog_day,scl.changelog_note,FROM_UNIXTIME(e.enrolled_createtime,'%Y-%m-%d %H:%i:%S') as enrolled_createtime,'1' as student_type,(select GROUP_CONCAT(oo.coursecat_branch) FROM crm_student_intention as ii LEFT JOIN smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=s.student_id ) as course_cnname,p.parenter_email  
                from smc_student_changelog as scl
                LEFT JOIN smc_student_change as sc on sc.change_pid=scl.change_pid
                LEFT JOIN smc_student as s on s.student_id=scl.student_id
                LEFT JOIN smc_student_family as f On f.student_id=s.student_id and f.family_isdefault =1
                LEFT JOIN smc_parenter as p ON p.parenter_id =f.parenter_id
                LEFT JOIN smc_student_enrolled as e ON e.student_id =scl.student_id and e.school_id =scl.school_id
                where {$datawhere} AND scl.student_id not in (select student_id from crm_student as rt where rt.student_type = 1 and student_distributionstatus =1)
                group by scl.changelog_id
                limit {$page},{$num}
                ";

        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        }else{
            foreach ($dataList as &$value){
                if ($value['student_crmtag']) {
                    $value['student_crmtag'] = explode(',', $this->LgStringSwitch($value['student_crmtag']));
                } else {
                    $value['student_crmtag'] = array();
                }
                if(!is_null($value['course_cnname'])){
                    $value['course_cnname'] = explode(',', $value['course_cnname']);
                }else{
                    $value['course_cnname'] = array();
                }

                if($this->isGmcPost == true){
                    $value['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['parenter_mobile']);
                }
            }
        }

        $countList = $this->DataControl->selectClear("select scl.changelog_id,s.student_id,scl.changelog_note
                from smc_student_changelog as scl
                LEFT JOIN smc_student_change as sc on sc.change_pid=scl.change_pid
                LEFT JOIN smc_student as s on s.student_id=scl.student_id
                LEFT JOIN smc_student_family as f On f.student_id=s.student_id and f.family_isdefault =1
                LEFT JOIN smc_parenter as p ON p.parenter_id =f.parenter_id
                where {$datawhere} AND scl.student_id not in (select student_id from crm_student as rt where rt.student_type =1 and student_distributionstatus =1 )
                group by scl.changelog_id
                ");
        $allnum = is_array($countList) ? count($countList) : 0;
//        if ($countList) {
//            $logNoteList = array_values(array_unique(array_column($countList, "changelog_note", 's.changelog_id')));
//        } else {
//            $logNoteList = array();
//        }

        $result = array();
        $result['list'] = $dataList;
//        $result['note_list'] = $logNoteList;
        $result['all_num'] = $allnum;
        return $result;
    }


    /**
     * 获取去跟进列表 在籍 流失
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return array
     */
    function getCrmStudyStuTrackList($paramArray)
    {
        $datawhere = " p.school_id='{$paramArray['school_id']}' and p.principal_leave = 0 and t.student_id > 0";

        if (isset($paramArray['postbe_crmuserlevel']) && $paramArray['postbe_crmuserlevel'] <> 1) {
            $datawhere .= " and (m.marketer_id = '{$paramArray['marketer_id']}' or fm.marketer_id = '{$paramArray['marketer_id']}') ";
        }

        if (isset($paramArray['student_type']) && $paramArray['student_type'] !== '') {
            $datawhere .= " and rt.student_type='{$paramArray['student_type']}'";
        } else {
            $datawhere .= " and rt.student_type= 0";
        }
        if (isset($paramArray['intention_level']) && $paramArray['intention_level'] !== '') {
            $datawhere .= " and rt.student_intention_level='{$paramArray['intention_level']}'";
        }
        if (isset($paramArray['student_tracestatus']) && $paramArray['student_tracestatus'] == '0') {
            $datawhere .= " and rt.student_tracestatus='0'";
        } elseif (isset($paramArray['student_tracestatus']) && $paramArray['student_tracestatus'] == '1') {
            $datawhere .= " and rt.student_tracestatus <>'0'";
        } elseif (isset($paramArray['student_tracestatus']) && $paramArray['student_tracestatus'] == '2') {
            $datawhere .= " and exists (select 1 from crm_student_invite as i where i.student_id=rt.student_id and i.school_id='{$paramArray['school_id']}')";//and i.invite_isvisit=1
        } elseif (isset($paramArray['student_tracestatus']) && $paramArray['student_tracestatus'] == '3') {
            $datawhere .= " and exists (select 1 from crm_student_audition as a where a.student_id=rt.student_id and a.school_id='{$paramArray['school_id']}')";//and a.audition_isvisit=1
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword']) {
            $datawhere .= " and (t.student_cnname like '%{$paramArray['keyword']}%' or t.student_enname like '%{$paramArray['keyword']}%' or t.student_branch like '%{$paramArray['keyword']}%' or pt.parenter_cnname like '%{$paramArray['keyword']}%' or pt.parenter_mobile like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_student_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=t.student_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }

        if (isset($paramArray['principal_ismajor']) && $paramArray['principal_ismajor'] !== '') {
            $datawhere .= " and p.principal_ismajor = '{$paramArray['principal_ismajor']}' ";
        }
        if (isset($paramArray['principal_marketer_id']) && $paramArray['principal_marketer_id'] !== '') {
            $datawhere .= " and p.marketer_id = '{$paramArray['principal_marketer_id']}' ";
        }

        $having = '1=1';
        $uncoursetypewhere = 'co.coursetype_id >0';
        // 非在读班组
        if (isset($paramArray['unstudy_coursetype_id']) && $paramArray['unstudy_coursetype_id']) {
            $uncoursetypewhere .= " and co.coursetype_id  = '{$paramArray['unstudy_coursetype_id']}'";
            $having .= " and unstudy_coursetype_id is Null";
        }
        //在读班组
        if (isset($paramArray['study_coursetype_id']) && $paramArray['study_coursetype_id']) {
            $datawhere .= " and tp.coursetype_id='{$paramArray['study_coursetype_id']}'";
        }
        //跟进班组
        $trackwhere = '1';
        if (isset($paramArray['track_coursetype_id']) && $paramArray['track_coursetype_id']) {
            $trackwhere .= " and k.coursetype_id='{$paramArray['track_coursetype_id']}'";
            $having .= " and track_coursetype_cnname is not Null";
        }
        //流失时间
        if (isset($paramArray['changelog_startday']) && $paramArray['changelog_startday']) {
            $datawhere .= " and cg.changelog_day>='{$paramArray['changelog_startday']}'";
        }
        if (isset($paramArray['changelog_endday']) && $paramArray['changelog_endday']) {
            $datawhere .= " and cg.changelog_day<='{$paramArray['changelog_endday']}'";
        }
        //分配时间
        if (isset($paramArray['allot_starttime']) && $paramArray['allot_starttime']) {
            $principal_starttime = strtotime($paramArray['allot_starttime']);
            $datawhere .= " and p.principal_createtime>='{$principal_starttime}'";
        }
        if (isset($paramArray['allot_endtime']) && $paramArray['allot_endtime']) {
            $principal_endtime = strtotime($paramArray['allot_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and p.principal_createtime<='{$principal_endtime}'";
        }
        //最后跟踪时间
        if (isset($paramArray['track_starttime']) && $paramArray['track_starttime']) {
            $trackstarttime = strtotime($paramArray['track_starttime']);
            $trackwhere .= " and k.track_createtime>='{$trackstarttime}'";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime']) {
            $trackendtime = strtotime($paramArray['track_endtime']) + 24 * 60 * 60 - 1;
            $trackwhere .= " and k.track_createtime<='{$trackendtime}'";
            $having .= " and track_createtime is not Null";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $tracestatus_array = array(0 => "待跟踪", 1 => '持续跟踪', 2 => '已柜询', 3 => '已试听');

        $sql = "
    select t.student_id,t.student_crmtag,t.student_cnname,t.student_enname,t.student_branch,t.student_sex,t.student_birthday,rt.student_tracestatus,rt.student_distributionstatus,pt.parenter_mobile,pt.parenter_cnname,student_intention_level,pt.parenter_email,
    concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )  as zhu_marketer_name,
    group_concat( DISTINCT concat( fm.marketer_name,(CASE WHEN ifnull( sr.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sr.staffer_enname ) END )  ) ) as fu_marketer_name,
    FROM_UNIXTIME(p.principal_createtime) as principal_createtime,FROM_UNIXTIME(e.enrolled_createtime,'%Y-%m-%d %H:%i:%S') as enrolled_createtime,group_concat(DISTINCT cg.changelog_note) as changelog_note ,group_concat(DISTINCT cg.changelog_day) as changelog_day,
    (SELECT co.coursetype_id from smc_student_study as d,smc_class as c,smc_course as co where d.class_id=c.class_id and c.course_id=co.course_id and d.study_isreading =1  and d.school_id=p.school_id and d.student_id=p.student_id and {$uncoursetypewhere} limit 0,1) as  unstudy_coursetype_id,
    (SELECT group_concat(DISTINCT ty.coursetype_cnname) from smc_student_study as d,smc_class as c,smc_course as co,smc_code_coursetype as ty where d.class_id=c.class_id and c.course_id=co.course_id and d.study_isreading =1 and d.school_id=p.school_id and d.student_id=p.student_id and co.coursetype_id=ty.coursetype_id ) as  study_coursetype_cnname,
    (select group_concat( DISTINCT t.coursetype_cnname)  from crm_student_track as k,smc_code_coursetype as t where t.coursetype_id =k.coursetype_id and k.student_id=p.student_id and k.school_id=p.school_id and track_isactive =1 and {$trackwhere}) as  track_coursetype_cnname,
    (select count(k.track_id)  from crm_student_track as k where k.student_id=p.student_id and k.school_id=p.school_id and track_isactive =1 ) as  track_num,
    (select k.track_createtime  from crm_student_track as k where k.student_id=p.student_id and k.school_id=p.school_id and track_isactive =1 and {$trackwhere} order by track_id DESC limit 0,1 ) as  track_createtime,
    (select k.track_note  from crm_student_track as k where k.student_id=p.student_id and k.school_id=p.school_id and track_isactive =1 order by track_id DESC limit 0,1 ) as  track_note, 
    (select GROUP_CONCAT(oo.coursecat_branch) FROM crm_student_intention as ii LEFT JOIN smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=t.student_id ) as course_cnname
    from crm_student_principal as p 
    left join smc_student as t ON t.student_id =p.student_id
    left join crm_student as rt ON rt.student_id=p.student_id 
    left join smc_student_family as f On t.student_id=f.student_id and f.family_isdefault =1
    left join smc_parenter as pt ON pt.parenter_id =f.parenter_id
    left join crm_student_principal as pl On pl.student_id=p.student_id and pl.principal_ismajor =1 and pl.school_id=p.school_id and pl.principal_leave=0
    left join crm_marketer as m ON m.marketer_id = pl.marketer_id 
    left join smc_staffer as sf On sf.staffer_id = m.staffer_id
    left join crm_student_principal as spl On spl.student_id=p.student_id and spl.principal_ismajor =0 and spl.school_id=p.school_id and spl.principal_leave=0
    left join crm_marketer as fm ON fm.marketer_id = spl.marketer_id  
    left join smc_staffer as sr On sr.staffer_id = fm.staffer_id
    left join smc_student_enrolled as e ON e.student_id =p.student_id and e.school_id =p.school_id
    left join smc_student_study as d ON d.student_id=t.student_id and d.school_id=p.school_id and d.study_isreading =1
    left join smc_class as cs ON cs.class_id =d.class_id
    left join smc_course as co ON co.course_id =cs.course_id
    left join smc_code_coursetype as tp ON tp.coursetype_id =co.coursetype_id
    left join smc_student_changelog as cg ON  cg.student_id =p.student_id and cg.stuchange_code ='C02' and cg.school_id=p.school_id
    where {$datawhere}
    group by rt.student_id 
    HAVING {$having}
    ";
        $list_sql = $sql . "  LIMIT {$pagestart},{$num}";

        $studentList = $this->DataControl->selectClear($list_sql);

        if ($studentList) {
            foreach ($studentList as &$value) {
                $value['last_track_note'] = (!is_null($value['track_createtime'])?date('Y-m-d H:i', $value['track_createtime']).' ':'').($value['track_note'] == '' ? '--' : $value['track_note']);
                $value['last_tracktime'] = $value['track_createtime'] == false ? '--' : date("Y-m-d H:i", $value['track_createtime']);
                $value['student_tracestatus_name'] = $tracestatus_array[$value['student_tracestatus']];
                if($this->isGmcPost == true){
                    $value['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['parenter_mobile']);
                }
                if(!is_null($value['course_cnname'])){
                    $value['course_cnname'] = explode(',', $value['course_cnname']);
                }else{
                    $value['course_cnname'] = array();
                }
                if ($value['student_crmtag']) {
                    $value['student_crmtag'] = explode(',', $this->LgStringSwitch($value['student_crmtag']));
                } else {
                    $value['student_crmtag'] = array();
                }
            }
        } else {
            $studentList = array();
        }

        $count_List = $this->DataControl->selectClear($sql);
        $allnum = is_array($count_List) ? count($count_List) : 0;
        $result = array();
        $result['list'] = $studentList;
        $result['allnum'] = $allnum;
        return $result;
    }
}
