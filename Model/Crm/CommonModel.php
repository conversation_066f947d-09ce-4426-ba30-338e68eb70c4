<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class CommonModel extends modelTpl{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();

    public $m;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }
    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    //用户获取token
    function getToken($params=array()){
        $marketerOne = $this->DataControl->getFieldOne('crm_marketer',"marketer_id,staffer_id,marketer_tokencode,marketer_tokenencrypt","marketer_id='{$params['marketer_id']}'");
        if(!$marketerOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($marketerOne["marketer_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $marketerOne["marketer_tokenencrypt"]){
            $token = $marketerOne["marketer_tokenencrypt"];
        }else{
            //目前这里注释是为了测试方便
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE crm_marketer SET marketer_tokencode = '{$tokencode}',marketer_tokenencrypt = '{$md5tokenbar}' WHERE marketer_id ='{$marketerOne['marketer_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }
    //首页 -- 用户登陆
    function marketerLoginApi($paramArray){
//        $schoollogOne = $this->DataControl->selectOne("select school_id
//                      from imc_staffer_schoollog as s ,crm_marketer as m
//                      WHERE s.company_id = '{$paramArray['company_id']}' and s.schoollog_port = '1' and m.staffer_id = '{$paramArray['staffer_id']}'  and s.marketer_id = m.marketer_id ORDER BY schoollog_id DESC limit 0,1 ");
//        if($schoollogOne['school_id']) {
//            $paramArray['school_id'] = $schoollogOne['school_id'];
//        }

        $staffOne = $this->DataControl->getFieldOne("smc_staffer","account_class,staffer_img,staffer_enname","staffer_id = '{$paramArray['staffer_id']}' and company_id = '{$paramArray['company_id']}'");


        $sqlfields = " m.marketer_id,m.company_id,m.staffer_id,m.postrole_id,m.marketer_name,m.marketer_mobile,m.marketer_img,m.marketer_tokenencrypt,p.school_id,p.postbe_crmuserlevel,
        (select s.school_cnname from smc_school as s where s.school_id = p.school_id) as school_name,
        (select s.staffer_sex from smc_staffer as s where s.staffer_id = m.staffer_id and s.company_id = m.company_id) as staffer_sex ";


        $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id,postrole_id","postbe_id = '{$paramArray['re_postbe_id']}'");

        if($staffOne['account_class'] == 1){
            $datawhere = " m.staffer_id = '{$paramArray['staffer_id']}' and  m.marketer_status = 1 ";
        }else {
            if($postbeOne && $postbeOne['school_id'] !== '0') {
                $datawhere = " m.staffer_id = '{$paramArray['staffer_id']}' and p.school_id = '{$postbeOne['school_id']}' and  m.marketer_status = 1 ";
            }else{
                $datawhere = " m.staffer_id = '{$paramArray['staffer_id']}' and  m.marketer_status = 1 ";
            }
        }

        $marketerOne = $this->DataControl->selectOne("select {$sqlfields},g.company_logo
                                                    from crm_marketer as m
                                                    LEFT JOIN gmc_staffer_postbe as p on m.staffer_id = p.staffer_id and m.company_id = p.company_id left join gmc_company as g on g.company_id = m.company_id
                                                   WHERE {$datawhere} limit 0,1");





        $marketerOne['marketer_name'] = $marketerOne['marketer_name'].((isset($staffOne['staffer_enname']) && $staffOne['staffer_enname'] != '')?'-'.$staffOne['staffer_enname']:'');

        if($staffOne['marketer_img'] == ''){
            $marketerOne['marketer_img'] = $staffOne['staffer_img'];
        }
        if($staffOne['account_class'] == 1){
            $schoolOne = $this->DataControl->getFieldOne("smc_school","school_cnname","school_id = '{$paramArray['school_id']}' ");
            $marketerOne['school_id'] = $paramArray['school_id'];
            $marketerOne['school_cnname'] = $schoolOne['school_cnname'];
            $marketerOne['postbe_crmuserlevel'] = '1';
        }

        if($postbeOne['school_id'] == '0'){
            $schoolOne = $this->DataControl->getFieldOne("smc_school","school_cnname","school_id = '{$paramArray['school_id']}' ");
            $marketerOne['school_id'] = $paramArray['school_id'];
            $marketerOne['school_cnname'] = $schoolOne['school_cnname'];

            $postroleOne = $this->DataControl->selectOne(" select postpart_iscrmuser,postpart_crmuserlevel from gmc_company_postrole where postrole_id = '{$postbeOne['postrole_id']}' ");//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场  ********补充
            if($postroleOne['postpart_iscrmuser'] == '1' && isset($postroleOne['postpart_crmuserlevel'])){//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场 ********补充
                $marketerOne['postbe_crmuserlevel'] = $postroleOne['postpart_crmuserlevel'];
            }else {
                if ($marketerOne['postbe_crmuserlevel'] == '2') {
                    $marketerOne['postbe_crmuserlevel'] = '2';
                } else {
                    $marketerOne['postbe_crmuserlevel'] = '1';
                }
            }
        }

        if($marketerOne) {
            $data = array();
            $data['marketer_lasttime'] = time();
            $data['marketer_lastip'] = real_ip();
            $this->DataControl->updateData("crm_marketer", "marketer_id = '{$marketerOne['marketer_id']}'",$data );
            $marketerOne['marketer_tokenencrypt'] = $this->getToken($marketerOne);
        }

        $stafferOne = $this->DataControl->selectOne("select staffer_enname,staffer_branch  from crm_marketer as m,smc_staffer as sf where sf.staffer_id=m.staffer_id and m.marketer_id ='{$marketerOne['marketer_id']}'");
        $marketerOne['staffer_enname'] = $stafferOne["staffer_enname"];
        $marketerOne['staffer_branch'] = $stafferOne["staffer_branch"];
        return $marketerOne;
    }
    //职工首次登陆 -- 添加用户信息
    function addMarketerLoginApi($paramArray){
        $sqlfields = " s.staffer_id,s.company_id,s.postrole_id,s.postlevel_id,s.post_id,s.staffer_tokencode,s.staffer_tokenencrypt,s.staffer_sex,s.staffer_cnname,s.staffer_mobile,s.staffer_img,p.school_id,(select s.school_cnname from smc_school as s where s.school_id = p.school_id) as school_name ";
        $datawhere = " s.staffer_id='{$paramArray['staffer_id']}' ";//and staffer_leave = 0
        $apiuser = $this->DataControl->selectOne("select {$sqlfields},s.staffer_enname
                                                    from smc_staffer as s
                                                    LEFT JOIN gmc_staffer_postbe as p on s.staffer_id = p.staffer_id and s.company_id = p.company_id
                                                    WHERE {$datawhere} limit 0,1");
        $data = array();
        $data["company_id"] =  $apiuser["company_id"];
        $data["staffer_id"] =  $apiuser["staffer_id"];
        $data["postrole_id"] =  $apiuser["postrole_id"];
        $data["marketer_name"] =  $apiuser["staffer_cnname"];
        $data["marketer_mobile"] =  $apiuser["staffer_mobile"];
        $data["marketer_img"] =  $apiuser["staffer_img"];
        $data["marketer_status"] =  '1';
        $tokencode = rand(111111,999999);
        $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
        $data["marketer_tokencode"] =  $tokencode;
        $data["marketer_tokenencrypt"] =  $md5tokenbar;

        $data["marketer_lasttime"] =  time();
        $data["marketer_lastip"] =  real_ip();
        $data["marketer_updatetime"] =  time();
        $data["marketer_createtime"] =  time();
        if($this->DataControl->insertData('crm_marketer',$data)){
            $dataarray = array();
            $dataarray["marketer_id"] =  mysql_insert_id();
            $dataarray["company_id"] =  $data["company_id"];
            $dataarray["staffer_id"] =  $data["staffer_id"];
            $dataarray["postrole_id"] =  $data["postrole_id"];
            $dataarray["marketer_name"] =  $data["marketer_name"];
            $dataarray["marketer_mobile"] =  $data["marketer_mobile"];
            $dataarray["marketer_tokenencrypt"] =  $data["marketer_tokenencrypt"];
            $dataarray["marketer_img"] =  $data["marketer_img"];
            $dataarray["school_id"] =  $apiuser["school_id"];
            $dataarray["school_name"] =  $apiuser["school_name"];
            $dataarray["staffer_sex"] =  $apiuser["staffer_sex"];
            $dataarray["staffer_enname"] =  $apiuser["staffer_enname"];
            return $dataarray;
        }else{
            return false;
        }
    }
    //首页 -- 用户联系人
    function getLinkmanApi($paramArray){
        $sqlfields = " p.staffer_id,s.staffer_img,s.staffer_cnname,s.staffer_enname,s.staffer_sex,s.post_id,(select t.post_name from gmc_company_post as t where t.post_id=s.post_id) as post_name,s.staffer_mobile,s.staffer_branch ";
        $datawhere = " p.school_id = '{$paramArray['school_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_status = 1 and p.postbe_iscrmuser = 1 and s.staffer_id IS NOT NULL ";
        $stafferlist = $this->DataControl->selectClear("select {$sqlfields}
                                                    from gmc_staffer_postbe as p
                                                    LEFT JOIN smc_staffer as s on s.staffer_id = p.staffer_id
                                                    LEFT JOIN crm_marketer as m on p.staffer_id = m.staffer_id and m.marketer_id <> ''
                                                    WHERE {$datawhere}
                                                    GROUP BY p.staffer_id");
        $dataarray = array();
        if($stafferlist) {
            foreach ($stafferlist as $key => $info) {
                $info['post_name'] = ($info['post_name'] == ''?$this->LgStringSwitch('待定'):$info['post_name']);
                $info['post_id'] = ($info['post_id'] == ''?0:$info['post_id']);
                if(funcmtel($info['staffer_mobile'])) {
                    $info['staffer_mobile'] = substr_replace($info['staffer_mobile'], '****', 3, 4);
                }
                $dataarray[$info['post_id']][] = $info;
            }
        }
        return $dataarray;
    }
    //首页 -- 用户联系人
    function getMarketerApi($paramArray){
        $marketerOne = $this->DataControl->selectClear("SELECT concat( m.marketer_name,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )  ) as marketer_name, m.marketer_mobile, s.staffer_img AS marketer_img, s.staffer_sex, c.company_logo, c.company_ismajor,c.company_isassist,c.company_isnointention,
	( SELECT count(n.nearschool_id) FROM crm_code_nearschool AS n WHERE n.school_id = '{$paramArray['school_id']}' AND n.company_id = s.company_id ) AS nearnum
FROM crm_marketer AS m, smc_staffer AS s, gmc_company AS c
WHERE s.staffer_id = m.staffer_id AND s.company_id = c.company_id AND m.marketer_id = '{$paramArray['marketer_id']}'");
        if($marketerOne){
            foreach ($marketerOne as &$marketervar){
                $marketervar['company_logo'] = $marketervar['company_logo']?$marketervar['company_logo'].'?x-oss-process=image/resize,m_lfit,w_300,limit_0/auto-orient,1/quality,q_90 ':'';
                $marketervar['marketer_img'] = $marketervar['marketer_img']?$marketervar['marketer_img'].'?x-oss-process=image/resize,m_lfit,w_200,limit_0/auto-orient,1/quality,q_90 ':'';
                if($marketervar['nearnum'] > 0) {
                    $marketervar['isopennearschool'] = '1';
                }else{
                    $marketervar['isopennearschool'] = '0';
                }
            }
        }

        $schsqltwo = " select b.seats_intentionlevel from crm_merchant_seats_schoolapply as a,crm_merchant_seats as b where a.school_id = '{$paramArray['school_id']}' and a.seats_id = b.seats_id and b.company_id = '{$paramArray['company_id']}' and b.seats_isdel = '0' and b.seats_isopen = '1' and b.seats_isofficial = '1' and b.seats_intentionlevel <> ''  order by a.merchant_id ";
        $merchantTwo = $this->DataControl->selectOne($schsqltwo);

        $marketerOne[0]['seats_intentionlevel'] = $merchantTwo['seats_intentionlevel']?$merchantTwo['seats_intentionlevel']:'';
        return $marketerOne;
    }

    //首页 -- 获取对应职工信息  -- 模块跳转
    function stafferApi($paramArray){
        $sqlfields = " s.staffer_id,s.staffer_tokenencrypt as token ";
        $datawhere = " m.marketer_id = '{$paramArray['marketer_id']}' and m.marketer_tokenencrypt = '{$paramArray['token']}' ";
        $stafferOne = $this->DataControl->selectClear("select {$sqlfields}
                                                    from crm_marketer as m
                                                    LEFT JOIN smc_staffer as s ON m.staffer_id = s.staffer_id
                                                    WHERE {$datawhere}");
        return $stafferOne;
    }

    //首页 -- 职工对应学校
    function getMarketerSchoolApi($paramArray){
        $datawhere =" 1 ";
        $schoollist = $this->DataControl->selectClear(" select s.school_id
                      from crm_marketer as m
                      LEFT JOIN gmc_staffer_postbe as s on m.staffer_id = s.staffer_id and m.company_id = s.company_id
                      where m.marketer_id = '{$paramArray['marketer_id']}' ");
        if($schoollist){
            $schoolstring = '';
            $schoolArray = array();
            foreach($schoollist as $schoollistVar){
                $schoolArray[] = $schoollistVar['school_id'];
            }
            $schoolstring = implode(",",$schoolArray);
            $datawhere .= " and s.school_id in ($schoolstring)  ";
        }else{
            $datawhere .= " and s.school_id in (0)  ";
        }

        $sqlfields = " s.school_id,s.school_cnname,s.school_enname ";
        $dataList = $this->DataControl->selectClear("SELECT  {$sqlfields} FROM smc_school as s WHERE {$datawhere}");
        return $dataList;
    }
    //首页 -- 筛选学校1
    function getSchoolApi($paramArray){
        $datawhere =" s.company_id = '{$paramArray['company_id']}' ";
        if(isset($paramArray['postbe_crmuserlevel'])){
            $datawhere .= " and s.school_type =1";
        }
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%' )";//or c.company_cnname like '%{$paramArray['keyword']}%'
        }
        //区域
        if(isset($paramArray['district_id']) && $paramArray['district_id'] != ''){
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //省份
        if(isset($paramArray['school_province']) && $paramArray['school_province'] != ''){
            $datawhere .= " and s.school_province = '{$paramArray['school_province']}'";
        }
        //城市
        if(isset($paramArray['school_city']) && $paramArray['school_city'] != ''){
            $datawhere .= " and s.school_city = '{$paramArray['school_city']}'";
        }
        //城市
        if(isset($paramArray['school_cnname_initial']) && $paramArray['school_cnname_initial'] != ''){
            $datawhere .= " and s.school_cnname_initial = '{$paramArray['school_cnname_initial']}'";
        }
        //组织
        if(isset($paramArray['organize_id']) && $paramArray['organize_id'] != ''){
            $datawhere .= " and s.school_id in ( SELECT o.school_id FROM gmc_company_organizeschool as o where o.organize_id = '{$paramArray['organize_id']}')";
        }

        $schoolstring = "";
        if(isset($paramArray['staffer_id']) && $paramArray['staffer_id'] !== ''){
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,account_class,company_id", "staffer_id = '{$paramArray['staffer_id']}'");
            if($stafferOne['account_class'] == '0'){
                if(isset($paramArray['re_postbe_id']) && $paramArray['re_postbe_id'] !== ''){
                    $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "school_id,postrole_id,organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
                    if($postbeOne['school_id'] !== '0'){
                        $daonewhere = "p.staffer_id = '{$stafferOne['staffer_id']}' and p.postbe_status = '1' AND p.school_id <> '0'";
                        if(isset($paramArray['marketer_id']) && $paramArray['marketer_id'] !== ''){
                            $daonewhere .= " and p.postbe_iscrmuser = '1'";
                        }
                        $schoollist = $this->DataControl->selectClear("SELECT p.school_id FROM gmc_staffer_postbe AS p WHERE {$daonewhere}");
                      }else{
                        $daonewhere = "p.organize_id = b.organize_id AND b.staffer_id = '{$stafferOne['staffer_id']}' and b.postbe_status = '1' AND b.school_id = '0' and  b.organize_id = '{$postbeOne['organize_id']}'";
                        if(isset($paramArray['marketer_id']) && $paramArray['marketer_id'] !== ''){
                            $postroleOne = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscompanyuser", "postrole_id = '{$paramArray['postrole_id']}'");
                            if($postroleOne['postpart_iscompanyuser'] == '0'){
                                $daonewhere .= " and p.school_id = '0'";
                            }
                        }
                        $schoollist = $this->DataControl->selectClear("SELECT p.school_id FROM gmc_company_organizeschool AS p, gmc_staffer_postbe AS b WHERE {$daonewhere}");
                    }
                }else{
                    $schoollist = $this->DataControl->selectClear("SELECT p.school_id FROM gmc_staffer_postbe AS p WHERE p.staffer_id = '{$stafferOne['staffer_id']}' and p.postbe_status = '1' AND p.school_id <> '0' UNION ALL
SELECT p.school_id FROM gmc_company_organizeschool AS p, gmc_staffer_postbe AS b WHERE p.organize_id = b.organize_id AND b.staffer_id = '{$stafferOne['staffer_id']}' and b.postbe_status = '1' AND b.school_id = '0'");
                }
                if($schoollist){
                    $schoolArray = array();
                    foreach($schoollist as $schoollistVar){
                        $schoolArray[] = $schoollistVar['school_id'];
                    }
                    $schoolstring = implode(",",$schoolArray);
                    $datawhere .= " and s.school_id in ($schoolstring)  ";
                }else{
                    $datawhere .= " and s.school_id in (0)  ";
                }
            }
        }

        //首字母筛选
        $sqlfields = " s.school_cnname_initial ";
        $sql="SELECT  {$sqlfields}
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                WHERE {$datawhere}
                GROUP BY s.school_cnname_initial";
        $initial = $this->DataControl->selectClear($sql);
        $initialarray = array();
        if($initial) {
            foreach ($initial as $initialvar) {
                $initialarray[] = $initialvar['school_cnname_initial'];
            }
        }

        if(isset($paramArray['re_postbe_id'])){
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            if($postbeOne['postrole_id'] == '0'){
                $sqlfields = " s.school_id,s.company_id,s.district_id,s.school_branch,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_shortname,s.school_cnname,s.school_enname,c.company_cnname,s.school_address,s.school_phone  ";
                $sql="SELECT  {$sqlfields},p.postbe_id
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                left join gmc_staffer_postbe as p on p.school_id = s.school_id
                WHERE {$datawhere} and p.staffer_id = '{$paramArray['staffer_id']}' and school_isclose = 0 GROUP BY s.school_id ORDER BY s.school_sort DESC";
                $dataList = $this->DataControl->selectClear($sql);
            }else{
                $sqlfields = " s.school_id,s.company_id,s.district_id,s.school_branch,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_shortname,s.school_cnname,s.school_enname,c.company_cnname,s.school_address,s.school_phone  ";
                $sql="SELECT  {$sqlfields},(select postbe_id from gmc_staffer_postbe WHERE postbe_id = '{$paramArray['re_postbe_id']}') as postbe_id
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                WHERE {$datawhere} and school_isclose = 0 GROUP BY s.school_id ORDER BY s.school_sort DESC";
                $dataList = $this->DataControl->selectClear($sql);
            }
        }else{
            $sqlfields = " s.school_id,s.company_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,c.company_cnname,s.school_address,s.school_phone  ";
            $sql="SELECT  {$sqlfields}
                FROM smc_school as s
                LEFT JOIN gmc_company as c ON s.company_id = c.company_id
                WHERE {$datawhere} and school_isclose = 0 GROUP BY s.school_id ORDER BY s.school_sort DESC";
            $dataList = $this->DataControl->selectClear($sql);
        }


        $dataarray = array();
        if($dataList) {
            foreach ($dataList as $key => $info) {
                $dataarray[$info['company_id']][] = $info;
            }
        }
        $result= array();
        $result['datalist'] = $dataarray;
        $result['initial'] = $initialarray;
        $result['allid'] = $schoolstring;
        return $result;
    }
    //首页 -- 筛选学校机构
    function getSchooOrganizelApi($paramArray){
        if($paramArray['marketer_id'] != '') {
            $schoollist = $this->DataControl->selectClear(" select o.organize_id,o.organize_cnname,o.organize_enname
                      from crm_marketer as m
                      LEFT JOIN gmc_staffer_postbe as s on m.staffer_id = s.staffer_id and m.company_id = s.company_id
                      LEFT JOIN gmc_company_organizeschool as g ON s.school_id = g.school_id
                      LEFT JOIN gmc_company_organize as o ON g.organize_id = o.organize_id
                      where m.marketer_id = '{$paramArray['marketer_id']}'
                      GROUP BY o.organize_id");
        }elseif($paramArray['staffer_id'] != ''){
            $schoollist = $this->DataControl->selectClear(" select o.organize_id,o.organize_cnname,o.organize_enname
                      from smc_staffer as m
                      LEFT JOIN gmc_staffer_postbe as s on m.staffer_id = s.staffer_id and m.company_id = s.company_id
                      LEFT JOIN gmc_company_organizeschool as g ON s.school_id = g.school_id
                      LEFT JOIN gmc_company_organize as o ON g.organize_id = o.organize_id
                      where m.staffer_id = '{$paramArray['staffer_id']}'
                      GROUP BY o.organize_id");
        }else{
            return false;
        }
        return $schoollist;
    }


    //获取某个学校的详细信息（3.0校务系统）
    function getSchoolOneApi($paramArray){

        $sqlfields = " s.school_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,s.school_phone,s.school_address,s.school_province,s.school_city,s.school_area";
        $sql="SELECT  {$sqlfields} FROM smc_school as s WHERE school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}' ";
        $dataList = $this->DataControl->selectClear($sql);

        return $dataList;
    }


    //官网使用 -- 筛选学校
    function getGwSchoolApi($paramArray){
        $datawhere =" 1 and s.school_istest <> '1' and s.school_isclose <> '1'";
        if(isset($paramArray['company_id']) && $paramArray['company_id'] != ''){
            $datawhere .= " and s.company_id = '{$paramArray['company_id']}'";
        }
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%')";
        }
        //区域
        if(isset($paramArray['district_id']) && $paramArray['district_id'] != ''){
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //省份
        if(isset($paramArray['school_province']) && $paramArray['school_province'] != ''){
            $datawhere .= " and s.school_province = '{$paramArray['school_province']}'";
        }else{
            $datawhere .= " and s.school_province = '10'";
        }
        //城市
        if(isset($paramArray['school_city']) && $paramArray['school_city'] != ''){
            $datawhere .= " and s.school_city = '{$paramArray['school_city']}'";
        }
        //城市
        if(isset($paramArray['school_type']) && $paramArray['school_type'] != ''){
            $datawhere .= " and s.school_type = '{$paramArray['school_type']}'";
        }
        //地址
        if(isset($paramArray['school_address']) && $paramArray['school_address'] != ''){
            $datawhere .= " and s.school_address = '{$paramArray['school_address']}'";
        }

        $sqlfields = " s.school_id,s.district_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,s.school_phone,s.school_address ";
        $sql="SELECT  {$sqlfields} FROM smc_school as s WHERE {$datawhere}";
        $dataList = $this->DataControl->selectClear($sql);

        return $dataList;
    }
    //官网使用 -- 筛选学校
    function getGwSchoolAreaApi($paramArray){
        $datawhere = "1 and s.school_istest <> '1' and s.school_isclose <> '1' and s.school_province <> ''";
        if(isset($paramArray['company_id']) && $paramArray['company_id'] != ''){
            $datawhere .= " and s.company_id = '{$paramArray['company_id']}'";
        }
        //城市
        if(isset($paramArray['school_type']) && $paramArray['school_type'] != ''){
            $datawhere .= " and s.school_type = '{$paramArray['school_type']}'";
        }

        $sqlfields = "r.region_id,r.region_name";
        $sql="SELECT {$sqlfields} FROM smc_school as s LEFT JOIN smc_code_region as r ON r.region_id=s.school_province WHERE {$datawhere} GROUP BY s.school_province ORDER BY r.region_sort ASC ";
        $dataList = $this->DataControl->selectClear($sql);
        return $dataList;
    }

    //模块对应的 文档
    function getModuleHandbookApi($paramArray){
        $datawhere = "1";
//        $paramArray['module_id'] = '1';
        //模块 id
        if(isset($paramArray['module_id']) && $paramArray['module_id'] != ''){
            $datawhere .= " and h.module_id = '{$paramArray['module_id']}'";
        }
        if(isset($paramArray['module_type']) && $paramArray['module_type'] == '0'){//文本
            $sqlfields = "H.handbook_id,H.module_id,H.handbook_name,H.handbook_note";
        }elseif(isset($paramArray['module_type']) && $paramArray['module_type'] == '1'){//视频
            $sqlfields = "H.handbook_id,H.module_id,H.handbook_name,H.handbook_videourl ";
        }
        $sql="SELECT {$sqlfields} FROM imc_module_handbook as h WHERE {$datawhere} ORDER BY h.handbook_weight ASC ,h.handbook_id DESC ";
        $dataList = $this->DataControl->selectClear($sql);
        return $dataList;
    }

    //模块对应的 文档
    function getModuleHandbookOneApi($paramArray){
        $datawhere = "1";
//        $paramArray['module_id'] = '1';
        //模块 id
        if(isset($paramArray['module_id']) && $paramArray['module_id'] != ''){
            $datawhere .= " and h.module_id = '{$paramArray['module_id']}'";
        }
        //模块培训教程 id
        if(isset($paramArray['handbook_id']) && $paramArray['handbook_id'] != ''){
            $datawhere .= " and h.handbook_id = '{$paramArray['handbook_id']}'";
        }
        $sqlfields = "H.handbook_id,H.module_id,H.handbook_name,H.handbook_note ";
        $sql="SELECT {$sqlfields} FROM imc_module_handbook as h WHERE {$datawhere}";
        $dataList = $this->DataControl->selectClear($sql);
        return $dataList;
    }

    //第三方录入地推人员名单
    function addPromotionAction($paramArray){
        if($paramArray['promotion_name'] == ''){
            $this->error = 1;
            $this->errortip = "推广人姓名不能为空";
            return false;
        }elseif($paramArray['promotion_jobnumber'] == ''){
            $this->error = 1;
            $this->errortip = "推广人工号不能为空";
            return false;
        }elseif($paramArray['promotion_mobile'] == ''){
            $this->error = 1;
            $this->errortip = "推广人电话不能为空";
            return false;
        }else{
            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_jobnumber,promotion_mobile"
                , "company_id = '{$paramArray['company_id']}' and (promotion_jobnumber = '{$paramArray['promotion_jobnumber']}' or promotion_mobile = '{$paramArray['promotion_mobile']}')");
            if(!$promotionOne){
                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['school_id'] = $paramArray['school_id'];
                $data['marketer_id'] = $paramArray['marketer_id'];
                $data['promotion_name'] = $paramArray['promotion_name'];
                $data['promotion_mobile'] = $paramArray['promotion_mobile'];
                $data['promotion_jobnumber'] = $paramArray['promotion_jobnumber'];
                $data['promotion_bankcard'] = $paramArray['promotion_bankcard'];
                $data['promotion_bank'] = $paramArray['promotion_bank'];
                $data['promotion_type'] = $paramArray['promotion_type']; //0 市场  1 销售
                $data['promotion_createtime'] = time();
                $this->DataControl->insertData('crm_ground_promotion', $data);
            }else{
                //判断手机号码是否存在
                if($this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_jobnumber,promotion_mobile"
                    , "company_id = '{$paramArray['company_id']}' and promotion_mobile = '{$paramArray['promotion_mobile']}'")){
                    //编号不存在
                    if(!$this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_jobnumber,promotion_mobile"
                        , "company_id = '{$paramArray['company_id']}' and promotion_jobnumber = '{$paramArray['promotion_jobnumber']}' AND promotion_mobile <> '{$paramArray['promotion_mobile']}'")){
                        $data = array();
                        $data['promotion_jobnumber'] = $paramArray['promotion_jobnumber'];
                        $data['promotion_name'] = $paramArray['promotion_name'];
                        $data['promotion_bankcard'] = $paramArray['promotion_bankcard'];
                        $data['promotion_bank'] = $paramArray['promotion_bank'];
                        $data['promotion_type'] = $paramArray['promotion_type']; //0 市场  1 销售
                        $data['promotion_updatetime'] = time();
                        $this->DataControl->updateData('crm_ground_promotion',"company_id = '{$paramArray['company_id']}' and promotion_mobile = '{$paramArray['promotion_mobile']}'", $data);
                    }else{
                        $this->error = 1;
                        $this->errortip = "此地推编号已被其他手机号使用，请检查信息是否正确！";
                        return false;
                    }
                }else{
                    //编号存在
                    if($this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_jobnumber,promotion_mobile"
                        , "company_id = '{$paramArray['company_id']}' and promotion_jobnumber = '{$paramArray['promotion_jobnumber']}'")){
                        $data = array();
                        $data['promotion_mobile'] = $paramArray['promotion_mobile'];
                        $data['promotion_name'] = $paramArray['promotion_name'];
                        $data['promotion_bankcard'] = $paramArray['promotion_bankcard'];
                        $data['promotion_bank'] = $paramArray['promotion_bank'];
                        $data['promotion_type'] = $paramArray['promotion_type']; //0 市场  1 销售
                        $data['promotion_updatetime'] = time();
                        $this->DataControl->updateData('crm_ground_promotion',"company_id = '{$paramArray['company_id']}' and promotion_jobnumber = '{$paramArray['promotion_jobnumber']}'", $data);
                    }else{
                        $this->error = 1;
                        $this->errortip = "异常信息，不存在此情况！";
                        return false;
                    }
                }
            }
            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_jobnumber,promotion_mobile"
                , "company_id = '{$paramArray['company_id']}'  AND promotion_mobile = '{$paramArray['promotion_mobile']}'");

            if(isset($paramArray['api_school_id']) && $paramArray['api_school_id'] != '0' && $paramArray['api_school_id'] != ''){//多个学校 ID 的处理
                $schoolids = explode(',',$paramArray['api_school_id']);
                foreach ($schoolids as $schoolidvar){
                    $list = array();
                    $list['company_id'] = $paramArray['company_id'];
                    $list['school_id'] = $schoolidvar;
                    $list['promotion_id'] = $promotionOne['promotion_id'];
                    $this->DataControl->insertData('crm_ground_promotion_open', $list);
                }
            }else {
                if ($paramArray['school_id'] !== '0') {
                    $list = array();
                    $list['company_id'] = $paramArray['company_id'];
                    $list['school_id'] = $paramArray['school_id'];
                    $list['promotion_id'] = $promotionOne['promotion_id'];
                    $this->DataControl->insertData('crm_ground_promotion_open', $list);
                }
            }

            $this->addCrmWorkLog($paramArray['company_id'], $paramArray['school_id'], $paramArray['marketer_id'], "第三方对接录入地推名单", '录入地推名单', dataEncode($paramArray));
            $this->error = 0;
            $this->errortip = "地推人员添加/更新成功";
            return true;
        }
    }

    //地推宝  获取学校名单 （除了早教托育）
    function getAllSchoolApi($paramArray){
        $datawhere = " company_id = '{$paramArray['company_id']}' and school_isclose = '0' and school_istest = '0' and school_type = '1' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%' )";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sqlfields = " s.school_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,g.region_name as pre_name,i.region_name as city_name,n.region_name as area_name ";
        $sql="SELECT {$sqlfields} FROM smc_school as s
LEFT JOIN smc_code_region AS g ON s.school_province = g.region_id
LEFT JOIN smc_code_region AS i ON s.school_city = i.region_id
LEFT JOIN smc_code_region AS n ON s.school_area = n.region_id
WHERE {$datawhere} limit {$pagestart},{$num} ";
        $dataList = $this->DataControl->selectClear($sql);

        //统计总数
        $allnum = $this->DataControl->selectOne("select count(s.school_branch) as allnum FROM smc_school as s WHERE {$datawhere}");

        $fieldstring = array('school_id','school_branch', 'school_shortname', 'school_cnname', 'school_enname', 'pre_name', 'city_name', 'area_name');
        $fieldname = array('学校ID','学校编号', '学校简称', '学校中文名', '学校英文名', '省', '市', '区');
        $fieldcustom = array("1","1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1","1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if($dataList){
            $result = array();
            $result['allnum'] = $allnum['allnum'];
            $result["field"] = $field;
            $result['list'] = $dataList;

            $this->error = 0;
            $this->errortip = "学校信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $result = array();
            $result['allnum'] = $allnum['allnum'];
            $result["field"] = $field;
            $result['list'] = array();

            $this->error = 1;
            $this->errortip = "学校信息获取失败";
            $this->result = $result;
            return false;
        }
    }




    public function __call($method, $args) {

    }
}
