<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  ReportModel extends modelTpl
{
    public $m;
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $company_isassist = 0;
    public $isGmcPost = null;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'], $publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    /**
     * 招生咨询明细表
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/28 0028  此日期修改
     * @param $request
     * @return mixed
     */
    function schoolMothReport($request)
    {
        $datawhere = " cs.school_id ='{$request['school_id']}' and cs.is_enterstatus=1";
        $trcwhere = "1 and t.track_isactive =1";
        $Having = "1=1";
        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $stattime = strtotime($request['starttime']);
            $trcwhere .= " and t.track_createtime >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']));
            $endime = strtotime($endime) + 60 * 60 * 24 - 1;
            $trcwhere .= " and t.track_createtime <='{$endime}'";
        }

        if (isset($request['is_invite']) && $request['is_invite'] !== "") {
            if ($request['is_invite'] == 1) {
                $Having .= " and (is_invite >0 or is_audition > 0)";
            } else {
                $Having .= " and (is_invite is null and is_audition is null)";
            }
        }
        if (isset($request['is_invite_arrive']) && $request['is_invite_arrive'] !== "") {
            if ($request['is_invite_arrive'] == 1) {
                $Having .= " and (is_invite_arrive >0 or is_audition_arrive > 0)";
            } else {
                $Having .= " and (is_invite_arrive is null and is_audition_arrive is null  )";
            }
        }

        if (isset($request['client_source']) && $request['client_source'] !== "") {
            $datawhere .= " and c.client_source ='{$request['client_source']}'";
        }
        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus = '{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['channel_id']) && $request['channel_id'] !== "") {
            $datawhere .= " and c.channel_id ='{$request['channel_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            select q.client_id,q.school_id,q.marketer_id,c.client_intention_level,c.client_intention_maxlevel,q.track_createtime,q.track_note,
            c.client_cnname,c.client_enname,c.client_createtime,c.client_tracestatus,c.client_mobile,
        c.client_address,c.client_source,c.client_sponsor,
        ch.channel_name,
        concat(stf.staffer_cnname,(CASE WHEN ifnull( stf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', stf.staffer_enname ) END)) as marketer_name,
        (select i.invite_id from crm_client_invite as i where i.school_id=cs.school_id and i.client_id=c.client_id order by i.invite_id DESC LIMIT 0,1) as is_invite,
         (select i.invite_isvisit from crm_client_invite as i where i.school_id=cs.school_id and i.client_id=c.client_id and invite_isvisit=1 order by i.invite_id DESC LIMIT 0,1) as is_invite_arrive,
         (select a.audition_id from crm_client_audition as a where a.school_id=cs.school_id and a.client_id=c.client_id order by a.audition_id DESC Limit 0,1 ) as is_audition,
         (select a.audition_isvisit from crm_client_audition as a where a.school_id=cs.school_id and a.client_id=c.client_id and audition_isvisit =1 order by a.audition_id DESC Limit 0,1 ) as is_audition_arrive,
        (select group_concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) from crm_client_principal as cp,crm_marketer as m, smc_staffer as sf where m.staffer_id=sf.staffer_id and m.marketer_id =cp.marketer_id and  cp.client_id =c.client_id and cp.client_id =c.client_id  and cp.school_id =cs.school_id and principal_ismajor =1 and principal_leave =0 ) as  main_marketer_name,
        (select group_concat(concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) )) from crm_client_principal as cp,crm_marketer as m, smc_staffer as sf  where sf.staffer_id =m.staffer_id and  m.marketer_id =cp.marketer_id and  cp.client_id =c.client_id and cp.client_id =c.client_id  and cp.school_id=cs.school_id and principal_ismajor =0 and principal_leave =0  ) as  fu_marketer_name
            from (SELECT t.client_id,t.school_id,t.marketer_id,t.track_intention_level,t.track_createtime,t.track_note
            FROM crm_client_track as t WHERE t.school_id = '{$request['school_id']}' AND {$trcwhere} ORDER BY t.track_createtime DESC) as q
            left join crm_client as c ON c.client_id=q.client_id
            left join crm_client_schoolenter as cs On cs.client_id=c.client_id
            left join crm_marketer as m ON m.marketer_id=q.marketer_id
            left join smc_staffer as stf ON stf.staffer_id=m.staffer_id
            left join crm_code_channel as ch ON ch.channel_id=c.channel_id
            where  {$datawhere} 
            group by q.client_id
            HAVING {$Having}
            order by q.track_createtime DESC
        ";
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as $key => $val) {
                $dateexcelarray[$key]['client_createtime'] = date("Y-m-d", $val['client_createtime']);
                $dateexcelarray[$key]['track_createtime'] = date("Y-m-d", $val['track_createtime']);
                $dateexcelarray[$key]['client_tracestatus'] = $clientTracestatus[$val['client_tracestatus']];
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_id'] = $dateexcelvar['client_id'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    if ($request['re_postbe_id'] > 0) {
                        $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    } else {
                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    }
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['is_invite'] = $dateexcelvar['is_invite'] > 0 ? '是' : '否';
                    $datearray['is_invite_arrive'] = $dateexcelvar['is_invite_arrive'] == 1 ? '是' : '否';
                    $datearray['is_audition'] = $dateexcelvar['is_audition'] > 0 ? '是' : '否';
                    $datearray['is_audition_arrive'] = $dateexcelvar['is_audition_arrive'] == 1 ? '是' : '否';
                    $datearray['main_marketer_name'] = $dateexcelvar['main_marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");

            $excelheader = $this->LgArraySwitch(array('会员序号', '中文名', '英文名', '手机号', '客户状态', '渠道类型', '渠道明细', '意向星级', '名单创建时间', '是否邀约柜询', '是否柜询到访', '是否邀约试听', '是否试听到访', '主招教师', '辅招教师', '最后跟踪时间', '最后跟踪教师', '最后跟踪内容'));
            $excelfileds = array('client_id', 'client_cnname', 'client_enname', 'client_mobile', 'client_tracestatus', 'client_source', 'channel_name', 'client_intention_level', 'client_createtime', 'is_invite', 'is_invite_arrive', 'is_audition', 'is_audition_arrive', 'main_marketer_name', 'fu_marketer_name', 'track_createtime', 'marketer_name', 'track_note');
            if ($this->company_isassist == 1) {
                $excelheader = $this->LgArraySwitch(array('会员序号', '中文名', '英文名', '手机号', '客户状态', '渠道类型', '渠道明细', '意向星级', '名单创建时间', '是否邀约柜询', '是否柜询到访', '是否邀约试听', '是否试听到访', '主招教师', '最后跟踪时间', '最后跟踪教师', '最后跟踪内容'));
                $excelfileds = array('client_id', 'client_cnname', 'client_enname', 'client_mobile', 'client_tracestatus', 'client_source', 'channel_name', 'client_intention_level', 'client_createtime', 'is_invite', 'is_invite_arrive', 'is_audition', 'is_audition_arrive', 'main_marketer_name', 'track_createtime', 'marketer_name', 'track_note');
            }

            $fielname = $this->LgStringSwitch("招生咨询明细表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);


            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    $datalist[$key]['track_createtime'] = date("Y-m-d", $value['track_createtime']);
                    $datalist[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    $datalist[$key]['client_tracestatus'] = $clientTracestatus[$value['client_tracestatus']];
                    $datalist[$key]['is_invite'] = $value['is_invite'] > 0 ? '是' : '否';
                    $datalist[$key]['is_invite_arrive'] = $value['is_invite_arrive'] > 0 ? '是' : '否';
                    $datalist[$key]['is_audition'] = $value['is_audition'] > 0 ? '是' : '否';
                    $datalist[$key]['is_audition_arrive'] = $value['is_audition_arrive'] > 0 ? '是' : '否';
                    $datalist[$key]['is_audition_arrive'] = $value['is_audition_arrive'] > 0 ? '是' : '否';
                    if ($this->isGmcPost == true) {
                        $datalist[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    } else {
                        $datalist[$key]['client_mobile'] = $value['client_mobile'];
                    }
                }
            } else {
                $datalist = array();
            }
            $data['list'] = $datalist;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectClear(" select 1,
     
            (select i.invite_id from crm_client_invite as i where i.school_id=cs.school_id and i.client_id=c.client_id order by i.invite_id DESC LIMIT 0,1) as is_invite,
         (select i.invite_isvisit from crm_client_invite as i where i.school_id=cs.school_id and i.client_id=c.client_id order by i.invite_id DESC LIMIT 0,1) as is_invite_arrive,
         (select a.audition_id from crm_client_audition as a where a.school_id=cs.school_id and a.client_id=c.client_id order by a.audition_id DESC Limit 0,1 ) as is_audition,
         (select a.audition_isvisit from crm_client_audition as a where a.school_id=cs.school_id and a.client_id=c.client_id  order by a.audition_id DESC Limit 0,1 ) as is_audition_arrive
              
           from (SELECT t.client_id,t.school_id,t.marketer_id,t.track_intention_level,t.track_createtime,t.track_note
            FROM crm_client_track as t WHERE t.school_id = '{$request['school_id']}' AND {$trcwhere} ORDER BY t.track_createtime DESC) as q
            left join crm_client as c ON c.client_id=q.client_id
            left join crm_client_schoolenter as cs On cs.client_id=c.client_id
            left join crm_marketer as m ON m.marketer_id=q.marketer_id
            left join smc_staffer as stf ON stf.staffer_id=m.staffer_id
            left join crm_code_channel as ch ON ch.channel_id=c.channel_id
            where  {$datawhere} 
            group by q.client_id
              HAVING {$Having}
            ");

            if ($allNum) {
                $data['allnums'] = is_array($allNum) ? count($allNum) : 0;

            } else {
                $data['allnums'] = 0;
            }
        }
        return $data;
    }

    /**
     * 个人招生业绩统计
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/22
     * @param $request
     * @return array|bool
     */
    function marketerOneClientReport($request)
    {
//        $datawhere = "1 and exists (select 1 from crm_client_principal as p where p.marketer_id=m.marketer_id and p.school_id='{$this->school_id}')";
        $datawhere = "1 AND EXISTS (SELECT p.principal_id FROM crm_client_principal AS p,crm_client AS c
WHERE p.marketer_id = m.marketer_id AND p.client_id = c.client_id AND p.principal_leave <> '1' AND p.school_id = '{$request['school_id']}' AND c.client_tracestatus <> '4')";
        $trackwhere = "t.school_id='{$request['school_id']}'";
        $invwhere = "i.school_id='{$request['school_id']}'";
        $audwhere = "au.school_id='{$request['school_id']}'";
        $trackwherestu = "t.school_id='{$request['school_id']}'";
        $invwherestu = "i.school_id='{$request['school_id']}'";
        $audwherestu = "au.school_id='{$request['school_id']}'";
//        $pgwhere = "pg.school_id='{$request['school_id']}'";
        $reginfowhere = " r.school_id = '{$request['school_id']}' and r.info_status =1 ";
//        if (isset($request['starttime']) && $request['starttime']) {
//            $starttime = strtotime($request['starttime']);
//            $trackwhere .= " and t.track_createtime >= '{$starttime}'";
//            $invwhere .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $audwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $pgwhere .= " and from_unixtime(pg.conversionlog_time,'%Y-%m-%d') >= '{$request['starttime']}'";
//        }
//        if (isset($request['endtime']) && $request['endtime']) {
//            $endtime = strtotime($request['endtime']) + 24 * 3600 - 1;
//            $trackwhere .= " and t.track_createtime <= '{$endtime}'";
//            $invwhere .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') <= '{$request['endtime']}'";
//            $audwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') <= '{$request['endtime']}'";
//            $pgwhere .= " and from_unixtime(pg.conversionlog_time,'%Y-%m-%d') <= '{$request['endtime']}'";
//        }
        if (isset($request['starttime']) && $request['starttime']) {
            $starttime = strtotime($request['starttime']);
            $trackwhere .= " and t.track_createtime >= '{$starttime}'";
            $invwhere .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') >= '{$request['starttime']}'";
            $audwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') >= '{$request['starttime']}'";
            $trackwherestu .= " and t.track_createtime >= '{$starttime}'";
            $invwherestu .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') >= '{$request['starttime']}'";
            $audwherestu .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $pgwhere .= " and from_unixtime(pg.conversionlog_time,'%Y-%m-%d') >= '{$request['starttime']}'";
            $reginfowhere .= " and r.pay_successtime >= '{$starttime}' ";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endtime = strtotime($request['endtime']) + 24 * 3600 - 1;
            $trackwhere .= " and t.track_createtime <= '{$endtime}'";
            $invwhere .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') <= '{$request['endtime']}'";
            $audwhere .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') <= '{$request['endtime']}'";
            $trackwherestu .= " and t.track_createtime <= '{$endtime}'";
            $invwherestu .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') <= '{$request['endtime']}'";
            $audwherestu .= " and DATE_FORMAT(au.audition_visittime,'%Y-%m-%d') <= '{$request['endtime']}'";
//            $pgwhere .= " and from_unixtime(pg.conversionlog_time,'%Y-%m-%d') <= '{$request['endtime']}'";
            $reginfowhere .= " and r.pay_successtime <= '{$endtime}' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") { 
                $trackwhere .= " and t.coursetype_id = '{$request['coursetype_id']}' and t.coursecat_id = '{$request['coursecat_id']}' ";
                $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $trackwherestu .= " and t.coursetype_id = '{$request['coursetype_id']}' and t.coursecat_id = '{$request['coursecat_id']}' ";
                $invwherestu .= " AND i.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $audwherestu .= " AND au.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $pgwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
                $reginfowhere .= " and r.coursecat_id='{$request['coursecat_id']}' ";
            } else {
                $trackwhere .= " and t.coursetype_id = '{$request['coursetype_id']}' ";
                $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $trackwherestu .= " and t.coursetype_id = '{$request['coursetype_id']}' ";
                $invwherestu .= " AND i.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audwherestu .= " AND au.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $pgwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $reginfowhere .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $trackwhere .= " and t.coursecat_id = '{$request['coursecat_id']}' ";
            $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $trackwherestu .= " and t.coursecat_id = '{$request['coursecat_id']}' ";
            $invwherestu .= " AND i.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audwherestu .= " AND au.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $pgwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $reginfowhere .= " and r.coursecat_id='{$request['coursecat_id']}' ";
        }

//        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
//            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
//                $trackwhere .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $trackwherestu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $invwherestu .= " AND i.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $audwherestu .= " AND au.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
////                $pgwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
//                $reginfowhere .= " and r.coursecat_id='{$request['coursecat_id']}' ";
//            } else {
//                $trackwhere .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $trackwherestu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $invwherestu .= " AND i.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $audwherestu .= " AND au.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
////                $pgwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
//                $reginfowhere .= " and r.coursetype_id='{$request['coursetype_id']}' ";
//            }
//        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
//            $trackwhere .= " AND t.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $trackwherestu .= " AND t.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $invwherestu .= " AND i.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $audwherestu .= " AND au.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
////            $pgwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
//            $reginfowhere .= " and r.coursecat_id='{$request['coursecat_id']}' ";
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select m.marketer_name,sf.staffer_enname,sf.staffer_branch,
            (select count(DISTINCT t.client_id) from crm_client_track as t where t.marketer_id=m.marketer_id and t.track_isactive =1 and {$trackwhere}) as track_client_num,
            (select count(DISTINCT t.student_id) from crm_student_track as t where t.marketer_id=m.marketer_id and t.track_isactive =1 and {$trackwherestu}) as track_client_num_two,
            
            (select count(t.client_id) from crm_client_track as t where t.marketer_id=m.marketer_id and t.track_isactive =1 and {$trackwhere}) as track_num,
            (select count(t.student_id) from crm_student_track as t where t.marketer_id=m.marketer_id and t.track_isactive =1 and {$trackwherestu}) as track_num_two,
            
            (select count(au.audition_id) from crm_client_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =0 and {$audwhere}) as OH_audition_num,
            (select count(au.audition_id) from crm_student_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =0 and {$audwherestu}) as OH_audition_num_two,
            
            (select count(au.audition_id) from crm_client_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =0 and au.audition_isvisit =1 and {$audwhere}) as OH_audition_arrivenum,
            (select count(au.audition_id) from crm_student_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =0 and au.audition_isvisit =1 and {$audwherestu}) as OH_audition_arrivenum_two,
            
            (select count(au.audition_id) from crm_client_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =1  and {$audwhere}) as Class_audition_num,
            (select count(au.audition_id) from crm_student_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =1  and {$audwherestu}) as Class_audition_num_two,
            
            (select count(au.audition_id) from crm_client_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =1 and au.audition_isvisit =1 and {$audwhere}) as Class_audition_arrivenum,
            (select count(au.audition_id) from crm_student_audition as au where au.marketer_id=m.marketer_id and au.audition_genre =1 and au.audition_isvisit =1 and {$audwherestu}) as Class_audition_arrivenum_two,
            
            (select count(invite_id) from crm_client_invite as i where i.marketer_id=m.marketer_id and {$invwhere} ) as invire_num,  
            (select count(invite_id) from crm_client_invite as i where i.marketer_id=m.marketer_id and i.invite_isvisit=1 and {$invwhere} ) as invire_arrivenum,
            
            (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where {$reginfowhere} and r.info_type = '0' AND r.xz_marketer_id = m.marketer_id) as main_conversionlog_num, 
            (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where {$reginfowhere} and r.info_type = '1' AND r.kz_marketer_id = m.marketer_id) as fu_conversionlog_num 

            from crm_marketer as m 
            left join smc_staffer as sf ON sf.staffer_id=m.staffer_id 
            where {$datawhere} and sf.staffer_leave =0
            order by track_client_num DESC
            ";
//               (select count(pg.conversionlog_id) from crm_client_conversionlog as pg where pg.marketer_id =m.marketer_id and conversionlog_ismajor=1 and {$pgwhere}) as main_conversionlog_num,
//               (select count(pg.conversionlog_id) from crm_client_conversionlog as pg where pg.marketer_id =m.marketer_id and conversionlog_ismajor=0 and {$pgwhere}) as fu_conversionlog_num
//        (select count(au.audition_id) from crm_client_audition as au,crm_client_conversionlog as pg where au.client_id= pg.client_id and au.school_id=pg.school_id  and pg.marketer_id=m.marketer_id and au.audition_genre =0 and {$audwhere} and {$pgwhere} ) as OH_audition_positivenum,
//        (select count(au.audition_id) from crm_client_audition as au,crm_client_conversionlog as pg where au.client_id= pg.client_id and au.school_id=pg.school_id  and pg.marketer_id=m.marketer_id and au.audition_genre =0 and {$pgwhere} ) as OH_audition_allpositivenum,
//        (select count(au.audition_id) from crm_client_audition as au,crm_client_conversionlog as pg where au.client_id= pg.client_id and au.school_id=pg.school_id and  pg.marketer_id=m.marketer_id and au.audition_genre =1 and {$pgwhere} ) as Class_audition_allpositivenum,
//        (select count(invite_id) from crm_client_invite as i,crm_client_conversionlog as pg where pg.client_id=i.client_id and pg.school_id=i.school_id  and  pg.marketer_id=m.marketer_id and {$invwhere} and {$pgwhere}) as invire_positivenum,
//        (select count(invite_id) from crm_client_invite as i,crm_client_conversionlog as pg where pg.client_id=i.client_id and pg.school_id=i.school_id  and  pg.marketer_id=m.marketer_id and  {$pgwhere}) as invire_all_positivenum,
//        (select count(au.audition_id) from crm_client_audition as au,crm_client_conversionlog as pg where au.client_id= pg.client_id and au.school_id=pg.school_id and pg.marketer_id=m.marketer_id and au.audition_genre =1 and {$audwhere} and {$pgwhere} ) as Class_audition_positivenum,
        //添加了 本地tmk 产品让删除
        //(select count(t.track_id) from crm_client_track as t where t.marketer_id=m.marketer_id and t.track_state=-2 and t.school_id='{$request['school_id']}' and t.track_isactive =1 and {$trackwhere} ) as unefftive_num
        $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname,school_openclass", "school_id='{$request['school_id']}'");
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['track_client_num'] = $dateexcelvar['track_client_num']+$dateexcelvar['track_client_num_two'];
                    $datearray['track_num'] = $dateexcelvar['track_num']+$dateexcelvar['track_num_two'];
                    if ($schoolOne['school_openclass'] == 1) {
                        $datearray['OH_audition_num'] = $dateexcelvar['OH_audition_num']+$dateexcelvar['OH_audition_num_two'];
                        $datearray['OH_audition_arrivenum'] = $dateexcelvar['OH_audition_arrivenum']+$dateexcelvar['OH_audition_arrivenum_two'];
                    }
                    $datearray['Class_audition_num'] = $dateexcelvar['Class_audition_num']+$dateexcelvar['Class_audition_num_two'];
                    $datearray['Class_audition_arrivenum'] = $dateexcelvar['Class_audition_arrivenum']+$dateexcelvar['Class_audition_arrivenum_two'];
                    $datearray['invire_num'] = $dateexcelvar['invire_num'];
                    $datearray['invire_arrivenum'] = $dateexcelvar['invire_arrivenum'];
//                    if ($schoolOne['school_openclass'] == 1) {
//                        $datearray['OH_audition_positivenum'] = $dateexcelvar['OH_audition_positivenum'];
//                        $datearray['OH_audition_allpositivenum'] = $dateexcelvar['OH_audition_allpositivenum'];
//                    }
//                    $datearray['Class_audition_positivenum'] = $dateexcelvar['Class_audition_positivenum'];
//                    $datearray['Class_audition_allpositivenum'] = $dateexcelvar['Class_audition_allpositivenum'];
//                    $datearray['invire_positivenum'] = $dateexcelvar['invire_positivenum'];
//                    $datearray['invire_all_positivenum'] = $dateexcelvar['invire_all_positivenum'];
                    $datearray['main_conversionlog_num'] = $dateexcelvar['main_conversionlog_num'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_conversionlog_num'] = $dateexcelvar['fu_conversionlog_num'];
                    }
//                    $datearray['unefftive_num'] = $dateexcelvar['unefftive_num'];
                    $outexceldate[] = $datearray;
                }
            }

            if ($this->company_isassist == 0) {
                $excelheader = $this->LgArraySwitch(array('教师中文名', '教师英文名', '教师编号', '沟通人数', '沟通人次',
                    'OH邀约名单数', 'OH到访名单数', '插班试听名单数', "插班试听到访名单数", '柜询邀约名单数',
                    '柜询到访名单数', '新招报名数', '扩科报名数'));
                $excelfileds = array('marketer_name', 'staffer_enname', 'staffer_branch', 'track_client_num', 'track_num',
                    'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'invire_num',
                    'invire_arrivenum', 'main_conversionlog_num', 'fu_conversionlog_num');
                if ($schoolOne['school_openclass'] == 0) {
                    $excelheader = $this->LgArraySwitch(array('教师中文名', '教师英文名', '教师编号', '沟通人数', '沟通人次',
                        '插班邀约名单数', "插班到访名单数", '柜询邀约名单数', '柜询到访名单数', '插班邀约报名数',
                        '新招报名数', '扩科报名数'));
                    $excelfileds = array('marketer_name', 'staffer_enname', 'staffer_branch', 'track_client_num', 'track_num',
                        'Class_audition_num', 'Class_audition_arrivenum', 'invire_num', 'invire_arrivenum', 'OH_audition_allpositivenum',
                        'main_conversionlog_num', 'fu_conversionlog_num');
                }
            } else {
                $excelheader = $this->LgArraySwitch(array('教师中文名', '教师英文名', '教师编号', '沟通人数', '沟通人次',
                    'OH邀约名单数', 'OH到访名单数', '插班邀约名单数', "插班到访名单数", '柜询邀约名单数',
                    '柜询到访名单数', '新招报名数', '扩科报名数'));
                $excelfileds = array('marketer_name', 'staffer_enname', 'staffer_branch', 'track_client_num', 'track_num',
                    'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'invire_num',
                    'invire_arrivenum', 'main_conversionlog_num', 'fu_conversionlog_num');
                if ($schoolOne['school_openclass'] == 0) {
                    $excelheader = $this->LgArraySwitch(array('教师中文名', '教师英文名', '教师编号', '沟通人数', '沟通人次',
                        '插班邀约名单数', "插班到访名单数", '柜询邀约名单数', '柜询名单到访数', '插班邀约报名数',
                        '新招报名数', '扩科报名数'));
                    $excelfileds = array('marketer_name', 'staffer_enname', 'staffer_branch', 'track_client_num', 'track_num',
                        'Class_audition_num', 'Class_audition_arrivenum', 'invire_num', 'invire_arrivenum', 'OH_audition_allpositivenum',
                        'main_conversionlog_num', 'fu_conversionlog_num');
                }

            }
            $fielname = $this->LgStringSwitch("招生个人业绩统计报表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;

        } else {
            $sql .= " Limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $dataList = array();
            }else{
                foreach ($dataList as &$dataListVar){
                    $dataListVar['track_client_num'] = $dataListVar['track_client_num']+$dataListVar['track_client_num_two'];
                    $dataListVar['track_num'] = $dataListVar['track_num']+$dataListVar['track_num_two'];
                    $dataListVar['OH_audition_num'] = $dataListVar['OH_audition_num']+$dataListVar['OH_audition_num_two'];
                    $dataListVar['OH_audition_arrivenum'] = $dataListVar['OH_audition_arrivenum']+$dataListVar['OH_audition_arrivenum_two'];
                    $dataListVar['Class_audition_num'] = $dataListVar['Class_audition_num']+$dataListVar['Class_audition_num_two'];
                    $dataListVar['Class_audition_arrivenum'] = $dataListVar['Class_audition_arrivenum']+$dataListVar['Class_audition_arrivenum_two'];
                }
            }
            $allnum = $this->DataControl->selectOne("select count(m.marketer_id) as marketer_num from crm_marketer as m 
            left join smc_staffer as sf ON sf.staffer_id=m.staffer_id 
            where {$datawhere} and sf.staffer_leave =0 ");
        }
        $result = array();
        $result['list'] = $dataList;
        $result['allnums'] = is_array($allnum) ? $allnum['marketer_num'] : 0;
        return $result;
    }


    /**
     *  招生渠道业绩
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/24 0024
     * @param $request
     * @return mixed
     */
    function schChannelClientReport($request)
    {
        $trackwhere = "1 and ct.school_id='{$this->school_id}'";
        $audwhere = " 1 and au.school_id='{$this->school_id}'";
        $invwhere = " 1 and i.school_id='{$this->school_id}'";
        $clientwhere = " 1 and st.school_id='{$this->school_id}' and st.is_enterstatus =1";
        $positwhere = " 1 and pg.school_id ='{$this->school_id}'";

        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $stattime = strtotime($request['starttime']);
            $clientwhere .= " and cl.client_createtime >='{$stattime}'";
            $trackwhere .= " and cl.client_createtime >='{$stattime}'";
            $audwhere .= " and  cl.client_createtime >='{$stattime}' ";
            $invwhere .= " and cl.client_createtime >='{$stattime}' ";
            $positwhere .= " and  cl.client_createtime >='{$stattime}' ";
        }
        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']));
            $endime = strtotime($endime) + 60 * 60 * 24 - 1;
            $clientwhere .= " and cl.client_createtime <='{$endime}'";
            $trackwhere .= " and cl.client_createtime <='{$endime}'";
            $audwhere .= " and cl.client_createtime <='{$endime}' ";
            $invwhere .= " and cl.client_createtime <='{$endime}' ";
            $positwhere .= " and cl.client_createtime <='{$endime}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $clientwhere .= " AND cl.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
                $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
                $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
                $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
                $positwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
            } else {
                $clientwhere .= " AND cl.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $positwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $clientwhere .= " AND cl.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $trackwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $audwhere .= " AND au.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invwhere .= " AND i.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $positwhere .= " AND pg.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            select cf.frommedia_id,cf.frommedia_name,
            (select count(cc.channel_id) from crm_code_channel as cc where cc.channel_medianame =cf.frommedia_name and company_id='{$request['company_id']}' and exists (select cl.client_id from crm_client as cl,crm_client_schoolenter as st  where cl.client_id =st.client_id and cl.channel_id=cc.channel_id and {$clientwhere}) ) as channel_num,
            (select count(cl.client_id) from crm_client as cl,crm_client_schoolenter as st where cl.client_id=st.client_id and st.is_enterstatus=1 and cl.client_source=cf.frommedia_name and {$clientwhere} )  as client_allnum,   
            (select count(DISTINCT cl.client_id) from crm_client as cl,crm_client_track as ct where cl.client_id= ct.client_id and {$trackwhere}  and ct.track_isactive =1 and cl.client_source=cf.frommedia_name) as  track_num,
            (select count(au.audition_id) from crm_client as cl,crm_client_audition as au where cl.client_id =au.client_id and cl.client_source=cf.frommedia_name and audition_genre =0 and {$audwhere} ) as HO_aud_num,
            (select count(au.audition_id) from crm_client as cl,crm_client_audition as au where cl.client_id =au.client_id and cl.client_source=cf.frommedia_name and audition_genre =0 and au.audition_isvisit =1 and {$audwhere} ) as HO_aud_arrivenum,
            (select count(au.audition_id) from crm_client as cl,crm_client_audition as au where cl.client_id =au.client_id and cl.client_source=cf.frommedia_name and audition_genre =1 and {$audwhere} ) as Class_aud_num,
            (select count(au.audition_id) from crm_client as cl,crm_client_audition as au where cl.client_id =au.client_id and cl.client_source=cf.frommedia_name and audition_genre =1  and au.audition_isvisit =1 and {$audwhere} ) as Class_aud_arrivenum,
            (select count(i.invite_id) from crm_client as cl,crm_client_invite as i where cl.client_id =i.client_id and cl.client_source=cf.frommedia_name and {$invwhere} ) as invite_num,
            (select count(i.invite_id) from crm_client as cl,crm_client_invite as i where cl.client_id =i.client_id and cl.client_source=cf.frommedia_name and invite_isvisit =1 and {$invwhere} ) as invite_arrivenum,
            (select count(pg.client_id) from crm_client as cl,crm_client_positivelog as pg where pg.client_id=cl.client_id and {$positwhere} and cl.client_source=cf.frommedia_name and (exists (select 1 from crm_client_audition as au where au.client_id=cl.client_id and {$audwhere} union select 1 from crm_client_invite as i where i.client_id=cl.client_id and {$invwhere} ))) as inv_positive_num,
            (select count(pg.client_id) from  crm_client as cl,crm_client_positivelog as pg where cl.client_id=pg.client_id and cl.client_source=cf.frommedia_name and {$positwhere} ) positive_num,
            (select count(DISTINCT ct.client_id) from crm_client as cl,crm_client_track as ct where ct.client_id=cl.client_id and cl.client_source=cf.frommedia_name and {$trackwhere} and ct.track_state ='-2') as  client_uneffive_num,
            (select count(DISTINCT ct.client_id) from crm_client as cl,crm_client_track as ct where ct.client_id=cl.client_id and cl.client_source=cf.frommedia_name and {$trackwhere} and ct.track_state ='-1') as  client_nointention_num
            from crm_code_frommedia as cf
            where cf.company_id='{$this->company_id}' and exists (select 1 from crm_client as cl,crm_client_schoolenter as st where cl.client_id=st.client_id and cl.client_source=cf.frommedia_name and {$clientwhere}) 
            order by cf.frommedia_id DESC
         ";

        if (isset($request['is_export']) && $request['is_export']) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname,school_openclass", "school_id='{$request['school_id']}'");
            if ($dateexcelarray) {
                $outexceldate = array();
                $datearray = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];
                    $datearray['channel_num'] = $dateexcelvar['channel_num'];
                    $datearray['client_allnum'] = $dateexcelvar['client_allnum'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    if ($schoolOne['school_openclass'] == 1) {
                        $datearray['HO_aud_num'] = $dateexcelvar['HO_aud_num'];
                        $datearray['HO_aud_arrivenum'] = $dateexcelvar['HO_aud_arrivenum'];
                    }

                    $datearray['Class_aud_num'] = $dateexcelvar['Class_aud_num'];
                    $datearray['Class_aud_arrivenum'] = $dateexcelvar['Class_aud_arrivenum'];
                    $datearray['invite_num'] = $dateexcelvar['invite_num'];
                    $datearray['invite_arrivenum'] = $dateexcelvar['invite_arrivenum'];
                    $datearray['inv_positive_num'] = $dateexcelvar['inv_positive_num'];
                    $datearray['positive_num'] = $dateexcelvar['positive_num'];
                    $datearray['client_uneffive_num'] = $dateexcelvar['client_uneffive_num'];
                    $datearray['client_nointention_num'] = $dateexcelvar['client_nointention_num'];
                    $datearray['school_positive_rate'] = $dateexcelvar['client_allnum'] ? round($dateexcelvar['positive_num'] / $dateexcelvar['client_allnum'], 4) * 100 : 0;
                    $outexceldate[] = $datearray;
                }
            }

            if ($schoolOne['school_openclass'] == 1) {
                $excelheader = $this->LgArraySwitch(array('招生渠道类型', '招生渠道明细数量', '新增毛名单数', '跟踪人数', '新增OH邀约名单数', '新增OH邀约到访名单数', '新增插班邀约名单数', '新增插班邀约到访名单数', '新增柜询邀约名单数', '新增柜询邀约到访名单数', '新增邀约报名人数', "新增报名人数", "新增无效名单数", "新增无意向名单", "转正率"));

                $excelfileds = array('frommedia_name', 'channel_num', 'client_allnum', 'track_num', 'HO_aud_num', 'HO_aud_arrivenum', 'Class_aud_num', 'Class_aud_arrivenum', 'invite_num', "invite_arrivenum", 'inv_positive_num', "positive_num", 'client_uneffive_num', "client_nointention_num", "school_positive_rate");
            } else {
                $excelheader = $this->LgArraySwitch(array('招生渠道类型', '招生渠道明细数量', '新增毛名单数', '跟踪人数', '新增插班邀约名单数', '新增插班邀约到访名单数', '新增柜询邀约名单数', '新增柜询邀约到访名单数', '新增邀约报名人数', "新增报名人数", "新增无效名单数", "新增无意向名单", "转正率"));

                $excelfileds = array('frommedia_name', 'channel_num', 'client_allnum', 'track_num', 'Class_aud_num', 'Class_aud_arrivenum', 'invite_num', "invite_arrivenum", 'inv_positive_num', "positive_num", 'client_uneffive_num', "client_nointention_num", "school_positive_rate");
            }
            $fielname = $this->LgStringSwitch("渠道招生统计报表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;


        } else {
            $sql .= " Limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as $key => $value) {
                    $dataList[$key]['school_positive_rate'] = $value['client_allnum'] ? round($value['positive_num'] / $value['client_allnum'], 4) * 100 . '%' : 0 . '%';
                }
            } else {
                $dataList = array();
            }
            $allnum = $this->DataControl->selectOne("select count(distinct cf.frommedia_id) AS all_num 
            FROM crm_client_schoolenter st 
            left join crm_client cl on cl.client_id = st.client_id
            left join crm_code_frommedia cf ON cf.frommedia_name= cl.client_source
            WHERE {$clientwhere}
            and cf.company_id = '{$this->company_id}'");
            $data = array();
            $data['all_num'] = is_array($allnum) ? $allnum['all_num'] : 0;
            $data['list'] = $dataList;
            return $data;
        }
    }

    /**
     * 单校班组招生统计报表
     * author: qizhugong
     * 对应接口文档 0001
     * Date 2021/06/09
     * @param $request
     * @return mixed
     */
    function courseRecruitCount($request)
    {
        $datawhere = "u.company_id = '{$request['company_id']}' AND u.coursetype_isrecruit = '1'";//t.coursecat_iscrmadded
        $clientwhere = "c.client_id = e.client_id AND e.school_id = '{$request['school_id']}' AND c.client_id = i.client_id AND i.coursetype_id = u.coursetype_id";//AND e.is_enterstatus = '1'
        $trackwhere = "t.school_id = '{$request['school_id']}' and (t.track_isactive = '1' or t.track_isgmcactive = '1' ) AND t.coursetype_id = u.coursetype_id";
        $conwhere = "r.school_id = '{$request['school_id']}' AND r.coursetype_id = u.coursetype_id and r.info_status =1";

        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
            $trackwhere .= " and t.track_createtime >='{$stattime}'";
            $conwhere .= " and r.pay_successtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $clientwhere .= " and c.client_createtime <='{$endime}'";
            $trackwhere .= " and t.track_createtime <='{$endime}'";
            $conwhere .= " and r.pay_successtime <='{$endime}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and u.coursetype_cnname like '%{$request['keyword']}%'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;
        $sql = "select u.coursetype_cnname,
                (select count(DISTINCT c.client_id) FROM crm_client as c,crm_client_schoolenter as e,crm_client_intention i
                where {$clientwhere}) as client_maonum,
                (select count(DISTINCT c.client_id) FROM crm_client as c,crm_client_schoolenter as e,crm_client_intention i
                where {$clientwhere} AND c.client_tracestatus <> '-2' and c.client_isgross = '0') as  client_validnum,
                (select count(DISTINCT c.client_id) FROM crm_client as c,crm_client_schoolenter as e,crm_client_intention i
                where {$clientwhere} AND c.client_tracestatus <> '-2' and c.client_isgross = '0' and c.client_intention_maxlevel >= 3) as client_level_validnum,
                (select count(DISTINCT t.client_id) from crm_client_track as t where {$trackwhere}) as client_tracknum,
                (select count(t.client_id) from crm_client_track as t where {$trackwhere}) as school_track_num,
                (select count(t.track_id) from crm_client_track as t where {$trackwhere} and t.track_linktype='电话沟通') as track_moblie_num,
	            (select count(r.student_id) from smc_student_registerinfo as r where {$conwhere}) as positive_num
                FROM smc_code_coursetype AS u where {$datawhere} 
                HAVING client_maonum > 0 or client_validnum > 0 or client_level_validnum > 0 or client_tracknum > 0 or school_track_num > 0 or track_moblie_num > 0 or positive_num > 0";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['client_maonum'] = $dateexcelvar['client_maonum'];
                    $datearray['client_validnum'] = $dateexcelvar['client_validnum'];
                    $datearray['client_intention_maxlevel'] = $dateexcelvar['client_intention_maxlevel'];
                    $datearray['client_tracknum'] = $dateexcelvar['client_tracknum'];
                    $datearray['school_track_num'] = $dateexcelvar['school_track_num'];
                    $datearray['track_moblie_num'] = $dateexcelvar['track_moblie_num'];
                    $datearray['positive_num'] = $dateexcelvar['positive_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('班组名称', '新增毛名单数', '新增有效名单数', '新增三星以上有效名单数', '新增跟踪人数', '新增跟踪人次', '新增电话沟通人次', '新增累计报名人数'));
            $excelfileds = array('coursetype_cnname', 'client_maonum', 'client_validnum', 'client_intention_maxlevel', 'client_tracknum', 'school_track_num', 'track_moblie_num', 'positive_num');

            $fielname = $this->LgStringSwitch("班组招生统计报表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['start_time']}-{$request['end_time']}.xlsx");
            exit;

        } else {
            $datalist = $this->DataControl->selectClear($sql . " limit {$pagestart},{$num}");
            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    if ($value['school_openclass'] == 0) {
                        $datalist[$key]['OH_client_num'] = "--";
                        $datalist[$key]['OH_client_arrive_num'] = "--";
                    }
                }
            }
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $all_num = $this->DataControl->selectClear($sql);
                if ($all_num) {
                    $data['allnums'] = count($all_num);
                } else {
                    $data['allnums'] = 0;
                }
            }

            if (!$datalist) {
                $datalist = array();
            }
            $data['list'] = $datalist;
            return $data;
        }

    }


    /**
     * 渠道名单跟踪状况表
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/29 0029
     * @param $request
     * @return mixed
     */
    function channelConsult($request)
    {
        $datawhere = "cs.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' AND cs.is_enterstatus = '1'";
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime>='{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime<='{$endtime}'";
        }
        if (isset($request['client_source']) && $request['client_source'] !== '') {
            $datawhere .= " and c.client_source='{$request['client_source']}'";
        }

        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus = '{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['channel_id']) && $request['channel_id'] !== '') {
            $datawhere .= " and c.channel_id='{$request['channel_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select c.client_cnname,c.client_age,c.client_sex,c.client_mobile,c.client_address,c.client_source,c.client_remark,cl.channel_name,cl.channel_way,FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') as client_createtime ,c.client_tracestatus,c.client_id,
               (select  concat( m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) from crm_client_principal as pl,crm_marketer as m,smc_staffer as sf  where pl.marketer_id=m.marketer_id and m.staffer_id = sf.staffer_id and pl.client_id=c.client_id and pl.school_id=cs.school_id and principal_ismajor =1 order by principal_createtime DESC limit 0,1) as marketer_name,
               (select invite_id from crm_client_invite as ci where ci.client_id=c.client_id and ci.school_id=cs.school_id order by ci.invite_createtime DESC limit 0,1) as is_invite,
                (select invite_visittime from crm_client_invite as ci where ci.client_id=c.client_id and ci.school_id=cs.school_id order by ci.invite_createtime DESC limit 0,1) as invite_visittime,
                  (select invite_isvisit from crm_client_invite as ci where ci.client_id=c.client_id and ci.school_id=cs.school_id order by ci.invite_createtime DESC limit 0,1) as invite_isvisit,
               (select audition_id from crm_client_audition as ca where ca.client_id=c.client_id and ca.school_id=cs.school_id order by ca.audition_createtime DESC limit 0,1) as is_audition,
                  (select audition_visittime from crm_client_audition as ca where ca.client_id=c.client_id and ca.school_id=cs.school_id order by ca.audition_createtime DESC limit 0,1) as audition_visittime,
                  (select receiver_name from crm_client_audition as ca where ca.client_id=c.client_id and ca.school_id=cs.school_id order by ca.audition_createtime DESC limit 0,1) as au_receiver_name,
                   (select audition_isvisit from crm_client_audition as ca where ca.client_id=c.client_id and ca.school_id=cs.school_id order by ca.audition_createtime DESC limit 0,1) as audition_isvisit
               from crm_client as c 
               left join crm_code_channel as cl On c.channel_id = cl.channel_id
               left join crm_client_schoolenter as cs on cs.client_id=c.client_id 
               where {$datawhere} order by c.client_createtime DESC ";
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'] == "" ? '' : $dateexcelvar['channel_name'];
                    $datearray['channel_way'] = $dateexcelvar['channel_way'] == 0 ? $this->LgStringSwitch('线上') : $this->LgStringSwitch('线下');
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    if ($request['re_postbe_id'] > 0) {
                        $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    } else {
                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    }
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['is_invite'] = $dateexcelvar['is_invite'] == false ? $this->LgStringSwitch('否') : $this->LgStringSwitch('是');
                    $datearray['invite_visittime'] = $dateexcelvar['invite_visittime'];
                    $datearray['invite_isvisit'] = $dateexcelvar['invite_isvisit'] == 1 ? '是' : '否';
                    $datearray['is_audition'] = $dateexcelvar['is_audition'] == false ? $this->LgStringSwitch('否') : $this->LgStringSwitch('是');
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];
                    $datearray['au_receiver_name'] = $dateexcelvar['au_receiver_name'];
                    $datearray['audition_isvisit'] = $dateexcelvar['audition_isvisit'] == 1 ? '是' : '否';
                    $datearray['tracestatus_name'] = $clientTracestatus[$dateexcelvar['client_tracestatus']];
                    $outexceldate[] = $datearray;
                }
            }

            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array('渠道类型', '明细名称', '渠道方式', '中文名', '性别', ' 年龄', '电话', "备注", '名单创建时间', '主负责人', '是否邀约柜询', '柜询时间', '柜询是否到访', '是否邀约试听', '试听时间', '试听接待人', '试听是否到访 ', '客户状态'));

            $excelfileds = array('client_source', 'channel_name', 'channel_way', 'client_cnname', 'client_sex', 'client_age', 'client_mobile', 'client_remark', 'client_createtime', 'marketer_name', 'is_invite', 'invite_visittime', 'invite_isvisit', 'is_audition', 'audition_visittime', 'au_receiver_name', 'audition_isvisit', 'tracestatus_name');

            $fielname = $this->LgStringSwitch("渠道名单跟踪状况表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);

            if (!$datalist) {
                $datalist = array();
            } else {
                foreach ($datalist as &$value) {
                    $value['channel_way'] = $value['channel_way'] == 0 ? $this->LgStringSwitch('线上') : $this->LgStringSwitch('线下');
                    $value['is_invite'] = $value['is_invite'] == false ? $this->LgStringSwitch('否') : $this->LgStringSwitch('是');
                    $value['is_audition'] = $value['is_audition'] == false ? $this->LgStringSwitch('否') : $this->LgStringSwitch('是');
                    $value['invite_isvisit'] = $value['invite_isvisit'] == 1 ? '是' : '否';
                    $value['audition_isvisit'] = $value['audition_isvisit'] == 1 ? '是' : '否';
                    $value['tracestatus_name'] = $clientTracestatus[$value['client_tracestatus']];
                    $value['invite_visittime'] = $value['invite_visittime'] ? $value['invite_visittime'] : '--';
                    $value['audition_visittime'] = $value['audition_visittime'] ? $value['audition_visittime'] : '--';
                    $value['au_receiver_name'] = $value['au_receiver_name'] ? $value['au_receiver_name'] : '--';
                    if ($this->isGmcPost == true) {
                        $value['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    } else {
                        $value['client_mobile'] = $value['client_mobile'];
                    }
                }
            }
            $data['list'] = $datalist;
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $all_num = $this->DataControl->selectOne("select count(c.client_id) as  all_num
                from crm_client as c 
               left join crm_code_channel as cl On c.channel_id = cl.channel_id
               left join crm_client_schoolenter as cs on cs.client_id=c.client_id 
               where {$datawhere}");

            if ($all_num) {
                $data['allnums'] = $all_num['all_num'];

            } else {
                $data['allnums'] = 0;
            }
        }
        return $data;
    }

    /**
     * 校咨询报表的问题
     * author: ling
     * 对应接口文档 0001
     */
    function crmSchoolClientTrack($request)
    {
        $datawhere = "cs.school_id ='{$this->school_id}' and c.client_tracestatus <> '4'";
        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $stattime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']));
            $endime = strtotime($endime) + 60 * 60 * 24 - 1;
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }
        if (isset($request['client_source']) && $request['client_source'] !== "") {
            $datawhere .= " and c.client_source ='{$request['client_source']}'";
        }
        if (isset($request['channel_id']) && $request['channel_id'] !== "") {
            $datawhere .= " and c.channel_id ='{$request['channel_id']}'";
        }

        $Having = '1 =1';
        if (isset($request['track_num']) && $request['track_num'] !== "") {
            $Having .= " and track_num <= '{$request['track_num']}'";
        }
        if (isset($request['is_invite']) && $request['is_invite'] !== "") {
            if ($request['is_invite'] == 1) {
                $Having .= " and is_invite > 0";
            } else {
                $Having .= " and is_invite = 0";
            }

        }
        if (isset($request['is_invite_arrive']) && $request['is_invite_arrive'] !== "") {
            if ($request['is_invite_arrive'] == 1) {
                $Having .= " and is_invite_arrive > 0";
            } else {
                $Having .= " and is_invite_arrive = 0";
            }
        }
        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = " c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_createtime,c.client_tracestatus,
        c.client_age,c.client_sex,c.client_address,c.client_source,c.client_sponsor,
        c.client_intention_level,ch.channel_name";
        $sql = "select {$fields},
            (select track_createtime from crm_client_track as t where t.client_id=c.client_id and t.school_id=cs.school_id and track_isactive =1 order by t.track_id DESC  limit 0,1) as track_createtime,
            (select track_note from crm_client_track as t where t.client_id=c.client_id and t.school_id=cs.school_id and track_isactive =1 order by t.track_id DESC  limit 0,1) as track_note,
            (select concat( m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) from crm_client_track as t ,crm_marketer as  m,smc_staffer as sf  where t.marketer_id =m.marketer_id and sf.staffer_id =m.staffer_id and  t.client_id=c.client_id and t.school_id=cs.school_id and track_isactive =1 order by t.track_id DESC  limit 0,1) as marketer_name,
            (select concat( m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )
             from crm_client_principal as cp,crm_marketer as m,smc_staffer as sf where sf.staffer_id=m.staffer_id and  m.marketer_id =cp.marketer_id and  cp.client_id =cs.client_id and cp.school_id =cs.school_id  and  principal_ismajor =1 and principal_leave =0 limit 0,1) as  main_marketer_name,
            (select concat( m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) from crm_client_principal as cp,crm_marketer as m ,smc_staffer as sf where sf.staffer_id=m.staffer_id and  m.marketer_id =cp.marketer_id and  cp.client_id =cs.client_id and cp.school_id =cs.school_id  and  principal_ismajor =0 and principal_leave =0 limit 0,1) as  fu_marketer_name,
            (select count(t.track_id) from crm_client_track as t where t.client_id=c.client_id and t.school_id=cs.school_id and track_isactive =1 ) as track_num,
           (select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id=cs.school_id)+( 
select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id=cs.school_id )) as is_invite,
  (select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id=cs.school_id and  invite_isvisit =1)+( 
select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id=cs.school_id and audition_isvisit =1 )) as is_invite_arrive
        from crm_client as c
        left join crm_client_schoolenter as cs ON cs.client_id = c.client_id 
        left join crm_code_channel as ch ON c.channel_id = ch.channel_id
        where {$datawhere}
        HAVING {$Having} order by c.client_id DESC";
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['client_createtime'] = date("Y-m-d", $val['client_createtime']);
                $val['client_mobile'] = substr_replace($val['client_mobile'], '****', 3, 4);
                if ($val['track_createtime']) {
                    $val['track_createtime'] = date("Y-m-d", $val['track_createtime']);
                } else {
                    $val['track_createtime'] = '--';
                }
                $val['client_tracestatus'] = $clientTracestatus[$val['client_tracestatus']];
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_id'] = $dateexcelvar['client_id'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $datearray['is_invite'] = $dateexcelvar['is_invite'] > 0 ? "是" : '否';
                    $datearray['is_invite_arrive'] = $dateexcelvar['is_invite_arrive'] > 0 ? "是" : '否';
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['main_marketer_name'] = $dateexcelvar['main_marketer_name'];
                    $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    $datearray['track_contenttime'] = $dateexcelvar['track_createtime'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            if ($request['language_type'] == 'tw') {
                $excelheader = $this->LgArraySwitch(array('諮詢編號', '中文名', '英文名', '手机号码', '創建日期', '溝通狀態', '主動溝通次数', '是否邀約', "是否到訪", '年齡', '性别', '聯系地址', '渠道類型', '渠道明細', '意向級別', '主招老師', '輔招老師', '最後跟蹤時間', '最後跟蹤老師', '最後跟蹤內容'));//'最後跟蹤時間 最後聯系時間',
            } else {
                $excelheader = $this->LgArraySwitch(array('咨询编号', '中文名', '英文名', '手機號碼', '创建日期', '沟通状态', '主动沟通次数', '是否邀约', "是否到访", '年龄', '性别', '联系地址', '渠道类型', '渠道明细', '意向星级', '主招教师', '辅招教师', '最后跟踪时间', '最后跟踪教师', '最后跟踪内容'));//'最后跟踪时间 最后联系时间',
            }

            $excelfileds = array('client_id', 'client_cnname', 'client_enname', 'client_mobile', 'client_createtime', 'client_tracestatus', 'track_num', 'is_invite', 'is_invite_arrive', 'client_age', 'client_sex', 'client_address', 'client_source', 'channel_name', 'client_intention_level', 'main_marketer_name', 'fu_marketer_name', 'track_contenttime', 'marketer_name', 'track_note');//'track_createtime',

            $fielname = $this->LgStringSwitch("校招生咨询分析表");

            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);

            if ($datalist) {
                foreach ($datalist as $key => $value) {
                    if ($value['track_createtime']) {
                        $datalist[$key]['track_createtime'] = date("Y-m-d", $value['track_createtime']);
                    } else {
                        $datalist[$key]['track_createtime'] = '--';
                    }
                    $datalist[$key]['client_mobile'] = substr_replace($value['client_mobile'], '****', 3, 4);
                    $datalist[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    $datalist[$key]['client_tracestatus'] = $clientTracestatus[$value['client_tracestatus']];
                    $datalist[$key]['is_invite'] = $value['is_invite'] > 0 ? "是" : '否';
                    $datalist[$key]['is_invite_arrive'] = $value['is_invite_arrive'] > 0 ? "是" : '否';
                }
            } else {
                $datalist = array();
            }
            $data['list'] = $datalist;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectClear("select c.client_id,
        (select count(t.track_id) from crm_client_track as t where t.client_id=c.client_id and t.school_id=cs.school_id and track_isactive =1 ) as track_num,
        (select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id=cs.school_id)+( 
        select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id=cs.school_id )) as is_invite,
        (select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id=cs.school_id and  invite_isvisit =1)+( 
        select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id=cs.school_id and audition_isvisit =1 )) as is_invite_arrive
        from crm_client as c 
        left join crm_client_schoolenter as cs ON cs.client_id = c.client_id 
        left join crm_code_channel as ch ON c.channel_id = ch.channel_id
        where cs.school_id ='{$this->school_id}'  and {$datawhere}
        HAVING {$Having}");

            if ($allNum) {
                $data['allnums'] = count($allNum);
            } else {
                $data['allnums'] = 0;
            }
        }
        return $data;
    }

    /**
     * 校招生咨询明细
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/23 0023
     * @param $request
     */
    function schClientTrack($request)
    {
//        跟踪状态：0待跟踪1持续跟踪2已柜询3已视听4已转正-1无意向-2无效名单
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        $datawhere = " 1 and exists (select 1 from crm_client_track as t where t.client_id =c.client_id and t.school_id={$this->school_id} and t.track_isactive =1 )";
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['end_time']));
            $endime = strtotime($endime) + 60 * 60 * 24 - 1;
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }
        if (isset($request['client_source']) && $request['client_source'] !== "") {
            $datawhere .= " and c.client_source ='{$request['client_source']}'";
        }
        if (isset($request['channel_id']) && $request['channel_id'] !== "") {
            $datawhere .= " and c.channel_id ='{$request['channel_id']}'";
        }
        $Having = '1 =1';
        if (isset($request['track_num']) && $request['track_num'] !== "") {
            $Having .= " and track_num <= '{$request['track_num']}'";
        }
        if (isset($request['is_invite']) && $request['is_invite'] !== "") {
            if ($request['is_invite'] == 1) {
                $Having .= " and is_invite > 0";
            } else {
                $Having .= " and is_invite = 0";
            }
        }
        if (isset($request['is_invite_arrive']) && $request['is_invite_arrive'] !== "") {
            if ($request['is_invite_arrive'] == 1) {
                $Having .= " and is_invite_arrive > 0";
            } else {
                $Having .= " and is_invite_arrive = 0";
            }
        }
        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus = '{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.client_id,c.client_cnname,client_mobile,c.client_enname,c.client_createtime,c.client_tracestatus,c.client_age,c.client_sex,c.client_address,c.client_source,cl.channel_name,c.client_intention_level,c.client_intention_maxlevel,c.client_sponsor,
(select count(t.client_id) from crm_client_track as t where t.client_id=c.client_id and t.school_id='{$this->school_id}' and t.track_isactive =1 ) as track_num,
(select concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) from crm_client_principal as pl,crm_marketer as m,smc_staffer as  sf where sf.staffer_id=m.staffer_id and m.marketer_id=pl.marketer_id and pl.client_id=c.client_id and pl.school_id='{$this->school_id}' and pl.principal_ismajor =1 and pl.principal_leave = 0 limit 0,1 ) as mian_marketer_name,
(select group_concat(concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ))) from crm_client_principal as pl,crm_marketer as m,smc_staffer as sf where sf.staffer_id=m.staffer_id and m.marketer_id=pl.marketer_id and pl.client_id=c.client_id and pl.school_id='{$this->school_id}' and pl.principal_ismajor =0 and pl.principal_leave = 0  ) as fu_marketer_name,
(select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id= '{$this->school_id}')+( 
select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id= '{$this->school_id}')) as is_invite,
(select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id= '{$this->school_id}' and  invite_isvisit =1)+( 
select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id= '{$this->school_id}' and audition_isvisit =1 )) as is_invite_arrive,
(SELECT group_concat(CONCAT(from_unixtime(ct.track_createtime, '%m-%d'),' 跟踪：',ct.track_note,'#') ORDER BY ct.track_id DESC) 
            FROM crm_client_track AS ct WHERE ct.client_id = c.client_id AND ct.track_validinc=1 and ct.track_isactive = '1') AS track_detail
                from crm_client as c
                left join crm_code_channel as cl On c.channel_id = cl.channel_id
                left join crm_client_schoolenter as cs On cs.client_id = c.client_id 
                where {$datawhere} and cs.school_id='{$this->school_id}' and is_enterstatus =1
                having {$Having} 
                order by client_createtime DESC
                ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if ($dateexcelarray) {
                foreach ($dateexcelarray as $k => $dateexcelOne) {
                    $inviteOne = $this->DataControl->selectOne("select i.invite_id,invite_visittime,receiver_name, if(invite_isvisit=1,'是','否') as invite_isvisit
from crm_client_invite as i where i.client_id='{$dateexcelOne['client_id']}' and i.school_id='{$this->school_id}' order by i.invite_id DESC limit 0,1 ");
                    $auditionOne = $this->DataControl->selectOne("select au.audition_id,au.audition_visittime,receiver_name as au_receiver_name,if(audition_isvisit=1,'是','否') as audition_isvisit
from crm_client_audition as au where au.client_id='{$dateexcelOne['client_id']}' and au.school_id='{$this->school_id}' order by au.audition_id DESC limit 0,1");
                    $trackOne = $this->DataControl->selectOne("select track_createtime,track_note,m.marketer_name from crm_client_track as t
left join crm_marketer as m On m.marketer_id=t.marketer_id  where t.client_id='{$dateexcelOne['client_id']}' and t.school_id='{$this->school_id}' and t.track_isactive =1
order by t.track_createtime DESC ");
                    $dateexcelarray[$k]['is_invite_name'] = $inviteOne ? '是' : '否';
                    $dateexcelarray[$k]['invite_visittime'] = $inviteOne ? $inviteOne['invite_visittime'] : '--';
                    $dateexcelarray[$k]['receiver_name'] = $inviteOne ? $inviteOne['receiver_name'] : '--';
                    $dateexcelarray[$k]['invite_isvisit'] = $inviteOne ? $inviteOne['invite_isvisit'] : '--';
                    $dateexcelarray[$k]['is_audition_name'] = $auditionOne ? '是' : '否';
                    $dateexcelarray[$k]['audition_visittime'] = $auditionOne ? $auditionOne['audition_visittime'] : '--';
                    $dateexcelarray[$k]['au_receiver_name'] = $auditionOne ? $auditionOne['au_receiver_name'] : '--';
                    $dateexcelarray[$k]['audition_isvisit'] = $auditionOne ? $auditionOne['audition_isvisit'] : '--';
                    $dateexcelarray[$k]['track_createtime'] = $trackOne ? $trackOne['track_createtime'] : '--';
                    $dateexcelarray[$k]['marketer_name'] = $trackOne ? $trackOne['marketer_name'] : '--';
                    $dateexcelarray[$k]['track_note'] = $trackOne ? $trackOne['track_note'] : '--';
                }
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['client_tracestatus_name'] = $clientTracestatus[$dateexcelvar['client_tracestatus']];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['is_invite_name'] = $dateexcelvar['is_invite_name'];
                    $datearray['invite_visittime'] = $dateexcelvar['invite_visittime'];
                    $datearray['receiver_name'] = $dateexcelvar['receiver_name'];
                    $datearray['invite_isvisit'] = $dateexcelvar['invite_isvisit'];
                    $datearray['is_audition_name'] = $dateexcelvar['is_audition_name'];
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];
                    $datearray['au_receiver_name'] = $dateexcelvar['au_receiver_name'];
                    $datearray['audition_isvisit'] = $dateexcelvar['audition_isvisit'];
                    $datearray['mian_marketer_name'] = $dateexcelvar['mian_marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['track_createtime'] = date("Y-m-d", $dateexcelvar['track_createtime']);
                    $datearray['track_detail'] = $dateexcelvar['track_detail'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 1) {
                $excelheader = $this->LgArraySwitch(array( '中文名', '英文名', '创建日期', '客户状态', '跟进次数', '年龄', '性别', "联系地址", '渠道类型', '渠道明细', '意向星级', '是否邀约柜询', '柜询时间', '柜询接待人', '柜询是否到访', '是否邀约试听', '试听时间', '试听接待人', '试听是否到访', '主招老师', '辅招老师', '介绍人', '跟踪明细', '最后跟踪时间'));
                $excelfileds = array( 'client_cnname', 'client_enname', 'client_createtime', 'client_tracestatus_name', 'track_num', 'client_age', 'client_sex', 'is_invite', 'is_invite_arrive', 'client_age', 'client_address', 'client_source', 'channel_name', 'client_intention_level', 'is_invite_name', 'invite_visittime', 'receiver_name', 'invite_isvisit', 'is_audition_name', 'audition_visittime', 'au_receiver_name', 'audition_isvisit', 'mian_marketer_name', 'fu_marketer_name', 'client_sponsor', 'track_createtime', 'track_detail');
            } else {
                $excelheader = $this->LgArraySwitch(array( '中文名', '英文名', '创建日期', '客户状态', '跟进次数', '年龄', '性别', "联系地址", '渠道类型', '渠道明细', '意向星级', '是否邀约柜询', '柜询时间', '柜询接待人', '柜询是否到访', '是否邀约试听', '试听时间', '试听接待人', '试听是否到访', '主招老师', '介绍人', '跟踪明细', '最后跟踪时间', '最后跟踪内容'));
                $excelfileds = array( 'client_cnname', 'client_enname', 'client_createtime', 'client_tracestatus_name', 'track_num', 'client_age', 'client_sex', 'is_invite', 'is_invite_arrive', 'client_age', 'client_address', 'client_source', 'channel_name', 'client_intention_level', 'is_invite_name', 'invite_visittime', 'receiver_name', 'invite_isvisit', 'is_audition_name', 'audition_visittime', 'au_receiver_name', 'audition_isvisit', 'mian_marketer_name', 'client_sponsor', 'track_createtime', 'track_detail');
            }


            $fielname = $this->LgStringSwitch("校招生咨询分析表");
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as $key => $dataOne) {
                    $inviteOne = $this->DataControl->selectOne("select i.invite_id,invite_visittime,receiver_name, if(invite_isvisit=1,'是','否') as invite_isvisit
from crm_client_invite as i where i.client_id='{$dataOne['client_id']}' and i.school_id='{$this->school_id}' order by i.invite_id DESC limit 0,1 ");
                    $auditionOne = $this->DataControl->selectOne("select au.audition_id,au.audition_visittime,receiver_name as au_receiver_name,if(audition_isvisit=1,'是','否') as audition_isvisit
from crm_client_audition as au where au.client_id='{$dataOne['client_id']}' and au.school_id='{$this->school_id}' order by au.audition_id DESC limit 0,1");
                    $trackOne = $this->DataControl->selectOne("select track_createtime,track_note,m.marketer_name from crm_client_track as t left join crm_marketer as m On m.marketer_id=t.marketer_id  where t.client_id='{$dataOne['client_id']}' and t.school_id='{$this->school_id}' and t.track_isactive =1 order by t.track_createtime DESC ");

                    $dataList[$key]['is_invite_name'] = $inviteOne ? '是' : '否';
                    $dataList[$key]['invite_visittime'] = $inviteOne ? $inviteOne['invite_visittime'] : '--';
                    $dataList[$key]['receiver_name'] = $inviteOne ? $inviteOne['receiver_name'] : '--';
                    $dataList[$key]['invite_isvisit'] = $inviteOne ? $inviteOne['invite_isvisit'] : '--';
                    $dataList[$key]['is_audition_name'] = $auditionOne ? '是' : '否';
                    $dataList[$key]['audition_visittime'] = $auditionOne ? $auditionOne['audition_visittime'] : '--';
                    $dataList[$key]['au_receiver_name'] = $auditionOne ? $auditionOne['au_receiver_name'] : '--';
                    $dataList[$key]['audition_isvisit'] = $auditionOne ? $auditionOne['audition_isvisit'] : '--';
                    $dataList[$key]['track_createtime'] = $trackOne ? date("Y-m-d", $trackOne['track_createtime']) : '--';
                    $dataList[$key]['marketer_name'] = $trackOne ? $trackOne['marketer_name'] : '--';
                    $dataList[$key]['track_note'] = $trackOne ? $trackOne['track_note'] : '--';
                    $dataList[$key]['client_createtime'] = date("Y-m-d", $dataOne['client_createtime']);
                    $dataList[$key]['client_tracestatus_name'] = $clientTracestatus[$dataOne['client_tracestatus']];

                }
            } else {
                $dataList = array();
            }
            $all_num = $this->DataControl->selectClear("select c.client_id,
             (select count(t.client_id) from crm_client_track as t where t.client_id=c.client_id and t.school_id='{$this->school_id}' and t.track_isactive =1 ) as track_num,
             (select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id= '{$this->school_id}')+( 
            select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id= '{$this->school_id}')) as is_invite,
            (select (select count(i.invite_id)  from crm_client_invite as i where i.client_id = c.client_id and  i.school_id= '{$this->school_id}' and  invite_isvisit =1)+( 
            select count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id and  a.school_id= '{$this->school_id}' and audition_isvisit =1 )) as is_invite_arrive
             from crm_client as c
                left join crm_code_channel as cl On c.channel_id = cl.channel_id
                left join crm_client_schoolenter as cs On cs.client_id = c.client_id 
                where {$datawhere} and cs.school_id='{$this->school_id}' and is_enterstatus =1
                having {$Having} ");
            $data = array();
            $data['all_num'] = is_array($all_num) ? count($all_num) : 0;
            $data['list'] = $dataList;
            return $data;
        }
    }

    /**
     *  校活动业绩统计表
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function activityPositiveReport($request)
    {
        $datawhere = "sa.company_id='{$this->company_id}'";
        $invitewhere = '1';
        $auwhere = '1';
        $chwhere = '1';
        $now_year_where = "r.student_id=s.student_id and ct.client_id=s.from_client_id and ct.activity_id=sa.activity_id and r.school_id = '{$request['school_id']}' and r.info_status =1";
        if (isset($request['starttime']) && $request['starttime']) {
            $stattime = strtotime($request['starttime']);
            $chwhere .= " and ct.client_createtime >='{$stattime}'";
            $auwhere .= " and DATE_FORMAT(ct.audition_visittime,'%Y-%m-%d') >='{$request['starttime']}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') >='{$request['starttime']}'";
            $now_year_where .= " and r.pay_successtime >= '{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endime = strtotime($request['endtime']) + 60 * 60 * 24 - 1;
            $chwhere .= " and ct.client_createtime <='{$endime}'";
            $auwhere .= " and DATE_FORMAT(ct.audition_visittime,'%Y-%m-%d') <='{$request['endtime']}'";
            $invitewhere .= " and DATE_FORMAT(iv.invite_visittime,'%Y-%m-%d') <='{$request['endtime']}'";
            $now_year_where .= " and r.pay_successtime <='{$endime}'";
        }
        if (isset($request['channel_id']) && $request['channel_id']) {
            $datawhere .= " and sa.channel_id='{$request['channel_id']}'";
        }
        if (isset($request['client_source']) && $request['client_source']) {
            $datawhere .= " and sa.frommedia_name='{$request['client_source']}'";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $chwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and  cit.coursecat_id = '{$request['coursecat_id']}' )";
                $auwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and  cit.coursecat_id = '{$request['coursecat_id']}' )";
                $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}'  and cit.coursecat_id = '{$request['coursecat_id']}' )";
                $now_year_where .= " AND r.coursetype_id = '{$request['coursetype_id']}' AND r.coursecat_id = '{$request['coursecat_id']}'";
            } else {
                $chwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $auwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
                $now_year_where .= " AND r.coursetype_id = '{$request['coursetype_id']}'";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $chwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $auwhere .= " AND ct.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $invitewhere .= " AND iv.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $now_year_where .= " AND r.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            select sa.activity_name,l.channel_name,sa.frommedia_name,if(sa.activity_type=1,'集团活动','校园活动') as activity_type_name,
            (select count(ct.client_id) from crm_client as ct,crm_client_schoolenter as s where ct.client_id=s.client_id and ct.activity_id=sa.activity_id and s.school_id='{$request['school_id']}' and {$chwhere} and  s.is_enterstatus=1) as client_allnum,
            (select count(ct.client_id) from crm_client as ct,crm_client_schoolenter as s where ct.client_id=s.client_id and ct.activity_id=sa.activity_id and s.school_id='{$request['school_id']}' and {$chwhere} and ct.client_tracestatus <> '-2' and ct.client_isgross = '0' and  s.is_enterstatus=1) as client_validnum,
            (select count(ct.client_id) from crm_client as ct,crm_client_schoolenter as s where ct.client_id=s.client_id and ct.activity_id=sa.activity_id and s.school_id='{$request['school_id']}' and {$chwhere} and ct.client_tracestatus <> '-2' and ct.client_isgross = '0' and ct.client_intention_maxlevel >= 3 and  s.is_enterstatus=1) as client_level_validnum,
            (select count(DISTINCT c.client_id) from crm_client_invite as  iv,crm_client as c where iv.client_id = c.client_id and c.activity_id =sa.activity_id and iv.school_id='{$request['school_id']}'  and {$invitewhere}) + (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c where ct.client_id=c.client_id and ct.school_id='{$request['school_id']}'  and c.activity_id=sa.activity_id and {$auwhere} and ct.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id = ct.client_id and iv.school_id='{$request['school_id']}'  and {$invitewhere} )) as client_invite_num,  
            ((select count(DISTINCT c.client_id) from crm_client_invite as  iv,crm_client as c where iv.client_id = c.client_id and c.activity_id =sa.activity_id and iv.school_id='{$request['school_id']}'  and iv.invite_isvisit =1 and {$invitewhere}) + (select count(DISTINCT ct.client_id) from crm_client_audition as ct,crm_client as c where ct.client_id=c.client_id and ct.school_id='{$request['school_id']}'  and c.activity_id=sa.activity_id and ct.audition_isvisit =1 and {$auwhere} and ct.client_id not in (select iv.client_id from crm_client_invite as iv where iv.client_id = ct.client_id and iv.school_id='{$request['school_id']}'  and {$invitewhere} and iv.invite_isvisit =1 )) ) as inv_aud_arrivenum,
            (select count(DISTINCT ct.client_id) from smc_student_registerinfo as r,smc_student as s,crm_client as ct where {$now_year_where} ) as inv_aud_positivenum,
            (select count(DISTINCT ct.client_id) from smc_student_registerinfo as r,smc_student as s,crm_client as ct where r.student_id=s.student_id and ct.client_id=s.from_client_id and ct.activity_id=sa.activity_id and r.school_id = '{$request['school_id']}' and r.info_status =1 ) as positive_validnum
            from crm_sell_activity as sa
            left join crm_code_channel as l ON l.channel_id=sa.channel_id
            where {$datawhere} and exists (select 1 from crm_client as t,crm_client_schoolenter as ts where  t.client_id=ts.client_id and t.activity_id=sa.activity_id and ts.school_id='{$this->school_id}' and ts.is_enterstatus =1 )
            HAVING client_allnum > 0
            order by  client_allnum DESC
        ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname,school_openclass", "school_id='{$request['school_id']}'");
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['activity_name'] = $dateexcelvar['activity_name'];
                $datearray['activity_type_name'] = $dateexcelvar['activity_type_name'];
                $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];
                $datearray['channel_name'] = $dateexcelvar['channel_name'];
                $datearray['client_allnum'] = $dateexcelvar['client_allnum'];
                $datearray['client_validnum'] = $dateexcelvar['client_validnum'];
                $datearray['client_level_validnum'] = $dateexcelvar['client_level_validnum'];
                $datearray['client_invite_num'] = $dateexcelvar['client_invite_num'];
                $datearray['inv_aud_arrivenum'] = $dateexcelvar['inv_aud_arrivenum'];
                $datearray['inv_aud_positivenum'] = $dateexcelvar['inv_aud_positivenum'];
                $datearray['positive_validnum'] = $dateexcelvar['positive_validnum'];
                $outexceldate[] = $datearray;
            }

            $excelfileds = $this->LgArraySwitch(array('activity_name', 'activity_type_name', 'frommedia_name', 'channel_name', 'client_allnum', 'client_validnum', 'client_level_validnum', 'client_invite_num', 'inv_aud_arrivenum', 'inv_aud_positivenum', 'positive_validnum'));
            $excelheader = array('活动名称', '活动类型', '招生渠道类型', '招生渠道明细', '新增毛名单', '新增有效名单数', '新增三星以上有效名单数', '新增邀约名单数', '新增邀约到访数', '新增邀约报名数', '累计报名数');

            $fielname = $this->LgStringSwitch("活动业绩统计表");

            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $dataList = $this->DataControl->selectClear($sql . " limit {$pagestart},{$num}");
            if (!$dataList) {
                $dataList = array();
            }
            $allnum = $this->DataControl->selectClear($sql);
            $data = array();
            $data['allnum'] = count($allnum) + 0;
            $data['list'] = is_array($dataList) ? $dataList : array();
            return $data;
        }
    }

    /**
     * 教师月度目标分析表 (渠道业绩报表修改)
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/24 0024
     * @param $request
     */
    function marketerGains($request)
    {
        $audwhere = "au.school_id='{$this->school_id}'";
        $invwhere = "i.school_id='{$this->school_id}'";
        $datawhere = "1 and sg.school_id='{$request['school_id']}'";
        $cgwhere = "1 and cg.school_id='{$request['school_id']}'";
        $trackwhere = "1 and ct.school_id='{$request['school_id']}'";
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $datawhere .= " and sg.goal_createtime >= '{$stattime}'";
            $invwhere .= " and UNIX_TIMESTAMP(i.invite_visittime) >='{$stattime}' ";
            $audwhere .= " and UNIX_TIMESTAMP(au.audition_visittime) >='{$stattime}' ";
            $cgwhere .= " and cg.conversionlog_time >='{$stattime}' ";
            $trackwhere .= " and ct.track_createtime >='{$stattime}' ";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = strtotime($request['end_time']) + 60 * 60 * 24 - 1;
            $datawhere .= " and sg.goal_createtime <='{$endime}'";
            $invwhere .= " and UNIX_TIMESTAMP(i.invite_visittime) <='{$endime}' ";
            $audwhere .= " and UNIX_TIMESTAMP(au.audition_visittime) <='{$endime}' ";
            $cgwhere .= " and cg.conversionlog_time <='{$endime}'";
            $trackwhere .= " and ct.track_createtime <='{$endime}' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and  (sg.goal_name like '%{$request['keyword']}%' or m.marketer_name like '%{$request['keyword']}%' or  sf.staffer_enname like '%{$request['keyword']}%' or sf.staffer_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " 
            select m.marketer_id,m.marketer_name,sf.staffer_enname,sf.staffer_branch,p.post_name,sg.goal_normtrack,sg.goal_norminvite,goal_normofficial,sg.goal_name,concat(goal_starttime,goal_endtime) as goal_time,
            (select count(DISTINCT p.client_id) from crm_client_principal as p where p.marketer_id=m.marketer_id and principal_leave = 0 and p.school_id='{$this->school_id}') as principal_num, 
            (select (select count(au.audition_id) from crm_client_audition as au where au.marketer_id=m.marketer_id and {$audwhere})+
            (select count(i.invite_id) from crm_client_invite as i where i.marketer_id=m.marketer_id and {$invwhere}) ) as  aud_invite_num,
            (select count(cg.conversionlog_id) from crm_client_conversionlog as cg where cg.marketer_id=m.marketer_id and {$cgwhere}) as  conversion_num,
            (select count(ct.track_id) from crm_client_track as ct where ct.marketer_id=m.marketer_id and {$trackwhere}) as track_num
            from  crm_marketer as m
            left join crm_sell_goal as sg On m.marketer_id=sg.marketer_id 
            left join smc_staffer as sf On sf.staffer_id =m.staffer_id
            left join gmc_staffer_postbe as pb ON pb.staffer_id=sf.staffer_id  and pb.school_id='{$this->school_id}'
            left join gmc_company_post as p ON p.post_id=pb.post_id
            where {$datawhere}  
        ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            foreach ($dateexcelarray as $key => $dataOne) {
                $dateexcelarray[$key]['track_goal_num'] = intval($dataOne['track_num']) . '/' . $dataOne['goal_normtrack'];
                $dateexcelarray[$key]['invite_aud_goal_num'] = intval($dataOne['aud_invite_num']) . '/' . $dataOne['goal_norminvite'];
                $dateexcelarray[$key]['positive_goal_num'] = intval($dataOne['conversion_num']) . '/' . $dataOne['goal_normofficial'];
                $dateexcelarray[$key]['conversion_rate'] = $dataOne['goal_normofficial'] > 0 ? round($dataOne['conversion_num'] / $dataOne['goal_normofficial'], 4) * 100 : 0;
            }
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                $datearray['post_name'] = $dateexcelvar['post_name'];
                $datearray['principal_num'] = $dateexcelvar['principal_num'];
                $datearray['goal_name'] = $dateexcelvar['goal_name'];
                $datearray['goal_time'] = $dateexcelvar['goal_time'];
                $datearray['track_goal_num'] = $dateexcelvar['track_goal_num'];
                $datearray['invite_aud_goal_num'] = $dateexcelvar['invite_aud_goal_num'];
                $datearray['positive_goal_num'] = $dateexcelvar['positive_goal_num'];
                $datearray['conversion_rate'] = $dateexcelvar['conversion_rate'];
                $outexceldate[] = $datearray;
            }
            $excelheader = $this->LgArraySwitch(array('marketer_name', 'staffer_enname', 'staffer_branch', 'post_name', 'principal_num', 'goal_name', 'goal_time', "track_goal_num", 'invite_aud_goal_num', 'positive_goal_num', 'conversion_rate'));
            $excelfileds = array('教师中文名', '教师英文名', '教师编号', '在校职务', '意向名单数', '招生目标名称', '目标执行时间', '跟踪人次/目标跟踪人次', '柜询试听人次/目标柜询试听人次', '转化人次/目标转化人次', '转化率');

            $fielname = $this->LgStringSwitch("教师月度目标分析表");
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as $key => $dataOne) {
                    $dataList[$key]['track_goal_num'] = intval($dataOne['track_num']) . '/' . $dataOne['goal_normtrack'];
                    $dataList[$key]['invite_aud_goal_num'] = intval($dataOne['aud_invite_num']) . '/' . $dataOne['goal_norminvite'];
                    $dataList[$key]['positive_goal_num'] = intval($dataOne['conversion_num']) . '/' . $dataOne['goal_normofficial'];
                    $dataList[$key]['conversion_rate'] = $dataOne['goal_normofficial'] > 0 ? round($dataOne['conversion_num'] / $dataOne['goal_normofficial'], 4) * 100 : 0;
                }
            } else {
                $dataList = array();
            }
            $allnum = $this->DataControl->selectOne("select count(1) as num from  crm_marketer as m
            left join crm_sell_goal as sg On m.marketer_id=sg.marketer_id 
            left join smc_staffer as sf On sf.staffer_id =m.staffer_id
            left join gmc_staffer_postbe as pb ON pb.staffer_id=sf.staffer_id and pb.school_id='{$this->school_id}'
            left join gmc_company_post as p ON p.post_id=pb.post_id
            where {$datawhere}  and sg.school_id='{$this->school_id}' ");

            $result = array();
            $result['allnum'] = is_array($allnum) ? $allnum['num'] : 0;
            $result['list'] = $dataList;
            return $result;
        }
    }


    /**
     * 活动名单明细
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/25 0025
     * @param $request
     * @return array
     */
    function activityClientList($request)
    {
//        '招生活动模式：0普招模式1验券模式2海报模式//
//        0待跟踪1持续跟踪2已柜询3已视听4已转正-1无意向-2无效名单
        $array_pattern = array(0 => '普通活动', 1 => '招生券活动', 2 => '海报活动');
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        $datawhere = "sa.company_id='{$this->company_id}' and cs.school_id='{$this->school_id}' and sa.activity_type = '0' ";
        if (isset($request['starttime']) && $request['starttime']) {
            $stattime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >= '{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endtime = strtotime($request['endtime']) + 24 * 3600;
            $datawhere .= " and c.client_createtime < '{$endtime}'";
        }

        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus = '{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sa.activity_name like  '%{$request['keyword']}%' or c.client_cnname like '%{$request['keyword']}%' or p.promotion_name like '%{$request['keyword']}%' )";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sa.activity_name,if(sa.activity_type=1,'集团活动','校园活动') as activity_type_name,concat(sa.activity_starttime,'~',sa.activity_endtime) as activity_time,activity_pattern,
                c.client_cnname,c.client_enname,c.client_age,c.client_sex,c.client_mobile,c.client_tracestatus,c.client_createtime,c.client_source,cl.channel_name,c.client_id,p.promotion_name,ifnull(p.promotion_jobnumber,'--') as promotion_jobnumber,p.promotion_type,c.client_frompage,c.client_answerphone,c.client_intention_maxlevel,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) as marketer_name,
                (SELECT mk.marketer_name FROM crm_marketer as mk WHERE mk.marketer_id = c.marketer_id) as lrmarketer_name,
                (select p.parenter_cnname from crm_client_family as f,smc_parenter as p where f.parenter_id=p.parenter_id and f.client_id=c.client_id order by family_isdefault DESC limit 0,1 ) as parenter_cnname,
                (select ct.track_createtime from crm_client_track as ct where ct.client_id=c.client_id and ct.school_id=cs.school_id and ct.track_isactive =1 order by ct.track_createtime DESC limit 0,1) as track_createtime,
                (select ct.track_note from crm_client_track as ct where ct.client_id=c.client_id and ct.school_id=cs.school_id and ct.track_isactive =1 order by ct.track_createtime DESC limit 0,1) as track_note,
                (select au.audition_id from crm_client_audition as au where au.client_id=c.client_id and au.school_id=cs.school_id order by au.audition_id DESC limit 0,1) as audition_id,
                (select i.invite_id from crm_client_invite as i where i.client_id=c.client_id and i.school_id=cs.school_id order by i.invite_id DESC limit 0,1) as invite_id,
                (select au.audition_id from crm_client_audition as au where au.client_id=c.client_id and au.school_id=cs.school_id and au.audition_isvisit =1 limit 0,1) as arrive_audition_id,
                (select i.invite_id from crm_client_invite as i where i.client_id=c.client_id and i.school_id=cs.school_id and i.invite_isvisit =1 limit 0,1) as arrive_invite_id,
                (select positivelog_id from crm_client_positivelog as pg where pg.school_id=cs.school_id and pg.client_id =c.client_id limit 0,1 ) as positivelog_id,
                (select rf.info_id  from crm_client_positivelog pg ,smc_student st,smc_student_registerinfo rf where pg.school_id = cs.school_id and pg.client_id = c.client_id and st.student_branch = pg.student_branch and rf.student_id = st.student_id and rf.pay_price > 0 and rf.info_status = '1' limit 0,1 ) as info_id 
                from crm_sell_activity as sa 
                left join crm_client as c ON sa.activity_id=c.activity_id
                left join crm_client_schoolenter as cs ON cs.client_id=c.client_id
                left join crm_marketer as m ON m.marketer_id=sa.marketer_id 
                left join smc_staffer as  sf ON sf.staffer_id=m.staffer_id
                left join crm_code_channel as cl ON cl.channel_id=c.channel_id
                left join crm_ground_promotion as p ON p.promotion_id=c.promotion_id
                where {$datawhere} and cs.is_enterstatus =1
                order by client_createtime DESC
                 ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['client_id'] = $dateexcelvar['client_id'];
                $datearray['activity_name'] = $dateexcelvar['activity_name'];
                $datearray['activity_pattern_name'] = $array_pattern[$dateexcelvar['activity_pattern']];
                $datearray['activity_time'] = $dateexcelvar['activity_time'];
                $datearray['activity_type_name'] = $dateexcelvar['activity_type_name'];
                $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                $datearray['client_enname'] = $dateexcelvar['client_enname'];
                $datearray['client_sex'] = $dateexcelvar['client_sex'];
                $datearray['client_age'] = $dateexcelvar['client_age'];
                $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                if ($request['re_postbe_id'] > 0) {
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                } else {
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                }
                $datearray['client_answerphone_name'] = ($dateexcelvar['client_answerphone'] == '1') ? (($dateexcelvar['client_intention_maxlevel'] > '2') ? '接通有效' : '接通无效') : '未接通';
                $datearray['client_tracestatus_name'] = $clientTracestatus[$dateexcelvar['client_tracestatus']];
                $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber'];
                if (is_null($dateexcelvar['promotion_type'])) {
                    $datearray['post_name'] = '--';
                } else {
                    if ($dateexcelvar['promotion_type'] == 0) {
                        $datearray['post_name'] = '市场';
                    } else {
                        $datearray['post_name'] = '销售';
                    }
                }
                $datearray['client_frompage'] = $dateexcelvar['client_frompage'] ? $dateexcelvar['client_frompage'] : '--';

                $datearray['track_createtime'] = $dateexcelvar['track_createtime'] ? date("Y-m-d", $dateexcelvar['track_createtime']) : '--';
                $datearray['track_note'] = $dateexcelvar['track_note'];
                $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                if ($dateexcelvar['audition_id'] === null && $dateexcelvar['invite_id'] === null) {
                    $datearray['is_aud_invite'] = '否';
                } else {
                    $datearray['is_aud_invite'] = '是';
                }
                $datearray['is_aud_invite_arrive'] = ($dateexcelvar['arrive_invite_id'] + $dateexcelvar['arrive_audition_id'] + 0) > 0 ? '是' : '否';
                $datearray['is_audition'] = ($dateexcelvar['arrive_audition_id'] + 0) > 0 ? '是' : '否';
                $datearray['is_positive'] = ($dateexcelvar['positivelog_id'] + 0) > 0 ? '是' : '否';
//                $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                if ($dateexcelvar['promotion_name']) {
                    $datearray['lrmarketer_name'] = $dateexcelvar['promotion_name'];
                } else {
                    $datearray['lrmarketer_name'] = $dateexcelvar['lrmarketer_name'];
                }
                $datearray['client_source'] = $dateexcelvar['client_source'];
                $datearray['channel_name'] = $dateexcelvar['channel_name'];
                $outexceldate[] = $datearray;
            }
            $excelfileds = $this->LgArraySwitch(array('client_id', 'activity_name', 'activity_pattern_name', 'activity_time', 'activity_type_name', 'client_cnname', 'client_enname', 'client_sex', "client_age", 'parenter_cnname', 'client_mobile', 'client_answerphone_name', 'client_tracestatus_name', 'promotion_jobnumber', 'post_name', 'client_frompage', 'track_createtime', 'track_note', 'client_createtime', 'is_aud_invite', 'is_aud_invite_arrive', 'is_audition', 'is_positive', 'lrmarketer_name', 'client_source', 'channel_name'));
            $excelheader = array('会员序号', '招生活动名称', '招生活动模式', '招生活动时间', '招生活动类型', '中文名', '英文名', '性别', '年龄', '主要联系人', '联系号码', '接通状态', '客户状态', '地推工号', '职务', '接触点', '最后跟踪时间', '最后跟踪内容', '创建时间', '是否邀约', '是否到访', '是否试听', '是否报名', '录入人', '渠道类型', '渠道明细');

            $fielname = $this->LgStringSwitch("活动名单明细报表");
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as $key => $dataOne) {
                    $dataList[$key]['activity_pattern_name'] = $array_pattern[$dataOne['activity_pattern']];
                    $dataList[$key]['client_tracestatus_name'] = $clientTracestatus[$dataOne['client_tracestatus']];
                    $dataList[$key]['track_createtime'] = $dataOne['track_createtime'] ? date("Y-m-d", $dataOne['track_createtime']) : '--';
                    $dataList[$key]['client_createtime'] = date("Y-m-d", $dataOne['client_createtime']);
                    if ($dataOne['audition_id'] === null && $dataOne['invite_id'] === null) {
                        $dataList[$key]['is_aud_invite'] = '否';
                    } else {
                        $dataList[$key]['is_aud_invite'] = '是';
                    }
                    $dataList[$key]['is_aud_invite_arrive'] = ($dataOne['arrive_invite_id'] + $dataOne['arrive_audition_id'] + 0) > 0 ? '是' : '否';
                    $dataList[$key]['is_audition'] = ($dataOne['arrive_audition_id'] + 0) > 0 ? '是' : '否';
                    $dataList[$key]['is_positive'] = ($dataOne['positivelog_id'] + 0) > 0 ? '是' : '否';
                    $dataList[$key]['is_pay'] = ($dataOne['info_id'] + 0) > 0 ? '是' : '否';
                    $dataList[$key]['marketer_name'] = $dataOne['marketer_name'] ? $dataOne['marketer_name'] : '--';
                    $dataList[$key]['client_frompage'] = $dataOne['client_frompage'] ? $dataOne['client_frompage'] : '--';
                    $dataList[$key]['client_answerphone_name'] = ($dataOne['client_answerphone'] == '1') ? (($dataOne['client_intention_maxlevel'] > '2') ? '接通有效' : '接通无效') : '未接通';
                    if ($dataOne['promotion_name']) {
                        $dataList[$key]['lrmarketer_name'] = $dataOne['promotion_name'];
                    } else {
                        $dataList[$key]['lrmarketer_name'] = $dataOne['lrmarketer_name'] ? $dataOne['lrmarketer_name'] : '--';
                    }
                    if ($this->isGmcPost == true) {
//                        $dataList[$key]['client_mobile'] = str_replace(substr($dataOne['client_mobile'], 3, 4), '****', $dataOne['client_mobile']);
                        $dataList[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dataOne['client_mobile']);
                    } else {
                        $dataList[$key]['client_mobile'] = $dataOne['client_mobile'];
                    }
                    if (is_null($dataOne['promotion_type'])) {
                        $dataList[$key]['post_name'] = '--';
                    } else {
                        if ($dataOne['promotion_type'] == 0) {
                            $dataList[$key]['post_name'] = '市场';
                        } else {
                            $dataList[$key]['post_name'] = '销售';
                        }
                    }
                }
            } else {
                $dataList = array();
            }
            $allnum = $this->DataControl->selectOne("select count(1) as allnum from crm_sell_activity as sa 
                left join crm_client as c ON sa.activity_id=c.activity_id
                left join crm_client_schoolenter as cs ON cs.client_id=c.client_id
                left join crm_marketer as m ON m.marketer_id=sa.marketer_id 
                left join crm_code_channel as cl ON cl.channel_id=c.channel_id
                left join crm_ground_promotion as p ON p.promotion_id=c.promotion_id
                where {$datawhere} and cs.is_enterstatus =1  ");

            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = $allnum['allnum'] + 0;
            return $result;
        }
    }

    /**
     * 个人执行检核
     * author: ling
     * 对应接口文档 0001
     * Date 2020/12/28 0028
     * @param $request
     * @return mixed
     */
    function marketerExecute($request)
    {
        $datawhere = " 1 AND EXISTS (SELECT p.principal_id FROM crm_client_principal AS p,crm_client AS c
WHERE p.marketer_id = m.marketer_id AND p.client_id = c.client_id AND p.principal_leave <> '1' AND p.school_id = '{$request['school_id']}' AND c.client_tracestatus <> '4')";
        $prinwhere = "1";

        $prinstuwhere = " 1 ";
        if (isset($request['start_time']) && $request['start_time']) {
            $stattime = strtotime($request['start_time']);
            $prinwhere .= " p.principal_createtime >='{$stattime}'";
            $prinstuwhere .= " p.principal_createtime >='{$stattime}' ";
        }
        if (isset($request['end_time']) && $request['end_time']) {
            $endime = strtotime($request['end_time']) + 60 * 60 * 24 - 1;
            $prinwhere .= " p.principal_createtime <='{$endime}'";
            $prinstuwhere .= " p.principal_createtime <='{$endime}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
                $prinwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
                $prinstuwhere .= " AND c.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' and cit.coursecat_id = '{$request['coursecat_id']}' )";
            } else {
                $prinwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' )";
                $prinstuwhere .= " AND c.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' )";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $prinwhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
            $prinstuwhere .= " AND c.student_id IN (SELECT cit.student_id FROM crm_student_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select 
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) as marketer_name,sf.staffer_enname,sf.staffer_branch,sf.staffer_leave,            
  
                (select count(p.principal_id) from crm_student_principal as p,crm_student as c where c.student_id=p.student_id and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and p.principal_leave =0 and c.student_distributionstatus = '1' and c.student_type = 0 and {$prinstuwhere}) as principal_stunumthan,
                (select count(distinct (t.student_id)) from crm_student_track as t where t.marketer_id = m.marketer_id and t.school_id = '{$request['school_id']}' and t.track_isactive = '1' and exists(select 1 from crm_student_principal as p,crm_student as c where c.student_id=p.student_id and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and p.principal_leave =0 and c.student_distributionstatus = '1' and c.student_id = t.student_id and c.student_type = 0 limit 0,1) ) as principal_stutracknum,  
                
                (select count(p.principal_id) from crm_client_principal as p,crm_client as c where c.client_id=p.client_id and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0 and c.client_tracestatus <> '4' and {$prinwhere}) as principal_num,
                (select count(p.principal_id) from crm_client_principal as p,crm_client as c where c.client_id=p.client_id and  p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0 and c.client_tracestatus <> '4' and {$prinwhere} and not exists (select 1 from crm_client_track as t where t.school_id=p.school_id and t.client_id=p.client_id and track_isactive =1  )) as principal_no_tracknum,
                (select count(p.principal_id) from crm_client_principal as p,crm_client as c where c.client_id = p.client_id  and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0 and c.client_tracestatus <> '4' and not exists (select 1 from crm_client_invite as i where i.school_id=p.school_id and i.client_id=p.client_id and i.marketer_id=p.marketer_id and i.invite_isvisit <> '-1') and {$prinwhere} and not exists (select 1 from crm_client_audition as a where a.school_id=p.school_id and a.client_id=p.client_id and a.audition_isvisit <> '-1' ) ) as principal_no_aud_invitenum,
                (select count(p.principal_id) from crm_client_principal as p,crm_client_invite as i,crm_client as c where i.client_id=c.client_id and i.client_id=p.client_id and p.school_id = i.school_id and i.invite_isvisit=0 and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0    
                and c.client_tracestatus <> '4' and {$prinwhere}) as principal_no_invite_num,
                (select count(p.principal_id) from crm_client_principal as p,crm_client_audition as a,crm_client as c where a.client_id=p.client_id and  p.client_id=c.client_id and a.school_id = p.school_id and a.audition_isvisit=0 and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0 and c.client_tracestatus <> '4' and {$prinwhere}  ) as principal_no_audition_num,
                (select count(p.principal_id) from crm_client_principal as p,crm_client as c where c.client_id=p.client_id and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and p.principal_leave =0  and {$prinwhere} and (exists (select 1 from crm_client_invite as i where i.school_id=p.school_id and i.client_id=p.client_id and i.invite_isvisit = '1') or exists (select 1 from crm_client_audition as a where a.school_id=p.school_id and a.client_id=p.client_id and a.audition_isvisit = '1' )) and c.client_tracestatus <> '4') as principal_no_positive_num,
                (select count(p.principal_id) from crm_client_principal as p,crm_client as c where p.client_id = c.client_id and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0 and {$prinwhere} and c.client_tracestatus >=0 and c.client_tracestatus <4  and not exists (select 1 from crm_client_track as t where t.track_isactive and  t.school_id=p.school_id and t.client_id=p.client_id and t.track_createtime > UNIX_TIMESTAMP(date_sub(curdate(), INTERVAL 15 DAY)) ) ) as principal_notrack_num,
                (select count(DISTINCT c.client_id) from crm_client_principal as p,crm_client as c where p.client_id = c.client_id and p.school_id='{$request['school_id']}' and p.marketer_id =m.marketer_id and principal_leave =0  and c.client_tracestatus <> '4' and {$prinwhere} and not exists (select count(t.track_id) as track_num from crm_client_track as t where  t.track_isactive and t.school_id=p.school_id and t.marketer_id=p.marketer_id and t.client_id=p.client_id  having track_num > 3)) as principal_notthreerack_num
                  FROM crm_marketer as m, smc_staffer as sf
                  WHERE {$datawhere} AND sf.staffer_leave = 0 AND sf.account_class = 0 AND sf.staffer_id =m.staffer_id ORDER BY principal_no_aud_invitenum DESC ";

        if (isset($request['is_export']) && $request['is_export'] == '1') {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                $datearray['principal_num'] = $dateexcelvar['principal_num'];
                $datearray['principal_stunum'] = $dateexcelvar['principal_stunumthan'];
                $datearray['principal_no_tracknum'] = $dateexcelvar['principal_no_tracknum'];
                $datearray['principal_no_aud_invitenum'] = $dateexcelvar['principal_no_aud_invitenum'];
                $datearray['principal_no_invite_num'] = $dateexcelvar['principal_no_invite_num'];
                $datearray['principal_no_audition_num'] = $dateexcelvar['principal_no_audition_num'];
                $datearray['principal_no_positive_num'] = $dateexcelvar['principal_no_positive_num'];
                $datearray['principal_stunumthan'] = round($dateexcelvar['principal_stutracknum'] / $dateexcelvar['principal_stunumthan'] * 100, 2) . '%';
                $datearray['principal_notrack_num'] = $dateexcelvar['principal_notrack_num'];
                $datearray['principal_notthreerack_num'] = $dateexcelvar['principal_notthreerack_num'];
                $outexceldate[] = $datearray;
            }

            $excelheader = array('教师姓名', '教师编号', "意向客户数","扩科意向客户数", "待有效跟踪客户数", "待邀约客户数", "柜询待确认数", "试听待确认数", "到访未报名数","扩科客户跟踪比", "15日内未进行跟踪客户数", "低频跟踪客户数");
            $excelfileds = $this->LgArraySwitch(array("marketer_name", "staffer_branch", "principal_num","principal_stunum", "principal_no_tracknum", "principal_no_aud_invitenum", "principal_no_invite_num", "principal_no_audition_num", "principal_no_positive_num", "principal_stunumthan","principal_notrack_num", "principal_notthreerack_num"));

            $fielname = $this->LgStringSwitch("个人招生执行检核表");
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
        }
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$dataOne) {
                $dataOne['staffer_leave'] = $dataOne['staffer_leave'] == '1' ? '离职' : '在职';
                $dataOne['principal_stunum'] = $dataOne['principal_stunumthan'];
                if($dataOne['principal_stunumthan'] > 0){
                    $dataOne['principal_stunumthan'] = round($dataOne['principal_stutracknum'] / $dataOne['principal_stunumthan'] * 100, 2) . '%';
                }else{
                    $dataOne['principal_stunumthan'] = '0.00%';
                }
            }
        }
        $allnum = $this->DataControl->selectOne("select count(1) as allnum from crm_marketer as m,smc_staffer as sf
                  where {$datawhere} and sf.staffer_leave =0 and sf.account_class = 0 AND sf.staffer_id =m.staffer_id");
        $result = array();
        $result['allnum'] = $allnum['allnum'] + 0;
        $result['list'] = $dataList;
        return $result;
    }


    //招生业绩年度汇总表
    function getPerformanceYear($request)
    {
        $schoolwhere = "s.company_id = '{$request['company_id']}' and p.school_id = '{$request['school_id']}' and p.principal_ismajor = 1 and p.principal_leave = 0 and p.marketer_id > 0 and s.school_isclose = 0 and s.school_istest = 0 and c.client_tracestatus in (0,1,2,3) and c.client_isgross = 0";
        $where = "vp.school_id = p.school_id";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (s.school_shortname like '%{$request['keyword']}%' or d.district_cnname like '%{$request['keyword']}%') ";
        }
        //班组
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $where .= " and vp.coursetype_id = '{$request['coursetype_id']}'";
        }
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $starttime = strtotime($request['start_time']);
        } else {
            $starttime = strtotime(date("Y-1-1"));
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endtime = strtotime($request['end_time']) + 60 * 60 * 24 - 1;
        } else {
            $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        }
        $where .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.school_id,s.school_shortname,s.school_enname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,count(IF ( c.client_intention_maxlevel > 2, p.client_id, NULL )) as clientnum,
                (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_city) as school_districtname,d.district_cnname,
                (SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.school_id = p.school_id AND b.staffer_id = sf.staffer_id) as post_name,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$where} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_clientnum
                from crm_client_principal as p
                left join crm_client as c ON c.client_id = p.client_id
                left join smc_school as s ON s.school_id = p.school_id
                left join crm_marketer as m ON m.marketer_id = p.marketer_id
                left join smc_staffer as sf ON sf.staffer_id = m.staffer_id
                left join gmc_company_district as d ON d.district_id = s.district_id
                where {$schoolwhere}
                group by p.marketer_id,p.school_id
                having clientnum > 0
                order by p.school_id";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");

        if ($marketList) {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $temp['school_districtname'] = $value['school_districtname'];//地区
                $temp['school_branch'] = $value['school_branch'];//校区编号
                $temp['school_shortname'] = $value['school_shortname'];//校区名称
                $temp['district_cnname'] = $value['district_cnname'] ? $value['district_cnname'] : '--';//所属区域
                $temp['organization'] = $value['organization'] ? $value['organization'] : '--';//校务编制
                $temp['staffer_cnname'] = $value['staffer_cnname'];//中文名
                $temp['staffer_enname'] = $value['staffer_enname'] ? $value['staffer_enname'] : '--';//英文名
                $temp['post_name'] = $value['post_name'] ? $value['post_name'] : '--';//本校职务
                $temp['clientnum'] = $value['clientnum'];//系统名单总数
                $temp['princ_clientnum'] = $value['princ_clientnum'];//报名/当年
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_districtname'] = $dateexcelvar['school_districtname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['organization'] = $dateexcelvar['organization'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];
                    $datearray['princ_clientnum'] = $dateexcelvar['princ_clientnum'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('地区', '校区编号', '校区名称', '所属区域', '校务编制', '中文名', '英文名', '本校职务', '系统名单总数', '报名/当年'));
            $excelfileds = array('school_districtname', 'school_branch', 'school_shortname', 'district_cnname', 'organization', 'staffer_cnname', 'staffer_enname', 'post_name', 'clientnum', 'princ_clientnum');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生业绩年度汇总表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $mark_data;
        return $data;
    }


    //招生业绩月度汇总表
    function getPerformanceMonth($request)
    {
        $schoolwhere = "s.company_id = '{$request['company_id']}' and p.school_id = '{$request['school_id']}' and p.principal_ismajor = 1 and p.principal_leave = 0 and p.marketer_id > 0 and s.school_isclose = 0 and s.school_istest = 0 and c.client_tracestatus in (0,1,2,3) and c.client_isgross = 0";
        $upmonthwhere = "vp.school_id = p.school_id";
        $nowmonthwhere = "vp.school_id = p.school_id";
        $monthwhere = "vp.school_id = p.school_id";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (s.school_shortname like '%{$request['keyword']}%' or d.district_cnname like '%{$request['keyword']}%') ";
        }
        //班组
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $upmonthwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $nowmonthwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $monthwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
        }

        $upmonth = GetTheMonth(GetMonth(date("Y-m-d")));
        $starttime = strtotime($upmonth[0]);
        $endtime = strtotime($upmonth[1]) + 60 * 60 * 24 - 1;
        $upmonthwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $nowmonth = GetTheMonth(date("Y-m-d"));
        $starttime = strtotime($nowmonth[0]);
        $endtime = strtotime($nowmonth[1]) + 60 * 60 * 24 - 1;
        $nowmonthwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime("-1 month");
        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        $monthwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.school_id,s.school_shortname,s.school_enname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,count(IF ( c.client_intention_maxlevel > 2, p.client_id, NULL )) as clientnum,
                (SELECT d.region_name FROM smc_code_region as d WHERE d.region_id = s.school_city) as school_districtname,d.district_cnname,
                (SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.school_id = p.school_id AND b.staffer_id = sf.staffer_id) as post_name,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$upmonthwhere} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_upmonth_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$nowmonthwhere} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_nowmonth_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$monthwhere} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_month_num
                from crm_client_principal as p
                left join crm_client as c ON c.client_id = p.client_id
                left join smc_school as s ON s.school_id = p.school_id
                left join crm_marketer as m ON m.marketer_id = p.marketer_id
                left join smc_staffer as sf ON sf.staffer_id = m.staffer_id
                left join gmc_company_district as d ON d.district_id = s.district_id
                where {$schoolwhere}
                group by p.marketer_id,p.school_id
                having clientnum > 0
                order by p.school_id";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");

        if ($marketList) {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $temp['school_districtname'] = $value['school_districtname'];//地区
                $temp['school_branch'] = $value['school_branch'];//校区编号
                $temp['school_shortname'] = $value['school_shortname'];//校区名称
                $temp['district_cnname'] = $value['district_cnname'] ? $value['district_cnname'] : '--';//所属区域
                $temp['organization'] = $value['organization'] ? $value['organization'] : '--';//校务编制
                $temp['staffer_cnname'] = $value['staffer_cnname'];//中文名
                $temp['staffer_enname'] = $value['staffer_enname'] ? $value['staffer_enname'] : '--';//英文名
                $temp['post_name'] = $value['post_name'] ? $value['post_name'] : '--';//本校职务
                $temp['clientnum'] = $value['clientnum'];//系统名单总数
                $temp['princ_upmonth_num'] = $value['princ_upmonth_num'];//报名/上月
                $temp['princ_nowmonth_num'] = $value['princ_nowmonth_num'];//报名/当月
                $temp['princ_month_num'] = $value['princ_month_num'];//报名/月
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_districtname'] = $dateexcelvar['school_districtname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['organization'] = $dateexcelvar['organization'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];
                    $datearray['princ_upmonth_num'] = $dateexcelvar['princ_upmonth_num'];
                    $datearray['princ_nowmonth_num'] = $dateexcelvar['princ_nowmonth_num'];
                    $datearray['princ_month_num'] = $dateexcelvar['princ_month_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('地区', '校区编号', '校区名称', '所属区域', '校务编制', '中文名', '英文名', '本校职务', '系统名单总数', '报名/上月', '报名/当月', '报名/月'));
            $excelfileds = array('school_districtname', 'school_branch', 'school_shortname', 'district_cnname', 'organization', 'staffer_cnname', 'staffer_enname', 'post_name', 'clientnum', 'princ_upmonth_num', 'princ_nowmonth_num', 'princ_month_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生业绩月度汇总表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $mark_data;
        return $data;
    }


    //招生业绩周度汇总表
    function getPerformanceWeek($request)
    {
        $schoolwhere = "s.company_id = '{$request['company_id']}' and p.school_id = '{$request['school_id']}' and p.principal_ismajor = 1 and p.principal_leave = 0 and p.marketer_id > 0 and s.school_isclose = 0 and s.school_istest = 0 and c.client_tracestatus in (0,1,2,3) and c.client_isgross = 0";
        $upweekwhere = "vp.school_id = p.school_id";
        $nowweekwhere = "vp.school_id = p.school_id";
        $weekwhere = "vp.school_id = p.school_id";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (s.school_shortname like '%{$request['keyword']}%' or d.district_cnname like '%{$request['keyword']}%') ";
        }
        //班组
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $upweekwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $nowweekwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $weekwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
        }

        $week = GetWeekAll(date("Y-m-d"));
        $starttime = strtotime($week['lastweek_start']);
        $endtime = strtotime($week['lastweek_end']) + 60 * 60 * 24 - 1;
        $upweekwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime($week['nowweek_start']);
        $endtime = strtotime($week['nowweek_end']) + 60 * 60 * 24 - 1;
        $nowweekwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime("-7 day");
        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        $weekwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.school_id,s.school_shortname,s.school_enname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,count(IF ( c.client_intention_maxlevel > 2, p.client_id, NULL )) as clientnum,
                (SELECT d.region_name FROM smc_code_region as d WHERE d.region_id = s.school_city) as school_districtname,d.district_cnname,
                (SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.school_id = p.school_id AND b.staffer_id = sf.staffer_id) as post_name,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$upweekwhere} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_upweek_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$nowweekwhere} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_nowweek_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$weekwhere} and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))) as princ_week_num
                from crm_client_principal as p
                left join crm_client as c ON c.client_id = p.client_id
                left join smc_school as s ON s.school_id = p.school_id
                left join crm_marketer as m ON m.marketer_id = p.marketer_id
                left join smc_staffer as sf ON sf.staffer_id = m.staffer_id
                left join gmc_company_district as d ON d.district_id = s.district_id
                where {$schoolwhere}
                group by p.marketer_id,p.school_id
                having clientnum > 0
                order by p.school_id";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");

        if ($marketList) {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $temp['school_districtname'] = $value['school_districtname'];//地区
                $temp['school_branch'] = $value['school_branch'];//校区编号
                $temp['school_shortname'] = $value['school_shortname'];//校区名称
                $temp['district_cnname'] = $value['district_cnname'] ? $value['district_cnname'] : '--';//所属区域
                $temp['organization'] = $value['organization'] ? $value['organization'] : '--';//校务编制
                $temp['staffer_cnname'] = $value['staffer_cnname'];//中文名
                $temp['staffer_enname'] = $value['staffer_enname'] ? $value['staffer_enname'] : '--';//英文名
                $temp['post_name'] = $value['post_name'] ? $value['post_name'] : '--';//本校职务
                $temp['clientnum'] = $value['clientnum'];//系统名单总数
                $temp['princ_upweek_num'] = $value['princ_upweek_num'];//报名/上周
                $temp['princ_nowweek_num'] = $value['princ_nowweek_num'];//报名/当周
                $temp['princ_week_num'] = $value['princ_week_num'];//报名/周
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_districtname'] = $dateexcelvar['school_districtname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['organization'] = $dateexcelvar['organization'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];
                    $datearray['princ_upweek_num'] = $dateexcelvar['princ_upweek_num'];
                    $datearray['princ_nowweek_num'] = $dateexcelvar['princ_nowweek_num'];
                    $datearray['princ_week_num'] = $dateexcelvar['princ_week_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('地区', '校区编号', '校区名称', '所属区域', '校务编制', '中文名', '英文名', '本校职务', '系统名单总数', '报名/上周', '报名/当周', '报名/周'));
            $excelfileds = array('school_districtname', 'school_branch', 'school_shortname', 'district_cnname', 'organization', 'staffer_cnname', 'staffer_enname', 'post_name', 'clientnum', 'princ_upweek_num', 'princ_nowweek_num', 'princ_week_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生业绩周度汇总表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $mark_data;
        return $data;
    }

    //招生邀约状况统计表
    function getInvite($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and s.school_shortname like '%{$request['keyword']}%'";
        }
        //班组
        $coursetypewhere = " 1 ";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $coursetypewhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
        }

        $iwhere = ' 1 ';
        $awhere = ' 1 ';
        $regiwhere = ' 1 ';
        $regawhere = ' 1 ';
        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $stattime = strtotime($request['starttime']);
            $iwhere .= " and unix_timestamp(i.invite_visittime) >='{$stattime}'";
            $awhere .= " and unix_timestamp(i.audition_visittime) >='{$stattime}'";
            $regiwhere .= " and unix_timestamp(i.visittime) >='{$stattime}'";
            $regawhere .= " and unix_timestamp(i.visittime) >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']));
            $endime = strtotime($endime) + 60 * 60 * 24 - 1;
            $iwhere .= " and unix_timestamp(i.invite_visittime) <='{$endime}'";
            $awhere .= " and unix_timestamp(i.audition_visittime) <='{$endime}'";
            $regiwhere .= " and unix_timestamp(i.visittime) <='{$endime}'";
            $regawhere .= " and unix_timestamp(i.visittime) <='{$endime}'";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
SELECT
	p.marketer_id,
	( SELECT r.region_name FROM smc_code_region AS r WHERE r.region_id = s.school_city ) AS region_name,
	s.school_branch,
	s.school_cnname,
	( SELECT r.district_cnname FROM gmc_company_district AS r WHERE r.district_id = s.district_id ) AS district_cnname,
	f.staffer_cnname,
	f.staffer_enname,
	(
	SELECT
		ps.post_name 
	FROM
		gmc_company_post AS ps,
		gmc_staffer_postbe AS b 
	WHERE
		ps.post_id = b.post_id 
		AND b.school_id = p.school_id 
		AND b.staffer_id = f.staffer_id 
	) AS post_name,
	COUNT( IF ( c.client_intention_maxlevel > 2, p.client_id, NULL ) ) as clientnum,
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and {$iwhere} ) as num1,
	(select count(i.audition_id) from crm_client_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and {$awhere}) as num2,
	(select count(i.invite_id) from crm_student_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and {$iwhere} ) as numstu1,
	(select count(i.audition_id) from crm_student_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and {$awhere}) as numstu2,
	
	(select count(i.audition_id) from crm_client_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.audition_genre = '0' and {$awhere}) as num3,
	(select count(i.audition_id) from crm_student_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.audition_genre = '0' and {$awhere}) as numstu3,
	
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_genre = '1' and {$iwhere}) as num4,
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0') and {$iwhere}) as num13,
	
  (select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_genre = '3' and {$iwhere}) as num5,
  
	(SELECT COUNT(vp.student_id) FROM crm_client_invite as i left join smc_student_registerinfo as vp on vp.from_client_id = i.client_id WHERE vp.info_status=1 and {$coursetypewhere} and vp.school_id = p.school_id AND (vp.xz_marketer_id = p.marketer_id OR vp.kz_marketer_id = p.marketer_id) and i.invite_genre = '3' and {$iwhere}) as num6,
	
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_isvisit = '1' and {$iwhere}) as num7, 
	(select count(i.invite_id) from crm_student_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_isvisit = '1' and {$iwhere}) as numstu7,
	
	(select count(i.audition_id) from crm_client_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.audition_isvisit = '1' and {$awhere}) as num8,
	(select count(i.audition_id) from crm_student_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.audition_isvisit = '1' and {$awhere}) as numstu8,
	
	(select count(i.audition_id) from crm_client_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1' and {$awhere}) as num9,
	(select count(i.audition_id) from crm_student_audition as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1' and {$awhere}) as numstu9,
	
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_genre = '1' and i.invite_isvisit = '1' and {$iwhere}) as num10,
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0') and i.invite_isvisit = '1' and {$iwhere}) as num14,
	
	(SELECT COUNT(vp.student_id) FROM view_crm_invitelog as i left join smc_student_registerinfo as vp on vp.from_client_id = i.client_id WHERE vp.info_status=1 and {$coursetypewhere} and vp.school_id = p.school_id AND (vp.xz_marketer_id = p.marketer_id OR vp.kz_marketer_id = p.marketer_id) and vp.pay_successtime >= unix_timestamp(i.visittime) and i.isvisit = '1' and vp.info_status = '1' and vp.info_type = '0' and {$regiwhere}) as num11, 
	
	(SELECT COUNT(vp.student_id) FROM view_crm_stu_invitelog as i left join smc_student_registerinfo as vp on vp.student_id = i.student_id WHERE vp.info_status=1 and {$coursetypewhere} and vp.school_id = p.school_id AND (vp.xz_marketer_id = p.marketer_id OR vp.kz_marketer_id = p.marketer_id) and vp.pay_successtime >= unix_timestamp(i.visittime) and i.isvisit = '1' and vp.info_status = '1' and vp.info_type = '1' and {$regawhere}) as num12 
	
FROM
	crm_client_principal AS p,
	crm_client AS c,
	smc_school AS s,
	crm_marketer AS m,
	smc_staffer AS f 
WHERE
    {$datawhere}
	AND p.marketer_id = m.marketer_id 
	AND p.school_id = s.school_id 
	AND p.client_id = c.client_id 
	AND m.staffer_id = f.staffer_id 
	AND c.client_tracestatus IN ( 0, 1, 2, 3 ) 
	AND p.principal_leave = '0' 
	AND s.school_isclose = '0' 
	AND s.school_istest = '0' 
	AND s.company_id = '{$request['company_id']}' 
	AND p.school_id = '{$request['school_id']}' 
	AND p.principal_ismajor = '1' 
	AND c.client_isgross = '0'
GROUP BY
	p.marketer_id,
	p.school_id 
ORDER BY
	p.school_id

";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");


        if (!$marketList) {
            return array();
        } else {
            $mark_data = array();
            foreach ($marketList as $key => $value) {

                $temp['region_name'] = $value['region_name'];//地区
                $temp['school_branch'] = $value['school_branch'];//校区编号
                $temp['school_cnname'] = $value['school_cnname'];//校区名称
                $temp['district_cnname'] = $value['district_cnname'];//所属区域
                $temp['staffer_cnname'] = $value['staffer_cnname'];//中文名
                if ($value['staffer_enname']) {
                    $temp['staffer_enname'] = $value['staffer_enname'];//英文名
                } else {
                    $temp['staffer_enname'] = '--';//英文名
                }
                if ($value['post_name']) {
                    $temp['post_name'] = $value['post_name'];//本校职务
                } else {
                    $temp['post_name'] = '--';//本校职务
                }
                $temp['clientnum'] = $value['clientnum'];//系统名单总数
                $temp['yallnum'] = $value['num1'] + $value['num2']+$value['numstu1'] + $value['numstu2'];//邀约诺访/月
                $temp['num3'] = $value['num3']+$value['numstu3'];//OH邀约诺访/月
                $temp['num2'] = $value['num2']+$value['numstu2'];//试听邀约诺访/月
                $temp['num4'] = $value['num4'];//插测邀约诺访/月
                $temp['num13'] = $value['num13'];//柜询邀约诺访/月
                $temp['tallnum'] = $value['num7'] + $value['num8'] - $value['num5']+$value['numstu7'] + $value['numstu8'] ;//邀约到访/月
                $temp['num9'] = $value['num9']+$value['numstu9'];//OH邀约到访/月
                $temp['num8'] = $value['num8']+$value['numstu8'];//试听邀约到访/月
                $temp['num10'] = $value['num10'];//插测邀约到访/月
                $temp['num14'] = $value['num14'];//柜询邀约到访/月
                if($temp['yallnum'] > 0){
                    $temp['rate1'] = round($temp['tallnum'] / $temp['yallnum'] * 100, 2) . '%';//邀约到访率/月
                }else{
                    $temp['rate1'] = '0.00%';//邀约到访率/月
                }

                $temp['pallnum'] = $value['num11'] + $value['num12'] ;//邀约到访报名/月
                if($temp['tallnum'] > 0) {
                    $temp['rate2'] = round($temp['pallnum'] / $temp['tallnum'] * 100, 2) . '%';//邀约到访报名率/月
                }else{
                    $temp['rate2'] = '0.00%';//邀约到访报名率/月
                }
                $temp['num5'] = $value['num5'];//主动到访/月
                $temp['num6'] = $value['num6'];//主动到访报名/月
                if($temp['num5'] > 0) {
                    $temp['rate3'] = round($temp['num6'] / $temp['num5'] * 100, 2) . '%';//主动到访报名率/月
                }else{
                    $temp['rate3'] = '0.00%';//主动到访报名率/月
                }

                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['region_name'] = $dateexcelvar['region_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];
                    $datearray['yallnum'] = $dateexcelvar['yallnum'];
                    $datearray['num3'] = $dateexcelvar['num3'];
                    $datearray['num2'] = $dateexcelvar['num2'];
                    $datearray['num4'] = $dateexcelvar['num4'];
                    $datearray['num13'] = $dateexcelvar['num13'];
                    $datearray['tallnum'] = $dateexcelvar['tallnum'];
                    $datearray['num9'] = $dateexcelvar['num9'];
                    $datearray['num8'] = $dateexcelvar['num8'];
                    $datearray['num10'] = $dateexcelvar['num10'];
                    $datearray['num14'] = $dateexcelvar['num14'];
                    $datearray['rate1'] = $dateexcelvar['rate1'];
                    $datearray['pallnum'] = $dateexcelvar['pallnum'];
                    $datearray['rate2'] = $dateexcelvar['rate2'];
                    $datearray['num5'] = $dateexcelvar['num5'];
                    $datearray['num6'] = $dateexcelvar['num6'];
                    $datearray['rate3'] = $dateexcelvar['rate3'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('地区', '校区编号', '校区名称', '所属区域', '中文名', '英文名', '本校职务', '系统名单总数', '邀约诺访', 'OH邀约诺访', '试听邀约诺访', '柜询插测邀约诺访', '柜询邀约诺访', '邀约到访', 'OH邀约到访', '试听邀约到访', '柜询插测邀约到访', '柜询邀约到访', '邀约到访率', '邀约到访报名', '邀约到访报名率', '主动到访', '主动到访报名', '主动到访报名率'));
            $excelfileds = array('region_name', 'school_branch', 'school_cnname', 'district_cnname', 'staffer_cnname', 'staffer_enname', 'post_name', 'clientnum', 'yallnum', 'num3', 'num2', 'num4', 'num13', 'tallnum', 'num9', 'num8', 'num10', 'num14', 'rate1', 'pallnum', 'rate2', 'num5', 'num6', 'rate3');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生邀约状况统计表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $mark_data;
        return $data;
    }

    //招生主动到访统计表
    function getInviteSelf($request)
    {
        $datawhere = " 1 ";

        $beginLastweek = mktime(0, 0, 0, date('m'), date('d') - date('w') + 1 - 7, date('Y'));
        $endLastweek = mktime(23, 59, 59, date('m'), date('d') - date('w') + 7 - 7, date('Y'));
        $beginThisweek = mktime(0, 0, 0, date('m'), date('d') - date('w') + 1, date('Y'));
        $endThisweek = mktime(23, 59, 59, date('m'), date('d') - date('w') + 7, date('Y'));

        $month = strtotime(date('Y-m-d', strtotime('-1 month')));//当前时间戳+1月 2017-02-09 21:04:11

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and s.school_shortname like '%{$request['keyword']}%'";
        }
        //班组
        $coursetypewhere = " 1 ";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $coursetypewhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
	p.marketer_id,
	( SELECT r.region_name FROM smc_code_region AS r WHERE r.region_id = s.school_city ) AS region_name,
	s.school_branch,
	s.school_cnname,
	( SELECT r.district_cnname FROM gmc_company_district AS r WHERE r.district_id = s.district_id ) AS district_cnname,
	f.staffer_cnname,
	f.staffer_enname,
	(
	SELECT
		ps.post_name 
	FROM
		gmc_company_post AS ps,
		gmc_staffer_postbe AS b 
	WHERE
		ps.post_id = b.post_id 
		AND b.school_id = p.school_id 
		AND b.staffer_id = f.staffer_id 
	) AS post_name,
	COUNT( IF ( c.client_intention_maxlevel > 2, p.client_id, NULL ) ) as clientnum,
	(select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_genre = '3' and unix_timestamp(i.invite_visittime) > '{$beginLastweek}' and unix_timestamp(i.invite_visittime) < '{$endLastweek}' and i.invite_isvisit = '1') as num1,
  (SELECT COUNT(vp.student_id) FROM crm_client_invite as i left join smc_student_registerinfo as vp on vp.from_client_id = i.client_id WHERE vp.info_status=1 and {$coursetypewhere} and vp.school_id = p.school_id AND (vp.xz_marketer_id = p.marketer_id OR vp.kz_marketer_id = p.marketer_id) and i.invite_genre = '3' and unix_timestamp(i.invite_visittime) > '{$beginLastweek}' and unix_timestamp(i.invite_visittime) < '{$endLastweek}'  and i.invite_isvisit = '1' and vp.pay_successtime >= unix_timestamp(i.invite_visittime)) as num2,
  (select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_genre = '3' and unix_timestamp(i.invite_visittime) > '{$beginThisweek}' and unix_timestamp(i.invite_visittime) < '{$endThisweek}'  and i.invite_isvisit = '1') as num3,
  (SELECT COUNT(vp.student_id) FROM crm_client_invite as i left join smc_student_registerinfo as vp on vp.from_client_id = i.client_id WHERE vp.info_status=1 and vp.school_id = p.school_id AND (vp.xz_marketer_id = p.marketer_id OR vp.kz_marketer_id = p.marketer_id) and i.invite_genre = '3' and unix_timestamp(i.invite_visittime) > '{$beginThisweek}' and unix_timestamp(i.invite_visittime) < '{$endThisweek}'  and i.invite_isvisit = '1' and vp.pay_successtime >= unix_timestamp(i.invite_visittime)) as num4,
  (select count(i.invite_id) from crm_client_invite as i where {$coursetypewhere} and i.school_id = p.school_id and i.marketer_id = m.marketer_id and i.invite_genre = '3' and unix_timestamp(i.invite_visittime) > '{$month}'  and i.invite_isvisit = '1') as num5,
  (SELECT COUNT(vp.student_id) FROM crm_client_invite as i left join smc_student_registerinfo as vp on vp.from_client_id = i.client_id WHERE vp.info_status=1 and vp.school_id = p.school_id AND (vp.xz_marketer_id = p.marketer_id OR vp.kz_marketer_id = p.marketer_id) and i.invite_genre = '3' and unix_timestamp(i.invite_visittime) > '{$month}'  and i.invite_isvisit = '1' and vp.pay_successtime >= unix_timestamp(i.invite_visittime)) as num6
FROM
	crm_client_principal AS p,
	crm_client AS c,
	smc_school AS s,
	crm_marketer AS m,
	smc_staffer AS f 
WHERE {$datawhere}
	AND p.marketer_id = m.marketer_id 
	AND p.school_id = s.school_id 
	AND p.client_id = c.client_id 
	AND m.staffer_id = f.staffer_id 
	AND c.client_tracestatus IN ( 0, 1, 2, 3 ) 
	AND p.principal_leave = '0' 
	AND s.school_isclose = '0' 
	AND s.school_istest = '0' 
	AND s.company_id = '{$request['company_id']}' 
	AND p.school_id = '{$request['school_id']}' 
	AND p.principal_ismajor = '1' 
	AND c.client_isgross = '0'
GROUP BY
	p.marketer_id,
	p.school_id 
ORDER BY
	p.school_id


";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");


        if (!$marketList) {
            return array();
        } else {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $temp['region_name'] = $value['region_name'];//地区
                $temp['school_branch'] = $value['school_branch'];//校区编号
                $temp['school_cnname'] = $value['school_cnname'];//校区名称
                $temp['district_cnname'] = $value['district_cnname'];//所属区域
                $temp['organization'] = '--';//校务编制
                $temp['staffer_cnname'] = $value['staffer_cnname'];//中文名
                if ($value['staffer_enname']) {
                    $temp['staffer_enname'] = $value['staffer_enname'];//英文名
                } else {
                    $temp['staffer_enname'] = '--';//英文名
                }
                if ($value['post_name']) {
                    $temp['post_name'] = $value['post_name'];//本校职务
                } else {
                    $temp['post_name'] = '--';//本校职务
                }
                $temp['clientnum'] = $value['clientnum'];//系统名单总数
                $temp['num1'] = $value['num1'];//主动到访/上周
                $temp['num2'] = $value['num2'];//主动到访报名/上周
                $temp['rate1'] = round($temp['num2'] / $temp['num1'] * 100, 2) . '%';//主动到访报名率/上周
                $temp['num3'] = $value['num3'];//主动到访/当周
                $temp['num4'] = $value['num4'];//主动到访报名/当周
                $temp['rate2'] = round($temp['num4'] / $temp['num3'] * 100, 2) . '%';//主动到访报名率/当周
                $temp['num5'] = $value['num5'];//主动到访/月
                $temp['num6'] = $value['num6'];//主动到访报名/月
                $temp['rate3'] = round($temp['num6'] / $temp['num5'] * 100, 2) . '%';//主动到访报名率/月
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['region_name'] = $dateexcelvar['region_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];
                    $datearray['num1'] = $dateexcelvar['num1'];
                    $datearray['num2'] = $dateexcelvar['num2'];
                    $datearray['rate1'] = $dateexcelvar['rate1'];
                    $datearray['num3'] = $dateexcelvar['num3'];
                    $datearray['num4'] = $dateexcelvar['num4'];
                    $datearray['rate2'] = $dateexcelvar['rate2'];
                    $datearray['num5'] = $dateexcelvar['num5'];
                    $datearray['num6'] = $dateexcelvar['num6'];
                    $datearray['rate3'] = $dateexcelvar['rate3'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('地区', '校区编号', '校区名称', '所属区域', '中文名', '英文名', '本校职务', '系统名单总数', '主动到访/上周', '主动到访报名/上周', '主动到访报名率/上周', '主动到访/当周', '主动到访报名/当周', '主动到访报名率/当周', '主动到访/月', '主动到访报名/月', '主动到访报名率/月'));
            $excelfileds = array('region_name', 'school_branch', 'school_cnname', 'district_cnname', 'staffer_cnname', 'staffer_enname', 'post_name', 'clientnum', 'num1', 'num2', 'rate1', 'num3', 'num4', 'rate2', 'num5', 'num6', 'rate3');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生主动到访统计表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $mark_data;
        return $data;
    }


    function getCenterTrackWeek($request)
    {

        $datawhere = " s.company_id='{$this->company_id}'";
        $where = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and p.school_id='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $where .= " and t.coursetype_id='{$request['coursetype_id']}'";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $starttime = strtotime("-7 day");
        $endtime = time();

        $sql = "SELECT s.school_branch,s.school_cnname,f.staffer_cnname,ifnull(f.staffer_enname,'--') as staffer_enname,ifnull(s.school_tagbak,'--') as school_tagbak, f.staffer_leave, COUNT(IF ( c.client_intention_maxlevel > 2, p.client_id, NULL )) as listNum
                ,(SELECT r.region_name FROM smc_code_region AS r WHERE r.region_id = s.school_city) as region_name
                ,ifnull((SELECT r.district_cnname FROM gmc_company_district AS r WHERE r.district_id = s.district_id),'--') as district_cnname
                ,ifnull((SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.school_id = p.school_id AND b.staffer_id = f.staffer_id),'--') as post_name
                 ,(SELECT COUNT(t.client_id) as num FROM crm_client_track AS t WHERE {$where} and t.marketer_id = p.marketer_id 
AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime > '{$starttime}') as clientTime
                ,(SELECT COUNT(t.student_id) as num FROM crm_student_track AS t WHERE {$where} and t.marketer_id = p.marketer_id AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime > '{$starttime}') as studentTime
                ,(SELECT COUNT(DISTINCT t.client_id) as num FROM crm_client_track AS t WHERE {$where} and t.marketer_id = p.marketer_id 
AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime > '{$starttime}') as clientNum
                ,(SELECT COUNT(DISTINCT t.student_id) as num FROM crm_student_track AS t WHERE {$where} and t.marketer_id = p.marketer_id AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime > '{$starttime}') as studentNum
                FROM
                 crm_client_principal AS p,crm_client AS c,smc_school AS s,crm_marketer AS m,smc_staffer AS f
                WHERE
                 {$datawhere} and p.marketer_id = m.marketer_id AND p.school_id = s.school_id AND p.client_id = c.client_id AND m.staffer_id = f.staffer_id AND c.client_tracestatus IN (0, 1, 2, 3) AND p.principal_leave = '0' AND s.school_isclose = '0' AND s.school_istest = '0' and c.client_isgross=0
                AND p.principal_ismajor = '1'
                GROUP BY
                 p.marketer_id,
                 p.school_id
                ORDER BY p.school_id";


        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['region_name'] = $dateexcelvar['region_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_tagbak'] = $dateexcelvar['school_tagbak'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['listNum'] = $dateexcelvar['listNum'];
                    $datearray['StuNum'] = $dateexcelvar['clientNum'] + $dateexcelvar['studentNum'];
                    $datearray['StuTime'] = $dateexcelvar['clientTime'] + $dateexcelvar['studentTime'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("地区", "校区编号", "校区名称", "所属区域", "中文名", "英文名", "本校职务", "系统名单总数", "追踪人数/周", "追踪人次/周"));
            $excelfileds = array('region_name', 'school_branch', 'school_cnname', 'school_tagbak', 'staffer_cnname', 'staffer_enname', 'post_name', 'listNum', 'StuNum', 'StuTime');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('招生追踪周度统计表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $clientList = $this->DataControl->selectClear($sql);
            if (!$clientList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($clientList as &$clientOne) {
                $clientOne['staffer_enname'] = $clientOne['staffer_enname'] ? $clientOne['staffer_enname'] : '--';
                $clientOne['StuNum'] = $clientOne['clientNum'] + $clientOne['studentNum'];
                $clientOne['StuTime'] = $clientOne['clientTime'] + $clientOne['studentTime'];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT p.marketer_id
                FROM
                 crm_client_principal AS p,crm_client AS c,smc_school AS s,crm_marketer AS m,smc_staffer AS f
                WHERE
                 {$datawhere} and p.marketer_id = m.marketer_id AND p.school_id = s.school_id AND p.client_id = c.client_id AND m.staffer_id = f.staffer_id AND c.client_tracestatus IN (0, 1, 2, 3) AND p.principal_leave = '0' AND s.school_isclose = '0' AND s.school_istest = '0'
                AND p.principal_ismajor = '1' and c.client_isgross=0
                GROUP BY
                 p.marketer_id,
                 p.school_id
                ORDER BY p.school_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $clientList;
            return $data;
        }
    }

    function getCenterTrackMonth($request)
    {

        $datawhere = " s.company_id='{$this->company_id}'";

        $where = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and p.school_id='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $where .= " and t.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


//        $starttime = strtotime("-30 day");
        $starttime = strtotime(date("Y-m-d",strtotime("-29 day")));
        $endtime = time();

        $sql = "SELECT s.school_branch,s.school_cnname,f.staffer_cnname,ifnull(f.staffer_enname,'--') as staffer_enname,ifnull(s.school_tagbak,'--') as school_tagbak, f.staffer_leave, COUNT(IF ( c.client_intention_maxlevel > 2, p.client_id, NULL )) as listNum
                ,(SELECT r.region_name FROM smc_code_region AS r WHERE r.region_id = s.school_city) as region_name
                ,ifnull((SELECT r.district_cnname FROM gmc_company_district AS r WHERE r.district_id = s.district_id),'--') as district_cnname
                ,ifnull((SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.school_id = p.school_id AND b.staffer_id = f.staffer_id),'--') as post_name
                 ,(SELECT COUNT(t.client_id) as num FROM crm_client_track AS t WHERE {$where} and t.marketer_id = p.marketer_id 
                AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime >= '{$starttime}') as clientTime
                ,(SELECT COUNT(t.student_id) as num FROM crm_student_track AS t WHERE {$where} and t.marketer_id = p.marketer_id 
                AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime >= '{$starttime}') as studentTime
                
                ,(SELECT COUNT(DISTINCT t.client_id) as num FROM crm_client_track AS t WHERE {$where} and t.marketer_id = p.marketer_id 
                AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime >= '{$starttime}') as clientNum
                ,(SELECT COUNT(DISTINCT t.student_id) as num FROM crm_student_track AS t WHERE {$where} and t.marketer_id = p.marketer_id 
                AND t.school_id = p.school_id AND t.track_isactive = '1' AND t.track_createtime >= '{$starttime}') as studentNum
                
                FROM
                 crm_client_principal AS p,crm_client AS c,smc_school AS s,crm_marketer AS m,smc_staffer AS f
                WHERE
                 {$datawhere} and p.marketer_id = m.marketer_id AND p.school_id = s.school_id AND p.client_id = c.client_id AND m.staffer_id = f.staffer_id AND c.client_tracestatus IN (0, 1, 2, 3) AND p.principal_leave = '0' AND s.school_isclose = '0' AND s.school_istest = '0'
                AND p.principal_ismajor = '1' and c.client_isgross=0
                GROUP BY
                 p.marketer_id,
                 p.school_id
                ORDER BY p.school_id";


        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['region_name'] = $dateexcelvar['region_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_tagbak'] = $dateexcelvar['school_tagbak'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['listNum'] = $dateexcelvar['listNum'];
                    $datearray['StuNum'] = $dateexcelvar['clientNum'] + $dateexcelvar['studentNum'];
                    $datearray['StuTime'] = $dateexcelvar['clientTime'] + $dateexcelvar['studentTime'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("地区", "校区编号", "校区名称", "所属区域", "中文名", "英文名", "本校职务", "系统名单总数", "追踪人数/月", "追踪人次/月"));
            $excelfileds = array('region_name', 'school_branch', 'school_cnname', 'school_tagbak', 'staffer_cnname', 'staffer_enname', 'post_name', 'listNum', 'StuNum', 'StuTime');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('招生追踪月度统计表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $clientList = $this->DataControl->selectClear($sql);
            if (!$clientList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($clientList as &$clientOne) {
                $clientOne['staffer_enname'] = $clientOne['staffer_enname'] ? $clientOne['staffer_enname'] : '--';
                $clientOne['StuNum'] = $clientOne['clientNum'] + $clientOne['studentNum'];
                $clientOne['StuTime'] = $clientOne['clientTime'] + $clientOne['studentTime'];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT p.marketer_id
                FROM
                 crm_client_principal AS p,crm_client AS c,smc_school AS s,crm_marketer AS m,smc_staffer AS f
                WHERE
                 {$datawhere} and p.marketer_id = m.marketer_id AND p.school_id = s.school_id AND p.client_id = c.client_id AND m.staffer_id = f.staffer_id AND c.client_tracestatus IN (0, 1, 2, 3) AND p.principal_leave = '0' AND s.school_isclose = '0' AND s.school_istest = '0'
                AND p.principal_ismajor = '1' and c.client_isgross=0
                GROUP BY
                 p.marketer_id,
                 p.school_id
                ORDER BY p.school_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $clientList;
            return $data;
        }
    }

    function getRegisterInfo($request)
    {
        $datawhere = " and a.company_id='{$this->company_id}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (f.student_branch like '%{$request['keyword']}%' 
            or f.student_cnname like '%{$request['keyword']}%'
            or d.marketer_name like '%{$request['keyword']}%'
            or e.marketer_name like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " and b.school_istest='0' and b.school_isclose='0'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['info_type']) && $request['info_type'] !== '') {
            $datawhere .= " and a.info_type='{$request['info_type']}'";
        }

        $starttime = strtotime(date("Y-1-1"));
        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = strtotime($request['starttime']);
        }

        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = strtotime($request['endtime']) + 60 * 60 * 24 - 1;
        }

        $datawhere .= " and a.pay_successtime>='{$starttime}'";
        $datawhere .= " and a.pay_successtime<='{$endtime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.info_id,ifnull(b.school_tagbak,'--') as school_tagbak
        ,(SELECT r.region_name FROM smc_code_region AS r WHERE r.region_id = b.school_city) as region_name
        ,b.school_id,b.school_branch,b.school_cnname
        ,a.student_id,f.student_branch,f.student_cnname
        ,a.coursetype_id,a.coursetype_branch,a.coursetype_cnname
        ,a.info_type,a.pay_price as regi_price
        ,FROM_UNIXTIME(a.pay_successtime) as regi_time
        ,(case when a.info_type=0 then ifnull(d.marketer_name,'--') else ifnull(e.marketer_name,'--') end) as main_marketer
        from smc_student_registerinfo a
        left join smc_school b on a.school_id=b.school_id
        left join crm_marketer d on a.xz_marketer_id=d.marketer_id
        left join crm_marketer e on a.kz_marketer_id=e.marketer_id
        left join smc_student f on a.student_id=f.student_id
        where a.info_status=1
        {$datawhere}
        order by a.pay_successtime
        ";

        $type = $this->LgArraySwitch(array('0' => '新招报名', '1' => '扩科报名'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['region_name'] = $dateexcelvar['region_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_tagbak'] = $dateexcelvar['school_tagbak'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['info_type'] = $type[$dateexcelvar['info_type']];
                    $datearray['regi_time'] = $dateexcelvar['regi_time'];
                    $datearray['regi_price'] = $dateexcelvar['regi_price'];
                    $datearray['main_marketer'] = $dateexcelvar['main_marketer'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("地区", "校区编号", "校区名称", "所属区域", "学员编号", "中文名", "报名班组", "报名类型", "报名时间", "报名金额", "主负责人"));
            $excelfileds = array('region_name', 'school_branch', 'school_cnname', 'school_tagbak', 'student_branch', 'student_cnname', 'coursetype_cnname', 'info_type', 'regi_time', 'regi_price', 'main_marketer');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('招生对照明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $clientList = $this->DataControl->selectClear($sql);
            if (!$clientList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($clientList as &$clientOne) {
                $clientOne['info_type'] = $type[$clientOne['info_type']];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.info_id 
                from smc_student_registerinfo a
                left join smc_school b on a.school_id=b.school_id
                left join crm_marketer d on a.xz_marketer_id=d.marketer_id
                left join crm_marketer e on a.kz_marketer_id=e.marketer_id
                left join smc_student f on a.student_id=f.student_id
                where a.info_status=1
                {$datawhere}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $clientList;
            return $data;
        }
    }

    //教师招生分析表
    function getTeachersAdmissions($request)
    {
        // AND c.client_intention_level > 2
        $schoolwhere = "s.company_id = '{$request['company_id']}' 
        and p.school_id = '{$request['school_id']}' 
        and p.principal_ismajor = 1 
        and p.principal_leave = 0 
        and p.marketer_id > 0 
        and s.school_isclose = 0 
        and s.school_istest = 0 
        and c.client_tracestatus in (0,1,2,3) 
        and c.client_isgross = 0";
        $yearwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $upmonthwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $nowmonthwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $monthwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $upweekwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $nowweekwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $weekwhere = "vp.school_id = p.school_id and ((vp.xz_marketer_id=p.marketer_id and vp.info_type=0) or (vp.kz_marketer_id=p.marketer_id and vp.info_type=1))";
        $trackmonthwhere = "t.marketer_id = p.marketer_id and t.school_id = p.school_id and t.track_isactive = '1'";
        $trackweekwhere = "t.marketer_id = p.marketer_id and t.school_id = p.school_id and t.track_isactive = '1'";
        $inviteupmonthwhere = "i.school_id = p.school_id";
        $auditionupmonthwhere = "i.school_id = p.school_id";
        $invitenowmonthwhere = "i.school_id = p.school_id";
        $auditionnowmonthwhere = "i.school_id = p.school_id";
        $inviteupweekwhere = "i.school_id = p.school_id";
        $auditionupweekwhere = "i.school_id = p.school_id";
        $invitenowweekwhere = "i.school_id = p.school_id";
        $auditionnowweekwhere = "i.school_id = p.school_id";
        $invitemonthwhere = "i.school_id = p.school_id and i.invite_genre = '3' and i.invite_isvisit = '1'";
        $inviteweekwhere = "i.school_id = p.school_id and i.invite_genre = '3' and i.invite_isvisit = '1'";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $schoolwhere .= " and (s.school_shortname like '%{$request['keyword']}%' or d.district_cnname like '%{$request['keyword']}%') ";
        }
        //班组
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $yearwhere .= " and vp.coursetype_id = '{$request['coursetype_id']}'";
            $upmonthwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $nowmonthwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $monthwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $upweekwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $nowweekwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $weekwhere .= "  and vp.coursetype_id = '{$request['coursetype_id']}'";
            $trackmonthwhere .= "  and t.coursetype_id = '{$request['coursetype_id']}'";
            $trackweekwhere .= "  and t.coursetype_id = '{$request['coursetype_id']}'";
            $inviteupmonthwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $auditionupmonthwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $invitenowmonthwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $auditionnowmonthwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $inviteupweekwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $auditionupweekwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $invitenowweekwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $auditionnowweekwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $invitemonthwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
            $inviteweekwhere .= "  and i.coursetype_id = '{$request['coursetype_id']}'";
        }

        $starttime = strtotime(date("Y-1-1"));
        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        $yearwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $upmonth = GetTheMonth(GetMonth(date("Y-m-d")));
        $starttime = strtotime($upmonth[0]);
        $endtime = strtotime($upmonth[1]) + 60 * 60 * 24 - 1;
        $upmonthwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $nowmonth = GetTheMonth(date("Y-m-d"));
        $starttime = strtotime($nowmonth[0]);
        $endtime = strtotime($nowmonth[1]) + 60 * 60 * 24 - 1;
        $nowmonthwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime("-1 month");
        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        $monthwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $week = GetWeekAll(date("Y-m-d"));
        $starttime = strtotime($week['lastweek_start']);
        $endtime = strtotime($week['lastweek_end']) + 60 * 60 * 24 - 1;
        $upweekwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime($week['nowweek_start']);
        $endtime = strtotime($week['nowweek_end']) + 60 * 60 * 24 - 1;
        $nowweekwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime("-7 day");
        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
        $weekwhere .= " and vp.pay_successtime >= '{$starttime}' and vp.pay_successtime <= '{$endtime}'";

        $starttime = strtotime("-1 month");
        $trackmonthwhere .= " and t.track_createtime > '{$starttime}'";

        $starttime = strtotime("-7 day");
        $trackweekwhere .= " and t.track_createtime > '{$starttime}'";

        $upmonth = GetTheMonth(GetMonth(date("Y-m-d")));
        $starttime = date("Y-m-d H:i:s",strtotime($upmonth[0]));
        $endtime = date("Y-m-d H:i:s",strtotime($upmonth[1]) + 60 * 60 * 24 - 1);
        $inviteupmonthwhere .= " and i.invite_visittime >= '{$starttime}' and i.invite_visittime<= '{$endtime}'";
        $auditionupmonthwhere .= " and i.audition_visittime >= '{$starttime}' and i.audition_visittime<= '{$endtime}'";

        $nowmonth = GetTheMonth(date("Y-m-d"));
        $starttime = date("Y-m-d H:i:s",strtotime($nowmonth[0]));
        $endtime = date("Y-m-d H:i:s",strtotime($nowmonth[1]) + 60 * 60 * 24 - 1);
        $invitenowmonthwhere .= " and i.invite_visittime >= '{$starttime}' and i.invite_visittime<= '{$endtime}'";
        $auditionnowmonthwhere .= " and i.audition_visittime >= '{$starttime}' and i.audition_visittime<= '{$endtime}'";

        $week = GetWeekAll(date("Y-m-d"));
        $starttime = date("Y-m-d H:i:s",strtotime($week['lastweek_start']));
        $endtime = date("Y-m-d H:i:s",strtotime($week['lastweek_end']) + 60 * 60 * 24 - 1);
        $inviteupweekwhere .= " and i.invite_visittime >= '{$starttime}' and i.invite_visittime<= '{$endtime}'";
        $auditionupweekwhere .= " and i.audition_visittime >= '{$starttime}' and i.audition_visittime<= '{$endtime}'";

        $starttime = date("Y-m-d H:i:s",strtotime($week['nowweek_start']));
        $endtime = date("Y-m-d H:i:s",strtotime($week['nowweek_end']) + 60 * 60 * 24 - 1);
        $invitenowweekwhere .= " and i.invite_visittime >= '{$starttime}' and i.invite_visittime<= '{$endtime}'";
        $auditionnowweekwhere .= " and i.audition_visittime >= '{$starttime}' and i.audition_visittime<= '{$endtime}'";

        $starttime = date("Y-m-d H:i:s",strtotime("-1 month"));
        $endtime = date("Y-m-d H:i:s",strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1);
        $invitemonthwhere .= " and i.invite_visittime >= '{$starttime}' and i.invite_visittime<= '{$endtime}'";

        $starttime = date("Y-m-d H:i:s",strtotime("-7 day"));
        $endtime = date("Y-m-d H:i:s",strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1);
        $inviteweekwhere .= " and i.invite_visittime >= '{$starttime}' and i.invite_visittime<= '{$endtime}'";

//        $upmonth = GetTheMonth(GetMonth(date("Y-m-d")));
//        $starttime = strtotime($upmonth[0]);
//        $endtime = strtotime($upmonth[1]) + 60 * 60 * 24 - 1;
//        $inviteupmonthwhere .= " and unix_timestamp(i.invite_visittime) >= '{$starttime}' and unix_timestamp(i.invite_visittime)<= '{$endtime}'";
//        $auditionupmonthwhere .= " and unix_timestamp(i.audition_visittime) >= '{$starttime}' and unix_timestamp(i.audition_visittime)<= '{$endtime}'";
//
//        $nowmonth = GetTheMonth(date("Y-m-d"));
//        $starttime = strtotime($nowmonth[0]);
//        $endtime = strtotime($nowmonth[1]) + 60 * 60 * 24 - 1;
//        $invitenowmonthwhere .= " and unix_timestamp(i.invite_visittime) >= '{$starttime}' and unix_timestamp(i.invite_visittime)<= '{$endtime}'";
//        $auditionnowmonthwhere .= " and unix_timestamp(i.audition_visittime) >= '{$starttime}' and unix_timestamp(i.audition_visittime)<= '{$endtime}'";
//
//        $week = GetWeekAll(date("Y-m-d"));
//        $starttime = strtotime($week['lastweek_start']);
//        $endtime = strtotime($week['lastweek_end']) + 60 * 60 * 24 - 1;
//        $inviteupweekwhere .= " and unix_timestamp(i.invite_visittime) >= '{$starttime}' and unix_timestamp(i.invite_visittime)<= '{$endtime}'";
//        $auditionupweekwhere .= " and unix_timestamp(i.audition_visittime) >= '{$starttime}' and unix_timestamp(i.audition_visittime)<= '{$endtime}'";
//
//        $starttime = strtotime($week['nowweek_start']);
//        $endtime = strtotime($week['nowweek_end']) + 60 * 60 * 24 - 1;
//        $invitenowweekwhere .= " and unix_timestamp(i.invite_visittime) >= '{$starttime}' and unix_timestamp(i.invite_visittime)<= '{$endtime}'";
//        $auditionnowweekwhere .= " and unix_timestamp(i.audition_visittime) >= '{$starttime}' and unix_timestamp(i.audition_visittime)<= '{$endtime}'";
//
//        $starttime = strtotime("-1 month");
//        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
//        $invitemonthwhere .= " and unix_timestamp(i.invite_visittime) >= '{$starttime}' and unix_timestamp(i.invite_visittime)<= '{$endtime}'";
//
//        $starttime = strtotime("-7 day");
//        $endtime = strtotime(date("Y-m-d")) + 60 * 60 * 24 - 1;
//        $inviteweekwhere .= " and unix_timestamp(i.invite_visittime) >= '{$starttime}' and unix_timestamp(i.invite_visittime)<= '{$endtime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.school_id,s.school_shortname,s.school_enname,s.school_branch,sf.staffer_cnname,sf.staffer_enname,count(IF ( c.client_intention_maxlevel > 2, p.client_id, NULL )) as clientnum,
                (SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.school_id = p.school_id AND b.staffer_id = sf.staffer_id) as post_name,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$yearwhere}) as princ_clientnum,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$upmonthwhere}) as princ_upmonth_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$nowmonthwhere}) as princ_nowmonth_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$monthwhere}) as princ_month_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$upweekwhere}) as princ_upweek_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$nowweekwhere}) as princ_nowweek_num,
                (SELECT count(vp.student_id) FROM smc_student_registerinfo as vp WHERE info_status=1 and {$weekwhere}) as princ_week_num,
                (SELECT count(DISTINCT t.client_id) FROM crm_client_track as t WHERE {$trackmonthwhere}) as clientMonthNum,
                (SELECT count(DISTINCT t.student_id) FROM crm_student_track as t WHERE {$trackmonthwhere}) as studentMonthNum,
                (SELECT count(t.client_id) FROM crm_client_track as t WHERE {$trackmonthwhere}) as clientMonthTime,
                (SELECT count(t.student_id) FROM crm_student_track as t WHERE {$trackmonthwhere}) as studentMonthTime,
                (SELECT count(DISTINCT t.client_id) FROM crm_client_track as t WHERE {$trackweekwhere}) as clientWeekNum,
                (SELECT count(DISTINCT t.student_id) FROM crm_student_track as t WHERE {$trackweekwhere}) as studentWeekNum,
                (SELECT count(t.client_id) FROM crm_client_track as t WHERE {$trackweekwhere}) as clientWeekTime,
                (SELECT count(t.student_id) FROM crm_student_track as t WHERE {$trackweekwhere}) as studentWeekTime,
                
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id) as invite_upmonth_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupmonthwhere} and i.marketer_id = m.marketer_id) as audition_upmonth_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupmonthwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0') as ohaudition_upmonth_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1') as invitetwo_upmonth_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0')) as invitesan_upmonth_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '3') as invitefour_upmonth_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id and i.invite_isvisit = '1') as invitefive_upmonth_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupmonthwhere} and i.marketer_id = m.marketer_id and i.audition_isvisit = '1') as auditiontwo_upmonth_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupmonthwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1') as oharrive_upmonth_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1' and i.invite_isvisit = '1') as invitesix_upmonth_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupmonthwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0') and i.invite_isvisit = '1') as inviteseven_upmonth_num,
                
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id) as invite_nowmonth_num,
                (SELECT count(i.invite_id) FROM crm_student_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id) as invite_nowmonth_num_two,
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id) as audition_nowmonth_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id) as audition_nowmonth_num_two,
	            
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0') as ohaudition_nowmonth_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0') as ohaudition_nowmonth_num_two,
	            
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1') as invitetwo_nowmonth_num,
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0')) as invitesan_nowmonth_num,
                
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '3') as invitefour_nowmonth_num, 
                
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and i.invite_isvisit = '1') as invitefive_nowmonth_num,
                (SELECT count(i.invite_id) FROM crm_student_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and i.invite_isvisit = '1') as invitefive_nowmonth_num_two,
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id and i.audition_isvisit = '1') as auditiontwo_nowmonth_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id and i.audition_isvisit = '1') as auditiontwo_nowmonth_num_two,

	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1') as oharrive_nowmonth_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowmonthwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1') as oharrive_nowmonth_num_two,
	            
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1' and i.invite_isvisit = '1') as invitesix_nowmonth_num,
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowmonthwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0') and i.invite_isvisit = '1') as inviteseven_nowmonth_num,
                
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id) as invite_upweek_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupweekwhere} and i.marketer_id = m.marketer_id) as audition_upweek_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupweekwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0') as ohaudition_upweek_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1') as invitetwo_upweek_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0')) as invitesan_upweek_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '3') as invitefour_upweek_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id and i.invite_isvisit = '1') as invitefive_upweek_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupweekwhere} and i.marketer_id = m.marketer_id and i.audition_isvisit = '1') as auditiontwo_upweek_num,
	            -- (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionupweekwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1') as oharrive_upweek_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1' and i.invite_isvisit = '1') as invitesix_upweek_num,
                -- (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteupweekwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0') and i.invite_isvisit = '1') as inviteseven_upweek_num,
                
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id) as invite_nowweek_num,
                (SELECT count(i.invite_id) FROM crm_student_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id) as invite_nowweek_num_two,
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id) as audition_nowweek_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id) as audition_nowweek_num_two,
	            
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0') as ohaudition_nowweek_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0') as ohaudition_nowweek_num_two,
	            
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1') as invitetwo_nowweek_num,
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0')) as invitesan_nowweek_num,
 
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '3') as invitefour_nowweek_num, 
                
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and i.invite_isvisit = '1') as invitefive_nowweek_num,
                (SELECT count(i.invite_id) FROM crm_student_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and i.invite_isvisit = '1') as invitefive_nowweek_num_two,
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id and i.audition_isvisit = '1') as auditiontwo_nowweek_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id and i.audition_isvisit = '1') as auditiontwo_nowweek_num_two,
	            
	            (SELECT count(i.audition_id) FROM crm_client_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1') as oharrive_nowweek_num,
	            (SELECT count(i.audition_id) FROM crm_student_audition as i WHERE {$auditionnowweekwhere} and i.marketer_id = m.marketer_id and i.audition_genre = '0' and i.audition_isvisit = '1') as oharrive_nowweek_num_two,
	            
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and i.invite_genre = '1' and i.invite_isvisit = '1') as invitesix_nowweek_num,
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitenowweekwhere} and i.marketer_id = m.marketer_id and (i.invite_genre = '1' or i.invite_genre = '0') and i.invite_isvisit = '1') as inviteseven_nowweek_num,
                
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$invitemonthwhere} and i.marketer_id = m.marketer_id) as invite_month_num,
                -- (SELECT count(vp.student_id) FROM crm_client_invite as i LEFT JOIN smc_student_registerinfo as vp ON vp.from_client_id = i.client_id WHERE info_status=1 and {$invitemonthwhere} and (vp.xz_marketer_id = p.marketer_id or vp.kz_marketer_id = p.marketer_id) and vp.pay_successtime >= unix_timestamp(i.invite_visittime)) as enroll_month_num,
                (SELECT count(i.invite_id) FROM crm_client_invite as i WHERE {$inviteweekwhere} and i.marketer_id = m.marketer_id) as invite_week_num
                -- ,(SELECT count(vp.student_id) FROM crm_client_invite as i LEFT JOIN smc_student_registerinfo as vp ON vp.from_client_id = i.client_id WHERE info_status=1 and {$inviteweekwhere} and (vp.xz_marketer_id = p.marketer_id or vp.kz_marketer_id = p.marketer_id) and vp.pay_successtime >= unix_timestamp(i.invite_visittime)) as enroll_week_num
                from crm_client_principal as p
                left join crm_client as c ON c.client_id = p.client_id
                left join smc_school as s ON s.school_id = p.school_id
                left join crm_marketer as m ON m.marketer_id = p.marketer_id
                left join smc_staffer as sf ON sf.staffer_id = m.staffer_id
                where {$schoolwhere}
                group by p.marketer_id,p.school_id
                having clientnum > 0
                order by p.school_id";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");

        if ($marketList) {
            $mark_data = array();
            foreach ($marketList as $key => $value) {
                $temp['school_shortname'] = $value['school_shortname'];//校区名称
                $temp['staffer_cnname'] = $value['staffer_cnname'];//教师中文名
                $temp['staffer_enname'] = $value['staffer_enname'] ? $value['staffer_enname'] : '--';//教师英文名
                $temp['post_name'] = $value['post_name'] ? $value['post_name'] : '--';//本校职务
                $temp['clientnum'] = $value['clientnum'];//系统名单总数
                $temp['princ_clientnum'] = $value['princ_clientnum'];//报名/当年
                $temp['princ_upmonth_num'] = $value['princ_upmonth_num'];//报名/上月
                $temp['princ_nowmonth_num'] = $value['princ_nowmonth_num'];//报名/当月
                $temp['princ_month_num'] = $value['princ_month_num'];//报名/月
                $temp['princ_upweek_num'] = $value['princ_upweek_num'];//报名/上周
                $temp['princ_nowweek_num'] = $value['princ_nowweek_num'];//报名/当周
                $temp['princ_week_num'] = $value['princ_week_num'];//报名/周
                $temp['track_month_num'] = $value['clientMonthNum'] + $value['studentMonthNum'];;//追踪人数/月
                $temp['track_month_time'] = $value['clientMonthTime'] + $value['studentMonthTime'];//追踪人次/月
                $temp['track_week_num'] = $value['clientWeekNum'] + $value['studentWeekNum'];;//追踪人数/月
                $temp['track_week_time'] = $value['clientWeekTime'] + $value['studentWeekTime'];//追踪人次/月
                $temp['invite_nowmonth_num'] = $value['invite_nowmonth_num'] + $value['audition_nowmonth_num']+$value['invite_nowmonth_num_two'] + $value['audition_nowmonth_num_two'];//邀约诺访/当月
                $temp['ohaudition_nowmonth_num'] = $value['ohaudition_nowmonth_num']+$value['ohaudition_nowmonth_num_two'];//OH邀约诺访/当月
                $temp['audition_nowmonth_num'] = $value['audition_nowmonth_num']+$value['audition_nowmonth_num_two'];//试听邀约诺访/当月
                $temp['invitetwo_nowmonth_num'] = $value['invitetwo_nowmonth_num'];//柜询插测邀约诺访/当月
                $temp['invitesan_nowmonth_num'] = $value['invitesan_nowmonth_num'];//柜询邀约诺访/当月
                $temp['arrive_nowmonth_num'] = $value['invitefive_nowmonth_num'] + $value['auditiontwo_nowmonth_num'] - $value['invitefour_nowmonth_num']+$value['invitefive_nowmonth_num_two']+$value['auditiontwo_nowmonth_num_two'];//邀约到访/当月
                $temp['oharrive_nowmonth_num'] = $value['oharrive_nowmonth_num']+$value['oharrive_nowmonth_num_two'];//OH邀约到访/当月
                $temp['auditiontwo_nowmonth_num'] = $value['auditiontwo_nowmonth_num']+$value['auditiontwo_nowmonth_num_two'];//试听邀约到访/当月
                $temp['invitesix_nowmonth_num'] = $value['invitesix_nowmonth_num'];//柜询插测邀约到访/当月
                $temp['inviteseven_nowmonth_num'] = $value['inviteseven_nowmonth_num'];//柜询邀约到访/当月
                $temp['arrive_nowmonth_rate'] = $temp['invite_nowmonth_num'] > 0 ? round($temp['arrive_nowmonth_num'] / $temp['invite_nowmonth_num'] * 100, 2) . '%' : '0%';//邀约到访率/当月
                $temp['invite_nowweek_num'] = $value['invite_nowweek_num'] + $value['audition_nowweek_num']+$value['invite_nowweek_num_two'] + $value['audition_nowweek_num_two'];//邀约诺访/当周
                $temp['ohaudition_nowweek_num'] = $value['ohaudition_nowweek_num']+$value['ohaudition_nowweek_num_two'];//OH邀约诺访/当周
                $temp['audition_nowweek_num'] = $value['audition_nowweek_num']+$value['audition_nowweek_num_two'];//试听邀约诺访/当周
                $temp['invitetwo_nowweek_num'] = $value['invitetwo_nowweek_num'];//柜询插测邀约诺访/当周
                $temp['invitesan_nowweek_num'] = $value['invitesan_nowweek_num'];//柜询邀约诺访/当周
                $temp['arrive_nowweek_num'] = $value['invitefive_nowweek_num'] + $value['auditiontwo_nowweek_num'] - $value['invitefour_nowweek_num']+ $value['invitefive_nowweek_num_two'] + $value['auditiontwo_nowweek_num_two'];//邀约到访/当周
                $temp['oharrive_nowweek_num'] = $value['oharrive_nowweek_num']+$value['oharrive_nowweek_num_two'];//OH邀约到访/当周
                $temp['auditiontwo_nowweek_num'] = $value['auditiontwo_nowweek_num']+$value['auditiontwo_nowweek_num_two'];//试听邀约到访/当周
                $temp['invitesix_nowweek_num'] = $value['invitesix_nowweek_num'];//柜询插测邀约到访/当周
                $temp['inviteseven_nowweek_num'] = $value['inviteseven_nowweek_num'];//柜询邀约到访/当周
                $temp['arrive_nowweek_rate'] = $temp['invite_nowweek_num'] > 0 ? round($temp['arrive_nowweek_num'] / $temp['invite_nowweek_num'] * 100, 2) . '%' : '0%';//邀约到访率/当周
                $temp['invite_month_num'] = $value['invite_month_num'];//主动到访/月
                $temp['invite_week_num'] = $value['invite_week_num'];//主动到访/周
                $mark_data[] = $temp;
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $mark_data;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];
                    $datearray['princ_clientnum'] = $dateexcelvar['princ_clientnum'];
                    $datearray['princ_upmonth_num'] = $dateexcelvar['princ_upmonth_num'];
                    $datearray['princ_nowmonth_num'] = $dateexcelvar['princ_nowmonth_num'];
                    $datearray['princ_month_num'] = $dateexcelvar['princ_month_num'];
                    $datearray['princ_upweek_num'] = $dateexcelvar['princ_upweek_num'];
                    $datearray['princ_nowweek_num'] = $dateexcelvar['princ_nowweek_num'];
                    $datearray['princ_week_num'] = $dateexcelvar['princ_week_num'];
                    $datearray['track_month_num'] = $dateexcelvar['track_month_num'];
                    $datearray['track_month_time'] = $dateexcelvar['track_month_time'];
                    $datearray['track_week_num'] = $dateexcelvar['track_week_num'];
                    $datearray['track_week_time'] = $dateexcelvar['track_week_time'];
                    $datearray['invite_nowmonth_num'] = $dateexcelvar['invite_nowmonth_num'];
                    $datearray['ohaudition_nowmonth_num'] = $dateexcelvar['ohaudition_nowmonth_num'];
                    $datearray['audition_nowmonth_num'] = $dateexcelvar['audition_nowmonth_num'];
                    $datearray['invitetwo_nowmonth_num'] = $dateexcelvar['invitetwo_nowmonth_num'];
                    $datearray['invitesan_nowmonth_num'] = $dateexcelvar['invitesan_nowmonth_num'];
                    $datearray['arrive_nowmonth_num'] = $dateexcelvar['arrive_nowmonth_num'];
                    $datearray['oharrive_nowmonth_num'] = $dateexcelvar['oharrive_nowmonth_num'];
                    $datearray['auditiontwo_nowmonth_num'] = $dateexcelvar['auditiontwo_nowmonth_num'];
                    $datearray['invitesix_nowmonth_num'] = $dateexcelvar['invitesix_nowmonth_num'];
                    $datearray['inviteseven_nowmonth_num'] = $dateexcelvar['inviteseven_nowmonth_num'];
                    $datearray['arrive_nowmonth_rate'] = $dateexcelvar['arrive_nowmonth_rate'];
                    $datearray['invite_nowweek_num'] = $dateexcelvar['invite_nowweek_num'];
                    $datearray['ohaudition_nowweek_num'] = $dateexcelvar['ohaudition_nowweek_num'];
                    $datearray['audition_nowweek_num'] = $dateexcelvar['audition_nowweek_num'];
                    $datearray['invitetwo_nowweek_num'] = $dateexcelvar['invitetwo_nowweek_num'];
                    $datearray['invitesan_nowweek_num'] = $dateexcelvar['invitesan_nowweek_num'];
                    $datearray['arrive_nowweek_num'] = $dateexcelvar['arrive_nowweek_num'];
                    $datearray['oharrive_nowweek_num'] = $dateexcelvar['oharrive_nowweek_num'];
                    $datearray['auditiontwo_nowweek_num'] = $dateexcelvar['auditiontwo_nowweek_num'];
                    $datearray['invitesix_nowweek_num'] = $dateexcelvar['invitesix_nowweek_num'];
                    $datearray['inviteseven_nowweek_num'] = $dateexcelvar['inviteseven_nowweek_num'];
                    $datearray['arrive_nowweek_rate'] = $dateexcelvar['arrive_nowweek_rate'];
                    $datearray['invite_month_num'] = $dateexcelvar['invite_month_num'];
                    $datearray['invite_week_num'] = $dateexcelvar['invite_week_num'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('校区名称', '中文名', '英文名', '本校职务', '系统名单总数', '报名/当年', '报名/上月', '报名/当月', '报名/月',
                '报名/上周', '报名/当周', '报名/周', "追踪人数/月", "追踪人次/月", "追踪人数/周", "追踪人次/周",
                "邀约诺访/当月", "OH邀约诺访/当月", "试听邀约诺访/当月", "柜询插测邀约诺访/当月", "柜询邀约诺访/当月",
                "邀约到访/当月", "OH邀约到访/当月", "试听邀约到访/当月", "柜询插测邀约到访/当月", "柜询邀约到访/当月", "邀约到访率/当月",
                "邀约诺访/当周", "OH邀约诺访/当周", "试听邀约诺访/当周", "柜询插测邀约诺访/当周", "柜询邀约诺访/当周",
                "邀约到访/当周", "OH邀约到访/当周", "试听邀约到访/当周", "柜询插测邀约到访/当周", "柜询邀约到访/当周", "邀约到访率/当周",
                "主动到访/月", "主动到访/周"));
            $excelfileds = array('school_shortname', 'staffer_cnname', 'staffer_enname', 'post_name', 'clientnum', 'princ_clientnum', 'princ_upmonth_num', 'princ_nowmonth_num', 'princ_month_num',
                'princ_upweek_num', 'princ_nowweek_num', 'princ_week_num', 'track_month_num', 'track_month_time', 'track_week_num', 'track_week_time',
                'invite_nowmonth_num', 'ohaudition_nowmonth_num', 'audition_nowmonth_num', 'invitetwo_nowmonth_num', 'invitesan_nowmonth_num',
                'arrive_nowmonth_num', 'oharrive_nowmonth_num', 'auditiontwo_nowmonth_num', 'invitesix_nowmonth_num', 'inviteseven_nowmonth_num', 'arrive_nowmonth_rate',
                'invite_nowweek_num', 'ohaudition_nowweek_num', 'audition_nowweek_num', 'invitetwo_nowweek_num', 'invitesan_nowweek_num',
                'arrive_nowweek_num', 'oharrive_nowweek_num', 'auditiontwo_nowweek_num', 'invitesix_nowweek_num', 'inviteseven_nowweek_num', 'arrive_nowweek_rate',
                'invite_month_num', 'invite_week_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("教师招生分析表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $mark_data;
        return $data;
    }

}