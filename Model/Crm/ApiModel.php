<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  ApiModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
    }

    /**
     * 针对积分商城的接口 -- 有推荐人的有效名单
     * @param $paramArray
     * @return array
     */
    public function getEffectiveClient($paramArray)
    {
        if (!isset($paramArray['company_id']) || $paramArray['company_id'] == '') {
            $this->error = 1;
            $this->errortip = "集团ID必传";
            return false;
        }

        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.client_stubranch <> '' and c.client_stubranch is not null  and ( c.client_intention_level > 2 
         or (SELECT t.track_id FROM crm_client_track as t WHERE t.client_id = c.client_id and t.track_followmode > 1 and t.track_followmode < 3 limit 0,1 ) > 0 ) 
         and c.client_id = i.client_id and i.coursecat_id > 0 and (i.coursetype_id = '65'  or i.coursetype_id = '79654' or i.coursetype_id = '79655' or i.coursetype_id = '61')";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' )";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = strtotime($paramArray['start_time']);
            $datawhere .= " and c.client_createtime >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = strtotime($paramArray['end_time']) + 89399;
            $datawhere .= " and c.client_createtime <= '{$end_time}'";
        }

        $sql = " SELECT c.client_id,c.client_cnname,c.client_enname,c.client_sponsor,c.client_stubranch,c.client_createtime,FROM_UNIXTIME(c.client_createtime) as client_createtimedate,i.coursetype_id,i.coursecat_id
                FROM crm_client c ,crm_client_intention i 
                WHERE {$datawhere} 
                GROUP BY c.client_id ASC 
                ORDER BY c.client_id DESC ";

        $clientList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT count(c.client_id) as allnum FROM crm_client c,crm_client_intention i  WHERE {$datawhere} ");
        $allnums = $all_num['allnum'];

        $result = array();
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if($clientList) {
            $this->errortip = "数据获取成功";
        }else{
            $this->errortip = "暂时没有数据";
        }
        return $result;
    }
    /**
     * 针对积分商城的接口 -- 有推荐人的有效名单 有到访的名单
     * @param $paramArray
     * @return array
     */
    public function getEffectiveClientVisit($paramArray)
    {
        if (!isset($paramArray['company_id']) || $paramArray['company_id'] == '') {
            $this->error = 1;
            $this->errortip = "集团ID必传";
            return false;
        }

        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.client_stubranch <> '' and c.client_stubranch is not null and c.client_intention_level > 2 and c.client_id = v.client_id and v.isvisit = '1' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' )";
        }
//        if (isset($paramArray['type']) && $paramArray['type'] == '1') {
//            $datawhere .= " and v.coursetype_id = '65'";
//        }elseif (isset($paramArray['type']) && $paramArray['type'] == '2') {
//            $datawhere .= " and v.coursetype_id = '64'";
//        }elseif (isset($paramArray['type']) && $paramArray['type'] == '3') {
//            $datawhere .= " and v.coursetype_id = '61'";
//        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = strtotime($paramArray['start_time']);
            $datawhere .= " and c.client_createtime >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = strtotime($paramArray['end_time']) + 89399;
            $datawhere .= " and c.client_createtime <= '{$end_time}'";
        }

        $sql = " SELECT c.client_id,c.client_cnname,c.client_enname,c.client_sponsor,c.client_stubranch,c.client_createtime,v.coursetype_id,v.coursecat_id,v.visittime,v.isvisit  
                FROM crm_client c,view_crm_invitelog v 
                WHERE {$datawhere} and (v.coursetype_id = '65'  or v.coursetype_id = '79654' or v.coursetype_id = '79655' or v.coursetype_id = '61')
                ORDER BY c.client_id DESC ";

        $clientList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT count(c.client_id) as allnum FROM crm_client c,view_crm_invitelog v  WHERE {$datawhere} ");
        $allnums = $all_num['allnum'];

        $result = array();
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if($clientList) {
            $this->errortip = "数据获取成功";
        }else{
            $this->errortip = "暂时没有数据";
        }
        return $result;
    }
    /**
     * 针对积分商城的接口 -- 有推荐人的有效名单 报名的名单
     * @param $paramArray
     * @return array
     */
    public function getEffectiveClientReg($paramArray)
    {
        if (!isset($paramArray['company_id']) || $paramArray['company_id'] == '') {
            $this->error = 1;
            $this->errortip = "集团ID必传";
            return false;
        }

        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.client_stubranch <> '' and c.client_stubranch is not null and ( c.client_intention_level > 2 or (SELECT t.track_id FROM crm_client_track as t WHERE t.client_id = c.client_id and t.track_followmode > 1 and t.track_followmode < 3 limit 0,1 ) > 0 ) and c.client_id = s.from_client_id and s.student_id = r.student_id and r.pay_price > 0 and r.info_status = '1' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' )";
        }
//        if (isset($paramArray['type']) && $paramArray['type'] == '1') {
//            $datawhere .= " and r.coursetype_id = '65'";
//        }elseif (isset($paramArray['type']) && $paramArray['type'] == '2') {
//            $datawhere .= " and r.coursetype_id = '64'";
//        }elseif (isset($paramArray['type']) && $paramArray['type'] == '3') {
//            $datawhere .= " and r.coursetype_id = '61'";
//        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = strtotime($paramArray['start_time']);
            $datawhere .= " and r.pay_firsttime >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = strtotime($paramArray['end_time']) + 89399;
            $datawhere .= " and r.pay_firsttime <= '{$end_time}'";
        }

        $sql = " SELECT c.client_id,c.client_cnname,c.client_enname,c.client_sponsor,c.client_stubranch,c.client_createtime,r.pay_firsttime,r.coursetype_id,r.coursecat_id 
                FROM crm_client c ,smc_student s ,smc_student_registerinfo r  
                WHERE {$datawhere}  and (r.coursetype_id = '65' or r.coursetype_id = '79654' or r.coursetype_id = '79655' or r.coursetype_id = '61')
                ORDER BY c.client_id DESC ";

        $clientList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT count(c.client_id) as allnum FROM crm_client c ,smc_student s ,smc_student_registerinfo r WHERE {$datawhere} ");
        $allnums = $all_num['allnum'];

        $result = array();
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if($clientList) {
            $this->errortip = "数据获取成功";
        }else{
            $this->errortip = "暂时没有数据";
        }
        return $result;
    }

}
