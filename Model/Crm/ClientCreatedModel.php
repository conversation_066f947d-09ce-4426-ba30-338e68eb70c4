<?php

namespace Model\Crm;

class ClientCreatedModel extends modelTpl{
    public $error = false;
    public $errortwo = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $companyOne = array();//操作公司
    public $schoolOne = array();//操作学校
    public $marketerOne = false;//操作人
    public $activityOne = false;//入口活动

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_language,company_misopen,company_isaddstudent", "company_id='{$publicarray['company_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['activity_id'])) {
            $this->activityOne = $this->DataControl->getFieldOne("crm_sell_activity"
                , "activity_id,activity_type,activity_pattern", "company_id ='{$this->companyOne['company_id']}' AND activity_id = '{$publicarray['activity_id']}'");
        }

        if (isset($publicarray['marketer_id'])) {
            $this->verdictMarketer($publicarray['marketer_id']);
        }

        if (isset($publicarray['school_id'])) {
            $this->schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_branch,school_cnname", "school_id = '{$publicarray['school_id']}' AND company_id='{$publicarray['company_id']}' and school_isclose = '0' ");
        }

        if (isset($publicarray['school_branch'])) {
            $this->schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_branch,school_cnname", "school_branch = '{$publicarray['school_branch']}' AND company_id='{$publicarray['company_id']}' and school_isclose = '0' ");
        }
    }

    //验证订单信息
    function verdictMarketer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "CRM用户不存在";
            return false;
        }
    }

    /**0
     * 名单撞单逻辑处理类
     **/
    function CrmClientVerify($paramArray, $track_note = '',$isgmcimport = 0)
    {
        //计算生日
        if(strlen($paramArray['client_birthday']) > 7){
            $nowyear = date("Y", time());
            $getyear = date("Y", strtotime($paramArray['client_birthday']));
            $age = $nowyear - $getyear;
            $paramArray['client_age'] = $age;
        }elseif(preg_match("/^((\d){1,2})$/", $paramArray['client_age']) == '1'){
            $nowyear = date("Y", time());
            $cyear = $nowyear - $paramArray['client_age'];
            $paramArray['client_birthday'] = $cyear.'-06-01';
        }

        if(isset($paramArray['t_linktype']) && $paramArray['t_linktype'] != ''){
            $addlinktype = $this->LgStringSwitch($paramArray['t_linktype']);
            $updatalinktype = $this->LgStringSwitch($paramArray['t_linktype'].'更新');
        }elseif (!$this->activityOne) {
            $addlinktype = $this->LgStringSwitch('渠道新增名单');
            $updatalinktype = $this->LgStringSwitch('渠道名单更新');
        } else {
            $addlinktype = $this->LgStringSwitch("活动新增名单");
            $updatalinktype = $this->LgStringSwitch("活动名单更新");
        }

        $paramArray['client_mobile'] = trim($paramArray['client_mobile']);
        //STEP1 在校学员检测
        $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch,s.student_id FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['client_mobile']}' and s.company_id = '{$paramArray['company_id']}' AND s.student_isdel = '0'
 ORDER BY s.student_id limit 0,1");
        if ($familyOne) {
            $result = array();
            $result['isstudent'] = 1;
            $result['student_id'] = $familyOne['student_id'];

            $this->error = '1';
            $this->errortip = $this->LgStringSwitch("检测到您已是在校学员：{$familyOne['student_cnname']}，可直接联络校区老师或官方电话咨询！(1)");
            $this->result = $result;
            return false;
        }
        //STEP2 检测是否已存在名单
        if(!$this->DataControl->getFieldOne("crm_client", "client_id", "client_mobile = '{$paramArray['client_mobile']}' and company_id = '{$paramArray['company_id']}'")){
            return true;
        }

        $clientExistOne = $this->DataControl->getOne("crm_client", "client_mobile = '{$paramArray['client_mobile']}' and company_id = '{$paramArray['company_id']}' order by client_tracestatus desc,client_id ASC ");

        //STEP3 检测名单是否已报名
        if($clientExistOne['client_tracestatus'] >= '4'){
            $this->error = '1';
            if($this->companyOne['company_language'] == 'tw'){
                $this->errortip = $this->LgStringSwitch("已檢測到您的報名訊息，無須重複提交名單諮詢！(2)");
            }else{
                $this->errortip = $this->LgStringSwitch("已检测到您的报名信息，无需重复提交名单咨询！(2)");
            }
            return false;
        }

        //STEP4 检测名单是否存在同胞转介绍
        $comClientTwo = $this->DataControl->selectClear("select a.client_id,a.channel_id 
                            from crm_client as a
                            left join crm_client_family as b ON a.client_id = b.client_id 
                            left join smc_parenter as c ON b.parenter_id = c.parenter_id 
                            where (a.client_mobile='{$paramArray['client_mobile']}' or c.parenter_mobile = '{$paramArray['client_mobile']}') AND (a.channel_id = '577' or a.channel_id = '580') and a.company_id='{$paramArray['company_id']}'
                            limit 0,1");
        if($comClientTwo){
            $trackData = array();
            if ($paramArray['school_id'] > 0) {
                $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $addlinktype);
            } else {
                $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $addlinktype);
            }

            if($this->companyOne['company_language'] == 'tw') {
                $trackData['track_note'] = $this->LgStringSwitch($track_note . "檢測到手機號碼存在渠道為兄弟姊妹已在名單，禁止創建！(一)");
            }else{
                $trackData['track_note'] = $this->LgStringSwitch($track_note . "检测到手机号存在渠道为同胞转介绍名单，禁止创建！(一)");
            }
            $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, 0, 1);

            $this->error = '1';
            if($this->companyOne['company_language'] == 'tw'){
                $this->errortip = $this->LgStringSwitch("檢測到手機號碼存在渠道為兄弟姊妹已在名單，無須提交名單諮詢！(3)");
            }else{
                $this->errortip = $this->LgStringSwitch("检测到手机号存在渠道为同胞转介绍名单，无需提交名单咨询！(3)");
            }
            return false;
        }

        //获取名单渠道 负责人等信息
        $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_maxday", "channel_id = '{$clientExistOne['channel_id']}'");
        //当前单校TMK负责人
        $tmkprincipal = $this->DataControl->selectOne("select t.tmkprincipal_id from crm_client as c,crm_client_tmkprincipal as t 
                         where c.client_id = t.client_id and c.client_id='{$clientExistOne['client_id']}' and c.company_id='{$paramArray['company_id']}' and t.tmkprincipal_leave = 0");
        //当前单校负责人
        $principal = $this->DataControl->selectOne("select p.principal_id from crm_client as c,crm_client_principal as p 
                      where c.client_id = p.client_id and c.client_id='{$clientExistOne['client_id']}' and c.company_id='{$paramArray['company_id']}' and p.principal_leave = 0");
        //当前在学校名单
        $schoolEnter = $this->DataControl->getFieldOne("crm_client_schoolenter", "school_id", "client_id = '{$clientExistOne['client_id']}' and is_enterstatus = 1");

        $time = date("Y-m-d");
        $create = date("Y-m-d", $clientExistOne['client_createtime']);
        $max = date("Y-m-d", strtotime($create . "+{$channelOne['channel_maxday']} day"));
        //45天操作保护期
        $endtimes = time() - 3600 * 24 * 45;
        $IsrecTrack = $this->DataControl->getFieldOne("crm_client_track", "track_id", "client_id = '{$clientExistOne['client_id']}' and (track_isgmcactive=1 or track_isactive=1) AND track_createtime > '{$endtimes}'");

        //STEP5 检测名单是否无意向或无效名单
        if($clientExistOne['client_tracestatus'] < '0'){

            //集团导入无意向名单或无意向名单处理
            if($isgmcimport == '1'){
                //集团导入 无意向名单 最后跟踪时间1个月内有主动跟踪不让导入（ima 3个月）
                if($clientExistOne['client_tracestatus'] == '-1' ){
                    $time1month = strtotime(date("Y-m-d") . "-3 month");
                    $trackOne30 = $this->DataControl->getFieldOne("crm_client_track", "track_id", "client_id = '{$clientExistOne['client_id']}' and (track_isgmcactive=1 or track_isactive=1) AND track_createtime > '{$time1month}'");
                    if($trackOne30) {
                        //补充生日年龄
                        $this->agebrithUpdataClient($paramArray,$clientExistOne);

                        $this->error = '1';
                        $this->errortip = $this->LgStringSwitch("集团导入名单，经检测发现无意向名单三个月内有人跟进，禁止导入！(4)");
                        return false;
                    }
                }
                //集团导入 无效名单 最后跟踪时间6个月内有主动跟踪不让导入（ima 12个月）
                if($clientExistOne['client_tracestatus'] == '-2' ){
                    $time6month = strtotime(date("Y-m-d") . "-12 month");
                    $trackOne180 = $this->DataControl->getFieldOne("crm_client_track", "track_id", "client_id = '{$clientExistOne['client_id']}' and (track_isgmcactive=1 or track_isactive=1) AND track_createtime > '{$time6month}'");
                    if($trackOne180) {
                        //补充生日年龄
                        $this->agebrithUpdataClient($paramArray,$clientExistOne);

                        $this->error = '1';
                        $this->errortip = $this->LgStringSwitch("集团导入名单，经检测发现无效名单一年内有人跟进，禁止导入！(5)");
                        return false;
                    }
                }
            }

            //判断是否过保护期
            if ($channelOne['channel_maxday'] != 0 && $time > $max) {
                //过保护期
                if ($IsrecTrack) {
                    // 45天内有人跟进
                    //解除负责人信息
                    $this->leavePrincipal($clientExistOne['client_id'],$isgmcimport);
                    //重新分配名单，并补全名单信息
                    $this->distributionUpdataClient($paramArray,$clientExistOne,$isgmcimport);

                    $trackData = array();
                    if($this->schoolOne){
                        //重新校区激活适配
                        $this->addSchoolEnter($clientExistOne['client_id'], $this->schoolOne['school_id']);

                        if($schoolEnter['school_id'] !== $this->schoolOne['school_id']){
                            $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                        }else{
                            $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        }
                        $TrackType = 0;//到校
                    }else{
                        $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                        $TrackType = 1;//集团
                    }
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，已有负责人近期正在跟进，渠道替换失败。(二)");
                    $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, $TrackType, 1);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $clientExistOne['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    $this->error = '1';
                    $this->errortwo = '0';//为了课包活动，转为待分配也算撞单成功，加的状态
                    $this->errortip = $this->LgStringSwitch("名单激活失败，已有负责人近期正在跟进，渠道替换失败。(6)");
                    $this->result = $result;
                    return false;
                }
                else{
                    // 45天内无人跟进
                    //激活名单
                    $this->activatebutionUpdataClient($paramArray,$clientExistOne,$isgmcimport);
                    //解除负责人信息
                    $this->leavePrincipal($clientExistOne['client_id'],$isgmcimport);
                    //渠道变更记录
                    $this->addChannellog($clientExistOne,$paramArray['channel_id'],$this->LgStringSwitch("名单无负责人，过渠道保护期，变更渠道，重新激活！"));

                    // 可以补充 618 活动发券规则
                    $this->sendCoupHuodong($paramArray['student_id'],$paramArray['cou_type']);
                    // 集团导入名单 不进入学校操作 有歧义
                    $this->gmcClietToSchool($isgmcimport,$paramArray['istoschool'],$clientExistOne['client_id']);

                    $trackData = array();
                    if($this->schoolOne){
                        //重新校区激活适配
                        $this->addSchoolEnter($clientExistOne['client_id'], $this->schoolOne['school_id']);

                        if($schoolEnter['school_id'] !== $this->schoolOne['school_id']){
                            $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                        }else{
                            $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        }
                        $TrackType = 0;//到校
                    }else{
                        // 20250402 tmk 刘慧确认 集团导入的 可以撞单成功的，名单上不带学校，全部进入集团
                        $this->gmcClietToSchoolSecond($isgmcimport,$clientExistOne['client_id']);

                        $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                        $TrackType = 1;//集团
                    }
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活成功，旧名单过保护期且近期无人跟进，渠道替换成功。(三)");
                    $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, $TrackType, 1, 0);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $clientExistOne['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    $this->error = '0';
                    $this->errortip = $this->LgStringSwitch("名单激活成功，旧名单过保护期且近期无人跟进，渠道替换成功。(7)");
                    $this->result = $result;
                    return false;
                }
            }else{
                //未过保护期
                if(!$tmkprincipal && !$principal){
                    //无负责人名单状况
                    //不用处理
                }else{
                    //解除负责人信息
                    $this->leavePrincipal($clientExistOne['client_id'],$isgmcimport);
                }
                //重新分配名单，并补全名单信息
                $this->distributionUpdataClient($paramArray,$clientExistOne,$isgmcimport);
                $this->error = '1';
                $this->errortwo = '0';//为了课包活动，转为待分配也算撞单成功，加的状态
                $this->errortip = $this->LgStringSwitch("名单激活失败，未过名单保护期，重新转为待分配有效名单。(8)");

                $trackData = array();
                if($this->schoolOne){
                    //重新校区激活适配
                    $this->addSchoolEnter($clientExistOne['client_id'], $this->schoolOne['school_id']);

                    if($schoolEnter['school_id'] !== $this->schoolOne['school_id']){
                        $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                    }else{
                        $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                    }
                    $TrackType = 0;//到校
                }else{
                    $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                    $TrackType = 1;//集团
                }
                $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，未过名单保护期，重新转为待分配有效名单。(四)");
                $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, $TrackType, 1, 0);

                $result = array();
                $result['isstudent'] = 0;
                $result['client_id'] = $clientExistOne['client_id'];
                $result['school_id'] = $this->schoolOne['school_id'];
                $this->result = $result;

                return false;
            }
        }

        //STEP7 检测名单是否有效名单
        if($clientExistOne['client_tracestatus'] < '4' && $clientExistOne['client_tracestatus'] >= '0'){
            if ($channelOne['channel_maxday'] != 0 && $time > $max) {
                // 45天内有人跟进
                if ($IsrecTrack) {
                    //补充生日年龄
                    $this->agebrithUpdataClient($paramArray,$clientExistOne);

                    $trackData = array();
                    if ($paramArray['school_id'] > 0) {
                        $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $addlinktype);
                    } else {
                        $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $addlinktype);
                    }
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，近期有负责人跟进名单，新增名单撞单提醒。(五)");
                    $this->addTrack($clientExistOne['client_id'], $paramArray['school_id'], '0', $trackData, 0, 1);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $clientExistOne['client_id'];
                    $result['school_id'] = $this->schoolOne['school_id'];

                    $this->error = '1';
                    $this->errortip = $this->LgStringSwitch("名单激活失败，近期有负责人跟进名单，新增名单撞单提醒。(9)");
                    $this->result = $result;
                    return false;
                }

                //激活名单
                $this->activatebutionUpdataClient($paramArray,$clientExistOne,$isgmcimport);
                //解除负责人信息
                $this->leavePrincipal($clientExistOne['client_id'],$isgmcimport);
                //渠道变更记录
                $this->addChannellog($clientExistOne,$paramArray['channel_id'],$this->LgStringSwitch("名单近期无人跟进，已过渠道保护期，变更渠道，重新激活！"));

                // 可以补充 618 活动发券规则
                $this->sendCoupHuodong($paramArray['student_id'],$paramArray['cou_type']);
                // 集团导入名单 不进入学校操作 有歧义
                $this->gmcClietToSchool($isgmcimport,$paramArray['istoschool'],$clientExistOne['client_id']);

                //判断此名单是否是本校有效名单
                if ($this->schoolOne && $this->DataControl->getFieldOne("crm_client_schoolenter", "school_id"
                    , "client_id = '{$clientExistOne['client_id']}' AND is_enterstatus = '1' and school_id = '{$this->schoolOne['school_id']}'")) {
                    if ($principal || $tmkprincipal) {
                        //有负责人
                        $trackData = array();
                        $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "本校名单近期无人跟进，解除负责人，已过渠道保护期，渠道替换重新激活成功。(六)");
                        $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, 0, 1);
                    }
                    else{
                        //无负责人
                        $trackData = array();
                        $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "本校名单近期无人跟进，已过渠道保护期，渠道替换重新激活成功。(七)");
                        $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, 0, 1);
                    }

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $clientExistOne['client_id'];
                    $result['school_id'] = $this->schoolOne['school_id'];

                    $this->error = '0';
                    $this->errortip = "本校名单激活成功，近期无人跟进且过保护期，渠道替换成功。(10)";
                    $this->result = $result;
                    return false;
                }else{
                    // 集团导入名单 不进入学校操作 有歧义 ----- 20250401
                    $this->gmcClietToSchool($isgmcimport,$paramArray['istoschool'],$clientExistOne['client_id']);

                    //当不在这个学校的时候
                    $trackData = array();
                    if($this->schoolOne){
                        //重新校区激活适配
                        $this->addSchoolEnter($clientExistOne['client_id'], $this->schoolOne['school_id']);

                        if($schoolEnter['school_id'] !== $this->schoolOne['school_id']){
                            $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                        }else{
                            $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        }
                        $TrackType = 0;//到校
                    }else{
                        // 20250402 tmk 刘慧确认 集团导入的 可以撞单成功的，名单上不带学校，全部进入集团
                        $this->gmcClietToSchoolSecond($isgmcimport,$clientExistOne['client_id']);

                        $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                        $TrackType = 1;//集团
                    }

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $clientExistOne['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    if ($this->DataControl->getFieldOne("crm_client_schoolenter", "school_id", "client_id = '{$clientExistOne['client_id']}' AND is_enterstatus = '1'")) {
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单在其他校区库中，近期无人跟进，激活成功，渠道替换成功。(八)");
                        $this->errortip = $this->LgStringSwitch("名单在其他校区库中，近期无人跟进，激活成功，渠道替换成功。(11)");
                    }else{
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单无校区负责，近期无人跟进，激活成功，渠道替换成功。(九)");
                        $this->errortip = $this->LgStringSwitch("名单无校区负责，近期无人跟进，激活成功，渠道替换成功。(12)");
                    }
                    $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, $TrackType, 1);

                    $this->error = '0';
                    $this->result = $result;
                    return false;
                }
            }else{
                //补充生日年龄
                $this->agebrithUpdataClient($paramArray,$clientExistOne);

                //未过保护期名单禁止更新渠道
                $trackData = array();
                if($this->schoolOne){
                    if($schoolEnter['school_id'] !== $this->schoolOne['school_id']){
                        $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                    }else{
                        $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                    }
                    $TrackType = 0;//到校
                }else{
                    $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                    $TrackType = 1;//集团
                }
                $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，未过名单保护期，新增名单撞单提醒。(十)");
                $this->addTrack($clientExistOne['client_id'], $this->schoolOne['school_id'], '0', $trackData, $TrackType, 1, 0);

                //未过保护期
                $result = array();
                $result['isstudent'] = 0;
                $result['client_id'] = $clientExistOne['client_id'];
                $result['school_id'] = $this->schoolOne['school_id'];

                $this->error = '1';
                $this->errortip = $this->LgStringSwitch("名单激活失败，未过名单保护期，新增名单撞单提醒。(13)");
                $this->result = $result;
                return false;
            }
        }

        $this->error = '1';
        $this->errortip = $this->LgStringSwitch("检测名单状态有误，请检核");
        return false;
    }

    /**
     * 激活成功，重新分配名单，并补全名单信息
     * $paramArray 名单入参
     * $clientExistOne 已存在名单
     **/
    function activatebutionUpdataClient($paramArray,$clientExistOne,$isgmcimport=0){
        //渠道
        $channelOne = $this->DataControl->selectOne("select channel_id,channel_medianame,channel_intention_level from crm_code_channel where company_id = '{$this->companyOne['company_id']}' AND channel_id = '{$paramArray['channel_id']}'");

        $data = array();
        if(isset($paramArray['promotion_id'])){
            $data['promotion_id'] = $paramArray['promotion_id'];
        }else{
            $data['promotion_id'] = 0;
        }
        $data['client_cnname'] = $paramArray['client_cnname'];
        if ($paramArray['client_address']) {
            $data['client_address'] = $paramArray['client_address'];
        }
        if ($paramArray['client_patriarchname']) {
            $data['client_patriarchname'] = $paramArray['client_patriarchname'];
        }
        if ($paramArray['client_email']) {
            $data['client_email'] = $paramArray['client_email'];
        }
        if ($paramArray['client_age']) {
            $data['client_age'] = $paramArray['client_age'];
        }
        if ($paramArray['client_birthday']) {
            $data['client_birthday'] = $paramArray['client_birthday'];
        }
        if ($paramArray['client_lineid']) {
            $data['client_lineid'] = $paramArray['client_lineid'];
        }
        if ($paramArray['client_facebookid']) {
            $data['client_facebookid'] = $paramArray['client_facebookid'];
        }
        if($channelOne['channel_intention_level'] > 0){
            $data['client_intention_level'] = $channelOne['channel_intention_level'];
            $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
        }else{
            $data['client_intention_maxlevel'] = 0;
        }
        $data['client_tracestatus'] = 0;
        $data['client_distributionstatus'] = 0;
        $data['client_ischaserlapsed'] = 0;

        $data['client_isinvalidreview'] = 0;
        $data['invalidnote_code'] = '';
        $data['client_invalidmarketer_id'] = 0;
        $data['client_invalidreviewtime'] = '';
        if($isgmcimport == 1){
            $data['client_gmcdistributionstatus'] = 0;
        }
        if ($clientExistOne['client_isgross'] == 1) {
            $data['client_isgross'] = 0;
        }
        if ($paramArray['client_sponsor']) {
            $data['client_sponsor'] = $paramArray['client_sponsor'];
        }
        if ($paramArray['client_stubranch']) {
            $data['client_stubranch'] = $paramArray['client_stubranch'];
        }
        if ($paramArray['client_frompage']) {//接触点
            $data['client_frompage'] = $paramArray['client_frompage'];
        }
        if ($paramArray['client_easxstubranch']) {//事业部来源学员编号
            $data['client_easxstubranch'] = $paramArray['client_easxstubranch'];
        }
        $data['client_isfromgmc'] = '0';
        if($this->activityOne){
            if($this->activityOne['activity_type'] == '1'){
                $data['client_isfromgmc'] = '1';
            }
            $data['activity_id'] = $this->activityOne['activity_id'];

            //活动激活触发更新
            $this->activityUpdata($paramArray,$clientExistOne['client_id']);
        }
        $data['channel_id'] = $paramArray['channel_id'];
        $data['client_source'] = $paramArray['client_source'];
        $data['client_isnewtip'] = '1';
        $data['client_updatetime'] = time();
        $data['client_createtime'] = time();
        if (isset($paramArray['client_remark']) && trim($paramArray['client_remark']) !== '') {
            $data['client_remark'] = $clientExistOne['client_remark'].";".$paramArray['client_remark'];
        }
        $data['client_teachername'] = $paramArray['client_teachername'];//20230210发现微商城推荐学生推荐老师没有存入，故在撞单操作中补充老师信息
        $data['client_teacherid'] = $paramArray['client_teacherid'];//20230210发现微商城推荐学生推荐老师没有存入，故在撞单操作中补充老师信息
        $this->DataControl->updateData("crm_client", "client_id = '{$clientExistOne['client_id']}'", $data);

        //添加名单状态记录
        $Model = new  \Model\Api\CalloutModel($paramArray);
        if($isgmcimport =='1'){
            $Model->addClientTimerecord($this->companyOne['company_id'], $this->schoolOne['school_id'], $clientExistOne['client_id'], 1, $this->marketerOne['marketer_id'], "撞单成功，名单变为待分配");
        }else {
            $Model->addClientTimerecord($this->companyOne['company_id'], $this->schoolOne['school_id'], $clientExistOne['client_id'], 1, $this->marketerOne['marketer_id'], "撞单成功，名单变为待分配");
        }

        //更新意向课程
        $this->UpdataIntention($paramArray,$clientExistOne['client_id']);

        if($this->marketerOne){
            //更新负责人信息
            $this->updataClientPrincipal($paramArray,$clientExistOne['client_id']);
        }
    }

    /**
     * 活动激活触发更新
     * $client_id 名单ID
     **/
    function activityUpdata($paramArray,$clientId){
        //活动验券流程
        if ($this->activityOne['activity_pattern'] == '1') {
            //活动验券模式
            if ($this->DataControl->selectOne("select freevoucher_id from crm_sell_activity_freevoucher 
                      WHERE activity_id = '{$this->activityOne['activity_id']}' and freevoucher_class = '0'")) {
                $freevoucher_data = array();
                $freevoucher_data['client_id'] = $clientId;
                $freevoucher_data['freevoucher_isuse'] = 1;
                $freevoucher_data['freevoucher_usetime'] = time();
                $this->DataControl->updateData('crm_sell_activity_freevoucher',
                    "freevoucher_pid='{$paramArray['freevoucher_pid']}' and activity_id='{$this->activityOne['activity_id']}' ", $freevoucher_data);
            } else {
                $freevoucher_data = array();
                $freevoucher_data['freevoucher_class'] = 1;
                $freevoucher_data['company_id'] = $this->activityOne['company_id'];
                $freevoucher_data['activity_id'] = $this->activityOne['activity_id'];
                $freevoucher_data['client_id'] = $clientId;
                $freevoucher_data['freevoucher_pid'] = $paramArray['freevoucher_pid'];
                $freevoucher_data['freevoucher_name'] = $this->LgStringSwitch("合作专用券");
                $freevoucher_data['freevoucher_content'] = $this->LgStringSwitch("合作专用券");
                $freevoucher_data['freevoucher_isuse'] = 1;
                $freevoucher_data['freevoucher_usetime'] = time();
                $freevoucher_data['freevoucher_createtime'] = time();
                $this->DataControl->insertData('crm_sell_activity_freevoucher', $freevoucher_data);
            }
        }
    }

    /**
     * 未激活名单，重新分配名单，并补全名单信息
     * $paramArray 名单入参
     * $clientExistOne 已存在名单
     **/
    function distributionUpdataClient($paramArray,$clientExistOne,$isgmcimport){
        //补充名单信息
        $data = array();
        if($clientExistOne['client_cnname'] == ''){
            $data['client_cnname'] = $paramArray['client_cnname'];
        }
        if($clientExistOne['client_address'] == ''){
            $data['client_address'] = $paramArray['client_address'];
        }
        if ($clientExistOne['client_lineid'] == '') {
            $data['client_lineid'] = $paramArray['client_lineid'];
        }
        if ($clientExistOne['client_facebookid'] == '') {
            $data['client_facebookid'] = $paramArray['client_facebookid'];
        }
        //微信唯一值
        if($paramArray['client_wxunionid'] != ''){
            $data['client_wxunionid'] = $paramArray['client_wxunionid'];
            $data['client_syncqwtime'] = time();
        }
        if($clientExistOne['client_birthday']){
            $nowyear = date("Y", time());
            $getyear = date("Y", strtotime($clientExistOne['client_birthday']));
            $age = $nowyear - $getyear;
            $data['client_age'] = $age;
        }else {
            if ($paramArray['client_age']) {
                $data['client_age'] = $paramArray['client_age'];
            }
            if ($paramArray['client_birthday']) {
                $data['client_birthday'] = $paramArray['client_birthday'];
            }
        }

        $data['client_tracestatus'] = 0;
        $data['client_distributionstatus'] = 0;

        $data['client_ischaserlapsed'] = 0;
        $data['client_isinvalidreview'] = 0;
        $data['client_invalidmarketer_id'] = 0;
        $data['client_invalidreviewtime'] = '';
        $data['invalidnote_code'] = '';
        //毛名单优化待分配
        if ($clientExistOne['client_isgross'] == 1) {
            $data['client_isgross'] = 0;
        }
        $data['client_isnewtip'] = '1';
        $data['client_updatetime'] = time();
        $this->DataControl->updateData("crm_client", "client_id = '{$clientExistOne['client_id']}'", $data);

        //添加名单状态记录
        $Model = new  \Model\Api\CalloutModel($paramArray);
        if($isgmcimport =='1'){
            $Model->addClientTimerecord($this->companyOne['company_id'], $this->schoolOne['school_id'], $clientExistOne['client_id'], 1, $this->marketerOne['marketer_id'], "撞单失败，改为待分配");
        }else {
            $Model->addClientTimerecord($this->companyOne['company_id'], $this->schoolOne['school_id'], $clientExistOne['client_id'], 1, $this->marketerOne['marketer_id'], "撞单失败，改为待分配");
        }
    }

    /**
     * 未激活名单，补充名单信息
     * $paramArray 名单入参
     * $clientExistOne 已存在名单
     **/
    function agebrithUpdataClient($paramArray,$clientExistOne){
        //补充名单信息
        $data = array();
        if($clientExistOne['client_birthday']){
            $nowyear = date("Y", time());
            $getyear = date("Y", strtotime($clientExistOne['client_birthday']));
            $age = $nowyear - $getyear;
            $data['client_age'] = $age;
        }else {
            if ($paramArray['client_age']) {
                $data['client_age'] = $paramArray['client_age'];
            }
            if ($paramArray['client_birthday']) {
                $data['client_birthday'] = $paramArray['client_birthday'];
            }
        }
        $data['client_updatetime'] = time();
        $this->DataControl->updateData("crm_client", "client_id = '{$clientExistOne['client_id']}'", $data);
    }

    /**
     * 验证传输数据完整度
     **/
    function verdictClient($clientData){
        if(!isset($request['client_cnname']) || $request['client_cnname'] == ''){
            $this->error = true;
            $this->errortip = "学员称呼不能为空";
            return false;
        }

        if(!isset($request['client_mobile']) || $request['client_mobile'] == ''){
            $this->error = true;
            $this->errortip = "学员联系手机不能为空";
            return false;
        }

        return true;
    }


    /**
     * 客户线索 -- 意向课程 crm_client_intention
     */
    function UpdataIntention($paramArray,$clientId){
        //意向课程
        $CoursecatArray = json_decode(stripslashes($paramArray['coursecat_json']), 1);
        if (!empty($CoursecatArray)) {
            foreach ($CoursecatArray as $CoursecatVar) {
                if (!$this->DataControl->getFieldOne("crm_client_intention", "intention_id",
                    "client_id = '{$clientId}' AND coursecat_id = '{$CoursecatVar['coursecat_id']}'")) {
                    $coursetypeOne = $this->DataControl->selectOne("select coursetype_id from smc_code_coursecat 
                     where coursecat_id = '{$CoursecatVar['coursecat_id']}' AND company_id = '{$this->companyOne['company_id']}' limit 0,1 ");
                    if($coursetypeOne){
                        $intention = array();
                        $intention['client_id'] = $clientId;
                        $intention['coursetype_id'] = $coursetypeOne['coursetype_id'];
                        $intention['coursecat_id'] = $CoursecatVar['coursecat_id'];
                        $intention['intention_updatetime'] = time();
                        $this->DataControl->insertData("crm_client_intention", $intention);
                    }
                }
            }
        }
    }

    /**
     * 客户线索 -- 客户标签 crm_client_tags
     */


    /**
     * 客户线索 -- 学员适配学校表 crm_client_schoolenter
     */
    function addSchoolEnter($client_id, $school_id)
    {
        if($school_id > 0){
            $exitNowschool = false;
            $schoolenterList = $this->DataControl->selectClear("select school_id from crm_client_schoolenter where client_id='{$client_id}'");//20250402 有做修改
            if($schoolenterList){
                $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id<>'{$school_id}'", array("is_enterstatus" => '-1',"schoolenter_updatetime" => time()));
                foreach ($schoolenterList as $schoolenterOne){
                    if(isset($schoolenterOne['school_id']) && $schoolenterOne['school_id'] == $school_id){
                        $schoolenter_array = array();
                        $schoolenter_array['is_enterstatus'] = '1';
                        $schoolenter_array['schoolenter_updatetime'] = time();
                        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id='{$school_id}'", $schoolenter_array);
                        $exitNowschool = true;
                    }
                }
            }

            if(!$exitNowschool){
                $schoolenter_array = array();
                $schoolenter_array['client_id'] = $client_id;
                $schoolenter_array['school_id'] = $school_id;
                $schoolenter_array['company_id'] = $this->companyOne['company_id'];
                $schoolenter_array['is_enterstatus'] = '1';
                $schoolenter_array['schoolenter_updatetime'] = time();
                $schoolenter_array['schoolenter_createtime'] = time();
                $this->DataControl->insertData("crm_client_schoolenter", $schoolenter_array);
            }
        }
    }

    /**
     * 客户线索 -- 添加名单跟踪记录 crm_client_track
     */
    function addTrack($client_id, $school_id = 0, $marketer_id, $track, $type, $isImport, $followmode = 0)
    {
        //添加名单状态记录
        $Model = new  \Model\Api\CalloutModel($client_id);
        $Model->addClientTimerecord($this->companyOne['company_id'], $this->schoolOne['school_id'], $client_id, 5, $this->marketerOne['marketer_id'], "名单撞单记录");

        $trackData = array();
        $trackData['client_id'] = $client_id;
        if($school_id > 0){
            $trackData['school_id'] = $school_id;
        }
        if($marketer_id > 0){
            $trackData['marketer_id'] = $marketer_id;
            $trackData['marketer_name'] = $this->marketerOne['marketer_name'];
        }else{
            $trackData['marketer_id'] = '0';
            $trackData['marketer_name'] = $this->LgStringSwitch('系统');
        }

        $trackData['track_validinc'] = 1;
        $trackData['track_followmode'] = $followmode;
        $trackData['track_linktype'] = $track['track_linktype'];
        $trackData['track_note'] = $track['track_note'];
        $trackData['track_createtime'] = time();
        $trackData['track_type'] = $type;
        $trackData['track_initiative'] = $isImport;
        $this->DataControl->insertData('crm_client_track', $trackData);
    }


    /**
     * 招募负责人表 crm_client_allotlog
     */
    function updataClientPrincipal($paramArray,$clientId){
        if ($this->marketerOne && $this->schoolOne) {
            $allotlog = array();
            $allotlog['client_id'] = $clientId;
            $allotlog['school_id'] = $this->schoolOne['school_id'];
            $allotlog['marketer_id'] = $this->marketerOne['marketer_id'];
            $allotlog['allot_marketer_id'] = $this->marketerOne['marketer_id'];
            $allotlog['allotlog_status'] = 1;
            $allotlog['allotlog_createtime'] = time();
            $this->DataControl->insertData('crm_client_allotlog', $allotlog);

            //主招教师编号
            $this->setClientPrincipal($clientId, $this->marketerOne['marketer_id'], $this->schoolOne['school_id'], 1);
        }
    }
    /**
     * 设定主负责人信息 crm_client_principal
     */
    public function setClientPrincipal($client_id, $tomarketer_id, $to_school_id, $ismajor = 1)
    {
        if (!$this->DataControl->selectOne("SELECT principal_id FROM crm_client_principal 
                    where client_id = '{$client_id}' AND marketer_id = '{$tomarketer_id}' AND school_id = '{$to_school_id}'")) {
            $dataPrincipal = array();
            $dataPrincipal['client_id'] = $client_id;;
            $dataPrincipal['marketer_id'] = $tomarketer_id;
            $dataPrincipal['school_id'] = $to_school_id;
            $dataPrincipal['principal_ismajor'] = $ismajor;
            $dataPrincipal['principal_createtime'] = time();
            if (!$this->DataControl->insertData('crm_client_principal', $dataPrincipal)) {
                return false;
            } else {
                return true;
            }
        } else {
            $dataPrintcipal = array();
            $dataPrintcipal['principal_ismajor'] = $ismajor;
            $dataPrintcipal['principal_leave'] = 0;
            $dataPrintcipal['principal_updatatime'] = time();
            $this->DataControl->updateData('crm_client_principal'
                , "client_id = '{$client_id}' AND marketer_id = '{$tomarketer_id}' AND school_id = '{$to_school_id}'", $dataPrintcipal);
            return true;
        }
    }

    /**
     * 渠道变更记录 处理渠道变更历史 channellog crm_client_channel_tracks
     */
    function addChannellog($comClientOne,$new_channel_id,$channellog_note = "名单在无意向/无效，重新激活，直接变更渠道！")
    {
        $channello = array();
        $channello['company_id'] = $this->companyOne['company_id'];
        $channello['client_id'] = $comClientOne['client_id'];
        $channello['from_channel_id'] = $comClientOne['channel_id'];
        $channello['to_channel_id'] = $new_channel_id;
        $channello['channellog_note'] = $this->LgStringSwitch($channellog_note);
        $channello['client_createtime_old'] = $comClientOne['client_createtime'];
        $channello['channellog_createtime'] = time();
        if ($id = $this->DataControl->insertData("crm_client_channellog", $channello)) {
            $trackData = array();
            $trackData['client_id'] = $comClientOne['client_id'];
            $trackData['channellog_id'] = $id;
            $trackData['tracks_title'] = '0';
            $trackData['staffer_id'] = $this->markertOne['staffer_id'];
            $trackData['tracks_time'] = time();
            $this->DataControl->insertData("crm_client_channel_tracks", $trackData);
        }
    }

    /**
     * 解除名单负责人 crm_client_fal
     */
    function leavePrincipal($client_id,$isgmcimport=0)
    {
        //集团导入的时候撞单成功时解除原来用的集团负责人
        if($isgmcimport == '1'){
            //解除集团负责人
            $principal_data = array();
            $principal_data['principal_leave'] = 1;
            $principal_data['principal_updatatime'] = time();
            $this->DataControl->updateData("crm_client_principal", "client_id='{$client_id}' AND school_id  = '0'", $principal_data);

            //负责人日志更新
            $allotlog_data = array();
            $allotlog_data['allotlog_status'] = 0;
            $allotlog_data['allotlog_removetime'] = time();
            $this->DataControl->updateData("crm_client_allotlog", "client_id='{$client_id}' AND school_id  = '0'", $allotlog_data);
        }

        //解除单校负责人
        $principal_data = array();
        $principal_data['principal_leave'] = 1;
        $principal_data['principal_updatatime'] = time();
        $this->DataControl->updateData("crm_client_principal", "client_id='{$client_id}' AND school_id  <> '0'", $principal_data);

        //负责人日志更新
        $allotlog_data = array();
        $allotlog_data['allotlog_status'] = 0;
        $allotlog_data['allotlog_removetime'] = time();
        $this->DataControl->updateData("crm_client_allotlog", "client_id='{$client_id}' AND school_id  <> '0'", $allotlog_data);
        //解除单校TMK负责人
        $tmkprincipal_data = array();
        $tmkprincipal_data['tmkprincipal_leave'] = 1;
        $tmkprincipal_data['tmkprincipal_updatatime'] = time();
        $this->DataControl->updateData("crm_client_tmkprincipal", "client_id='{$client_id}' AND school_id  <> '0'", $tmkprincipal_data);

        //负责人日志更新
        $allotlog_data = array();
        $allotlog_data['tmkallotlog_status'] = 0;
        $allotlog_data['tmkallotlog_removetime'] = time();
        $this->DataControl->updateData("crm_client_tmkallotlog", "client_id='{$client_id}'", $allotlog_data);
    }

    //20230523  集团名单那导入 是否进入校务  istoschool -1 否    istoschool  1 进
    function gmcClietToSchool($isgmcimport = 0,$istoschool,$clientid){
        if($isgmcimport == 1 && $istoschool == '-1') {
            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$clientid}' ", $schoolenter);
        }
    }

    //20250402 tmk 刘慧确认 集团导入的 可以撞单成功的，名单上不带学校，全部进入集团
    function gmcClietToSchoolSecond($isgmcimport = 0,$clientid){
        if($isgmcimport == 1) {
            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$clientid}' ", $schoolenter);
        }
    }

    //20230523  618转介绍活动撞单成功的 也需要发券和改推荐老师
    function sendCoupHuodong($student_id,$cou_type){
        if($cou_type == 1 || $cou_type == 2 || $cou_type == 3) {
            // 618 推荐成功 自动 领券
            $receivedata = array();
            if($cou_type == 1){//----美语
                $receivedata['applytype_id'] = '676';
                $receivedata['applytype_branch'] = 'pddE';
            }elseif($cou_type == 2){//----素质
                $receivedata['applytype_id'] = '677';
                $receivedata['applytype_branch'] = 'pddSZ';
            }elseif($cou_type == 3){//----安亲
                $receivedata['applytype_id'] = '678';
                $receivedata['applytype_branch'] = 'pddT';
            }
            $receivedata['cou_type'] = $cou_type;

            //判断这个学生获取了多少张这类券
            $stuCouCount = $this->DataControl->selectOne(" select count(apply_id) as applycount from smc_student_coupons_apply 
                                      where company_id = '{$this->companyOne['company_id']}' and school_id = '{$this->schoolOne['school_id']}' and student_id = '{$student_id}' 
                                        and applytype_branch = '{$receivedata['applytype_branch']}'  ");
            if($stuCouCount['applycount']<10) {
                $receivedata['student_id'] = $student_id;
                $receivedata['company_id'] = $this->companyOne['company_id'];
                //默认操作发券人
                $staffcomone = $this->DataControl->selectOne(" select staffer_id from smc_staffer where company_id = '{$this->companyOne['company_id']}' 
                                    order by account_class desc,staffer_id ASC limit 0,1 ");
                $receivedata['staffer_id'] = $staffcomone['staffer_id'];
                $receivedata['school_id'] = $this->schoolOne['school_id'];
                $StudentModel = new \Model\Smc\StudentModel($receivedata);
                $StudentModel->receiveCoupons($receivedata);
            }
        }
    }
}