<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  ModuleModel extends modelTpl
{
    public $m;
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }
    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    //机构管理 -- 首页
    function getModuleList($paramArray)
    {
        $result = array();
        $schoolOne = $this->DataControl->getFieldOne("smc_school","school_cnname","school_id = '{$paramArray['school_id']}'");
        //此段了解目的
        $result['school_cnname'] = $schoolOne['school_cnname'];

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","account_class","staffer_id = '{$paramArray['staffer_id']}'");
        $levelOne = array();
        if($stafferOne['account_class'] == '1'){
            $levelOne['postbe_crmuserlevel'] = '1';
        }else{
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe","school_id,postrole_id,postbe_crmuserlevel","postbe_id = '{$paramArray['re_postbe_id']}'");
            if($postbeOne['school_id'] !== '0'){
                if($postbeOne && $postbeOne['postbe_crmuserlevel'] == '1'){
                    $levelOne['postbe_crmuserlevel'] = '1';
                }elseif($postbeOne && $postbeOne['postbe_crmuserlevel'] == '2'){
                    $levelOne['postbe_crmuserlevel'] = '2';
                }elseif($postbeOne && $postbeOne['postbe_crmuserlevel'] == '3'){//市场权限
                    $levelOne['postbe_crmuserlevel'] = '3';
                }else{
                    $levelOne['postbe_crmuserlevel'] = '0';
                }
            }else{
                $postroleOne = $this->DataControl->selectOne(" select postpart_iscrmuser,postpart_crmuserlevel from gmc_company_postrole where postrole_id = '{$postbeOne['postrole_id']}' ");//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场  20230330补充
                if($postroleOne['postpart_iscrmuser'] == '1' && isset($postroleOne['postpart_crmuserlevel'])){//有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场 20230330补充
                    $levelOne['postbe_crmuserlevel'] = $postroleOne['postpart_crmuserlevel'];
                }else {
                    if ($postbeOne && $postbeOne['postbe_crmuserlevel'] == '2') {
                        $levelOne['postbe_crmuserlevel'] = '2';
                    } elseif ($postbeOne && $postbeOne['postbe_crmuserlevel'] == '3') {//市场权限
                        $levelOne['postbe_crmuserlevel'] = '3';
                    } else {
                        $levelOne['postbe_crmuserlevel'] = '1';
                    }
                }
            }
        }
        $result['level'] = $levelOne;
        //此段了解目的
        $result['title'] = '招生菜单';

        $ismajorOne = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$paramArray['company_id']}'");
        $moduleWhere = "module_class = '3' and module_ismajor <= '{$ismajorOne['company_ismajor']}'";
        if($paramArray['company_id'] !== '1001'){
            $moduleWhere .= " AND module_isshow = '1'";
        }

        if($this->companyOne['company_isnointention'] == '0'){
            $moduleWhere .= " AND module_id <> '18' ";
        }
        //此段了解292目的
        if($levelOne['postbe_crmuserlevel'] == '0'){
            $moduleWhere .= " AND module_id <> '4' and module_id <> '292' and module_id <> '685' and module_id <> '683' and module_id <> '726'";
        }
        if($levelOne['postbe_crmuserlevel'] == '2'){
            $moduleWhere .= " AND (module_id = '1' or module_id = '2' or module_id = '9' or module_id = '683')";
        }
        //市场权限
        if($levelOne['postbe_crmuserlevel'] == '3'){
            $moduleWhere .= " AND module_id <> '3' and module_id <> '684' and module_id <> '683' and module_id <> '574' and module_id <> '18' and module_id <> '310' and module_id <> '453' ";
        }

        $moduleList = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id FROM imc_module where {$moduleWhere} order by module_weight ASC");

        $moduleList = $this->tree($moduleList);
        $result['children'] = $moduleList;


        //此段了解目的
        if($paramArray['url']){
            $moduleOne = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$paramArray['url']}'");
            $moduleCur = array();
            $moduleCur['module_id'] = $moduleOne['module_id'];
            $moduleCur['father_id'] = $moduleOne['father_id'];
        }else{
            $moduleCur = array();
        }
        $result['moduleCur'] = $moduleCur;
        return $result;
    }

    function tree($items)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if(is_array($items)){
            foreach($items as $k=>&$v) {
                if($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['label'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['module_markurl'] = $v['module_markurl'];
                    $son[$k]['module_icon'] = $v['module_icon'];
                    foreach ($items as $key=>$value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $urls = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' order by module_weight ASC limit 0,1");
                            $son[$k]['children'][$key]['label'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts.'-'.($count+1);
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($urls) {
                                $son[$k]['children'][$key]['module_markurl'] = $urls[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['module_markurl'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['module_icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_markurl'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];

                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }

}
