<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  ExceedAgeModel extends modelTpl
{
    public $m;
    public $allottip = "";
    public $allotnum = "";
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $isGmcPost = null;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }
    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'],$publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    //超龄名单 -> 超龄名单列表
    function getExceedAgeList($paramArray)
    {
        $datawhere = " 1 and c.client_distributionstatus = '0' and c.client_tracestatus = '0' "; //针对未分配的（有效名单）
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        $starttime = strtotime($paramArray['starttime']);
        $endtime = strtotime($paramArray['endtime']);
        if (isset($starttime) && $starttime != '' && isset($endtime) && $endtime != '' && $starttime == $endtime) {
            $endtime = $endtime + 86399;
            $datawhere .= " and c.client_createtime <= '{$endtime}' and c.client_createtime >= '{$starttime}'";
        } elseif (isset($starttime) && $starttime != '' && isset($endtime) && $endtime != '') {
            $datawhere .= " and c.client_createtime <= '{$endtime}' and c.client_createtime >= '{$starttime}'";
        } elseif (isset($starttime) && $starttime != '') {
            $datawhere .= " and c.client_createtime >= '{$starttime}'";
        } elseif (isset($endtime) && $endtime != '') {
            $datawhere .= " and c.client_createtime <= '{$endtime}'";
        }
        if (isset($paramArray['client_source']) && $paramArray['client_source'] !== '') {
            $datawhere .= " and c.client_source = '{$paramArray['client_source']}' ";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '') {
            $datawhere .= " and c.channel_id = '{$paramArray['channel_id']}'";
        }
        //超龄日期
        if ($paramArray['exceedagetime']) {
            $nowyear = date("Y", time());
            $getyear = date("Y", strtotime($paramArray['exceedagetime']));
            $age = $nowyear - $getyear;
            $datawhere .= " and (c.client_birthday < '{$paramArray['exceedagetime']}' or ( c.client_birthday = '' and c.client_age >= '{$age}') )";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  c.client_id
                FROM crm_client as c  
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id
                WHERE {$datawhere} and s.school_id = '{$paramArray['school_id']}'  GROUP BY c.client_id ";
            $count = $this->DataControl->selectClear($sql);
            if ($count) {
                $count = count($count);
            } else {
                $count = 0;
            }
        } else {
            $count = '';
        }

        $sqlFields = 'c.client_id,c.client_cnname,c.client_enname,c.client_tracestatus,c.client_actisagree,c.client_sex,c.client_age,c.client_mobile,c.client_birthday,c.client_source,c.client_createtime,c.client_img,c.client_email';
        $fieldOrder = 'c.client_id DESC';

        $sql = "SELECT {$sqlFields},
				(select p.parenter_cnname FROM crm_client_family as f  LEFT  JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				(select ch.channel_name  from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name
                FROM crm_client as c  
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id
                WHERE {$datawhere} and s.school_id = '{$paramArray['school_id']}'  
                GROUP BY c.client_id
                order by  {$fieldOrder}
                limit {$pagestart},{$num}";
        $goalList = $this->DataControl->selectClear($sql);

        $tracestatus = $this->LgArraySwitch(array("待跟踪","持续跟踪","已柜询","已试听","已转正","已流失","无效名单"));

        $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $sql = "SELECT {$sqlFields},
				(select p.parenter_cnname FROM crm_client_family as f  LEFT  JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				(select ch.channel_name  from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name
                FROM crm_client as c  
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id
                WHERE {$datawhere} and s.school_id = '{$paramArray['school_id']}'  GROUP BY c.client_id
                order by  {$fieldOrder}";
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            if ($dateexcelarray) {
                foreach ($dateexcelarray as &$dateexcelavar) {
                    $dateexcelavar['client_createtime'] = date("Y-m-d H:i:s", $dateexcelavar['client_createtime']);
                    $dateexcelavar['client_tracestatus'] = $tracestatus[$dateexcelavar['client_tracestatus']];
                }
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_id'] = $dateexcelvar['client_id'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_actisagree'] = ($dateexcelvar['client_actisagree'] == '1' ? $this->LgStringSwitch('同意') : $this->LgStringSwitch('不同意'));
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_birthday'] = $dateexcelvar['client_birthday'];
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
//                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
//                    if($staffone['account_class'] == '1'){
//                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
//                    }else {
//                        $datearray['client_mobile'] = str_replace(substr($dateexcelvar['client_mobile'], 3, 4), '****', $dateexcelvar['client_mobile']);
                        $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);
//                    }
                    $datearray['client_email'] = $dateexcelvar['client_email'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($paramArray['language_type'] == 'tw') {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性別', '年齡', '出生日期','跟踪狀態', '主要連絡人', '聯繫電話','Email', '招生通路類型', '招生通路明細', '創建時間'));
            } else {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '出生日期','跟踪状态', '主要联系人', '联系电话','Email', '招生渠道类型', '招生渠道明细', '创建时间'));
            }
            $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_birthday','client_tracestatus', 'family_cnname', 'client_mobile','client_email', 'client_source', 'channel_name', 'client_createtime');
            $fielname = $this->LgStringSwitch("超龄名单");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$paramArray['starttime']}-{$paramArray['endtime']}.xlsx");
            exit;
        }

        if ($goalList) {
            foreach ($goalList as &$goalvar) {
                $goalvar['client_createtime'] = date("Y-m-d H:i:s", $goalvar['client_createtime']);
                $goalvar['client_tracestatus'] = $tracestatus[$goalvar['client_tracestatus']];
                if($this->isGmcPost == true){
//                    $goalvar['client_mobile'] = str_replace(substr($goalvar['client_mobile'], 3, 4), '****', $goalvar['client_mobile']);
                    $goalvar['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $goalvar['client_mobile']);
                }else{
                    $goalvar['client_mobile'] = $goalvar['client_mobile'];
                }
            }
        }

        $result = array();
        $result["datalist"] = $goalList;
        $result["count"] = $count;
        return $result;
    }

    //超龄名单 -- 超龄日期
    function getExceedAgeApi($paramArray)
    {
        $sql = "SELECT * FROM crm_client_exceedage  WHERE school_id = '{$paramArray['school_id']}' ";
        $ExceedAgeOne = $this->DataControl->selectOne($sql);
        return $ExceedAgeOne;
    }

    //超龄名单 -- 修改超龄日期
    function updateExceedAgeAction($paramArray)
    {
        $data = array();
        $data['exceed_time'] = $paramArray['exceed_time'];
        $istrue = $this->DataControl->updateData("crm_client_exceedage", "school_id = '{$paramArray['school_id']}' ", $data);
        if ($istrue) {
            return $data;
        } else {
            return false;
        }
    }

    //超龄名单 -- 添加超龄日期
    function addExceedAgeAction($paramArray)
    {
        $data = array();
        $data['exceed_time'] = $paramArray['exceed_time'];
        $data['school_id'] = $paramArray['school_id'];
        $exceedage_id = $this->DataControl->insertData('crm_client_exceedage', $data);
        $exceedageOne = $this->DataControl->selectOne("select school_id from crm_client_exceedage where school_id = '{$paramArray['school_id']}' and exceed_time = '{$paramArray['exceed_time']}' limit 0,1 ");
        if ($exceedageOne) {
            return true;
        } else {
            return false;
        }
    }

    //超龄名单 -- 删除超龄日期
    function delExceedAgeAction($school_id)
    {
        $istrue = $this->DataControl->delData("crm_client_exceedage", "school_id = '{$school_id}' ");
        if ($istrue) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }

    /**
     * 超龄名单的转校
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @return bool
     */
    function transferEAClientSchool($paramArray)
    {
        if($paramArray['postbe_crmuserlevel'] <> 1){
            $this->error = 1;
            $this->errortip = "您暂无此权限";
            return false;
        }

        $arr_client = json_decode(stripslashes($paramArray['client_list']), true);
        if (is_array($arr_client) && count($arr_client) > 0) {
            $count_num = 0;
               $this->DataControl->begintransaction();
            for ($i = 0; $i < count($arr_client); $i++) {
                $paramArray['client_id'] = $arr_client[$i];
                if ($this->transferExceedAgeClientSchool($paramArray)) {
                    $count_num++;
                } else {
                    $this->DataControl->rollback();
                    return false;
                }
            }
            $this->DataControl->commit();
            $this->error = 0;
            $this->errortip = "共转校成功{$count_num}名学员";
            return false;
        }
    }

    //超龄名单个人转校
    private function transferExceedAgeClientSchool($paramArray)
    {
        //禁止非有效名单转校
        $clientOne = $this->DataControl->getOne("crm_client", "client_id='{$paramArray['client_id']}'");
        if ($clientOne['client_distributionstatus'] == 1 && $clientOne['client_tracestatus'] <> 0) {
            $this->error = 1;
            $this->errortip = "请先将改学员转为有效名单";
            return false;
        }
        $intentModel = new IntentionClientModel();
        $checkData = array();
        $checkData['client_cnname'] = $clientOne['client_cnname'];
        $checkData['client_mobile'] = $clientOne['client_mobile'];
        $checkData['school_id'] = $paramArray['re_school_id'];
        $checkData['company_id'] = $paramArray['company_id'];
        $from = "transferExcee";
        if (!$paramArray['re_school_id']) {
            $this->error = 1;
            $this->errortip = "请选择需要转入的目标学校";
            return false;
        }
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$paramArray['school_id']}'");
        $reSchoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$paramArray['re_school_id']}'");
        $intent = $intentModel->checkIntentionClient($checkData, $from, '');
        if ($intent) {
            $this->error = 1;
            $this->errortip = "目标学校中，存在中文名与手机号相同的学员";
            return false;
        }
        $schoolenterOne = $this->DataControl->getFieldOne('crm_client_schoolenter', "schoolenter_id", "school_id='{$paramArray['re_school_id']}' and  client_id='{$paramArray['client_id']}'");
        if (!$schoolenterOne) {
            $schoolData = array();
            $schoolData['company_id'] = $paramArray['company_id'];
            $schoolData['client_id'] = $paramArray['client_id'];
            $schoolData['school_id'] = $paramArray['re_school_id'];
            $schoolData['is_schoolenter'] = 0;
            $schoolData['schoolenter_createtime'] = time();
            $schoolData['schoolenter_updatetime'] = time();
            $this->DataControl->insertData('crm_client_schoolenter', $schoolData);

            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$paramArray['client_id']}' and school_id = '{$paramArray['school_id']}'", $schoolenter);

            //$this->DataControl->delData('crm_client_schoolenter', "school_id='{$paramArray['school_id']}' and  client_id='{$paramArray['client_id']}'");
            $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id='{$paramArray['marketer_id']}'");
            $traData = array();
            $traData['client_id'] = $paramArray['client_id'];
            $traData['school_id'] = $paramArray['school_id'];
            $traData['marketer_id'] = $paramArray['marketer_id'];
            $traData['marketer_name'] = $markertOne['marketer_name'];
            if (empty($paramArray['note'])) {
                $paramArray['note'] = $this->LgStringSwitch("名单由{$schoolOne['school_cnname']}转到{$reSchoolOne['school_cnname']}");
            } else {
                $paramArray['note'] .= $this->LgStringSwitch(",名单由{$schoolOne['school_cnname']}转到{$reSchoolOne['school_cnname']}");
            }
            $traData['track_note'] = $paramArray['note'];
            $traData['object_code'] = "";
            $traData['track_state'] = "0";
            $traData['track_createtime'] = time();
            $traData['track_type'] = 0;
            $traData['track_initiative'] = 0;
            $traData['track_followmode'] = 5;
            $this->DataControl->insertData('crm_client_track', $traData);
            $clientData = array();
            $clientData['client_updatetime'] = time();
            $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);
            $this->error = 0;
            $this->errortip = "转校成功";
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "该学员已在目标学校";
            return false;
        }

    }
}
