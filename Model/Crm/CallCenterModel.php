<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class CallCenterModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $errortipaa = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $company_isassist = 0;
    public $company_istrackcoursetype = 0;
    public $company_ismusttrackcoursetype = 0;
    public $isGmcPost = null;

    public $m;
    public $allottip = "";
    public $allotnum = "";

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist,company_istrackcoursetype,company_ismusttrackcoursetype", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
            $this->company_istrackcoursetype = $companyOne['company_istrackcoursetype'];
            $this->company_ismusttrackcoursetype = $companyOne['company_ismusttrackcoursetype'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'], $publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->marketer_id = $publicarray['marketer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }else{
            return true;
        }
    }
    //支持多种传值方式
    function httpRequest($url, $data='', $method='GET',$header=''){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if($header!='') {//可以传递 RAW 方式下的 Josn 值
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }
        if($method=='POST')
        {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '')
            {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

    //慧捷 -- 获取平台后端接口访问token
    function getHjToken($request){
        $nowtime = time();
        $tokenOne = $this->DataControl->selectOne(" select * from crm_merchant_token where token_type = 1 and token_failuretime > '{$nowtime}'");
        if($tokenOne['token_string']){
            $nowtoken = $tokenOne['token_string'];
        }else {
            $param = array(
                'appId' => "00e79317-e617-4d5e-9c4c-1f4ce3b10462", //
                'appSecret' => "1EF4B181-1B99-4212-8408-DA5E0C1BEFF8", //
                'apiKey' => "7A5sSpQuvFuMdw3mmsKrb71IJjYGUqWR", //
            );
            $header = array();
            $header[] = "Accept: application/json";
            $header[] = "Content-type: application/json;charset='utf-8'";

            //POST参数 RAW中JSON方式传值获取结果
            $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/accesstoken/apply", json_encode($param), "POST", $header);
            $bakData = json_decode($getBackJson, true);

            //存表
            $record = array();
            $record['token_type'] = 1;
            $record['token_string'] = $bakData['access_token'];
            $record['token_failuretime'] = strtotime(date('Y-m-d', strtotime('+1 day')));
            $this->DataControl->insertData("crm_merchant_token",$record);

            $nowtoken = $bakData['access_token'];
        }
        return $nowtoken;
    }
    //慧捷双呼 -- 双呼接口
    function getHjTwoCall($request){
        //获取慧捷访问Token
        $tokenData = $this->getHjToken();

//        //呼叫者 （主叫）
//        $caller = $request['caller']?$request['caller']:'***********';
//        //被呼叫者  （被叫）
//        $callee = $request['callee']?$request['callee']:'***********';

        $param = [
            "deviceId" => "<EMAIL>", //发起外呼的分机号码（双呼模式下不需要注册分机，通话录音会关联此分机号）
            "prefix" => "9", //外呼前缀  固话前缀是8  手机号前缀是9
            "customerPhone" => "***********", //客户原始号码；***********建波的手机号码
            "options" => [
                "bindPhone" => "9***********",//固定前缀 9 ，后边改 主叫号码即可
            ]
        ];

        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/ext/v2/calls/uc", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);

        if($bakData['callId']){
            $record = array();
            $record['school_id'] = $request['school_id'];
            $record['client_id'] = $request['client_id'];
            $record['marketer_id'] = $request['marketer_id'];
            $record['callrecord_type'] = 1;
            $record['callrecord_callid'] = $bakData['callId'];
            $record['callrecord_createtime'] = time();
            $this->DataControl->insertData("crm_client_callrecord",$record);

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打行为记录！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "勋章列表获取失败！";
            return false;
        }
    }
    //慧捷事件订阅 -- 事件回调接口
    function callBackHjData($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            die;
        }
        $requestdata = json_decode($request, true);

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$requestdata['callId']}' and callbacklog_class = '1' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 1;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/CallCenter/callBackHjData";
        $list['callbacklog_class'] = 1;
        $list['callbacklog_parameter'] = $requestdata['callId'];
        $list['callbacklog_bakjson'] = $request;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            //修改表 crm_client_callrecord  是否接通
            $callid = $requestdata['callId'];
            $updata = array();
            $updata['callrecord_isconnect'] = 1;
            $updata['callrecord_updatetime'] = time();
            $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '1' ",$updata);
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;

        die;
        $backData = array();
        $backData[''] = $request['eventName'];//事件名称
        $backData[''] = $request['eventSrc'];//事件源
        $backData[''] = $request['timestamp'];//事件时间戳
        $backData[''] = $request['serial'];//事件序列号,值递增
        $backData[''] = $request['eventType'];//事件类型
        $backData[''] = $request['callId'];//呼叫CallId
        $backData[''] = $request['cid'];//CMS内部CallID
        $backData[''] = $request['ani'];//原始主叫号码
        $backData[''] = $request['dnis'];//原始被叫号码
        $backData[''] = $request['callType'];//Inbound  xxxxx
        $backData[''] = $request['deviceId'];//分机号码（参与呼叫的第一个分机）
        $backData[''] = $request['agentId'];//坐席工号 （参与呼叫的第一个分机签入的坐席）
        $backData[''] = $request['customerPhone'];//客户方号码
        $backData[''] = $request['startTime'];//呼叫开始时间戳
        $backData[''] = $request['answerTime'];//呼叫应答时间戳
        $backData[''] = $request['endTime'];//呼叫结束时间戳
        $backData[''] = $request['queueLength'];//呼叫排队时长,单位：秒
        $backData[''] = $request['talkLength'];//通话时长,单位：秒
        $backData[''] = $request['endReason'];//endReason xxxxxxx
        $backData[''] = $request['customerRingLength'];//客户方振铃时长,单位：秒
        $backData[''] = $request['deviceRingLength'];//坐席振铃时长,单位：秒
        $backData[''] = $request['displayNumber'];//外呼外显号码
        $backData[''] = $request['trunkName'];//客户方使用中继名称
        $backData[''] = $request['customerAreaCode'];//客户方所属行政区号
        $backData[''] = $request['customerCityCode'];//客户方所属市区号
        $backData[''] = $request['resultCode'];//int xxxxxxxx

    }
    //慧捷 -- 根据callId查询录音记录
    function getHjCallidRecording($request){
//        //callrecord_callid
//        $callrecordOne = $this->DataControl->selectOne(" select * from crm_client_callrecord where school_id = '{$request['school_id']}' and client_id = '{$request['client_id']}' ");
        $callidOne = "61cd0dc9-f388-42dc-9923-d14ce52fafe9";

        //获取慧捷访问Token
        $tokenData = $this->getHjToken();

        $header = array();
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/cdr/2.0/recordings/open/call/".$callidOne,'', "GET",$header);
        $bakData = json_decode($getBackJson, true);

        print_r($bakData);die;
        //json 返回的数据
//        [
//            {
//                "id": "1416746870004089451",
//                "callId": "61cd0dc9-f388-42dc-9923-d14ce52fafe9",
//                "deviceId": "<EMAIL>",
//                "timeIn": null,
//                "startTime": "2023-06-28 18:04:47",
//                "endTime": "2023-06-28 18:04:56",
//                "recordChannel": 1,
//                "recordLength": 9.093,
//                "recordType": "2",
//                "recordFormat": null,
//                "audioInMos": "4.5",
//                "filePath": "/mnt/volumes/recordings/jdb.cc/2023/0628/<EMAIL>/1416746870004089451_61cd0dc9-f388-42dc-9923-d14ce52fafe9.mp3",
//                "repoId": "default",
//                "failReason": "None",
//                "recordingRepoNfsPath": "**********:31080",
//                "recordingRepoRecordingPath": "/mnt/volumes/recordings/"
//            }
//        ]

        if($bakData['callId']){
            $result = array();
            $result['list'] = $bakData;

            $this->error = 0;
            $this->errortip = "拨打行为记录！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "勋章列表获取失败！";
            return false;
        }
    }
    //慧捷 -- 通过callId调听录音
    function getHjCallidRecordingFile($request){
        header("Content-Type:audio/x-wav");
//        //callrecord_callid
//        $callrecordOne = $this->DataControl->selectOne(" select * from crm_client_callrecord where school_id = '{$request['school_id']}' and client_id = '{$request['client_id']}' ");
        $callidOne = "61cd0dc9-f388-42dc-9923-d14ce52fafe9";

        //获取慧捷访问Token
        $tokenData = $this->getHjToken();

        $isDownload = 0;//下载或调听（1:下载；0:调听；2:重定向）
        $isWav = 0;//录音类型(1：wav双轨道，0：mp3单轨道)
        $header = array();
        $header[] = "Content-Type:audio/x-wav";
        $header[] = "wellcloud_token:".$tokenData;

        //get 传参
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/cdr/2.0/recordings/open/call/{$callidOne}/stream?isDownload={$isDownload}&isWav={$isWav}",'', "GET",$header);
        echo $getBackJson;
        die;
    }



    //合力 -- 获取平台后端接口访问token
    function getHliToken($request){
//        $nowtime = time();
//        $tokenOne = $this->DataControl->selectOne(" select * from crm_merchant_token where token_type = 2 and token_failuretime > '{$nowtime}'");
//        if($tokenOne['token_string']){
//            $nowtoken = $tokenOne['token_string'];
//        }else {
            $param = array(
                'account' => "N000000003929",//
                'appid' => "ws0vyro33lv50zfr",//
                'secret' => "a3c0dd00392d11ec8f25b1ddcafd1faf",//
            );
            $getBackJson = request_by_curl("https://a1.7x24cc.com/accessToken", dataEncode($param), "GET");
            $bakData = json_decode($getBackJson, true);

            //存表
            $record = array();
            $record['token_type'] = 2;
            $record['token_string'] = $bakData['accessToken'];
            $record['token_failuretime'] = time() + 7200;//合力的有效期为 2 小时
            $this->DataControl->insertData("crm_merchant_token",$record);

            $nowtoken = $bakData['accessToken'];
//        }
        return $nowtoken;
    }
    //合力 -- IVR
    function getHliTwoCall($request){
        //获取合力访问Token
        $tokenData = $this->getHliToken();

        //呼叫者 （主叫）
        $caller = $request['caller']?$request['caller']:'***********';
        //被呼叫者  （被叫）
        $callee = $request['callee']?$request['callee']:'***********';

        $param = array(
            "flag" => "107",//固定为107
            "accessToken" => $tokenData,//该参数值请使用获取accessToken接口获取
            "account" => "N000000003929",//您的账户编号为 : xxxxxxx
            "ServiceNo" => "80552",//外呼号码接通后 , 电话会转到这个服务号 ( 参数值见服务号与PBXID列表 )
            "phonenum" => $caller, //被呼叫用户的手机号码或固话号码
            "IvrVars"=>"num:{$callee}",//（REAL_FROM_CID:***********,）这个参数可以不用      吴光辉：***********    周建波：***********
            "OutBoundVars"=>"hlcallid:{$callee},hlbranch:xxxxxxxx",//您可以在我们提供的接口后面拼接上您自己的业务参数 , 我们通过通话记录回传给您
        );
        $getBackJson = request_by_curl("https://a1.7x24cc.com/commonInte", dataEncode($param), "GET");
        $bakData = json_decode($getBackJson, true);
        print_r($bakData);//   0    4

        die;

        if($bakData['callId']){
            $record = array();
            $record['school_id'] = $request['school_id'];
            $record['client_id'] = $request['client_id'];
            $record['marketer_id'] = $request['marketer_id'];
            $record['callrecord_type'] = 1;
            $record['callrecord_callid'] = $bakData['callId'];
            $record['callrecord_createtime'] = time();
            $this->DataControl->insertData("crm_client_callrecord",$record);

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打行为记录！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "勋章列表获取失败！";
            return false;
        }
    }

    //合力事件订阅 -- 事件回调接口 ---- 主叫号码没有data，被叫号码有 data 数据
    function callBackHliData($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            die;
        }
        $requestjson = json_encode($request, JSON_UNESCAPED_UNICODE);//转为 json

        $list = array();
        $list['test_mistxt'] = $requestjson;
        $list['test_time'] = time();
        $listOne = $this->DataControl->insertData("temp_test",$list);
        die;

        $requestdata = json_decode($request, true);

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$requestdata['callId']}' and callbacklog_class = '3' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 1;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/CallCenter/callBackHjData";
        $list['callbacklog_class'] = 2;
        $list['callbacklog_parameter'] = $requestdata['callId'];
        $list['callbacklog_bakjson'] = $request;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            //修改表 crm_client_callrecord  是否接通
            $callid = $requestdata['callId'];
            $updata = array();
            $updata['callrecord_isconnect'] = 1;
            $updata['callrecord_updatetime'] = time();
            $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '1' ",$updata);
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }
    //合力 -- 查询录音记录--- 没有完全走通
    function getHliCallidRecording($request){
//        //callrecord_callid
//        $callrecordOne = $this->DataControl->selectOne(" select * from crm_client_callrecord where school_id = '{$request['school_id']}' and client_id = '{$request['client_id']}' ");
//        $callidOne = "61cd0dc9-f388-42dc-9923-d14ce52fafe9";

        //获取合力访问Token
        $tokenData = $this->getHliToken();

        //主叫号码或者被叫号码
        $callNo = $request['callNo']?$request['callNo']:'***********';

        $param = array(
            "flag" => "1000",//固定为107
            "accessToken" => $tokenData,//该参数值请使用获取accessToken接口获取
            "account" => "N000000003929",//您的账户编号为 : xxxxxxx

//            "startTime" => date("Y-m-d 00:00:00",time()),//说明：通话的呼叫时间，格式：2019-10-22 00:00:00，默认为当天00:00:00，返回的数据呼叫时间大于或等于该值
//            "endTime" => date("Y-m-d 23:59:59",time()), //说明：通话的呼叫时间，格式：2019-10-22 23:59:59，默认为当天23:59:59，返回的数据呼叫时间小于或等于该值
//            "callType" => "normal", //说明：normal普通来电 , dialout外呼通话 , transfer转接电话 , dialtransfer外呼转接
            "callNo"=>$callNo,//说明：主叫号码或者被叫号码
            "limit"=>"100",//说明：每次查询返回的通话记录条数，默认为10，最大为1000；
        );
        $getBackJson = request_by_curl("https://a1.7x24cc.com/commonInte", dataEncode($param), "GET");
        $bakData = json_decode($getBackJson, true);
        print_r($bakData);//   0    4

        die;
        //json 返回的数据
//        [
//            {
//                "id": "1416746870004089451",
//                "callId": "61cd0dc9-f388-42dc-9923-d14ce52fafe9",
//                "deviceId": "<EMAIL>",
//                "timeIn": null,
//                "startTime": "2023-06-28 18:04:47",
//                "endTime": "2023-06-28 18:04:56",
//                "recordChannel": 1,
//                "recordLength": 9.093,
//                "recordType": "2",
//                "recordFormat": null,
//                "audioInMos": "4.5",
//                "filePath": "/mnt/volumes/recordings/jdb.cc/2023/0628/<EMAIL>/1416746870004089451_61cd0dc9-f388-42dc-9923-d14ce52fafe9.mp3",
//                "repoId": "default",
//                "failReason": "None",
//                "recordingRepoNfsPath": "**********:31080",
//                "recordingRepoRecordingPath": "/mnt/volumes/recordings/"
//            }
//        ]

        if($bakData['callId']){
            $result = array();
            $result['list'] = $bakData;

            $this->error = 0;
            $this->errortip = "拨打行为记录！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "勋章列表获取失败！";
            return false;
        }
    }



    //士决 双呼
    function getSjiTwoCall($request){
        //获取合力访问Token
        $tokenData = "fc41a859ca29a10fe8fa90dc7d2053f8:ccf79fc65dbd93e3f809f313860e1a57";

        $param = [
            'caller' => '***********', //'***********', 主叫
            'callee' => '***********', //'***********', 被叫
            'pstnnumber' => '***********', //企业大号 PSTN  有多个
        ];echo json_encode($param);die;
        $header = array();
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "authorization:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://call.kidcastle.com.cn/uas/********/accounts/calls/twowaycallbackWithIpAuth.json", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);
print_r($getBackJson);die;
        die;

        if($bakData['callId']){
            $record = array();
            $record['school_id'] = $request['school_id'];
            $record['client_id'] = $request['client_id'];
            $record['marketer_id'] = $request['marketer_id'];
            $record['callrecord_type'] = 1;
            $record['callrecord_callid'] = $bakData['callId'];
            $record['callrecord_createtime'] = time();
            $this->DataControl->insertData("crm_client_callrecord",$record);

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打行为记录！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "勋章列表获取失败！";
            return false;
        }
    }

}
