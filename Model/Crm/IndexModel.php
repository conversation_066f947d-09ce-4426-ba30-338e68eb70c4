<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class IndexModel extends modelTpl
{
    public $m;
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    //首页
    function recruitStudent($paramArray)
    {
        $companyOne = $this->DataControl->selectOne("select company_id,company_isnointention from gmc_company where company_id = '{$paramArray['company_id']}' ");

        $datawhere = "b.school_id = '{$paramArray['school_id']}' and a.company_id = '{$paramArray['company_id']}'";
        $invite_where = " i.client_id = a.client_id and i.school_id = b.school_id and i.invite_isvisit = '1' ";
        $audition_where = " i.client_id = a.client_id and i.school_id = b.school_id and i.audition_isvisit = '1' ";
        $con_where = " i.client_id = a.client_id and i.school_id = b.school_id and i.student_branch <> '' ";

        $invite_two_where = " 1 ";
        $audition_two_where = " 1 ";
        $invalid_two_where = " 1 ";
        $con_two_where = " 1 ";

        //来源人 筛选
        if (isset($paramArray['from_marketer_id']) && $paramArray['from_marketer_id'] != '') {
            $datawhere .= " and c.marketer_id = '{$paramArray['from_marketer_id']}' and c.principal_leave = '0'";

            $invite_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";
            $audition_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";
            $con_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";

            $invite_two_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";
            $audition_two_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";
            $invalid_two_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";
            $con_two_where .= " and i.marketer_id = '{$paramArray['from_marketer_id']}' ";
        }
        //开始结束时间
        if (isset($paramArray['client_starttime']) && $paramArray['client_starttime'] != '') {
            $stime = strtotime($paramArray['client_starttime']);
            $sdatetime = date("Y-m-d",$stime);

            $datawhere .= " and a.client_createtime >= '{$stime}'";

            $invite_where .= " and i.invite_createtime >= '{$stime}'";
            $audition_where .= " and i.audition_createtime >= '{$stime}'";
            $con_where .= " and i.conversionlog_time >= '{$stime}'";

            $invite_two_where .= " and i.invite_visittime >= '{$sdatetime}'";
            $audition_two_where .= " and i.audition_visittime >= '{$sdatetime}'";
            $invalid_two_where .= " and i.track_createtime >= '{$stime}'";
            $con_two_where .= " and i.conversionlog_time >= '{$stime}'";
        }
        if (isset($paramArray['client_endtime']) && $paramArray['client_endtime'] != '') {
            $etime = strtotime($paramArray['client_endtime'])+86399;
            $e1datetime = date("Y-m-d 23:59:59",$etime);
            $e2datetime = date("Y-m-d",$etime);

            $datawhere .= " and a.client_createtime <= '{$etime}'";

            $invite_where .= " and i.invite_createtime <= '{$etime}'";
            $audition_where .= " and i.audition_createtime <= '{$etime}'";
            $con_where .= " and i.conversionlog_time <= '{$etime}'";

            $invite_two_where .= " and i.invite_visittime <= '{$e1datetime}'";
            $audition_two_where .= " and i.audition_visittime <= '{$e1datetime}'";
            $invalid_two_where .= " and i.track_createtime <= '{$etime}'";
            $con_two_where .= " and i.conversionlog_time <= '{$etime}'";
        }

        $sql = "SELECT a.company_id,a.client_id,a.client_distributionstatus,a.client_tracestatus,a.client_ischaserlapsed,a.client_isgross,a.client_schtmkdistributionstatus
                ,c.principal_leave 
                ,ifnull((select 1 from crm_client_invite as i where {$invite_where} limit 0,1 ),0) as isinvite
                ,ifnull((select 1 from crm_client_audition as i where {$audition_where} limit 0,1),0) as isaudition
                ,ifnull((select 1 from crm_client_conversionlog as i where {$con_where} limit 0,1),0) as isconversion 
                FROM crm_client as a
                LEFT JOIN crm_client_schoolenter as b ON a.client_id = b.client_id and a.company_id = b.company_id and b.is_enterstatus = '1'
                left join crm_client_principal as c ON a.client_id = c.client_id and c.school_id = b.school_id and c.principal_leave = '0'
                WHERE {$datawhere} 
                GROUP BY a.client_id";
        $clientList = $this->DataControl->selectClear($sql);
        //数据
        if (is_array($clientList)) {
            $addGrossClient = 0;//新增名单  毛名单+待分配  --- 毛名单
//            $allocatedClient = 0;//待分配
//            $addClient = 0;//有效名单（待分配、柜询、试听、转正、无意向）  --- 有效名单
            $intentionClient = 0;//意向
            $inviteClient = 0;//柜询
            $auditionClient = 0;//试听
            $loseClient = 0;//无意向
            $invalidClient = 0;//无效
            $officialClient = 0;//转正  --- 转正客户（操作时间）
//            $aa = '';
            foreach ($clientList as $clientVar) {
                //增加毛名单
                ++$addGrossClient;
//                //待分配列表 (包含有效 和 毛名单)
//                if ($clientVar['client_distributionstatus'] == '0' && $clientVar['client_tracestatus'] >= 0 && $clientVar['client_tracestatus'] <= 3
//                    && ( ($clientVar['client_schtmkdistributionstatus'] == '0' && $clientVar['client_isgross'] == '1')
//                        || $clientVar['client_isgross'] == '0'
//                    )) {
//                    ++$allocatedClient;
//                }
//                //有效名单（待分配、柜询、试听、转正、无意向）
//                if ($clientVar['client_tracestatus'] <> '-2' && $clientVar['client_isgross'] <> '1') {
//                    ++$addClient;
//                }
                //意向客户
                if($clientVar['client_isgross'] == '0' and $clientVar['client_tracestatus'] > '-1' and $clientVar['client_tracestatus'] != '4' and $clientVar['principal_leave'] == '0'){
                    ++$intentionClient;
//                    $aa .= ','.$clientVar['client_id'];
                }
                //柜询
                if ($clientVar['isinvite'] == '1') {
                    ++$inviteClient;
                }
                //试听
                if ($clientVar['isaudition'] == '1') {
                    ++$auditionClient;
                }
                //无意向
                if ($clientVar['client_tracestatus'] == '-1') {
                    ++$loseClient;
                }
                //无效
                if ($clientVar['client_tracestatus'] == '-2') {
                    ++$invalidClient;
                }
                //转正（操作时间）
                if ($clientVar['isconversion'] == '1') {
                    ++$officialClient;
                }
            }
//            echo $aa;die;
        }
        //招生统计--新增邀约客户（在所选时间范围内，按照邀约时间，邀约客户来访的人数（包含柜询试听）） -- 不用看到访
        $inviteArray = $this->DataControl->selectOne("select count(distinct i.client_id) as allnum from crm_client_invite as i where i.school_id = '{$paramArray['school_id']}' and {$invite_two_where} ");
        $auditionArray = $this->DataControl->selectOne("select count(distinct i.client_id) as allnum from crm_client_audition as i where i.school_id = '{$paramArray['school_id']}' and {$audition_two_where} ");

        //新增无效客户（在所选时间范围内，按照无效时间，操作无效的客户（包括待审核）且当前无效）
        $invalidClientTwo = $this->DataControl->selectClear("
                select a.client_id
                FROM crm_client as a
                LEFT JOIN crm_client_schoolenter as b ON a.client_id = b.client_id and a.company_id = b.company_id and b.is_enterstatus = '1' 
                WHERE a.company_id = '{$paramArray['company_id']}' and a.client_tracestatus = '-2' and b.school_id = '{$paramArray['school_id']}'
                ");
        if($invalidClientTwo){
            $invalidClientStr = implode(',',array_column($invalidClientTwo,'client_id'));
            $invalidArray = $this->DataControl->selectOne("select count(distinct i.client_id) as allnum from crm_client_track as i where i.school_id = '{$paramArray['school_id']}' and {$invalid_two_where} and i.client_id in ($invalidClientStr) ");
        }else{
            $invalidArray['allnum'] = 0;
        }
        //新增正式学员（在所选时间范围内，按照转正时间，操作转正的正式学员）
        $conArray = $this->DataControl->selectOne("select count(distinct i.client_id) as allnum from crm_client_conversionlog as i where i.school_id = '{$paramArray['school_id']}' and {$con_two_where} ");


        $dataArray = array();
        $dataArray["yaoyueClient"] = $inviteArray['allnum'] + $auditionArray['allnum'] + 0;//招生统计-新增邀约客户（在所选时间范围内，按照邀约时间，邀约客户来访的人数（包含柜询试听））
        $dataArray["wuxiaoClient"] = $invalidArray['allnum'] + 0;//招生统计-新增无效客户（在所选时间范围内，按照无效时间，操作无效的客户（包括待审核）且当前无效）
        $dataArray["zhengshiClient"] = $conArray['allnum'] + 0;//招生统计-新增正式学员（在所选时间范围内，按照转正时间，操作转正的正式学员）

        $dataArray["addGrossClient"] = $addGrossClient + 0;//招生统计--新增毛名单 同 （招生转化统计--毛名单）
        $dataArray["intentionClient"] = $intentionClient + 0;//招生转化统计 -- 意向客户
        $dataArray["allInviteClient"] = $inviteClient + $auditionClient + 0;//招生转化统计 -- 柜询客户（柜询+试听）
        $dataArray["auditionClient"] = $auditionClient + 0;//招生转化统计 -- 试听客户
        $dataArray["officialClient"] = $officialClient + 0;//招生转化统计 -- 转正客户

        $dataArray["invalidClient"] = $invalidClient + 0;//无效 -- 转化率用到了
        $dataArray["loseClient"] = $loseClient + 0;//无意向
//        $dataArray["allocatedClient"] = $allocatedClient + 0;//待分配
//        $dataArray["addClient"] = $addClient + 0;//有效名单
//        $dataArray["inviteClient"] = $inviteClient + 0;//柜询
        //转化率
        if (($addGrossClient + 0) > 0) {
            $dataArray["maoToYiRate"] = sprintf("%.2f", ($intentionClient + 0) / ($addGrossClient + 0)) * 100;//● 毛名单转化为意向客户数量/转化率：80/100（80%）
            $dataArray["yiToShiRate"] = sprintf("%.2f", ($auditionClient + 0) / ($intentionClient + 0)) * 100;//● 意向客户转化为试听客户数量/转化率：80/100（80%）
            $dataArray["yiToZhengRate"] = sprintf("%.2f", ($officialClient + 0) / ($intentionClient + 0)) * 100;//● 意向客户转化为正式客户数量/转化率：80/100（80%）
            $dataArray["maoToWuRate"] = sprintf("%.2f", ($invalidClient + 0) / ($addGrossClient + 0)) * 100;//● 毛名单转化为无效客户数量/转化率：80/100（80%）
        } else {
            $dataArray["maoToYiRate"] = 0;
            $dataArray["yiToShiRate"] = 0;
            $dataArray["yiToZhengRate"] = 0;
            $dataArray["maoToWuRate"] = 0;
        }

        //前端显示的格式
        $dataArray["maoToYiRate"] = ($intentionClient + 0)."/".($addGrossClient + 0)."(".$dataArray["maoToYiRate"]."%)";
        $dataArray["yiToShiRate"] = ($auditionClient + 0)."/".($intentionClient + 0)."(".$dataArray["yiToShiRate"]."%)";
        $dataArray["yiToZhengRate"] = ($officialClient + 0)."/".($intentionClient + 0)."(".$dataArray["yiToZhengRate"]."%)";
        $dataArray["maoToWuRate"] = ($invalidClient + 0)."/".($addGrossClient + 0)."(".$dataArray["maoToWuRate"]."%)";

        $dataArray['companyOne'] = $companyOne;
        return $dataArray;
    }


    function ClinetCount($paramArray)
    {

        $markerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$paramArray['staffer_id']}'");

        $intentionwhere = " 1 and s.school_id = '{$paramArray['school_id']}' and c.company_id = '{$paramArray['company_id']}' and p.marketer_id = '{$markerOne['marketer_id']}'  and (p.principal_leave=0 or  c.client_tracestatus =4  )   ";

        if (isset($paramArray['client_starttime']) && $paramArray['client_starttime'] != '') {
            $paramArray['client_starttime'] = date('Y-m-d', strtotime($paramArray['client_starttime']));
            $intentionwhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['client_starttime']}'";
        }
        if (isset($paramArray['client_endtime']) && $paramArray['client_endtime'] != '') {
            $paramArray['client_endtime'] = date('Y-m-d', strtotime($paramArray['client_endtime']));
            $intentionwhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['client_endtime']}'";
        }
        $sql = "	select c.client_id,c.client_tracestatus,c.client_distributionstatus
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id
				where {$intentionwhere}  Group by c.client_id";
        $intentionClint = $this->DataControl->selectClear($sql);

        $intentionAll = $this->DataControl->selectOne("
 				select count(c.client_id) as client_allnum
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id
				where s.school_id = '{$paramArray['school_id']}' and c.company_id = '{$paramArray['company_id']}' and p.marketer_id = '{$markerOne['marketer_id']}'  and (p.principal_leave=0 or  c.client_tracestatus =4  )
				");

        $intentionAdd = $this->DataControl->selectOne("
 				select count(c.client_id) as client_addnum
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id
				where s.school_id = '{$paramArray['school_id']}' and c.company_id = '{$paramArray['company_id']}' and p.marketer_id = '{$markerOne['marketer_id']}' and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['client_starttime']}' and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['client_endtime']}' and (p.principal_leave=0 or  c.client_tracestatus =4  )
				");
        if (!$intentionAll) {
            $intentionAll = array();
        }

        $track_nonum = 0;
        $track_alnum = 0;
        $track_invitenum = 0;
        $track_autionnum = 0;
        $track_conversionnum = 0;
        if ($intentionClint) {
            foreach ($intentionClint as $key => $value) {
                if ($value['client_tracestatus'] == 0) {
                    //待跟进
                    ++$track_nonum;
                }
                if ($value['client_tracestatus'] == 1) {
                    //持续跟踪
                    ++$track_alnum;
                }
                if ($value['client_tracestatus'] == 2) {
                    //邀约
                    ++$track_invitenum;
                }
                if ($value['client_tracestatus'] == 3) {
                    //试听
                    ++$track_autionnum;
                }
                if ($value['client_tracestatus'] == 4) {
                    //试听
                    ++$track_conversionnum;
                }
            }
        }
        $data = array();
        $data['client_allnum'] = $intentionAll['client_allnum'] + 0;
        $data['client_addnum'] = $intentionAdd['client_addnum'] + 0;
        $data['track_nonum'] = $track_nonum + 0;
        $data['track_alnum'] = $track_alnum + $track_invitenum + $track_autionnum;
        $data['track_invitenum'] = $track_invitenum + $track_autionnum + 0;
        $data['track_conversionnum'] = $track_conversionnum + 0;
        return $data;
    }

    //首页 -- 跟踪提醒
    function remindApi($paramArray)
    {
        $stu_datawhere = $datawhere = " 1 and r.marketer_id = '{$paramArray['marketer_id']}' ";

        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%')";
            $stu_datawhere .= " and (c.student_cnname like '%{$paramArray['keyword']}%' or c.student_enname like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['remind_starttime']) && $paramArray['remind_starttime'] != '') {
            $datawhere .= " and r.remind_time >= '{$paramArray['remind_starttime']}'";
            $stu_datawhere .= " and r.remind_time >= '{$paramArray['remind_starttime']}'";
        }
        if (isset($paramArray['remind_endtime']) && $paramArray['remind_endtime'] != '') {
            $datawhere .= " and r.remind_time <= '{$paramArray['remind_endtime']}'";
            $stu_datawhere .= " and r.remind_time <= '{$paramArray['remind_endtime']}'";
        }
        if (isset($paramArray['remind_isread']) && $paramArray['remind_isread'] != '') {
            $datawhere .= " and r.remind_isread = '{$paramArray['remind_isread']}'";
            $stu_datawhere .= " and r.remind_isread = '{$paramArray['remind_isread']}'";
        }
        $having = '1=1';
        if (isset($paramArray['track_followuptime']) && $paramArray['track_followuptime'] != '') {
            $having .= " and nextTrackTime = '{$paramArray['track_followuptime']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  r.remind_id,
  (select t.track_followuptime from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as nextTrackTime
                FROM crm_remind as r 
                LEFT JOIN crm_client as c ON r.client_id = c.client_id 
                WHERE {$datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND r.school_id = '{$paramArray['school_id']}'  AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4 
                GROUP BY c.client_id
                Having {$having} 
                union 
                select r.remind_id,(select t.track_followuptime from crm_student_track as t where t.student_id = r.student_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as nextTrackTime
                from crm_student_remind as r
                left join smc_student as c ON c.student_id=r.student_id
                left join crm_student as t ON t.student_id=c.student_id
                left join smc_student_family as f ON f.student_id=c.student_id
                left join smc_parenter as p ON p.parenter_id=f.parenter_id
                 WHERE {$stu_datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND r.school_id = '{$paramArray['school_id']}' AND t.student_tracestatus <> -2 AND t.student_tracestatus <> -1 AND t.student_tracestatus <> 4 
                  Having {$having} 
                ";
            $count = $this->DataControl->selectClear($sql);

            if (is_array($count)) {
                $count = count($count) + 0;
            } else {
                $count = 0;
            }
        } else {
            $count = '';
        }

        //线索 统计
        $sql = "SELECT r.remind_id,r.remind_time,r.remind_remark,c.client_id,c.client_cnname,c.client_enname,c.client_sex,c.client_birthday,c.client_mobile,c.client_tracestatus,c.client_isnewtip,c.client_intention_level,c.client_img,r.remind_isread,'crm' as remind_type,
                (select count(t.track_id) from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id and t.track_validinc = '1' ) as trackNum,
                (select t.track_createtime from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as lastTrackTime,
                (select t.track_followuptime from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as nextTrackTime,
                (select count(t.track_id) from crm_client_track as t where t.client_id = r.client_id and t.marketer_id =r.marketer_id and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= r.remind_time ) as todayTrackNum,'' as stuenrolledtype 
                FROM crm_remind as r 
                LEFT JOIN crm_client as c ON r.client_id = c.client_id 
                WHERE {$datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND r.school_id = '{$paramArray['school_id']}' AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4 
                GROUP BY c.client_id
                  Having {$having}
                union 
                select r.remind_id,r.remind_time,r.remind_remark,c.student_id as client_id,c.student_cnname as client_cnname,c.student_enname as client_enname,c.student_sex as client_sex,
                    c.student_birthday as client_birthday,p.parenter_mobile as client_mobile,t.student_tracestatus as client_tracestatus, '0' as client_isnewtip,t.student_intention_level as client_intention_level,c.student_img as client_img,r.remind_isread,'stu'as remind_type,
                  (select count(t.track_id) from crm_student_track as t where t.student_id = r.student_id and t.marketer_id = r.marketer_id and t.track_validinc = '1' ) as trackNum,
                (select t.track_createtime from crm_student_track as t where t.student_id = r.student_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as lastTrackTime,
                (select t.track_followuptime from crm_student_track as t where t.student_id = r.student_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as nextTrackTime,
                (select count(t.track_id) from crm_student_track as t where t.student_id = r.student_id and t.marketer_id =r.marketer_id and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= r.remind_time ) as todayTrackNum,
                (select e.enrolled_status from smc_student_enrolled as e where e.student_id = c.student_id and e.school_id=r.school_id) as stuenrolledtype 
                from crm_student_remind as r
                left join smc_student as c ON c.student_id=r.student_id
                left join crm_student as t ON t.student_id=c.student_id
                left join smc_student_family as f ON f.student_id=c.student_id
                left join smc_parenter as p ON p.parenter_id=f.parenter_id
                 WHERE {$stu_datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND r.school_id = '{$paramArray['school_id']}' AND t.student_tracestatus <> -2 AND t.student_tracestatus <> -1 AND t.student_tracestatus <> 4 
                  Having {$having}
                  order by remind_time DESC,nextTrackTime DESC 
                limit {$pagestart},{$num}";

        //and r.remind_time >= current_date
        $dataList = $this->DataControl->selectClear($sql);
        //数据
        if (is_array($dataList)) {
            foreach ($dataList as &$dataVar) {
                if ($dataVar['todayTrackNum'] > 0) {
                    $dataVar['isTrack'] = '1';
                    $dataVar['isTracknum'] = $this->LgStringSwitch('已跟踪');
                } else {
                    $dataVar['isTrack'] = '0';
                    $dataVar['isTracknum'] = $this->LgStringSwitch('未跟踪');
                }

                if($dataVar['remind_type'] == 'crm'){
                    $dataVar['stuisloss'] = '';
                }elseif($dataVar['remind_type'] == 'stu') {
                    if ($dataVar['stuenrolledtype'] == '-1') {
                        $dataVar['stuisloss'] = '1';
                    } else {
                        $dataVar['stuisloss'] = '0';
                    }
                }

                if ($dataVar['client_tracestatus'] == '0') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('待跟踪');
                } elseif ($dataVar['client_tracestatus'] == '1') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('持续跟踪');
                } elseif ($dataVar['client_tracestatus'] == '2') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('已柜询');
                } elseif ($dataVar['client_tracestatus'] == '3') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('已试听');
                } elseif ($dataVar['client_tracestatus'] == '4') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('已转正');
                } elseif ($dataVar['client_tracestatus'] == '-1') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('无意向');
                }
                if ($dataVar['lastTrackTime'] != '' && $dataVar['lastTrackTime'] != '0') {
                    $dataVar['lastTrackTime'] = date('Y-m-d', $dataVar['lastTrackTime']);
                } else {
                    $dataVar['lastTrackTime'] = $this->LgStringSwitch('无记录');
                }
                $dataVar['nextTrackTime'] = ($dataVar['nextTrackTime'] == '') ? null : $dataVar['nextTrackTime'];
                $dataVar['client_age'] = birthdaytoage($dataVar['client_birthday']);
            }
        }
        $result = array();
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    //首页 -- 新增日程安排
    function addEventAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['marketer_id'] = $paramArray['marketer_id'];
        $data['event_tag'] = $paramArray['event_tag'];
        $data['event_time'] = $paramArray['event_time'];
        $data['event_remark'] = $paramArray['event_remark'];
        $data['event_createtime'] = time();

        if ($this->DataControl->insertData('crm_event', $data)) {
            return true;
        } else {
            return false;
        }
    }

    //首页 -- 某日的日程安排
    function eventOneApi($paramArray)
    {
        $datawhere = "e.school_id='{$paramArray['school_id']}' and e.event_time = '{$paramArray['event_time']}' and e.marketer_id = '{$paramArray['marketer_id']}' ";
        $dataList = $this->DataControl->selectClear("SELECT e.event_tag,e.event_remark,e.client_id FROM crm_event as e WHERE {$datawhere}");
        return $dataList;
    }

    //首页 -- 月份列表日程展示
    function monthEventApi($paramArray)
    {
        $date = getthemonth($paramArray['yearMonth']);
        //当前日期
        $sdefaultDate = date("Y-m-d");

        $sql = "select e.event_id,e.event_time from crm_event AS e where
e.marketer_id='{$paramArray['marketer_id']}' and e.event_time between '{$date[0]}' and '{$date[1]}' and e.school_id='{$paramArray['school_id']}' GROUP BY e.event_time";
        $mothListArray = $this->DataControl->selectClear($sql);

        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['event_time']));
                $v['month'] = date('m', strtotime($v['event_time']));
                $v['day'] = date('d', strtotime($v['event_time']));
                $temp = $v['year'] . "-" . $v['month'] . "-" . $v['day'];

                $v['week'] = date('w', strtotime($temp));
                unset($mothListArray[$k]['event_time']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = $i;
                    $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                    if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                        $dateWeek[$i]['isnow'] = 1;
                    } else {
                        $dateWeek[$i]['isnow'] = 0;
                    }

                    $data['is_have'] = strval(-1);

                    array_push($mothListArray, $data);
                }

                usort($mothListArray, function ($a, $b) {
                    if ($a['day'] == $b['day']) return 0;
                    return $a['day'] > $b['day'] ? 1 : -1;
                });
            }
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = $i;
                $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                    $dateWeek[$i]['isnow'] = 1;
                } else {
                    $dateWeek[$i]['isnow'] = 0;
                }

                $data['is_have'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first = 0;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w = date('w', strtotime($sdefaultDate));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start = date('Y-m-d', strtotime("$sdefaultDate -" . ($w ? $w - $first : 6) . ' days'));
        //本周结束日期
        $week_end = date('Y-m-d', strtotime("$week_start +6 days"));
        //echo "$week_start"."$week_end";
        //组合数据
        $dateWeek = [];
        for ($i = 0; $i <= 6; $i++) {
            $dateWeek[$i]['yearMonth'] = date('Y-m', strtotime("$week_start + $i days"));
            $dateWeek[$i]['daytime'] = date('Y-m-d', strtotime("$week_start + $i days"));
            $dateWeek[$i]['day'] = date('d', strtotime("$week_start + $i days"));
            $dateWeek[$i]['week'] = date('w', strtotime("$week_start + $i days"));
            if ($dateWeek[$i]['daytime'] == $sdefaultDate) {
                $dateWeek[$i]['isnow'] = 1;
            } else {
                $dateWeek[$i]['isnow'] = 0;
            }
        }
        $data = array();
        $data['week'] = $dateWeek;
        $data['mothList'] = $mothListArray;
        return $data;
    }

    //首页 -- 校园教师
    function schoolMarketerApi($paramArray)
    {
        $datawhere = " 1   ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and m.marketer_name like '%{$paramArray['keyword']}%' ";
        }

        $sql = "SELECT m.marketer_id,m.marketer_name,s.staffer_enname
                FROM crm_marketer as m
                LEFT JOIN smc_staffer as s ON m.staffer_id=s.staffer_id 
                LEFT JOIN gmc_staffer_postbe as p ON m.staffer_id=p.staffer_id 
                WHERE {$datawhere} and s.account_class <> '1' and s.company_id='{$paramArray['company_id']}' and p.school_id='{$paramArray['school_id']}'  GROUP BY m.marketer_id ";
        $marketerList = $this->DataControl->selectClear($sql);
        return $marketerList;
    }

    //单校教师业绩统计
    function getSchMarketerEnrollApi($paramArray){

        $datawhere = " b.school_id = '{$paramArray['school_id']}' and b.company_id = '{$paramArray['company_id']}' and b.postbe_status = '1' and b.postbe_iscrmuser = '1' and b.postbe_crmuserlevel <> '2' and m.marketer_id > 0 ";

        $infowhere = '';
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = strtotime($paramArray['start_time']);
            $infowhere .= " and r.pay_successtime > '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = strtotime($paramArray['end_time'])+86400;
            $infowhere .= " and r.pay_successtime <= '{$end_time}'";
        }
        $coursetypewhere = '';
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $coursetypewhere .= " and u.coursetype_id = '{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
//        $sql = "SELECT m.marketer_id,m.marketer_name,f.staffer_cnname,f.staffer_enname,f.staffer_branch,
//(SELECT COUNT(DISTINCT(c.client_id))
//FROM crm_client as c,smc_student as s,smc_student_registerinfo as r
//WHERE p.client_id = c.client_id and c.client_id = s.from_client_id and s.student_id = r.student_id and p.school_id = r.school_id
//and r.info_status = '1' {$infowhere}) as clientnum
//        FROM crm_client_principal as p,crm_marketer as m,smc_staffer as f
//        WHERE {$datawhere}
//        GROUP BY p.marketer_id
//        LIMIT {$pagestart},{$num}";

        $sql = "SELECT m.marketer_id,m.marketer_name,f.staffer_cnname,f.staffer_enname,f.staffer_branch,
            (SELECT COUNT(DISTINCT(c.client_id)) 
            FROM crm_client_principal as pp,crm_client as c,smc_student as s,smc_student_registerinfo as r 
            WHERE b.school_id = '{$paramArray['school_id']}' and pp.school_id = '{$paramArray['school_id']}' and pp.marketer_id = m.marketer_id 
            and pp.principal_ismajor = '1' and pp.principal_leave = '0' and pp.client_id = c.client_id and c.client_id = s.from_client_id 
            and s.student_id = r.student_id and pp.school_id = r.school_id  
            and r.info_status = '1' {$infowhere}) as clientnum
                FROM gmc_staffer_postbe as b
                LEFT JOIN smc_staffer as f ON b.staffer_id = f.staffer_id 
                LEFT JOIN crm_marketer as m ON f.staffer_id = m.staffer_id 
                WHERE {$datawhere} GROUP BY m.marketer_id  
                ORDER BY clientnum DESC 
                LIMIT {$pagestart},{$num}";
        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            foreach ($clientList as &$value) {
                $value['marketer_name'] = $value['staffer_enname'] ? $value['marketer_name'].'-'.$value['staffer_enname'] : $value['marketer_name'];

                //班组 的报名数
                $coursesql = "SELECT u.coursetype_cnname,u.coursetype_branch,CONCAT('Z',u.coursetype_branch) as coursetype_branchname,
(SELECT COUNT(DISTINCT(c.client_id)) 
FROM crm_client_principal as p,crm_client as c,smc_student as s,smc_student_registerinfo as r 
WHERE p.school_id = '{$paramArray['school_id']}' and p.marketer_id = '{$value['marketer_id']}' and p.principal_ismajor = '1' and p.principal_leave = '0' 
and p.client_id = c.client_id and c.client_id = s.from_client_id and s.student_id = r.student_id and p.school_id = r.school_id  
and r.info_status = '1' and u.coursetype_id = r.coursetype_id {$infowhere} {$coursetypewhere}) as clientnums 
        from smc_code_coursetype as u  
        WHERE u.company_id = '{$paramArray['company_id']}' and u.coursetype_isrecruit = '1' {$coursetypewhere} order by coursetype_id asc  ";
                $courseList = $this->DataControl->selectClear($coursesql);

                $coursetype = array_column($courseList, 'coursetype_branchname');
                $clientnums = array_column($courseList, 'clientnums');
                $courseclientnum = array_combine($coursetype,$clientnums);
                $value = array_merge($value,$courseclientnum);
            }
        }
//print_r($clientList);die;
        $allNum = $this->DataControl->selectClear("SELECT m.marketer_id FROM gmc_staffer_postbe as b
                LEFT JOIN smc_staffer as f ON b.staffer_id = f.staffer_id 
                LEFT JOIN crm_marketer as m ON f.staffer_id = m.staffer_id 
                LEFT JOIN crm_client_principal as p ON m.marketer_id = p.marketer_id 
                WHERE {$datawhere} GROUP BY m.marketer_id");
        $allnums = is_array($allNum)?count($allNum):0;

        //班组的 field
        $coursetypeList = $this->DataControl->selectClear("SELECT u.coursetype_cnname,u.coursetype_branch,CONCAT('Z',u.coursetype_branch) as coursetype_branchname from smc_code_coursetype as u WHERE u.company_id = '{$paramArray['company_id']}' and u.coursetype_isrecruit = '1' {$coursetypewhere} order by coursetype_id asc");
        $typefield = array();
        if($coursetypeList){
            foreach ($coursetypeList as $key=>$coursetypeListvar){
                $typefield[$key]['fieldname'] = $coursetypeListvar['coursetype_branchname'];
                $typefield[$key]['fieldstring'] = $coursetypeListvar['coursetype_cnname'];
                $typefield[$key]['custom'] = 1;
                $typefield[$key]['show'] = 1;
            }
        }
//print_r($typefield);die;
        $result = array();
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;
        $result['typefield'] = $typefield;
        $this->error = 0;
        if($clientList) {
            $this->errortip = "数据获取成功";
        }else{
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }

    public function __call($method, $args)
    {

    }
}
