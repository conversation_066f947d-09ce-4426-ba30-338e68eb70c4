<?php
/**
 * 跨界活动Token
 */

namespace Model\Crm;
require(ROOT_PATH . 'Core/Tools/JwtToken/src/JWT.php');
use \Firebase\JWT\JWT;

class TransboundaryTokenModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->publicarray = $publicarray;
        }

    }

    /**
     * 创建Token
     * @param $paramArray
     * @return bool
     */
    function lssue($paramArray)
    {
        if (empty($paramArray['user_id']) && empty($paramArray['business_id'])) {
            ajax_return(array('error' => 1, 'errortip'=>'未获取到用户信息!','result' =>array()));
        }

        //用户
        if ($paramArray['end']=='user') {
            $user_id = $paramArray['user_id'];
            $token = [
                'iss' => 'http://www.mohism.cn', //签发者 可选
                'aud' => ['http://www.mohism.cn'], //接收该JWT的一方，可选
                'iat' => time(), //签发时间
                'nbf' => time() , //(Not Before)：某个时间点后才能访问，比如设置time+30，表示当前时间30秒后才能使用
                'exp' => time()+31536000, //过期时间(秒)
                'data' => [ //自定义信息，不要定义敏感信息
                    'user_id' => $user_id
                ]
            ];
        } elseif ($paramArray['end']=='business') {
            //商户
            $business_id = $paramArray['business_id'];
            $token = [
                'iss' => 'http://www.mohism.cn', //签发者 可选
                'aud' => ['http://www.mohism.cn'], //接收该JWT的一方，可选
                'iat' => time(), //签发时间
                'nbf' => time() , //(Not Before)：某个时间点后才能访问，比如设置time+30，表示当前时间30秒后才能使用
                'exp' => time()+31536000, //过期时间(秒)
                'data' => [ //自定义信息，不要定义敏感信息
                    'business_id' => $business_id
                ]
            ];
        } else {
            ajax_return(array('error' => 1, 'errortip'=>'end参数错误!','result' =>array()));
        }
        $key = 'mohism-2021-crm-'.$paramArray['end']; //key
        $JWT = new JWT();
        $data = $JWT ->encode($token, $key);

        $result = array();
        $result['token'] = $data;
        $result['info'] = $paramArray;
        if($data)
        {
            $this->error = 0;
            $this->errortip = "秘钥获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "秘钥获取失败";
            $this->result = array();
            return false;
        }

    }

    /**
     * 校验Token
     * @param $paramArray token
     * @param $paramArray end
     * @return bool
     */
    function verification($paramArray)
    {

        $jwt = $paramArray['token'];
        $key = 'mohism-2021-crm-'.$paramArray['end']; //key //key要和签发的时候一样
        try {
            JWT::$leeway = 60;//当前时间减去60，把时间留点余地
            $decoded = JWT::decode($jwt, $key, ['HS256']); //HS256方式，这里要和签发的时候对应
            $data = json_decode(json_encode($decoded), true);

            return $data['data'];
            //无法捕获到异常 其他异常均在JWT.php中抛出异常
//        } catch(\Firebase\JWT\SignatureInvalidException $e) {  //签名不正确
//
//            $res = array('error' => 1, 'errortip'=>'签名不正确!','msg' => $e->getMessage());
//            ajax_return($res);
//        }catch(\Firebase\JWT\BeforeValidException $e) {  // 签名在某个时间点之后才能用
//            $res = array('error' => 1, 'errortip'=>'签名在某个时间点之后才能用!','msg' => $e->getMessage());
//            ajax_return($res);
//        }catch(\Firebase\JWT\ExpiredException $e) {  // token过期
//            $res = array('error' => 1, 'errortip'=>'token过期!','msg' => $e->getMessage());
//            ajax_return($res);
        } catch(\Exception $e) {  //其他错误  后面补充
            $res = array('error' => 1, 'errortip'=>'Token异常!','msg' => $e->getMessage());
            ajax_return($res);

        }
        //Firebase定义了多个 throw new，我们可以捕获多个catch来定义问题，catch加入自己的业务，比如token过期可以用当前Token刷新一个新Token

    }

}