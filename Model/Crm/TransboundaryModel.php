<?php
/**
 * 跨界活动-PC后台端
 */

namespace Model\Crm;


class TransboundaryModel extends modelTpl
{

    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $isGmcPost = null;

    /**
     * 添加跨界活动
     * @param $paramArray
     * @return bool
     */
    public function addActivity($paramArray)
    {
        if($this->DataControl->getFieldOne("crm_transboundary_activity","activity_id","company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and activity_name = '{$paramArray['activity_name']}'")){
            $this->error = 1;
            $this->errortip = "活动名称已存在";
            $this->result = [];
            return false;
        }

        if($paramArray['activity_amount'] > 0){
            $amountrange = explode(',', $paramArray['activity_amountrange']);
            if($paramArray['activity_amount'] < $amountrange[1]){
                $this->error = 1;
                $this->errortip = "中奖金额不能大于预算金额";
                $this->result = [];
                return false;
            }
        }

        $activity_enddate = $paramArray['activity_enddate'].' 23:59:59';

        //查询该手机号是否有对应的商家入驻
        $businessInfo = $this->DataControl->selectOne("SELECT business_id FROM crm_transboundary_business WHERE business_mobile = '{$paramArray['business_mobile']}'");
        if(!$businessInfo){
            //根据手机号创建商户
            $business_id = $this->DataControl->insertData("crm_transboundary_business", ['business_mobile'=>$paramArray['business_mobile'], 'business_createtime'=>time()]);
        }else{
            $business_id = $businessInfo['business_id'];
            //已有商户 检测活动时间是否有冲突
//            $activityInfo = $this->DataControl->selectOne("
//            SELECT activity_id
//            FROM crm_transboundary_activity
//            WHERE business_id = '{$business_id}'
//            AND (
//            (activity_startdate BETWEEN STR_TO_DATE('{$paramArray['activity_startdate']}','%Y-%m-%d %H:%i:%s') AND STR_TO_DATE('{$activity_enddate}','%Y-%m-%d %H:%i:%s'))
//            OR
//            (activity_enddate BETWEEN STR_TO_DATE('{$paramArray['activity_startdate']}','%Y-%m-%d %H:%i:%s') AND STR_TO_DATE('{$activity_enddate}','%Y-%m-%d %H:%i:%s'))
//            )
//            ");
//            if($activityInfo){
//                $this->error = 1;
//                $this->errortip = "商户该时间段内有正在进行的活动,请重新规划时间!";
//                $this->result = [];
//                return false;
//            }
        }
        $activity_amountrange_array = explode(',', $paramArray['activity_amountrange']);
        if(count($activity_amountrange_array) <= 1){
            $this->error = 1;
            $this->errortip = "金额范围格式错误!";
            $this->result = [];
            return false;
        }
        if ($activity_amountrange_array) {
            foreach ($activity_amountrange_array as $key =>$value) {
                if(!is_numeric($value)){
                    $this->error = 1;
                    $this->errortip = "金额内容错误!";
                    $this->result = [];
                    return false;
                }
            }
        }
        if(time() > strtotime($activity_enddate)){
            $this->error = 1;
            $this->errortip = "不可添加已结束活动!";
            $this->result = [];
            return false;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['business_id'] = $business_id;
        $data['activity_name'] = $paramArray['activity_name'];
        $data['activity_contract'] = $paramArray['activity_contract'];
        $data['business_mobile'] = $paramArray['business_mobile'];
        $data['activity_rule'] = $paramArray['activity_rule'];
        $frommedia = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id", "company_id = '{$paramArray['company_id']}' and frommedia_name = '{$paramArray['frommedia_name']}'");
        $data['frommedia_id'] = $frommedia['frommedia_id'];
        $data['channel_id'] = $paramArray['channel_id'];
        $data['activity_amount'] = $paramArray['activity_amount'];
        $data['activity_leftamount'] = $paramArray['activity_amount'];
        $data['activity_amounttype'] = $paramArray['activity_amounttype'];
        $data['activity_startdate'] = $paramArray['activity_startdate'];
        $data['activity_enddate'] = $paramArray['activity_enddate'];
        if(time() > strtotime($paramArray['activity_startdate']) && time() < strtotime($activity_enddate)){
            $data['activity_status'] = '1';
        }
        $data['activity_amountrange'] = $paramArray['activity_amountrange'];
        $data['activity_createtime'] = time();
        $data['activity_qrcode'] = "https://pic.kedingdang.com/schoolmanage/202106111722x751280448.png";
        $data['business_qrcode'] = "https://pic.kedingdang.com/schoolmanage/202106111722x751280448.png";

        //创建活动
        $activity_id = $this->DataControl->insertData("crm_transboundary_activity", $data);
        if($activity_id){
            $this->error = 0;
            $this->errortip = "跨界活动添加成功";
            $this->result = [];
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "跨界活动添加失败~";
            $this->result = [];
            return false;
        }
    }

    /**
     * 编辑活动
     * @param $paramArray
     * @return bool
     */
    public function editActivity($paramArray)
    {
        if($this->DataControl->getFieldOne("crm_transboundary_activity","activity_id","company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and activity_id <> '{$paramArray['activity_id']}' and activity_name = '{$paramArray['activity_name']}'")){
            $this->error = 1;
            $this->errortip = "活动名称已存在";
            $this->result = [];
            return false;
        }

        if($paramArray['activity_amount'] > 0){
            $amountrange = explode(',', $paramArray['activity_amountrange']);
            if($paramArray['activity_amount'] < $amountrange[1]){
                $this->error = 1;
                $this->errortip = "中奖金额不能大于预算金额";
                $this->result = [];
                return false;
            }
        }

        $activity_enddate = $paramArray['activity_enddate'].' 23:59:59';

        //查询该活动是否存在
        $activityData = $this->DataControl->selectOne("SELECT activity_status FROM crm_transboundary_activity WHERE activity_id = '{$paramArray['activity_id']}'");
        if(empty($activityData)){
            $this->error = 1;
            $this->errortip = "跨界活动不存在~";
            $this->result = [];
            return false;
        }else{
            if($activityData['activity_status'] == 0){
                //查询该手机号是否有对应的商家入驻
                $businessInfo = $this->DataControl->selectOne("SELECT business_id FROM crm_transboundary_business WHERE business_mobile = '{$paramArray['business_mobile']}'");
                if(!$businessInfo){
                    //根据手机号创建商户
                    $business_id = $this->DataControl->insertData("crm_transboundary_business",['business_mobile'=>$paramArray['business_mobile'], 'business_createtime'=>time()]);
                }else{
                    $business_id = $businessInfo['business_id'];
                    //已有商户 检测活动时间是否有冲突
//                    $activityInfo = $this->DataControl->selectOne("
//                    SELECT activity_id
//                    FROM crm_transboundary_activity
//                    WHERE business_id='{$business_id}'
//                    AND (
//                    (activity_startdate BETWEEN STR_TO_DATE('{$paramArray['activity_startdate']}','%Y-%m-%d %H:%i:%s') AND STR_TO_DATE('{$activity_enddate}','%Y-%m-%d %H:%i:%s'))
//                    OR
//                    (activity_enddate BETWEEN STR_TO_DATE('{$paramArray['activity_startdate']}','%Y-%m-%d %H:%i:%s') AND STR_TO_DATE('{$activity_enddate}','%Y-%m-%d %H:%i:%s'))
//                    ) ");
//                    if($activityInfo){
//                        $this->error = 1;
//                        $this->errortip = "商户该时间段内有正在进行的活动,请重新规划时间!";
//                        $this->result = [];
//                        return false;
//                    }
                }
                $activity_amountrange_array = explode(',', $paramArray['activity_amountrange']);
                if(count($activity_amountrange_array) <= 1){
                    $this->error = 1;
                    $this->errortip = "金额范围格式错误!";
                    $this->result = [];
                    return false;
                }
                if ($activity_amountrange_array) {
                    foreach ($activity_amountrange_array as $key =>$value) {
                        if(!is_numeric($value)){
                            $this->error = 1;
                            $this->errortip = "金额内容错误!";
                            $this->result = [];
                            return false;
                        }
                    }
                }
                if(time() > strtotime($activity_enddate)){
                    $this->error = 1;
                    $this->errortip = "不可添加已结束活动!";
                    $this->result = [];
                    return false;
                }

                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['school_id'] = $paramArray['school_id'];
                $data['business_id'] = $business_id;
                $data['activity_name'] = $paramArray['activity_name'];
                $data['activity_contract'] = $paramArray['activity_contract'];
                $data['business_mobile'] = $paramArray['business_mobile'];
                $data['activity_rule'] = $paramArray['activity_rule'];
                $frommedia = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id", "company_id = '{$paramArray['company_id']}' and frommedia_name = '{$paramArray['frommedia_name']}'");
                $data['frommedia_id'] = $frommedia['frommedia_id'];
                $data['channel_id'] = $paramArray['channel_id'];
                $data['activity_amount'] = $paramArray['activity_amount'];
                $data['activity_leftamount'] = $paramArray['activity_amount'];
                $data['activity_amounttype'] = $paramArray['activity_amounttype'];
                $data['activity_startdate'] = $paramArray['activity_startdate'];
                $data['activity_enddate'] = $paramArray['activity_enddate'];
                if(time() > strtotime($paramArray['activity_startdate']) && time() < strtotime($activity_enddate)){
                    $data['activity_status'] = '1';
                }
                $data['activity_amountrange'] = $paramArray['activity_amountrange'];

                //更新活动
                $activity_id = $this->DataControl->updateData("crm_transboundary_activity","activity_id = '{$paramArray['activity_id']}'", $data);
                if($activity_id){
                    $this->error = 0;
                    $this->errortip = "跨界活动编辑成功";
                    $this->result = [];
                    return true;
                }else{
                    $this->error = 1;
                    $this->errortip = "跨界活动编辑失败~";
                    $this->result = [];
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "活动已开始,不可编辑~";
                $this->result = [];
                return false;
            }
        }
    }

    /**
     * 跨界活动列表
     * @param $paramArray
     * @return bool
     */
    public function activityList($paramArray)
    {
        $datawhere = " a.company_id = '{$paramArray['company_id']}'";
        //学校
        if($paramArray['school_id'] != 0) {
            $datawhere.=" AND a.school_id='{$paramArray['school_id']}' ";
        }
        //活动渠道类型ID
        if(isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != ''){
            $frommedia = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id", "company_id = '{$paramArray['company_id']}' and frommedia_name = '{$paramArray['frommedia_name']}'");
            $datawhere .= " AND a.frommedia_id = '{$frommedia['frommedia_id']}'";
        }
        //活动渠道ID
        if(isset($paramArray['channel_id']) && $paramArray['channel_id'] != ''){
            $datawhere .= " AND a.channel_id = '{$paramArray['channel_id']}'";
        }
        //开始时间
        if(isset($paramArray['activity_startdate']) && $paramArray['activity_startdate'] != ''){
            $datawhere .= " AND a.activity_startdate <= '{$paramArray['activity_startdate']}'";
        }
        //结束时间
        if(isset($paramArray['activity_enddate']) && $paramArray['activity_enddate'] != ''){
            $datawhere .= " AND a.activity_enddate >= '{$paramArray['activity_enddate']}'";
        }
        //中奖类型
        if(isset($paramArray['activity_amounttype']) && $paramArray['activity_amounttype'] != ''){
            $datawhere .= " AND a.activity_amounttype = '{$paramArray['activity_amounttype']}'";
        }
        //是否有预算 0无 1有
        if(isset($paramArray['activity_is_amount']) && $paramArray['activity_is_amount'] != ''){
            if($paramArray['activity_is_amount'] == 0){
                $datawhere .= " AND a.activity_amount = '0.00'";
            }else{
                $datawhere .= " AND a.activity_amount > 0";
            }
        }
        //名称  联系人  手机号
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " AND (a.activity_name LIKE '%{$paramArray['keyword']}%' OR a.activity_contract LIKE '%{$paramArray['keyword']}%' OR a.business_mobile LIKE '%{$paramArray['keyword']}%')";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "SELECT 
                    a.*,cf.frommedia_id,cf.frommedia_name,cc.channel_id,cc.channel_name 
                FROM 
                    crm_transboundary_activity as a 
                    LEFT JOIN crm_code_frommedia as cf ON a.frommedia_id = cf.frommedia_id 
                    LEFT JOIN crm_code_channel as cc ON a.channel_id = cc.channel_id 
                WHERE 
                    {$datawhere} 
                ORDER BY 
                    a.activity_createtime DESC ";

        if(isset($paramArray['is_export']) && $paramArray['is_export'] == 1){
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['activity_contract'] = $dateexcelvar['activity_contract'];
//                    $datearray['business_mobile'] = $dateexcelvar['business_mobile'];
                    $datearray['business_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['business_mobile']);
                    $datearray['activity_validusernum'] = $dateexcelvar['activity_validusernum'];
                    $datearray['activity_date'] = $dateexcelvar['activity_startdate'].'-'.$dateexcelvar['activity_enddate'];
                    $datearray['activity_createdate'] = date('Y-m-d H:i:s', $dateexcelvar['activity_createtime']);
                    $datearray['business_qrcode'] = $dateexcelvar['business_qrcode'];
                    $datearray['activity_qrcode'] = $dateexcelvar['activity_qrcode'];
                    if($dateexcelvar['activity_status'] == 0){
                        $datearray['activity_statusname'] = "未开始";
                    }elseif ($dateexcelvar['activity_status'] == 1){
                        $datearray['activity_statusname'] = "进行中";
                    }elseif ($dateexcelvar['activity_status'] == 2){
                        $datearray['activity_statusname'] = "已结束";
                    }
                    if($dateexcelvar['activity_amounttype']==1){
                        $datearray['activity_amounttypename'] = '随机金额';
                        $datearray['activity_amountrange'] = str_replace(',','-', $dateexcelvar['activity_amountrange']);
                    }else {
                        $datearray['activity_amounttypename'] = '固定金额';
                        $activity_amountrange_array = explode(',', $dateexcelvar['activity_amountrange']);
                        $datearray['activity_amountrange'] = $activity_amountrange_array[0];
                    }
                    if($dateexcelvar['activity_amount'] == '0.00'){
                        $datearray['activity_amount'] = '--';
                    }else{
                        $datearray['activity_amount'] = $dateexcelvar['activity_amount'];
                    }
                    $datearray['activity_paymentamount'] = $dateexcelvar['activity_paymentamount'];
                    if($dateexcelvar['activity_amount'] == '0.00' && $dateexcelvar['activity_leftamount'] == '0.00'){
                        $datearray['activity_leftamount'] = '--';
                    }else{
                        $datearray['activity_leftamount'] = $dateexcelvar['activity_leftamount'];
                    }
                    $datearray['activity_settleamount'] = $dateexcelvar['activity_settleamount'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("活动名称", "渠道类型", "渠道明细", "联系人", "手机号", "有效名单数", '活动时间', "创建时间", "商家二维码", "活动二维码", "活动状态", "中奖类型", "中奖金额", "活动预算", "已消耗金额", "剩余金额", "已结算金额"));
            $excelfileds = array('activity_name','frommedia_name','channel_name','activity_contract','business_mobile','activity_validusernum','activity_date','activity_createdate','business_qrcode','activity_qrcode','activity_statusname','activity_amounttypename','activity_amountrange','activity_amount','activity_paymentamount','activity_leftamount','activity_settleamount');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("跨界活动数据明细表.xlsx"));
            exit;
        }

        $dataList = $this->DataControl->selectClear($sql." LIMIT {$pagestart},{$num}");
        if($dataList){
            foreach ($dataList as &$value) {
                if(time() > strtotime($value['activity_startdate']) && time() < strtotime($value['activity_enddate'].' 23:59:59')){
                    if($value['activity_status'] == 0){
                        $this->DataControl->updateData("crm_transboundary_activity","activity_id='{$value['activity_id']}'", ['activity_status' => 1]);
                    }
                }
                if(time() > strtotime($value['activity_enddate'].' 23:59:59')){
                    if($value['activity_status'] != 2){
                        $this->DataControl->updateData("crm_transboundary_activity","activity_id='{$value['activity_id']}'", ['activity_status' => 2]);
                    }
                }

                if ($this->isGmcPost($paramArray['re_postbe_id'], $paramArray['company_id']) == true) {
                    $value['business_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['business_mobile']);
                } else {
                    $value['business_mobile'] = $value['business_mobile'];
                }

                $value['activity_date'] = $value['activity_startdate'].'-'.$value['activity_enddate'];
                $value['activity_createdate'] = date('Y-m-d H:i:s', $value['activity_createtime']);
                if($value['activity_amount'] == '0.00' && $value['activity_leftamount'] == '0.00'){
                    $value['activity_leftamount'] = '--';
                }
                if($value['activity_amount'] == '0.00'){
                    $value['activity_amount'] = '--';
                }
                if($value['activity_amounttype'] == 1){
                    $value['activity_amounttypename'] = '随机金额';
                    $value['activity_amountrange'] = str_replace(',','-',$value['activity_amountrange']);
                }else {
                    $value['activity_amounttypename'] = '固定金额';
                    $activity_amountrange_array = explode(',',$value['activity_amountrange']);
                    $value['activity_amountrange'] = $activity_amountrange_array[0];
                }
                if($value['activity_status'] == 0){
                    $value['activity_statusname'] = "未开始";
                }elseif ($value['activity_status'] == 1){
                    $value['activity_statusname'] = "进行中";
                }elseif ($value['activity_status'] == 2){
                    $value['activity_statusname'] = "已结束";
                }
                $QRcodeUrl = "company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&activity_id={$value['activity_id']}";
                //二维码
                $value['activityQRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getWXQRcodeApi?".$QRcodeUrl;
                $value['businessQRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getWXQRcodesApi";
            }
        }else{
            $dataList = array();
        }

        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $count = $this->DataControl->selectOne("SELECT 
                                                            COUNT(a.activity_id) as num 
                                                        FROM 
                                                            crm_transboundary_activity a 
                                                            LEFT JOIN crm_code_frommedia cf ON a.frommedia_id=cf.frommedia_id 
                                                            LEFT JOIN crm_code_channel cc ON a.channel_id=cc.channel_id 
                                                        WHERE 
                                                            {$datawhere}");
            $count = $count['num']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = $dataList;
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "跨界活动列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无跨界活动";
            $this->result = $result;
            return false;
        }
    }

    /**
     * 活动参与名单
     * @param $paramArray activity_id
     * @return bool
     */
    public function activityRecordList($paramArray)
    {
        $datawhere = "ac.activity_id = '{$paramArray['activity_id']}'";

        //开始时间
        if(isset($paramArray['activity_startdate']) && $paramArray['activity_startdate'] != ''){
            $start_time = strtotime($paramArray['activity_startdate']);
            $datawhere .= " AND ac.record_createtime >= '{$start_time}'";
        }
        //结束时间
        if(isset($paramArray['activity_enddate']) && $paramArray['activity_enddate'] != ''){
            $end_time = strtotime($paramArray['activity_enddate'].' 23:59:59');
            $datawhere .= " AND ac.record_createtime <= '{$end_time}'";
        }
        //是否结算 0否 1是
        if(isset($paramArray['activity_is_amount']) && $paramArray['activity_is_amount'] != ''){
            $datawhere .= " AND ac.record_settlementstatus = '{$paramArray['activity_is_amount']}' ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    ac.*,u.user_name,u.user_mobile,u.user_age,u.user_sex,
                    (SELECT a.activity_name FROM crm_transboundary_activity AS a WHERE a.activity_id = ac.activity_id) as activity_name
                FROM 
                    crm_transboundary_activity_record AS ac 
                    LEFT JOIN crm_transboundary_user AS u ON ac.user_id=u.user_id 
                WHERE 
                    {$datawhere} and ac.record_winningamount > 0
                ORDER BY 
                    ac.record_createtime DESC";

        if(isset($paramArray['is_export']) && $paramArray['is_export'] == 1){
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['user_name'] = $dateexcelvar['user_name'];
                    $datearray['user_sex'] = $dateexcelvar['user_sex'];
                    $datearray['user_age'] = $dateexcelvar['user_age'];
                    $datearray['user_mobile'] = $dateexcelvar['user_mobile'];
                    $datearray['record_createdate'] = date('Y-m-d H:i:s', $dateexcelvar['record_createtime']);
                    $datearray['record_winningamount'] = $dateexcelvar['record_winningamount'];
                    if($dateexcelvar['record_settlementstatus'] == 1){
                        $datearray['record_settlementstatus_name'] = "已结算";
                    }else{
                        $datearray['record_settlementstatus_name'] = "未结算";
                    }
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("姓名", "性别", "年龄", "手机号", "创建时间", "中奖金额", "是否结算"));
            $excelfileds = array('user_name','user_sex','user_age','user_mobile','record_createdate','record_winningamount','record_settlementstatus_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("活动参与名单数据明细表.xlsx"));
            exit;
        }

        $dataList = $this->DataControl->selectClear($sql. " LIMIT {$pagestart},{$num} ");
        if($dataList){
            foreach ($dataList as &$value) {
                $value['record_createdate'] = date('Y-m-d H:i:s',$value['record_createtime']);
                if($value['record_settlementstatus'] == 1){
                    $value['record_settlementstatus_name'] = "已结算";
                }else{
                    $value['record_settlementstatus_name'] = "未结算";
                }
            }
        }else{
            $dataList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $count = $this->DataControl->selectOne("SELECT COUNT(ac.record_id) AS num 
                                                        FROM crm_transboundary_activity_record AS ac 
                                                        LEFT JOIN crm_transboundary_user AS u ON ac.user_id=u.user_id 
                                                        WHERE {$datawhere} and ac.record_winningamount > 0");
            $count = $count['num']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = $dataList;
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "活动参与列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无参与信息~";
            $this->result = $result;
            return false;
        }
    }

    /**
     * 批量结算
     * @param $paramArray
     * @return bool
     */
    public function batchSettlement($paramArray)
    {
        $record_json = json_decode(stripslashes($paramArray['record_json']), 1);
        if(empty($record_json)){
            $this->error = 1;
            $this->errortip = "批量参数无效";
            return false;
        }else{
            $settlementamount = 0;
            foreach ($record_json as $key => $value) {
                $recordData = $this->DataControl->getFieldOne("crm_transboundary_activity_record", "record_winningamount,record_settlementstatus,activity_id", "record_id='{$value['record_id']}'");
                if($recordData['record_settlementstatus'] == 0){
                    $settlementamount += $recordData['record_winningamount'];
                    $updateResult = $this->DataControl->updateData("crm_transboundary_activity_record","record_id='{$value['record_id']}' ",[
                        'record_settlementstatus'=>1,
                        'record_settlementamount'=>$recordData['record_winningamount']
                    ]);
                    if($updateResult){
                        //更新至活动
                        $activityData = $this->DataControl->getFieldOne("crm_transboundary_activity", "activity_settleamount", "activity_id = '{$recordData['activity_id']}'");
                        $activity_settleamount = ($activityData['activity_settleamount']*100 + $recordData['record_winningamount']*100)/100;
                        $this->DataControl->updateData("crm_transboundary_activity"," activity_id='{$recordData['activity_id']}' ",['activity_settleamount'=>$activity_settleamount]);
                    }
                }
            }

            $this->error = 0;
            $this->errortip = "已结算完毕";
            $this->result = ["record_num" => count($record_json), "record_settlementamount" => $settlementamount];
            return true;
        }
    }

    /**
     * 结束活动
     * @param $paramArray
     * @return bool
     */
    public function closeActivity($paramArray)
    {
        $activityData = $this->DataControl->selectOne("SELECT activity_name,business_mobile,activity_status FROM crm_transboundary_activity WHERE activity_id='{$paramArray['activity_id']}'");
        if(empty($activityData)){
            $this->error = 1;
            $this->errortip = "没有找到该活动";
            $this->result = [];
            return false;
        }else{
            if($activityData['activity_status'] <= 1){
                $updateResult = $this->DataControl->updateData("crm_transboundary_activity"," activity_id='{$paramArray['activity_id']}' " ,['activity_status'=>2]);
                if($updateResult){
                    $this->error = 0;
                    $this->errortip = "已成功结束活动!";
                    $this->result = $activityData;
                    return true;
                }else{
                    $this->error = 1;
                    $this->errortip = "操作失败!";
                    $this->result = [];
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "活动已结束";
                $this->result = [];
                return false;
            }
        }
    }

    /**
     * 删除 活动
     * @param $paramArray
     * @return bool
     */
    public function deleteActivity($paramArray)
    {
        $activityData = $this->DataControl->getFieldOne("crm_transboundary_activity", "activity_status", "activity_id = '{$paramArray['activity_id']}'");
        if(empty($activityData)){
            $this->error = 1;
            $this->errortip = "没有找到该活动";
            $this->result = [];
            return true;
        }else{
            //未开始
            if($activityData['activity_status'] == 0){
                //删除
                $deletaResult = $this->DataControl->delData("crm_transboundary_activity"," activity_id='{$paramArray['activity_id']}' ");
                if($deletaResult){
                    $this->error = 0;
                    $this->errortip = "已成功删除该活动!";
                    $this->result = [];
                    return true;
                }else{
                    $this->error = 1;
                    $this->errortip = "操作失败,请刷新后重试";
                    $this->result = [];
                    return true;
                }
            }else{
                $this->error = 1;
                $this->errortip = "该活动不可删除";
                $this->result = [];
                return true;
            }
        }
    }
}