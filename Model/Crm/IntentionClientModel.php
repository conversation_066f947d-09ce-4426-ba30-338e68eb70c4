<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  IntentionClientModel extends modelTpl
{
    public $m;
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $company_isassist = 0;
    public $isGmcPost = null;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'], $publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile,marketer_outtype,marketer_jjhlworknumber,marketer_hjagentid,marketer_hjdeviceid,marketer_hjpassword", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }else {
            return true;
        }
    }

    //手机端 拨打电话操作
    function callClientMobileAction($paramArray){//
        //获取电话厂商
        $schsql = " select a.merchant_id from crm_merchant_seats_schoolapply as a,crm_merchant_seats as b where a.school_id = '{$paramArray['school_id']}' AND a.merchant_id = b.merchant_id and a.seats_id = b.seats_id and b.company_id = '{$paramArray['company_id']}' and b.seats_isdel = '0' and b.seats_isopen = '1' and b.seats_isofficial = '0'  order by a.merchant_id ";
        $merchantOne = $this->DataControl->selectOne($schsql);
        $merchant_id = $merchantOne['merchant_id']?$merchantOne['merchant_id']:3;
//        $merchant_id = 2;
        //获取 名单手机号  $clientOne['client_mobile']
        $clientOne = $this->DataControl->selectOne(" select client_mobile from crm_client where client_id = '{$paramArray['client_id']}' and company_id = '{$paramArray['company_id']}' ");

//        $this->marketerOne['marketer_mobile'] = '15306128118';//教师手机号 --- 模拟数据
//        $clientOne['client_mobile'] = '17317543821';//名单手机号 模拟数据

        //3 为 400电话选择
        if($paramArray['numbertype'] == '3'){
            $schsqltwo = " select a.merchant_id from crm_merchant_seats_schoolapply as a,crm_merchant_seats as b where a.school_id = '{$paramArray['school_id']}' AND a.merchant_id = b.merchant_id and a.seats_id = b.seats_id and b.company_id = '{$paramArray['company_id']}' and b.seats_isdel = '0' and b.seats_isopen = '1' and b.seats_isofficial = '1'  order by a.merchant_id ";
            $merchantTwo = $this->DataControl->selectOne($schsqltwo);
            $merchant_id = $merchantTwo['merchant_id']?$merchantTwo['merchant_id']:0;
            if($merchant_id > 0){
                if ($merchant_id == '19' || $merchant_id == '20') {//慧捷 400 电话
                    do {
                        $callbranch = 'HJ400' . getCallBatchId();
                    } while ($this->DataControl->selectOne("select callrecord_id from crm_client_callrecord where callrecord_callid='{$callbranch}' and callrecord_type = '5' limit 0,1"));
                    $paramArray['callbranch'] = $callbranch;
                    //慧捷电话双呼 操作
                    $Model = new \Model\Crm\PhoneOutboundModel();
                    $Model->getHuijieFourDualCall($paramArray, $this->marketerOne['marketer_mobile'], $clientOne['client_mobile'],$paramArray['numbertype']);
                    if ($Model->error == '0') {
                        $this->error = 0;
                        $this->errortip = $Model->errortip;
                        $this->result = $Model->result;
                        return true;
                    } else {
                        $this->error = 1;
                        $this->errortip = $Model->errortip;
                        return false;
                    }
                }else{
                    $this->error = 1;
                    $this->errortip = "厂商不在设定的范围";
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "厂商不在设定的范围";
                return false;
            }
        }
        else{
            if ($merchant_id == '1' || $merchant_id == '14') {//慧捷
                //获取学校省市
                $schOne = $this->DataControl->selectOne(" select school_province,school_city from smc_school where company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' ");
                //找一个分机号
                $seatsql = "select * 
                    from crm_merchant_seats as a 
                    where company_id = '{$paramArray['company_id']}' and merchant_id = '{$merchant_id}' and seats_isopen = '1' and seats_isdel = '0' 
                    ORDER BY RAND() LIMIT 1";//ORDER BY RAND() LIMIT 1
                $seatsOne = $this->DataControl->selectOne($seatsql);

                if ($seatsOne['seats_isway'] == '2') {
                    do {
                        $callbranch = 'HJ' . getCallBatchId();
                    } while ($this->DataControl->selectOne("select callrecord_id from crm_client_callrecord where callrecord_callid='{$callbranch}' and callrecord_type = '1' limit 0,1"));
                    $paramArray['callbranch'] = $callbranch;
                    //慧捷电话双呼 操作
                    $Model = new \Model\Crm\PhoneOutboundModel();
                    $Model->getHuijieDualCall($paramArray, $this->marketerOne['marketer_mobile'], $clientOne['client_mobile'], $paramArray['numbertype'], $seatsOne['seats_account'], $schOne['school_city']);
                    if ($Model->error == '0') {
                        $this->error = 0;
                        $this->errortip = $Model->errortip;
                        $this->result = $Model->result;
                        return true;
                    } else {
                        $this->error = 1;
                        $this->errortip = $Model->errortip;
                        return false;
                    }
                } else {
                    $this->error = 1;
                    $this->errortip = '暂未开放！';//暂未开放慧捷其他呼叫模式
                    return false;
                }
            } elseif ($merchant_id == '2' || $merchant_id == '15') {
                //教师手机号 --- 模拟数据
//            $this->marketerOne['marketer_mobile'] = '***********';//
//            $clientOne['client_mobile'] = '***********';//名单手机号 模拟数据
                //找一个分机号
                $seatsql = "select * 
                    from crm_merchant_seats as a 
                    where company_id = '{$paramArray['company_id']}' and merchant_id = '{$merchant_id}' and seats_isopen = '1' and seats_isdel = '0' ";
                $seatsOne = $this->DataControl->selectOne($seatsql);
                if ($seatsOne['seats_isway'] == '2') {
                    do {
                        $callbranch = 'HL' . getCallBatchId();
                    } while ($this->DataControl->selectOne("select callrecord_id from crm_client_callrecord where callrecord_callid='{$callbranch}' and callrecord_type = '2' limit 0,1"));
                    $paramArray['callbranch'] = $callbranch;
                    //合力电话双呼 操作
                    $Model = new \Model\Crm\PhoneOutboundModel();
                    $Model->getHeliDualCall($paramArray, $this->marketerOne['marketer_mobile'], $clientOne['client_mobile'], $paramArray['numbertype'], $seatsOne['seats_account']);
                    if ($Model->error == '0') {
                        $this->error = 0;
                        $this->errortip = $Model->errortip;
                        $this->result = $Model->result;
                        return true;
                    } else {
                        $this->error = 1;
                        $this->errortip = $Model->errortip;
                        return false;
                    }
                } else {
                    $this->error = 1;
                    $this->errortip = '暂未开放！';//暂未开放合力其他呼叫模式
                    return false;
                }

            } elseif ($merchant_id == '3' || $merchant_id == '16') {//士决
                //教师手机号 --- 模拟数据
//            $this->marketerOne['marketer_mobile'] = $paramArray['marketer_mobile'];//
//            $clientOne['client_mobile'] = '***********';//名单手机号 模拟数据

                //找一个分机号
                $seatsql = "select * ,
                        if((select b.schoolapply_id from crm_merchant_seats_schoolapply as b where a.merchant_id = b.merchant_id and a.seats_id = b.seats_id and b.school_id = '{$paramArray['school_id']}' limit 0,1  ),1,0) as schoolapply_id 
                    from crm_merchant_seats as a 
                    where company_id = '{$paramArray['company_id']}' and merchant_id = '{$merchant_id}' and seats_isopen = '1' and seats_isdel = '0' 
                    having schoolapply_id = 1
                    ORDER BY RAND() LIMIT 1";//ORDER BY RAND() LIMIT 1
                $seatsOne = $this->DataControl->selectOne($seatsql);

                if ($seatsOne['seats_isway'] == '2') {
                    do {
                        $callbranch = 'SJ' . getCallBatchId();
                    } while ($this->DataControl->selectOne("select callrecord_id from crm_client_callrecord where callrecord_callid='{$callbranch}' and callrecord_type = '3' limit 0,1"));
                    $paramArray['callbranch'] = $callbranch;
                    //慧捷电话双呼 操作
                    $Model = new \Model\Crm\PhoneOutboundModel();
                    $Model->getShijueDualCall($paramArray, $this->marketerOne['marketer_mobile'], $clientOne['client_mobile'], $paramArray['numbertype'], $seatsOne['seats_account']);
                    if ($Model->error == '0') {
                        $this->error = 0;
                        $this->errortip = $Model->errortip;
                        $this->result = $Model->result;
                        return true;
                    } else {
                        $this->error = 1;
                        $this->errortip = $Model->errortip;
                        return false;
                    }
                } else {
                    $this->error = 1;
                    $this->errortip = '暂未开放！';//士决其他呼叫模式
                    return false;
                }
            } elseif ($merchant_id == '17' || $merchant_id == '17') {//极简互联
                //教师手机号 --- 模拟数据
//            $this->marketerOne['marketer_mobile'] = '***********';//
//            $clientOne['client_mobile'] = '***********';//名单手机号 模拟数据
                //找一个分机号
                $seatsql = "select * 
                    from crm_merchant_seats as a 
                    where company_id = '{$paramArray['company_id']}' and merchant_id = '{$merchant_id}' and seats_isopen = '1' and seats_isdel = '0' ";
                $seatsOne = $this->DataControl->selectOne($seatsql);
                if ($seatsOne['seats_isway'] == '2') {
                    do {
                        $callbranch = 'JJ' . getCallBatchId();
                    } while ($this->DataControl->selectOne("select callrecord_id from crm_client_callrecord where callrecord_callid='{$callbranch}' and callrecord_type = '4' limit 0,1"));
                    $paramArray['callbranch'] = $callbranch;
//                //极简互联电话双呼 操作
//                $Model = new \Model\Crm\PhoneOutboundModel();
//                $Model->getJjhlDualCal($paramArray,$this->marketerOne['marketer_mobile'], $clientOne['client_mobile'], $paramArray['numbertype'], $seatsOne['seats_account']);
                    //极简互联 外呼双呼 操作 -- 坐席概念
                    $Model = new \Model\Crm\PhoneOutboundModel();
                    $Model->getJjhlCallout($paramArray, $this->marketerOne['marketer_mobile'], $this->marketerOne['marketer_jjhlworknumber'], $clientOne['client_mobile'], $paramArray['numbertype']);
                    if ($Model->error == '0') {
                        $this->error = 0;
                        $this->errortip = $Model->errortip;
                        $this->result = $Model->result;
                        return true;
                    } else {
                        $this->error = 1;
                        $this->errortip = $Model->errortip;
                        return false;
                    }
                } else {
                    $this->error = 1;
                    $this->errortip = '暂未开放！';//暂未开放极简互联其他呼叫模式
                    return false;
                }

            } else {
                $this->error = 1;
                $this->errortip = '运营商未开启！';
                return false;
            }
        }
    }

    /**
     * 获取意向客户列表 -- 针对手机版本（呼叫中心)
     * @param $paramArray
     */
    function getIntentionClientListMobile($paramArray){
        $datawhere = "s.company_id ='{$paramArray['company_id']}' and p.school_id='{$paramArray['school_id']}' and p.principal_leave = 0  and c.client_isgross = '0' and c.client_tracestatus > '-1' ";
        if (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] == 1 or $paramArray['postbe_crmuserlevel'] == 3)  ) {
            $datawhere .= " and  c.client_tracestatus <> 4";
            if (isset($paramArray['client_isnewtip']) && $paramArray['client_isnewtip']) {
                $datawhere .= "  and c.client_isnewtip = '{$paramArray['client_isnewtip']}'";
            }
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}' and c.client_distributionstatus = 1 and  c.client_tracestatus <> 4 ";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' )";
        }
        if (isset($paramArray['client_id']) && $paramArray['client_id'] !== '') {
            $datawhere .= " and c.client_id = '{$paramArray['client_id']}' ";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sqlfields = " c.client_id,c.client_img,c.client_cnname,c.client_enname,c.client_sex,c.client_age,c.client_birthday,c.client_tracestatus,c.client_intention_level,c.client_mobile,c.client_source,ch.channel_name,ch.channel_id ";

        if (isset($paramArray['order']) && $paramArray['order'] == "") {
            $paramArray['order'] = "1";
        }
        if (isset($paramArray['order']) && $paramArray['order'] !== "1") {
            $sqlorder = "c.{$paramArray['order']} DESC";
        } else {
            $sqlorder = 'c.client_tracestatus ASC,c.client_intention_level,c.client_updatetime DESC';
        }
        $sql = "select {$sqlfields} 
				from  crm_client as c
				Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id 
				left join crm_code_channel as ch on ch.channel_id = c.channel_id
				where {$datawhere}  group by c.client_id ORDER BY {$sqlorder}";
        $sql .= "  LIMIT {$pagestart},{$num}";
        $clientList = $this->DataControl->selectClear($sql);

        $fieldname = array('client_id','client_img','client_cnname','client_enname','client_sex','client_age','client_birthday','client_tracestatus',
            'client_intention_level','client_mobile','client_source','channel_name','channel_id');
        $fieldstring = array('名单ID','名单头像','名单中文名','名单英文名','名单性别','名单年龄，名单生日','名单跟踪状态',
            '名单意向登记1-5','名单手机号','名单渠道类型','名单渠道明细','名单渠道明细ID');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1"  , "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1"   , "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if ($clientList) {
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as &$value) {
                $value['client_status'] = $clientTracestatus[$value['client_tracestatus']];
            }

            $all_num = $this->DataControl->selectClear("SELECT c.client_id
				from  crm_client as c
				Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id 
				left join crm_code_channel as ch on ch.channel_id = c.channel_id
				where {$datawhere}  group by c.client_id  ");
            $allnum = $all_num?count($all_num):0;
        }else{
            $allnum = 0;

            $result = array();
            $result["field"] = $field;
            $result['count'] = 0;
            $result['datalist'] = array();

            $this->error = 1;
            $this->errortip = "暂无意向客户信息";
            $this->result = $result;
            return false;
        }

        $result = array();
        $result["field"] = $field;
        $result['count'] = $allnum;
        $result['datalist'] = $clientList?$clientList:array();

        $this->error = 0;
        $this->errortip = "信息获取成功";
        $this->result = $result;
        return true;

    }
    //获取意向客户 分类的统计数字
    function getIntentionNum($paramArray)
    {
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_ispositive", "company_id='{$paramArray['company_id']}'");
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.client_isgross = '0' and c.client_tracestatus > '-1' and p.school_id='{$paramArray['school_id']}' and p.principal_leave = 0  ";
        if (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] == 1 or $paramArray['postbe_crmuserlevel'] == 3)  ) {
            $datawhere .= " and  c.client_tracestatus <> 4";
            if (isset($paramArray['client_isnewtip']) && $paramArray['client_isnewtip']) {
                $datawhere .= "  and c.client_isnewtip = '{$paramArray['client_isnewtip']}'";
            }
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}' and c.client_distributionstatus = 1 and  c.client_tracestatus <> 4 ";
        }

        //逾期
        $Having = '1';
        $havingsql = '';
        $levellist = $this->DataControl->selectClear("select * from gmc_code_intentlevel where company_id = '{$paramArray['company_id']}'");
        if($levellist){
            $someHave = "";
            foreach ($levellist as $levelVar){
                if($levelVar['intentlevel_trackday'] > 0 ){
                    $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                    if($someHave){
                        $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime < '{$stime}' ) ";
                    }else{
                        $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime < '{$stime}' ) ";
                    }
                }
            }
        }
        $Having .= $someHave? " and ($someHave)  ":$someHave;
        $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime";

        $sqlfields = 'c.client_id,c.client_intention_level';
        $sql = "select {$sqlfields},{$havingsql}  
            from  crm_client as c
            Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
            Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
            Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
            left join crm_code_channel as ch on ch.channel_id = c.channel_id 
            where {$datawhere}  group by c.client_id Having {$Having}  ";
        $yuqiList = $this->DataControl->selectClear($sql);
        $yuqiNum = $yuqiList?count($yuqiList):0;

        //预警
        $Having = '1';
        $havingsql = '';
        $levellist = $this->DataControl->selectClear("select * from gmc_code_intentlevel where company_id = '{$paramArray['company_id']}'");
        if($levellist){
            $someHave = "";
            foreach ($levellist as $levelVar){
                if($levelVar['intentlevel_warningday'] > 0 ){
                    $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                    $etime = strtotime(date('Y-m-d',time()-(86400*($levelVar['intentlevel_trackday'] - $levelVar['intentlevel_warningday']))));
                    if($someHave){
                        $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                    }else{
                        $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                    }
                }
            }
        }
        $Having .= $someHave? " and ($someHave)  ":$someHave;
        $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime";

        $sqlfields = 'c.client_id,c.client_intention_level';
        $sql = "select {$sqlfields},{$havingsql}  
            from  crm_client as c
            Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
            Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
            Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
            left join crm_code_channel as ch on ch.channel_id = c.channel_id 
            where {$datawhere}  group by c.client_id Having {$Having}  ";
        $yujingList = $this->DataControl->selectClear($sql);
        $yujingNum = $yujingList?count($yujingList):0;

        $data = array();
        $data['yuqi'] = $yuqiNum;
        $data['yujing'] = $yujingNum;

        $field = [
            'yuqi' => '逾期',
            'yujing' => '预警',
        ];

        $result = array();
        $result['field'] = $field;
        $result['list'] = $data;

        $this->error = 0;
        $this->errortip = "统计信息！";
        $this->result = $result;
        return false;
    }
    /**
     * 获取意向客户列表
     * @param $paramArray
     * @return array
     */
    function getIntentionClientList($paramArray)
    {
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_ispositive", "company_id='{$paramArray['company_id']}'");
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.client_isgross = '0' and c.client_tracestatus > '-1' and p.school_id='{$paramArray['school_id']}' and p.principal_leave = 0  ";
        if (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] == 1 or $paramArray['postbe_crmuserlevel'] == 3)  ) {
            $datawhere .= " and  c.client_tracestatus <> 4";
            if (isset($paramArray['client_isnewtip']) && $paramArray['client_isnewtip']) {
                $datawhere .= "  and c.client_isnewtip = '{$paramArray['client_isnewtip']}'";
            }
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}' and c.client_distributionstatus = 1 and  c.client_tracestatus <> 4 ";
        }
        if (isset($paramArray['main_marketer_id']) && $paramArray['main_marketer_id']) {
            //同时支持主负责人 和 副负责人筛选
            $datawhere .= " and p.marketer_id = '{$paramArray['main_marketer_id']}' and principal_ismajor = 1";
        }
        if (isset($paramArray['fu_marketer_id']) && $paramArray['fu_marketer_id']) {
            $datawhere .= "  and p.marketer_id = '{$paramArray['fu_marketer_id']}' and principal_ismajor = 0";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '') {
            $datawhere .= " and c.channel_id ='{$paramArray['channel_id']}'";
        }
        if (isset($paramArray['client_source']) && $paramArray['client_source'] !== '') {
            $datawhere .= " and c.client_source ='{$paramArray['client_source']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '') {
            $datawhere .= " and c.client_source ='{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or c.client_frompage like '%{$paramArray['keyword']}%'  or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['track_intention_level']) && $paramArray['track_intention_level'] !== '') {
            $datawhere .= " and c.client_intention_level ='{$paramArray['track_intention_level']}'";
        }
        //接通状态 0未接通 1接通有效 2接通无效
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone = '{$paramArray['client_answerphone']}'";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel >= 3";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel < 3";
        }
        if (isset($paramArray['toaday']) && $paramArray['today'] != "") {
            $today = date('Y-m-d');
            $datawhere .= " and FROM_UNIXTIME(c.client_updatetime,'%Y-%m-%d')  = '{$today}'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != "" && $paramArray['activity_id'] != 0) {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >='{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <='{$create_endtime}'";
        }
        if (isset($paramArray['client_updatetime']) && $paramArray['client_updatetime'] !== '') {
            $update_starttime = strtotime($paramArray['client_updatetime']);
            $datawhere .= " and c.client_updatetime >='{$update_starttime}'";
        }
        if (isset($paramArray['client_updatetime']) && $paramArray['client_updatetime'] !== '') {
            $update_endtime = strtotime($paramArray['client_updatetime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_updatetime <='{$update_endtime}'";
        }
        //手机端更新时间
        if (isset($paramArray['client_updatestarttime']) && $paramArray['client_updatestarttime'] !== '') {
            $update_starttime = strtotime($paramArray['client_updatestarttime']);
            $datawhere .= " and c.client_updatetime >='{$update_starttime}'";
        }
        if (isset($paramArray['client_updateendtime']) && $paramArray['client_updateendtime'] !== '') {
            $update_endtime = strtotime($paramArray['client_updateendtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_updatetime <='{$update_endtime}'";
        }
        //跟踪状态：0待跟踪1持续跟踪2已邀约3已视听4已转正-1无意向-2无效名单  -----------  状态就是当前状态
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$paramArray['client_tracestatus']}' ";
        }

        // 20240321 优化子查询转为 in 方法
        if($paramArray['nohavetrack'] == '1' || $paramArray['nohavetrack'] == '0' || (isset($paramArray['track_createtime']) && $paramArray['track_createtime'] != '') || (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] != '') || (isset($paramArray['track_count']) && $paramArray['track_count'] != '') ){
            $sonhaving = " ";
            if (isset($paramArray['nohavetrack']) && $paramArray['nohavetrack'] == '1') {
                $sevendaytime = strtotime(date("Ymd", strtotime("-6 days"))) + 24 * 60 * 60 - 1;
                $sonhaving .= " and MAX(a.track_createtime) <= '{$sevendaytime}' ";
            }elseif(isset($paramArray['nohavetrack']) && $paramArray['nohavetrack'] == '0') {
                $sevendaytime = strtotime(date("Ymd", strtotime("-6 days"))) + 24 * 60 * 60 - 1;
                $sonhaving .= " and MAX(a.track_createtime) >= '{$sevendaytime}' ";
            }
            if (isset($paramArray['track_createtime']) && $paramArray['track_createtime'] != '') {
                $track_createtime = strtotime($paramArray['track_createtime']);
                $sonhaving .= " and MAX(a.track_createtime) >= '{$track_createtime}' ";
            }
            if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] !== '') {
                $track_endtime = strtotime($paramArray['track_endtime']) + 3600 * 24 - 1;
                $sonhaving .= " and MAX(a.track_createtime) <= '{$track_endtime}'  ";
            }
            if (isset($paramArray['track_count']) && $paramArray['track_count'] != '') {
                $sonhaving .= " and COUNT(a.track_id) <= '{$paramArray['track_count']}'";
            }
            $sonsql = "SELECT a.client_id 
                        FROM crm_client_track as a,crm_client as b 
                        WHERE a.school_id = '{$paramArray['school_id']}' and a.track_isactive = 1 
                            and a.client_id = b.client_id and b.client_tracestatus > '-1' and b.client_tracestatus < '4'   
                        GROUP BY a.client_id 
                        HAVING 1 {$sonhaving} ";
            $sonList = $this->DataControl->selectClear($sonsql);
            if($sonList) {
                $sonClientIdArray = array_column($sonList, 'client_id');
                $sonClientIdStr = implode(',', $sonClientIdArray);
            }else{
                $sonClientIdStr = '0';
            }

            $datawhere .= " and c.client_id in ({$sonClientIdStr}) ";
        }

        $Having = '1';
        $havingsql = '';
        //为提醒使用
        $tipHaving = '1';
        $tiphavingsql = '';
        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $Having .= " and active_invite_id is null and invite_idthree is null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree, ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $Having .= " and invite_idthree is not null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree, ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $Having .= " and active_invite_id is not null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,  ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null and audition_idthree is null  ";
            $havingsql .= " (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree, ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $Having .= " and audition_idthree is not null ";
            $havingsql .= "  (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree, ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $Having .= " and active_audition_id is not null ";
            $havingsql .= " (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id, ";
        }


        $levellist = $this->DataControl->selectClear("select * from gmc_code_intentlevel where company_id = '{$paramArray['company_id']}'");
        //判断 提箱低频状态   overduetype  1 常规跟进  2 跟进逾期   3 低频跟进预警名单    （常规跟进+跟进逾期=所有意向客户）
        if (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '1') {
            if($levellist){
                $someHave = "";
                foreach ($levellist as $levelVar){
                    if($levelVar['intentlevel_trackday'] > 0 ){
                        $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                        if($someHave){
                            $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and (last_zhu_track_createtime >= '{$stime}' or last_zhu_track_createtime = 1) ) ";
                        }else{
                            $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and (last_zhu_track_createtime >= '{$stime}' or last_zhu_track_createtime = 1) ) ";
                        }
                    }
                }
            }
            $Having .= $someHave? " and ( c.client_intention_level = '0' or $someHave)  ":$someHave;
            $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";

            $tipHaving .= $someHave? " and ( c.client_intention_level = '0' or $someHave)  ":$someHave;
            $tiphavingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";
        }elseif (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '2') {
            if($levellist){
                $someHave = "";
                foreach ($levellist as $levelVar){
                    if($levelVar['intentlevel_trackday'] > 0 ){
                        $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                        if($someHave){
                            $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1 and last_zhu_track_createtime < '{$stime}' ) ";
                        }else{
                            $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1 and last_zhu_track_createtime < '{$stime}' ) ";
                        }
                    }
                }
            }
            $Having .= $someHave? " and ($someHave)  ":$someHave;
            $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";

            $tipHaving .= $someHave? " and ($someHave)  ":$someHave;
            $tiphavingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";
        }elseif (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '3') {
            if($levellist){
                $someHave = "";
                foreach ($levellist as $levelVar){
                    if($levelVar['intentlevel_warningday'] > 0 ){
                        $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                        $etime = strtotime(date('Y-m-d',time()-(86400*($levelVar['intentlevel_trackday'] - $levelVar['intentlevel_warningday']))));
                        if($someHave){
                            $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1 and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                        }else{
                            $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1 and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                        }
                    }
                }
            }
            $Having .= $someHave? " and ($someHave)  ":$someHave;
            $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";

            $tipHaving .= $someHave? " and ($someHave)  ":$someHave;
            $tiphavingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($paramArray['is_count']) and $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectClear("select {$havingsql}c.client_id,c.client_intention_level 
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id 
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id AND s.school_id = p.school_id
				where {$datawhere}  Group by c.client_id
				HAVING {$Having}");
            if ($all_num) {
                $data['allnums'] = count($all_num);
            } else {
                $data['allnums'] = 0;
            }
        }
        if (isset($paramArray['order']) && $paramArray['order'] == "") {
            $paramArray['order'] = "1";
        }
        if (isset($paramArray['order']) && $paramArray['order'] !== "1") {
            $sqlorder = "c.{$paramArray['order']} DESC";
        } else {
            $sqlorder = 'c.client_tracestatus ASC,c.client_intention_level,c.client_updatetime DESC';
        }
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $sqlfields = 'c.client_id,c.promotion_id,c.client_img,c.client_actisagree,c.client_cnname,c.client_enname,c.client_age,c.client_tag,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,c.client_frompage,c.client_sponsor,c.client_stubranch,c.client_teachername,FROM_UNIXTIME(c.client_createtime) as client_createtime,c.client_tracestatus,c.client_remark,ch.channel_id,ch.channel_name,ch.channel_medianame,c.client_email,c.client_lineid,c.client_facebookid';
            $sql = "select {$sqlfields},{$havingsql} 
                (select pt.promotion_jobnumber from crm_ground_promotion as pt where pt.promotion_id = c.promotion_id ) as promotion_jobnumber
				,(select  group_concat(k.marketer_name)
                    from crm_client_principal as p
                    Left JOIN  crm_marketer as k ON k.marketer_id = p.marketer_id 
                    WHERE p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=1 and  p.school_id={$paramArray['school_id']} ) as marketer_name	
				,(select group_concat(DISTINCT o.coursecat_branch) FROM crm_client_intention as i 
                    LEFT JOIN smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
                    WHERE i.client_id=c.client_id ) as course_cnname  
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id  and s.is_enterstatus = '1'
				left join crm_code_channel as ch on ch.channel_id = c.channel_id 
				where {$datawhere}  group by c.client_id Having {$Having} ORDER BY {$sqlorder}";
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    //客户名称 年龄 主要联系电话（脱敏）意向星级 客户状态 主负责人 渠道类型 渠道明细 接触点 地推工号 推荐学员编号 意向课程 创建时间
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];//客户名称
                    $datearray['client_age'] = $dateexcelvar['client_age'];//年龄
                    $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);//主要联系电话
                    $datearray['client_email'] = $dateexcelvar['client_email'];//意向星级
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];//意向星级
                    $datearray['client_status'] = $clientTracestatus[$dateexcelvar['client_tracestatus']];//客户状态
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];//主负责人
                    $datearray['client_source'] = $dateexcelvar['client_source'];//渠道类型
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道明细
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];//接触点
                    $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber']?$dateexcelvar['promotion_jobnumber']:'--';//地推工号
                    $datearray['client_stubranch'] = $dateexcelvar['client_stubranch'];//推荐学员编号
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];//意向课程
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];//创建时间
                    $datearray['client_lineid'] = $dateexcelvar['client_lineid']?$dateexcelvar['client_lineid']:'--';
                    $datearray['client_facebookid'] = $dateexcelvar['client_facebookid']?$dateexcelvar['client_facebookid']:'--';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('客户名称', '年龄', '主要联系电话', 'Email', '意向星级', '客户状态', '主负责人', '渠道类型', '渠道明细', '接触点', '地推工号', '推荐学员编号', '意向课程', '创建时间', 'LineID', 'FacebookID'));
            $excelfileds = array('client_cnname', 'client_age', 'client_mobile', 'client_email', 'client_intention_level', 'client_status', 'marketer_name', 'client_source', 'channel_name', 'client_frompage','promotion_jobnumber', 'client_stubranch', 'course_cnname', 'client_createtime', 'client_lineid', 'client_facebookid');
            $fielname = $this->LgStringSwitch("意向客户记录表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {

            $sqlfields = 'c.client_id,c.activity_id,c.channel_id,c.promotion_id,c.client_img,c.client_actisagree,s.school_id,c.client_cnname,c.client_isnewtip,c.client_birthday,c.client_enname,c.client_sex,c.client_age,c.client_tag,c.client_answerphone,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,c.client_frompage,c.client_sponsor,c.client_teachername,c.client_oh_month,c.client_push_month,c.client_email,FROM_UNIXTIME(c.client_createtime) as client_createtime ,c.client_address,c.client_distributionstatus,c.client_tracestatus,c.client_remark,s.is_outmobile,ch.channel_id,ch.channel_name,ch.channel_medianame,ch.channel_minday,ch.channel_maxday';
            //,nr.nearschool_name,ch.channel_id,ch.channel_medianame,ch.channel_minday,ch.channel_maxday,sc.school_cnname
            $sql = "select {$sqlfields},{$havingsql} 
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id
                ,(select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id 
				,(select p.parenter_cnname
                    FROM crm_client_family as f
                    LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id
                    WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname
				,(select principal_id from crm_client_principal as ch where ch.client_id=c.client_id and marketer_id='{$paramArray['marketer_id']}' and principal_leave=0  and  ch.school_id={$paramArray['school_id']} limit 0,1 ) as is_principal_leave 
				,(select ch.channellog_id from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1) as channellog_id
				,(select group_concat(DISTINCT o.coursecat_branch) FROM crm_client_intention as i 
                    LEFT JOIN smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
                    WHERE i.client_id=c.client_id ) as course_cnname 
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id and s.is_enterstatus = '1'
				left join crm_code_channel as ch on ch.channel_id = c.channel_id 
				where {$datawhere}  group by c.client_id Having {$Having} ORDER BY {$sqlorder}";
            $sql .= "  LIMIT {$pagestart},{$num}";
            $clientList = $this->DataControl->selectClear($sql);

            if ($clientList) {
                foreach ($clientList as $key => &$value) {

                    $clientList[$key]['warninginfo'] = '';
                    if (isset($paramArray['overduetype']) && ($paramArray['overduetype'] == '2' || $paramArray['overduetype'] == '3') ){
                        if($levellist){
                            $trackdayArr = array_column($levellist,'intentlevel_trackday','intentlevel_starnum');
                            $warningdayArr = array_column($levellist,'intentlevel_warningday','intentlevel_starnum');

                            if (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '2'){//产品说 当天也算一天
                                $YuQiJieDian = strtotime(date('Y-m-d',$value['last_zhu_track_createtime'])) + 86399 + $trackdayArr[$value['client_intention_level']]*86400;
                                $YuQi =  $YuQiJieDian - time();
                                $yuqiday = ceil(abs($YuQi)/86400);
                                $clientList[$key]['warninginfo'] = "当前{$value['client_intention_level']}星名单逾期{$yuqiday}天跟踪，被标记低跟进名单，请尽快跟进！";
                            }elseif(isset($paramArray['overduetype']) && $paramArray['overduetype'] == '3'){//产品说 当天也算一天
                                $YuJingJieDian = strtotime(date('Y-m-d',$value['last_zhu_track_createtime'])) + 86399 +  ( $trackdayArr[$value['client_intention_level']] * 86400 );

                                $YuJing =  $YuJingJieDian - time();
                                $YuJingday = ceil($YuJing/86400);
                                $clientList[$key]['warninginfo'] = "当前{$value['client_intention_level']}星名单还剩{$YuJingday}天进入低跟进，请尽快跟进！";
                            }
                        }
                    }

                    if($value['promotion_id'] > 1){
                        $proOne = $this->DataControl->selectOne(" select promotion_jobnumber from crm_ground_promotion where promotion_id = '{$value['promotion_id']}' limit 0,1 ");
                        $clientList[$key]['promotion_jobnumber'] = $proOne['promotion_jobnumber'];
                    }else{
                        $clientList[$key]['promotion_jobnumber'] = '';
                    }
                    if($value['activity_id'] > 1){
                        $actOne = $this->DataControl->selectOne(" select activity_name from crm_sell_activity where activity_id = '{$value['activity_id']}' limit 0,1 ");
                        $clientList[$key]['activity_name'] = $actOne['activity_name'];
                    }else{
                        $clientList[$key]['activity_name'] = '';
                    }
                    if($value['nearschool_id'] > 1){
                        $proOne = $this->DataControl->selectOne(" select nearschool_name from crm_code_nearschool where nearschool_id = '{$value['nearschool_id']}' limit 0,1 ");
                        $clientList[$key]['nearschool_name'] = $proOne['nearschool_name'];
                    }else{
                        $clientList[$key]['nearschool_name'] = '';
                    }
                    //最后跟踪时间和内容
                    $tracktime = $this->DataControl->selectOne("select track_createtime,track_note from crm_client_track where client_id = '{$value['client_id']}' and track_isactive =1 order by track_createtime DESC limit 0,1");
                    $clientList[$key]['track_createtime'] = !is_null($tracktime['track_createtime']) ? date('Y-m-d H:i', $tracktime['track_createtime']):'--';
                    $clientList[$key]['track_note'] = (!is_null($tracktime['track_createtime']) ? date('Y-m-d H:i', $tracktime['track_createtime']) . ' ' : '') . ($tracktime['track_note'] == '' ? '--' : $tracktime['track_note']);

                    $main_marketer_name = $this->DataControl->selectOne(" select  group_concat(mk.marketer_name) as main_marketer_name 
                            from crm_client_principal as p
                            Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id 
                            WHERE p.client_id = '{$value['client_id']}' and p.principal_leave= 0 and p.principal_ismajor=1 and p.school_id={$paramArray['school_id']} 
                            ORDER by principal_id Desc");
                    $clientList[$key]['marketer_name'] = $main_marketer_name['main_marketer_name'];

                    $fu_marketer_name = $this->DataControl->selectOne(" select  group_concat(mk.marketer_name) as fu_marketer_name 
                            from crm_client_principal as p
                            Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
                            left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
                            WHERE p.client_id = '{$value['client_id']}' and p.principal_leave= 0 and p.principal_ismajor=0 and p.school_id={$paramArray['school_id']} ");
                    $clientList[$key]['fu_marketer_name'] = $fu_marketer_name['fu_marketer_name'];
                    
                    if ($value['client_enname']) {
                        $clientList[$key]['client_cnname'] = $value['client_cnname'] . '/' . $value['client_enname'];
                    }
                    $clientList[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                    $clientList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                    if ($value['client_tag']) {
                        $clientList[$key]['client_tag'] = explode(',', $this->LgStringSwitch($value['client_tag']));
                    } else {
                        $clientList[$key]['client_tag'] = array();
                    }
                    if (!is_null($value['course_cnname'])) {
                        $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                    } else {
                        $clientList[$key]['course_cnname'] = array();
                    }
                    if ($this->isGmcPost == true) {
                        $clientList[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    } else {
                        $clientList[$key]['client_mobile'] = $value['client_mobile'];
                    }
                    $clientList[$key]['client_intention_level'] = intval($value['client_intention_level']);
                    $clientList[$key]['is_principal_leave'] = intval($value['is_principal_leave']);
                    $clientList[$key]['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                    if ($clientList[$key]['client_tracestatus'] == 4 || $clientList[$key]['is_principal_leave'] == 0) {
                        $clientList[$key]['client_forbid'] = 0;   //禁止跟进操作
                    } else {
                        $clientList[$key]['client_forbid'] = 1;    //允许跟进操作
                    }
                    if ($paramArray['postbe_crmuserlevel'] == 1) {
                        $clientList[$key]['client_forbid'] = 1;    //高管账号允许跟进操作
                    }
                    if ($value['client_answerphone'] == 1) {
                        if ($value['client_intention_maxlevel'] >= 3) {
                            $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
                        } else {
                            $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
                        }
                    } else {
                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                    }
                    if ($companyOne['company_ispositive'] == 1) {
                        $clientList[$key]['is_positive'] = 1; // 允许
                    } else {
                        $clientList[$key]['is_positive'] = 0; //是否允许转正操作  不允许
                    }
                    $time = date('Y-m-d', time());
                    $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                    if ($value['channel_maxday'] == 0 || $time <= $max) {
                        $value['status'] = 1;
                    } else {
                        $value['status'] = 0;
                    }
                    if ($value['channellog_id'] != '' || $value['channel_id'] == '146' || $value['channel_id'] == '577') {
                        $value['apply'] = '0';
                    } else {
                        $value['apply'] = '1';
                    }
                    if ($value['is_outmobile'] == -1) {
                        $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                        if ($isoutCity) {
                            if ($isoutCity == 'local') {
                                $clientList[$key]['isoutmobile'] = 0;
                            } else {
                                $clientList[$key]['isoutmobile'] = 1;
                            }
                        } else {
                            $clientList[$key]['isoutmobile'] = -1;
                        }
                    } else {
                        $clientList[$key]['isoutmobile'] = $value['is_outmobile'];
                    }
                }
                $data['list'] = $clientList;
            } else {
                $data['list'] = array();
            }

            //提醒
            if (isset($paramArray['postbe_crmuserlevel']) && $paramArray['postbe_crmuserlevel'] == 1) {
                $datawhere = " 1  and c.client_distributionstatus = 1 and s.company_id ='{$paramArray['company_id']}' and s.school_id='{$paramArray['school_id']}' and p.principal_leave=0 and  c.client_ischaserlapsed =0 and c.client_tracestatus <> 4";
            } else {
                $datawhere = " 1 and c.client_distributionstatus = 1  and s.company_id ='{$paramArray['company_id']}' and s.school_id='{$paramArray['school_id']}' and mk.marketer_id='{$paramArray['marketer_id']}'  and p.principal_leave=0  and  c.client_ischaserlapsed =0 and c.client_tracestatus <> 4 ";
            }
            $num = $this->DataControl->selectClear("  
 				select {$tiphavingsql}c.client_id,c.client_intention_level
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id AND s.school_id = p.school_id
				where {$datawhere} and c.client_tracestatus <> '-1' and c.client_tracestatus <> '-2' and c.client_isnewtip = '1' 
				Group by c.client_id 
				Having {$tipHaving}
				");
            if ($num) {
                $notreadnum = count($num);
            } else {
                $notreadnum = '0';
            }
            $data['notreadnum'] = $notreadnum;

            return $data;
        }
    }
    /**
     * 获取意向客户列表  ---------  bak 这是 20240311 到 20240320 中间的一版
     * @param $paramArray
     * @return array
     */
    function getIntentionClientListBakTwo($paramArray)
    {
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_ispositive", "company_id='{$paramArray['company_id']}'");
        $datawhere = "c.company_id ='{$paramArray['company_id']}' and c.client_isgross = '0' and c.client_tracestatus > '-1' and p.school_id='{$paramArray['school_id']}' and p.principal_leave = 0  ";
        if (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] == 1 or $paramArray['postbe_crmuserlevel'] == 3)  ) {
            $datawhere .= " and  c.client_tracestatus <> 4";

            if (isset($paramArray['client_isnewtip']) && $paramArray['client_isnewtip']) {
                $datawhere .= "  and c.client_isnewtip = '{$paramArray['client_isnewtip']}'";
            }
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}' and c.client_distributionstatus = 1 and  c.client_tracestatus <> 4 ";
        }
        if (isset($paramArray['main_marketer_id']) && $paramArray['main_marketer_id']) {
            //同时支持主负责人 和 副负责人筛选
            $datawhere .= " and p.marketer_id = '{$paramArray['main_marketer_id']}' and principal_ismajor = 1";
        }
        if (isset($paramArray['fu_marketer_id']) && $paramArray['fu_marketer_id']) {
            $datawhere .= "  and p.marketer_id = '{$paramArray['fu_marketer_id']}' and principal_ismajor = 0";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '') {
            $datawhere .= " and c.channel_id ='{$paramArray['channel_id']}'";
        }
        if (isset($paramArray['client_source']) && $paramArray['client_source'] !== '') {
            $datawhere .= " and c.client_source ='{$paramArray['client_source']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '') {
            $datawhere .= " and c.client_source ='{$paramArray['frommedia_name']}'";
        }
        //首页隐藏参数，近七日未跟进的名单筛选
        if (isset($paramArray['nohavetrack']) && $paramArray['nohavetrack'] == '1') {
            $sevendaytime = strtotime(date("Ymd", strtotime("-6 days"))) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_id NOT IN ( SELECT t.client_id FROM crm_client_track AS t WHERE t.school_id = p.school_id AND t.track_isactive = '1' and t.track_isschooltmk = '0' AND t.track_createtime >= '{$sevendaytime}') ";
        }elseif(isset($paramArray['nohavetrack']) && $paramArray['nohavetrack'] == '0') {
            $sevendaytime = strtotime(date("Ymd", strtotime("-6 days"))) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_id IN ( SELECT t.client_id FROM crm_client_track AS t WHERE t.school_id = p.school_id AND t.track_isactive = '1' and t.track_isschooltmk = '0' AND t.track_createtime >= '{$sevendaytime}') ";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%'  or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['track_intention_level']) && $paramArray['track_intention_level'] !== '') {
            $datawhere .= " and c.client_intention_level ='{$paramArray['track_intention_level']}'";
        }
        //接通状态 0未接通 1接通有效 2接通无效
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone = '{$paramArray['client_answerphone']}'";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel >= 3";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel < 3";
        }
        if (isset($paramArray['toaday']) && $paramArray['today'] != "") {
            $today = date('Y-m-d');
            $datawhere .= " and FROM_UNIXTIME(c.client_updatetime,'%Y-%m-%d')  = '{$today}'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != "" && $paramArray['activity_id'] != 0) {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >='{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <='{$create_endtime}'";

        }
        if (isset($paramArray['client_updatetime']) && $paramArray['client_updatetime'] !== '') {
            $update_starttime = strtotime($paramArray['client_updatetime']);
            $datawhere .= " and c.client_updatetime >='{$update_starttime}'";
        }
        if (isset($paramArray['client_updatetime']) && $paramArray['client_updatetime'] !== '') {
            $update_endtime = strtotime($paramArray['client_updatetime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_updatetime <='{$update_endtime}'";
        }
        //手机端更新时间
        if (isset($paramArray['client_updatestarttime']) && $paramArray['client_updatestarttime'] !== '') {
            $update_starttime = strtotime($paramArray['client_updatestarttime']);

            $datawhere .= " and c.client_updatetime >='{$update_starttime}'";
        }
        if (isset($paramArray['client_updateendtime']) && $paramArray['client_updateendtime'] !== '') {
            $update_endtime = strtotime($paramArray['client_updateendtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_updatetime <='{$update_endtime}'";
        }
        //跟踪状态：0待跟踪1持续跟踪2已邀约3已视听4已转正-1无意向-2无效名单  -----------  状态就是当前状态
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$paramArray['client_tracestatus']}' ";
        }
        $Having = '1';
        $havingsql = ' ';
        if (isset($paramArray['track_createtime']) && $paramArray['track_createtime'] !== '') {
            $track_createtime = strtotime($paramArray['track_createtime']);
            $Having .= " and track_createtime >= '{$track_createtime}' ";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] !== '') {
            $track_endtime = strtotime($paramArray['track_endtime']) + 3600 * 24 - 1;
            $Having .= " and track_createtime <= '{$track_endtime}'  ";
        }
        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $Having .= " and active_invite_id is null and invite_idthree is null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree, ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $Having .= " and invite_idthree is not null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree, ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $Having .= " and active_invite_id is not null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,  ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null and audition_idthree is null  ";
            $havingsql .= " (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree, ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $Having .= " and audition_idthree is not null ";
            $havingsql .= "  (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree, ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $Having .= " and active_audition_id is not null ";
            $havingsql .= " (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id, ";
        }
        //跟进次数
        if (isset($paramArray['track_count']) && $paramArray['track_count'] != '') {
            $Having .= " and track_count <= '{$paramArray['track_count']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($paramArray['is_count']) and $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectClear("select c.client_id,{$havingsql}
 				(select st.track_createtime from crm_client_track as st   where st.client_id =c.client_id  and st.track_isactive =1 order by track_createtime DESC limit 0,1 ) as track_createtime, 
				(SELECT COUNT(t.track_id ) FROM crm_client_track t WHERE t.client_id = c.client_id and t.school_id = p.school_id and t.track_isactive = '1' and t.track_type = '0') as track_count 
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id 
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id AND s.school_id = p.school_id
				where {$datawhere}  Group by c.client_id
				HAVING {$Having}");
            if ($all_num) {
                $data['allnums'] = count($all_num);
            } else {
                $data['allnums'] = 0;
            }
        }
        $sqlfields = 'c.client_id,c.activity_id,c.channel_id,c.promotion_id,c.client_img,c.client_actisagree,s.school_id,c.client_cnname,c.client_isnewtip,c.client_img,c.client_birthday,c.client_enname,c.client_sex,c.client_age,c.client_tag,c.client_answerphone,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,c.client_frompage,c.client_sponsor,c.client_teachername,c.client_oh_month,c.client_push_month,c.client_email,FROM_UNIXTIME(c.client_createtime) as client_createtime ,c.client_address,c.client_distributionstatus,c.client_tracestatus,c.client_remark,s.is_outmobile,ch.channel_id,ch.channel_name,ch.channel_medianame,ch.channel_minday,ch.channel_maxday';//,nr.nearschool_name,ch.channel_id,ch.channel_medianame,ch.channel_minday,ch.channel_maxday,sc.school_cnname
        if (isset($paramArray['order']) && $paramArray['order'] == "") {
            $paramArray['order'] = "1";
        }
        if (isset($paramArray['order']) && $paramArray['order'] !== "1") {
            $sqlorder = "c.{$paramArray['order']} DESC";
        } else {
            $sqlorder = 'c.client_tracestatus ASC,c.client_intention_level,c.client_updatetime DESC';
        }
        $sql = "select {$sqlfields},{$havingsql}

                -- (select pt.promotion_jobnumber from crm_ground_promotion as pt where pt.promotion_id = c.promotion_id ) as promotion_jobnumber,
				-- (select sa.activity_name  from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name,
				-- (select  ch.channel_name  from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name,
				
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id, 
				(select p.parenter_cnname
				FROM crm_client_family as f
				LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id
				WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				
				-- (select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
                --     from crm_client_principal as p
                --     Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
                --     left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
                --     WHERE p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=1 and  p.school_id={$paramArray['school_id']} ORDER by principal_id Desc Limit 0,1) as marketer_name,
				-- (select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
                --     from crm_client_principal as p
                --     Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
                --     left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
                --     WHERE   p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=0 and  p.school_id={$paramArray['school_id']}  ) as fu_marketer_name,
				 
				(select principal_id from crm_client_principal as ch where ch.client_id=c.client_id and marketer_id='{$paramArray['marketer_id']}' and principal_leave=0  and  ch.school_id={$paramArray['school_id']} limit 0,1 ) as is_principal_leave ,
				-- (select r.region_name  from smc_code_region as r where c.province_id=r.region_id ) as province_name,
				-- (select r.region_name  from smc_code_region as r where c.city_id=r.region_id ) as city_name,
				-- (select r.region_name  from smc_code_region as r where c.area_id=r.region_id ) as area_name,
				(select ch.channellog_id from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1) as channellog_id,
				(select group_concat(DISTINCT o.coursecat_branch)
                    FROM crm_client_intention as i
                    LEFT JOIN smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
                    WHERE i.client_id=c.client_id ) as course_cnname,
				
				(select st.track_createtime from crm_client_track as st   where st.client_id =c.client_id  and st.track_isactive =1 order by track_createtime DESC limit 0,1 ) as track_createtime,
				(select st.track_note from crm_client_track as st  where st.client_id =c.client_id and  st.track_isactive =1  order by track_createtime DESC  limit 0,1) as track_note,
				(SELECT COUNT(t.track_id ) FROM crm_client_track t WHERE t.client_id = c.client_id and t.school_id = p.school_id and t.track_isactive = '1' and t.track_type = '0') as track_count
				-- ,(SELECT invite_visittime  FROM  crm_client_invite as ci WHERE ci.client_id= c.client_id and ci.school_id = s.school_id  order by  invite_id DESC limit 0,1 ) as  invite_visittime,
				-- IFNULL((SELECT i.intentlevel_remark FROM gmc_code_intentlevel AS i WHERE i.company_id='{$paramArray['company_id']}' AND i.intentlevel_starnum=c.client_intention_level),c.client_intention_level) AS client_intentlevel_remark
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id
				-- LEFT JOIN crm_code_nearschool as nr ON nr.nearschool_id =c.nearschool_id
				left join crm_code_channel as ch on ch.channel_id = c.channel_id
				-- left join smc_school as sc on sc.school_id = s.school_id
				where {$datawhere}  group by c.client_id Having {$Having} ORDER BY {$sqlorder}";
//        echo $sql;die;
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            foreach ($dateexcelarray as $key => $value) {
                if($value['promotion_id'] > 1){
                    $proOne = $this->DataControl->selectOne(" select promotion_jobnumber from crm_ground_promotion where promotion_id = '{$value['promotion_id']}' limit 0,1 ");
                    $dateexcelarray[$key]['promotion_jobnumber'] = $proOne['promotion_jobnumber'];
                }else{
                    $dateexcelarray[$key]['promotion_jobnumber'] = '';
                }
                if($value['activity_id'] > 1){
                    $actOne = $this->DataControl->selectOne(" select activity_name from crm_sell_activity where activity_id = '{$value['activity_id']}' limit 0,1 ");
                    $dateexcelarray[$key]['activity_name'] = $actOne['activity_name'];
                }else{
                    $dateexcelarray[$key]['activity_name'] = '';
                }
//                if($value['channel_id'] > 1){
//                    $proOne = $this->DataControl->selectOne(" select channel_name from crm_code_channel where channel_id = '{$value['channel_id']}' limit 0,1 ");
//                    $dateexcelarray[$key]['channel_name'] = $proOne['channel_name'];
//                }else{
//                    $dateexcelarray[$key]['channel_name'] = '';
//                }
                if($value['nearschool_id'] > 1){
                    $proOne = $this->DataControl->selectOne(" select nearschool_name from crm_code_nearschool where nearschool_id = '{$value['nearschool_id']}' limit 0,1 ");
                    $dateexcelarray[$key]['nearschool_name'] = $proOne['nearschool_name'];
                }else{
                    $dateexcelarray[$key]['nearschool_name'] = '';
                }

                $dateexcelarray[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                $dateexcelarray[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                if ($value['track_createtime']) {
                    $dateexcelarray[$key]['track_createtime'] = date('Y-m-d H:i', $value['track_createtime']);
                } else {
                    $dateexcelarray[$key]['track_createtime'] = '--';
                }
                $dateexcelarray[$key]['client_intention_level'] = intval($value['client_intention_level']);
                $dateexcelarray[$key]['is_principal_leave'] = intval($value['is_principal_leave']);
                $dateexcelarray[$key]['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                if ($value['client_answerphone'] == 1) {
                    if ($value['client_intention_maxlevel'] >= 3) {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
                    } else {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
                    }
                } else {
                    $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_tag'] = $dateexcelvar['client_tag'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    if ($paramArray['re_postbe_id'] == 0) {
                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    } else {
                        $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);
                    }
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['client_status'] = $dateexcelvar['client_status'];
                    $datearray['track_count'] = $dateexcelvar['track_count'];
                    $datearray['client_answerphone'] = $dateexcelvar['client_answerphone'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['city_name'] = $dateexcelvar['city_name'];
                    $datearray['area_name'] = $dateexcelvar['area_name'];
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['nearschool_name'] = $dateexcelvar['nearschool_name'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 0) {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '标签', '主要联系人', '主要联系手机', '意向星级', '跟进次数', '客户状态', '接通状态', '主要负责人', '活动', '渠道类型', '渠道明细', '接触点', '联系地址', "省", "市", "区", "推荐人", '意向课程', '创建时间', '最后跟踪时间', '最后跟踪内容', '附近学校', '备注'));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'family_cnname', 'client_mobile', 'client_intention_level', 'track_count', 'client_status', 'client_answerphone', 'marketer_name', 'activity_name', 'client_source', 'channel_name', 'client_frompage', 'client_address', 'province_name', 'city_name', 'area_name', 'client_sponsor', 'course_cnname', 'client_createtime', 'track_createtime', 'track_note', 'nearschool_name', 'client_remark');
            } else {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '标签', '主要联系人', '主要联系手机', '意向星级', '跟进次数', '客户状态', '接通状态', '主要负责人', '协助负责人', '活动', '渠道类型', '渠道明细','接触点',  '联系地址', "省", "市", "区", "推荐人", '意向课程', '创建时间', '最后跟踪时间', '最后跟踪内容', '附近学校', '备注'));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'family_cnname', 'client_mobile', 'client_intention_level', 'client_status', 'track_count', 'client_answerphone', 'marketer_name', 'fu_marketer_name', 'activity_name', 'client_source', 'channel_name', 'client_frompage', 'client_address', 'province_name', 'city_name', 'area_name', 'client_sponsor', 'course_cnname', 'client_createtime', 'track_createtime', 'track_note', 'nearschool_name', 'client_remark');
            }
            $fielname = $this->LgStringSwitch("意向客户记录表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= "  LIMIT {$pagestart},{$num}";
            $clientList = $this->DataControl->selectClear($sql);

            if ($clientList) {
                foreach ($clientList as $key => &$value) {
                    if($value['promotion_id'] > 1){
                        $proOne = $this->DataControl->selectOne(" select promotion_jobnumber from crm_ground_promotion where promotion_id = '{$value['promotion_id']}' limit 0,1 ");
                        $clientList[$key]['promotion_jobnumber'] = $proOne['promotion_jobnumber'];
                    }else{
                        $clientList[$key]['promotion_jobnumber'] = '';
                    }
                    if($value['activity_id'] > 1){
                        $actOne = $this->DataControl->selectOne(" select activity_name from crm_sell_activity where activity_id = '{$value['activity_id']}' limit 0,1 ");
                        $clientList[$key]['activity_name'] = $actOne['activity_name'];
                    }else{
                        $clientList[$key]['activity_name'] = '';
                    }
                    if($value['channel_id'] > 1){
                        $proOne = $this->DataControl->selectOne(" select channel_name from crm_code_channel where channel_id = '{$value['channel_id']}' limit 0,1 ");
                        $clientList[$key]['channel_name'] = $proOne['channel_name'];
                    }else{
                        $clientList[$key]['channel_name'] = '';
                    }
                    if($value['nearschool_id'] > 1){
                        $proOne = $this->DataControl->selectOne(" select nearschool_name from crm_code_nearschool where nearschool_id = '{$value['nearschool_id']}' limit 0,1 ");
                        $clientList[$key]['nearschool_name'] = $proOne['nearschool_name'];
                    }else{
                        $clientList[$key]['nearschool_name'] = '';
                    }

                    if ($value['client_enname']) {
                        $clientList[$key]['client_cnname'] = $value['client_cnname'] . '/' . $value['client_enname'];
                    }
                    $clientList[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                    $clientList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                    if ($value['client_tag']) {
                        $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                    } else {
                        $clientList[$key]['client_tag'] = array();
                    }
                    if (!is_null($value['course_cnname'])) {
                        $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                    } else {
                        $clientList[$key]['course_cnname'] = array();
                    }
                    if ($this->isGmcPost == true) {
                        $clientList[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    } else {
                        $clientList[$key]['client_mobile'] = $value['client_mobile'];
                    }
                    $clientList[$key]['client_intention_level'] = intval($value['client_intention_level']);
                    $clientList[$key]['track_note'] = (!is_null($value['track_createtime']) ? date('Y-m-d H:i', $value['track_createtime']) . ' ' : '') . ($value['track_note'] == '' ? '--' : $value['track_note']);
                    $clientList[$key]['is_principal_leave'] = intval($value['is_principal_leave']);
                    $clientList[$key]['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                    if ($clientList[$key]['client_tracestatus'] == 4 || $clientList[$key]['is_principal_leave'] == 0) {
                        $clientList[$key]['client_forbid'] = 0;   //禁止跟进操作
                    } else {
                        $clientList[$key]['client_forbid'] = 1;    //允许跟进操作
                    }
                    if ($paramArray['postbe_crmuserlevel'] == 1) {
                        $clientList[$key]['client_forbid'] = 1;    //高管账号允许跟进操作
                    }
                    if ($value['track_createtime']) {
                        $clientList[$key]['track_createtime'] = date('Y-m-d H:i', $value['track_createtime']);
                    } else {
                        $clientList[$key]['track_createtime'] = '--';
                    }
                    if ($value['client_answerphone'] == 1) {
                        if ($value['client_intention_maxlevel'] >= 3) {
                            $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
                        } else {
                            $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
                        }
                    } else {
                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                    }
                    if ($companyOne['company_ispositive'] == 1) {
                        $clientList[$key]['is_positive'] = 1; // 允许
                    } else {
                        $clientList[$key]['is_positive'] = 0; //是否允许转正操作  不允许
                    }
                    $time = date('Y-m-d', time());
                    $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                    if ($value['channel_maxday'] == 0 || $time <= $max) {
                        $value['status'] = 1;
                    } else {
                        $value['status'] = 0;
                    }
                    if ($value['channellog_id'] != '' || $value['channel_id'] == '146' || $value['channel_id'] == '577') {
                        $value['apply'] = '0';
                    } else {
                        $value['apply'] = '1';
                    }
                    if ($value['is_outmobile'] == -1) {
                        $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                        if ($isoutCity) {
                            if ($isoutCity == 'local') {
                                $clientList[$key]['isoutmobile'] = 0;
                            } else {
                                $clientList[$key]['isoutmobile'] = 1;
                            }
                        } else {
                            $clientList[$key]['isoutmobile'] = -1;
                        }
                    } else {
                        $clientList[$key]['isoutmobile'] = $value['is_outmobile'];
                    }
                }
                $data['list'] = $clientList;
            } else {
                $data['list'] = array();
            }
            return $data;
        }
    }
    /**
     * 获取意向客户列表  ---------  bak 这是 20240311 时候处理复制出来的之前的 程序
     * @param $paramArray
     * @return array
     */
    function getIntentionClientListBak($paramArray)
    {
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_ispositive", "company_id='{$paramArray['company_id']}'");
        $datawhere = "s.company_id ='{$paramArray['company_id']}' and p.school_id='{$paramArray['school_id']}' and p.principal_leave = 0  and c.client_isgross = '0' and c.client_tracestatus > '-1' ";
//        $datawhere = "c.company_id ='{$paramArray['company_id']}' and s.school_id='{$paramArray['school_id']}' and s.is_enterstatus = '1'  and c.client_isgross = '0' and c.client_tracestatus > '-1' ";
        if (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] == 1 or $paramArray['postbe_crmuserlevel'] == 3)  ) {
            $datawhere .= " and  c.client_tracestatus <> 4";

            if (isset($paramArray['client_isnewtip']) && $paramArray['client_isnewtip']) {
                $datawhere .= "  and c.client_isnewtip = '{$paramArray['client_isnewtip']}'";
            }
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}' and c.client_distributionstatus = 1 and  c.client_tracestatus <> 4 ";
        }
        if (isset($paramArray['main_marketer_id']) && $paramArray['main_marketer_id']) {
            //同时支持主负责人 和 副负责人筛选
            $datawhere .= " and p.marketer_id = '{$paramArray['main_marketer_id']}' and principal_ismajor = 1";
        }
        if (isset($paramArray['fu_marketer_id']) && $paramArray['fu_marketer_id']) {
            $datawhere .= "  and p.marketer_id = '{$paramArray['fu_marketer_id']}' and principal_ismajor = 0";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '') {
            $datawhere .= " and c.channel_id ='{$paramArray['channel_id']}'";
        }
        if (isset($paramArray['client_source']) && $paramArray['client_source'] !== '') {
            $datawhere .= " and c.client_source ='{$paramArray['client_source']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '') {
            $datawhere .= " and c.client_source ='{$paramArray['frommedia_name']}'";
        }
        //首页隐藏参数，近七日未跟进的名单筛选
        if (isset($paramArray['nohavetrack']) && $paramArray['nohavetrack'] == '1') {
            $sevendaytime = strtotime(date("Ymd", strtotime("-6 days"))) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_id NOT IN ( SELECT t.client_id FROM crm_client_track AS t WHERE t.school_id = p.school_id AND t.track_isactive = '1' and t.track_isschooltmk = '0' AND t.track_createtime >= '{$sevendaytime}') ";
        }
//        //同之前
//        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '' && $paramArray['channel_id'] != '[]') {
//            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
//            if (is_array($channelArray) && count($channelArray) > 0) {
//                $channelstr = '';
//                foreach ($channelArray as $channelvar) {
//                    $channelstr .= "'" . $channelvar . "'" . ',';
//                }
//                $channelstr = substr($channelstr, 0, -1);
//                $datawhere .= " and c.channel_id in ({$channelstr}) ";
//            }
//        }
//        //同之前
//        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '' && $paramArray['frommedia_name'] != '[]') {
//            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
//            if (is_array($commodeArray) && count($commodeArray) > 0) {
//                $commodestr = '';
//                foreach ($commodeArray as $commodevar) {
//                    $commodestr .= "'" . $commodevar . "'" . ',';
//                }
//                $commodestr = substr($commodestr, 0, -1);
//                $datawhere .= " and c.client_source in ({$commodestr}) ";
//            }
//        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%'  or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['track_intention_level']) && $paramArray['track_intention_level'] !== '') {
            $datawhere .= " and c.client_intention_level ='{$paramArray['track_intention_level']}'";
        }

        //接通状态 0未接通 1接通有效 2接通无效
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone = '{$paramArray['client_answerphone']}'";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel >= 3";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel < 3";
        }

        if (isset($paramArray['toaday']) && $paramArray['today'] != "") {
            $today = date('Y-m-d');
            $datawhere .= " and FROM_UNIXTIME(c.client_updatetime,'%Y-%m-%d')  = '{$today}'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != "" && $paramArray['activity_id'] != 0) {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >='{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <='{$create_endtime}'";

        }
        if (isset($paramArray['client_updatetime']) && $paramArray['client_updatetime'] !== '') {
            $update_starttime = strtotime($paramArray['client_updatetime']);
            $datawhere .= " and c.client_updatetime >='{$update_starttime}'";
        }
        if (isset($paramArray['client_updatetime']) && $paramArray['client_updatetime'] !== '') {
            $update_endtime = strtotime($paramArray['client_updatetime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_updatetime <='{$update_endtime}'";
        }
        //手机端更新时间
        if (isset($paramArray['client_updatestarttime']) && $paramArray['client_updatestarttime'] !== '') {
            $update_starttime = strtotime($paramArray['client_updatestarttime']);

            $datawhere .= " and c.client_updatetime >='{$update_starttime}'";
        }
        if (isset($paramArray['client_updateendtime']) && $paramArray['client_updateendtime'] !== '') {
            $update_endtime = strtotime($paramArray['client_updateendtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_updatetime <='{$update_endtime}'";
        }
        $Having = '1';
        if (isset($paramArray['track_createtime']) && $paramArray['track_createtime'] !== '') {
            $track_createtime = strtotime($paramArray['track_createtime']);
            $Having .= " and track_createtime >= '{$track_createtime}' ";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] !== '') {
            $track_endtime = strtotime($paramArray['track_endtime']) + 3600 * 24 - 1;
            $Having .= " and track_createtime <= '{$track_endtime}'  ";
        }
        //跟踪状态：0待跟踪1持续跟踪2已邀约3已视听4已转正-1无意向-2无效名单  -----------  状态就是当前状态
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$paramArray['client_tracestatus']}' ";
        }
        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $Having .= " and active_invite_id is null and invite_idthree is null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $Having .= " and invite_idthree is not null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $Having .= " and active_invite_id is not null ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null and audition_idthree is null  ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $Having .= " and audition_idthree is not null ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $Having .= " and active_audition_id is not null ";
        }
        //跟进次数
        if (isset($paramArray['track_count']) && $paramArray['track_count'] != '') {
            $Having .= " and track_count <= '{$paramArray['track_count']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($paramArray['is_count']) and $paramArray['is_count'] != "") {
//            $all_num = $this->DataControl->selectClear("select c.client_id,
//                (SELECT sp.principal_id from crm_client_principal as sp WHERE sp.client_id = c.client_id and sp.school_id = s.school_id and sp.principal_leave = '0' limit 0,1) as principalOne ,
//                (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,
//                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
//                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
//                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
//                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree,
// 				(select st.track_createtime from crm_client_track as st   where st.client_id =c.client_id  and st.track_isactive =1 order by track_createtime DESC limit 0,1 ) as track_createtime,
//				(SELECT COUNT(t.track_id ) FROM crm_client_track t WHERE t.client_id = c.client_id and t.school_id = s.school_id and t.track_isactive = '1' and t.track_type = '0') as track_count
//				from  crm_client as c
//                Left JOIN  crm_client_schoolenter as s ON s.client_id=c.client_id
//                LEFT JOIN crm_code_nearschool as nr ON nr.nearschool_id =c.nearschool_id
//                left join crm_code_channel as ch on ch.channel_id = c.channel_id
//                left join smc_school as sc on sc.school_id = s.school_id
//				where {$datawhere}  Group by c.client_id
//				HAVING {$Having} and principalOne > 0 ");

            $all_num = $this->DataControl->selectClear("select c.client_id,
                (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree,
 				(select st.track_createtime from crm_client_track as st   where st.client_id =c.client_id  and st.track_isactive =1 order by track_createtime DESC limit 0,1 ) as track_createtime, 
				(SELECT COUNT(t.track_id ) FROM crm_client_track t WHERE t.client_id = c.client_id and t.school_id = p.school_id and t.track_isactive = '1' and t.track_type = '0') as track_count 
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id AND s.school_id = p.school_id
				where {$datawhere}  Group by c.client_id
				HAVING {$Having}");

//            (SELECT COUNT(t.track_id ) FROM crm_client_principal p,crm_client_track t WHERE p.client_id = c.client_id and p.principal_leave = '0' and t.client_id = p.client_id and t.marketer_id = p.marketer_id and t.track_isactive = '1' and t.track_createtime > p.principal_createtime) as track_count

            if ($all_num) {
                $data['allnums'] = count($all_num);
            } else {
                $data['allnums'] = 0;
            }
        }
        $sqlfields = 'c.client_id,c.promotion_id,c.client_img,c.client_actisagree,s.school_id,c.client_cnname,c.client_isnewtip,c.client_img,c.client_birthday,c.client_enname,c.client_sex,c.client_age,c.client_tag,c.client_answerphone,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,c.client_frompage,c.client_sponsor,c.client_teachername,c.client_oh_month,c.client_push_month,c.client_email,FROM_UNIXTIME(c.client_createtime) as client_createtime ,c.client_address,c.client_distributionstatus,c.client_tracestatus,c.client_remark,nr.nearschool_name,ch.channel_id,ch.channel_medianame,ch.channel_minday,ch.channel_maxday,sc.school_cnname';
        if (isset($paramArray['order']) && $paramArray['order'] == "") {
            $paramArray['order'] = "1";
        }
        if (isset($paramArray['order']) && $paramArray['order'] !== "1") {
            $sqlorder = "c.{$paramArray['order']} DESC";
        } else {
            $sqlorder = 'c.client_tracestatus ASC,c.client_intention_level,c.client_updatetime DESC';
        }
//        $sql = "select {$sqlfields},s.is_outmobile,
//                (SELECT sp.principal_id from crm_client_principal as sp WHERE sp.client_id = c.client_id and sp.school_id = s.school_id and sp.principal_leave = '0' limit 0,1) as principalOne ,
//                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
//                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id,
//                (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,
//                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
//                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
//                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
//                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree,
//				(select p.parenter_cnname
//				FROM crm_client_family as f
//				LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id
//				WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
//				(select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
//				from crm_client_principal as p
//				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
//				left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
//				WHERE p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=1 and  p.school_id={$paramArray['school_id']} ORDER by principal_id Desc Limit 0,1) as marketer_name,
//				(select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
//				from crm_client_principal as p
//				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
//				left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
//				 WHERE   p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=0 and  p.school_id={$paramArray['school_id']}  ) as fu_marketer_name,
//				 (SELECT invite_visittime  FROM  crm_client_invite as ci WHERE ci.client_id= c.client_id and ci.school_id = s.school_id  order by  invite_id DESC limit 0,1 ) as  invite_visittime,
//				 (select sa.activity_name  from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name,
//				 (select  ch.channel_name  from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name,
//				 (select principal_id from crm_client_principal as ch where ch.client_id=c.client_id and marketer_id='{$paramArray['marketer_id']}' and principal_leave=0  and  ch.school_id={$paramArray['school_id']} limit 0,1 ) as is_principal_leave ,
//				 (select st.track_createtime from crm_client_track as st   where st.client_id =c.client_id  and st.track_isactive =1 order by track_createtime DESC limit 0,1 ) as track_createtime,
//				 (select st.track_note from crm_client_track as st  where st.client_id =c.client_id and  st.track_isactive =1  order by track_createtime DESC  limit 0,1) as track_note,
//				(select r.region_name  from smc_code_region as r where c.province_id=r.region_id ) as province_name,
//				(select r.region_name  from smc_code_region as r where c.city_id=r.region_id ) as city_name,
//				(select r.region_name  from smc_code_region as r where c.area_id=r.region_id ) as area_name,
//				(select ch.channellog_id from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1) as channellog_id,
//				IFNULL((SELECT i.intentlevel_remark FROM gmc_code_intentlevel AS i WHERE i.company_id='{$paramArray['company_id']}' AND i.intentlevel_starnum=c.client_intention_level),c.client_intention_level) AS client_intentlevel_remark,
//				 (select group_concat(DISTINCT o.coursecat_branch)
//				FROM crm_client_intention as i
//				LEFT JOIN smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
//				WHERE i.client_id=c.client_id ) as course_cnname,
//				(SELECT COUNT(t.track_id ) FROM crm_client_track t WHERE t.client_id = c.client_id and t.school_id = s.school_id and t.track_isactive = '1' and t.track_type = '0') as track_count
//				from  crm_client as c
//                Left JOIN  crm_client_schoolenter as s ON s.client_id=c.client_id
//                LEFT JOIN crm_code_nearschool as nr ON nr.nearschool_id =c.nearschool_id
//                left join crm_code_channel as ch on ch.channel_id = c.channel_id
//                left join smc_school as sc on sc.school_id = s.school_id
//				where {$datawhere}  group by c.client_id Having {$Having} and principalOne > 0 ORDER BY {$sqlorder}";


        $sql = "select {$sqlfields},s.is_outmobile,
                (select pt.promotion_jobnumber from crm_ground_promotion as pt where pt.promotion_id = c.promotion_id ) as promotion_jobnumber,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id,
                (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree,
				(select p.parenter_cnname
				FROM crm_client_family as f
				LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id
				WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				(select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
				from crm_client_principal as p
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
				WHERE p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=1 and  p.school_id={$paramArray['school_id']} ORDER by principal_id Desc Limit 0,1) as marketer_name,
				(select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
				from crm_client_principal as p
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
				 WHERE   p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=0 and  p.school_id={$paramArray['school_id']}  ) as fu_marketer_name,
				 (SELECT invite_visittime  FROM  crm_client_invite as ci WHERE ci.client_id= c.client_id and ci.school_id = s.school_id  order by  invite_id DESC limit 0,1 ) as  invite_visittime,
				 (select sa.activity_name  from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name,
				 (select  ch.channel_name  from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name,
				 (select principal_id from crm_client_principal as ch where ch.client_id=c.client_id and marketer_id='{$paramArray['marketer_id']}' and principal_leave=0  and  ch.school_id={$paramArray['school_id']} limit 0,1 ) as is_principal_leave ,
				 (select st.track_createtime from crm_client_track as st   where st.client_id =c.client_id  and st.track_isactive =1 order by track_createtime DESC limit 0,1 ) as track_createtime,
				 (select st.track_note from crm_client_track as st  where st.client_id =c.client_id and  st.track_isactive =1  order by track_createtime DESC  limit 0,1) as track_note,
				(select r.region_name  from smc_code_region as r where c.province_id=r.region_id ) as province_name,
				(select r.region_name  from smc_code_region as r where c.city_id=r.region_id ) as city_name,
				(select r.region_name  from smc_code_region as r where c.area_id=r.region_id ) as area_name,
				(select ch.channellog_id from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1) as channellog_id,
				IFNULL((SELECT i.intentlevel_remark FROM gmc_code_intentlevel AS i WHERE i.company_id='{$paramArray['company_id']}' AND i.intentlevel_starnum=c.client_intention_level),c.client_intention_level) AS client_intentlevel_remark,
				 (select group_concat(DISTINCT o.coursecat_branch)
				FROM crm_client_intention as i
				LEFT JOIN smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
				WHERE i.client_id=c.client_id ) as course_cnname,
				(SELECT COUNT(t.track_id ) FROM crm_client_track t WHERE t.client_id = c.client_id and t.school_id = p.school_id and t.track_isactive = '1' and t.track_type = '0') as track_count
				from  crm_client as c
				Left JOIN  crm_client_principal as  p ON p.client_id = c.client_id
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				Left JOIN  crm_client_schoolenter as s  ON s.client_id=c.client_id and p.school_id=s.school_id
				LEFT JOIN crm_code_nearschool as nr ON nr.nearschool_id =c.nearschool_id
				left join crm_code_channel as ch on ch.channel_id = c.channel_id
				left join smc_school as sc on sc.school_id = s.school_id
				where {$datawhere}  group by c.client_id Having {$Having} ORDER BY {$sqlorder}";
//echo $sql;die;
//        (SELECT COUNT(t.track_id ) FROM crm_client_principal p,crm_client_track t WHERE p.client_id = c.client_id and p.principal_leave = '0' and t.client_id = p.client_id and t.marketer_id = p.marketer_id and t.track_isactive = '1') as track_count
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }

            foreach ($dateexcelarray as $key => $value) {
                $dateexcelarray[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                $dateexcelarray[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                if ($value['track_createtime']) {
                    $dateexcelarray[$key]['track_createtime'] = date('Y-m-d H:i', $value['track_createtime']);
                } else {
                    $dateexcelarray[$key]['track_createtime'] = '--';
                }
                $dateexcelarray[$key]['client_intention_level'] = intval($value['client_intention_level']);
                $dateexcelarray[$key]['is_principal_leave'] = intval($value['is_principal_leave']);
                $dateexcelarray[$key]['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                if ($value['client_answerphone'] == 1) {
                    if ($value['client_intention_maxlevel'] >= 3) {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
                    } else {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
                    }
                } else {
                    $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_tag'] = $dateexcelvar['client_tag'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    if ($paramArray['re_postbe_id'] == 0) {
                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    } else {
//                        $datearray['client_mobile'] = str_replace(substr($dateexcelvar['client_mobile'], 3, 4), '****', $dateexcelvar['client_mobile']);
                        $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);
                    }
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['client_status'] = $dateexcelvar['client_status'];
                    $datearray['track_count'] = $dateexcelvar['track_count'];
                    $datearray['client_answerphone'] = $dateexcelvar['client_answerphone'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['city_name'] = $dateexcelvar['city_name'];
                    $datearray['area_name'] = $dateexcelvar['area_name'];
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['nearschool_name'] = $dateexcelvar['nearschool_name'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 0) {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '标签', '主要联系人', '主要联系手机', '意向星级', '跟进次数', '客户状态', '接通状态', '主要负责人', '活动', '渠道类型', '渠道明细', '接触点', '联系地址', "省", "市", "区", "推荐人", '意向课程', '创建时间', '最后跟踪时间', '最后跟踪内容', '附近学校', '备注'));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'family_cnname', 'client_mobile', 'client_intention_level', 'track_count', 'client_status', 'client_answerphone', 'marketer_name', 'activity_name', 'client_source', 'channel_name', 'client_frompage', 'client_address', 'province_name', 'city_name', 'area_name', 'client_sponsor', 'course_cnname', 'client_createtime', 'track_createtime', 'track_note', 'nearschool_name', 'client_remark');
            } else {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '标签', '主要联系人', '主要联系手机', '意向星级', '跟进次数', '客户状态', '接通状态', '主要负责人', '协助负责人', '活动', '渠道类型', '渠道明细','接触点',  '联系地址', "省", "市", "区", "推荐人", '意向课程', '创建时间', '最后跟踪时间', '最后跟踪内容', '附近学校', '备注'));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'family_cnname', 'client_mobile', 'client_intention_level', 'client_status', 'track_count', 'client_answerphone', 'marketer_name', 'fu_marketer_name', 'activity_name', 'client_source', 'channel_name', 'client_frompage', 'client_address', 'province_name', 'city_name', 'area_name', 'client_sponsor', 'course_cnname', 'client_createtime', 'track_createtime', 'track_note', 'nearschool_name', 'client_remark');
            }

            $fielname = $this->LgStringSwitch("意向客户记录表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= "  LIMIT {$pagestart},{$num}";
            $clientList = $this->DataControl->selectClear($sql);

            if ($clientList) {
                foreach ($clientList as $key => &$value) {
                    if ($value['client_enname']) {
                        $clientList[$key]['client_cnname'] = $value['client_cnname'] . '/' . $value['client_enname'];
                    }
                    $clientList[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                    $clientList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                    if ($value['client_tag']) {
                        $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                    } else {
                        $clientList[$key]['client_tag'] = array();
                    }
                    if (!is_null($value['course_cnname'])) {
                        $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                    } else {
                        $clientList[$key]['course_cnname'] = array();
                    }

                    if ($this->isGmcPost == true) {
                        $clientList[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    } else {
                        $clientList[$key]['client_mobile'] = $value['client_mobile'];
                    }
                    $clientList[$key]['client_intention_level'] = intval($value['client_intention_level']);
                    $clientList[$key]['track_note'] = (!is_null($value['track_createtime']) ? date('Y-m-d H:i', $value['track_createtime']) . ' ' : '') . ($value['track_note'] == '' ? '--' : $value['track_note']);
                    $clientList[$key]['is_principal_leave'] = intval($value['is_principal_leave']);
                    $clientList[$key]['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                    if ($clientList[$key]['client_tracestatus'] == 4 || $clientList[$key]['is_principal_leave'] == 0) {
                        $clientList[$key]['client_forbid'] = 0;   //禁止跟进操作
                    } else {
                        $clientList[$key]['client_forbid'] = 1;    //允许跟进操作
                    }
                    if ($paramArray['postbe_crmuserlevel'] == 1) {
                        $clientList[$key]['client_forbid'] = 1;    //高管账号允许跟进操作
                    }
                    if ($value['track_createtime']) {
                        $clientList[$key]['track_createtime'] = date('Y-m-d H:i', $value['track_createtime']);
                    } else {
                        $clientList[$key]['track_createtime'] = '--';
                    }

                    if ($value['client_answerphone'] == 1) {
                        if ($value['client_intention_maxlevel'] >= 3) {
                            $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
                        } else {
                            $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
                        }
                    } else {
                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                    }

                    if ($companyOne['company_ispositive'] == 1) {
                        $clientList[$key]['is_positive'] = 1; // 允许
                    } else {
                        $clientList[$key]['is_positive'] = 0; //是否允许转正操作  不允许
                    }
                    $time = date('Y-m-d', time());
//                    $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                    $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));

                    if ($value['channel_maxday'] == 0 || $time <= $max) {
                        $value['status'] = 1;
                    } else {
                        $value['status'] = 0;
                    }
                    if ($value['channellog_id'] != '' || $value['channel_id'] == '146' || $value['channel_id'] == '577') {
                        $value['apply'] = '0';
                    } else {
                        $value['apply'] = '1';
                    }

                    if ($value['is_outmobile'] == -1) {
                        $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                        if ($isoutCity) {
                            if ($isoutCity == 'local') {
                                $clientList[$key]['isoutmobile'] = 0;
                            } else {
                                $clientList[$key]['isoutmobile'] = 1;
                            }
                        } else {
                            $clientList[$key]['isoutmobile'] = -1;
                        }
                    } else {
                        $clientList[$key]['isoutmobile'] = $value['is_outmobile'];
                    }
                }

                $data['list'] = $clientList;
            } else {
                $data['list'] = array();
            }
            return $data;
        }
    }

    /**
     * 获取电销名单跟进表
     * @param $paramArray
     * @return array
     */
    function getOneTmkClient($paramArray)
    {

        $datawhere = "c.company_id ='{$paramArray['company_id']}' and p.school_id='{$paramArray['school_id']}' and p.tmkprincipal_leave = 0 and c.client_isgross = '1' ";

        if (isset($paramArray['postbe_crmuserlevel']) && $paramArray['postbe_crmuserlevel'] == 1) {
            $datawhere .= " and  c.client_tracestatus <> 4";
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}' and c.client_schtmkdistributionstatus = 1 and  c.client_tracestatus <> 4 ";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >='{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <='{$create_endtime}'";
        }
        if (isset($paramArray['track_intention_level']) && $paramArray['track_intention_level'] !== '') {
            $datawhere .= " and c.client_intention_level ='{$paramArray['track_intention_level']}'";
        }
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$paramArray['client_tracestatus']}' ";
        }
        //接通状态 0未接通 1接通有效 2接通无效
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone = '{$paramArray['client_answerphone']}'";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel >= 3";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel < 3";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != "" && $paramArray['activity_id'] != 0) {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        //首页隐藏参数，近七日未跟进的名单筛选
        if (isset($paramArray['nohavetrack']) && $paramArray['nohavetrack'] == '1') {
            $sevendaytime = strtotime(date("Ymd", strtotime("-6 days"))) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_id NOT IN ( SELECT t.client_id FROM crm_client_track AS t WHERE t.school_id = p.school_id AND t.track_isactive = '1' and t.track_isschooltmk = '1' AND t.track_createtime >= '{$sevendaytime}') ";
        }
        //同之前
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            } else {
                $datawhere .= " and c.channel_id = '{$paramArray['channel_id']}' ";
            }
        }
        //同之前
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            } else {
                $datawhere .= " and c.client_source = '{$paramArray['frommedia_name']}' ";
            }
        }
        //筛选负责人
        $trackdatawhere = '';
        if (isset($paramArray['tmk_marketer_id']) && $paramArray['tmk_marketer_id'] !== '') {
            $datawhere .= " and p.marketer_id ='{$paramArray['tmk_marketer_id']}'";
            $trackdatawhere .= " and st.marketer_id ='{$paramArray['tmk_marketer_id']}'";
        }

        $Having = '1';
        if (isset($paramArray['track_createtime']) && $paramArray['track_createtime'] !== '') {
            $track_createtime = strtotime($paramArray['track_createtime']);
            $Having .= " and track_createtime >= '{$track_createtime}' ";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] !== '') {
            $track_endtime = strtotime($paramArray['track_endtime']) + 3600 * 24 - 1;
            $Having .= " and track_createtime <= '{$track_endtime}'  ";
        }
        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $Having .= " and active_invite_id is null and invite_idthree is null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $Having .= " and invite_idthree is not null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $Having .= " and active_invite_id is not null ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null and audition_idthree is null  ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $Having .= " and audition_idthree is not null ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $Having .= " and active_audition_id is not null ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        // 是否接通  筛选人
        $sqlfields = "c.client_id,c.client_img,c.client_actisagree,c.client_cnname,c.client_isnewtip,c.client_img,c.client_birthday,c.client_enname,c.client_sex,c.client_age,c.client_tag
        ,c.client_answerphone,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,c.client_sponsor,FROM_UNIXTIME(c.client_createtime) as client_createtime ,c.client_address,c.client_email 
        ,c.client_distributionstatus,c.client_tracestatus,c.client_remark,ch.channel_id,ch.channel_medianame,ch.channel_minday,ch.channel_maxday,mk.marketer_name as marketer_tmkname
        ,(select f.staffer_enname from smc_staffer as f where f.staffer_id = mk.staffer_id) as staffer_enname
        ,(select p.parenter_cnname FROM crm_client_family as f LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname
        ,(select sa.activity_name from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name
        ,(select ch.channel_name from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name
        ,(select st.track_createtime from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 {$trackdatawhere} order by track_createtime DESC limit 0,1 ) as track_createtime
        ,(select st.track_note from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 {$trackdatawhere} order by track_createtime DESC limit 0,1) as track_note
        ,(select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id
        ,(select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id 
        ,(select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree
        ,(select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree
        ,(select  GROUP_CONCAT(DISTINCT o.coursecat_branch) FROM crm_client_intention as i LEFT JOIN  smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id WHERE i.client_id=c.client_id ) as course_cnname";

        $sql = " select {$sqlfields} 
        from crm_client as c
        Left JOIN crm_client_tmkprincipal as p ON p.client_id = c.client_id
        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id 
        left join crm_code_channel as ch on ch.channel_id = c.channel_id 
        where {$datawhere}  Group by c.client_id
        HAVING {$Having} ";

        $sql .= "  LIMIT {$pagestart},{$num}";
        $clientList = $this->DataControl->selectClear($sql);
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if ($clientList) {
            foreach ($clientList as &$value) {
                $value['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                if (!is_null($value['staffer_enname']) && $value['staffer_enname'] != '') {
                    $value['marketer_tmkname'] = $value['marketer_tmkname'] . '/' . $value['staffer_enname'];
                }
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '/' . $value['client_enname'];
                }
                if ($value['client_tag']) {
                    $value['client_tag'] = explode(',', $this->LgArraySwitch($value['client_tag']));
                } else {
                    $value['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $value['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $value['course_cnname'] = array();
                }

                $value['track_note'] = (!is_null($value['track_createtime']) ? date('Y-m-d H:i', $value['track_createtime']) . ' ' : '') . ($value['track_note'] == '' ? '--' : $value['track_note']);
                if ($value['track_createtime']) {
                    $value['track_createtime'] = date('Y-m-d H:i', $value['track_createtime']);
                } else {
                    $value['track_createtime'] = '--';
                }
                if ($this->isGmcPost == true) {
                    $value['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                }
                if ($value['client_answerphone'] == 1) {
                    if ($value['client_intention_maxlevel'] >= 3) {
                        $value['client_answerphone'] = $this->LgStringSwitch("接通有效");
                    } else {
                        $value['client_answerphone'] = $this->LgStringSwitch("接通无效");
                    }
                } else {
                    $value['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
                $value['client_intention_level'] = intval($value['client_intention_level']);
            }
        }

        if (isset($paramArray['is_count']) and $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectClear(" select c.client_id,
            (select st.track_createtime from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 {$trackdatawhere} order by track_createtime DESC limit 0,1 ) as track_createtime,
            (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
            (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id, 
            (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,   
            (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree
            from crm_client as c
            Left JOIN crm_client_tmkprincipal as p ON p.client_id = c.client_id
            Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id 
            left join crm_code_channel as ch on ch.channel_id = c.channel_id 
            where {$datawhere}  Group by c.client_id
            HAVING {$Having}  ");
        }
        $allnums = is_array($all_num) ? count($all_num) : 0;

        $result = array();
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }

    /**
     * 获取电销名单跟进表 -- 记录
     * @param $paramArray
     * @return array
     */
    function getOneTmkClientRecord($paramArray)
    {

        $datawhere = "c.company_id ='{$paramArray['company_id']}' and p.school_id='{$paramArray['school_id']}'  ";//and p.tmkprincipal_leave = 0

        if (isset($paramArray['postbe_crmuserlevel']) && $paramArray['postbe_crmuserlevel'] == 1) {
            $datawhere .= " and  c.client_tracestatus <> 4";
        } else {
            $datawhere .= " and mk.marketer_id='{$paramArray['marketer_id']}'  and  c.client_tracestatus <> 4 ";//and c.client_schtmkdistributionstatus = 1
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >='{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <='{$create_endtime}'";
        }
        if (isset($paramArray['track_intention_level']) && $paramArray['track_intention_level'] !== '') {
            $datawhere .= " and c.client_intention_level ='{$paramArray['track_intention_level']}'";
        }
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$paramArray['client_tracestatus']}' ";
        }
        //同之前
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            } else {
                $datawhere .= " and c.channel_id = '{$paramArray['channel_id']}' ";
            }
        }
        //同之前
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            } else {
                $datawhere .= " and c.client_source = '{$paramArray['frommedia_name']}' ";
            }
        }
        //接通状态 0未接通 1接通有效 2接通无效
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone = '{$paramArray['client_answerphone']}'";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel >= 3";
        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_maxlevel < 3";
        }

        //筛选负责人
        $trackdatawhere = '';
        if (isset($paramArray['tmk_marketer_id']) && $paramArray['tmk_marketer_id'] !== '') {
            $datawhere .= " and p.marketer_id ='{$paramArray['tmk_marketer_id']}'";
            $trackdatawhere .= " and st.marketer_id ='{$paramArray['tmk_marketer_id']}'";
        }

        $Having = " 1 and (select st.track_id from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 {$trackdatawhere} order by track_createtime DESC limit 0,1 ) > 0";
        if (isset($paramArray['track_createtime']) && $paramArray['track_createtime'] !== '') {
            $track_createtime = strtotime($paramArray['track_createtime']);
            $Having .= " and track_createtime >= '{$track_createtime}' ";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] !== '') {
            $track_endtime = strtotime($paramArray['track_endtime']) + 3600 * 24 - 1;
            $Having .= " and track_createtime <= '{$track_endtime}'  ";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != "" && $paramArray['activity_id'] != 0) {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $Having .= " and active_invite_id is null and invite_idthree is null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $Having .= " and invite_idthree is not null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $Having .= " and active_invite_id is not null ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null and audition_idthree is null  ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $Having .= " and audition_idthree is not null ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $Having .= " and active_audition_id is not null ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        // 是否接通  筛选人
        $sqlfields = "c.client_id,c.client_img,c.client_actisagree,c.client_cnname,c.client_isnewtip,c.client_img,c.client_birthday,c.client_enname,c.client_sex,c.client_age,c.client_tag
        ,c.client_answerphone,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,c.client_sponsor,FROM_UNIXTIME(c.client_createtime) as client_createtime ,c.client_address,c.client_email
        ,c.client_distributionstatus,c.client_tracestatus,c.client_remark,ch.channel_id,ch.channel_medianame,ch.channel_minday,ch.channel_maxday,mk.marketer_name as marketer_tmkname
        ,(select p.parenter_cnname FROM crm_client_family as f LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname
        ,(select sa.activity_name from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name
        ,(select ch.channel_name from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name
        ,(select st.track_createtime from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 and st.marketer_id = p.marketer_id order by track_createtime DESC limit 0,1 ) as track_createtime
        ,(select st.track_note from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 and st.marketer_id = p.marketer_id order by track_createtime DESC limit 0,1) as track_note
        ,(select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id
        ,(select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id
        ,(select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree
        ,(select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree";

        $sql = " select {$sqlfields} 
        from crm_client as c
        Left JOIN crm_client_tmkprincipal as p ON p.client_id = c.client_id
        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id 
        left join crm_code_channel as ch on ch.channel_id = c.channel_id 
        where {$datawhere}  Group by p.client_id,p.marketer_id 
        HAVING {$Having} ";

        $sql .= "  LIMIT {$pagestart},{$num}";
        $clientList = $this->DataControl->selectClear($sql);
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if ($clientList) {
            foreach ($clientList as &$value) {
                $value['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '/' . $value['client_enname'];
                }
                if ($value['client_tag']) {
                    $value['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $value['client_tag'] = array();
                }
                $value['track_note'] = (!is_null($value['track_createtime']) ? date('Y-m-d H:i', $value['track_createtime']) . ' ' : '') . ($value['track_note'] == '' ? '--' : $value['track_note']);
                if ($value['track_createtime']) {
                    $value['track_createtime'] = date('Y-m-d H:i', $value['track_createtime']);
                } else {
                    $value['track_createtime'] = '--';
                }
                if ($this->isGmcPost == true) {
                    $value['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                }
                if ($value['client_answerphone'] == 1) {
                    if ($value['client_intention_maxlevel'] >= 3) {
                        $value['client_answerphone'] = $this->LgStringSwitch("接通有效");
                    } else {
                        $value['client_answerphone'] = $this->LgStringSwitch("接通无效");
                    }
                } else {
                    $value['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
                $value['client_intention_level'] = intval($value['client_intention_level']);
            }
        }

        if (isset($paramArray['is_count']) and $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectClear(" select c.client_id,
            (select st.track_createtime from crm_client_track as st where st.client_id =c.client_id and st.track_isactive =1 and st.track_isschooltmk=1 {$trackdatawhere} order by track_createtime DESC limit 0,1 ) as track_createtime,
            (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
            (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id, 
            (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,   
            (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree
            from crm_client as c
            Left JOIN crm_client_tmkprincipal as p ON p.client_id = c.client_id
            Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id 
            left join crm_code_channel as ch on ch.channel_id = c.channel_id 
            where {$datawhere}  Group by p.client_id,p.marketer_id 
            HAVING {$Having}  ");
        }
        $allnums = is_array($all_num) ? count($all_num) : 0;

        $result = array();
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }


    function getAuditionTypes($paramArray)
    {

        $data = array();

        $key = 0;
        $data[$key]['value'] = 0;
        $data[$key]['name'] = $this->LgStringSwitch('普通公开课试听');
        $key++;
        $data[$key]['value'] = 1;
        $data[$key]['name'] = $this->LgStringSwitch('插班试听');
        $key++;
        $data[$key]['value'] = 2;
        $data[$key]['name'] = $this->LgStringSwitch('试读公开课试听');
        $key++;

        return $data;
    }


    /**
     * @param $paramArray
     * @return bool
     * 新增意向客户
     */
    function addIntentionClient($paramArray)
    {
        if (isset($paramArray['client_icard']) && $paramArray['client_icard'] !== "") {
            if (!$this->checkClientIcard($paramArray['client_icard'])) {
                $this->error = 1;
                $this->errortip = "身份证号重复,无法添加";
                return false;
            }
        }
        $checkData = array();
        $checkData['client_cnname'] = $paramArray['client_cnname'];
        $checkData['client_mobile'] = $paramArray['client_mobile'];
        $checkData['company_id'] = $paramArray['company_id'];
        $checkData['school_id'] = $paramArray['school_id'];
        $from = "add";
        if ($this->checkIntentionClient($checkData, $from)) {
            $this->error = 1;
            $this->errortip = '数据重复,无法添加';
            return false;
        }

        if ($paramArray['company_id'] == '8888') {
            if ($this->checkIntentionClient($checkData, '') && ($paramArray['channel_id'] <> '577' && $paramArray['channel_id'] <> '647') ) {
                $this->error = 1;
                $this->errortip = "手机号重复并且渠道不是【同胞转介绍】,无法添加";
                $this->result = ['isTipsPhoneChannel' => '1'];
                return false;
            }
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['marketer_id'] = $paramArray['marketer_id'];
        $data['activity_id'] = $paramArray['activity_id'];
        $data['client_cnname'] = $paramArray['client_cnname'];
        $data['client_enname'] = $paramArray['client_enname'];
        $data['client_img'] = $paramArray['client_img'];
        $data['client_fromtype'] = $paramArray['client_fromtype'];
        $data['client_age'] = birthdaytoage($paramArray['client_birthday']);
        //添加客户标签
        $label_list = json_decode(stripslashes($paramArray['label_list']), true);
        if ($label_list) {
            if (is_array($label_list)) {
                $data['client_tag'] = implode(',', $label_list);
                //添加的标签 加 1  -- 开始
                if ($label_list) {
                    foreach ($label_list as $tagvar) {
                        $sql = "update crm_client_label set label_usenum=label_usenum+1 where company_id = '{$paramArray['company_id']}' and label_name = '{$tagvar}' ";
                        $this->DataControl->selectClear($sql);
                    }
                }
                //添加的标签 加 1  -- 结束
            } else {
                $data['client_tag'] = $paramArray['label_list'];

                //添加的标签 加 1  -- 开始
                $sql = "update crm_client_label set label_usenum=label_usenum+1 where company_id = '{$paramArray['company_id']}' and label_name = '{$paramArray['label_list']}' ";
                $this->DataControl->selectClear($sql);
                //添加的标签 加 1  -- 结束
            }
        }
        $data['province_id'] = $paramArray['province_id'];
        $data['city_id'] = $paramArray['city_id'];
        $data['area_id'] = $paramArray['area_id'];
        $data['client_birthday'] = $paramArray['client_birthday'];
        $data['client_sex'] = $paramArray['client_sex'];
        $data['client_mobile'] = $paramArray['client_mobile'];
        $data['client_icard'] = $paramArray['client_icard'];
        $data['client_remark'] = $paramArray['client_remark'];
        $data['client_source'] = $paramArray['client_source'];
        //加地推渠道判断   --  要添加地推人员信息
        $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_push,channel_id,channel_medianame,channel_intention_level", "channel_id = '{$paramArray['channel_id']}'");
        if ($channelOne['channel_push'] == '1') {
            if (!$paramArray['promotion_id']) {
                $this->error = 1;
                $this->errortip = '地推渠道,请输入地推工号';
                return false;
            } else {
                $data['promotion_id'] = $paramArray['promotion_id'];
            }
        } else {
            $data['promotion_id'] = 0;
        }
        $data['channel_id'] = $paramArray['channel_id'];
        $data['client_sponsor'] = $paramArray['client_sponsor'];
        $data['client_teachername'] = $paramArray['client_teachername'];
        $data['client_tracestatus'] = 0;
        $data['client_distributionstatus'] = 1;
        $data['client_cardnuber'] = $paramArray['client_cardnuber'];
        $data['client_nationtype'] = $paramArray['client_nationtype'];
        $data['client_publicschool'] = $paramArray['client_publicschool'];
        $data['client_primaryschool'] = $paramArray['client_primaryschool'];
        if($channelOne['channel_intention_level'] > 0){
            $data['client_intention_level'] = $channelOne['channel_intention_level'];
            $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
        }else {
            $data['client_intention_level'] = $paramArray['client_intention_level'];
            $data['client_intention_maxlevel'] = $paramArray['client_intention_level'];
        }
        $data['nearschool_id'] = $paramArray['nearschool_id'];
        $data['client_createtime'] = time();
        $data['client_updatetime'] = time();  //加此字段好排序
        $data['client_actisagree'] = 1;//20191231 导入的名单默认同意协议
        if (isset($paramArray['is_recommend']) && $paramArray['is_recommend'] == 1) {
            $data['client_sponsor'] = $paramArray['client_sponsor'];
            $data['client_stubranch'] = trim($paramArray['client_stubranch']);
        }

        //家长联系人 ==
        $family = $paramArray['client_family_list'];
        $familycount = count($family);
        $family_mobile = array();
        if ($familycount) {
            for ($k = 0; $k < $familycount; $k++) {
                if ($family[$k]['family_mobile'] != "") {

                    $family[$k]['family_mobile'] = trim($family[$k]['family_mobile']);

                    $family_mobile[] = $family[$k]['family_mobile'];
                    //主要联系人的电话同步
                    if ($family[$k]['family_isdefault'] == 1) {
                        $data['client_mobile'] = $family[$k]['family_mobile'];
                        if($family[$k]['family_email']) {
                            $data['client_email'] = $family[$k]['family_email'];
                        }
                    }
                } else {
                    $family_mobile = array();
                }
            }
            if (count($family_mobile) != count(array_unique($family_mobile))) {

                $this->error = 1;
                $this->errortip = "家长手机号重复";
                return false;
            }
        }
        $this->DataControl->begintransaction();    //开启事务
        if ($id = $this->DataControl->insertData("crm_client", $data)) {

            //添加名单状态记录
            $Model = new  \Model\Api\CalloutModel($paramArray);
            $Model->addClientTimerecord($paramArray['company_id'],$paramArray['school_id'],$id,1,$paramArray['marketer_id'],"CRM新增意向客户");

            $trackData = array();
            $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id='{$paramArray['marketer_id']}'");
            $trackData['client_id'] = $id;
            $trackData['marketer_id'] = $paramArray['marketer_id'];
            $trackData['marketer_name'] = $markertOne['marketer_name'];
            $trackData['track_intention_level'] = $paramArray['client_intention_level'];
//            $trackData['track_linktype'] = "新增意向客户";
//            $trackData['track_note'] = "新增意向客户";
            $trackData['track_linktype'] = $this->LgStringSwitch("系统新增");
            $trackData['track_note'] = $this->LgStringSwitch("系统新增意向客户");
            $trackData['school_id'] = $paramArray['school_id'];
            $trackData['track_createtime'] = time();
            $trackData['track_type'] = 0;
            $trackData['track_initiative'] = 1;
            $this->DataControl->insertData('crm_client_track', $trackData);

            //校园适配
            $dataschool = array();
            $dataschool['school_id'] = $paramArray['school_id'];
            $dataschool['company_id'] = $paramArray['company_id'];
            $dataschool['is_schoolenter'] = 1;
            $dataschool['client_id'] = $id;
            $dataschool['schoolenter_createtime'] = time();
            $dataschool['schoolenter_updatetime'] = time();
            if (!$this->DataControl->insertData('crm_client_schoolenter', $dataschool)) {
                $this->DataControl->rollback();
                $this->error = 1;
                $this->errortip = '新增校园适配失败';
                return false;
            }

            //家长联系人
            $family = $paramArray['client_family_list'];
            $familycount = count($family);
            if ($familycount) {
                for ($key = 0; $key < $familycount; $key++) {
                    if ($family[$key]['family_mobile'] != "") {

                        $parenter_id = $this->DataControl->getFieldOne("smc_parenter", 'parenter_id', "parenter_mobile = {$family[$key]['family_mobile']}");
                        if ($parenter_id) {

                            $familydata = array();
                            $familydata['client_id'] = $id;
                            $familydata['company_id'] = $paramArray['company_id'];
                            $familydata['family_createtime'] = time();
                            $familydata['family_relation'] = $family[$key]['family_relation'];
                            $familydata['parenter_id'] = $parenter_id['parenter_id'];
                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = '新增家庭联系人失败';
                                return false;
                            }

                        } else {
                            $parenterData = array();
                            $parenterData['parenter_cnname'] = $family[$key]['family_cnname'];
                            $parenterData['parenter_mobile'] = $family[$key]['family_mobile'];
                            if($family[$key]['family_email']) {
                                $parenterData['parenter_email'] = $family[$key]['family_email'];
                            }
                            $parenterData['parenter_addtime'] = time();
                            if (!$insert_id = $this->DataControl->insertData("smc_parenter", $parenterData)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = '新增家长失败';
                                return false;
                            }
                            $familydata = array();
                            $familydata['client_id'] = $id;
                            $familydata['company_id'] = $paramArray['company_id'];
                            $familydata['family_createtime'] = time();
                            $familydata['family_relation'] = $family[$key]['family_relation'];
                            $familydata['parenter_id'] = $insert_id;
                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = '新增家庭联系人失败';
                                return false;
                            }
                        }
                    }
                }
            }

            //添加意向课程
            $intention = array();
            $intention['client_id'] = $id;
            $course = $paramArray['intention_course'];
            $coursecount = count($course);
            if ($coursecount && $paramArray['intention_course'] !== '') {
                for ($i = 0; $i < $coursecount; $i++) {
                    if ($course[$i]['coursecat_id'] !== '' && $course[$i]['coursecat_id'] !== '0') {
                        $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat"
                            , "coursecat_id,coursetype_id", "coursecat_id='{$course[$i]['coursecat_id']}' AND company_id = '{$paramArray['company_id']}'");
                        if ($coursecatOne) {
                            if (!$this->DataControl->getFieldOne("crm_client_intention", "intention_id",
                                "client_id = '{$id}' AND coursecat_id = '{$coursecatOne['coursecat_id']}'")) {
                                $intention['coursetype_id'] = $coursecatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursecatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_client_intention", $intention);
                            }
                        }
                    }
                }
            }

            $cilentModel = new ClientModel();
            //添加主负责人
            /*$dataPrincipal = array();
            $dataPrincipal['client_id'] = $id;
            $dataPrincipal['marketer_id'] = $paramArray['main_marketer_id'];
            $dataPrincipal['principal_ismajor'] = 1;
            $dataPrincipal['school_id'] = $paramArray['school_id'];
            $dataPrincipal['principal_createtime'] = time();*/
            if (isset($paramArray['main_marketer_id']) && $paramArray['main_marketer_id'] != '') {
                if (!$this->setClientPrincipal($id, $paramArray['main_marketer_id'], $paramArray['school_id'], 1)) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = '新增主负责人失败';
                    return false;
                }
                if (!$cilentModel->addAllotLog($id, $paramArray['main_marketer_id'], $paramArray['marketer_id'], $paramArray['school_id'], 1)) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = '增加主分配记录失败';
                    return false;
                }
            }
            //副负责人
            $datafuPrin['client_id'] = $id;
            $datafuPrin['principal_ismajor'] = 0;
            $datafuPrin['school_id'] = $paramArray['school_id'];
            $datafuPrin['principal_createtime'] = time();

            if (isset($paramArray['fu_marketer_id']) && $paramArray['fu_marketer_id'] != '') {
                if (!is_array($paramArray['fu_marketer_id'])) {
                    $paramArray['fu_marketer_id'] = [$paramArray['fu_marketer_id']];
                }
                for ($i = 0; $i < count($paramArray['fu_marketer_id']); $i++) {
                    $datafuPrin['marketer_id'] = $paramArray['fu_marketer_id'][$i];
                    if (!$this->setClientPrincipal($datafuPrin['client_id'], $datafuPrin['marketer_id'], $datafuPrin['school_id'], 0)) {
                        $this->DataControl->rollback();
                        return false;
                    }
                    if (!$cilentModel->addAllotLog($id, $paramArray['fu_marketer_id'][$i], $paramArray['marketer_id'], $paramArray['school_id'], 0)) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = '增加副分配记录失败';
                        return false;
                    }
                }
            }
        } else {
            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = '增加客户失败';
            return false;
        }
        $this->DataControl->commit();
        $this->error = 0;
        $this->errortip = '新增成功';
        return true;
    }

    /**
     * @param $paramArray
     * @return array|bool
     * 意向客户的流转  --招生有效名单   --无意向客户
     * 根据 $paramArray['type'] 来确定 转为招生有效名单 0  还是 转为无意向客户 -1
     * crm_client 分配状态 改为0 ,客户状改为 0
     * crm_client_allotlog  添加解除日志
     * crm_client_principal 解除负责人
     * crm_client_track     增加跟踪记录
     */
    function changeIntentionClientStatus($paramArray)
    {
        if (!is_array($paramArray['client_id'])) {
            $paramArray['client_id'] = [$paramArray['client_id']];
        }

//		更新客户状态
        $dataClient = array();
        $dataClient['client_distributionstatus'] = 0;
        $dataClient['client_updatetime'] = time();

        //解除负责人
        $dataPrincipal = array();
        $dataPrincipal['principal_leave'] = 1;
        $dataPrincipal['principal_updatatime'] = time();
        // 增加跟踪记录
        $dataTrack = array();
        $dataTrack['marketer_id'] = $paramArray['marketer_id'];

        $dataTrack['school_id'] = $paramArray['school_id'];
        $dataTrack['track_createtime'] = time();
        if ($paramArray['type'] == -1) {
            $dataTrack['track_state'] = -1;
            $dataTrack['track_followmode'] = -1;
            $dataTrack['track_linktype'] = $this->LgStringSwitch("流转操作");
            $dataTrack['track_note'] = $this->LgStringSwitch('系统流转:意向客户流转为无意向客户');
        }elseif ($paramArray['type'] == -2) {
            $dataTrack['track_state'] = -2;
            $dataTrack['track_followmode'] = -3;
            $dataTrack['track_invalidreason'] = $paramArray['invalidnote_reason'];
            $dataTrack['track_linktype'] = $this->LgStringSwitch("流转操作");
            $dataTrack['track_note'] = $this->LgStringSwitch($paramArray['track_note']);
        } else {
            $dataTrack['track_state'] = 0;
            $dataTrack['track_followmode'] = 0;
            $dataTrack['track_linktype'] = $this->LgStringSwitch("流转操作");
            $dataTrack['track_note'] = $this->LgStringSwitch('系统流转:意向客户流转为有效名单');
        }
        $markOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
        $dataTrack['marketer_name'] = $markOne['marketer_name'];

        $stafferPost = $this->DataControl->selectOne("select sp.postbe_crmuserlevel from crm_marketer as m, gmc_staffer_postbe as sp where m.staffer_id = sp.staffer_id and sp.postbe_crmuserlevel =1 and  m.marketer_id ='{$paramArray['marketer_id']}' limit 0,1 ");

        $stafferOne = $this->DataControl->selectOne("select s.account_class from  crm_marketer as m ,smc_staffer as s where m.staffer_id=s.staffer_id  and m.marketer_id ='{$paramArray['marketer_id']}' and m.company_id='{$paramArray['company_id']}'  ");

        $this->DataControl->begintransaction();

        $fail = 0;
        $scc = 0;
        $isgmcnum = 0;
        $audnum = 0;
        for ($i = 0; $i < count($paramArray['client_id']); $i++) {

            if ($paramArray['type'] == -1 || $paramArray['type'] == -2) {
                //判断批量操作名单是不是集团间接分配来源的名单
                $gmctoschooloness = $this->DataControl->selectOne("select * 
                                from crm_client_schoolenter as s 
                                where s.client_id = '{$paramArray['client_id'][$i]}' and s.school_id = '{$paramArray['school_id']}' 
                                and s.is_enterstatus = '1' and s.is_gmctocrmschool = '1' and s.is_gmcdirectschool = '0' limit 0,1 ");
            }
            if ($gmctoschooloness['schoolenter_id'] > 1) {
                $isgmcnum++;
            } else {
                $client_id = $paramArray['client_id'][$i];
                if ((!$this->verificationTrack($client_id, $paramArray['marketer_id'], 1)) && $paramArray['postbe_crmuserlevel'] <> 1 && $stafferOne['account_class'] == 0) {
                    $fail++;
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "您不是主负责人,没有流转权限";
                    continue;
                }

                if ($paramArray['type'] == -1) {
                    $dataClient['client_tracestatus'] = -1;
                    $dataClient['client_ischaserlapsed'] = 0;
                }elseif ($paramArray['type'] == -2) {
                    $dataClient['client_tracestatus'] = -2;
                    $dataClient['client_isinvalidreview'] = 0;
                    $dataClient['invalidnote_code'] = $paramArray['invalidnote_code'];

                    $sql = "SELECT a.audition_id as visit_id
                        FROM crm_client_audition a
                        WHERE  a.client_id = '{$paramArray['client_id'][$i]}' AND a.audition_isvisit = '0'
                        UNION ALL
                        SELECT i.invite_id as visit_id
                        FROM crm_client_invite i
                        WHERE  i.client_id = '{$paramArray['client_id'][$i]}' AND i.invite_isvisit = '0'";
                    if ($this->DataControl->selectOne($sql)) {
                        $audnum++;
                        $this->error = 1;
                        $this->errortip = "客户存在柜询/试听到访待确认信息，请确认是否到访后，再进行无效设置！";
                        continue;
                    }

                } else {
                    //判断批量操作名单是不是集团间接分配来源的名单
                    $gmctoschoolone = $this->DataControl->selectOne("select s.schoolenter_id 
                                from crm_client_schoolenter as s 
                                where s.client_id = '{$client_id}' and s.school_id = '{$paramArray['school_id']}' 
                                and s.is_enterstatus = '1' and s.is_gmctocrmschool = '1' and s.is_gmcdirectschool = '0' limit 0,1 ");
                    $auditionone = $this->DataControl->selectOne("SELECT i.audition_id  
                        FROM crm_client_audition as i
                        where i.client_id = '{$client_id}' and i.school_id = '{$paramArray['school_id']}' 
                        limit 0,1 ");
                    $inviteone = $this->DataControl->selectOne("SELECT i.invite_id  
                        FROM crm_client_invite as i
                        where i.client_id = '{$client_id}' and i.school_id = '{$paramArray['school_id']}' 
                        limit 0,1 ");

                    if ($gmctoschoolone && ($inviteone or $auditionone)) {

                    } else {
                        $dataClient['client_tracestatus'] = 0;
                        $dataClient['client_ischaserlapsed'] = 0;
                        $dataClient['client_isinvalidreview'] = 0;
                        $dataClient['invalidnote_code'] = '';
                    }
                }
                $dataClient['client_id'] = $paramArray['client_id'][$i];
                //更新客户状态
                if (!$this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $dataClient)) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新客户状态失败";
                    continue;
                    //return [false,'更新客户状态失败'];
                }
                if ($paramArray['type'] == 0) {
                    //增加分配日志 解除负责人的解除日志
                    if ($this->addRemoveAllotLog($client_id, '-1', $paramArray['marketer_id'], $paramArray['school_id'])) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "增加分配日志:解除日志失败";
                        continue;
                    }
                    //解除负责人的负责状态  一定要先添加解除日志
                    if ($this->DataControl->updateData('crm_client_principal', "client_id='{$client_id}' and school_id > 0 ", $dataPrincipal)) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "解除负责人失败";
                        continue;
                        //	return [false,'解除负责人失败'];
                    }
                } else {
                    //增加分配日志 解除所有人的负责日志
                    if (!$this->addRemoveAllotLog($client_id, -1, $paramArray['marketer_id'], $paramArray['school_id'])) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "增加分配日志:解除日志 失败";
                        continue;
                    }

                    //解除所有人的负责状态  一定要先添加解除日志
                    if (!$this->DataControl->updateData('crm_client_principal', "client_id='{$client_id}' and school_id > 0 ", $dataPrincipal)) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "解除负责人失败";
                        continue;
                    }
                }
                //增加跟踪记录
                $dataTrack['client_id'] = $client_id;
                $dataTrack['track_type'] = 0;
                $dataTrack['track_initiative'] = 0;
                if (!$this->DataControl->insertData('crm_client_track', $dataTrack)) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "增加跟踪记录失败";
                    continue;
                }
                $scc++;
            }
        }


        $this->DataControl->commit();

        if ($isgmcnum > 0 && $audnum >0) {
            $tip = "已排除{$isgmcnum}条集团来源名单和{$audnum}条柜询/试听待确认名单，其余{$scc}条批量操作成功";
            $this->error = 1;
        }elseif ($isgmcnum > 0) {
            $tip = "已排除{$isgmcnum}条集团来源名单，其余{$scc}条批量操作成功";
            $this->error = 1;
        }elseif ($audnum > 0) {
            $tip = "已排除{$audnum}条柜询/试听待确认名单，其余{$scc}条批量操作成功";
            $this->error = 1;
        } else {
            $tip = "批量操作成功";
            $this->error = 0;
        }
        if($fail>0){
            $tip .= ",{$fail}条非负责人无权限";
        }
        $this->errortip = $tip;
        return true;

    }

    //高管分配单个 待分配名单 转 无效名单  -------- 20240923台湾需求补充
    function changeIntentionClientOneStatus($paramArray)
    {
        $sql = "SELECT
                    a.audition_id as visit_id
                FROM
                    crm_client_audition a
                WHERE a.client_id = '{$paramArray['client_id']}'
                AND a.audition_isvisit = '0'
                UNION ALL
                SELECT
                    i.invite_id as visit_id
                FROM
                    crm_client_invite i
                WHERE  i.client_id = '{$paramArray['client_id']}'
                AND i.invite_isvisit = '0'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = 1;
            $this->errortip = "客户存在柜询/试听到访待确认信息，请确认是否到访后，再进行无效设置！";
            return false;
        }


        $client_id = $paramArray['client_id'];
        $markOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
        $stafferOne = $this->DataControl->selectOne("select s.account_class from  crm_marketer as m ,smc_staffer as s where m.staffer_id=s.staffer_id  and m.marketer_id ='{$paramArray['marketer_id']}' and m.company_id='{$paramArray['company_id']}'  ");

        //判断批量操作名单是不是集团间接分配来源的名单
        $gmctoschooloness = $this->DataControl->selectOne("select * 
                            from crm_client_schoolenter as s 
                            where s.client_id = '{$paramArray['client_id']}' and s.school_id = '{$paramArray['school_id']}' 
                            and s.is_enterstatus = '1' and s.is_gmctocrmschool = '1' and s.is_gmcdirectschool = '0' limit 0,1 ");
        if ($gmctoschooloness['schoolenter_id'] > 1) {
            $this->error = 1;
            $this->errortip = "该名单来源集团名单不能无效操作！";
            return false;
        }
        if ((!$this->verificationTrack($client_id, $paramArray['marketer_id'], 1)) && $paramArray['postbe_crmuserlevel'] <> 1 && $stafferOne['account_class'] == 0) {
            $this->error = 1;
            $this->errortip = "您不是主负责人,没有流转权限";
            return false;
        }

        //更新客户状态
        $dataClient = array();
        $dataClient['client_distributionstatus'] = 0;
        $dataClient['client_updatetime'] = time();
        $dataClient['client_tracestatus'] = -2;
        $dataClient['client_ischaserlapsed'] = 0;
        $dataClient['client_isinvalidreview'] = 0;
        $dataClient['invalidnote_code'] = $paramArray['invalidnote_code'];
        $dataClient['client_id'] = $client_id;
        if (!$this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $dataClient)) {
            $this->error = 1;
            $this->errortip = "更新客户状态失败";
            return false;
        }
        //增加跟踪记录
        $dataTrack = array();
        $dataTrack['marketer_id'] = $paramArray['marketer_id'];
        $dataTrack['school_id'] = $paramArray['school_id'];
        $dataTrack['track_createtime'] = time();
        $dataTrack['track_state'] = -2;
        $dataTrack['track_followmode'] = -3;
        $dataTrack['track_invalidreason'] = $paramArray['invalidnote_reason'];
        $dataTrack['track_linktype'] = $this->LgStringSwitch("名单流转");
        $note = $markOne['marketer_name']."教师将待分配有效名单转无效名单"."【原因：{$dataTrack['track_note']}】";
        $dataTrack['track_note'] = $this->LgStringSwitch($note);
        $dataTrack['marketer_name'] = $markOne['marketer_name'];
        $dataTrack['client_id'] = $client_id;
        $dataTrack['track_type'] = 0;
        $dataTrack['track_initiative'] = 0;
        if (!$this->DataControl->insertData('crm_client_track', $dataTrack)) {
            $this->error = 1;
            $this->errortip = "增加跟踪记录失败";
            return false;
        }

        $this->error = 0;
        $this->errortip = "名单转为无效名单成功";
        return true;
    }

    /**
     * @param $client_id
     * @return bool
     * 解除一个客户的所有负责状态
     */
    function updateClientPrincipal($client_id)
    {
        $data = array();
        $data['principal_leave'] = 0;
        if ($this->DataControl->updateData('crm_client_principal', "client_id='{$client_id}'", $data)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param $client_id
     * @return bool
     *  新增一个客户的分配记录-解除记录 -由负责人表查询
     *  -1 解除所有人
     *   解除负责人状态一定要放在调用这个方法后面
     */
//	 											负责人			操作人
    function addRemoveAllotLog($client_id, $main_marketer_id, $marketer_id, $school_id)
    {
        $where = " 1 and  principal_leave = 0 ";
        if ($main_marketer_id === -1) {
            $where .= " and client_id='{$client_id}'";
        } else {
            $where .= " and client_id='{$client_id}' and marketer_id='{$main_marketer_id}' ";
        }
        $clientPrincipalList = $this->DataControl->getList("crm_client_principal", $where, "order by principal_createtime DESC");
        if ($clientPrincipalList) {
            $dataAllotlog = array();
            $dataAllotlog['allotlog_status'] = 0;
            $dataAllotlog['allotlog_removetime'] = time();

            foreach ($clientPrincipalList as $key => $value) {
                if ($this->DataControl->updateData('crm_client_allotlog', "allotlog_status =1 and school_id='{$school_id}' and client_id='{$client_id}' and allot_marketer_id='{$value['marketer_id']}'", $dataAllotlog)) {
                    $res = true;
                } else {
                    $res = false;
                }
            }

            return $res;
        } else {
            return false;
        }
    }

    /**
     * @param $paramArray
     * @param string $from
     * @param string $client_id
     * @return array|bool
     *  新增意向客户的重名检测
     *
     *  编辑时返回重复列表 $from 为空  $client_id 有值
     *  新增时 返回重复列表   $from 为空   $client_id 有值
     *  编辑 提交 检测    $from 有值 $client_id  有值
     *  新增 提交 检测    $from 有值 $client_id  无值
     *
     */
    function checkIntentionClient($paramArray, $from = "", $client_id = "")
    {
        $paramArray['client_cnname'] = trim($paramArray['client_cnname']);
        $paramArray['client_mobile'] = trim($paramArray['client_mobile']);

//        $where = "cs.client_id = c.client_id and cs.is_enterstatus = 1 AND s.school_id = cs.school_id and c.company_id='{$paramArray['company_id']}'";//老 230912前
        $where = " c.company_id='{$paramArray['company_id']}'";
        $clientarray = $this->DataControl->selectClear("SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p 
                   WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}'");
        if($clientarray){
            $clientIDarray = array_column($clientarray, 'client_id');
            $clientisstr = implode(",", $clientIDarray);
            $where .= " AND (client_mobile='{$paramArray['client_mobile']}' OR c.client_id IN ( $clientisstr )) ";
        }else{
            $where .= " AND client_mobile='{$paramArray['client_mobile']}'";
        }

//        if ($from != "") {
//            $where = "c.client_cnname='{$paramArray['client_cnname']}' and (c.client_mobile='{$paramArray['client_mobile']}' OR c.client_id IN ( SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}' )) and cs.company_id ='{$paramArray['company_id']}' and cs.school_id = '{$paramArray['school_id']}' ";
//        }//老 230912前
//        if ($from != "") {
//            $where = "c.client_cnname='{$paramArray['client_cnname']}' and (c.client_mobile='{$paramArray['client_mobile']}' OR c.client_id IN ( SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}' ))   ";
//        }//老 240517重复了 加上 点where之后
        if ($from != "") {
            $where .= " and c.client_cnname='{$paramArray['client_cnname']}' ";
        }
        if ($client_id != "") {
            $where .= " and (c.client_id <> '{$client_id}')";
        }
        if ($paramArray['client_id']) {
            $where .= " and (c.client_id <> '{$paramArray['client_id']}')";
        }

        $sqlfields = "c.client_id,c.client_img,c.client_cnname,client_enname,c.client_sex,c.client_age,c.client_isgross,c.client_distributionstatus,c.client_createtime,c.client_mobile,s.school_id,s.school_cnname,c.client_tracestatus,c.client_source,c.channel_id,'0' as issmcstu";
        $sqlorder = 'c.client_createtime';

//        $sql = "select {$sqlfields},
//                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
//                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id,
//                (select h.channel_name from crm_code_channel as h where h.channel_id = c.channel_id limit 0,1) as channel_name,
//                (select marketer_name from crm_client_principal as p,crm_marketer as m where p.client_id = c.client_id
//                and p.marketer_id = m.marketer_id and p.principal_ismajor = 1 AND p.school_id <> '0' and p.principal_leave = 0 limit 0,1) as marketer_name,
//                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and c.client_id = p.client_id and p.marketer_id = '{$paramArray['marketer_id']}' limit 0,1 ) as is_intention,
//                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and p.client_id = c.client_id and p.school_id = '{$paramArray['school_id']}' limit 0,1 ) as is_exitnowschool,
//                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and p.client_id = c.client_id and p.school_id <> '{$paramArray['school_id']}' limit 0,1 ) as is_exitotherschool,
//                (select t.track_followmode from crm_client_track as t where t.client_id = c.client_id order by t.track_createtime Desc limit 0,1 ) as track_followmode
//                FROM crm_client as c,crm_client_schoolenter as cs,smc_school as s WHERE {$where} ORDER BY {$sqlorder} DESC ";//老 230912前

        $sql = "select {$sqlfields},
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id,
                (select h.channel_name from crm_code_channel as h where h.channel_id = c.channel_id limit 0,1) as channel_name,
                (select marketer_name from crm_client_principal as p,crm_marketer as m where p.client_id = c.client_id 
                and p.marketer_id = m.marketer_id and p.principal_ismajor = 1 AND p.school_id <> '0' and p.principal_leave = 0 limit 0,1) as marketer_name,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and c.client_id = p.client_id and p.marketer_id = '{$paramArray['marketer_id']}' limit 0,1 ) as is_intention,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and p.client_id = c.client_id and p.school_id = '{$paramArray['school_id']}' limit 0,1 ) as is_exitnowschool,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and p.client_id = c.client_id and p.school_id <> '{$paramArray['school_id']}' limit 0,1 ) as is_exitotherschool,
                (select t.track_followmode from crm_client_track as t where t.client_id = c.client_id order by t.track_createtime Desc limit 0,1 ) as track_followmode
                FROM crm_client as c
                left join crm_client_schoolenter as cs ON c.client_id = cs.client_id 
                left join smc_school as s ON cs.school_id = s.school_id 
                WHERE {$where} ORDER BY {$sqlorder} DESC ";

        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            $clientidarray = array_column($clientList, 'client_id');
            $clientidsstr = implode(",", $clientidarray);
//            0待跟踪1持续跟踪2已柜询3已试听4已转正-1已流失-2已无效'
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => $value) {
                $clientList[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                $clientList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                if (empty($value['marketer_name'])) {
                    $clientList[$key]['marketer_name'] = "--";
                }

                if($value['is_intention'] && $value['school_id'] == $paramArray['school_id'] && $value['client_tracestatus'] <> 4){
                    //如果属于当前销售负责名单，且在当前校区
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                    $clientList[$key]['is_track'] = 1;
                }elseif ($value['client_tracestatus'] == 0 || $value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1) {
                    //如果名单待跟踪/无意向/无效
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                    $clientList[$key]['is_track'] = 1;
                }else{
                    if($value['client_tracestatus'] <> 4){
                        //判断名单是否在90天保护期
                        $minenterTimes = time()-3600*24*90;
                        if($value['client_createtime'] <= $minenterTimes){
                            //不在保护期，未报名名单，可以直接抢单
                            if($value['school_id'] == $paramArray['school_id']){
                                //本校抢单
                                $clientList[$key]['client_status'] = 0;
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("抢单跟踪");
                                $clientList[$key]['is_track'] = 1;
                            }else{
                                //跨校抢单
                                $clientList[$key]['client_status'] = 0;
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("跨校抢单");
                                $clientList[$key]['is_track'] = 1;
                            }
                        }else{
                            //在保护期内，需协调抢单
                            $clientList[$key]['client_status'] = 1;
                            $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                            $clientList[$key]['is_track'] = 0;
                        }
                    }else{
                        $clientList[$key]['client_status'] = 1;
                        $clientList[$key]['track_stuname'] = $this->LgStringSwitch("已报名名单");
                        $clientList[$key]['is_track'] = 0;
                    }
                }

                /*if (!$value['is_intention'] && $value['marketer_name'] && $value['school_id'] == $paramArray['school_id']) {
                    $clientList[$key]['client_status'] = 1;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                } elseif ($value['marketer_name'] && $value['school_id'] <> $paramArray['school_id']) {
                    $clientList[$key]['client_status'] = 1;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                } elseif ($value['is_intention'] && $value['school_id'] == $paramArray['school_id']) {
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                } elseif ($value['school_id'] <> $paramArray['school_id'] && $value['client_tracestatus'] <= 0) {
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                } elseif ($value['school_id'] == $paramArray['school_id'] && $value['client_distributionstatus'] == 0 && $value['client_tracestatus'] <> 4) {
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                } else {
                    $clientList[$key]['client_status'] = 1;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                }

                if ($value['client_isgross'] == 0) {
                    if (!$value['is_exitnowschool']
                        && $value['school_id'] == $paramArray['school_id'] &&
                        ($value['client_tracestatus'] == 0 || $value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1)) {
                        $clientList[$key]['is_track'] = 1;
                    } elseif (!$value['is_exitotherschool'] && $value['school_id'] <> $paramArray['school_id'] && ($value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1)) {
                        $clientList[$key]['is_track'] = 1;
                    } elseif (!$value['this_company'] && ($value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1)) {
                        $clientList[$key]['is_track'] = 1;
                    } elseif ($value['is_intention'] && $value['school_id'] == $paramArray['school_id']) {
                        $clientList[$key]['is_track'] = 1;
                    } else {
                        $clientList[$key]['is_track'] = 0;
                    }
                } else {
                    $clientList[$key]['is_track'] = 0;
                }
                */

                $clientList[$key]['client_tracestatus_name'] = $clientTracestatus[$value['client_tracestatus']];
            }
        } else {
            $clientList = array();
        }

        $stuwhere = " sf.family_mobile = '{$paramArray['client_mobile']}' ";
        if ($clientidsstr) {
            $stuwhere .= " and from_client_id not in ($clientidsstr) ";
        }
        if ($from != "") {
            $stuwhere .= " and student_cnname='{$paramArray['client_cnname']}' ";
        }
        $schsql = " select  s.student_id as client_id,
                        s.student_cnname as client_cnname,
                        s.student_sex as client_sex,
                        s.student_birthday,
                        h.school_cnname,
                        '--' as client_source,
                        '--' as channel_name,
                        '--' as marketer_name,
                        '已转正' as client_tracestatus_name,
                        '1' as issmcstu 
 
                        FROM smc_student_enrolled as se
                        left join smc_student as s on se.student_id=s.student_id
                        left join smc_student_family as sf on sf.student_id=s.student_id 
                        left join smc_school as h on h.school_id = se.school_id
                        where {$stuwhere} and sf.family_isdefault=1 and h.company_id = '{$paramArray['company_id']}' 
                        order by s.student_id ASC 
                        limit 0,1 ";
        $clientListTwo = $this->DataControl->selectClear($schsql);

        if ($clientListTwo) {
            foreach ($clientListTwo as &$clientVar) {
                $clientVar['client_age'] = birthdaytoage($clientVar['student_birthday']);
                $clientVar['track_stuname'] = $this->LgStringSwitch("继续跟踪");
            }
        } else {
            $clientListTwo = array();
        }

        $clientAll = array_merge($clientList, $clientListTwo);
        if (!$clientAll) {
//            '姓名', '性别', '年龄', '所属学校', '渠道类型', '渠道明细','主要负责人', '客户状态',
//            'client_cnname', 'client_sex',   'client_age', 'school_cnname','client_source','channel_name','marketer_name',  'client_tracestatus_name',

            $stuwhere = " sf.family_mobile = '{$paramArray['client_mobile']}' ";
            if ($from != "") {
                $stuwhere .= " and student_cnname='{$paramArray['client_cnname']}' ";
            }
            $schsql = " select  s.student_id as client_id,
                        s.student_cnname as client_cnname,
                        s.student_sex as client_sex,
                        s.student_birthday,
                        h.school_cnname,
                        '--' as client_source,
                        '--' as channel_name,
                        '--' as marketer_name,
                        '已转正' as client_tracestatus_name,
                        '1' as issmcstu 
 
                        FROM smc_student_enrolled as se
                        left join smc_student as s on se.student_id=s.student_id
                        left join smc_student_family as sf on sf.student_id=s.student_id 
                        left join smc_school as h on h.school_id = se.school_id
                        where {$stuwhere} and sf.family_isdefault=1 and h.company_id = '{$paramArray['company_id']}' 
                        order by s.student_id ASC 
                        limit 0,1 ";
            $clientAll = $this->DataControl->selectClear($schsql);

            if ($clientAll) {
                foreach ($clientAll as &$clientVar) {
                    $clientVar['client_age'] = birthdaytoage($clientVar['student_birthday']);
                    $clientVar['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                }
            } else {
                $clientAll = array();
            }
        }
        //一个学员可同时存在在多家校，输入学员手机号码时若有重复则提示重复学员记录，重复记录点击可查看，弹窗内检索整个集团的学员；
        //1.该学员在本校且已有其他负责人的情况下，按钮【我要跟踪】显示为灰色状态不可点击；
        //2.该学员在本校无负责人的情况下（可能是已无意向或在有效名单里），按钮显示为蓝色【我要跟踪】点击弹出弹窗进入跟进页面；
        //3.若该学员在本校且是自己正在负责，按钮显示为蓝色【继续跟踪】点击弹出弹窗进入跟进页面；
        //4.若该学员在其他学校，按钮都显示为蓝色【我要跟踪】点击弹出弹窗进入跟进页面，即转为自己的意向客户，是两个学校的有效名单；
        return $clientAll;
    }


    //设定名单主负责人
    public function setClientPrincipal($client_id, $tomarketer_id, $to_school_id, $ismajor = 1)
    {
        if (!$this->DataControl->selectOne("SELECT principal_id FROM crm_client_principal where client_id = '{$client_id}' AND marketer_id = '{$tomarketer_id}' AND school_id = '{$to_school_id}'")) {
            $dataPrincipal = array();
            $dataPrincipal['client_id'] = $client_id;;
            $dataPrincipal['marketer_id'] = $tomarketer_id;
            $dataPrincipal['school_id'] = $to_school_id;
            $dataPrincipal['principal_ismajor'] = $ismajor;
            $dataPrincipal['principal_createtime'] = time();
            if (!$this->DataControl->insertData('crm_client_principal', $dataPrincipal)) {
                return false;
            } else {
                return true;
            }
        } else {
            $dataPrintcipal = array();
            $dataPrintcipal['principal_ismajor'] = $ismajor;
            $dataPrintcipal['principal_leave'] = 0;
            $dataPrintcipal['principal_updatatime'] = time();
            $this->DataControl->updateData('crm_client_principal', "client_id = '{$client_id}' AND marketer_id = '{$tomarketer_id}' AND school_id = '{$to_school_id}'", $dataPrintcipal);
            return true;
        }
    }


    function trackRepeatClient($paramArray)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$paramArray['school_id']}'");
        $reSchoolOne = $this->DataControl->selectOne("SELECT s.school_cnname FROM crm_client_schoolenter as cs,smc_school as s 
                       WHERE cs.school_id = s.school_id and cs.is_enterstatus = 1 and cs.client_id = '{$paramArray['client_id']}' 
                         and cs.school_id <>'{$paramArray['school_id']}' LIMIT 1");
        if (!$reSchoolOne['school_cnname']) {
            $reSchoolOne['school_cnname'] = '集团分配';
        } else {
            $reSchoolOne['school_cnname'] = $reSchoolOne['school_cnname'] . '转入';
        }
        $markOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");

        //再次判断是否满足抢单条件
        $clientExits = $this->DataControl->selectOne("SELECT c.client_id, c.client_isgross, c.client_distributionstatus, c.client_tracestatus,c.client_createtime,cs.school_id
,( SELECT p.principal_id FROM crm_client_principal AS p WHERE p.principal_leave = 0 AND c.client_id = p.client_id AND p.marketer_id = '{$paramArray['marketer_id']}' LIMIT 0, 1) AS is_selfroll
, ( SELECT p.principal_id FROM crm_client_principal AS p WHERE p.principal_leave = 0 AND p.client_id = c.client_id AND p.school_id = '{$paramArray['school_id']}' LIMIT 0, 1 ) AS is_exitnowschool
,( SELECT p.principal_id FROM crm_client_principal AS p WHERE p.principal_leave = 0 AND p.client_id = c.client_id AND p.school_id <> '{$paramArray['school_id']}' LIMIT 0, 1 ) AS is_exitotherschool 
FROM crm_client AS c, crm_client_schoolenter AS cs 
WHERE cs.client_id = c.client_id AND cs.is_enterstatus = '1' AND c.client_id = '{$paramArray['client_id']}'");
        if($clientExits){
            //再次判断是否满足抢单条件
            if($clientExits['is_selfroll'] && $clientExits['school_id'] == $paramArray['school_id'] && $clientExits['client_tracestatus'] <> 4){
                $this->error = 1;
                $this->errortip = "已在您的意向客户中，请勿重复抢单";
                return false;
            }elseif ($clientExits['client_tracestatus'] == 0 || $clientExits['client_tracestatus'] == -2 || $clientExits['client_tracestatus'] == -1) {
                //如果名单待跟踪/无意向/无效
                $trackData = array();
                $trackData['client_id'] = $paramArray['client_id'];
                $trackData['school_id'] = $paramArray['school_id'];
                $trackData['marketer_id'] = $paramArray['marketer_id'];
                $trackData['marketer_name'] = $markOne['marketer_name'];
                $trackData['track_validinc'] = 1;
                $trackData['track_note'] = $this->LgStringSwitch("系统操作:非跟进中名单，CC抢单成功,解除之前负责人！");
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 0;
                $trackData['track_initiative'] = 0;
                $this->DataControl->insertData('crm_client_track', $trackData);

                //解除非集团TMK负责人记录
                $palData = array();
                $palData['principal_leave'] = 1;
                $palData['principal_updatatime'] = time();
                $this->DataControl->updateData('crm_client_principal', "client_id='{$paramArray['client_id']}' AND school_id <> '0'", $palData);

                //解除日志
                $allotlog = array();
                $allotlog['allotlog_status'] = 0;
                $allotlog['allotlog_removetime'] = time();
                $allotlog['allotlog_note'] = $this->LgStringSwitch("抢单成功，解除其他有效负责人名单状态!");
                $this->DataControl->updateData('crm_client_allotlog', "client_id='{$paramArray['client_id']}' AND school_id <> '0' AND marketer_id <> '{$paramArray['marketer_id']}' AND allotlog_status = 1", $allotlog);
            }else{
                if($clientExits['client_tracestatus'] <> 4){
                    //判断名单是否在90天保护期
                    $minenterTimes = time()-3600*24*90;
                    if($clientExits['client_createtime'] <= $minenterTimes){
                        //不在保护期，未报名名单，可以直接抢单
                        if($clientExits['school_id'] == $paramArray['school_id']){
                            //本校抢单
                            $trackData = array();
                            $trackData['client_id'] = $paramArray['client_id'];
                            $trackData['school_id'] = $paramArray['school_id'];
                            $trackData['marketer_id'] = $paramArray['marketer_id'];
                            $trackData['marketer_name'] = $markOne['marketer_name'];
                            $trackData['track_validinc'] = 1;
                            $trackData['track_note'] = $this->LgStringSwitch("系统操作:本校名单过保护期，CC抢单成功,解除之前负责人！");
                            $trackData['track_createtime'] = time();
                            $trackData['track_type'] = 0;
                            $trackData['track_initiative'] = 0;
                            $this->DataControl->insertData('crm_client_track', $trackData);

                            //解除非集团TMK负责人记录
                            $palData = array();
                            $palData['principal_leave'] = 1;
                            $palData['principal_updatatime'] = time();
                            $this->DataControl->updateData('crm_client_principal'
                                , "client_id='{$paramArray['client_id']}' AND school_id <> '0' AND marketer_id <> '{$paramArray['marketer_id']}'", $palData);

                            //解除日志
                            $allotlog = array();
                            $allotlog['allotlog_status'] = 0;
                            $allotlog['allotlog_removetime'] = time();
                            $allotlog['allotlog_note'] = $this->LgStringSwitch("本校抢单成功，解除其他有效负责人名单状态!");
                            $this->DataControl->updateData('crm_client_allotlog', "client_id='{$paramArray['client_id']}' AND marketer_id <> '{$paramArray['marketer_id']}' AND school_id <> '0' AND allotlog_status = 1", $allotlog);
                        }else{
                            //跨校抢单
                            $trackData = array();
                            $trackData['client_id'] = $paramArray['client_id'];
                            $trackData['school_id'] = $paramArray['school_id'];
                            $trackData['marketer_id'] = $paramArray['marketer_id'];
                            $trackData['marketer_name'] = $markOne['marketer_name'];
                            $trackData['track_validinc'] = 1;
                            $trackData['track_note'] = $this->LgStringSwitch("系统操作:跨校名单过保护期，CC抢单成功,解除之前负责人！");
                            $trackData['track_createtime'] = time();
                            $trackData['track_type'] = 0;
                            $trackData['track_initiative'] = 0;
                            $this->DataControl->insertData('crm_client_track', $trackData);

                            //解除非集团TMK负责人记录
                            $palData = array();
                            $palData['principal_leave'] = 1;
                            $palData['principal_updatatime'] = time();
                            $this->DataControl->updateData('crm_client_principal'
                                , "client_id='{$paramArray['client_id']}' AND school_id <> '0' AND marketer_id <> '{$paramArray['marketer_id']}'", $palData);

                            //解除日志
                            $allotlog = array();
                            $allotlog['allotlog_status'] = 0;
                            $allotlog['allotlog_removetime'] = time();
                            $allotlog['allotlog_note'] = $this->LgStringSwitch("跨校抢单成功，解除其他有效负责人名单状态!");
                            $this->DataControl->updateData('crm_client_allotlog', "client_id='{$paramArray['client_id']}' AND marketer_id <> '{$paramArray['marketer_id']}' AND school_id <> '0' AND allotlog_status = 1", $allotlog);
                        }
                    }else{
                        //在保护期内，需协调抢单
                        $this->error = 1;
                        $this->errortip = "在保护期内，需协调抢单";
                        return false;
                    }
                }else{
                    //在保护期内，需协调抢单
                    $this->error = 1;
                    $this->errortip = "已报名单，禁止抢单";
                    return false;
                }
            }
        }

        $clischoolOne = $this->DataControl->selectOne("select cs.is_enterstatus FROM crm_client_schoolenter as cs 
                         where cs.school_id ='{$paramArray['school_id']}' and cs.client_id='{$paramArray['client_id']}'");
        //在本校没有记录
        if (!$clischoolOne) {
            $schData = array();
            $schData['company_id'] = $paramArray['company_id'];
            $schData['client_id'] = $paramArray['client_id'];
            $schData['school_id'] = $paramArray['school_id'];
            $schData['is_enterstatus'] = '1';
            $schData['schoolenter_createtime'] = time();
            $schData['schoolenter_updatetime'] = time();
            if ($this->DataControl->insertData('crm_client_schoolenter', $schData)) {
                $other_Data = array();
                $other_Data['is_enterstatus'] = "-1";
                $other_Data['schoolenter_updatetime'] = time();
                $this->DataControl->updateData('crm_client_schoolenter', "client_id='{$paramArray['client_id']}' and school_id <> '{$paramArray['school_id']}' ", $other_Data);

                //变为我的意向客户
                $cilentModel = new ClientModel();
                //添加主负责人
                $this->setClientPrincipal($paramArray['client_id'], $paramArray['marketer_id'], $paramArray['school_id'], 1);
                //添加分配记录
                $cilentModel->addAllotLog($paramArray['client_id'], $paramArray['marketer_id'], $paramArray['marketer_id'], $paramArray['school_id'], 1);


                $clientData = array();
                $clientData['client_isgross'] = '0';//20250618 重复名单列表抢单过来的 默认为 有效名单
                $clientData['client_distributionstatus'] = '1';
                $clientData['client_ischaserlapsed'] = '0';
                $clientOne = $this->DataControl->getFieldOne("crm_client", "client_tracestatus", "client_id='{$paramArray['client_id']}'");
                if ($clientOne['client_tracestatus'] < 0) {
                    $clientData['client_tracestatus'] = 0;
                }
                $clientData['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);

                $traData = array();
                $traData['client_id'] = $paramArray['client_id'];
                $traData['school_id'] = $paramArray['school_id'];
                $traData['marketer_id'] = $paramArray['marketer_id'];
                $traData['marketer_name'] = $markOne['marketer_name'];
                $traData['track_note'] = $this->LgStringSwitch("名单由{$reSchoolOne['school_cnname']}{$schoolOne['school_cnname']}");
                $traData['object_code'] = "";
                $traData['track_state'] = "0";
                $traData['track_createtime'] = time();
                $traData['track_type'] = 0;
                $traData['track_initiative'] = 0;
                $traData['track_followmode'] = 5;
                $this->DataControl->insertData('crm_client_track', $traData);

                $trackData['client_id'] = $paramArray['client_id'];
                $trackData['school_id'] = $paramArray['school_id'];
                $trackData['marketer_id'] = $paramArray['marketer_id'];
                $trackData['marketer_name'] = $markOne['marketer_name'];
                $trackData['track_validinc'] = 1;
                $trackData['track_note'] = $this->LgStringSwitch("系统操作:我要跟踪,抢单跟进其他学校的学员");
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 0;
                $trackData['track_initiative'] = 0;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $this->error = 0;
                $this->errortip = "跟踪成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "跟踪失败1";
                return false;
            }
        } else {
            $schData = array();
            $schData['is_enterstatus'] = '1';
            $schData['schoolenter_updatetime'] = time();
            $this->DataControl->updateData('crm_client_schoolenter', "client_id = '{$paramArray['client_id']}' and school_id = '{$paramArray['school_id']}'", $schData);

            $other_Data = array();
            $other_Data['is_enterstatus'] = "-1";
            $other_Data['schoolenter_updatetime'] = time();
            $this->DataControl->updateData('crm_client_schoolenter', "client_id = '{$paramArray['client_id']}' and school_id <> '{$paramArray['school_id']}'", $other_Data);

            if ($clischoolOne['is_enterstatus'] != '1') {
                $traData = array();
                $traData['client_id'] = $paramArray['client_id'];
                $traData['school_id'] = $paramArray['school_id'];
                $traData['marketer_id'] = $paramArray['marketer_id'];
                $traData['marketer_name'] = $markOne['marketer_name'];
                $traData['track_note'] = $this->LgStringSwitch("名单由{$reSchoolOne['school_cnname']}{$schoolOne['school_cnname']}，抢单跟踪此名单！");
                $traData['object_code'] = "";
                $traData['track_state'] = "0";
                $traData['track_createtime'] = time();
                $traData['track_type'] = 0;
                $traData['track_initiative'] = 0;
                $traData['track_followmode'] = 5;
                $this->DataControl->insertData('crm_client_track', $traData);
            }

            $principal = $this->DataControl->selectClear("select * from crm_client_principal where client_id='{$paramArray['client_id']}' and school_id='{$paramArray['school_id']}' and principal_leave = 0 limit 0,1");
            if ($principal) {
                $arr_marketer = array_column($principal, 'marketer_id');
            } else {
                $arr_marketer = array();
            }
            $clientModel = new ClientModel();
            if ($principal && in_array($paramArray['marketer_id'], $arr_marketer)) {
                $clientData = array();
                $clientData['client_isgross'] = '0';//20250618 重复名单列表抢单过来的 默认为 有效名单
                $clientData['client_distributionstatus'] = '1';
                $clientData['client_ischaserlapsed'] = '0';
                $clientOne = $this->DataControl->getFieldOne("crm_client", "client_tracestatus", "client_id='{$paramArray['client_id']}'");
                if ($clientOne['client_tracestatus'] < 0) {
                    $clientData['client_tracestatus'] = 0;
                }
                $clientData['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);

                $this->error = 1;
                $this->errortip = "已是您的意向客户";
                return false;
            }
//            elseif (!$principal || !in_array($paramArray['marketer_id'], $arr_marketer)) {
            elseif ($principal && !in_array($paramArray['marketer_id'], $arr_marketer)) {
                $clientData = array();
                $clientData['client_isgross'] = '0';//20250618 重复名单列表抢单过来的 默认为 有效名单
                $clientData['client_distributionstatus'] = '0';
                $clientData['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);

                $Array = array();
                $Array['client_id'] = $paramArray['client_id'];
                $Array['fu_marketer_id'] = $paramArray['marketer_id'];
                $Array['marketer_id'] = $paramArray['marketer_id'];
                $Array['school_id'] = $paramArray['school_id'];
                $clientModel->allotClientToIntention($Array, $this->LgStringSwitch("本校跟踪"));
                $this->error = 0;
                $this->errortip = "已分配为您的意向客户";
                return true;
            } else {
                $clientData = array();
                $clientData['client_isgross'] = '0';//20250618 重复名单列表抢单过来的 默认为 有效名单
                $clientData['client_distributionstatus'] = '0';
                $clientData['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);

                $Array = array();
                $Array['client_id'] = $paramArray['client_id'];
                $Array['main_marketer_id'] = $paramArray['marketer_id'];
                $Array['marketer_id'] = $paramArray['marketer_id'];
                $Array['school_id'] = $paramArray['school_id'];
                $clientModel->allotClientToIntention($Array, $this->LgStringSwitch("本校跟踪"));
                $this->error = 0;
                $this->errortip = "已分配为您的意向客户2";
                return true;
            }
        }
    }

    function Conversion($paramArray)
    {
//        if ($paramArray['company_id'] != '8888' && $paramArray['company_id'] != '1001') {
//            $this->error = 1;
//            $this->errortip = "该集团,暂不允转正操作";
//            return false;
//        }

        if (!$paramArray['student_branch']) {
            $this->error = 1;
            $this->errortip = "请输入学员编号";
            return false;
        }
        $clientlOne = $this->DataControl->getFieldOne("crm_client", "client_tracestatus,client_cnname", "client_id='{$paramArray['client_id']}' and  company_id='{$paramArray['company_id']}'");
        if ($clientlOne['client_tracestatus'] == -2) {
            $this->error = 1;
            $this->errortip = "该学员已无效";
            return false;
        } elseif ($clientlOne['client_tracestatus'] == -1) {
            $this->error = 1;
            $this->errortip = "该学员已无意向";
            return false;
        } elseif ($clientlOne['client_tracestatus'] == 4) {
            $this->error = 1;
            $this->errortip = "该学员已转正";
            return false;
        }
        if (!$this->DataControl->getOne("crm_client_schoolenter", "school_id='{$paramArray['school_id']}' and client_id='{$paramArray['client_id']}'")) {
            $this->error = 1;
            $this->errortip = "该学员不在该校";
            return false;
        }
//         $clientOne = $this->DataControl->selectOne("select student_id from smc_student where  from_client_id='{$paramArray['client_id']}'");
//         if($clientOne){
//             $this->error = 1;
//             $this->errortip = "该学员已存在转正记录";
//             return false;
//         }
//         $studentOne = $this->DataControl->selectOne("select student_id from  smc_student where student_branch='{$paramArray['student_branch']}' ");
//         if($studentOne){
//             $this->error = 1;
//             $this->errortip = "该学员编号已存在";
//             return false;
//         }
        $this->DataControl->begintransaction();
        $markWork = $this->DataControl->selectOne("select cm.* from crm_marketer as cm where marketer_id ='{$paramArray['marketer_id']}' ");
        $workData = array();
        $workData['company_id'] = $paramArray['company_id'];
        $workData['school_id'] = $paramArray['school_id'];
        $workData['client_id'] = $paramArray['client_id'];
        $workData['marketer_id'] = $paramArray['marketer_id'];
        $workData['marketer_name'] = $markWork['marketer_name'];
        $workData['student_branch'] = $paramArray['student_branch'];
        $workData['course_branch'] = $paramArray['course_branch'];
        $workData['positivelog_time'] = $paramArray['positivelog_time'];
        $workData['positvelog_addtime'] = time();
        $workData['positvelog_note'] = $paramArray['positvelog_note'];
        if (!$this->DataControl->insertData("crm_client_positivelog", $workData)) {
            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = "该编号已有转正记录";
            return false;
        }

        $data_track = array();
        $data_track['client_id'] = $paramArray['client_id'];
        $data_track['school_id'] = $paramArray['school_id'];
        $data_track['marketer_id'] = $paramArray['marketer_id'];
        $data_track['marketer_name'] = $markWork['marketer_name'];
        $data_track['track_validinc'] = '1';
        $data_track['track_linktype'] = $this->LgStringSwitch('招生CRM转正');
        $data_track['track_followmode'] = '3';
        //$data_track['track_note'] = "由招生CRM转正,操作人:管理员ID为{$markWork['marketer_id']}";
        $data_track['track_note'] = $this->LgStringSwitch($markWork['marketer_name'] . '在CRM系统转正' . $clientlOne['client_cnname'] . ",学员编号为{$paramArray['student_branch']}," . "转正日期{$paramArray['positivelog_time']}");
        $data_track['track_createtime'] = time();
        $data_track['track_type'] = 0;
        $data_track['track_initiative'] = 0;
        $this->DataControl->insertData('crm_client_track', $data_track);

        $tem_data = array();
        $tem_data['client_tracestatus'] = 4;
        $tem_data['client_distributionstatus'] = 1;
        $tem_data['client_updatetime'] = time();
        $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}' and company_id='{$paramArray['company_id']}'", $tem_data);

        $marketerList = $this->DataControl->selectClear("select marketer_id,principal_ismajor from crm_client_principal where client_id='{$paramArray['client_id']}'and school_id='{$paramArray['school_id']}' and principal_leave=0  and marketer_id > 0 group by marketer_id ");
        if ($marketerList) {
            foreach ($marketerList as $marketerOne) {
                $data = array();
                $data['marketer_id'] = $marketerOne['marketer_id'];
                $data['school_id'] = $paramArray['school_id'];
                $data['client_id'] = $paramArray['client_id'];
                $data['student_branch'] = $paramArray['student_branch'];
                $data['conversionlog_ismajor'] = $marketerOne['principal_ismajor'];
                $data['course_branch'] = $paramArray['course_branch'];
                $data['conversionlog_time'] = time();
                $this->DataControl->insertData("crm_client_conversionlog", $data);
            }
        } else {
            $data = array();
            $data['school_id'] = $paramArray['school_id'];
            $data['client_id'] = $paramArray['client_id'];
            $data['student_branch'] = $paramArray['student_branch'];
            $data['course_branch'] = $paramArray['course_branch'];
            $data['conversionlog_time'] = time();
            $this->DataControl->insertData("crm_client_conversionlog", $data);
        }

//         $principalList=$this->DataControl->selectClear("select principal_id from crm_client_principal where client_id='{$paramArray['client_id']}' and  principal_leave=0  ");
//         if($principalList){
//             $prin_data =array();
//             $prin_data['principal_leave'] =1;
//             $prin_data['principal_updatatime'] =time();
//             foreach($principalList as $value){
//                 $this->DataControl->updateData('crm_client_principal',"principal_id='{$value['principal_id']}'",$prin_data);
//             }
//         }
        $this->DataControl->commit();
        $this->error = 0;
        $this->errortip = "转正成功";
        return true;
    }

    function conversionCourseApi($paramArray)
    {
        $where = "1";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== "") {
            $where .= " and (co.course_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%')";
        }
        $courseList = $this->DataControl->selectClear("
            select  co.course_id,co.course_branch,course_cnname
            from smc_course as co
            left join smc_code_coursecat as ct ON co.coursecat_id =ct.coursecat_id
            left join smc_code_coursetype as cp ON  cp.coursetype_id =co.coursetype_id
            where co.company_id ='{$paramArray['company_id']}' and ct.coursecat_iscrmadded =1 and coursetype_isopenclass=0 and {$where}"
        );

        if (!$courseList) {
            $courseList = array();
        }
        return $courseList;
    }

    //将离职人员的意向客户,转为有效名单
    function IntentionToClientApi($staffer_id, $company_id)
    {
        //暂时关闭这个功能
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", 'marketer_id,marketer_name', "staffer_id='{$staffer_id}' and company_id='{$company_id}'");
        if (!$marketerOne) {
            $this->error = false;
            $this->errortip = "暂无CRM账号";
            return false;
        }
        $sql = "select pl.principal_id,pl.client_id,pl.principal_ismajor,pl.marketer_id,pl.school_id,c.client_tracestatus
            from crm_client_principal as pl,crm_client AS c where
            pl.client_id = c.client_id AND pl.marketer_id = '{$marketerOne['marketer_id']}' and pl.principal_leave = 0 AND c.client_tracestatus IN(0,1,2,3) GROUP BY pl.client_id";
        $principalList = $this->DataControl->selectClear($sql);
        if ($principalList) {
            foreach ($principalList as $value) {
                if ($value['principal_ismajor'] == 0) {
                    $data = array();
                    $data['principal_leave'] = 1;
                    $data['principal_updatatime'] = time();
                    $this->DataControl->updateData("crm_client_principal", "principal_id='{$value['principal_id']}'", $data);
                } else {
                    $data = array();
                    $data['principal_leave'] = 1;
                    $data['principal_updatatime'] = time();
                    $this->DataControl->updateData("crm_client_principal", "client_id='{$value['client_id']}' AND marketer_id = '{$marketerOne['marketer_id']}'", $data);

                    $clientData = array();
                    $clientData['client_tracestatus'] = 0;
                    $clientData['client_distributionstatus'] = 0;
                    $clientData['client_updatetime'] = time();
                    $this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $clientData);

                    $trackData[] = array();
                    $trackData['client_id'] = $value['client_id'];
                    $trackData['school_id'] = $value['school_id'];
                    $trackData['marketer_id'] = $marketerOne['marketer_id'];
                    $trackData['marketer_name'] = $marketerOne['marketer_name'];
                    $trackData['track_note'] = "主负责人{$marketerOne['marketer_name']}离职,解除所有负责人,流转为有效名单.";
                    $trackData['track_type'] = 0;
                    $trackData['track_initiative'] = 1;
                    $trackData['track_createtime'] = time();
                    $this->DataControl->insertData("crm_client_track", $trackData);
                }
            }
            $this->error = true;
            $this->errortip = "解除负责人成功";
            return true;
        } else {

            $this->error = false;
            $this->errortip = "暂无负责的客户";
            return false;
        }

    }

    /*
     * 主管审阅跟踪记录
     */
    function levelReadTrackOne($request)
    {
        if (!$request['track_id']) {
            $this->error = 1;
            $this->errortip = "请选择需要回复的跟进记录";
            return false;
        }
        $data = array();
        $data['track_isreading'] = 1;
        $data['track_readtime'] = time();
        $data['level_marketer_id'] = $request['marketer_id'];
        if ($this->DataControl->updateData('crm_client_track', "track_id='{$request['track_id']}'", $data)) {
            $this->error = 0;
            $this->errortip = "审阅成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "审阅失败";
            return false;
        }
    }

    /**
     * 主管回复跟踪记录
     * author: ling
     * @param $request
     */
    function levelReplayTrack($request)
    {
        $data = array();
        $track_id = intval($request['track_id']);
        if (!$track_id) {
            $this->error = 1;
            $this->errortip = "请选择需要回复的跟进记录";
            return false;
        }
        if (!$request['replay_note']) {
            $this->error = 1;
            $this->errortip = "请输入需要回复的内容";
            return false;
        }
        $data['track_id'] = $track_id;
        $data['school_id'] = $request['school_id'];
        $data['marketer_id'] = $request['marketer_id'];
        $data['school_id'] = $request['school_id'];
        $data['replay_note'] = $request['replay_note'];
        $data['replay_createtime'] = time();
        if ($this->DataControl->insertData('crm_track_replay', $data)) {
            $this->error = 0;
            $this->errortip = "回复成功";
            return false;
        } else {
            $this->error = 1;
            $this->errortip = "您已经回复过该记录啦";
            return false;
        }

    }

    function ImportIntentionTrack($request, $sqlarray)
    {
        if (!$sqlarray) {
            $this->error = true;
            $errortip = $this->LgStringSwitch("请确认文件是否存在数据");
            $this->errortip = $errortip;
            return false;
        }
        $t_num = 0;
        $f_num = 0;
        $f_array = array();

        foreach ($sqlarray as &$one) {
            if ($request['step'] == '1') {
                $data = array();
                $data['reason'] = '';
                $data['client_cnname'] = addslashes($one['client_cnname']);
                $data['client_enname'] = $one['client_enname'];
                $data['client_sex'] = addslashes($one['client_sex']);
                if ($one['client_birthday']) {
                    $n = intval(($one['client_birthday'] - 25569) * 3600 * 24);
                    $one['client_birthday'] = gmdate('Y-m-d', $n);
                    $data['client_birthday'] = $one['client_birthday'] >= date("Y-m-d") ? date("Y-m-d") : $one['client_birthday'];
                }
                $data['client_age'] = birthdaytoage($data['client_birthday']);
                $data['parenter_cnname'] = addslashes($one['parenter_cnname']);
                $data['client_mobile'] = addslashes($one['client_mobile']);
                $data['client_source'] = addslashes($one['client_source']);
                $data['channel_name'] = addslashes($one['channel_name']);
                if ($one['client_fromtype_name'] == '外部招生') {
                    $data['client_fromtype'] = 0;
                } elseif ($one['client_fromtype_name'] == '内部招生') {
                    $data['client_fromtype'] = 1;
                } elseif ($one['client_fromtype_name'] == '专案招生' || $one['client_fromtype_name'] == '専案招生' || $one['client_fromtype_name'] == '專案招生') {
                    $data['client_fromtype'] = 2;
                }
                $data['client_remark'] = addslashes($one['client_remark']);
                $data['client_createtime'] = time();
                $data['client_actisagree'] = 1;//20191231 导入的名单默认同意协议
                $reson = $this->verifyClient($one, $request);
                if ($reson) {
                    $data['reson'] = $reson;
                    $f_num++;
                    $f_array[] = $data;
                }
                $data = array();
                $data['suc'] = $t_num;
                $data['fal'] = $f_num;
                $data['falarray'] = $f_array;
                return $data;
            } elseif ($request['step'] == '0') {
                if ($one['client_birthday']) {
                    $n = intval(($one['client_birthday'] - 25569) * 3600 * 24);
                    $one['client_birthday'] = gmdate('Y-m-d', $n);
                }
                return $sqlarray;
            } else {
                return array();
            }
        }

    }

    /**
     * 检测导入客户
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/22 0022
     * @param $one
     * @param $request
     * @return string
     */
    private function verifyClient($one, $request)
    {
        $reson = '';
        switch ($one) {
            case $one['client_birthday'] == '' :
                $reson .= $this->LgStringSwitch("生日为空");
                break;
            case $one['client_sex'] == '' :
                $reson .= $this->LgStringSwitch("性别为空");
                break;
            case $one['client_cnname'] == '' :
                $reson .= $this->LgStringSwitch("中文名为空");
                break;
            case $one['client_source'] == '' :
                $reson .= $this->LgStringSwitch("招生渠道类型为空");
                break;
            case $one['channel_name'] == '' :
                $reson .= $this->LgStringSwitch("招生渠道明细为空");
                break;
            case $one['client_mobile'] == '' :
                $reson .= $this->LgStringSwitch("手机号为空");
                break;
            case strlen($one['client_mobile']) > 11 && substr($one['client_mobile'], 0, 3) != '100' :
                $reson .= $this->LgStringSwitch("手机号格式不正确");
                break;
        }
        $clientOne = $this->DataControl->selectOne("
                    select cc.client_id,client_tracestatus from crm_client as cc, crm_client_schoolenter as cs  
                    where cs.client_id=cc.client_id and cc.client_mobile='{$one['client_mobile']}' and cs.school_id='{$request['school_id']}' and cc.client_tracestatus >= 0 limit 0,1");
        $is_student = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s WHERE f.student_id = s.student_id AND f.family_mobile = '{$one['client_mobile']}' and s.company_id = '{$this->company_id}' AND s.student_isdel = '0' ORDER BY s.student_id limit 0,1");
        $frommedia_id = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id", "company_id='{$request['company_id']}' and frommedia_name='{$one['client_source']}'");
        $channel_id = $this->DataControl->getFieldOne("crm_code_channel", "channel_id", "company_id='{$request['company_id']}' and  channel_name='{$one['channel_name']}'");
        $is_channel_frommedia = $this->DataControl->getFieldOne("crm_code_channel", "channel_id", "company_id='{$request['company_id']}' and channel_medianame='{$one['client_source']}'  and  channel_name='{$one['channel_name']}'");
        if (!$frommedia_id) {
            $reson .= $this->LgStringSwitch('不存在对应的渠道类型');
        } elseif (!$channel_id) {
            $reson .= $this->LgStringSwitch('不存在对应的渠道明细');
        } elseif (!$is_channel_frommedia) {
            $reson .= $this->LgStringSwitch("渠道明细无法对应渠道类型");
        } elseif ($clientOne) {
            $reson .= $this->LgStringSwitch("该手机号名已在本校存在数据");
        } elseif ($is_student) {
            $reson .= $this->LgStringSwitch("'该手机号已是在校生,禁止导入");
        } else {
            $reson = '';
        }
        return $reson;
    }
}
