<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  LossclientModel extends modelTpl
{
    public $m;
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $company_isassist = 0;
    public $isGmcPost = null;


    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'],$publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 流失客户管理
     */
    function getLossclientList($paramArray)
    {
        $datawhere = "c.client_tracestatus = -1 and c.client_distributionstatus = 0  and s.school_id='{$paramArray['school_id']}' and cs.is_enterstatus =1  and cs.company_id='{$paramArray['company_id']}' and c.client_isgross = '0' ";
//        //不包含集团这边 分配过来的人
//        $datawhere .= " and not exists(select 1 from crm_client_schoolenter as h where h.client_id=c.client_id and h.school_id='{$paramArray['school_id']}' and is_enterstatus = '1' and h.is_gmctocrmschool = '1' ) ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%'  or c.client_frompage like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['client_source']) && $paramArray['client_source'] !== '') {
            $datawhere .= "  and c.client_source = '{$paramArray['client_source']}'";
        }
        if (isset($paramArray['client_ischaserlapsed']) && $paramArray['client_ischaserlapsed'] !== '') {
            $datawhere .= "  and c.client_ischaserlapsed = '{$paramArray['client_ischaserlapsed']}'";
        }
        //创建时间
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >= '{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <= '{$create_endtime}'";
        }
        //历史最高意向星级
        if (isset($paramArray['client_intention_maxlevel']) && $paramArray['client_intention_maxlevel'] !== '') {
            $datawhere .= "  and c.client_intention_maxlevel = '{$paramArray['client_intention_maxlevel']}'";
        }
        //渠道类型
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '') {
            $datawhere .= " and c.client_source = '{$paramArray['frommedia_name']}'";
        }
        $Having = '1';
        //是否参与试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//已试听
            $Having .= " and active_audition_id is not null ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("
 				select count(c.client_id) as client_num,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id
				from  crm_client as c
				Left JOIN crm_client_schoolenter as cs ON cs.client_id = c.client_id
				Left JOIN smc_school as s ON s.school_id = cs.school_id
				where {$datawhere} HAVING {$Having} 
				");
            $allnums = $all_num['client_num'];
            if ($allnums) {
                $data['allnums'] = $allnums;
            } else {
                $data['allnums'] = 0;
            }
        }
        $sqlfield = 'c.client_id,c.client_ischaserlapsed,c.client_cnname,c.client_enname,c.client_sex,c.client_age,c.client_tag,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,s.school_cnname,c.client_sponsor,client_fromtype,a.activity_name,c.client_createtime,client_address,c.client_remark,c.client_email,c.client_frompage';
        $sqlorder = 'c.client_updatetime';
        $sql = "select  {$sqlfield} ,
                (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
				(select p.parenter_cnname FROM crm_client_family as f LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				(select concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )  from  crm_client_principal  as p
				 LEFT JOIN crm_marketer  as m ON  m.marketer_id = p.marketer_id
				 LEFT JOIN smc_staffer as sf On sf.staffer_id = m.staffer_id
				where p.client_id = c.client_id and  principal_ismajor=1 and principal_leave=1  and p.school_id=cs.school_id ORDER by principal_createtime DESC limit 0,1 ) as marketer_name,
				(select group_concat(concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ))  from  crm_client_principal  as p
				 LEFT JOIN crm_marketer  as m ON  m.marketer_id = p.marketer_id
				 LEFT JOIN smc_staffer as sf ON sf.staffer_id =m.staffer_id
				where p.client_id = c.client_id and  principal_ismajor=0 and principal_leave=1  and p.school_id=cs.school_id ORDER by principal_createtime DESC limit 0,1 ) as fu_marketer_name,
			    (select cn.channel_name from crm_code_channel as cn where cn.channel_id=c.channel_id) as channel_name ,
			    (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and t.track_followmode ='-1' order by track_createtime DESC limit 0,1) as tracktime,
                (select concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )) from crm_client_track as t,crm_marketer as m,smc_staffer as sf where t.client_id = c.client_id and t.school_id = cs.school_id and t.track_followmode ='-1' and t.marketer_id = m.marketer_id and sf.staffer_id=m.staffer_id  order by track_createtime DESC limit 0,1) as caozuo_name,
                (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and t.track_followmode ='-2' order by track_createtime DESC limit 0,1) as shenhe_time,
                (select m.marketer_name from crm_client_track as t,crm_marketer as m where t.client_id = c.client_id and t.school_id = cs.school_id and t.track_followmode ='-2' and t.marketer_id = m.marketer_id  order by track_createtime DESC limit 0,1) as shenhe_name,
                (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and track_isactive =1 order by track_createtime DESC limit 0,1) as last_tracktime,
                (select t.track_note from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and track_isactive =1 order by track_createtime DESC limit 0,1) as last_track_note,
                (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
			    (select r.region_name from smc_code_region as r where c.province_id=r.region_id ) as province_name,
                (select r.region_name from smc_code_region as r where c.city_id=r.region_id ) as city_name,
			    (select r.region_name from smc_code_region as r where c.area_id=r.region_id ) as area_name
				from  crm_client as c
				Left JOIN crm_client_schoolenter as cs ON cs.client_id = c.client_id
				Left JOIN smc_school as s ON s.school_id = cs.school_id
				LEFT JOIN crm_sell_activity as a ON a.activity_id = c.activity_id
				where {$datawhere} HAVING {$Having} Order by  {$sqlorder} DESC ";

        $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $arr_fromtype = array(0 => '外部招生', 1 => '内部招生', 2 => '专案招生');
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            if ($dateexcelarray) {
                foreach ($dateexcelarray as &$value) {
                    if ($value['client_ischaserlapsed'] == 0) {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("未确认");
                    } else {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("已确认");
                    }
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_tag'] = $this->LgStringSwitch($dateexcelvar['client_tag']);
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];

                    $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);

                    $datearray['client_email'] = $dateexcelvar['client_email'];
                    $datearray['client_intention_maxlevel'] = $dateexcelvar['client_intention_maxlevel'];
                    $datearray['ishaveaudition'] = $dateexcelvar['active_audition_id'] ? '是' : '否';
                    $datearray['last_tracktime'] = $dateexcelvar['last_tracktime'] == false ? '--' : date("Y-m-d H:i:s", $dateexcelvar['last_tracktime']);
                    $datearray['last_track_note'] = $dateexcelvar['last_track_note'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['client_fromtype_name'] = $arr_fromtype[$dateexcelvar['client_fromtype']];
                    $datearray['shenhe_name'] = $dateexcelvar['shenhe_name'];
                    $datearray['shenhe_time'] = $dateexcelvar['shenhe_time'] == false ? '--' : date("Y-m-d H:i:s", $dateexcelvar['shenhe_time']);
                    $datearray['client_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['client_createtime']);;
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['city_name'] = $dateexcelvar['city_name'];
                    $datearray['area_name'] = $dateexcelvar['area_name'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 0) {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '年龄', '性别', '标签', '主要联系人', '主要联系手机','Email', '历史最高意向星级', '是否参与试听', '最后跟踪时间', '最后跟踪内容', '主要负责人', '活动', '渠道类型', '渠道明细','接触点', '推荐人', '招生来源类型', '审核人', '审核时间', '名单创建时间', '联系地址', '省', '市', '区', '备注'));
                $excelfileds = array('client_cnname', 'client_enname', 'client_age', 'client_sex', 'client_tag', 'family_cnname', 'client_mobile','client_email', 'client_intention_maxlevel', 'ishaveaudition', 'last_tracktime', 'last_track_note', 'marketer_name', 'activity_name', 'client_source', 'channel_name','client_frompage', 'client_sponsor', 'client_fromtype_name', 'shenhe_name', 'shenhe_time', 'client_createtime', 'client_address', 'province_name', 'city_name', 'area_name', 'client_remark');
            } else {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '年龄', '性别', '标签', '主要联系人', '主要联系手机','Email', '历史最高意向星级', '是否参与试听', '最后跟踪时间', '最后跟踪内容', '主要负责人', '协助负责人', '活动', '渠道类型', '渠道明细','接触点', '推荐人', '招生来源类型', '审核人', '审核时间', '名单创建时间', '联系地址', '省', '市', '区', '备注'));
                $excelfileds = array('client_cnname', 'client_enname', 'client_age', 'client_sex', 'client_tag', 'family_cnname', 'client_mobile','client_email', 'client_intention_maxlevel', 'ishaveaudition', 'last_tracktime', 'last_track_note', 'marketer_name', 'fu_marketer_name', 'activity_name', 'client_source', 'channel_name','client_frompage', 'client_sponsor', 'client_fromtype_name', 'shenhe_name', 'shenhe_time', 'client_createtime', 'client_address', 'province_name', 'city_name', 'area_name', 'client_remark');
            }
            $fielname = $this->LgStringSwitch("无意向名单记录表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num} ";
            $clientList = $this->DataControl->selectClear($sql);
            if ($clientList) {
//                0外部招生1内部招生2专案招生
                $arr_fromtype = array(0 => '外部招生', 1 => '内部招生', 2 => '专案招生');
                foreach ($clientList as &$value) {
                    $value['client_frompage'] = $value['client_frompage']?$value['client_frompage']:'--';
                    if ($value['client_tag']) {
                        $value['client_tag'] = explode(',', $this->LgStringSwitch($value['client_tag']));
                    } else {
                        $value['client_tag'] = array();
                    }
                    if(!is_null($value['course_cnname'])){
                        $value['course_cnname'] = explode(',', $value['course_cnname']);
                    }else{
                        $value['course_cnname'] = array();
                    }
                    $value['client_fromtype_name'] = $arr_fromtype[$value['client_fromtype']];
                    if ($value['last_tracktime']) {
                        $value['last_tracktime'] = date("Y-m-d H:i:s", $value['last_tracktime']);
                    } else {
                        $value['last_tracktime'] = '--';
                    }
                    if ($value['tracktime']) {
                        $value['tracktime'] = date("Y-m-d H:i:s", $value['tracktime']);
                    } else {
                        $value['tracktime'] = '--';
                    }
                    $value['client_createtime'] = date("Y-m-d H:i:s", $value['client_createtime']);
                    if ($value['client_ischaserlapsed'] == 0) {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("未确认");
                    } else {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("已确认");
                    }
                    $value['shenhe_time'] = $value['shenhe_time'] == false ? '--' : date("Y-m-d H:i:s", $value['shenhe_time']);
                    if($this->isGmcPost == true){
                        $value['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    }else{
                        $value['client_mobile'] = $value['client_mobile'];
                    }
                    $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                    if($isoutCity){
                        if($isoutCity == 'local'){
                            $value['isoutmobile'] = 0;
                        }else{
                            $value['isoutmobile'] = 1;
                        }
                    }else{
                        $value['isoutmobile'] = -1;
                    }
                    if($value['active_audition_id']){
                        $value['ishaveaudition'] = '是';
                    }else{
                        $value['ishaveaudition'] = '否';
                    }
                }
                $data['list'] = $clientList;
            } else {
                $data['list'] = array();
            }
            return $data;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 流转 流失客户-招生有效名单
     */
    function changeLossClientToClient($paramArray)
    {
        $dataClient = array();
        $dataClient['client_distributionstatus'] = $paramArray['client_distributionstatus'];
        $dataClient['client_tracestatus'] = $paramArray['client_tracestatus'];
        $dataClient['client_ischaserlapsed'] = $paramArray['client_ischaserlapsed'];
        $dataClient['client_isinvalidreview'] = $paramArray['client_isinvalidreview'];
        $dataClient['invalidnote_code'] = $paramArray['invalidnote_code'];

        if (!is_array($paramArray['client_id'])) {
            $paramArray['client_id'] = [$paramArray['client_id']];
        }
        for ($i = 0; $i < count($paramArray['client_id']); $i++) {
            $client_id = $paramArray['client_id'][$i];
            $dataClient['client_id'] = $paramArray['client_id'][$i];
//            $dataClient['client_intention_level'] = 1;
//            $dataClient['client_intention_maxlevel'] = 1;
            $dataClient['client_updatetime'] = time();
            //更新客户状态
            $this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $dataClient);
            //增加跟踪记录
            $data = array();
            $data['marketer_id'] = $paramArray['marketer_id'];
            $markOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
            $data['marketer_name'] = $markOne['marketer_name'];
            $data['client_id'] = $client_id;
            $data['track_linktype'] = $this->LgStringSwitch("系统操作");
            $data['track_validinc'] = 0;
            $data['track_followmode'] = 0;
            $data['track_note'] = $this->LgStringSwitch("系统流转将无意向客户转为招生有效名单");
            $data['school_id'] = $paramArray['school_id'];
            $data['track_type'] = 0;
            $data['track_initiative'] = 0;
            $data['track_createtime'] = time();
            $this->DataControl->insertData('crm_client_track', $data);

        }
        $this->DataControl->commit();
        return true;
    }

    /**
     * @param $client_id
     * @return bool
     *  新增一个客户的分配记录-解除记录 -由负责人表查询
     *  -1 解除所有人
     *   解除负责人状态一定要放在调用这个方法后面
     */
//	 											负责人			操作人
    function addTmKRemoveAllotLog($client_id, $main_marketer_id, $marketer_id, $school_id)
    {
        $where = " 1 and  tmkprincipal_leave = 0 ";
        if ($main_marketer_id === -1) {
            $where .= " and client_id='{$client_id}'";
        } else {
            $where .= " and client_id='{$client_id}' and marketer_id='{$main_marketer_id}' ";
        }
        $clientPrincipalList = $this->DataControl->getList("crm_client_tmkprincipal", $where, "order by tmkprincipal_createtime DESC");
        if ($clientPrincipalList) {
            $dataAllotlog = array();
            $dataAllotlog['tmkallotlog_status'] = 0;
            $dataAllotlog['tmkallotlog_removetime'] = time();
            foreach ($clientPrincipalList as $key => $value) {
                if ($this->DataControl->updateData('crm_client_tmkallotlog', "tmkallotlog_status =1 and school_id='{$school_id}' and client_id='{$client_id}' and allot_marketer_id='{$value['marketer_id']}'", $dataAllotlog)) {
                    $res = true;
                } else {
                    $res = false;
                }
            }
            return $res;
        } else {
            return true;
        }
    }
    /**
     * @param $paramArray
     * @return array
     * 流转 无效无意向-毛名单
     */
    function changeLossClientTmkApi($paramArray)
    {
        $clientArray = json_decode(stripslashes($paramArray['clients_json']), 1);
        if(empty($clientArray))
        {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        }else {
            $dataClient = array();
            $dataClient['client_distributionstatus'] = 0;
            $dataClient['client_tracestatus'] = 0;
            $dataClient['client_ischaserlapsed'] = 0;
            $dataClient['client_isinvalidreview'] = 0;
            $dataClient['invalidnote_code'] = '';
            $dataClient['client_isgross'] = 1;
            $dataClient['client_schtmkdistributionstatus'] = 0;
            $dataClient['client_invalidmarketer_id'] = 0;
            $dataClient['client_invalidreviewtime'] = '';
            $dataClient['client_updatetime'] = time();

            foreach ($clientArray as $clientArrayVar){
                $client_id = $clientArrayVar['client_id'];
                $dataClient['client_id'] = $clientArrayVar['client_id'];
                //更新客户状态
                $this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $dataClient);

                if($this->DataControl->selectOne("select * from crm_client_tmkprincipal where client_id='{$client_id}'")) {
                    //解除负责人
                    $dataPrincipal = array();
                    $dataPrincipal['tmkprincipal_leave'] = 1;
                    $dataPrincipal['tmkprincipal_updatatime'] = time();
                    //增加分配日志 解除 TMK 所有人的负责日志
                    if (!$this->addTmKRemoveAllotLog($client_id, -1, $paramArray['marketer_id'], $paramArray['school_id'])) {
                        $this->error = 1;
                        $this->errortip = "增加分配日志:解除日志 失败";
                        return false;
                    }
                    //解除所有人的负责状态  一定要先添加解除日志
                    if (!$this->DataControl->updateData('crm_client_tmkprincipal', "client_id='{$client_id}'", $dataPrincipal)) {
                        $this->error = 1;
                        $this->errortip = "解除负责人失败";
                        return false;
                    }
                }

                //增加跟踪记录
                $data = array();
                $data['marketer_id'] = $paramArray['marketer_id'];
                $markOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
                $data['marketer_name'] = $markOne['marketer_name'];
                $data['client_id'] = $client_id;
                if($paramArray['re_postbe_id'] == '0'){
                    $data['track_linktype'] = $this->LgStringSwitch("主管操作");
                }else{
                    $postbe = $this->DataControl->selectOne("select postbe_crmuserlevel from gmc_staffer_postbe where postbe_id = '{$paramArray['re_postbe_id']}' limit 0,1");
                    if($postbe['postbe_crmuserlevel'] == '1'){
                        $data['track_linktype'] = $this->LgStringSwitch("主管操作");
                    }
                }
                $data['track_validinc'] = 0;
                $data['track_followmode'] = 0;
                if($paramArray['moduletype'] == '-1'){
                    $data['track_note'] = $this->LgStringSwitch("无意向名单转回毛名单");
                }elseif($paramArray['moduletype'] == '-2') {
                    $data['track_note'] = $this->LgStringSwitch("无效名单转回毛名单");
                }elseif($paramArray['moduletype'] == '1'){
                    $data['track_note'] = $this->LgStringSwitch("电销名单转回毛名单");
                }
                $data['track_createtime'] = time();
                $data['school_id'] = $paramArray['school_id'];
                $data['track_intention_level'] = 0;
                $data['track_type'] = 0;
                $data['track_initiative'] = 0;
                $this->DataControl->insertData('crm_client_track', $data);
            }
            $this->error = 0;
            $this->errortip = "批量操作成功";
            return true;
        }
    }

    /**
     * @param $paramArray
     * @return array
     * 流转 无意向客户-意向客户
     */
    function changeLossClientToIntention($paramArray)
    {
        $dataClient = array();
        $dataClient['client_distributionstatus'] = $paramArray['client_distributionstatus'];
        $dataClient['client_tracestatus'] = $paramArray['client_tracestatus'];
        $dataClient['client_ischaserlapsed'] = $paramArray['client_ischaserlapsed'];
        $dataClient['client_isinvalidreview'] = $paramArray['client_isinvalidreview'];
        $dataClient['invalidnote_code'] = $paramArray['invalidnote_code'];
        $dataClient['client_invalidmarketer_id'] = 0;
        $dataClient['client_invalidreviewtime'] = '';
        $dataClient['client_updatetime'] = time();
        $marketer_id = $paramArray['marketer_id'];

        if (!is_array($paramArray['client_id'])) {
            $paramArray['client_id'] = [$paramArray['client_id']];
        }
        for ($i = 0; $i < count($paramArray['client_id']); $i++) {
            $client_id = $paramArray['client_id'][$i];
            $dataClient['client_id'] = $paramArray['client_id'][$i];
//            $dataClient['client_intention_level'] = 1;
//            $dataClient['client_intention_maxlevel'] = 1;

            //更新客户状态
            $this->DataControl->begintransaction();
            if (!$this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $dataClient)) {
                $this->DataControl->rollback();
                return false;
            }

            //添加主负责人记录
            $this->model = new \Model\Crm\ClientModel();
            if (!$this->model->insertClientPrincipal($client_id, $marketer_id, $paramArray['school_id'], 1)) {
                $this->DataControl->rollback();
                return false;
            }

            $clientOne = $this->DataControl->selectOne("select client_tracestatus from crm_client where client_id = '{$client_id}' ");

            //增加跟踪记录
            $data = array();
            $data['marketer_id'] = $paramArray['marketer_id'];
            $markOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
            $data['marketer_name'] = $markOne['marketer_name'];
            $data['marketer_id'] = $paramArray['marketer_id'];
            $data['client_id'] = $client_id;
            $data['track_linktype'] = $this->LgStringSwitch("系统操作");
            $data['track_validinc'] = 0;
            $data['track_followmode'] = 0;
            if($clientOne['client_tracestatus'] == '-2'){
                $data['track_note'] = $this->LgStringSwitch("系统流转将无效客户转为意向客户");
            }else{
                $data['track_note'] = $this->LgStringSwitch("系统流转将无意向客户转为意向客户");
            }

            $data['school_id'] = $paramArray['school_id'];
            $data['track_type'] = 0;
            $data['track_initiative'] = 0;
            $data['track_createtime'] = time();
            if (!$this->DataControl->insertData('crm_client_track', $data)) {
                $this->DataControl->rollback();
                return false;
            }
            //增加分配记录
            $clintModel = new \Model\Crm\ClientModel();
            if (!$clintModel->addAllotLog($client_id, $paramArray['marketer_id'], $paramArray['marketer_id'], $paramArray['school_id'], 1)) {
                $this->DataControl->rollback();
                return false;
            }
        }

        $this->DataControl->commit();

        return true;
    }

    function lossExamine($paramArray)
    {
        $data = array();
        $data['client_ischaserlapsed'] = 1;
        $data['client_distributionstatus'] = 0;
        $data['client_tracestatus'] = '-1';
        $data['client_updatetime'] = time();
        $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $data);

        /**解除负责人状态**/
        $principalArray = $this->DataControl->selectClear("select principal_id,client_id,principal_ismajor,marketer_id
FROM crm_client_principal where principal_leave = '0' AND school_id <> '0' and client_id='{$paramArray['client_id']}' ");
        if ($principalArray) {
            $palData = array();
            $palData['principal_leave'] = 1;
            $palData['principal_updatatime'] = time();
            foreach ($principalArray as $principalOne) {
                $this->DataControl->updateData('crm_client_principal', "principal_id='{$principalOne['principal_id']}'", $palData);
                $dataAllotlog = array();
                $dataAllotlog['client_id'] = $paramArray['client_id'];
                $dataAllotlog['allotlog_status'] = 0;
                $dataAllotlog['allotlog_removetime'] = time();
                $this->DataControl->updateData('crm_client_allotlog', "allotlog_status =1 and school_id='{$paramArray['school_id']}' and client_id='{$paramArray['client_id']}' and allot_marketer_id='{$principalOne['marketer_id']}'", $dataAllotlog);
            }
        }


        $trackData = array();
        $trackData['client_id'] = $paramArray['client_id'];
        $trackData['marketer_id'] = $paramArray['marketer_id'];
        $marketOne = $this->DataControl->getFieldOne('crm_marketer', "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
        $trackData['marketer_name'] = $marketOne['marketer_name'];
        $trackData['client_id'] = $paramArray['client_id'];
        $trackData['track_linktype'] = $this->LgStringSwitch("系统操作");
        $trackData['track_note'] = $marketOne['marketer_name'] . $this->LgStringSwitch("主管审核无意向名单");
        $trackData['track_followmode'] = '-2';
        $trackData['track_validinc'] = 0;
        $trackData['track_state'] = -1;
        $trackData['school_id'] = $paramArray['school_id'];
        $trackData['track_createtime'] = time();
        $trackData['track_type'] = 0;
        $trackData['track_initiative'] = 0;
        $this->DataControl->insertData("crm_client_track", $trackData);
        return true;
    }


    function lossInvalidExamine($paramArray)
    {
        $data = array();
        $data['client_distributionstatus'] = 0;
        $data['client_gmcdistributionstatus'] = 0;
        $data['client_tracestatus'] = -2;

        $data['client_isinvalidreview'] = 1;
        $data['client_invalidmarketer_id'] = $paramArray['marketer_id'];
        $data['client_invalidreviewtime'] = date("Y-m-d H:i:s",time());
        $data['client_updatetime'] = time();
        $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $data);

        //解除负责人 --- 250711 补充
        $this->liftClientPrincipa('crm',$paramArray['client_id'],$paramArray['school_id']);
        //解除柜询
        $this->liftClientInvite('crm',$paramArray['client_id'],$paramArray['school_id']);
        //解除试听
        $this->liftClientAudition('crm',$paramArray['client_id'],$paramArray['school_id']);


        $trackData = array();
        $trackData['client_id'] = $paramArray['client_id'];
        $trackData['marketer_id'] = $paramArray['marketer_id'];
        $marketOne = $this->DataControl->getFieldOne('crm_marketer', "marketer_name", "marketer_id='{$paramArray['marketer_id']}'");
        $trackData['marketer_name'] = $marketOne['marketer_name'];
        $trackData['client_id'] = $paramArray['client_id'];
        $trackData['track_linktype'] = $this->LgStringSwitch("主管审核");
        $trackData['track_note'] = $marketOne['marketer_name'] . $this->LgStringSwitch("主管审核无效名单");
        $trackData['track_followmode'] = '-4';
        $trackData['track_validinc'] = 0;
        $trackData['track_state'] = -2;
        $trackData['school_id'] = $paramArray['school_id'];
        $trackData['track_createtime'] = time();
        $trackData['track_type'] = 0;
        $trackData['track_initiative'] = 0;
        $this->DataControl->insertData("crm_client_track", $trackData);
        return true;
    }

    function getInvalidClientList($paramArray)
    {
        $datawhere = " 1 and c.client_tracestatus =-2 and c.client_distributionstatus = 0  and s.school_id='{$paramArray['school_id']}'  and cs.is_enterstatus = 1 and cs.company_id='{$paramArray['company_id']}'  and c.client_isgross = '0'  ";
        //不包含集团这边 分配过来的人
        $datawhere .= " and not exists(select 1 from crm_client_schoolenter as h where h.client_id=c.client_id and h.school_id='{$paramArray['school_id']}' and is_enterstatus = '1' and h.is_gmctocrmschool = '1' ) ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%'  or c.client_frompage like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['client_source']) && $paramArray['client_source'] !== '') {
            $datawhere .= "  and c.client_source = '{$paramArray['client_source']}'";
        }
        if (isset($paramArray['client_isinvalidreview']) && $paramArray['client_isinvalidreview'] !== '') {
            $datawhere .= "  and c.client_isinvalidreview = '{$paramArray['client_isinvalidreview']}'";
        }
        if (isset($paramArray['invalidnote_code']) && $paramArray['invalidnote_code'] !== '') {
            $datawhere .= "  and c.invalidnote_code = '{$paramArray['invalidnote_code']}'";
        }
        //创建时间
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] !== '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >= '{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] !== '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 24 * 60 * 60 - 1;
            $datawhere .= " and c.client_createtime <= '{$create_endtime}'";
        }
        //历史最高意向星级
        if (isset($paramArray['client_intention_maxlevel']) && $paramArray['client_intention_maxlevel'] !== '') {
            $datawhere .= "  and c.client_intention_maxlevel = '{$paramArray['client_intention_maxlevel']}'";
        }
        //渠道类型
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '') {
            $datawhere .= " and c.client_source = '{$paramArray['frommedia_name']}'";
        }
        $Having = '1';
        $havingsql = '';
        //是否参与试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $Having .= " and active_audition_id is null ";
            $havingsql .= "(select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//已试听
            $Having .= " and active_audition_id is not null ";
            $havingsql .= "(select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] != "") {
            $all_num = $this->DataControl->selectOne("
 				select {$havingsql}count(c.client_id) as client_num                
				from  crm_client as c
				Left JOIN crm_client_schoolenter as cs ON cs.client_id = c.client_id
				Left JOIN smc_school as s ON s.school_id = cs.school_id
				where {$datawhere} HAVING {$Having} 
				");
            $allnums = $all_num['client_num'];
            if ($allnums) {
                $data['allnums'] = $allnums;
            } else {
                $data['allnums'] = 0;
            }
        }
        $sqlfield = 'c.client_id,c.client_ischaserlapsed,c.client_cnname,c.client_enname,c.client_sex,c.client_age,c.client_tag,c.client_intention_level,c.client_intention_maxlevel,c.client_mobile,c.client_source,s.school_cnname,c.client_sponsor,client_fromtype,a.activity_name,c.client_createtime,client_address,c.client_remark,c.client_email,c.client_frompage';
        $sqlorder = 'c.client_updatetime';
        $sql = "select  {$sqlfield} ,{$havingsql}
                (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
				(select p.parenter_cnname FROM crm_client_family as f LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				(select concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )  from  crm_client_principal  as p
				 LEFT JOIN crm_marketer  as m ON  m.marketer_id = p.marketer_id
				 LEFT JOIN smc_staffer as sf On sf.staffer_id = m.staffer_id
				where p.client_id = c.client_id and  principal_ismajor=1 and principal_leave=1  and p.school_id=cs.school_id ORDER by principal_createtime DESC limit 0,1 ) as marketer_name,
				(select group_concat(concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ))  from  crm_client_principal  as p
				 LEFT JOIN crm_marketer  as m ON  m.marketer_id = p.marketer_id
				 LEFT JOIN smc_staffer as sf ON sf.staffer_id =m.staffer_id
				where p.client_id = c.client_id and  principal_ismajor=0 and principal_leave=1  and p.school_id=cs.school_id ORDER by principal_createtime DESC limit 0,1 ) as fu_marketer_name,
			    (select cn.channel_name from crm_code_channel as cn where cn.channel_id=c.channel_id) as channel_name ,
			    (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and t.track_followmode ='-1' order by track_createtime DESC limit 0,1) as tracktime,
                (select concat(m.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )) from crm_client_track as t,crm_marketer as m,smc_staffer as sf where t.client_id = c.client_id and t.school_id = cs.school_id and t.track_followmode ='-1' and t.marketer_id = m.marketer_id and sf.staffer_id=m.staffer_id  order by track_createtime DESC limit 0,1) as caozuo_name,
                c.client_invalidreviewtime as shenhe_time,
                ifnull((select m.marketer_name from crm_marketer as m where m.marketer_id = c.client_invalidmarketer_id  limit 0,1),'') as shenhe_name,
                (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and track_isactive =1 order by track_createtime DESC limit 0,1) as last_tracktime,
                (select t.track_note from crm_client_track as t where t.client_id = c.client_id and t.school_id = cs.school_id and track_isactive =1 order by track_createtime DESC limit 0,1) as last_track_note, 
                
			    (select r.invalidnote_reason from crm_code_invalidnote as r where c.invalidnote_code=r.invalidnote_code ) as invalid_tj_reason,
			    (select FROM_UNIXTIME(r.track_createtime) from crm_client_track as r where c.client_id=r.client_id and r.track_followmode ='-3' order by r.track_createtime desc limit 0,1  ) as invalid_tj_time,
			    
			    (select r.region_name from smc_code_region as r where c.province_id=r.region_id ) as province_name,
                (select r.region_name from smc_code_region as r where c.city_id=r.region_id ) as city_name,
			    (select r.region_name from smc_code_region as r where c.area_id=r.region_id ) as area_name
				from  crm_client as c
				Left JOIN crm_client_schoolenter as cs ON cs.client_id = c.client_id
				Left JOIN smc_school as s ON s.school_id = cs.school_id
				LEFT JOIN crm_sell_activity as a ON a.activity_id = c.activity_id
				where {$datawhere} HAVING {$Having} Order by  {$sqlorder} DESC ";

        $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $arr_fromtype = array(0 => '外部招生', 1 => '内部招生', 2 => '专案招生');
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            if ($dateexcelarray) {
                foreach ($dateexcelarray as &$value) {
                    if ($value['client_ischaserlapsed'] == 0) {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("未确认");
                    } else {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("已确认");
                    }
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
//                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
//                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_allname'] = $dateexcelvar['client_enname']?$dateexcelvar['client_cnname']."-".$dateexcelvar['client_enname']:$dateexcelvar['client_cnname'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_tag'] = $this->LgStringSwitch($dateexcelvar['client_tag']);
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];

                    $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);

                    $datearray['client_email'] = $dateexcelvar['client_email'];
                    $datearray['client_intention_maxlevel'] = $dateexcelvar['client_intention_maxlevel'];
                    $datearray['ishaveaudition'] = $dateexcelvar['active_audition_id'] ? '是' : '否';
                    $datearray['last_tracktime'] = $dateexcelvar['last_tracktime'] == false ? '--' : date("Y-m-d H:i:s", $dateexcelvar['last_tracktime']);
                    $datearray['last_track_note'] = $dateexcelvar['last_track_note'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['client_fromtype_name'] = $arr_fromtype[$dateexcelvar['client_fromtype']];
//                    if($paramArray['client_isinvalidreview'] == '1') {
                        $datearray['shenhe_name'] = $dateexcelvar['shenhe_name']?$dateexcelvar['shenhe_name']:'--';
                        $datearray['shenhe_time'] = $dateexcelvar['shenhe_time']?$dateexcelvar['shenhe_time']:'--';
//                    }
                    $datearray['invalid_tj_reason'] =  $dateexcelvar['invalid_tj_reason'];
                    $datearray['invalid_tj_time'] =  $dateexcelvar['invalid_tj_time'];

                    $datearray['client_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['client_createtime']);;
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['city_name'] = $dateexcelvar['city_name'];
                    $datearray['area_name'] = $dateexcelvar['area_name'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 0) {
                $excelheader = $this->LgArraySwitch(array('姓名',  '年龄', '性别', '标签', '主要联系人', '主要联系手机','Email', '历史最高意向星级', '是否参与试听', '最后跟踪时间', '最后跟踪内容', '主要负责人', '活动', '渠道类型', '渠道明细','接触点', '推荐人', '招生来源类型', '审核人', '审核时间', '无效原因', '无效提交时间', '名单创建时间', '联系地址', '省', '市', '区', '备注'));
                $excelfileds = array('client_allname', 'client_age', 'client_sex', 'client_tag', 'family_cnname', 'client_mobile','client_email', 'client_intention_maxlevel', 'ishaveaudition', 'last_tracktime', 'last_track_note', 'marketer_name', 'activity_name', 'client_source', 'channel_name','client_frompage', 'client_sponsor', 'client_fromtype_name', 'shenhe_name', 'shenhe_time','invalid_tj_reason', 'invalid_tj_time', 'client_createtime', 'client_address', 'province_name', 'city_name', 'area_name', 'client_remark');
            } else {
                $excelheader = $this->LgArraySwitch(array('姓名',  '年龄', '性别', '标签', '主要联系人', '主要联系手机','Email', '历史最高意向星级', '是否参与试听', '最后跟踪时间', '最后跟踪内容', '主要负责人', '协助负责人', '活动', '渠道类型', '渠道明细','接触点', '推荐人', '招生来源类型', '审核人', '审核时间', '无效原因', '无效提交时间', '名单创建时间', '联系地址', '省', '市', '区', '备注'));
                $excelfileds = array('client_allname', 'client_age', 'client_sex', 'client_tag', 'family_cnname', 'client_mobile','client_email', 'client_intention_maxlevel', 'ishaveaudition', 'last_tracktime', 'last_track_note', 'marketer_name', 'fu_marketer_name', 'activity_name', 'client_source', 'channel_name','client_frompage', 'client_sponsor', 'client_fromtype_name', 'shenhe_name', 'shenhe_time','invalid_tj_reason', 'invalid_tj_time', 'client_createtime', 'client_address', 'province_name', 'city_name', 'area_name', 'client_remark');
            }
            $fielname = $this->LgStringSwitch("无意向名单记录表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num} ";
            $clientList = $this->DataControl->selectClear($sql);
            if ($clientList) {
//                0外部招生1内部招生2专案招生
                $arr_fromtype = array(0 => '外部招生', 1 => '内部招生', 2 => '专案招生');
                foreach ($clientList as &$value) {
                    $value['client_allname'] = $value['client_enname']?$value['client_cnname']."-".$value['client_enname']:$value['client_cnname'];
                    $value['client_frompage'] = $value['client_frompage']?$value['client_frompage']:'--';
                    if ($value['client_tag']) {
                        $value['client_tag'] = explode(',', $this->LgStringSwitch($value['client_tag']));
                    } else {
                        $value['client_tag'] = array();
                    }
                    if(!is_null($value['course_cnname'])){
                        $value['course_cnname'] = explode(',', $value['course_cnname']);
                    }else{
                        $value['course_cnname'] = array();
                    }
                    $value['client_fromtype_name'] = $arr_fromtype[$value['client_fromtype']];
                    if ($value['last_tracktime']) {
                        $value['last_tracktime'] = date("Y-m-d H:i:s", $value['last_tracktime']);
                    } else {
                        $value['last_tracktime'] = '--';
                    }
                    if ($value['tracktime']) {
                        $value['tracktime'] = date("Y-m-d H:i:s", $value['tracktime']);
                    } else {
                        $value['tracktime'] = '--';
                    }
                    $value['client_createtime'] = date("Y-m-d H:i:s", $value['client_createtime']);
                    if ($value['client_ischaserlapsed'] == 0) {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("未确认");
                    } else {
                        $value['client_chaserlapsedname'] = $this->LgStringSwitch("已确认");
                    }
                    $value['shenhe_time'] = $value['shenhe_time']?$value['shenhe_time']:'';
                    if($this->isGmcPost == true){
                        $value['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    }else{
                        $value['client_mobile'] = $value['client_mobile'];
                    }
                    $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                    if($isoutCity){
                        if($isoutCity == 'local'){
                            $value['isoutmobile'] = 0;
                        }else{
                            $value['isoutmobile'] = 1;
                        }
                    }else{
                        $value['isoutmobile'] = -1;
                    }
                    if($value['active_audition_id']){
                        $value['ishaveaudition'] = '是';
                    }else{
                        $value['ishaveaudition'] = '否';
                    }
                }
                $data['list'] = $clientList;
            } else {
                $data['list'] = array();
            }
            return $data;
        }
    }

    function toEffectiveAction($request)
    {
        $arr_cleint_id = $request['client_id'];
        if (!is_array($arr_cleint_id)) {
            $arr_cleint_id = [$arr_cleint_id];
        }
        $markterOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name", "marketer_id='{$request['marketer_id']}'");
        for ($i = 0; $i < count($arr_cleint_id); $i++) {
            $data = array();
            $data['client_distributionstatus'] = '0';
            $data['client_tracestatus'] = '0';
            $data['client_ischaserlapsed'] = '0';
            $data['client_isinvalidreview'] = '0';
            $data['invalidnote_code'] = '';
            $data['client_lapsedtype'] = '0';
            $data['client_invalidmarketer_id'] = '0';
            $data['client_invalidreviewtime'] = '';
//            $data['client_intention_level'] = '1';
//            $data['client_intention_maxlevel'] = '0';
            $data['client_updatetime'] = time();
            $this->DataControl->updateData("crm_client", "client_id='{$arr_cleint_id[$i]}'", $data);

            $trackData = array();
            $trackData['school_id'] = $request['school_id'];
            $trackData['client_id'] = $arr_cleint_id[$i];
            $trackData['track_linktype'] = $this->LgStringSwitch('系统操作');
            $trackData['track_note'] = $this->LgStringSwitch('系统流转将无效客户转为招生有效名单');
            $trackData['marketer_id'] = $request['marketer_id'];
            $trackData['marketer_name'] = $markterOne['marketer_name'];
            $trackData['track_type'] = 0;
            $trackData['track_initiative'] = 0;
            $trackData['track_createtime'] = time();
            $this->DataControl->insertData('crm_client_track', $trackData);
        }
        return true;
    }


}
