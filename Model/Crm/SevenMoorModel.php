<?php
/**
 * 容联七陌 第三方接口
 */

namespace Model\Crm;


class SevenMoorModel extends modelTpl
{
    //容联 七陌
    //账户编号(ACCOUNTID)：    N00000053984
    //帐号APISecret：          40f2b520-8165-11eb-a875-4b9f1970e55c
    //请求域名(HOST)：         https://apis.7moor.com
    const ACCOUNTID = "N00000053984";
    const APISecret = "40f2b520-8165-11eb-a875-4b9f1970e55c";
    const HOST = "https://apis.7moor.com";

    public function request_curl($url,$param,$header)
    {
        $ch = curl_init ();
        curl_setopt($ch, CURLOPT_URL, ($url) );//地址
        curl_setopt($ch, CURLOPT_POST, 1);   //请求方式为post
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($param)); //post传输的数据。
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER,$header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $return = curl_exec ( $ch );

        if($return){
            $result = json_decode($return,true);
        }else{
            $result = array('error' => '1', 'errortip' => curl_error($ch), 'result' => []);
        }
        curl_close ( $ch );
        return $result;
    }

    /**
     * 隐私模式(XB模式)
     * 小号绑定接口
     * @param $midNum
     * @param $called
     * @return array|mixed
     */
    public function midNumBind($midNum,$called)
    {
        $accountid = self::ACCOUNTID;//云呼账号
        $secret		=	self::APISecret;//云呼密码
        $host = self::HOST;
        $time		=	date("YmdHis");
        $authorization = base64_encode($accountid.":".$time);
        $sig		   = strtoupper(md5($accountid.$secret.$time));//authorization

        $param = array();
        $param['midNum'] = $midNum;
        $param['called'] = $called;

        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($param) );
        $header[] = "Authorization: ".$authorization;

        $url = "{$host}/v20160818/rlxh/midNumBind/{$accountid}?sig={$sig}";
        $result = $this->request_curl($url,$param,$header);
        return $result;
    }

    /**
     * 隐私模式(XB模式)
     * 小号解绑接口
     * @param $midNum
     * @param $mappingId
     * @return array|mixed
     */
    public function midNumUnBinding($midNum,$mappingId)
    {
        $accountid = self::ACCOUNTID;//云呼账号
        $secret		=	self::APISecret;//云呼密码
        $host = self::HOST;
        $time		=	date("YmdHis");
        $authorization = base64_encode($accountid.":".$time);
        $sig		   = strtoupper(md5($accountid.$secret.$time));//authorization

        $param = array();
        $param['midNum'] = $midNum;
        $param['mappingId'] = $mappingId;
//        $param['called'] = $called;

        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($param) );
        $header[] = "Authorization: ".$authorization;

        $url = "{$host}/v20160818/rlxh/midNumUnBinding/{$accountid}?sig={$sig}";
        $result = $this->request_curl($url,$param,$header);
        return $result;

    }

    /**
     * 外呼接口模式
     * @param $FromExten 坐席工号
     * @param $Exten 被叫号码
     * @return array|mixed
     */
    public function dialout($FromExten,$Exten)
    {
        $accountid  =   self::ACCOUNTID;//云呼账号
        $secret		=	self::APISecret;//云呼密码
        $host       =   self::HOST;
        $time		=	date("YmdHis");
        $authorization = base64_encode($accountid.":".$time);
        $sig		   = strtoupper(md5($accountid.$secret.$time));//authorization

        $param = array();
        $param['FromExten'] = $FromExten;
        $param['Exten'] = $Exten;

        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($param) );
        $header[] = "Authorization: ".$authorization;

        $url = "{$host}/v20180426/rlxh/dialout/{$accountid}?sig={$sig}";

        $result = $this->request_curl($url,$param,$header);
        return $result;

    }

    /**
     * 小号话单数据推送接口
     * @param $paramArray
     */
    public function addCallbackLog($paramArray)
    {
        $data = array();
        $data['callbacklog_callno'] = $paramArray['CallNo'];//主叫号码
        $data['callbacklog_calledno'] = $paramArray['CalledNo'];//被叫真实号码
        $data['callbacklog_callsheetid'] = $paramArray['CallSheetID'];//通话记录ID
        $data['callbacklog_callid'] = $paramArray['CallID'];//通话ID
        $data['callbacklog_calltype'] = $paramArray['CallType'];//通话类型：dialout外呼通话,normal普通来电,transfer呼入转接,dialTransfer外呼转接
        $data['callbacklog_ring'] = $paramArray['Ring'];//通话振铃时间（话务进入呼叫中心系统的时间）
        $data['callbacklog_ringingdate'] = $paramArray['RingingDate'];//被叫振铃开始时间（呼入是按座席振铃的时间,外呼按客户振铃的时间）
        $data['callbacklog_begin'] = $paramArray['Begin'];//通话接通时间（双方开始通话的时间,如果被叫没接听的话为空）
        $data['callbacklog_end'] = $paramArray['End'];//通话结束时间
        $data['callbacklog_exten'] = $paramArray['Exten'];//处理坐席的工号
        $data['callbacklog_agentName'] = $paramArray['AgentName'];//处理坐席的姓名
        $data['callbacklog_state'] = $paramArray['State'];//接听状态：dealing（已接）,notDeal（振铃未接听）,leak（ivr放弃）,queueLeak（排队放弃）,blackList（黑名单）,voicemail（留言）,limit（并发限制）
        $data['callbacklog_callstate'] = $paramArray['CallState'];//事件状态：Ring, Ringing, Link, Hangup(Unlink也当成Hangup处理)
        $data['callbacklog_actionid'] = $paramArray['ActionID'];//通过调用外呼接口时,该字段会保存请求的actionID,其它情况下该字段为空
        $data['callbacklog_webcallactionid'] = $paramArray['WebcallActionID'];//通过调用webcall接口,该字段会保存请求的actionID,其它情况下该字段为空
        $data['callbacklog_recordfile'] = $paramArray['RecordFile'];//通话录音文件名
        $data['callbacklog_fileserver'] = $paramArray['FileServer'];//通过FileServer中指定的地址加上RecordFile的值可以获取录音
        $data['callbacklog_province'] = $paramArray['Province'];//目标号码的省
        $data['callbacklog_district'] = $paramArray['District'];//目标号码的市
        $data['callbacklog_cdrvar'] = $paramArray['CdrVar'];//软电话条中的自定义参数，只有在软电话条中用CdrVar自定义id后才会有该字段
        $data['callbacklog_dialoutstrvar'] = $paramArray['DialoutStrVar'];//软电话条中的自定义参数，只有在软电话条中用CdrVar自定义id后才会有该字段
        $data['callbacklog_hanguppart'] = $paramArray['HangupPart'];//挂机方，字段值解释 ：agent 坐席挂机， customer 客户挂机，system 系统挂机
        $data['callbacklog_realcalled'] = $paramArray['RealCalled'];//如果使用的是小号接口外呼或者电销小号模式外呼，这里存放的是真实被叫，CalledNo存放的是小号
        $data['callbacklog_data'] = $paramArray['callbacklog_data'];//接收到的数据
        $data['callbacklog_callstatus'] = $paramArray['CallStatus'];//接收到的数据
        $data['callbacklog_realstate'] = $paramArray['RealState'];//接收到的数据
        $data['callbacklog_agent'] = $paramArray['Agent'];//接收到的数据
        $data['callbacklog_account'] = $paramArray['Account'];//接收到的数据
        $data['callbacklog_createtime'] = time();//创建时间
        $this->DataControl->insertData("gmc_sevenmoor_callbacklog", $data);

        $list = array();
        $list['outcall_caller'] = $paramArray['CallNo'];
        $list['outcall_called'] = $paramArray['CalledNo'];
        $list['outcall_begin_time'] = strtotime($paramArray['Begin']);
        $list['outcall_end_time'] = strtotime($paramArray['End']);
        if($list['outcall_begin_time']){
            $list['outcall_duration'] = $list['outcall_end_time'] - $list['outcall_begin_time'];
        }
        if($paramArray['CallType'] == 'dialout' || $paramArray['CallType'] == 'dialTransfer'){
            $list['outcall_call_type'] = 'OUT';
        }else{
            $list['outcall_call_type'] = 'IN';
        }
        $list['outcall_trunk'] = $paramArray['CallID'];
        $list['outcall_uuid'] = $paramArray['CallSheetID'];
        $list['outcall_playurl'] = str_replace("http://106.15.78.151/", "https://tmkrecording.kidcastle.cn/", $paramArray['FileServer'] . '/' . $paramArray['RecordFile']);
        if($paramArray['State'] == 'dealing' || $paramArray['State'] == 'voicemail'){
            $list['outcall_hangupcause'] = 'success';
        }else{
            $list['outcall_hangupcause'] = $paramArray['HangupPart'];
        }
        $list['outcall_cdr_type'] = 'voice';
//        $list['outcall_json'] = json_encode($paramArray);
        $list['outcall_createtime'] = time();
        $soundlogID = $this->DataControl->insertData("gmc_outcall", $list);
        if($soundlogID) {
            $jsondata = array();
            $jsondata['outcall_id'] = $soundlogID;
            $jsondata['outcall_json'] = json_encode($paramArray);
            $jsondata['json_createtime'] = time();
            $this->DataControl->insertData("gmc_outcall_json", $jsondata);
        }
    }


    /**
     * 外呼接口模式
     * @param $FromExten 坐席工号
     * @param $Exten 被叫号码
     * @return array|mixed
     */
    public function hangup($CallID,$Agent,$ActionID)
    {
        $accountid  =   self::ACCOUNTID;//云呼账号
        $secret		=	self::APISecret;//云呼密码
        $host       =   self::HOST;
        $time		=	date("YmdHis");
        $authorization = base64_encode($accountid.":".$time);
        $sig		   = strtoupper(md5($accountid.$secret.$time));//authorization

        $param = array();
        $param['CallID'] = $CallID;
        $param['Agent'] = $Agent;
//        $param['ActionID'] = $ActionID;

        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($param) );
        $header[] = "Authorization: ".$authorization;

        $url = "{$host}/v20160818/call/hangup/{$accountid}?sig={$sig}";

        $result = $this->request_curl($url,$param,$header);
        return $result;
    }

}