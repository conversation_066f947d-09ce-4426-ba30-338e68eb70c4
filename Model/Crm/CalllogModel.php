<?php

namespace Model\Crm;

class CalllogModel  extends modelTpl
{
    public $m;
    public $error = false;
    public $errortip = "";

    function __construct()
    {
        parent::__construct();
    }

    // 外呼接口
    function Call_curl($link, $data){
        $accountid	=	"N00000044473";//云呼账号
        $secret		=	"540aeb90-05b6-11ea-93d6-295587bbe6ce";//云呼密码
        $time		=	date("YmdHis");

        $authorization	=	base64_encode($accountid.":".$time);
        $sig			=	strtoupper(md5($accountid.$secret.$time));

        $url = $link . $accountid."?sig=".$sig;

        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Content-Length: ".strlen( json_encode($data) );
        $header[] = "Authorization: ".$authorization;


        $ch = curl_init ();
        curl_setopt($ch, CURLOPT_URL, ($url) );//地址
        curl_setopt($ch, CURLOPT_POST, 1);   //请求方式为post
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data)); //post传输的数据。
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER,$header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $return = curl_exec ( $ch );

        if($return === FALSE ){
            echo "CURL Error:".curl_error($ch);exit;
        }

        curl_close ( $ch );

        return $return;
    }
}