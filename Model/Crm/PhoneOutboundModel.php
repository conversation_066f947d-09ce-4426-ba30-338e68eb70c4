<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

use Work\Controller\Wwwapi\LoginController;

class PhoneOutboundModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $errortipaa = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    //支持多种传值方式
    function httpRequest($url, $data='', $method='GET',$header=''){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if($header!='') {//可以传递 RAW 方式下的 Josn 值
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }
        if($method=='POST')
        {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '')
            {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

    // -------------------- 慧捷 普通电话 ---- 开始 ---------------------------
    //慧捷 -- 获取平台后端接口访问token
    function getHuijieToken(){
        $nowtime = time();
        $tokenOne = $this->DataControl->selectOne(" select * from crm_merchant_token where token_type = 1 and token_failuretime > '{$nowtime}'");
        if($tokenOne['token_string']){
            $nowtoken = $tokenOne['token_string'];
        }else {
            $param = array(
                'appId' => "00e79317-e617-4d5e-9c4c-1f4ce3b10462", //
                'appSecret' => "1EF4B181-1B99-4212-8408-DA5E0C1BEFF8", //
                'apiKey' => "7A5sSpQuvFuMdw3mmsKrb71IJjYGUqWR", //
            );
            $header = array();
            $header[] = "Accept: application/json";
            $header[] = "Content-type: application/json;charset='utf-8'";

            //POST参数 RAW中JSON方式传值获取结果
            $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/accesstoken/apply", json_encode($param), "POST", $header);
            $bakData = json_decode($getBackJson, true);

            //存表
            $record = array();
            $record['token_type'] = 1;
            $record['token_string'] = $bakData['access_token'];
            $record['token_failuretime'] = strtotime(date('Y-m-d', strtotime('+1 day')));
            $this->DataControl->insertData("crm_merchant_token",$record);

            $nowtoken = $bakData['access_token'];
        }
        return $nowtoken;
    }

    /**
     * 慧捷双呼(老的) 不适用了
     * @param $marketer_mobile  主叫号码
     * @param $client_mobile    被叫号码
     * @param int $numbertype   外显号码类型 1 固化 2 手机（被叫人手机显示的号码）
     * @param $seats_account    分机号
     */
    function getHuijieDualCallOld($request,$marketer_mobile,$client_mobile,$numbertype=1,$seats_account){
        //获取慧捷访问Token
        $tokenData = $this->getHuijieToken();

        $prefix = ($numbertype == 2)?9:8;
        $param = [
            "deviceId" => $seats_account, //发起外呼的分机号码（双呼模式下不需要注册分机，通话录音会关联此分机号）
            "prefix" => $prefix, //外呼前缀  固话前缀是8  手机号前缀是9
            "customerPhone" => $client_mobile, //客户原始号码；***********建波的手机号码
//            "options" => [
//                "bindPhone" => "8{$marketer_mobile}",//固定前缀 8 或 9 ，看绑定的主叫号码前缀是几
//            ]
        ];

        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/ext/v2/calls/uc", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);

        if($bakData['callId']){
            $record = array();
            $record['school_id'] = $request['school_id'];
            $record['client_id'] = $request['client_id'];
            $record['marketer_id'] = $request['marketer_id'];
            $record['callrecord_type'] = 1;
            $record['callrecord_callid'] = $bakData['callId'];
            $record['callrecord_createtime'] = time();
            $this->DataControl->insertData("crm_client_callrecord",$record);

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "正在拨打电话！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "没有拨通电话！";
            return false;
        }
    }
    function getHuijieDualCall($request,$marketer_mobile,$client_mobile,$numbertype=1,$seats_account,$city_id=0){
        //获取慧捷访问Token
        $tokenData = $this->getHuijieToken();

//        2、目前97外呼资源调用方案是：如果当下这个城市没有固话资源，是调用的上海线路，可不可以调用当前城市手机资源（手机资源每个城市都有）
//        回复：假如选择固话线路没有资源，系统判别走当前城市手机线路资源，如当前城市没有资源，走备用资源（例如上海）
//        3、当未来我们临时出现新的城市来不及拿到号码资源时候，无论CC选择固话或手机，调用上海的备用固话线路
//        回复：是的
        $prefix = ($numbertype == 2)?9:8;
        if($city_id > 0){
//            if($prefix == '9'){ //手机号 通路
//                if($city_id == '85'){//长春市
//                    $prefix = '90431';
//                }elseif($city_id == '326'){//西安市
//                    $prefix = '9029';
//                }elseif($city_id == '133'){//合肥市
//                    $prefix = '90551';
//                }elseif($city_id == '328'){//宝鸡市
//                    $prefix = '90917';
//                }elseif($city_id == '86'){//吉林市
//                    $prefix = '90432';
//                }elseif($city_id == '329'){//咸阳市
//                    $prefix = '9029';
//                }elseif($city_id == '107'){//上海市
//                    $prefix = '9021';
//                }elseif($city_id == '273'){//成都市
//                    $prefix = '9028';
//                }else{
//                    $prefix = '8021';//没有资源全部走 （上海固话资源了）
//                }
//            }else{
                if($city_id == '85'){//长春市  --- 没有固话，走手机资源
                    $prefix = '80431';
                }elseif($city_id == '326'){//西安市  -- 走固话资源
                    $prefix = '8029';
                }elseif($city_id == '133'){//合肥市  --- 没有固话，走手机资源
                    $prefix = '80551';
                }elseif($city_id == '328'){//宝鸡市  --- 没有固话，走手机资源
                    $prefix = '80917';
                }elseif($city_id == '86'){//吉林市  --- 没有固话，走手机资源
                    $prefix = '80432';
                }elseif($city_id == '329'){//咸阳市  --- 没有固话，走手机资源
                    $prefix = '8029';
                }elseif($city_id == '107'){//上海市  -- 走固话资源
                    $prefix = '8021';
                }elseif($city_id == '273'){//成都市  --- 没有固话，走手机资源
                    $prefix = '8028';
                }elseif($city_id == '122'){//杭州市  --- 没有固话，走手机资源
                    $prefix = '80571';
                }else{
                    $prefix = '8021';//没有资源全部走 （上海固话资源了）
                }
//            }
        }

        //400 电话
        if($numbertype == '3'){
            $prefix = '95';
        }

        $param = [
            "prefix" => $prefix, //外呼前缀  固话前缀是8  手机号前缀是9
            "options" => [
                "bindPhone" => "8{$marketer_mobile}",//主叫号码
                "namespace" => "jdb.cc",//域名
            ],
            "customerPhone" => $client_mobile, //客户原始号码
            "userData" => [
                "hjbranch" => "{$request['callbranch']}",//用户随流数据, json 数据格式
            ]
        ];

        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/ext/v2/calls/uc/bindPhone", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);

        $record = array();
        $record['school_id'] = $request['school_id'];
        $record['client_id'] = $request['client_id'];
        $record['marketer_id'] = $request['marketer_id'];
        $record['callrecord_type'] = 1;
        $record['callrecord_callid'] = $bakData['callId'];
        $record['callrecord_createtime'] = time();
        $this->DataControl->insertData("crm_client_callrecord",$record);

        if($bakData['callId']){

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打成功......请等待系统回拨！";
            $this->result = $result;
            return true;
        }else{
            $recordOne = array();
            $recordOne['school_id'] = $request['school_id'];
            $recordOne['client_id'] = $request['client_id'];
            $recordOne['marketer_id'] = $request['marketer_id'];
            $recordOne['callerrorlog_class'] = 1;
            $recordOne['callerrorlog_parameter'] = json_encode($param);
            $recordOne['callerrorlog_bakjson'] = $getBackJson;
            $recordOne['callerrorlog_time'] = time();
            $this->DataControl->insertData("crm_merchant_seats_callerrorlog",$recordOne);

            $this->error = 1;
            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
            return false;
        }
    }

    //校务对接 吴磊简版的 点击事件（TMK）
    function addCallClickAction($paramArray){
        if(!$paramArray['client_id']){
            $this->error = 1;
            $this->errortip = '有效名单ID不能为空';
            return false;
        }
        if(!$paramArray['getBackJson']){
            $this->error = 1;
            $this->errortip = '慧捷返回值不能为空';
            return false;
        }

        $bakData = json_decode(stripslashes(stripslashes($paramArray['getBackJson'])), 1);//慧捷的返回值
        $markereOne = $this->DataControl->selectOne(" select * from crm_marketer where company_id = '{$paramArray['company_id']}' and staffer_id = '{$paramArray['staffer_id']}' limit 0,1 ");

        $record = array();
        $record['school_id'] = -1;
        $record['client_id'] = $paramArray['client_id'];
        $record['marketer_id'] = $markereOne['marketer_id'];
        $record['callrecord_type'] = 1;
        $record['callrecord_callid'] = $bakData['callId'];
        $record['callrecord_custompara'] = "TMK";
        $record['callrecord_createtime'] = time();
        $callrecordid = $this->DataControl->insertData("crm_client_callrecord",$record);
        if($callrecordid){
            $this->error = 0;
            $this->errortip = '添加成功';
            return false;
        }else{
            $this->error = 1;
            $this->errortip = '添加失败';
            return false;
        }
    }
    /**
     * 慧捷事件订阅 -- 事件回调接口 -- 通话结束事件
     * @param $request
     */
    function callBackHuijieAction($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            $this->error = 1;
            $this->errortip = "未获取到返回值！";
            return true;
            die;
        }
        $requestdata = json_decode($request, true);

        //判断回调的是不是集团TMK软电话  然后区分 校还是园的电话，分别入库
        if($requestdata['namespace'] == 'jdbtmk.cc'){
            $tmknumber = explode("@",$requestdata['ani']);
            $yuan = ['8005','8006','8007','8008'];
            if($tmknumber['1'] == 'jdbtmk.cc' && in_array($tmknumber[0],$yuan)){
                $data = array();
                $data['data'] = $request;
                $apiSting = request_by_curl("https://psatmk.kedingdang.com/Api/callBackHuijieAction", dataEncode($data), 'post');
//                $apiSting = request_by_curl("http://psatmk.kcclassin.com/Api/callBackHuijieAction", dataEncode($data), 'post');
                $ApiArray = json_decode($apiSting, "1");

                ajax_return(array('error' => $ApiArray['error'], 'errortip' => $ApiArray['errortip'], 'result' => $ApiArray['result']['list']));
                exit;
            }
        }

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$requestdata['callId']}' and callbacklog_class = '1' and callbacklog_eventtype = '1' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 1;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/Callout/callBackHuijieAction";
        $list['callbacklog_class'] = 1;//厂商 1 慧捷  2 合力  3仕决
        $list['callbacklog_eventtype'] = 1;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件
        $list['callbacklog_parameter'] = $requestdata['callId'];
        $list['callbacklog_bakjson'] = $request;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            //判断用户呼入的电话 发短信  ------ 开始

            if($requestdata['callType'] == 'Inbound') {
                $mobile = trim($requestdata['customerPhone']);
                $startTime = strtotime($requestdata['startTime']) - 3600;
//                $mobile = ***********;
//                $startTime = strtotime('2024-01-04 10:09:01.221') - 3600 * 3;

                $clientAll = $this->DataControl->selectClear("select client_id from crm_client where client_mobile = '{$mobile}' and company_id = '8888' ");
                $clientAllId = array_column($clientAll, 'client_id');
                $clientAllIdStr = implode(',', $clientAllId);
                if ($clientAllIdStr) {
                    $callrecordOne = $this->DataControl->selectOne("select b.marketer_mobile 
                            from crm_client_callrecord as a,crm_marketer as b 
                            where a.callrecord_type = '1' and a.callrecord_createtime > '{$startTime}' and a.client_id in ($clientAllIdStr) 
                            and a.marketer_id = b.marketer_id 
                            order by callrecord_id desc limit 0,1 ");
                    if ($callrecordOne['marketer_mobile']) {
                        $tilte = "名单呼入慧捷系统";
                        $somestr = '';
                        if ($requestdata['userData']['Number'] == '1') {
                            $somestr = '客户选择了：咨询成长中心，';
                        } elseif ($requestdata['userData']['Number'] == '2') {
                            $somestr = '客户选择了：咨询早教托育，';
                        } elseif ($requestdata['userData']['Number'] == '3') {
                            $somestr = '客户选择了：咨询幼儿园，';
                        }
                        $contxt = "老师您好，您之前拨打的客户手机号{$mobile}，回拨了外呼中心，{$somestr}请尽快联系，谢谢！";
                        // //短信接口不能用
                        $Controller = new LoginController();
                        $Controller->Sendmisgo($callrecordOne['marketer_mobile'], $contxt, $tilte, '名单呼入慧捷系统', 8888);
                    }
                }
            }
//            if($requestdata['callType'] == 'Inbound'){
//                $mobile = trim($requestdata['customerPhone']);
//                //一小时内发送次数
//                $mintime = time() - 3600;
//                $mislognum = $this->DataControl->selectOne("select count(mislog_id) as mislognum from gmc_mislog where mislog_mobile='{$mobile}' and mislog_tilte = '名单呼入慧捷系统' and mislog_time >= '{$mintime}' limit 0,1 ");
//                if ($mislognum['mislognum'] < 5) {
//                    //最近一次发送时间
//                    $sendmisrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='{$mobile}' and mislog_tilte = '名单呼入慧捷系统'", "order by mislog_time DESC");
//                    if(!$sendmisrz || ($sendmisrz && (time() - $sendmisrz['mislog_time']) > 60) ){
//                        $tilte = "名单呼入慧捷系统";
//                        $contxt = "xxxxxxxxxxxxxxxxxxxxxxxxxxx";
//                        // //短信接口不能用
//                        $Controller = new LoginController();
//                        $Controller->Sendmisgo($mobile, $contxt, $tilte, '名单呼入慧捷系统', 8888);
//                    }
//                }
//            }
            //判断用户呼入的电话 发短信  ------ 结束


            //修改表 crm_client_callrecord  是否接通
            $callid = $requestdata['callId'];
            $updata = array();
            $updata['callrecord_isconnect'] = 1;
            $updata['callrecord_updatetime'] = time();
            $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '1' ",$updata);

            $bindPhone = substr_replace($requestdata['systemData']['bindPhone'],'',0,1);
            $customerPhone = $requestdata['customerPhone'];
            //记录通话记录
            $this->getHuijieCallidRecording($callid,$bindPhone,$customerPhone);
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    /**
     * 慧捷 -- 根据callId查询录音记录 -- 并记录到数据表
     * @param $callid   通话ID
     * @param string $bindPhone 主叫
     * @param string $customerPhone 被叫
     * @return bool
     */
    function getHuijieCallidRecording($callid,$bindPhone='',$customerPhone='',$callbacklog_class='1'){
//        $callid = "e6d8f979-058d-4339-b100-7dc93f477a98";
        //获取慧捷访问Token
        $tokenData = $this->getHuijieToken();

        $header = array();
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/cdr/2.0/recordings/open/call/".$callid,'', "GET",$header);
        $bakData = json_decode($getBackJson, true);
        $bakData = $bakData[0];
        if(!$bakData){
            $this->error = 1;
            $this->errortip = "录音记录失败！";
            return false;
        }
        //获取通话归属信息
        $callrecordOne = $this->DataControl->selectOne("select * from crm_client_callrecord where callrecord_type = '{$callbacklog_class}' and callrecord_callid = '{$callid}' ");

        $data = array();
        $data['school_id'] = $callrecordOne['school_id'];
        $data['marketer_id'] = $callrecordOne['marketer_id'];
        $data['client_id'] = $callrecordOne['client_id'];
        $data['outcall_class'] = $callbacklog_class;//厂商 1 慧捷  2 合力  3仕决 4 极简互联 5 慧捷400电话
        $data['outcall_uuid'] = $callid;//本条通话记录的唯一ID
        $data['outcall_begin_time'] = $bakData['startTime'];//通话开始时间
        $data['outcall_end_time'] = $bakData['endTime'];//通话结束时间
        $data['outcall_playurl'] = $bakData['filePath'];//录音服务器地址  ---- 慧捷需要拼接一个域名  $bakData['recordingRepoNfsPath'].
        $data['outcall_caller'] = $bindPhone;//主叫号码
        if(mb_strlen($customerPhone)=='12' && (mb_substr($customerPhone,0,1) == '8' || mb_substr($customerPhone,0,1) == '9')){
            $data['outcall_called'] = mb_substr($customerPhone,1);//被叫号码
        }else{
            $data['outcall_called'] = $customerPhone;//被叫号码
        }
        $data['outcall_called_originally'] = $customerPhone;//被叫号码

        $data['outcall_trunk'] = $bakData['deviceId'];//分机

        $data['outcall_duration'] = $bakData['recordLength'];//通话时长
//        $data['soundlog_type'] = $bakData['CallState'];//通话类型
//        $data['soundlog_state'] = $bakData['State'];//接听状态
//        $data['soundlog_ringingdate'] = $bakData['RingingTime'];//被叫振铃开始时间
//        $data['soundlog_filename'] = $bakData['RecordFile'];//通话录音文件名
//        $data['soundlog_province'] = $bakData['Province'];//目标号码的省
//        $data['soundlog_district'] = $bakData['District'];//目标号码的市
        $data['outcall_createtime'] = time();
//        $data['outcall_json'] = $getBackJson;
        $soundlogID = $this->DataControl->insertData("gmc_outcall",$data);
        if($soundlogID){

            $jsondata = array();
            $jsondata['outcall_id'] = $soundlogID;
            $jsondata['outcall_json'] = $getBackJson;
            $jsondata['json_createtime'] = time();
            $this->DataControl->insertData("gmc_outcall_json",$jsondata);

            $this->error = 0;
            $this->errortip = "录音记录成功！";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "录音记录失败！";
            return false;
        }
    }

    /**
     * 慧捷 -- 根据callId查询录音调听资源
     * @param $callid   通话ID
     * @param string $bindPhone 主叫
     * @param string $customerPhone 被叫
     * @return bool
     */
    function getHuijieCallidRecordingFile($callid){
//        $callid = "c083c12a-26e9-48c0-9891-81404c8d3d49";
        //获取慧捷访问Token
        $tokenData = $this->getHuijieToken();

        $header = array();
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/cdr/2.0/recordings/open/call/{$callid}/stream?isDownload=0&isWav=0",'', "GET",$header);
        header('content-Type:audio/mp3');
//        header('Content-Encoding:identity');
        header('Content-Encoding:chunke');
//        header('Connection:Keep-Alive');
        header('Content-Length: '.strlen($getBackJson));
        print_r($getBackJson);die;
        $bakData = json_decode($getBackJson, true);
    }
    //测试 用  -------
    function getHuijieCallidRecordingFileTest($callid){
//        $callid = "c083c12a-26e9-48c0-9891-81404c8d3d49";
        //获取慧捷访问Token
        $tokenData = $this->getHuijieToken();

        $header = array();
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/cdr/2.0/recordings/open/call/{$callid}/stream?isDownload=0&isWav=0",'', "GET",$header);
        header('content-Type:audio/mp3');
//        header('Content-Encoding:identity');
        header('Content-Encoding:chunke');
//        header('Connection:Keep-Alive');
        header('Content-Length: '.strlen($getBackJson));
        print_r($getBackJson);die;
        $bakData = json_decode($getBackJson, true);
    }

    // -------------------- 慧捷 普通电话 ---- 结束 ---------------------------


    // -------------------- 慧捷 400电话 ---- 开始 ---------------------------
    // 因为用的 租户 和 教务系统一样，所以通话回调接口是在 教务系统，所以需要转一手从教务写入校务系统，所以用户的 token也最好是从 教务拿

    //慧捷 400 电话 -- 获取平台后端接口访问token  --- 要从 教务那边获取 token
    function getHuijieFourToken(){
        $param = array(
            'passname' => "getHJFOUEtoken",
            'password' => md5('KddGetHqHj400miyao')
        );
        //POST参数 RAW中JSON方式传值获取结果
//        $getBackJson = $this->httpRequest("http://teasxapi.qiqustuapp102.com/Merchant/getHuijieFourToken", $param, "POST");
        $getBackJson = $this->httpRequest("https://teasxapi.kidcastle.cn/Merchant/getHuijieFourToken", $param, "POST");
        $bakData = json_decode($getBackJson, true);
        return $bakData['result']['token'];
    }
    /**
     * 慧捷  400 电话 双呼
     * @param $marketer_mobile  主叫号码
     * @param $student_mobile    被叫号码
     * @param int $numbertype   外显号码类型 1 固化 2 手机（被叫人手机显示的号码）
     * @param $seats_account    分机号
     */
    function getHuijieFourDualCall($request,$marketer_mobile,$student_mobile,$numbertype=1){
        //获取慧捷访问Token
        $tokenData = $this->getHuijieFourToken();

        //400 电话
        if($numbertype == '3'){
            $prefix = '95';
        }

        $param = [
            "prefix" => $prefix, //外呼前缀  固话前缀是8  手机号前缀是9
            "options" => [
                "bindPhone" => "6{$marketer_mobile}",//主叫号码  --- 针对400屏蔽问题修改前缀为 6
                "namespace" => "jdb400.cc",//域名
            ],
            "customerPhone" => $student_mobile, //客户原始号码
            "userData" => [
                "hjbranch" => "{$request['callbranch']}",//用户随流数据, json 数据格式
                "fromproject" => "KddXw",//来源课叮铛校务
            ]
        ];

        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "wellcloud_token:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://jdc.welljoint.com:8090/api/ext/v2/calls/uc/bindPhone", json_encode($param), "POST",$header);
//        print_r($getBackJson);die;
        $bakData = json_decode($getBackJson, true);

        $record = array();
        $record['school_id'] = $request['school_id'];
        $record['client_id'] = $request['client_id'];
        $record['marketer_id'] = $request['marketer_id'];
        $record['callrecord_type'] = 5;
        $record['callrecord_callid'] = $bakData['callId'];
        $record['callrecord_createtime'] = time();
        $this->DataControl->insertData("crm_client_callrecord",$record);

        if($bakData['callId']){

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打成功......请等待系统回拨！";
            $this->result = $result;
            return true;
        }else{
            $recordOne = array();
            $recordOne['school_id'] = $request['school_id'];
            $recordOne['client_id'] = $request['client_id'];
            $recordOne['marketer_id'] = $request['marketer_id'];
            $recordOne['callerrorlog_class'] = 5;
            $recordOne['callerrorlog_parameter'] = json_encode($param);
            $recordOne['callerrorlog_bakjson'] = $getBackJson;
            $recordOne['callerrorlog_time'] = time();
            $this->DataControl->insertData("crm_merchant_seats_callerrorlog",$recordOne);

            $this->error = 1;
            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
            return false;
        }
    }

    //慧捷400通话 回调接口
    function callBackHuijieFourAction($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            $this->error = 1;
            $this->errortip = "未获取到返回值！";
            return true;
            die;
        }
        $requestdata = json_decode($request, true);

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$requestdata['callId']}' and callbacklog_class = '5' and callbacklog_eventtype = '1' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 1;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/Callout/callBackHuijieFourAction";
        $list['callbacklog_class'] = 5;//厂商 1 慧捷  2 合力  3仕决  4 极简互联 5 慧捷400电话
        $list['callbacklog_eventtype'] = 1;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件
        $list['callbacklog_parameter'] = $requestdata['callId'];
        $list['callbacklog_bakjson'] = $request;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            //修改表 crm_client_callrecord  是否接通
            $callid = $requestdata['callId'];
            $updata = array();
            $updata['callrecord_isconnect'] = 1;
            $updata['callrecord_updatetime'] = time();
            $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '5' ",$updata);

            $bindPhone = substr_replace($requestdata['systemData']['bindPhone'],'',0,1);
            $customerPhone = $requestdata['customerPhone'];
            //记录通话记录
            $this->getHuijieCallidRecording($callid,$bindPhone,$customerPhone,5);
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    // -------------------- 慧捷 400电话 ---- 结束 ---------------------------


    // -------------------- 士决 ---- 开始 ---------------------------

    /**
     * 士决 双呼
     * @param $marketer_mobile  主叫号码
     * @param $client_mobile    被叫号码
     * @param int $numbertype   外显号码类型 1 固化 2 手机（被叫人手机显示的号码）
     * @param $seats_account    分机号
     */
    function getShijueDualCall($request,$marketer_mobile,$client_mobile,$numbertype=1,$seats_account){
        //获取士决访问Token
        $tokenData = "fc41a859ca29a10fe8fa90dc7d2053f8:ccf79fc65dbd93e3f809f313860e1a57";

        $param = [
            'caller' => $marketer_mobile, // 主叫
            'callee' => $client_mobile, // 被叫
            'pstnnumber' => $seats_account, //企业大号 PSTN  有多个  ***********
            'cusData' => $request['callbranch'] //
        ];
        $header = array();
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "authorization:".$tokenData;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://call.kidcastle.com.cn/uas/********/accounts/calls/twowaycallbackWithIpAuth.json", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);

        if($bakData['statuscode'] == "000000"){
            $record = array();
            $record['school_id'] = $request['school_id'];
            $record['client_id'] = $request['client_id'];
            $record['marketer_id'] = $request['marketer_id'];
            $record['callrecord_callid'] = $request['callbranch'];//自定一个唯一编号
            $record['callrecord_type'] = 3;
            $record['callrecord_createtime'] = time();
            $this->DataControl->insertData("crm_client_callrecord",$record);

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打成功......请等待系统回拨！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
            return false;
        }
    }

    /**
     *  之前的回调地址 https://callserve.kidcastle.com.cn/callback/shijue
     * 士决 话单推送接口  ,http://api.kcclassin.com/Callout/callBackShijueAction
     * @param $request
     */
    function callBackShijueAction($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            $this->error = 1;
            $this->errortip = "未获取到返回值！";
            return true;
        }
        //返回值 json 转为 数据
        $redata = json_decode($request, true);
        if($redata){
            $allnum = 0;
            $allcount = count($redata);
            foreach ($redata as $redatavar){
                $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$redatavar['cusData']}' and callbacklog_class = '3' and callbacklog_eventtype = '1' ");
                if($recordlogOne['callbacklog_id']){
                    $allnum++;
                    continue;
                }
                //插入通话记录
                $list = array();
                $list['callbacklog_apiurl'] = "/Callout/callBackShijueAction";
                $list['callbacklog_class'] = 3;//厂商 1 慧捷  2 合力  3仕决
                $list['callbacklog_eventtype'] = 1;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件
                $list['callbacklog_parameter'] = $redatavar['call_id'];//电话 ID
                $list['callbacklog_custompara'] = $redatavar['cusData'];//自定一个拨打电话编号
                $list['callbacklog_bakjson'] = $request;
                $list['callbacklog_time'] = time();
                $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

                if($listOne && $redatavar['record_file'] != '' && $redatavar['cusData'] != '') {
                    //修改表 crm_client_callrecord  是否接通
                    $callid = $redatavar['cusData'];
                    $updata = array();
                    $updata['callrecord_isconnect'] = 1;
                    $updata['callrecord_updatetime'] = time();
                    $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '3' ",$updata);

                    $allnum++;
                }
            }
            if($allnum == $allcount){
                $this->error = 0;
                $this->errortip = "回调处理成功！";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "回调处理失败！";
                return false;
            }
        }
    }

    // -------------------- 士决 ---- 结束 ---------------------------

    // -------------------- 合力 ---- 开始 ---------------------------
    //合力 -- 获取平台后端接口访问token
    function getHeliToken(){
        $nowtime = time();
        $tokenOne = $this->DataControl->selectOne(" select * from crm_merchant_token where token_type = 2 and token_failuretime > '{$nowtime}' order by token_id desc ");
        if($tokenOne['token_string']){
            $nowtoken = $tokenOne['token_string'];
        }else {
//          //测试账号
//            $param = array(
//                'account' => "N000000003929",//
//                'appid' => "ws0vyro33lv50zfr",//
//                'secret' => "a3c0dd00392d11ec8f25b1ddcafd1faf",//
//            );
            //吉德堡正式关键参数
            $param = array(
                'account' => "N000000040320",//
                'appid' => "47khyj6elyh",//
                'secret' => "de44d1b0371f11eeb908150ab2a5c94a",//
            );
            $getBackJson = request_by_curl("https://a1.7x24cc.com/accessToken", dataEncode($param), "GET");
            $bakData = json_decode($getBackJson, true);

            //存表
            $record = array();
            $record['token_type'] = 2;
            $record['token_string'] = $bakData['accessToken'];
            $record['token_failuretime'] = time() + 7200;//合力的有效期为 2 小时
            $this->DataControl->insertData("crm_merchant_token",$record);

            $nowtoken = $bakData['accessToken'];
        }
        return $nowtoken;
    }

    /**
     * 合力双呼
     * @param $marketer_mobile  主叫号码
     * @param $client_mobile    被叫号码
     * @param int $numbertype   外显号码类型 1 固化 2 手机（被叫人手机显示的号码）
     * @param $seats_account    分机号
     */
    function getHeliDualCall($request,$marketer_mobile,$client_mobile,$numbertype=1,$seats_account){
        //获取慧捷访问Token
        $tokenData = $this->getHeliToken();

        //呼叫者 （主叫）
        $caller = $marketer_mobile;
        //被呼叫者  （被叫）
        $callee = $client_mobile;
//        //测试的  ---  和正式有些出入
//        $param = array(
//            "flag" => "107",//固定为107
//            "accessToken" => $tokenData,//该参数值请使用获取accessToken接口获取
//            "account" => $seats_account,//您的账户编号为 : xxxxxxx
//            "ServiceNo" => "***********",//外呼号码接通后 , 电话会转到这个服务号 ( 参数值见服务号与PBXID列表 )
//            "phonenum" => $caller, //被呼叫用户的手机号码或固话号码 (主叫）
//            "IvrVars"=>"num:{$callee}",//场景举例 : 将通话记录中主叫号码修复为调用接口时的外呼号码 , IvrVars=REAL_FROM_CID:138xxxxxxxx ( REAL_FROM_CID为固定参数 )  (被叫）
//            "OutBoundVars"=>"hlcallno:{$caller},hlcalledno:{$callee},school_id:{$request['school_id']},marketer_id:{$request['marketer_id']},client_id:{$request['client_id']},hlbranch:{$request['callbranch']}",//您可以在我们提供的接口后面拼接上您自己的业务参数 , 我们通过通话记录回传给您
//        );
        $param = array(
            "flag" => "107",//固定为107
            "accessToken" => $tokenData,//该参数值请使用获取accessToken接口获取
            "account" => $seats_account,//您的账户编号为 : xxxxxxx
            "ServiceNo" => "***********",//外呼号码接通后 , 电话会转到这个服务号 ( 参数值见服务号与PBXID列表 )
            "phonenum" => $caller, //第一方号码 （双呼模式技术对方技术有调整）
            "IvrVars" => "REAL_FROM_CID:{$caller},num:{$callee}",//REAL_FROM_CID:第一方号码,num:第二方号码 （双呼模式技术对方技术有调整）
            "OutBoundVars" => "hlcallno:{$caller},hlcalledno:{$callee},school_id:{$request['school_id']},marketer_id:{$request['marketer_id']},client_id:{$request['client_id']},hlbranch:{$request['callbranch']}",//您可以在我们提供的接口后面拼接上您自己的业务参数 , 我们通过通话记录回传给您
        );
        $getBackJson = request_by_curl("https://a1.7x24cc.com/commonInte", dataEncode($param), "GET");
//        $bakData = json_decode($getBackJson, true);
//        print_r($getBackJson);//   0    4
//        die;
        $record = array();
        $record['school_id'] = $request['school_id'];
        $record['client_id'] = $request['client_id'];
        $record['marketer_id'] = $request['marketer_id'];
        $record['callrecord_type'] = 2;
        $record['callrecord_callid'] = $request['callbranch'];//自定一个唯一编号
        $record['callrecord_custompara'] = $request['callbranch'];//自定一个唯一编号
        $record['callrecord_createtime'] = time();
        $this->DataControl->insertData("crm_client_callrecord",$record);
        if($getBackJson == '4'){ //4接通   3、5、0  拒接、关机、停机、响铃未接、不在服务区或空号

            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打成功......请等待系统回拨！";
            $this->result = $result;
            return true;
        }else{
            $recordOne = array();
            $recordOne['school_id'] = $request['school_id'];
            $recordOne['client_id'] = $request['client_id'];
            $recordOne['marketer_id'] = $request['marketer_id'];
            $recordOne['callerrorlog_class'] = 2;
            $recordOne['callerrorlog_parameter'] = json_encode($param);
            $recordOne['callerrorlog_bakjson'] = $getBackJson;
            $recordOne['callerrorlog_time'] = time();
            $this->DataControl->insertData("crm_merchant_seats_callerrorlog",$recordOne);

            $this->error = 1;
            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
            return false;
        }
    }

    /**
     * 合力事件订阅 -- 通话结束后回调
     * @param $request
     * @return bool
     */
    //https://callserve.kidcastle.com.cn/callback/heli  之前的通话结束事件
    //http://**********:8011/commonInte?Action=CallManager&Method=get_online_callSheet_event  接通
    function callBackHeliAction($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            die;
        }
        $requestjson = json_encode($request, JSON_UNESCAPED_UNICODE);//转为 json

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_class = '2' and callbacklog_eventtype = '1'  and callbacklog_parameter = '{$request['CallID']}' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 2;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/Callout/callBackHeliAction";
        $list['callbacklog_class'] = 2;//厂商 1 慧捷  2 合力  3仕决
        $list['callbacklog_eventtype'] = 1;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件 4 接通事件
        $list['callbacklog_parameter'] = $request['CallID'];
        $list['callbacklog_custompara'] = $request['hlbranch'];//合力 自定义唯一参数
        $list['callbacklog_bakjson'] = $requestjson;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            $updata = array();
            $updata['school_id'] = $request['school_id'];
            $updata['marketer_id'] = $request['marketer_id'];
            $updata['client_id'] = $request['client_id'];
            $updata['outcall_class'] = 2;//厂商 1 慧捷  2 合力  3仕决
            $updata['outcall_fax_result_code'] = $request['CallID'];//本条通话记录的唯一ID -- 合力的，记录到传真里
            $updata['outcall_uuid'] = $request['hlbranch'];//本条通话记录的唯一ID
            $updata['outcall_caller'] = $request['hlcallno'];//主叫号码
            $updata['outcall_called'] = $request['hlcalledno'];//被叫号码
//            $updata['soundlog_type'] = $request['CallState'];//通话类型
//            $updata['soundlog_state'] = $request['State'];//接听状态
            $updata['outcall_duration'] = $request['CallTimeLength'];//通话时长
            $updata['outcall_begin_time'] = $request['Begin'];//通话开始时间
            $updata['outcall_end_time'] = $request['End'];//通话结束时间
            $updata['outcall_playurl'] = $request['MonitorFilename'];//录音服务器地址
            $updata['outcall_createtime'] = time();
//            $updata['outcall_json'] = $requestjson;
            $soundlogID = $this->DataControl->insertData("gmc_outcall",$updata);

            if($soundlogID){
                $jsondata = array();
                $jsondata['outcall_id'] = $soundlogID;
                $jsondata['outcall_json'] = $requestjson;
                $jsondata['json_createtime'] = time();
                $this->DataControl->insertData("gmc_outcall_json",$jsondata);
            }
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    /**
     * 合力事件订阅 -- 接通回调接口
     * @param $request
     * @return bool
     */
    function callBackHeliThroughAction($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            die;
        }
        $requestjson = json_encode($request, JSON_UNESCAPED_UNICODE);//转为 json

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_class = '2' and callbacklog_eventtype = '4'  and callbacklog_parameter = '{$request['CallID']}' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 2;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/Callout/callBackHeliThroughAction";
        $list['callbacklog_class'] = 2;//厂商 1 慧捷  2 合力  3仕决
        $list['callbacklog_eventtype'] = 4;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件 4 接通事件
        $list['callbacklog_parameter'] = $request['CallID'];
        $list['callbacklog_custompara'] = $request['hlbranch'];//合力 自定义唯一参数
        $list['callbacklog_bakjson'] = $requestjson;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            //修改表 crm_client_callrecord  是否接通
            $custompara = $request['hlbranch'];
            $updata = array();
            $updata['callrecord_isconnect'] = 1;
            $updata['callrecord_updatetime'] = time();
            $this->DataControl->updateData("crm_client_callrecord"," callrecord_custompara = '{$custompara}' and callrecord_type = '2' ",$updata);
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    /**
     * 合力 -- 查询录音记录--- 没有完全走通
     * @param $request
     * @return bool
     */
    function getHeliCallRecording($request){
        //获取慧捷访问Token
        $tokenData = $this->getHeliToken();

        //主叫号码或者被叫号码
        $callNo = $request['callNo']?$request['callNo']:'***********';

        $param = array(
            "flag" => "1000",//固定为1000
            "accessToken" => $tokenData,//该参数值请使用获取accessToken接口获取
            "account" => "N000000040320",//您的账户编号为 : xxxxxxx

//            "startTime" => date("Y-m-d 00:00:00",time()),//说明：通话的呼叫时间，格式：2019-10-22 00:00:00，默认为当天00:00:00，返回的数据呼叫时间大于或等于该值
//            "endTime" => date("Y-m-d 23:59:59",time()), //说明：通话的呼叫时间，格式：2019-10-22 23:59:59，默认为当天23:59:59，返回的数据呼叫时间小于或等于该值
//            "callType" => "normal", //说明：normal普通来电 , dialout外呼通话 , transfer转接电话 , dialtransfer外呼转接
            "callNo"=>$callNo,//说明：主叫号码或者被叫号码
            "limit"=>"100",//说明：每次查询返回的通话记录条数，默认为10，最大为1000；
        );
        $getBackJson = request_by_curl("https://a1.7x24cc.com/commonInte", dataEncode($param), "GET");
        $bakData = json_decode($getBackJson, true);
        print_r($getBackJson);//   0    4

        die;
        //json 返回的数据
//        [
//            {
//                "id": "1416746870004089451",
//                "callId": "61cd0dc9-f388-42dc-9923-d14ce52fafe9",
//                "deviceId": "<EMAIL>",
//                "timeIn": null,
//                "startTime": "2023-06-28 18:04:47",
//                "endTime": "2023-06-28 18:04:56",
//                "recordChannel": 1,
//                "recordLength": 9.093,
//                "recordType": "2",
//                "recordFormat": null,
//                "audioInMos": "4.5",
//                "filePath": "/mnt/volumes/recordings/jdb.cc/2023/0628/<EMAIL>/1416746870004089451_61cd0dc9-f388-42dc-9923-d14ce52fafe9.mp3",
//                "repoId": "default",
//                "failReason": "None",
//                "recordingRepoNfsPath": "**********:31080",
//                "recordingRepoRecordingPath": "/mnt/volumes/recordings/"
//            }
//        ]

        if($bakData['callId']){
            $result = array();
            $result['list'] = $bakData;

            $this->error = 0;
            $this->errortip = "拨打行为记录！";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "勋章列表获取失败！";
            return false;
        }
    }

    // -------------------- 合力 ---- 结束 ---------------------------

    // -------------------- 爱普云 ---- 开始 ---------------------------
    //爱普云--获取token
    function getAipuToken($djson=''){
        $api_key = "5105986851569671";
        $api_secret = "dcc916bb682a67c3f417957a1a955b75";
        if($djson==''){
            return false;
        }
        $nowtoken = md5($djson.$api_secret);

        $data = array();
        $data['api_key'] = $api_key;
        $data['sign'] = $nowtoken;
        return $data;
    }
    //爱普云--绑定员工（绑定和解绑）
    function bangApyUser($request,$lift='0'){

        $marOne = $this->DataControl->selectOne(" select * from crm_marketer where marketer_id = '{$request['marketer_id']}' and company_id = '{$request['company_id']}' ");

        $marOne['marketer_name'] = '吉的堡魅族';
        $marOne['marketer_mobile'] = '13866877391';

        $param = array();
        $param['ts'] = time(); //时间戳
        $param['userId'] = $marOne['marketer_mobile']; //员工用户名(4~12 位)，如果已经存在，则修改原员工，如果不存在则创建新员工。
        $param['name'] = $marOne['marketer_name']; //姓名 (新增没传时，默认为员工用户名)
        $param['phone'] = $marOne['marketer_mobile']; //员工真实手机号（与 X 号码绑定的手机号
        if($lift==1){
            $param['xphone'] = -1;
        }else{
            $param['xphone'] = $marOne['marketer_mobile']; //需要绑定的 X 号码。 注：1、该参数传空字符串，则表示由系统分配空闲的 X 号码进行绑定。 2、该参数传"-1"，则表示仅解绑 X 号码，不进行新的绑定。
        }
print_r($param);die;
        //获取 参数签名
        $signdata = $this->getAipuToken(json_encode($param));
        if(!$signdata){
            $this->error = 1;
            $this->errortip = "参数签名不存在";
            return false;
        }
//print_r($signdata);die;
        //创建员工
        $header = array();
        $header[] = "Content-type: application/json";
        $header[] = "api_key:".$signdata['api_key'];
        $header[] = "sign:".$signdata['sign'];
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://api.51lianlian.cn/api/v2/ax-binding", json_encode($param), "POST", $header);
        print_r($getBackJson);die;
        $bakData = json_decode($getBackJson, true);
        if($bakData['code'] == '0'){

//            //记录员工同步情况 ---  职工注册 1 已发送 2 老师实名认证成功 3 老师实名认证失败
//            $upone = array();
//            $upone['xxxxxx'] = 1;
//            $upone['marketer_updatetime'] = time();
//            $this->DataControl->updateData('crm_marketer', "marketer_id = '{$marOne['marketer_id']}'", $upone);

        }elseif($bakData['code'] == '1'){
            $this->error = 1;
            $this->errortip = "签名验证错误";
            return false;
        }else{
            $this->error = 1;
            $this->errortip = "三方接口访问失败";
            return false;
        }
    }

    //爱普云--外呼
    function getAipuDualCall($request){
        $marOne = $this->DataControl->selectOne("select 1 from crm_marketer where marketer_id = '{$request['marketer_id']}' ");

        $param = array();
        $param['ts'] = time(); //时间戳
        $param['userId'] = $marOne['xxx']; //员工用户名，即 3.1 接口中的 userId。
        $param['caller'] = $marOne['xxx']; //主叫号码
        $param['called'] = $marOne['xxx']; //被叫号码，固话需带区号
        $param['payload'] = $marOne['xxx']; //扩展信息，话单推送时回传

        //获取 参数签名
        $signstr = $this->getAipuToken(json_encode($param));
        if(!$signstr){
            $this->error = 1;
            $this->errortip = "参数签名不存在";
            return false;
        }
        //创建员工
        $header = array();
        $header[] = "Content-type: application/json";
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://api.51lianlian.cn/api/v2/double-call", json_encode($param), "POST", $header);
        print_r($getBackJson);die;
//        $bakData = json_decode($getBackJson, true);
//
//        $record = array();
//        $record['school_id'] = $request['school_id'];
//        $record['client_id'] = $request['client_id'];
//        $record['marketer_id'] = $request['marketer_id'];
//        $record['callrecord_type'] = 1;
//        $record['callrecord_callid'] = $bakData['recordId'];
//        $record['callrecord_createtime'] = time();
//        $this->DataControl->insertData("crm_client_callrecord",$record);
//        if($bakData['recordId']){
//
//            $result = array();
//            $result['list'] = $record;
//
//            $this->error = 0;
//            $this->errortip = "拨打成功......请等待系统回拨！";
//            $this->result = $result;
//            return true;
//        }else{
//            $recordOne = array();
//            $recordOne['school_id'] = $request['school_id'];
//            $recordOne['client_id'] = $request['client_id'];
//            $recordOne['marketer_id'] = $request['marketer_id'];
//            $recordOne['callerrorlog_class'] = 1;
//            $recordOne['callerrorlog_parameter'] = json_encode($param);
//            $recordOne['callerrorlog_bakjson'] = $getBackJson;
//            $recordOne['callerrorlog_time'] = time();
//            $this->DataControl->insertData("crm_merchant_seats_callerrorlog",$recordOne);
//
//            $this->error = 1;
//            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
//            return false;
//        }

    }

    //爱普云--回调
    function callBackAipuAction($request){
        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            $this->error = 1;
            $this->errortip = "未获取到返回值！";
            return true;
            die;
        }
        $requestdata = json_decode($request, true);

//        //判断回调的是不是集团TMK软电话  然后区分 校还是园的电话，分别入库
//        if($requestdata['namespace'] == 'jdbtmk.cc'){
//            $tmknumber = explode("@",$requestdata['ani']);
//            $yuan = ['8005','8006','8007','8008'];
//            if($tmknumber['1'] == 'jdbtmk.cc' && in_array($tmknumber[0],$yuan)){
//                $data = array();
//                $data['data'] = $request;
//                $apiSting = request_by_curl("https://psatmk.kedingdang.com/Api/callBackHuijieAction", dataEncode($data), 'post');
////                $apiSting = request_by_curl("http://psatmk.kcclassin.com/Api/callBackHuijieAction", dataEncode($data), 'post');
//                $ApiArray = json_decode($apiSting, "1");
//
//                ajax_return(array('error' => $ApiArray['error'], 'errortip' => $ApiArray['errortip'], 'result' => $ApiArray['result']['list']));
//                exit;
//            }
//        }

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$requestdata['callId']}' and callbacklog_class = '1' and callbacklog_eventtype = '1' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 1;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
//        $list = array();
//        $list['callbacklog_apiurl'] = "/Callout/callBackHuijieAction";
//        $list['callbacklog_class'] = 4;//厂商 1 慧捷  2 合力  3仕决 4 爱普云
//        $list['callbacklog_eventtype'] = 1;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件
//        $list['callbacklog_parameter'] = $requestdata['callId'];
//        $list['callbacklog_bakjson'] = $request;
//        $list['callbacklog_time'] = time();
//        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
//            //修改表 crm_client_callrecord  是否接通
//            $callid = $requestdata['callId'];
//            $updata = array();
//            $updata['callrecord_isconnect'] = 1;
//            $updata['callrecord_updatetime'] = time();
//            $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '1' ",$updata);
//
//            $bindPhone = substr_replace($requestdata['systemData']['bindPhone'],'',0,1);
//            $customerPhone = $requestdata['customerPhone'];
//            //记录通话记录
//            $this->getHuijieCallidRecording($callid,$bindPhone,$customerPhone);
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }
    // -------------------- 爱普云 ---- 结束 ---------------------------

    // -------------------- 极简互联 ---- 开始 ---------------------------
    //极简互联 -- 获取签名
    function getJjhlSign($data){
        $sign = hash_hmac('sha256',$data,"594212A38DC64F5BA523AC5C7130A7A9");//secretKey 594212A38DC64F5BA523AC5C7130A7A9
        $endsign = base64_encode($sign);
        return $endsign;
    }
    //极简互联 -- 双向呼叫
    function  getJjhlDualCal($request,$marketer_mobile,$client_mobile,$numbertype=1,$seats_account,$city_id=0){
        $appid = 3101785;//appId
        $nowtime = time();
        $nonce = rand(100000,999999);
        $keyParam = $appid.$nowtime.$nonce;
        $signature = $this->getJjhlSign($keyParam);

        $param = array(
            'firstNumber' => $client_mobile, //（必填）第一被叫号码 -- 客户的
            'firstTimeout' => "", //第一被叫呼叫超时，不填默认120s
            'firstDisType' => "", //第一被叫外显号类型：number(外显号) group（外显组），不传则默认为number
            'firstDisNumber' => "", //当firstDisType为number时必传。呼叫第一被叫使用的外显号码，若firstDisType未传，系统会自动从租户下寻找可用号码外呼
            'firstCallerDisplayGroupName' => "", //当firstDisType为group时必传，呼叫第一被叫使用的外显号码组名称
            'secondNumber' => $marketer_mobile, //（必填）第二被叫号码 -- 销售的
            'type' => "number", //（必填）第二被叫类型，number（外线）、agentNumber（坐席工号），serviceNumber(服务号，通常用在语音通知场景)三选一。
            'secondTimeout' => "", //第二被叫呼叫超时，不填默认60s
            'secondDisNumber' => "", //呼叫第二被叫使用的外显号码，type为number、agentNumber时生效
            'extras' => [
                "jjhlxiaoshou" => $marketer_mobile,//用户随流数据 -- 销售
                "jjhlkehu" => $client_mobile,//用户随流数据
                "school_id" => $request['school_id'],//用户随流数据
                "marketer_id" => $request['marketer_id'],//用户随流数据
                "client_id" => $request['client_id'],//用户随流数据
            ], //自定义参数，通话中通过事件推送，通话后通话记录中可以查询搜索和定位 限制字节数为255个,并且使用json格式进行传输，超出或者格式不正确则报错。
        );

        //"OutBoundVars" => "hlcallno:{$caller},hlcalledno:{$callee},school_id:{$request['school_id']},marketer_id:{$request['marketer_id']},client_id:{$request['client_id']},

        $header = array();
        $header[] = "Content-type: application/json;charset=utf-8";
        $header[] = "appid: ".$appid;
        $header[] = "nonce: ".$nonce;
        $header[] = "timestamp: ".$nowtime;
        $header[] = "signature: ".$signature;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://openapi.useasy.cn/openapi/call/api/v1/twoWayCall", json_encode($param), "POST", $header);
        $bakData = json_decode($getBackJson, true);
//        print_r($bakData);die;

        $record = array();
        $record['school_id'] = $request['school_id'];
        $record['client_id'] = $request['client_id'];
        $record['marketer_id'] = $request['marketer_id'];
        $record['callrecord_type'] = 4;//极简互联
        $record['callrecord_callid'] = $bakData['data']['callUniqueId'];
        $record['callrecord_createtime'] = time();
        $this->DataControl->insertData("crm_client_callrecord",$record);

        if($bakData['code'] == '200' && $bakData['data']['callUniqueId']){
            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打成功......请等待系统回拨！";
            $this->result = $result;
            return true;
        }else{
            $recordOne = array();
            $recordOne['school_id'] = $request['school_id'];
            $recordOne['client_id'] = $request['client_id'];
            $recordOne['marketer_id'] = $request['marketer_id'];
            $recordOne['callerrorlog_class'] = 4;
            $recordOne['callerrorlog_parameter'] = json_encode($param);
            $recordOne['callerrorlog_bakjson'] = $getBackJson;
            $recordOne['callerrorlog_time'] = time();
            $this->DataControl->insertData("crm_merchant_seats_callerrorlog",$recordOne);

            $this->error = 1;
            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
            return false;
        }
    }

    //极简互联 -- 通话记录推送事件
    function callBackJijianHLAction($request){
        $request = addslashes(trim($request));
        $list = array();
        $list['lsjson_json'] = $request;
        $list['lsjson_createtime'] = time();
        $listOne = $this->DataControl->insertData("gmc_company_dping_lsjson",$list);

        //补充一个 回调参数日志表  记录所有回调数据
        if(!$request){
            $this->error = 1;
            $this->errortip = "未获取到返回值！";
            return true;
            die;
        }

        $requestdata = json_decode(stripslashes($request), true);

        $recordlogOne = $this->DataControl->selectOne("select callbacklog_id from crm_merchant_seats_callbacklog where callbacklog_parameter = '{$requestdata['callId']}' and callbacklog_class = '4' and callbacklog_eventtype = '1' ");
        if($recordlogOne['callbacklog_id']){
            $this->error = 1;
            $this->errortip = "已存在数据，无需存储！";
            return false;
        }
        $list = array();
        $list['callbacklog_apiurl'] = "/Callout/callBackJijianHLAction";
        $list['callbacklog_class'] = 4;//厂商 1 慧捷  2 合力  3仕决 4 极简互联
        $list['callbacklog_eventtype'] = 1;//回调事件类型  0 未知  1 通话结束事件 2 分机事件 3 ivr通话事件
        $list['callbacklog_parameter'] = $requestdata['callId'];
        $list['callbacklog_bakjson'] = $request;
        $list['callbacklog_time'] = time();
        $listOne = $this->DataControl->insertData("crm_merchant_seats_callbacklog",$list);

        if($listOne) {
            //修改表 crm_client_callrecord  是否接通
            $callid = $requestdata['callId'];
            $updata = array();
            $updata['callrecord_isconnect'] = 1;
            $updata['callrecord_updatetime'] = time();
            $this->DataControl->updateData("crm_client_callrecord"," callrecord_callid = '{$callid}' and callrecord_type = '4' ",$updata);

            $extras = json_decode($requestdata['extras'], true); //随流参数
            $updata = array();
            $updata['school_id'] = $extras['school_id'];
            $updata['marketer_id'] = $extras['marketer_id'];
            $updata['client_id'] = $extras['client_id'];
            $updata['outcall_class'] = 4;//厂商 1 慧捷  2 合力  3仕决  4 极简互联
            $updata['outcall_uuid'] = $requestdata['callId'];//本条通话记录的唯一ID
            $updata['outcall_caller'] = $extras['jjhlxiaoshou'];//主叫号码 --- 客户
            $updata['outcall_called'] = $requestdata['customerNumber'];//被叫号码
            $updata['outcall_duration'] = $requestdata['callDuration'];//通话时长
            $updata['outcall_begin_time'] = $requestdata['answerTime'];//通话开始时间
            $updata['outcall_end_time'] = $requestdata['hangupTime'];//通话结束时间
            $updata['outcall_playurl'] = $requestdata['recordUrl'];//录音服务器地址
            $updata['outcall_createtime'] = time();
            $soundlogID = $this->DataControl->insertData("gmc_outcall",$updata);

            if($soundlogID){
                $jsondata = array();
                $jsondata['outcall_id'] = $soundlogID;
                $jsondata['outcall_json'] = $request;
                $jsondata['json_createtime'] = time();
                $this->DataControl->insertData("gmc_outcall_json",$jsondata);
            }
        }
        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    //极简互联 -- 修改坐席接口  文档：https://help.useasy.cn/developer/seat_management/seat_information
    function updateAgent($request,$marketer_mobile){
        $appid = 3101785;//appId
        $nowtime = time();
        $nonce = rand(100000,999999);
        $keyParam = $appid.$nowtime.$nonce;
        $signature = $this->getJjhlSign($keyParam);

        $marketerOne = $this->DataControl->selectOne("select marketer_jjhlworknumber from crm_marketer where marketer_outtype = '4' and marketer_jjhlworknumber > 1 order by marketer_jjhlworknumber desc");

        if($marketerOne['marketer_jjhlworknumber']){
            $agentNumber = $marketerOne['marketer_jjhlworknumber']+1;
        }else{
            $agentNumber = 6001;
        }

        //坐席的信息修改，不需要的信息不要传值，可能误改数据，（不用的传空，对方也报错）
        $param = array(
            'agentNumber' => $agentNumber, //（必填）坐席工号
//            'agentName' => "", //坐席名称
//            'email' => "", //坐席邮箱
//            'password' => "", //坐席密码(可由字母、数字组成，长度为8-18个字不能使用特殊字符)
//            'crmId' => "", //用来关联三方系统id
//            'restTime' => "", //坐席整理时长，默认30s
            'mobile' => $marketer_mobile, //换绑的手机号
//            'groups' => "", //所属服务组列表   List<String>
//            'status' => "", //座席是否启用，1为启用，0为停用
//            'roles' => "", //角色列表, 请联系管理员获取字段信息
//            'versionId' => "", //licence版本id，请联系管理员获取字段信息
//            'callerStrategy' => "", //外呼号码策略 1为无、2为指定外显号码、3为指定外显号码组
//            'callerNumbers' => "", //外显号码列表   callerStrategy为1时(必填)   List<String>
//            'callerGroup' => "", //外显号码组id   callerStrategy为2时(必填)
//            'callInAnswerType' => "", //座席固定接听方式。CURRENT：当前登录方式。 PSTN：手机模式
        );

        $header = array();
        $header[] = "Content-type: application/json;charset=utf-8";
        $header[] = "appid: ".$appid;
        $header[] = "nonce: ".$nonce;
        $header[] = "timestamp: ".$nowtime;
        $header[] = "signature: ".$signature;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://openapi.useasy.cn/openapi/platform/v1/agent/update", json_encode($param), "POST", $header);
        $bakData = json_decode($getBackJson, true);

        if($bakData['code'] == '200' && $bakData['data']['agentNumber'] > 1 ){
            $upOne = array();
            $upOne['marketer_outtype'] = 4;
            $upOne['marketer_jjhlworknumber'] = $bakData['data']['agentNumber'];
            $this->DataControl->updateData("crm_marketer"," marketer_id = '{$request['marketer_id']}' and company_id = '{$request['company_id']}' ",$upOne);

            return $bakData['data']['agentNumber'];
        }else{
            return 0;
        }
    }


    //极简互联 -- 外呼接口 -- 有坐席概念
    function  getJjhlCallout($request,$marketer_mobile,$marketer_number,$client_mobile,$numbertype=1){
        //给新的职工分配坐席号
        if($marketer_number == '0' || $marketer_number < 6000){
            $agentId = $this->updateAgent($request,$marketer_mobile);
            $marketer_number = $agentId;
        }

        $appid = 3101785;//appId
        $nowtime = time();
        $nonce = rand(100000,999999);
        $keyParam = $appid.$nowtime.$nonce;
        $signature = $this->getJjhlSign($keyParam);

        if($client_mobile == '***********' || $client_mobile == '13764506328'){
            $numberGroupName = 'one';
        }elseif($client_mobile == '18297456643' ||  $client_mobile == '18703629223'){
            $numberGroupName = 'two';
        }else{
            $numberGroupName = 'three';
        }

        $param = array(
            'agentNumber' => $marketer_number, //（必填）坐席工号
            'customerNumber' => $client_mobile, //（必填）被叫号码，固话请输入完整区号。
            'calloutType' => "PSTN", //（必填）外呼方式:PSTN(坐席绑定手机号),SIP(sip话机)、WEBRTC(软电话)。
//            'agentTimeout' => "", //呼叫座席侧超时时间，默认60秒。
//            'customerTimeout' => "", //呼叫客户侧超时时间，默认60秒。
//            'agentDisNumber ' => "", //指定呼叫座席侧外显号码，优先级最高。
//            'customerDisNumber ' => "", //指定呼叫客户侧外显号码，此号码应为企业账户外显号码中的已存在的。
            'numberGroupName' => $numberGroupName, //号码组名称，传该值，会查找对应名称的号码组，并根据策略选择外显号。未找到号码组按照没传处理
//            'numberGroupId' => "", //号码组编号，传该值，会查找对应Id的号码组，并根据策略选择外显号。未找到号码组按照没传处理,号码组名称和id同时存在时，优先根据编号查找号码组
            'extras' => [
                "jjhlxiaoshou" => $marketer_mobile,//用户随流数据 -- 销售
                "jjhlkehu" => $client_mobile,//用户随流数据
                "school_id" => $request['school_id'],//用户随流数据
                "marketer_id" => $request['marketer_id'],//用户随流数据
                "client_id" => $request['client_id'],//用户随流数据
            ], //自定义参数，通话中通过事件推送，通话后通话记录中可以查询搜索和定位 限制字节数为255个,并且使用json格式进行传输，超出或者格式不正确则报错。同时可在此参数里传extraNumber分机号，实现分机号自动拨号。
        );

        //"OutBoundVars" => "hlcallno:{$caller},hlcalledno:{$callee},school_id:{$request['school_id']},marketer_id:{$request['marketer_id']},client_id:{$request['client_id']},
//print_r(json_encode($param));die;
        $header = array();
        $header[] = "Content-type: application/json;charset=utf-8";
        $header[] = "appid: ".$appid;
        $header[] = "nonce: ".$nonce;
        $header[] = "timestamp: ".$nowtime;
        $header[] = "signature: ".$signature;
//print_r(json_encode($param));
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://openapi.useasy.cn/openapi/call/api/v1/callout", json_encode($param), "POST", $header);
        $bakData = json_decode($getBackJson, true);
//print_r($bakData);
        $record = array();
        $record['school_id'] = $request['school_id'];
        $record['client_id'] = $request['client_id'];
        $record['marketer_id'] = $request['marketer_id'];
        $record['callrecord_type'] = 4;//极简互联
        $record['callrecord_callid'] = $bakData['data']['callUniqueId'];
        $record['callrecord_createtime'] = time();
        $this->DataControl->insertData("crm_client_callrecord",$record);

        if($bakData['code'] == '200' && $bakData['data']['callUniqueId']){
            $result = array();
            $result['list'] = $record;

            $this->error = 0;
            $this->errortip = "拨打成功......请等待系统回拨！";
            $this->result = $result;
            return true;
        }else{
            $recordOne = array();
            $recordOne['school_id'] = $request['school_id'];
            $recordOne['client_id'] = $request['client_id'];
            $recordOne['marketer_id'] = $request['marketer_id'];
            $recordOne['callerrorlog_class'] = 4;
            $recordOne['callerrorlog_parameter'] = json_encode($param);
            $recordOne['callerrorlog_bakjson'] = $getBackJson;
            $recordOne['callerrorlog_time'] = time();
            $this->DataControl->insertData("crm_merchant_seats_callerrorlog",$recordOne);

            $this->error = 1;
            $this->errortip = "系统繁忙中请稍后再拨，谢谢您的配合！";
            return false;
        }
    }

    //极简互联 -- 查询通话录音  ------ 目前未使用
    function  getJjhlCallLogs(){
        $appid = 3101785;//appId
        $nowtime = time();
        $nonce = rand(100000,999999);
        $keyParam = $appid.$nowtime.$nonce;
        $signature = $this->getJjhlSign($keyParam);

        $param = array(
            'startTime' => "2025-06-24 00:27:33", //
            'endTime' => "2025-26-24 14:30:33", //
        );
        $header = array();
        $header[] = "Content-type: application/json;charset=utf-8";
        $header[] = "appid: ".$appid;
        $header[] = "nonce: ".$nonce;
        $header[] = "timestamp: ".$nowtime;
        $header[] = "signature: ".$signature;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://openapi.useasy.cn/openapi/call/web/v1/cdr/query", json_encode($param), "POST", $header);
        $bakData = json_decode($getBackJson, true);
        print_r($bakData);die;
    }
    //极简互联 -- 查询外显号列表接口 ------ 目前未使用
    function  getJjhlNumbersUse(){
        $appid = 3101785;//appId
        $nowtime = time();
        $nonce = rand(100000,999999);
        $keyParam = $appid.$nowtime.$nonce;
        $signature = $this->getJjhlSign($keyParam);

        $param = array(
            "number" => "", //外显号
            "status" => "", //号码状态：1 启用，0 停用
            "page" => "1", //页数
            "pageSize" => "100", //每页条数，默认100
        );
        $header = array();
        $header[] = "Content-type: application/json;charset=utf-8";
        $header[] = "appid: ".$appid;
        $header[] = "nonce: ".$nonce;
        $header[] = "timestamp: ".$nowtime;
        $header[] = "signature: ".$signature;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://openapi.useasy.cn/openapi/call/web/v1/number/display/query", json_encode($param), "POST", $header);
        $bakData = json_decode($getBackJson, true);
        print_r($bakData);die;
    }
    //极简互联 -- 查询外显号码组 ----- 目前未使用
    function  getJjhlNumbersGroup(){
        $appid = 3101785;//appId
        $nowtime = time();
        $nonce = rand(100000,999999);
        $keyParam = $appid.$nowtime.$nonce;
        $signature = $this->getJjhlSign($keyParam);

        $param = array(
            "number" => "", //外显号
            "status" => "", //号码状态：1 启用，0 停用
            "page" => "1", //页数
            "pageSize" => "100", //每页条数，默认100
        );
        $header = array();
        $header[] = "Content-type: application/json;charset=utf-8";
        $header[] = "appid: ".$appid;
        $header[] = "nonce: ".$nonce;
        $header[] = "timestamp: ".$nowtime;
        $header[] = "signature: ".$signature;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = $this->httpRequest("https://openapi.useasy.cn/openapi/call/web/v1/number/group/query", json_encode($param), "POST", $header);
        $bakData = json_decode($getBackJson, true);
        print_r($bakData);die;
    }

    // -------------------- 极简互联 ---- 结束 ---------------------------

}
