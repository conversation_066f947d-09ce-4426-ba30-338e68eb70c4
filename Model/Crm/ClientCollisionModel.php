<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  ClientCollisionModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $errortipaa = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $company_isassist = 0;
    public $company_istrackcoursetype = 0;
    public $company_ismusttrackcoursetype = 0;
    public $isGmcPost = null;

    public $m;
    public $allottip = "";
    public $allotnum = "";

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist,company_istrackcoursetype,company_ismusttrackcoursetype", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
            $this->company_istrackcoursetype = $companyOne['company_istrackcoursetype'];
            $this->company_ismusttrackcoursetype = $companyOne['company_ismusttrackcoursetype'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'], $publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            } else {
                $this->marketer_id = $publicarray['marketer_id'];
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    /**
     * 校务 判断并处理一个名单的撞单逻辑
     *
     * @param $paramArray
     */
    function CrmClientCollision($paramArray, $activityOne = array(), $track_note = '',$isgmcimport = 0){
        $paramArray['school_id'] = ($paramArray['school_id']>1)?$paramArray['school_id']:'0';

        //判断手机号是不是在校生的联系人
        $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch,s.student_id 
                    FROM smc_student_family AS f ,smc_student AS s
                    WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['client_mobile']}' 
                    and s.company_id = '{$paramArray['company_id']}' AND s.student_isdel = '0'
                    ORDER BY s.student_id limit 0,1");
        if($familyOne){//是在校生
            $result = array();
            $result['isstudent'] = 1;
            $result['student_id'] = $familyOne['student_id'];

            $this->error = '1';
            $this->errortip = "检测到您已是在校生,学员：{$familyOne['student_cnname']}，请直接电话联系！(1)";
            $this->result = $result;
            return false;
        }

        if (!$activityOne) {
            $addlinktype = '渠道新增名单';
            $updatalinktype = '渠道名单更新';
        } else {
            $addlinktype = "活动新增名单";
            $updatalinktype = "活动名单更新";
        }

        //整个集团是否可以找到对应手机号的名单
        $comClientOne = $this->DataControl->selectOne("select client_id,channel_id,client_isgross,client_createtime,client_remark from crm_client where client_mobile = '{$paramArray['client_mobile']}' and company_id = '{$paramArray['company_id']}' order by client_id desc limit 0,1 ");
        if ($comClientOne) {

            //名单有没有负责人
            $principalOne = $this->DataControl->selectOne(" select a.client_id,a.channel_id,a.client_createtime,a.client_remark,a.client_isgross,c.channel_maxday 
                            from crm_client as a,crm_client_principal as b,crm_code_channel as c 
                            where a.client_mobile = '{$paramArray['client_mobile']}' and a.company_id = '{$paramArray['company_id']}' 
                            and a.client_id = b.client_id and a.principal_leave = '0' 
                            and a.channel_id = c.channel_id 
                            order by b.principal_ismajor DESC,b.principal_id DESC 
                            limit 0,1 ");

            //名单有没有负责人 -- CRM 部分的TMK
            $tmkPrincipalOne = $this->DataControl->selectOne(" select a.client_id,a.channel_id,a.client_createtime,a.client_remark,a.client_isgross,c.channel_maxday 
                            from crm_client as a,crm_client_tmkprincipal as b,crm_code_channel as c 
                            where a.client_mobile = '{$paramArray['client_mobile']}' and a.company_id = '{$paramArray['company_id']}' 
                            and a.client_id = b.client_id and a.principal_leave = '0' 
                            and a.channel_id = c.channel_id 
                            order by b.principal_ismajor DESC,b.principal_id DESC 
                            limit 0,1 ");
            if($principalOne){ //当名单有负责人的时候
                $time = date("Y-m-d");
                $create = date("Y-m-d", $principalOne['client_createtime']);
                $max = date("Y-m-d", strtotime($create . "+{$principalOne['channel_maxday']} day"));
                //判断名单是否过 保护期
                if($principalOne['channel_maxday'] != 0 && $time > $max){

                    //判断是 45 天内是否有主动跟踪
                    $endtimes = time() - 3600 * 24 * 45;
                    $track45One = $this->DataControl->selectOne(" select track_id from crm_client_track where client_id = '{$principalOne['client_id']}' and track_isactive=1 AND track_createtime > '{$endtimes}' ");
                    if (!$track45One) {

                        //更新有效名单为 待分配状态
                        $this->updateClient($paramArray,$principalOne);
                        //解除负责人状态
                        $this->updatePrincipal($principalOne['client_id']);
                        //渠道变更记录
                        $this->addChannellog($principalOne, $paramArray['company_id'], '0', $paramArray['channel_id']);

                        //添加名单的跟踪记录
                        $trackData = array();
                        //调用适配学校方法
                        if($paramArray['school_id'] > 1) {
                            $this->addSchoolEnter($principalOne['client_id'], $paramArray['school_id'], $paramArray['company_id']);
                            $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                        } else {
                            $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                        }
                        // 集团导入名单 不进入学校操作
                        $this->gmcClietToSchool($isgmcimport,$paramArray['istoschool'],$principalOne['client_id'],$paramArray['school_id']);

//                        $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活成功，近期无人跟进且过保护期，撞单渠道替换成功。");
                        $track_type = ($paramArray['school_id']>1)?0:1;
                        $this->addTrack($principalOne['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                        $result = array();
                        $result['isstudent'] = 0;
                        $result['client_id'] = $principalOne['client_id'];
                        $result['school_id'] = $paramArray['school_id'];

                        $this->error = '0';
                        $this->errortip = "名单激活成功，近期无人跟进且过保护期，撞单渠道替换成功。";
                        $this->result = $result;
                        return false;
                    }else{
                        //45 天内 有主动跟踪
                        $trackData = array();
                        $trackData['track_linktype'] = $this->LgStringSwitch($addlinktype);
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，已有负责人且45天内名单有主动跟踪，新增名单撞单提醒。");
                        $track_type = ($paramArray['school_id']>1)?0:1;
                        $this->addTrack($principalOne['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                        $result = array();
                        $result['isstudent'] = 0;
                        $result['client_id'] = $principalOne['client_id'];
                        $result['school_id'] = $paramArray['school_id'];

                        $this->error = '1';
                        $this->errortip = "名单激活失败，已有负责人保护期内正在跟进，新增名单撞单提醒。";
                        $this->result = $result;
                        return false;
                    }
                }else{
                    //未过 保护期
                    $trackData = array();
                    $trackData['track_linktype'] = $this->LgStringSwitch($addlinktype);
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，已有负责人且名单正在保护期内，新增名单撞单提醒。");
                    $track_type = ($paramArray['school_id']>1)?0:1;
                    $this->addTrack($principalOne['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $principalOne['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    $this->error = '1';
                    $this->errortip = "名单激活失败，已有负责人保护期内正在跟进，新增名单撞单提醒。";
                    $this->result = $result;
                    return false;
                }
            }elseif($tmkPrincipalOne){//当名单有负责人的时候 - CRM TMK人
                $time = date("Y-m-d");
                $create = date("Y-m-d", $tmkPrincipalOne['client_createtime']);
                $max = date("Y-m-d", strtotime($create . "+{$tmkPrincipalOne['channel_maxday']} day"));
                //判断名单是否过 保护期
                if($tmkPrincipalOne['channel_maxday'] != 0 && $time > $max){

                    //判断是 45 天内是否有主动跟踪
                    $endtimes = time() - 3600 * 24 * 45;
                    $track45One = $this->DataControl->selectOne(" select track_id from crm_client_track where client_id = '{$tmkPrincipalOne['client_id']}' and track_isactive=1 AND track_createtime > '{$endtimes}' ");
                    if (!$track45One) {

                        //更新有效名单为 待分配状态
                        $this->updateClient($paramArray,$tmkPrincipalOne);
                        //解除负责人状态
                        $this->updatePrincipal($tmkPrincipalOne['client_id']);
                        //渠道变更记录
                        $this->addChannellog($tmkPrincipalOne, $paramArray['company_id'], '0', $paramArray['channel_id']);

                        //添加名单的跟踪记录
                        $trackData = array();
                        //调用适配学校方法
                        if($paramArray['school_id'] > 1) {
                            $this->addSchoolEnter($tmkPrincipalOne['client_id'], $paramArray['school_id'], $paramArray['company_id']);
                            $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                        } else {
                            $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                        }
                        // 集团导入名单 不进入学校操作
                        $this->gmcClietToSchool($isgmcimport,$paramArray['istoschool'],$tmkPrincipalOne['client_id'],$paramArray['school_id']);

//                        $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活成功，近期无人跟进且过保护期，撞单渠道替换成功。");
                        $track_type = ($paramArray['school_id']>1)?0:1;
                        $this->addTrack($tmkPrincipalOne['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                        $result = array();
                        $result['isstudent'] = 0;
                        $result['client_id'] = $tmkPrincipalOne['client_id'];
                        $result['school_id'] = $paramArray['school_id'];

                        $this->error = '0';
                        $this->errortip = "名单激活成功，近期无人跟进且过保护期，撞单渠道替换成功。";
                        $this->result = $result;
                        return false;
                    }else{
                        //45 天内 有主动跟踪
                        $trackData = array();
                        $trackData['track_linktype'] = $this->LgStringSwitch($addlinktype);
                        $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，已有负责人且45天内名单有主动跟踪，新增名单撞单提醒。");
                        $track_type = ($paramArray['school_id']>1)?0:1;
                        $this->addTrack($tmkPrincipalOne['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                        $result = array();
                        $result['isstudent'] = 0;
                        $result['client_id'] = $tmkPrincipalOne['client_id'];
                        $result['school_id'] = $paramArray['school_id'];

                        $this->error = '1';
                        $this->errortip = "名单激活失败，已有负责人保护期内正在跟进，新增名单撞单提醒。";
                        $this->result = $result;
                        return false;
                    }
                }else{
                    //未过 保护期
                    $trackData = array();
                    $trackData['track_linktype'] = $this->LgStringSwitch($addlinktype);
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，已有负责人且名单正在保护期内，新增名单撞单提醒。");
                    $track_type = ($paramArray['school_id']>1)?0:1;
                    $this->addTrack($tmkPrincipalOne['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $tmkPrincipalOne['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    $this->error = '1';
                    $this->errortip = "名单激活失败，已有负责人保护期内正在跟进，新增名单撞单提醒。";
                    $this->result = $result;
                    return false;
                }
            }else{
                //当名单 没有负责人的时候

                //名单渠道信息
                $principalTwo = $this->DataControl->selectOne(" select a.client_id,a.channel_id,a.client_createtime,a.client_remark,c.channel_maxday 
                            from esc_client as a,esc_code_channel as c 
                            where a.client_mobile = '{$paramArray['client_mobile']}' and a.company_id = '{$paramArray['company_id']}' 
                            and a.channel_id = c.channel_id 
                            order by a.client_id DESC 
                            limit 0,1 ");

                $time = date("Y-m-d");
                $create = date("Y-m-d", $principalTwo['client_createtime']);
                $max = date("Y-m-d", strtotime($create . "+{$principalTwo['channel_maxday']} day"));
                //判断名单是否过 保护期
                if($principalTwo['channel_maxday'] != 0 && $time > $max){

                    //更新有效名单为 待分配状态
                    $this->updateClient($paramArray,$principalTwo);
                    //解除负责人状态
                    $this->updatePrincipal($principalTwo['client_id']);
                    //渠道变更记录
                    $this->addChannellog($principalTwo, $paramArray['company_id'], '0', $paramArray['channel_id']);

                    //添加名单的跟踪记录
                    $trackData = array();
                    //调用适配学校方法
                    if($paramArray['school_id'] > 1) {
                        $this->addSchoolEnter($principalOne['client_id'], $paramArray['school_id'], $paramArray['company_id']);
                        $trackData['track_linktype'] = $this->LgStringSwitch("他校" . $updatalinktype);
                    } else {
                        $trackData['track_linktype'] = $this->LgStringSwitch("集团" . $updatalinktype);
                    }
                    // 集团导入名单 不进入学校操作
                    $this->gmcClietToSchool($isgmcimport,$paramArray['istoschool'],$principalOne['client_id'],$paramArray['school_id']);

//                    $trackData['track_linktype'] = $this->LgStringSwitch($updatalinktype);
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活成功，无负责人且过保护期，撞单渠道替换成功。");
                    $track_type = ($paramArray['school_id']>1)?0:1;
                    $this->addTrack($principalTwo['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $principalTwo['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    $this->error = '0';
                    $this->errortip = "名单激活成功，无负责人且过保护期，撞单渠道替换成功。";
                    $this->result = $result;
                    return false;
                }else{
                    //更新有效名单为 待分配状态  ----- 只是把名单变为待分配
                    $data = array();
//                    $data['client_isfromgmc'] = ($paramArray['school_id']>1)?0:1;
                    $data['promotion_id'] = $paramArray['promotion_id'];
                    $data['client_address'] = $paramArray['client_address'];
                    $data['client_age'] = $paramArray['client_age'];
                    $data['client_tracestatus'] = 0;
                    $data['client_distributionstatus'] = 0;
                    $data['client_cnname'] = $paramArray['client_cnname'];
//                    $data['channel_id'] = $paramArray['channel_id'];
//                    $data['client_source'] = $paramArray['channel_medianame'];
                    $data['client_isnewtip'] = '1';
                    if ($paramArray['client_sponsor']) {
                        $data['client_sponsor'] = $paramArray['client_sponsor'];
                    }
                    if ($paramArray['client_stubranch']) {
                        $data['client_stubranch'] = $paramArray['client_stubranch'];
                    }
                    if ($paramArray['client_frompage']) {//接触点
                        $data['client_frompage'] = $paramArray['client_frompage'];
                    }
//                    $data['province_id'] = $paramArray['province_id'];
//                    $data['city_id'] = $paramArray['city_id'];
//                    $data['area_id'] = $paramArray['area_id'];
                    $data['client_updatetime'] = time();
//                    $data['client_createtime'] = time();
                    if (isset($paramArray['client_remark']) && trim($paramArray['client_remark']) !== '') {
                        $data['client_remark'] = $principalTwo['client_remark'].";".$paramArray['client_remark'];
                    }
                    $data['client_teachername'] = $paramArray['client_teachername'];//20230210发现微商城推荐学生推荐老师没有存入，故在撞单操作中补充老师信息
                    $data['client_teacherid'] = $paramArray['client_teacherid'];//20230210发现微商城推荐学生推荐老师没有存入，故在撞单操作中补充老师信息
                    $this->DataControl->updateData("esc_client", "client_id = '{$principalTwo['client_id']}'", $data);

                    //解除负责人状态
                    $this->updatePrincipal($principalTwo['client_id']);
//                    //调用适配学校方法
//                    if($paramArray['school_id'] > 1) {
//                        $this->addSchoolEnter($principalOne['client_id'], $paramArray['school_id'], $paramArray['company_id']);
//                    }

                    //未过 保护期（且 没有负责人）
                    $trackData = array();
                    $trackData['track_linktype'] = $this->LgStringSwitch($addlinktype);
                    $trackData['track_note'] = $this->LgStringSwitch($track_note . "名单激活失败，虽然没有负责人但是在保护期内，重新转为待分配有效名单。");
                    $track_type = ($paramArray['school_id']>1)?0:1;
                    $this->addTrack($principalTwo['client_id'], $paramArray['school_id'], '0', $trackData, $track_type, 1);

                    $result = array();
                    $result['isstudent'] = 0;
                    $result['client_id'] = $principalTwo['client_id'];
                    $result['school_id'] = $paramArray['school_id'];

                    $this->error = '1';
                    $this->errortip = "名单激活失败，虽然没有负责人但是在保护期内，重新转为待分配有效名单。";
                    $this->result = $result;
                    return false;
                }

            }

        }else{
            $this->error = '0';
            $this->errortip = "整个集团未找到对应手机号的有效名单！";
            return true;
        }
    }

    //添加名单跟踪记录
    function addTrack($client_id, $school_id, $marketer_id, $track, $type, $Import, $followmode = 0)
    {
        $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id='{$marketer_id}'");
        $trackData = array();
        $trackData['client_id'] = $client_id;
        $trackData['school_id'] = $school_id;
        $trackData['marketer_id'] = $marketer_id;
        $trackData['marketer_name'] = $markertOne['marketer_name'] ? $markertOne['marketer_name'] : '系统';
        $trackData['track_validinc'] = 1;
        $trackData['track_followmode'] = $followmode;
        $trackData['track_linktype'] = $track['track_linktype'];
        $trackData['track_note'] = $track['track_note'];
        $trackData['track_createtime'] = time();
        $trackData['track_type'] = $type;
        $trackData['track_initiative'] = $Import;
        $this->DataControl->insertData('esc_client_track', $trackData);
    }

    //更新有效名单状态变为  待分配 状态
    function updateClient($paramArray,$clientOne){
        $data = array();
        $data['client_isfromgmc'] = ($paramArray['school_id']>1)?0:1;
        $data['promotion_id'] = $paramArray['promotion_id'];
        $data['client_address'] = $paramArray['client_address'];
        $data['client_age'] = $paramArray['client_age'];
        $data['client_tracestatus'] = 0;
        $data['client_distributionstatus'] = 0;
        $data['client_schtmkdistributionstatus'] = 0;
        $data['client_gmcdistributionstatus'] = 0;
        if ($clientOne['client_isgross'] == 1) {
            $data['client_isgross'] = 0;
        }
        $data['client_cnname'] = $paramArray['client_cnname'];
        $data['channel_id'] = $paramArray['channel_id'];
        $data['client_source'] = $paramArray['channel_medianame'];
        $data['client_isnewtip'] = '1';
        if ($paramArray['client_sponsor']) {
            $data['client_sponsor'] = $paramArray['client_sponsor'];
        }
        if ($paramArray['client_stubranch']) {
            $data['client_stubranch'] = $paramArray['client_stubranch'];
        }
        if ($paramArray['client_frompage']) {//接触点
            $data['client_frompage'] = $paramArray['client_frompage'];
        }
        $data['province_id'] = $paramArray['province_id'];
        $data['city_id'] = $paramArray['city_id'];
        $data['area_id'] = $paramArray['area_id'];
        $data['client_updatetime'] = time();
        $data['client_createtime'] = time();
        if (isset($paramArray['client_remark']) && trim($paramArray['client_remark']) !== '') {
            $data['client_remark'] = $clientOne['client_remark'].";".$paramArray['client_remark'];
        }
        $data['client_teachername'] = $paramArray['client_teachername'];//20230210发现微商城推荐学生推荐老师没有存入，故在撞单操作中补充老师信息
        $data['client_teacherid'] = $paramArray['client_teacherid'];//20230210发现微商城推荐学生推荐老师没有存入，故在撞单操作中补充老师信息
        $this->DataControl->updateData("esc_client", "client_id = '{$clientOne['client_id']}'", $data);
    }
    //解除负责人状态
    function updatePrincipal($client_id)
    {
        $principal_data = array();
        $principal_data['principal_leave'] = 1;
        $principal_data['principal_updatatime'] = time();
        $this->DataControl->updateData("crm_client_principal", "client_id='{$client_id}'", $principal_data);
        $tmkprincipal_data = array();
        $tmkprincipal_data['tmkprincipal_leave'] = 1;
        $tmkprincipal_data['tmkprincipal_updatatime'] = time();
        $this->DataControl->updateData("crm_client_tmkprincipal", "client_id='{$client_id}' and school_id > 0", $tmkprincipal_data);
    }
    //渠道变更记录
    function addChannellog($comClientOne, $company_id, $marketer_id, $channel_id)
    {
        $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'staffer_id', "marketer_id='{$marketer_id}'");
        $channello = array();
        $channello['company_id'] = $company_id;
        $channello['client_id'] = $comClientOne['client_id'];
        $channello['from_channel_id'] = $comClientOne['channel_id'];
        $channello['to_channel_id'] = $channel_id;
        $channello['channellog_note'] = "撞单系统操作，重新激活，直接变更渠道！";
        $channello['client_createtime_old'] = $comClientOne['client_createtime'];
        $channello['channellog_createtime'] = time();
        if ($id = $this->DataControl->insertData("crm_client_channellog", $channello)) {
            $trackData = array();
            $trackData['client_id'] = $comClientOne['client_id'];
            $trackData['channellog_id'] = $id;
            $trackData['tracks_title'] = '0';
            $trackData['staffer_id'] = $markertOne['staffer_id'];
            $trackData['tracks_time'] = time();
            $this->DataControl->insertData("crm_client_channel_tracks", $trackData);
        }
    }
    //学生适配学校
    function addSchoolEnter($client_id, $school_id, $company_id)
    {
        if ($this->DataControl->selectOne("select school_id from crm_client_schoolenter where client_id='{$client_id}' and school_id='{$school_id}'")) {
            $schoolenter_array = array();
            $schoolenter_array['is_enterstatus'] = '1';

            $schoolenter_array['is_gmctocrmschool'] = '0';
            $schoolenter_array['is_gmcdirectschool'] = '0';

            $schoolenter_array['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id='{$school_id}'", $schoolenter_array);
        } else {
            $schoolenter_array = array();
            $schoolenter_array['client_id'] = $client_id;
            $schoolenter_array['school_id'] = $school_id;
            $schoolenter_array['company_id'] = $company_id;
            $schoolenter_array['is_enterstatus'] = '1';
            $schoolenter_array['schoolenter_createtime'] = time();
            $schoolenter_array['schoolenter_updatetime'] = time();
            $this->DataControl->insertData("crm_client_schoolenter", $schoolenter_array);
        }
        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id<>'{$school_id}'", array("is_enterstatus" => '-1',"schoolenter_updatetime" => time()));
    }

    //20230523  集团名单那导入 是否进入校务  istoschool -1 否    istoschool  1 进
    function gmcClietToSchool($isgmcimport = 0,$istoschool,$clientid,$school_id=0){
        if($isgmcimport == 1 && $istoschool == '-1') {
            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$clientid}' ", $schoolenter);
        }
    }



}
