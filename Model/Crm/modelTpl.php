<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;


class modelTpl extends \Model\modelTpl{
	
	public function __construct()
	{
        parent::__construct();
	}
	
	public function __call($method, $args)
	{
		echo "unknown method " . $method;
		return false;
		
	}

    public function addCrmWorkLog($company_id,$school_id,$marketer_id,$module,$type,$content,$module_id=0){
        $stafferOne = $this->DataControl->getFieldOne("crm_marketer","staffer_id","marketer_id='{$marketer_id}'");
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['school_id'] =$school_id;
        $logData['staffer_id'] =$stafferOne['staffer_id'];
        $logData['worklog_module'] =$module;
        $logData['worklog_type'] =$type;
        $logData['worklog_content'] =$content;
        $logData['worklog_ip'] =real_ip();
        $logData['worklog_time'] =time();
        $this->DataControl->insertData('crm_staffer_worklog',$logData);
    }

    /**
	 * @param $client_id
	 * @param $marketer_id
	 * @param $ismajor
	 * @return bool
	 *  验证是否为负责人 --跟进时验证 --传0
	 * 验证为否为主负责人 ---流失时验证 -- 传1
	 */
	function verificationTrack($client_id, $marketer_id, $ismajor = 0)
	{
		$where = "marketer_id={$marketer_id}  and client_id={$client_id} and principal_leave =0";
		if ($ismajor == 1) {
			$where .= " and  principal_ismajor=1";
		}
		$principalOne = $this->DataControl->getOne('crm_client_principal', $where);
		if ($principalOne) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * @param $client_id
	 * @return bool
	 * 检测用户状态 是否没有被分配  1-false被分配
	 */
	function checkClientstatus($client_id,$school_id)
	{
		if (!$client_id) {
			return false;
		}
        $schoolenterOne = $this->DataControl->getFieldOne('crm_client_schoolenter','schoolenter_id',"client_id={$client_id} and school_id='{$school_id}' AND is_enterstatus = '1'");
        if(!$schoolenterOne){
            return false;//不在校区名单，禁止分配
        }
		$clientOne = $this->DataControl->getFieldOne("crm_client", 'client_distributionstatus', "client_id={$client_id}");
        if(!$clientOne || $clientOne['client_distributionstatus'] == '1'){
            return false;//已分配名单禁止分配
        }
        return true;
	}

	/**
	 * @param $client_id
	 * @return bool
	 * 检测用户状态 是否没有被分配  1-false被分配
	 */
	function checkTmkClientstatus($client_id,$school_id)
	{
		if (!$client_id) {
			return false;
		}
        $schoolenterOne = $this->DataControl->getFieldOne('crm_client_schoolenter','schoolenter_id',"client_id={$client_id} and school_id='{$school_id}' AND is_enterstatus = '1'");
        if(!$schoolenterOne){
            return false;//不在校区名单，禁止分配
        }
		$clientOne = $this->DataControl->getFieldOne("crm_client", 'client_schtmkdistributionstatus', "client_id={$client_id}");
        if(!$clientOne || $clientOne['client_schtmkdistributionstatus'] == '1'){
            return false;//已分配名单禁止分配
        }
        return true;
	}
	
	/**
	 * @param $client_id
	 * @return bool
	 * 检测用户是处在柜询或试听状态
	 */
	function checkIsvisit($client_id,$school_id)
	{
		if (!$client_id) {
			return false;
		}
		
		$clientIsinvite = $this->DataControl->getOne("crm_client_invite", "client_id='{$client_id}'and school_id='{$school_id}'", ' Order by invite_createtime Desc');
		$clientIsaudition = $this->DataControl->getOne("crm_client_audition", "client_id='{$client_id}' and school_id='{$school_id}'", ' Order by audition_createtime Desc');
		if ( ($clientIsinvite && ($clientIsinvite['invite_isvisit'] == 0)) || ($clientIsaudition && ($clientIsaudition['audition_isvisit'] == 0)) ) {
			return false;
		}else{
			return true;
		}
	}
	
	function checkIsvisitDate($client_id,$date,$type='',$hour=''){
        if($type == '1'){//和集团不一样   -1  和  0 区别
            $clientIsaudition =   $this->DataControl->getOne("crm_client_audition","audition_visittime='{$date}' and client_id ='{$client_id}' and audition_isvisit =0");
        }elseif ($type == '2'){
            $clientIsinvite =   $this->DataControl->getOne("crm_client_invite","invite_visittime='{$date}' and client_id ='{$client_id}' and invite_isvisit = '-1'");
        }else {//和集团不一样   -1  和  0 区别
            $clientIsaudition =   $this->DataControl->getOne("crm_client_audition","audition_visittime='{$date}' and client_id ='{$client_id}' and audition_isvisit =0");
            $clientIsinvite =   $this->DataControl->getOne("crm_client_invite","invite_visittime='{$date}' and client_id ='{$client_id}' and invite_isvisit =0");
        }
		if ( ($clientIsinvite && ($clientIsinvite['invite_isvisit'] == 0)) || ($clientIsaudition && ($clientIsaudition['audition_isvisit'] == 0)) ) {
			return false;
		}else{
			return true;
		}
	}

    //判断今日是否有邀约记录a试听i柜询
    function checkIsinviteLog($client_id,$date,$school_id,$visitType='a'){
        if ($visitType == 'a') {
            $sql = "SELECT
                        a.audition_id
                    FROM
                        crm_client_audition a
                    WHERE
                        a.school_id = '{$school_id}'
                    AND a.client_id = '{$client_id}'
                    AND a.audition_isvisit <> '-1'
                    AND DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = DATE_FORMAT('{$date}', '%Y-%m-%d')
                    limit 0,1";
        } else {
            $sql = "SELECT
                        i.invite_id
                    FROM
                        crm_client_invite i
                    WHERE
                        i.school_id = '{$school_id}'
                    AND i.client_id = '{$client_id}'
                    AND i.invite_isvisit <> '-1'
                    AND DATE_FORMAT(i.invite_visittime, '%Y-%m-%d') = DATE_FORMAT('{$date}', '%Y-%m-%d')
                    limit 0,1";
        }
        if($this->DataControl->selectOne($sql)){
            return true;
        }else{
            return false;
        }
    }

    //判断今日是否有邀约记录a试听i柜询
    function checkAuditionLog($client_id,$date,$school_id,$coursetype_id){
        if($this->DataControl->selectOne("SELECT
                                                    a.audition_id
                                                FROM
                                                    crm_client_audition a
                                                WHERE
                                                    a.school_id = '{$school_id}'
                                                AND a.client_id = '{$client_id}'
                                                AND a.audition_isvisit <> '-1'
                                                AND a.coursetype_id = '{$coursetype_id}'
                                                AND DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = DATE_FORMAT('{$date}', '%Y-%m-%d') limit 0,1")){
            return true;
        }else{
            return false;
        }
    }
	
	/**
	 * @param $icard
	 * @return bool
	 * 检测用户身份证是否冲重复 ,重复不能添加
	 * 新增时 不用 传$client_id
	 * 编辑时 传 $client_id
	 */
	function  checkClientIcard($icard,$client_id=0){
		 $datawhere = "1 and client_icard ='{$icard}'";
		 if(isset($client_id) && $client_id !==0 ){
			 $datawhere .= "and client_id <> '{$client_id}'";
		 }
         $clientOne = $this->DataControl->selectOne("select client_id from crm_client where {$datawhere} ");

		 if($clientOne){
		 	  return false;   //有重复的
		 }else{
		 	  return true;    //没重复
		 }
	}

    //判断手机号是不是本地（省份）
    function isoutmobile($school_id,$mobile,$client_id=0){
        //学校
        $schooleOne = $this->DataControl->selectOne("SELECT r.region_name FROM smc_school AS s, smc_code_region AS r
WHERE s.school_id = '{$school_id}' AND s.school_province = r.region_id");
        if($schooleOne){
            $apiUrl = "https://apis.juhe.cn/mobile/get?phone={$mobile}&dtype=json&key=3e1d094c9e7312014887b73227dfd205";
            $apiInfo = request_by_curl($apiUrl, '', "GET");
            if($apiArray = json_decode($apiInfo, 1)){
                if(isset($apiArray['result']['province']) && strpos($schooleOne['region_name'], $apiArray['result']['province']) !== false){
                    if($client_id !== '0'){
                        $this->DataControl->query("UPDATE crm_client_schoolenter SET is_outmobile = '0',schoolenter_updatetime = UNIX_TIMESTAMP()
WHERE school_id = '{$school_id}' AND client_id = '{$client_id}'");
                    }
                    return 'local';
                }else{
                    if($client_id !== '0'){
                        $this->DataControl->query("UPDATE crm_client_schoolenter SET is_outmobile = '1',schoolenter_updatetime = UNIX_TIMESTAMP()
WHERE school_id = '{$school_id}' AND client_id = '{$client_id}'");
                    }
                    return 'outland';
                }
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    //判断登录角色是不是 校园职务
    function isGmcPost($postbe_id,$company_id){
	    if($postbe_id > 0) {
            $postbeOne = $this->DataControl->selectOne("select school_id from gmc_staffer_postbe where postbe_id = '{$postbe_id}' and company_id = '{$company_id}' ");
            if ($postbeOne['school_id'] > 0) {
                return false;
            } else {
                return true;
            }
        }else{
            return false;
        }
    }

    //清楚名单负责人
    function liftClientPrincipa($from='',$client_id,$school_id){
        if($from=='crm' and $school_id > 0) {
            $principalOne = $this->DataControl->selectOne("select 1 from crm_client_principal where client_id = '{$client_id}' and school_id > 0   and principal_leave = '0' limit 0,1 ");
            if ($principalOne) {
                //解除负责人分配日志
                $this->DataControl->updateData('crm_client_allotlog', "client_id='{$client_id}' and school_id > 0  and allotlog_status = '1' ", ["allotlog_status" => '0', "allotlog_removetime" => time()]);

                $pdata = array();
                $pdata['principal_leave'] = 1;
                $pdata['principal_updatatime'] = time();
                $this->DataControl->updateData('crm_client_principal', "client_id='{$client_id}' and school_id > 0 and principal_leave = '0' ", $pdata);
            }
        }
    }
    //清楚名单柜询
    function liftClientInvite($from='',$client_id,$school_id){
        //柜询都是在学校，所以可以全部确认掉
        $inviteOne = $this->DataControl->selectOne("select 1 from crm_client_invite where client_id = '{$client_id}' and invite_isvisit = '0' limit 0,1 ");
        if ($inviteOne) {
            $pdata = array();
            $pdata['invite_isvisit'] = -1;
            $pdata['invite_novisitreason'] = "主管审核无效名单，清楚未确认的柜询";
            $pdata['invite_updatetime'] = time();
            $this->DataControl->updateData('crm_client_invite', "client_id='{$client_id}' and invite_isvisit = '0' ", $pdata);
        }
    }
    //清楚名单试听
    function liftClientAudition($from='',$client_id,$school_id){
        //试听都是在学校，所以可以全部确认掉
        $audOne = $this->DataControl->selectOne("select 1 from crm_client_audition where client_id = '{$client_id}' and audition_isvisit = '0' limit 0,1 ");
        if ($audOne) {
            $pdata = array();
            $pdata['audition_isvisit'] = -1;
            $pdata['audition_novisitreason'] = "主管审核无效名单，清楚未确认的柜询";
            $pdata['audition_updatetime'] = time();
            $this->DataControl->updateData('crm_client_audition', "client_id='{$client_id}' and audition_isvisit = '0' ", $pdata);
        }
    }

}
