<?php

namespace Model\Crm;

class ConversationModel extends modelTpl
{
    public $m;
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }
    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    // 通话记录
    function CallRecordList($request){
        $datawhere = "1";
        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $dataList = $this->DataControl->selectClear("SELECT * FROM crm_client_calllog WHERE {$datawhere} LIMIT {$pagestart},{$num}");
        if($dataList){
            foreach($dataList as &$val){
                $val['AudioFile'] = $val['file_server'] . '/' . $val['record_file_name'];
            }
        }else{
            $data['list'] = array();
        }

        $all_num = $this->DataControl->select("SELECT COUNT(calllog_id) as num FROM crm_client_calllog WHERE {$datawhere}");
        $allnums = $all_num[0]['num'];
        if ($allnums) {
            $data['allnums'] = $allnums;
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $dataList;

        return $data;
    }

    // 外呼通话
    function CallAction($request){
        $data = array();
        $data["FromExten"]      = $request['FromExten'];    //坐席工号
        $data["Exten"]          = $request['Exten'];        //被叫号码
        $data["ExtenType"]      = $request['ExtenType'];    //外呼方式 Local(手机)/sip(话机)/gateway(语音网关)
        $data["ActionID"]       = $request['ActionID'];     //唯一字符串（可随机）
        $data["DialoutStrVar"]  = $request['DialoutStrVar'];//外呼自定义参数,json格式

        $res = $this->CallStatus($request['FromExten']);
        if($res['data']['flag'] !== 99){
            $Model = new \Model\Crm\CalllogModel();
            $res = $Model->Call_curl("https://apis.7moor.com/v20160818/call/dialout/", $data);
        }
        if(!is_array($res)){
            $res = json_decode($res, true);
        }
        return $res;
    }

    // 查询坐席状态
    function CallStatus($exten){
        $data = array("exten" => $exten);

        $Model = new \Model\Crm\CalllogModel();
        $res = $Model->Call_curl("https://apis.7moor.com/v20160818/user/queryUserState/", $data);

        return json_decode($res, true);
    }

    // 对接事件推送接口
    function CallRecord($request){
        $data = array();
        $DialoutStrVar = json_decode(stripslashes($request['DialoutStrVar']), true);
        $data["marketer_id"] = $DialoutStrVar['marketer_id'];
        $data["client_id"] = $DialoutStrVar['client_id'];
        $data["track_id"] = $DialoutStrVar['track_id'];
        $data["calllog_sheetid"] = $request['CallSheetID'];
        $data["calllog_outnumber"] = $request['Exten'];
        $data["calllog_mobile"] = $request['CalledNo'];

        if ($request['CallType'] == 'dialout') {
            $data["calllog_type"] = $this->LgStringSwitch('外呼通话');
        } elseif($request['CallType'] == 'normal') {
            $data["calllog_type"] = $this->LgStringSwitch('普通来电');
        } elseif($request['CallType'] == 'transfer') {
            $data["calllog_type"] = $this->LgStringSwitch('呼入转接');
        } elseif($request['CallType'] == 'dialTransfer') {
            $data["calllog_type"] = $this->LgStringSwitch('外呼转接');
        }

        if ($request['State'] == 'dealing') {
            $data["calllog_state"] = $this->LgStringSwitch('已接');
        } elseif($request['State'] == 'notDeal') {
            $data["calllog_state"] = $this->LgStringSwitch('振铃未接听');
        } elseif($request['State'] == 'leak') {
            $data["calllog_state"] = $this->LgStringSwitch('IVR放弃');
        } elseif($request['State'] == 'queueLeak') {
            $data["calllog_state"] = $this->LgStringSwitch('排队放弃');
        } elseif($request['State'] == 'blackList') {
            $data["calllog_state"] = $this->LgStringSwitch('黑名单');
        } elseif($request['State'] == 'voicemail') {
            $data["calllog_state"] = $this->LgStringSwitch('留言');
        } elseif($request['State'] == 'limit') {
            $data["calllog_state"] = $this->LgStringSwitch('并发限制');
        }

        $data["calllog_ringingdate"] = $request['RingingDate'];
        $data["calllog_begintime"] = $request['Begin'];
        $data["calllog_endtime"] = $request['End'];
        $data["calllog_filename"] = $request['RecordFile'];
        $data["calllog_fileurl"] = $request['FileServer'];
        $data["calllog_province"] = $request['Province'];
        $data["calllog_district"] = $request['District'];
        $data["calllog_tag"] = $request['DialoutStrVar'];
        $data["calllog_createtime"] = time();

        $list = array();
        $list['recordlog_apiurl'] = "{$request['u']}/{$request['t']}";
        $list['recordlog_parameter'] = @mysql_escape_string(http_build_query($request));
        $list['recordlog_time'] = time();
        $this->DataControl->insertData("crm_api_recordlog",$list);

        if($this->DataControl->insertData('crm_client_calllog',$data)){
            $res = array('error' => '0', 'errortip' => "接收成功", 'result' => array());
        }else{
            $res = array('error' => '1', 'errortip' => "接收失败", 'result' => array());
        }
        return $res;
    }

}