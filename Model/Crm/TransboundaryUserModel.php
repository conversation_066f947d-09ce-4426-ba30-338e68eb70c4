<?php
/**
 * 跨界活动-用户端
 */

namespace Model\Crm;


use Work\Controller\Crmapi\TransboundaryUserController;

class TransboundaryUserModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    /**
     * 获取用户信息
     * @param $paramArray
     * @return false|mixed
     */
    public function getUser($paramArray, $company_id)//openid,session_key,phoneNumber,company_id
    {
        //查询openid
        $userInfo = $this->DataControl->selectOne("SELECT user_id,user_mobile,user_nickname,user_img,user_name,user_age,user_sex FROM crm_transboundary_user WHERE company_id='{$company_id}' and user_openid='{$paramArray['openid']}'");
        //已存在则返回用户信息
        if($userInfo){

            return $userInfo;
        }else{
            //不存在则创建并返回用户信息
            $data = array();
            $data['company_id'] = $company_id;
            $data['user_openid'] = $paramArray['openid'];
            $data['user_mobile'] = $paramArray['phoneNumber'];
            $data['user_createtime'] = time();
            $id = $this->DataControl->insertData("crm_transboundary_user", $data);
            if($id){
                $newUserInfo = $this->DataControl->selectOne("SELECT user_id,user_mobile FROM crm_transboundary_user WHERE user_id='{$id}'");
            }else{
                ajax_return(array('error' => 1, 'errortip'=>'添加失败!','result' =>array()));
            }

            return $newUserInfo;
        }
    }

    /**
     * 更新用户信息
     * @param $paramArray
     * @return bool
     */
    public function updateUserInfo($paramArray)
    {
        $updateData = array();
        $updateData['user_nickname'] = $paramArray['user_nickname'];
        $updateData['user_img'] = $paramArray['user_img'];
        $updateData['user_name'] = $paramArray['user_name'];
        $updateData['user_age'] = $paramArray['user_age'];
        $updateData['user_sex'] = $paramArray['user_sex'];
        $userOne = $this->DataControl->getFieldOne("crm_transboundary_user", "user_id", "user_id='{$paramArray['user_id']}' and company_id = '{$paramArray['company_id']}'");
        if($userOne){
            $updateData['user_updatetime'] = time();
            $result = $this->DataControl->updateData("crm_transboundary_user","user_id='{$paramArray['user_id']}'",$updateData);
        }else{
            $userTwo = $this->DataControl->getFieldOne("crm_transboundary_user", "user_openid,user_mobile", "user_id='{$paramArray['user_id']}'");
            $updateData['company_id'] = $paramArray['company_id'];
            $updateData['user_openid'] = $userTwo['user_openid'];
            $updateData['user_mobile'] = $userTwo['user_mobile'];
            $updateData['user_updatetime'] = time();
            $updateData['user_createtime'] = time();
            $result = $this->DataControl->insertData("crm_transboundary_user", $updateData);
            $paramArray['user_id'] = $result;
        }
        if($result){
            $userInfo = $this->DataControl->selectOne("SELECT user_id,user_mobile,user_nickname,user_img,user_name,user_age,user_sex FROM crm_transboundary_user WHERE user_id='{$paramArray['user_id']}'");
            $this->error = 0;
            $this->errortip = "用户信息注册成功";
            $this->result = $userInfo;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "用户信息注册失败";
            $this->result = [];
            return false;
        }
    }

    /**
     * 活动首页
     * @param $paramArray activity_id
     * @return bool
     */
    public function activityHome($paramArray)
    {
        //活动信息
        $activityData = $this->DataControl->selectOne("SELECT activity_id,activity_name,activity_rule,activity_startdate,activity_enddate,activity_amounttype,activity_amountrange FROM crm_transboundary_activity WHERE activity_id = '{$paramArray['activity_id']}'");
        if($activityData) {
            $activityData['activity_startdate'] = date("n月j日", strtotime($activityData['activity_startdate']));
            $activityData['activity_enddate'] = date("n月j日", strtotime($activityData['activity_enddate']));
            $record = $this->DataControl->selectOne("SELECT r.record_id FROM crm_transboundary_activity_record AS r LEFT JOIN crm_transboundary_activity AS a ON a.activity_id = r.activity_id WHERE r.user_id = '{$paramArray['user_id']}' and a.company_id = '{$paramArray['company_id']}' LIMIT 1");
            $activityData['is_prize'] = $record ? true : false;
            //中奖用户列表
            $recordList = $this->DataControl->selectClear("
            SELECT 
                u.user_nickname,r.record_winningamount 
            FROM 
                crm_transboundary_activity as a
                LEFT JOIN crm_transboundary_activity_record AS r ON r.activity_id = a.activity_id 
                LEFT JOIN crm_transboundary_user AS u ON r.user_id = u.user_id 
            WHERE 
                a.company_id = '{$paramArray['company_id']}' and r.record_winningamount > 0 
            ORDER BY 
                r.record_createtime DESC 
            ");
            $amountrange = explode(',',$activityData['activity_amountrange']);
            if ($activityData['activity_amounttype']) {
                $amountrange1 = mt_rand($amountrange[0] * 100, $amountrange[1] * 100) / 100;
                $amountrange2 = mt_rand($amountrange[0] * 100, $amountrange[1] * 100) / 100;
                $amountrange3 = mt_rand($amountrange[0] * 100, $amountrange[1] * 100) / 100;
                $amountrange4 = mt_rand($amountrange[0] * 100, $amountrange[1] * 100) / 100;
                $amountrange5 = mt_rand($amountrange[0] * 100, $amountrange[1] * 100) / 100;
            } else {
                $amountrange1 = $amountrange2 = $amountrange3 = $amountrange4 = $amountrange5 = sprintf('%.2f',$amountrange[0]);
            }
            $ceshidata[0] = array('user_nickname' => '夏天的风', 'record_winningamount' => $amountrange1);
            $ceshidata[1] = array('user_nickname' => '小鱼儿',   'record_winningamount' => $amountrange2);
            $ceshidata[2] = array('user_nickname' => '小汤老师', 'record_winningamount' => $amountrange3);
            $ceshidata[3] = array('user_nickname' => 'Amber',   'record_winningamount' => $amountrange4);
            $ceshidata[4] = array('user_nickname' => '海的故乡', 'record_winningamount' => $amountrange5);
            if(!$recordList){
                $recordList = $ceshidata;
            }else{
                $recordList = array_merge($recordList, $ceshidata);
            }
            $userData = $this->DataControl->selectOne("SELECT user_name,user_age,user_sex FROM crm_transboundary_user WHERE user_id = '{$paramArray['user_id']}' and company_id = '{$paramArray['company_id']}'");
            if(!$userData['user_name'] || !$userData['user_age'] || !$userData['user_sex']) {
                $activityData['is_info'] = true;
            }else{
                $activityData['is_info'] = false;
            }
        }else{
            $activityData = [];
        }

        $result = [];
        $result['activity_data'] = $activityData;
        $result['record_list'] = $recordList;

        if($activityData){
            $this->error = 0;
            $this->errortip = "获取成功!";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "未找到该活动相关信息";
            $this->result = [];
            return true;
        }
    }

    /**
     * 我的奖品列表
     * @param $paramArray user_id
     * @return bool
     */
    public function myPrizeList($paramArray)
    {
        $prizeList = $this->DataControl->selectClear("SELECT 
                                                            a.activity_name,r.record_winningamount,r.record_createtime,r.record_settlementstatus 
                                                        FROM
                                                            crm_transboundary_activity_record AS r 
                                                            LEFT JOIN crm_transboundary_activity AS a ON r.activity_id = a.activity_id 
                                                        WHERE 
                                                            a.company_id = '{$paramArray['company_id']}' and r.user_id = '{$paramArray['user_id']}'
                                                        ORDER BY 
                                                            r.record_createtime DESC
                                                        LIMIT 1");
        if($prizeList){
            foreach ($prizeList as &$value) {
                $value['record_createdate'] = date("Y-m-d", $value['record_createtime']);
                if($value['record_winningamount'] == '0.00'){
                    $value['record_winningamount'] = '感谢参与';
                }
                if($value['record_settlementstatus'] == '1'){
                    $value['record_settlementstatus_name'] = '已发放';
                }else{
                    $value['record_settlementstatus_name'] = '待发放';
                }

            }
        }

        if($prizeList){
            $this->error = 0;
            $this->errortip = "我的奖品信息";
            $this->result = $prizeList;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "您还未中奖哦";
            $this->result = [];
            return false;
        }
    }

    /**
     * 参与抽奖
     * @param $paramArray user_id
     * @param $paramArray activity_id
     * @return bool
     */
    public function drawPrizes($paramArray)
    {
        $field = "frommedia_id,channel_id,activity_name,business_mobile,activity_amount,activity_leftamount,activity_amountrange,activity_amounttype,activity_startdate,activity_enddate,activity_status,activity_validusernum,activity_paymentamount";
        //活动信息
        $activityData = $this->DataControl->selectOne("SELECT {$field} FROM crm_transboundary_activity WHERE activity_id = '{$paramArray['activity_id']}'");
        if(empty($activityData)){
            $this->error = 1;
            $this->errortip = "未找到该跨界活动!";
            $this->result = [];
            return false;
        }else{
            //活动状态判断
            $activity_starttime = strtotime($activityData['activity_startdate']);
            $activity_endtime = strtotime($activityData['activity_enddate'].' 23:59:59');
            //不在活动时间范围内
            if($activityData['activity_status'] == 2){
                $this->error = 1;
                $this->errortip = "活动已经结束了哦~；下次记得早点来哦";
                $this->result = [];
                return false;
            }elseif(time() < $activity_starttime){
                //如果不在活动时间内 但状态为 进行中 则更新活动状态为 已结束
                if($activityData['activity_status'] == 1){
                    $this->DataControl->updateData("crm_transboundary_activity","activity_id='{$paramArray['activity_id']}'", ['activity_status' => 0]);
                }
                $startdate = date("m月d日", strtotime($activityData['activity_startdate']));
                $enddate = date("m月d日", strtotime($activityData['activity_enddate']));
                $this->error = 1;
                $this->errortip = "活动还没有开始哦~；活动时间{$startdate}-{$enddate}";
                $this->result = [];
                return false;
            }elseif(time() > $activity_endtime){
                //如果不在活动时间内 但状态为 进行中 则更新活动状态为 已结束
                if($activityData['activity_status'] == 1){
                    $this->DataControl->updateData("crm_transboundary_activity","activity_id='{$paramArray['activity_id']}'", ['activity_status' => 2]);
                }

                $this->error = 1;
                $this->errortip = "活动已经结束了哦~；下次记得早点来哦";
                $this->result = [];
                return false;
            }else{
                //如果在活动时间内 但状态为 未开始 则更新活动状态
                if($activityData['activity_status'] == 0){
                    $this->DataControl->updateData("crm_transboundary_activity","activity_id='{$paramArray['activity_id']}'", ['activity_status' => 1]);
                }
            }

            if($activityData['activity_amount'] > 0){
                $activity_amountrange_array = explode(',',$activityData['activity_amountrange']);
                if($activityData['activity_leftamount'] == 0 || $activityData['activity_leftamount'] < $activity_amountrange_array[0]){
                    //活动预算已抽完 活动状态为 已结束
                    if($activityData['activity_status'] == 1){
                        $this->DataControl->updateData("crm_transboundary_activity","activity_id='{$paramArray['activity_id']}'", ['activity_status' => 2]);

//                        $Controller = new TransboundaryUserController();
//                        $mistxt = "尊敬的商户您好，您参与的{$activityData['activity_name']}已经提前结束，请及时下架店内的红包抽奖二维码";
//                        $Controller->Sendmisgo($activityData['business_mobile'], $mistxt, '跨界活动', rand(111111, 999999), $paramArray['company_id']);
                    }
                    $this->error = 1;
                    $this->errortip = "活动已经结束了哦~；下次记得早点来哦";
                    $this->result = [];
                    return false;
                }
            }

            //是否参与过该抽奖 activity_id = '{$paramArray['activity_id']}' AND
            $recordData = $this->DataControl->selectOne("SELECT r.record_id FROM crm_transboundary_activity_record as r LEFT JOIN crm_transboundary_activity as a ON a.activity_id = r.activity_id WHERE a.company_id = '{$paramArray['company_id']}' and r.user_id = '{$paramArray['user_id']}' LIMIT 1");
            if($recordData){
                $this->error = 1;
                $this->errortip = "抽奖失败；您已抽过奖了，请勿重复抽奖。";
                $this->result = [];
                return false;
            }else{
                //计算中奖结果
                $lotteryResults = [];

                $this->DataControl->begintransaction();//开启事务

                $clientInfo = $this->DataControl->selectOne("SELECT client_id FROM crm_transboundary_user as u LEFT JOIN crm_client as c ON c.client_mobile = u.user_mobile WHERE c.company_id = '{$paramArray['company_id']}' AND u.user_id = '{$paramArray['user_id']}' LIMIT 1");
                //CRM新用户
                if (!$clientInfo) {
                    //抽奖金额范围 固定金额则数组2个值相同
                    $activity_amountrange_array = explode(',',$activityData['activity_amountrange']);

                    //是否为固定金额
                    if ($activityData['activity_amounttype'] == 0) {
                        $winningAmount = $activity_amountrange_array[0];

                        if($activityData['activity_amount'] > 0) {
                            $activity_leftamount = ($activityData['activity_leftamount'] * 100 - $winningAmount * 100) / 100;//活动剩余金额
                        }else{
                            $activity_leftamount = '0.00';//活动剩余金额
                        }
                    }else{
                        //非无限预算
                        //无限预算 不看不扣除余额
                        if($activityData['activity_amount'] > 0){
                            //计算随机金额
                            //查询余额是否大于 最大 抽奖范围
                            //大于则 取抽奖范围之间的随机数
                            //小于等于则 抽奖的金额为余额
                            $difference = $activityData['activity_leftamount']*100-$activity_amountrange_array[1]*100;
                            if($difference > 0){
                                $winningAmount = mt_rand($activity_amountrange_array[0]*100,$activity_amountrange_array[1]*100)/100;//中奖金额
                            }else{
                                $winningAmount = $activityData['activity_leftamount'];//中奖金额
                            }
                            $activity_leftamount = ($activityData['activity_leftamount']*100 - $winningAmount*100)/100;//活动剩余金额
                        }else{
                            $activity_leftamount = '0.00';//活动剩余金额
                            $winningAmount = mt_rand($activity_amountrange_array[0]*100,$activity_amountrange_array[1]*100)/100;//中奖金额
                        }
                    }

                    $activity_paymentamount = ($activityData['activity_paymentamount']*100 + $winningAmount*100)/100;//活动发放金额

                    $activity_validusernum = $activityData['activity_validusernum']+1;//有效用户数量+1

                    //更新 剩余金额 发放金额 有效用户数量
                    $this->DataControl->updateData("crm_transboundary_activity"," activity_id='{$paramArray['activity_id']}' ",[
                        'activity_paymentamount' => $activity_paymentamount,
                        'activity_leftamount'    => $activity_leftamount,
                        'activity_validusernum'  => $activity_validusernum
                    ]);

                    $lotteryResults['winning_amount'] = $winningAmount;//中奖金额
                    $lotteryResults['is_winning'] = 1;//中奖

                }else{
                    $lotteryResults['winning_amount'] = '0.00';//中奖金额
                    $lotteryResults['is_winning'] = 0;//未中奖
                }

                $data = array();
                $data['activity_id'] = $paramArray['activity_id'];
                $data['user_id'] = $paramArray['user_id'];
                $data['record_winningamount'] = $lotteryResults['winning_amount'];
                $data['record_settlementamount'] = '0.00';
                $data['record_createtime'] = time();

                //记录抽奖信息
                $record_id = $this->DataControl->insertData("crm_transboundary_activity_record", $data);
                if($record_id){
                    $frommedia = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_name", "frommedia_id = '{$activityData['frommedia_id']}'");
                    $channel = $this->DataControl->getFieldOne("crm_code_channel", "channel_name", "channel_id = '{$activityData['channel_id']}'");
                    $user = $this->DataControl->getFieldOne("crm_transboundary_user", "user_name,user_mobile,user_age,user_sex", "user_id = '{$paramArray['user_id']}'");
                    $school = $this->DataControl->getFieldOne("smc_school", "school_branch", "school_id = '{$paramArray['school_id']}'");
                    //存储到 CRM
                    $parameter = array();
                    $parameter['client_cnname'] = $user['user_name'];
                    $parameter['client_age'] = $user['user_age'];
                    $parameter['client_sex'] = $user['user_sex'];
                    $parameter['client_frompage'] = "跨界活动小程序";
                    $parameter['client_tag'] = "跨界活动小程序";
                    $parameter['client_source'] = $frommedia['frommedia_name'];
                    $parameter['channel_name'] = $channel['channel_name'];
                    $parameter['client_patriarchname'] = '匿名';
                    $parameter['client_mobile'] = $user['user_mobile'];
                    $parameter['client_remark'] = '跨界活动小程序';
                    $parameter['company_id'] = $paramArray['company_id'];
                    $parameter['school_branch'] = $school['school_branch'];

                    $parameter['activity_id'] = $paramArray['activity_id'];
                    request_by_curl("https://crmapi.kedingdang.com/PhoneActivity/addPhoneChannelAction",dataEncode($parameter),"POST",array());

                    $this->DataControl->commit();
                }else{
                    $this->DataControl->rollback();
                }

                $this->error = 0;
                $this->errortip = "已参与抽奖!";
                $this->result = $lotteryResults;
                return true;
            }
        }
    }
}