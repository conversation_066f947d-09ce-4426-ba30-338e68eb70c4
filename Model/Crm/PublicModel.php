<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  PublicModel extends modelTpl
{
	public $m;
	
	function __construct()
	{
		parent::__construct();
	}
    /**
     * @param $company_id
     * @return array
     * 用户学校切换记录
     */
    function addStafferSchoolAction($request) {

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['newschool_id'];
        $data['postbe_id'] = $request['re_postbe_id'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['marketer_id'] = $request['marketer_id'];
        $data['schoollog_port'] = 1;
        $data['schoollog_createtime'] = time();
        $log_id = $this->DataControl->insertData('imc_staffer_schoollog',$data);
        if($log_id){
            return true;
        }else{
            return false;
        }
    }

    /**
	 * @param $company_id
	 * @return array
	 * 获取沟通类型
	 */
	function getTrackTypeApi($company_id)
	{
		$commodeList = $this->DataControl->getFieldquery('smc_code_tracktype', 'tracktype_id,tracktype_name', "company_id='{$company_id}' and tracktype_eas='1'");
		$list = $commodeList;
		return $list;
	}

    /**
	 * @param $company_id
	 * @return array
	 * 获取沟通方式
	 */
	function getCommedeByCompanyId($company_id)
	{
		$commodeList = $this->DataControl->getFieldquery('crm_code_commode', 'commode_id,commode_name', "company_id='{$company_id}'"," ORDER BY commode_sort ASC");
		$list = $commodeList;
		return $list;
	}

    /**
	 * @param $company_id
	 * @return array
	 * 获取沟通结果
	 */
	function getResultTypeApi($company_id, $tracktype_id)
	{
		$commodeList = $this->DataControl->getFieldquery('smc_code_trackresult', 'trackresult_id,trackresult_name', "company_id='{$company_id}' and tracktype_id='{$tracktype_id}' ");
		$list = $commodeList;
		return $list;
	}

    /**
     * @param $company_id
     * @return array
     * 获取无效原因模板
     */
    function getInvalidnoteApi($company_id)
    {
        $mediaList = $this->DataControl->getFieldquery('crm_code_invalidnote', 'invalidnote_id,invalidnote_code,invalidnote_reason', "company_id='{$company_id}' and invalidnote_isopen = '1' ");

        if($mediaList){
            $list = $mediaList;
        }else{
            $list = array();
        }
        return $list;
    }
	/**
	 * @param $company_id
	 * @return array
	 * 获取沟通模板
	 */
	function getTraceNote($company_id)
	{
		$mediaList = $this->DataControl->getFieldquery('crm_code_tracenote', 'tracenote_code,tracenote_remk', "company_id={$company_id}");
		
		if($mediaList){
			$list = $mediaList;
		}else{
			$list = array();
		}
		return $list;
	}

    /**
     * @param $school_id
     * @return array
     * 获取附近学校
     */
    function getNearSchoolApi($school_id)
    {
        $mediaList = $this->DataControl->getFieldquery('crm_code_nearschool', 'nearschool_id,nearschool_name,nearschool_shortname', "school_id='{$school_id}' and nearschool_status = '1'");

        if($mediaList){
            $list = $mediaList;
        }else{
            $list = array();
        }
        return $list;
    }
	
	/**
	 * @param $company_id
	 * @return array
	 *获取招生来源
	 */
	function getSourceFromMedia($company_id,$isschoolchose = '1')
	{
	    $datawhere = "company_id='{$company_id}'";
	    if($isschoolchose == '1'){
            $datawhere .= " AND frommedia_isschoolchose='{$isschoolchose}'";
        }
		$mediaList = $this->DataControl->getFieldquery('crm_code_frommedia', 'frommedia_id,frommedia_name', $datawhere);
		if($mediaList){
			$list = $mediaList;
		}else{
			$list = array();
		}
		return $list;
	}

	/**
	 * @param $company_id
	 * @return array
	 *获取招生来源
	 */
	function getSourceFromMediaTwo($company_id,$isschoolchose = '1')
	{
        if($company_id == '8888'){
            $datawhere = "company_id='{$company_id}' and frommedia_id <> '387'";
        }else{
            $datawhere = "company_id='{$company_id}'";
        }
	    if($isschoolchose == '1'){
            $datawhere .= " AND frommedia_isschoolchose='{$isschoolchose}'";
        }
		$mediaList = $this->DataControl->getFieldquery('crm_code_frommedia', 'frommedia_id,frommedia_name', $datawhere);
		if($mediaList){
			$list = $mediaList;
		}else{
			$list = array();
		}
		return $list;
	}

	/**
	 * @param $request
	 * @return array
	 *获取活动
	 */
	function getActivityApi($request)
	{
        if($request['dataequity'] != '1'){
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe","organize_id","postbe_id = '{$request['re_postbe_id']}'");
        }
        //全部权益
	    if($request['dataequity'] == '0'){
            $mediaList = $this->DataControl->selectClear("select a.activity_id,a.activity_name, ( SELECT group_concat( cyo.organize_id )
FROM crm_channel_organize AS co LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id  WHERE co.channel_id = a.channel_id
	) AS organize_id from crm_sell_activity as a where a.company_id = {$request['company_id']} and a.activity_type = '1' having organize_id like '%{$organize_id['organize_id']}%'  ORDER BY a.activity_id DESC ");
        }else{
            $mediaList = $this->DataControl->getFieldquery('crm_sell_activity', 'activity_id,activity_name', "company_id={$request['company_id']} and activity_type = '1'"," ORDER BY activity_id DESC ");
        }

		if($mediaList){
			$list = $mediaList;
		}else{
			$list = array();
		}
		return $list;
	}
	
	/**
	 * @param $request
	 * @return array|bool
	 *   获取渠道明细
	 */
	function getClientChannel($request,$isschoolchose='1')
 	{
        if($request['dataequity'] != '1'){
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe","organize_id","postbe_id = '{$request['re_postbe_id']}'");
        }
 	    $where = "1 and  channel_isuse =1";

        if (isset($request['channel_medianame']) && $request['channel_medianame'] !== '' && $request['channel_medianame'] !== '[]') {
            if(is_array($request['channel_medianame'])){//集团招生无意向 联动问题，增加判断
                $commodestr = ' ';
                foreach ($request['channel_medianame'] as $commodevar) {
                    if($commodevar != '') {
                        $commodestr .= "'" . $commodevar . "'" . ',';
                    }
                }
                $commodestr = substr($commodestr, 0, -1);
                $commodestr = ($commodestr=='')?1:$commodestr;
                $where .= " and c.channel_medianame in ({$commodestr}) ";
            }else {
                $commodeArray = json_decode(stripslashes($request['channel_medianame']), 1);
                if (is_array($commodeArray) && count($commodeArray) > 0) {
                    $commodestr = '';
                    foreach ($commodeArray as $commodevar) {
                        $commodestr .= "'" . $commodevar . "'" . ',';
                    }
                    $commodestr = substr($commodestr, 0, -1);
                    $where .= " and c.channel_medianame in ({$commodestr}) ";
                } else {
                    $where .= " and c.channel_medianame  = '{$request['channel_medianame']}'";
                }
            }
        }

        //学校对应的组织
        if($request['dataequity'] == '0'){
            $organizeidstr = $this->DataControl->selectOne("select group_concat(organize_id) as organizeidstr from gmc_company_organizeschool WHERE school_id = '{$request['school_id']}'");
            if($organizeidstr['organizeidstr'] != ''){
                $where .= " and o.organize_id in ({$organizeidstr['organizeidstr']})";
            }
            $channelList = $this->DataControl->selectClear("select c.channel_id,c.channel_name, c.channel_push,
                                (
                                    SELECT
                                        group_concat( cyo.organize_id ) 
                                    FROM
                                        crm_channel_organize AS co
                                        LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
                                    WHERE
                                        co.channel_id = c.channel_id 
                                ) AS organize_id 
                                from crm_code_channel as c 
                                LEFT JOIN crm_channel_organize as o ON c.channel_id = o.channel_id 
                                WHERE company_id = '{$request['company_id']}' and {$where}
                                GROUP BY c.channel_id 
                                having organize_id like '%{$organize_id['organize_id']}%' ");
        }else{
            if(isset($request['fixed_channel_id'])){
                if($request['fixed_channel_id'] !==''){
                    $where .= " and (c.channel_isschoolchose = '1' or  c.channel_id = '{$request['fixed_channel_id']}' ) ";
                }else{
                    $where .= " and c.channel_isschoolchose = '1'";
                }
            }
            $channelList = $this->DataControl->selectClear(" select c.channel_id,c.channel_name,c.channel_push from crm_code_channel as c
                                                        LEFT JOIN crm_channel_organize as o ON c.channel_id = o.channel_id 
                                                        WHERE company_id = '{$request['company_id']}' and {$where}
                                                        GROUP BY c.channel_id ");
        }



		if($channelList){
			$list = $channelList;
		}else{
			$list = array();
		}
		return $list;
	 }

    /**
     * @param $request
     * @return array|bool
     *   获取渠道板块明细
     */
    function getChannelBoardApi($request)
    {

        $listData = $this->DataControl->selectClear("select channel_board from crm_code_channel where company_id = '{$request['company_id']}' and channel_board <> ''  group by channel_board ");

        if($listData){
            $list = $listData;
        }else{
            $list = array();
        }
        return $list;
    }

	/**
	 * @param $request
	 * @return array|bool
	 *   获取渠道明细
	 */
	function getClientChannelTwo($request,$isschoolchose='1')
 	{
        if($request['dataequity'] != '1'){
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe","organize_id","postbe_id = '{$request['re_postbe_id']}'");
        }
        if ($request['company_id'] == '8888') {
            $where = "c.channel_isuse = 1 and c.channel_medianame <> '转介绍' ";
        } else {
            $where = "c.channel_isuse = 1";
        }

        if (isset($request['channel_medianame']) && $request['channel_medianame'] !== '' && $request['channel_medianame'] !== '[]') {
            $commodeArray = json_decode(stripslashes($request['channel_medianame']),1);
            if(is_array($commodeArray) && count($commodeArray) > 0){
                $commodestr = '';
                foreach ($commodeArray as $commodevar){
                    $commodestr .= "'".$commodevar."'".',';
                }
                $commodestr = substr($commodestr, 0, -1) ;
                $where .= " and c.channel_medianame in ({$commodestr}) ";
            }else{
                $where .= " and c.channel_medianame  = '{$request['channel_medianame']}'";
            }
        }

        //学校对应的组织
        if($request['dataequity'] == '0'){
            $organizeidstr = $this->DataControl->selectOne("select group_concat(organize_id) as organizeidstr from gmc_company_organizeschool WHERE school_id = '{$request['school_id']}'");
            if($organizeidstr['organizeidstr'] != ''){
                $where .= " and o.organize_id in ({$organizeidstr['organizeidstr']})";
            }
            $channelList = $this->DataControl->selectClear("select c.channel_id,c.channel_name, 
                                (
                                    SELECT
                                        group_concat( cyo.organize_id ) 
                                    FROM
                                        crm_channel_organize AS co
                                        LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
                                    WHERE
                                        co.channel_id = c.channel_id 
                                ) AS organize_id 
                                from crm_code_channel as c 
                                LEFT JOIN crm_channel_organize as o ON c.channel_id = o.channel_id 
                                WHERE company_id = '{$request['company_id']}' and {$where}
                                GROUP BY c.channel_id 
                                having organize_id like '%{$organize_id['organize_id']}%' ");
        }else{
            if(isset($request['fixed_channel_id'])){
                if($request['fixed_channel_id'] !==''){
                    $where .= " and (c.channel_isschoolchose = '1' or  c.channel_id = '{$request['fixed_channel_id']}' ) ";
                }else{
                    $where .= " and c.channel_isschoolchose = '1'";
                }
            }
            $channelList = $this->DataControl->selectClear(" select c.channel_id,c.channel_name from crm_code_channel as c
                                                        LEFT JOIN crm_channel_organize as o ON c.channel_id = o.channel_id 
                                                        WHERE company_id = '{$request['company_id']}' and {$where}
                                                        GROUP BY c.channel_id ");
        }



		if($channelList){
			$list = $channelList;
		}else{
			$list = array();
		}
		return $list;
	 }

    function getReportClientChannel($request,$isschoolchose='1')
    {
//        if($request['dataequity'] != '1'){
//            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe","organize_id","postbe_id = '{$request['re_postbe_id']}'");
//        }
        $where = "1 and  channel_isuse =1";

        if (isset($request['channel_medianame']) && $request['channel_medianame'] !== '' && $request['channel_medianame'] !== '[]') {
            $commodeArray = json_decode(stripslashes($request['channel_medianame']),1);
            if(is_array($commodeArray) && count($commodeArray) > 0){
                $commodestr = '';
                foreach ($commodeArray as $commodevar){
                    $commodestr .= "'".$commodevar."'".',';
                }
                $commodestr = substr($commodestr, 0, -1) ;
                $where .= " and c.channel_medianame in ({$commodestr}) ";
            }else{
                $where .= " and c.channel_medianame  = '{$request['channel_medianame']}'";
            }
        }

        //学校对应的组织
        if(isset($request['fixed_channel_id'])){
            if($request['fixed_channel_id'] !==''){
                $where .= " and (c.channel_isschoolchose = '1' or  c.channel_id = '{$request['fixed_channel_id']}' ) ";
            }else{
                $where .= " and c.channel_isschoolchose = '1'";
            }
        }
        $channelList = $this->DataControl->selectClear(" select c.channel_id,c.channel_name from crm_code_channel as c
                                                        LEFT JOIN crm_channel_organize as o ON c.channel_id = o.channel_id 
                                                        WHERE company_id = '{$request['company_id']}' and {$where}
                                                        GROUP BY c.channel_id ");


        if($channelList){
            $list = $channelList;
        }else{
            $list = array();
        }
        return $list;
    }
	
	
	/**
	 * @param $company_id
	 * @return array
	 *获取绑定关系
	 */
	function getFriendRelation()
	{
		$relationList = $this->DataControl->getFieldquery('crm_code_friendrelation', 'friendrelation_id,friendrelation_name', '1');
		$list = $relationList;
		return $list;
	}
	/**
	 * @param $company_id
	 * @return array
	 *获取课程
	 */
	function getIntentCourse($company_id)
	{
		$courseList = $this->DataControl->getFieldquery('smc_code_coursecat', 'coursecat_id,coursecat_cnname', "company_id='{$company_id}'");
		
		if($courseList){
			$list = $courseList;
		}else{
			$list = array();
		}
		
		return $list;
	}
	
	/**
	 * @param $company_id
	 * @return array
	 *获取负责人列表
	 */
	function getMarketerList($company_id, $marketer_id,$school_id,$request=array())
	{
		$where = "m.staffer_id = s.staffer_id AND s.company_id = '{$company_id}' and s.staffer_leave = '0' ";
        $where .= " AND (s.staffer_leave = 0 OR  m.marketer_id IN (SELECT p.marketer_id FROM crm_client AS c,crm_client_principal as p
WHERE c.client_id = p.client_id AND p.principal_leave = '0' AND p.principal_ismajor = '1' AND p.school_id = '{$school_id}' AND c.client_tracestatus IN (0,1,2,3) ))";
		
		if($marketer_id){
			 $where .= " and m.marketer_id <> '{$marketer_id}' ";
		}
        if($request['isHaveSchStaff'] == '1'){//是否包含 校务的老师
            if($request['isHaveTmk'] == '1'){
                // 包含 tmk 的筛选
                $where .= " AND s.staffer_id IN ( SELECT p.staffer_id FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post as t ON p.post_id = t.post_id WHERE p.school_id = '{$school_id}' AND p.postbe_status = '1' and t.post_isteaching = '1' )";
            }else{
                // 不包含 tmk 的筛选
                $where .= " AND s.staffer_id IN ( SELECT p.staffer_id FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post as t ON p.post_id = t.post_id WHERE p.school_id = '{$school_id}' AND p.postbe_status = '1' and p.postbe_crmuserlevel <> '2' and t.post_isteaching = '1')";
            }
        }else{
            if($request['isHaveTmk'] != '1'){
                // 不包含 tmk 的筛选
                $where .= " AND s.staffer_id IN ( SELECT p.staffer_id FROM gmc_staffer_postbe AS p WHERE p.school_id = '{$school_id}' AND p.postbe_status = '1' AND postbe_iscrmuser = '1' and p.postbe_crmuserlevel <> '2' )";
            }else{
                // 包含 tmk 的筛选
                $where .= " AND s.staffer_id IN ( SELECT p.staffer_id FROM gmc_staffer_postbe AS p WHERE p.school_id = '{$school_id}' AND p.postbe_status = '1' AND postbe_iscrmuser = '1')";
            }
        }

        //20250612 添加 筛选负责人 关坚持
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $where .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or m.marketer_name like '%{$request['keyword']}%') ";
        }

        //20250612 添加了 是否负责招生人员的设置  (负责状态：  1 负责中  2 解除负责   0 全部    不传 或 空值默认 负责中)
        $having = " 1=1 ";
        if (isset($request['isfollowup']) && $request['isfollowup'] != '' && $request['isfollowup'] != '0') {
            $having .= " and isfollowup = '{$request['isfollowup']}'";
        }elseif($request['isfollowup'] != '0'){
            $having .= " and isfollowup = '1'";
        }

        // isfollowup  1 负责中  2 解除负责
        $sql = "
            SELECT m.marketer_id,s.staffer_cnname,s.staffer_enname,
                   if((select 2 from crm_marketer_followup as f where m.marketer_id = f.marketer_id and f.school_id = '{$school_id}'),2,1) as isfollowup
            FROM crm_marketer AS m, smc_staffer AS s  
            WHERE {$where} 
            having {$having}
        ";

		$marketerList = $this->DataControl->selectClear($sql);
		if($marketerList){
		    foreach ($marketerList as &$marketerListvar){
                $marketerListvar['marketer_name'] = $marketerListvar['staffer_cnname'].($marketerListvar['staffer_enname']?'/'.$marketerListvar['staffer_enname']:'');
            }
        }
		if ($marketerList){
			$list = $marketerList;
		}else{
			$list = array();
		}
		return $list;
	}

	/**
	 * @param $company_id
	 * @return array
	 *获取负责人列表
	 */
	function getReMarketer($company_id, $marketer_id,$school_id,$request=array())
	{

		$marketerList = $this->DataControl->selectClear("
            SELECT
                m.marketer_id,
                s.staffer_id,
                s.staffer_cnname,
                s.staffer_enname 
            FROM
                crm_marketer AS m
                LEFT JOIN smc_staffer AS s ON m.staffer_id = s.staffer_id
                LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = s.staffer_id 
            WHERE
                m.company_id = '{$company_id}' 
                AND s.staffer_leave = 0 
                AND p.school_id = '{$school_id}' 
                and (s.staffer_native = 0 or s.staffer_native = 3)
            GROUP BY
                p.staffer_id");
		if($marketerList){
		    foreach ($marketerList as &$marketerListvar){
                $marketerListvar['marketer_name'] = $marketerListvar['staffer_cnname'].($marketerListvar['staffer_enname']?'/'.$marketerListvar['staffer_enname']:'');
            }
        }
		if ($marketerList){
			$list = $marketerList;
		}else{
			$list = array();
		}
		return $list;
	}

	/**
	 * @param $company_id
	 * @return array
	 *获取负责人列表  tmk
	 */
	function getTmkMarketerApi($company_id,$school_id)
	{
		$where = "m.staffer_id = s.staffer_id AND s.company_id = '{$company_id}'";
		$where .= " AND s.staffer_id IN ( SELECT p.staffer_id FROM gmc_staffer_postbe AS p WHERE p.school_id = '{$school_id}' AND p.postbe_status = '1' AND postbe_iscrmuser = '1' AND postbe_crmuserlevel = '2')";
        $where .= " AND s.staffer_leave = 0 ";

		$marketerList = $this->DataControl->selectClear("SELECT m.marketer_id,s.staffer_cnname,s.staffer_enname 
FROM crm_marketer AS m, smc_staffer AS s WHERE {$where}");

		if($marketerList){
		    foreach ($marketerList as &$marketerListvar){
                $marketerListvar['marketer_name'] = $marketerListvar['staffer_cnname'].($marketerListvar['staffer_enname']?'/'.$marketerListvar['staffer_enname']:'');
            }
        }
		if ($marketerList){
			$list = $marketerList;
		}else{
			$list = array();
		}
		return $list;
	}
	
	/**
	 * @param $company_id
	 * @return array
	 *获取未到访原因
	 */
	function getInviteGenreApi($company_id,$iscrm = 1)
	{
//		$where = "company_id={$company_id} ";
        $reasonList = array();
        $reasonList[0]['genre_id'] = '0';
        $reasonList[0]['genre_reason'] = '邀约-普通柜询';
        $reasonList[1]['genre_id'] = '1';
        $reasonList[1]['genre_reason'] = '邀约-能力测试';
        if($iscrm == '1') {
            $reasonList[2]['genre_id'] = '2';
            $reasonList[2]['genre_reason'] = '来访-推带到访';
            $reasonList[3]['genre_id'] = '3';
            $reasonList[3]['genre_reason'] = '来访-主动到访';
        }
		$list = $reasonList;
		return $list;
	}
	/**
	 * @param $company_id
	 * @return array
	 *获取未到访原因
	 */
	function getIsvisterReasonList($company_id)
	{
		$where = "company_id={$company_id} ";

		$reasonList = $this->DataControl->getFieldquery('crm_code_isvisitreason', 'reason_code,reason_note', $where);

		$list = $reasonList;
		return $list;
	}
	
	/**
	 * @param $company_id
	 * @return array
	 *获取学校的班级
	 */
	function getSchoolClassList($school_id, $company_id)
	{
		
		$where = "school_id={$school_id} and company_id={$company_id}";
		$schoolList = $this->DataControl->getFieldquery('smc_class', 'class_id,class_cnname', $where,' limit 0,100');
		$list = $schoolList;
		return $list;
	}

	
	/**
	 * @param $family_id
	 * @return array
	 * 删除客户的亲属关系记录
	 */
	function deleteFamilyRelation($family_id)
	{
		if ($this->DataControl->delData('crm_client_family', "family_id={$family_id}")) {
			$res = true;
		} else {
			$res = false;
		}
		return $res;
	}
	
	/**
	 * @param $family_id
	 * @return array
	 * 删除客户的意向课程记录
	 */
	function deleteIntentionSource($intention_id)
	{
        $data = array();
        $data['intention_is_delete'] = 1;
        $data['intention_updatetime'] = time();
        if ($this->DataControl->updateData("crm_client_intention","intention_id='{$intention_id}'",$data)){
            $res = true;
        } else {
			$res = false;
		}
        return $res;

	    //之前的逻辑
//		if ($this->DataControl->delData("crm_client_intention", "intention_id={$intention_id}")) {
//			$res = true;
//		} else {
//			$res = false;
//		}
//		return $res;
	}

	
	/**
	 *
	 * @return array|bool
	 * 获取亲属关系
	 */
	function getFamilyRelation(){
		
		$familylist = $this->DataControl->getFieldquery('crm_code_familyrelation','familyrelation_id,familyrelation_code,familyrelation_name','1');
		if($familylist)
		{
			 $list = $familylist;
		}else{
			$list = array();
		}
		
		return $list;
		
	}

	/**
	 * @return array|bool
	 * 获取客户标签
	 */
	function getLabelApi($company_id, $client_id, $paramArray)
	{
		$labelList = $this->DataControl->selectClear("SELECT label_id,label_name,label_usenum FROM crm_client_label WHERE company_id = '{$company_id}' ");
        if($labelList){
            $label_list = array();
            $sort = array();
            foreach ($labelList as $key => $value) {
//                $tagnum = $this->DataControl->selectOne("SELECT COUNT(client_tag) as num FROM crm_client
//                                                            WHERE company_id = '{$company_id}' AND client_tag <> '' AND FIND_IN_SET('{$value['label_name']}', client_tag)");
                if($value['label_usenum'] > 0){
                    $label_list[$key]['label_id'] = $value['label_id'];
                    $label_list[$key]['label_name'] = $value['label_name'];
                    $label_list[$key]['num'] = $value['label_usenum'];
                    $sort[] = $value['label_usenum'];

                    $tag = $this->DataControl->selectOne("SELECT client_tag FROM crm_client WHERE client_id = '{$client_id}' AND FIND_IN_SET('{$value['label_name']}', client_tag)");
                    if($tag){
                        $label_list[$key]['is_select'] = true;
                    }
                }
            }
            $labelList = $label_list;
            array_multisort($sort, SORT_DESC, $labelList);
        }else{
            $labelList = array();
        }

        if(isset($paramArray['labeltype_type']) && $paramArray['labeltype_type'] != ''){
            $datawhere = " and labeltype_type like '%{$paramArray['labeltype_type']}%'  ";
        }else{
            $datawhere = " and labeltype_type like '%0%' ";
        }
		$labeltypeList = $this->DataControl->selectClear("SELECT labeltype_id,labeltype_name FROM crm_client_labeltype WHERE company_id = '{$company_id}' {$datawhere} ");
		if($labeltypeList){
			 foreach($labeltypeList as &$val){
				 $label = $this->DataControl->selectClear("SELECT label_id,label_name FROM crm_client_label WHERE labeltype_id = '{$val['labeltype_id']}'");
				 if($label){
				     foreach ($label as &$item) {
                         $tag = $this->DataControl->selectOne("SELECT client_tag FROM crm_client WHERE client_id = '{$client_id}' AND FIND_IN_SET('{$item['label_name']}', client_tag)");
                         if($tag){
                             $item['is_select'] = true;
                         }
                     }
                 }else{
                     $label = array();
                 }
				 $val['label'] = $label;
			 }
		}else{
			$labeltypeList = array();
		}

		$data = array();
		$data['label'] = $labelList ? $labelList : array();
		$data['list'] = $labeltypeList;

		return $data;
	}

	/**
	 * @return array|bool
	 * 获取地推人员
	 */
	function getGroundPromotionApi($paramArray)
	{
        $datawhere = "p.company_id = '{$paramArray['company_id']}'";
//        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
//            $datawhere .= " and (p.school_id = '{$paramArray['school_id']}' OR p.school_id = '0')";
//        }

        //地推工号
        if (isset($paramArray['promotion_jobnumber']) && $paramArray['promotion_jobnumber'] != '') {
            $datawhere .= " and (p.promotion_jobnumber like '%{$paramArray['promotion_jobnumber']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT p.promotion_id,p.promotion_name,p.promotion_jobnumber,p.promotion_mobile
                FROM crm_ground_promotion as p 
                WHERE {$datawhere} AND EXISTS (SELECT o.promotion_id FROM crm_ground_promotion_open AS o WHERE o.promotion_id = p.promotion_id AND o.school_id = '{$paramArray['school_id']}' AND o.open_status = 1) 
                ORDER BY p.promotion_id DESC LIMIT {$pagestart},{$num}";

		$dataList = $this->DataControl->selectClear($sql);
        if(!$dataList){
            $dataList = array();
        }

		$data = array();
		$data['list'] = $dataList;

		return $data;
	}

    /**
     * 意向星级列表
     * @param $company_id
     */
	public function getIntentionLevelList($company_id)
    {
        $list = $this->DataControl->selectClear("
        SELECT i.intentlevel_starnum,i.intentlevel_remark,i.intentlevel_trackday,i.intentlevel_warningday,i.intentlevel_describe 
        FROM gmc_code_intentlevel i 
        WHERE i.company_id = '{$company_id}' 
        ORDER BY i.intentlevel_starnum ASC 
        ");
        if(!empty($list)) {
            foreach($list as $key => $value)
            {
                $list[$key]['intentlevel_starnum'] = (int)$value['intentlevel_starnum'];

            }

        }
        $list = is_array($list)?$list:array();
        if($list){
            $this->error = 0;
            $this->errortip = "意向星级列表获取成功";
            $this->result = $list;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无数据";
            $this->result = [];
            return false;
        }


    }


}