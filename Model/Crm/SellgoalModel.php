<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class SellgoalModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    //招生来源列表
    function getFrommediaApi($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.frommedia_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.frommedia_id,
                p.frommedia_name,
                p.frommedia_remk
            FROM
                crm_code_frommedia AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY p.frommedia_id DESC ";

        $postList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.frommedia_id)
            FROM
                crm_code_frommedia AS p
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('frommedia_name ', 'frommedia_remk');
        $fieldname = $this->LgArraySwitch(array('来源名称', '来源备注'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无招生渠道信息", 'result' => $result);
        }

        return $res;
    }

    //招生目标 -- 招生目标管理
    function goalList($paramArray)
    {
        $datawhere = " 1 ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (g.goal_name like '%{$paramArray['keyword']}%' or m.marketer_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['goal_starttime']) && $paramArray['goal_starttime'] != '' && isset($paramArray['goal_endtime']) && $paramArray['goal_endtime'] != '') {
            $datawhere .= " and g.goal_starttime <= '{$paramArray['goal_endtime']}' and g.goal_endtime >= '{$paramArray['goal_starttime']}'";
        } elseif (isset($paramArray['goal_starttime']) && $paramArray['goal_starttime'] != '') {
            $datawhere .= " and g.goal_endtime >= '{$paramArray['goal_starttime']}'";
        } elseif (isset($paramArray['goal_endtime']) && $paramArray['goal_endtime'] != '') {
            $datawhere .= " and g.goal_starttime <= '{$paramArray['goal_endtime']}'";
        }

        //学校 筛选
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and g.school_id = '{$paramArray['school_id']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and g.marketer_id = '{$paramArray['found_marketer_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(g.goal_id) as datanum
                FROM crm_sell_goal as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere}";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }

        if (isset($paramArray['orderby']) && $paramArray['orderby'] != '') {
            $orderby = $paramArray['orderby'];
        } else {
            $orderby = " g.goal_id ";
        }
        if (isset($paramArray['sort']) && $paramArray['sort'] != '') {
            $sort = $paramArray['sort'];
        } else {
            $sort = "DESC";
        }
        $orderby = "ORDER BY {$orderby} {$sort}";

        $sqlfields = " g.goal_id,g.goal_name,g.marketer_id,g.goal_starttime,g.goal_endtime,m.marketer_name,g.goal_mintrack,g.goal_normtrack,g.goal_mininvite,g.goal_norminvite,g.goal_minofficial,g.goal_normofficial,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_goal as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere} {$orderby}";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $marketerList = $this->DataControl->selectClear("SELECT m.marketer_name,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname from crm_sell_goalcheck AS c 
                                                             LEFT JOIN crm_marketer AS m ON c.marketer_id = m.marketer_id
                                                             WHERE c.goal_id = '{$val['goal_id']}'");
                if (is_array($marketerList)) {
                    $marketerListstr = '';
                    foreach ($marketerList as $marketerVar) {
                        $marketerListstr .= $marketerVar['marketer_name'] . ((isset($marketerVar['staffer_enname']) && $marketerVar['staffer_enname'] != '') ? '-' . $marketerVar['staffer_enname'] : '') . ',';
                    }
                    $marketerListstr = substr($marketerListstr, 0, -1);
                } else {
                    $marketerListstr = '';
                }
                $val['participant'] = trim($marketerListstr, ',');
                $val['marketer_name'] = $val['marketer_name'] == null ? '' : $val['marketer_name'] . ((isset($val['staffer_enname']) && $val['staffer_enname'] != '') ? '-' . $val['staffer_enname'] : '');
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['goal_name'] = $dateexcelvar['goal_name'] == null ? '' : $dateexcelvar['goal_name'];
                    $datearray['goal_mintrack'] = $dateexcelvar['goal_mintrack'];
                    $datearray['goal_normtrack'] = $dateexcelvar['goal_normtrack'];
                    $datearray['goal_mininvite'] = $dateexcelvar['goal_mininvite'];
                    $datearray['goal_norminvite'] = $dateexcelvar['goal_norminvite'];
                    $datearray['goal_minofficial'] = $dateexcelvar['goal_minofficial'];
                    $datearray['goal_normofficial'] = $dateexcelvar['goal_normofficial'];
                    $datearray['goal_starttime'] = $dateexcelvar['goal_starttime'];
                    $datearray['goal_endtime'] = $dateexcelvar['goal_endtime'];
                    $datearray['participant'] = $dateexcelvar['participant'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'] == null ? '' : $dateexcelvar['marketer_name'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$paramArray['school_id']}'");
            $excelheader = $this->LgArraySwitch(array('招生目标名称', '跟踪小目标', '跟踪达标', '柜询/试听小目标', '柜询/试听达标', '转正小目标', '转正达标', '开始执行时间', '计划结束时间', '目标参与人', '创建人'));
            $excelfileds = array('goal_name', 'goal_mintrack', 'goal_normtrack', 'goal_mininvite', 'goal_norminvite', 'goal_minofficial', 'goal_normofficial', 'goal_starttime', 'goal_endtime', 'participant', 'marketer_name');

            $fielname = $this->LgStringSwitch("招生目标管理总表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $goalList = $this->DataControl->selectClear($sql);
        }

        if (is_array($goalList)) {
            $list = array();
            foreach ($goalList as $goalkey => $goalVar) {
                $list[$goalkey]['goal_id'] = $goalVar['goal_id'];
                $list[$goalkey]['goal_name'] = $goalVar['goal_name'] == null ? '' : $goalVar['goal_name'];
                $list[$goalkey]['goal_mintrack'] = $goalVar['goal_mintrack'];
                $list[$goalkey]['goal_normtrack'] = $goalVar['goal_normtrack'];
                $list[$goalkey]['goal_mininvite'] = $goalVar['goal_mininvite'];
                $list[$goalkey]['goal_norminvite'] = $goalVar['goal_norminvite'];
                $list[$goalkey]['goal_minofficial'] = $goalVar['goal_minofficial'];
                $list[$goalkey]['goal_normofficial'] = $goalVar['goal_normofficial'];
                $list[$goalkey]['goal_starttime'] = $goalVar['goal_starttime'];
                $list[$goalkey]['goal_endtime'] = $goalVar['goal_endtime'];

                $marketerList = $this->DataControl->selectClear("SELECT m.marketer_name,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname from crm_sell_goalcheck AS c 
                                                             LEFT JOIN crm_marketer AS m ON c.marketer_id = m.marketer_id
                                                             WHERE c.goal_id = '{$goalVar['goal_id']}'");
                if (is_array($marketerList)) {
                    $marketerListstr = '';
                    foreach ($marketerList as $marketerVar) {
                        $marketerListstr .= $marketerVar['marketer_name'] . ((isset($marketerVar['staffer_enname']) && $marketerVar['staffer_enname'] != '') ? '-' . $marketerVar['staffer_enname'] : '') . ',';
                    }
                    $marketerListstr = substr($marketerListstr, 0, -1);
                } else {
                    $marketerListstr = '';
                }

                $list[$goalkey]['participant'] = trim($marketerListstr, ',');
                $list[$goalkey]['marketer_name'] = $goalVar['marketer_name'] == null ? '' : $goalVar['marketer_name'] . ((isset($goalVar['staffer_enname']) && $goalVar['staffer_enname'] != '') ? '-' . $goalVar['staffer_enname'] : '');
            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //招生目标 -- 某个目标的详细情况
    function goalOneApi($paramArray)
    {
        $datawhere = " 1 and g.goal_id = '{$paramArray['goal_id']}' and g.school_id = '{$paramArray['school_id']}' ";
        $sqlfields = " g.goal_id,g.goal_name,g.marketer_id,m.marketer_name,g.goal_starttime,g.goal_endtime,g.goal_mintrack,g.goal_normtrack,g.goal_mininvite,g.goal_norminvite,g.goal_minofficial,g.goal_normofficial,g.goal_maxtracknumber ";
        $sql = "SELECT  {$sqlfields},(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname
                FROM crm_sell_goal as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere}";
        $goalOne = $this->DataControl->selectOne($sql);
        $goalOne['marketer_name'] = $goalOne['marketer_name'] . ((isset($goalOne['staffer_enname']) && $goalOne['staffer_enname'] != '') ? '-' . $goalOne['staffer_enname'] : '');
        if (is_array($goalOne)) {
            $marketerList = $this->DataControl->selectClear("
                            SELECT m.marketer_name,m.marketer_id,
                            (SELECT s.staffer_sex from smc_staffer as s where s.staffer_id = m.staffer_id) as marketer_sex
                            ,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname
                            from crm_sell_goalcheck AS c
                            LEFT JOIN crm_marketer AS m ON c.marketer_id = m.marketer_id
                            WHERE c.goal_id = '{$goalOne['goal_id']}'");

            if (is_array($marketerList)) {
                $marketerListstr = '';
                $marketeridstr = '';
                $marketerSexstr = '';
                foreach ($marketerList as $marketerVar) {
                    $marketerListstr .= $marketerVar['marketer_name'] . ((isset($marketerVar['staffer_enname']) && $marketerVar['staffer_enname'] != '') ? '-' . $marketerVar['staffer_enname'] : '') . ',';
                    $marketerSexstr .= (($marketerVar['marketer_sex'] == '') ? $this->LgStringSwitch('保密') : $marketerVar['marketer_sex']) . ',';
                    $marketeridstr .= $marketerVar['marketer_id'] . ',';
                }
                $marketerListstr = substr($marketerListstr, 0, -1);
                $marketerSexstr = substr($marketerSexstr, 0, -1);
                $marketeridstr = substr($marketeridstr, 0, -1);
            } else {
                $marketerListstr = '';
                $marketeridstr = '';
            }
            $goalOne['participant'] = trim($marketerListstr, ',');
            $goalOne['participantsex'] = $marketerSexstr;
            $goalOne['participantid'] = $marketeridstr;
        }
        return $goalOne;
    }

    //招生目标 -- 参与目标考核的所有员工
    function goalCheckMarketerApi($paramArray)
    {
        $datawhere = " 1 and m.company_id = '{$paramArray['company_id']}' and p.school_id = '{$paramArray['school_id']}' and p.postbe_iscrmuser = 1 ";
        $sqlfields = " m.marketer_id,m.marketer_name,(SELECT s.staffer_sex from smc_staffer as s where s.staffer_id = m.staffer_id) as marketer_sex ";
        $sql = "SELECT {$sqlfields},(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname 
                FROM crm_marketer as m
                LEFT JOIN smc_staffer as s ON m.staffer_id = s.staffer_id 
                LEFT JOIN gmc_staffer_postbe as p on s.staffer_id = p.staffer_id and s.company_id = p.company_id
                WHERE {$datawhere} 
                GROUP BY m.marketer_id
                ORDER BY m.marketer_id DESC ";
        $marketerList = $this->DataControl->selectClear($sql);
        if ($marketerList) {
            foreach ($marketerList as &$marketerVar) {
                $marketerVar['marketer_name'] = $marketerVar['marketer_name'] . ((isset($marketerVar['staffer_enname']) && $marketerVar['staffer_enname'] != '') ? '-' . $marketerVar['staffer_enname'] : '');
            }
        }
        return $marketerList;
    }

    //招生目标 -- 某目标参与成员的线索转正数
    function goalFinishStatusApi($paramArray)
    {
        $datawhere = " 1 and g.goal_id = '{$paramArray['goal_id']}' and s.school_id = '{$paramArray['school_id']}' ";
        //所有参与这个目标的人
        $sqlfields = " g.marketer_id,m.marketer_name,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname,
                (SELECT count(c.client_id) from crm_client_conversionlog as c 
                left join crm_client_schoolenter as s ON c.client_id = s.client_id 
                where m.marketer_id=c.marketer_id and FROM_UNIXTIME(c.conversionlog_time,'%Y-%m-%d') >= s.goal_starttime and FROM_UNIXTIME(c.conversionlog_time,'%Y-%m-%d') <= s.goal_endtime and s.company_id = '{$paramArray['company_id']}' and s.school_id = '{$paramArray['school_id']}'   ) as conversionnum ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_goalcheck as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                LEFT JOIN crm_sell_goal as s ON g.goal_id = s.goal_id
                WHERE {$datawhere} ORDER BY g.marketer_id DESC ";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$dataVar) {
                $dataVar['marketer_name'] = $dataVar['marketer_name'] . ((isset($dataVar['staffer_enname']) && $dataVar['staffer_enname'] != '') ? '-' . $dataVar['staffer_enname'] : '');
            }
        }
        return $dataList;
    }

    //招生目标 -- 某目标参与成员的跟踪柜询完成率
    function goalFinishRateApi11($paramArray)
    {
        $datawhere = " 1 and g.goal_id = '{$paramArray['goal_id']}' ";

        //筛选 -- 跟踪人
        if (isset($paramArray['from_marketer_id']) && $paramArray['from_marketer_id'] != '') {
            $datawhere .= " and g.marketer_id = '{$paramArray['from_marketer_id']}'";
        }

        //所有参与这个目标的人
        $sqlfields = " g.marketer_id,m.marketer_name,s.goal_starttime,s.goal_endtime,s.goal_normtrack,s.goal_norminvite ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_goalcheck as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                LEFT JOIN crm_sell_goal as s ON g.goal_id = s.goal_id
                WHERE {$datawhere} ORDER BY g.marketer_id DESC ";
        $dataList = $this->DataControl->selectClear($sql);

        if (is_array($dataList)) {
            foreach ($dataList as &$dataVar) {
                //跟踪人次
                $sql = "SELECT  t.client_id
                        FROM crm_client_track as t
                        WHERE t.marketer_id = '{$dataVar['marketer_id']}' and t.track_followmode <> '-1' and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$dataVar['goal_starttime']}' and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$dataVar['goal_endtime']}'
                        GROUP BY t.client_id
                        ORDER BY t.client_id DESC ";
                $trackOne = $this->DataControl->selectClear($sql);
                $tracknum = count($trackOne);
                //转化率
                if ($dataVar['goal_normtrack'] != '' && $dataVar['goal_normtrack'] != '0') {
                    $dataVar['tracknum'] = sprintf("%.2f", $tracknum / $dataVar['goal_normtrack']) * 100;
                    $dataVar['nottracknum'] = 100 - $dataVar['tracknum'];
                } else {
                    $dataVar['tracknum'] = 0;
                    $dataVar['nottracknum'] = 100;
                }

                //柜询人次
                $sql = "SELECT  count(t.client_id) as invitenum
                        FROM crm_client_track as t
                        WHERE t.marketer_id = '{$dataVar['marketer_id']}' and (t.track_followmode = '1' or t.track_followmode = '2') and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$dataVar['goal_starttime']}' and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$dataVar['goal_endtime']}'
                        ORDER BY t.client_id";
                $inviteOne = $this->DataControl->selectOne($sql);

                //转化率
                if ($dataVar['goal_norminvite'] != '' && $dataVar['goal_norminvite'] != '0') {
                    $dataVar['invitenum'] = sprintf("%.2f", $inviteOne['invitenum'] / $dataVar['goal_norminvite']) * 100;
                    $dataVar['notinvitenum'] = 100 - $dataVar['invitenum'];
                } else {
                    $dataVar['invitenum'] = 0;
                    $dataVar['notinvitenum'] = 100;
                }
            }
        }
        return $dataList;
    }

    function goalFinishRateApi($paramArray)
    {
        $sqlfields = " s.goal_starttime,s.goal_endtime,s.goal_normtrack,s.goal_norminvite  ";
        $datawhere = " 1 and s.goal_id = '{$paramArray['goal_id']}' ";
        $sql = " SELECT {$sqlfields}
                    FROM crm_sell_goal as s 
                    WHERE {$datawhere} and s.goal_starttime <= now()";
        $dataOne = $this->DataControl->selectOne($sql);

        //筛选 -- 跟踪人
        if (isset($paramArray['from_marketer_id']) && $paramArray['from_marketer_id'] != '') {
            //跟踪人次
            $sql = "SELECT  t.client_id
                    FROM crm_client_track as t
                    LEFT JOIN crm_client_schoolenter as s ON t.client_id = s.client_id 
                    WHERE t.marketer_id = '{$paramArray['from_marketer_id']}'
                    and t.track_followmode <> '-1'
                    and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$dataOne['goal_starttime']}'
                    and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$dataOne['goal_endtime']}'
                    and s.company_id = '{$paramArray['company_id']}'
                    and s.school_id = '{$paramArray['school_id']}' 
                    ORDER BY t.client_id";
//            GROUP BY t.client_id

            $trackOne = $this->DataControl->selectClear($sql);
            if (is_array($trackOne)) {
                $tracknum = count($trackOne);
            } else {
                $tracknum = 0;
            }
            //转化率
            if ($dataOne['goal_normtrack'] != '' && $dataOne['goal_normtrack'] != '0') {
                $dataOne['tracknum'] = sprintf("%.2f", $tracknum / $dataOne['goal_normtrack']) * 100;
                if ($dataOne['tracknum'] >= 100) {
                    $dataOne['nottracknum'] = 0;
                } else {
                    $dataOne['nottracknum'] = 100 - $dataOne['tracknum'];
                }
            } else {
                $dataOne['tracknum'] = 0;
                $dataOne['nottracknum'] = 100;
            }

            //柜询人次
            $sql = "SELECT t.client_id
                    FROM crm_client_track as t
                    LEFT JOIN crm_client_schoolenter as s ON t.client_id = s.client_id 
                    WHERE t.marketer_id = '{$paramArray['from_marketer_id']}'
                    and (t.track_followmode = '1' or t.track_followmode = '2')
                    and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$dataOne['goal_starttime']}'
                    and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$dataOne['goal_endtime']}'
                    and s.company_id = '{$paramArray['company_id']}'
                    and s.school_id = '{$paramArray['school_id']}'
                    ORDER BY t.client_id";
//            GROUP BY t.client_id
            $inviteOne = $this->DataControl->selectClear($sql);
            if (is_array($inviteOne)) {
                $invitenum = count($inviteOne);
            } else {
                $invitenum = 0;
            }
            //转化率
            if ($dataOne['goal_norminvite'] != '' && $dataOne['goal_norminvite'] != '0') {
                $dataOne['invitenum'] = sprintf("%.2f", $invitenum / $dataOne['goal_norminvite']) * 100;
                if ($dataOne['invitenum'] >= 100) {
                    $dataOne['notinvitenum'] = 0;
                } else {
                    $dataOne['notinvitenum'] = 100 - $dataOne['invitenum'];
                }
            } else {
                $dataOne['invitenum'] = 0;
                $dataOne['notinvitenum'] = 100;
            }

            return $dataOne;
        } else {
            $datawhere = " 1 and g.goal_id = '{$paramArray['goal_id']}' ";

            //所有参与这个目标的人
            $sql = "SELECT  g.marketer_id,m.marketer_name,s.goal_starttime,s.goal_endtime,s.goal_normtrack,s.goal_norminvite
                FROM crm_sell_goalcheck as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                LEFT JOIN crm_sell_goal as s ON g.goal_id = s.goal_id
                WHERE {$datawhere}";
            $dataList = $this->DataControl->selectClear($sql);

            $trackAllnum = 0;
            $inviteAllnum = 0;
            if (is_array($dataList)) {
                foreach ($dataList as &$dataVar) {
                    //跟踪人次
                    $sql = "SELECT  t.client_id
                        FROM crm_client_track as t 
                        LEFT JOIN crm_client_schoolenter as s ON t.client_id = s.client_id 
                        WHERE t.marketer_id = '{$dataVar['marketer_id']}'
                        and t.track_followmode <> '-1'
                        and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$dataVar['goal_starttime']}'
                        and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$dataVar['goal_endtime']}'
                        and s.company_id = '{$paramArray['company_id']}'
                        and s.school_id = '{$paramArray['school_id']}' 
                        ORDER BY t.client_id";
//                    GROUP BY t.client_id
                    $trackOne = $this->DataControl->selectClear($sql);
                    if (is_array($trackOne)) {
                        $tracknum = count($trackOne);
                    } else {
                        $tracknum = 0;
                    }
                    //总次数
                    $trackAllnum += $tracknum;

                    //柜询人次
                    $sql = "SELECT  t.client_id as invitenum
                        FROM crm_client_track as t 
                        LEFT JOIN crm_client_schoolenter as s ON t.client_id = s.client_id 
                        WHERE t.marketer_id = '{$dataVar['marketer_id']}'
                        and (t.track_followmode = '1' or t.track_followmode = '2')
                        and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$dataVar['goal_starttime']}'
                        and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$dataVar['goal_endtime']}'
                        and s.company_id = '{$paramArray['company_id']}'
                        and s.school_id = '{$paramArray['school_id']}' 
                        ORDER BY t.client_id";
//                    GROUP BY t.client_id

                    $inviteOne = $this->DataControl->selectClear($sql);
                    if (is_array($inviteOne)) {
                        $invitenum = count($inviteOne);
                    } else {
                        $invitenum = 0;
                    }
                    //总次数
                    $inviteAllnum += $invitenum;
                }
            }
            //转化率
            if ($dataOne['goal_normtrack'] != '' && $dataOne['goal_normtrack'] != '0') {
                $dataOne['tracknum'] = sprintf("%.2f", $trackAllnum / $dataOne['goal_normtrack']) * 100;
                if ($dataOne['tracknum'] >= 100) {
                    $dataOne['nottracknum'] = 0;
                } else {
                    $dataOne['nottracknum'] = 100 - $dataOne['tracknum'];
                }
            } else {
                $dataOne['tracknum'] = 0;
                $dataOne['nottracknum'] = 100;
            }

            //转化率
            if ($dataOne['goal_norminvite'] != '' && $dataOne['goal_norminvite'] != '0') {
                $dataOne['invitenum'] = sprintf("%.2f", $inviteAllnum / $dataOne['goal_norminvite']) * 100;
                if ($dataOne['invitenum'] >= 100) {
                    $dataOne['notinvitenum'] = 0;
                } else {
                    $dataOne['notinvitenum'] = 100 - $dataOne['invitenum'];
                }
            } else {
                $dataOne['invitenum'] = 0;
                $dataOne['notinvitenum'] = 100;
            }
            return $dataOne;
        }

    }

    //招生目标 -- 添加招生目标
    function addGoalAction($paramArray)
    {
        $data = array();
        $data['marketer_id'] = $paramArray['marketer_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['goal_name'] = $paramArray['goal_name'];
        $data['goal_starttime'] = $paramArray['goal_starttime'];
        $data['goal_endtime'] = $paramArray['goal_endtime'];
        $data['goal_normtrack'] = $paramArray['goal_normtrack'];
        $data['goal_mintrack'] = $paramArray['goal_mintrack'];
        $data['goal_norminvite'] = $paramArray['goal_norminvite'];
        $data['goal_mininvite'] = $paramArray['goal_mininvite'];
        $data['goal_normofficial'] = $paramArray['goal_normofficial'];
        $data['goal_minofficial'] = $paramArray['goal_minofficial'];
        $data['goal_maxtracknumber'] = $paramArray['goal_maxtracknumber'];
        $data['goal_createtime'] = time();
        $goal_id = $this->DataControl->insertData('crm_sell_goal', $data);
        if ($goal_id) {
            if (is_array($paramArray['checkMarketer'])) {
                foreach ($paramArray['checkMarketer'] as $checkMarketerVar) {
                    $dataOne = array();
                    $dataOne['goal_id'] = $goal_id;
                    $dataOne['marketer_id'] = $checkMarketerVar;
                    $dataOne['check_createtime'] = time();
                    $this->DataControl->insertData('crm_sell_goalcheck', $dataOne);
                }
            }
            return true;
        } else {
            return false;
        }

    }

    //招生目标 -- 修改招生目标
    function updateGoalAction($paramArray, $account_class = '0')
    {
        $data = array();
        $data['goal_name'] = $paramArray['goal_name'];
        if (isset($paramArray['goal_starttime'])) {
            $data['goal_starttime'] = $paramArray['goal_starttime'];
        }
        if (isset($paramArray['goal_endtime'])) {
            $data['goal_endtime'] = $paramArray['goal_endtime'];
        }
        if (isset($paramArray['goal_normtrack'])) {
            $data['goal_normtrack'] = $paramArray['goal_normtrack'];
        }
        if (isset($paramArray['goal_mintrack'])) {
            $data['goal_mintrack'] = $paramArray['goal_mintrack'];
        }
        if (isset($paramArray['goal_norminvite'])) {
            $data['goal_norminvite'] = $paramArray['goal_norminvite'];
        }
        if (isset($paramArray['goal_mininvite'])) {
            $data['goal_mininvite'] = $paramArray['goal_mininvite'];
        }
        if (isset($paramArray['goal_normofficial'])) {
            $data['goal_normofficial'] = $paramArray['goal_normofficial'];
        }
        if (isset($paramArray['goal_minofficial'])) {
            $data['goal_minofficial'] = $paramArray['goal_minofficial'];
        }
        if (isset($paramArray['goal_maxtracknumber'])) {
            $data['goal_maxtracknumber'] = $paramArray['goal_maxtracknumber'];
        }
        if (is_array($paramArray['checkMarketer'])) {
            $this->DataControl->delData('crm_sell_goalcheck', "goal_id = '{$paramArray['goal_id']}'  ");
            foreach ($paramArray['checkMarketer'] as $checkMarketerVar) {
                $dataOne = array();
                $dataOne['goal_id'] = $paramArray['goal_id'];
                $dataOne['marketer_id'] = $checkMarketerVar;
                $dataOne['check_createtime'] = time();
                $this->DataControl->insertData('crm_sell_goalcheck', $dataOne);
            }
        }
        $data['goal_upatetime'] = time();

        if ($account_class == '1') {
            $istrue = $this->DataControl->updateData("crm_sell_goal", "goal_id = '{$paramArray['goal_id']}' and school_id = '{$paramArray['school_id']}' ", $data);
        } else {
            $istrue = $this->DataControl->updateData("crm_sell_goal", "goal_id = '{$paramArray['goal_id']}' and marketer_id = '{$paramArray['marketer_id']}' and school_id = '{$paramArray['school_id']}'", $data);
        }
        if ($istrue) {
            return $data;
        } else {
            return false;
        }
    }

    //招生目标 -- 删除招生目标
    function delGoalAction($goal_id, $marketer_id, $account_class = '0')
    {
        if ($account_class == '1') {
            $istrue = $this->DataControl->delData("crm_sell_goal", "goal_id = '{$goal_id}' ");
        } else {
            $istrue = $this->DataControl->delData('crm_sell_goal', "goal_id='{$goal_id}' and marketer_id='{$marketer_id}' ");
        }

        if ($istrue) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }

    //招生目标 -- 目标创建人管理
    function goalFounderApi($paramArray)
    {
        $datawhere = " 1 and g.school_id = '{$paramArray['school_id']}'  ";
        $sql = "SELECT g.marketer_id,m.marketer_name,f.staffer_enname
                FROM crm_sell_goal as g
                LEFT JOIN crm_marketer as m ON g.marketer_id=m.marketer_id
                LEFT JOIN smc_staffer as f On  f.staffer_id =m.staffer_id
                WHERE {$datawhere} and m.marketer_id > 0
                GROUP BY g.marketer_id";
        $marketerList = $this->DataControl->selectClear($sql);
        return $marketerList;
    }

    //招生目标 -- >> 我的招生目标
    function goalMy($paramArray)
    {
        $datawhere = " 1 ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (g.goal_name like '%{$paramArray['keyword']}%' or m.marketer_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['goal_starttime']) && $paramArray['goal_starttime'] != '' && isset($paramArray['goal_endtime']) && $paramArray['goal_endtime'] != '') {
            $datawhere .= " and g.goal_starttime <= '{$paramArray['goal_endtime']}' and g.goal_endtime >= '{$paramArray['goal_starttime']}'";
        } elseif (isset($paramArray['goal_starttime']) && $paramArray['goal_starttime'] != '') {
            $datawhere .= " and g.goal_endtime >= '{$paramArray['goal_starttime']}'";
        } elseif (isset($paramArray['goal_endtime']) && $paramArray['goal_endtime'] != '') {
            $datawhere .= " and g.goal_starttime <= '{$paramArray['goal_endtime']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and g.marketer_id = '{$paramArray['found_marketer_id']}'";
        }
        //来源人 -- 我的
        if (isset($paramArray['marketer_id']) && $paramArray['marketer_id'] != '') {
            $datawhere .= " and c.marketer_id = '{$paramArray['marketer_id']}'";
        }

        //对应学校
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and g.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(g.goal_id)  as datanum
                FROM crm_sell_goalcheck as c
                LEFT JOIN crm_sell_goal as g ON c.goal_id = g.goal_id
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere}
                GROUP BY c.goal_id ";
            $count = $this->DataControl->selectClear($sql);
            $count = count($count) + 0;
        } else {
            $count = '';
        }
        $sqlfields = " g.goal_id,g.goal_name,g.marketer_id,g.goal_starttime,g.goal_endtime,m.marketer_name,g.goal_mintrack,g.goal_mininvite,g.goal_minofficial ";
        $sql = "SELECT  {$sqlfields},(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname
                FROM crm_sell_goalcheck as c
                LEFT JOIN crm_sell_goal as g ON c.goal_id = g.goal_id
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere}
                GROUP BY c.goal_id 
                ORDER BY g.goal_id DESC";


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $marketerList = $this->DataControl->selectClear("SELECT m.marketer_name,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname from crm_sell_goalcheck AS c 
                                                             LEFT JOIN crm_marketer AS m ON c.marketer_id = m.marketer_id
                                                             WHERE c.goal_id = '{$val['goal_id']}'");
                if (is_array($marketerList)) {
                    $marketerListstr = '';
                    foreach ($marketerList as $marketerVar) {
                        $marketerListstr .= $marketerVar['marketer_name'] . ((isset($marketerVar['staffer_enname']) && $marketerVar['staffer_enname'] != '') ? '-' . $marketerVar['staffer_enname'] : '') . ',';
                    }
                    $marketerListstr = substr($marketerListstr, 0, -1);
                } else {
                    $marketerListstr = '';
                }
                $val['participant'] = trim($marketerListstr, ',');
                $val['marketer_name'] = $val['marketer_name'] == null ? '' : $val['marketer_name'];
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['goal_name'] = $dateexcelvar['goal_name'] == null ? '' : $dateexcelvar['goal_name'];
                    $datearray['goal_mintrack'] = $this->LgStringSwitch("跟踪最少人数" . $dateexcelvar['goal_mininvite'] . ",柜询/试听最少人数" . $dateexcelvar['goal_mintrack']);
                    $datearray['goal_starttime'] = $dateexcelvar['goal_starttime'];
                    $datearray['goal_endtime'] = $dateexcelvar['goal_endtime'];
                    $datearray['participant'] = $dateexcelvar['participant'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'] == null ? '' : $dateexcelvar['marketer_name'] . ((isset($dateexcelvar['staffer_enname']) && $dateexcelvar['staffer_enname'] != '') ? '-' . $dateexcelvar['staffer_enname'] : '');
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$paramArray['school_id']}'");
            $excelheader = $this->LgArraySwitch(array('招生目标名称', '考核指标', '开始执行时间', '计划结束时间', '目标参与人', '创建人'));
            $excelfileds = array('goal_name', 'goal_mintrack', 'goal_starttime', 'goal_endtime', 'participant', 'marketer_name');

            $fielname = $this->LgStringSwitch("个人招生目标管理总表");

            if ($paramArray['language_type'] == 'tw') {
                //model
                $Model = new \Model\jianfanModel();
                //转义
                $fielname = $Model->gb2312_big5($fielname);
                $schoolOne['school_cnname'] = $Model->gb2312_big5($schoolOne['school_cnname']);
            }

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $goalList = $this->DataControl->selectClear($sql);
        }

        if (is_array($goalList)) {
            $list = array();
            foreach ($goalList as $goalkey => $goalVar) {
                $list[$goalkey]['goal_id'] = $goalVar['goal_id'];
                $list[$goalkey]['goal_name'] = $goalVar['goal_name'] == null ? '' : $goalVar['goal_name'];
                $list[$goalkey]['goal_mintrack'] = $this->LgStringSwitch("跟踪最少人数" . $goalVar['goal_mininvite'] . ",柜询/试听最少人数" . $goalVar['goal_mintrack']);
                $list[$goalkey]['goal_starttime'] = $goalVar['goal_starttime'];
                $list[$goalkey]['goal_endtime'] = $goalVar['goal_endtime'];

                $marketerList = $this->DataControl->selectClear("SELECT m.marketer_name,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname from crm_sell_goalcheck AS c 
                                                             LEFT JOIN crm_marketer AS m ON c.marketer_id = m.marketer_id
                                                             WHERE c.goal_id = '{$goalVar['goal_id']}'");
                if (is_array($marketerList)) {
                    $marketerListstr = '';
                    foreach ($marketerList as $marketerVar) {
                        $marketerListstr .= $marketerVar['marketer_name'] . ((isset($marketerVar['staffer_enname']) && $marketerVar['staffer_enname'] != '') ? '-' . $marketerVar['staffer_enname'] : '') . ',';
                    }
                    $marketerListstr = substr($marketerListstr, 0, -1);
                } else {
                    $marketerListstr = '';
                }

                $list[$goalkey]['participant'] = trim($marketerListstr, ',');
                $list[$goalkey]['marketer_name'] = $goalVar['marketer_name'] == null ? '' : $goalVar['marketer_name'] . ((isset($goalVar['staffer_enname']) && $goalVar['staffer_enname'] != '') ? '-' . $goalVar['staffer_enname'] : '');
            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //招生目标 -- >> 我的目标创建人管理
    function goalFounderMyApi($paramArray)
    {
        $datawhere = " 1 and g.school_id = '{$paramArray['school_id']}' ";
        $sql = "SELECT g.marketer_id,m.marketer_name
                FROM crm_sell_goalcheck as c
                LEFT JOIN crm_sell_goal as g ON c.goal_id = g.goal_id
                LEFT JOIN crm_marketer as m ON g.marketer_id=m.marketer_id
                WHERE {$datawhere}
                GROUP BY c.goal_id,g.marketer_id";
        $marketerList = $this->DataControl->selectClear($sql);
        return $marketerList;
    }

    //招生活动管理  -- 活动课包 --- 前端用接口  有字段限制
    function activityPackageListApi($paramArray)
    {
        $sql="SELECT a.package_id,a.package_type,a.package_name,a.package_branch,a.package_outurl,a.package_outtype,a.package_appid 
                FROM crm_sell_activity_package as a 
                WHERE a.company_id = '{$paramArray['company_id']}' and a.package_remove = '0' and a.package_frontsee = '1'
                ORDER BY a.package_id DESC";
        $packageList = $this->DataControl->selectClear($sql);
        return $packageList;
    }
    //招生活动 -- >> 活动创建人管理
    function ActivityFounderApi($paramArray)
    {
        $datawhere = " 1 and a.activity_type = '0' and s.school_id = '{$paramArray['school_id']}'  ";
        $sql = "SELECT a.marketer_id,m.marketer_name,f.staffer_enname
                FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id=m.marketer_id
                LEFT JOIN crm_sell_activity_school as s ON a.activity_id = s.activity_id
                LEFT JOIN smc_staffer as f ON f.staffer_id = m.staffer_id
                WHERE {$datawhere}
                GROUP BY a.marketer_id";
        $marketerList = $this->DataControl->selectClear($sql);
        return $marketerList;
    }

    //招生活动 -- >> 活动模板
    function ActivitytempApi($paramArray)
    {
        if ($paramArray['activitytemp_class'] == '1') {
            if ($paramArray['language_type'] == 'tw') {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '1' and activitytemp_class = '1'  ORDER BY activitytemp_id ASC ";
            } else {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '0' and activitytemp_class = '1'  ORDER BY activitytemp_id ASC ";
            }
        } else {
            if ($paramArray['language_type'] == 'tw') {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '1' and activitytemp_class = '0'  ORDER BY activitytemp_id ASC ";
            } else {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '0' and activitytemp_class = '0'  ORDER BY activitytemp_id ASC ";
            }
        }
        $activitytempList = $this->DataControl->selectClear($sql);
        return $activitytempList;
    }

    //招生目标 -- >> 某个活动的信息
    function ActivitytempOneApi($paramArray)
    {
        $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", " * ", " activitytemp_id = '{$paramArray['activitytemp_id']}' ");
        return $activitytempOne;
    }

    //招生目标 -- >> 海报活动
    function getShareposterList($paramArray)
    {
        $datawhere = " 1 and a.activity_pattern = '2' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (a.activity_name like '%{$paramArray['keyword']}%' or m.marketer_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '' && isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $paramArray['activity_starttime'] = $paramArray['activity_starttime'] . " 00:00:00";
            $paramArray['activity_endtime'] = $paramArray['activity_endtime'] . " 23:59:59";
            $datawhere .= " and a.activity_starttime <= '{$paramArray['activity_endtime']}' and a.activity_endtime >= '{$paramArray['activity_starttime']}'";
        } elseif (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '') {
            $paramArray['activity_starttime'] = $paramArray['activity_starttime'] . " 00:00:00";
            $datawhere .= " and a.activity_endtime >= '{$paramArray['activity_starttime']}'";
        } elseif (isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $paramArray['activity_endtime'] = $paramArray['activity_endtime'] . " 23:59:59";
            $datawhere .= " and a.activity_starttime <= '{$paramArray['activity_endtime']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and (a.marketer_id = '{$paramArray['found_marketer_id']}' or  s.staffer_id  = '{$paramArray['found_marketer_id']}' )";
        }
        //活动来源  0 自己录入的  1 集团录入的
        if (isset($paramArray['activity_type']) && $paramArray['activity_type'] != '') {
            $datawhere .= " and a.activity_type = '{$paramArray['activity_type']}'";
        }
        //活动来源  0普招模式1验券模式
//        if(isset($paramArray['activity_pattern']) && $paramArray['activity_pattern'] != ''){
//            $datawhere .= " and a.activity_pattern = '{$paramArray['activity_pattern']}'";
//        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '') {
            $datawhere .= " and a.frommedia_name = '{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '') {
            $datawhere .= " and a.channel_id = '{$paramArray['channel_id']}'";
        }
        //学校 筛选
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and h.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(a.activity_id)  as datanum
                 FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE {$datawhere}";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }
        $sqlfields = " a.activity_id,a.activity_type,a.activity_pattern,a.activity_istemp,a.activity_tempurl,a.activitytemp_id,a.activity_sharedesc,a.activity_shareimg,a.activity_name,a.marketer_id,m.marketer_name,a.activity_starttime,a.activity_endtime,a.activity_type,s.staffer_cnname,h.school_id,a.frommedia_name,a.channel_id,(select c.channel_name from crm_code_channel as c where a.channel_id = c.channel_id) as channel_name,s.staffer_enname ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE {$datawhere}
                ORDER BY activity_id DESC";


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                //发布人
                if ($val['activity_type'] == '1') {
                    $val['marketer_name'] = $val['staffer_cnname'] == null ? '' : $val['staffer_cnname'] . ((isset($val['staffer_enname']) && $val['staffer_enname'] != '') ? '-' . $val['staffer_enname'] : '');
                } else {
                    $val['marketer_name'] = $val['marketer_name'] == null ? '' : $val['marketer_name'] . ((isset($val['staffer_enname']) && $val['staffer_enname'] != '') ? '-' . $val['staffer_enname'] : '');
                }
                $val['activity_typename'] = $val['activity_type'] == '0' ? $this->LgStringSwitch('学校') : $this->LgStringSwitch('集团');
                if ($val['activity_pattern'] == '0') {
                    $val['activity_pattern'] = $this->LgStringSwitch('普招活动');
                } elseif ($val['activity_pattern'] == '1') {
                    $val['activity_pattern'] = $this->LgStringSwitch('招生券活动');
                } elseif ($val['activity_pattern'] == '2') {
                    $val['activity_pattern'] = $this->LgStringSwitch('海报活动');
                }
                $val['activity_name'] = $val['activity_name'] == null ? '' : $val['activity_name'];
                $val['activity_starttime'] = $val['activity_starttime'] . $this->LgStringSwitch(" 到 ") . $val['activity_endtime'];
                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                              LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$val['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_tracestatus <> '-1'");
                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$val['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'
                                                             and c.marketer_id = '{$paramArray['marketer_id']}'
                                                             and c.marketer_id <> ''");

                $actnum = $actnum['num'];
                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $val['clientnum'] = $clientcount;
                //活动来源的意向客户
                $val['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $val['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $val['percentconversion'] = (sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100) . "%";
                } else {
                    $val['percentconversion'] = (0) . "%";
                }

                //二维码
                $shareurl = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$val['activity_id']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";
                $val['shareqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($shareurl);//二维码;
                $val['shareqrcodeurl'] = $shareurl;//二维码url;

                $personalshareurl = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$val['activity_id']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";
                $val['personalshareqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($personalshareurl);//个人二维码
                $val['personalshareqrcodeurl'] = $personalshareurl;//二维码url;

            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];//招生活动名称
                    $datearray['activity_starttime'] = $dateexcelvar['activity_starttime'];//活动时间
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];//有效名单数
                    $datearray['actnum'] = $dateexcelvar['actnum'];//活动意向客户
                    $datearray['officialnum'] = $dateexcelvar['officialnum'];//转正人数
                    $datearray['percentconversion'] = $dateexcelvar['percentconversion'];//转化率
                    $datearray['qrcodeurl'] = $dateexcelvar['shareqrcode'];//学校二维码地址
                    $datearray['personalqrcodeurl'] = $dateexcelvar['personalshareqrcode'];//个人二维码地址
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];//发布人
                    $datearray['activity_typename'] = $dateexcelvar['activity_typename'];//活动来源
                    $datearray['activity_pattern'] = $dateexcelvar['activity_pattern'];//招生活动模式
                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];//渠道类型
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道明细
//                    $datearray['QRcodeUrl'] = $dateexcelvar['QRcodeUrl'];//学校小程序地址
//                    $datearray['personalQRcodeUrl'] = $dateexcelvar['personalQRcodeUrl'];//个人小程序地址
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$paramArray['school_id']}'");
            $excelheader = $this->LgArraySwitch(array('招生活动名称', '活动时间', '有效名单数', '活动意向客户', '转正人数', '转化率', '学校二维码地址', '个人二维码地址', '发布人', '活动来源', '招生活动模式', '渠道类型', '渠道明细'));
            $excelfileds = array('activity_name', 'activity_starttime', 'clientnum', 'actnum', 'officialnum', 'percentconversion', 'qrcodeurl', 'personalqrcodeurl', 'marketer_name', 'activity_typename', 'activity_pattern', 'frommedia_name', 'channel_name');

            $fielname = $this->LgStringSwitch("招生活动管理列表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
        }

        if (is_array($dataList)) {
            $list = array();
            foreach ($dataList as $datakey => $dataVar) {
                $list[$datakey]['activity_id'] = $dataVar['activity_id'];
                $list[$datakey]['marketer_id'] = $dataVar['marketer_id'];
                $list[$datakey]['activity_type'] = $dataVar['activity_type'];
                $list[$datakey]['activity_typename'] = $dataVar['activity_type'] == '0' ? $this->LgStringSwitch('学校') : $this->LgStringSwitch('集团');
                if ($dataVar['activity_pattern'] == '0') {
                    $list[$datakey]['activity_pattern'] = $this->LgStringSwitch('普招活动');
                } elseif ($dataVar['activity_pattern'] == '1') {
                    $list[$datakey]['activity_pattern'] = $this->LgStringSwitch('招生券活动');
                }
                $list[$datakey]['activity_name'] = $dataVar['activity_name'] == null ? '' : $dataVar['activity_name'];
                $list[$datakey]['activity_starttime'] = $dataVar['activity_starttime'] . $this->LgStringSwitch(" 到 ") . $dataVar['activity_endtime'];
                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                              LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_tracestatus <> '-1'");
                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'
                                                             and c.marketer_id = '{$paramArray['marketer_id']}'
                                                             and c.marketer_id <> ''");

                $actnum = $actnum['num'];
                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $list[$datakey]['clientnum'] = $clientcount;
                //活动来源的意向客户
                $list[$datakey]['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $list[$datakey]['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $list[$datakey]['percentconversion'] = (sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100) . "%";
                } else {
                    $list[$datakey]['percentconversion'] = (0) . "%";
                }

                //二维码
                $shareurl = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$dataVar['activity_id']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";
                $list[$datakey]['shareqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($shareurl);//二维码;
                $list[$datakey]['shareqrcodeurl'] = $shareurl;//二维码url;

                //个人二维码
                $personalshareurl = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$dataVar['activity_id']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";
                $list[$datakey]['personalshareqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($personalshareurl);//个人二维码
                $list[$datakey]['personalshareqrcodeurl'] = $personalshareurl;//二维码url;

                //发布人
                if ($dataVar['activity_type'] == '1') {
                    $list[$datakey]['marketer_name'] = $dataVar['staffer_cnname'] == null ? '' : $dataVar['staffer_cnname'] . ((isset($dataVar['staffer_enname']) && $dataVar['staffer_enname'] != '') ? '-' . $dataVar['staffer_enname'] : '');
                } else {
                    $list[$datakey]['marketer_name'] = $dataVar['marketer_name'] == null ? '' : $dataVar['marketer_name'] . ((isset($dataVar['staffer_enname']) && $dataVar['staffer_enname'] != '') ? '-' . $dataVar['staffer_enname'] : '');
                }
                $list[$datakey]['frommedia_name'] = $dataVar['frommedia_name'];
                $list[$datakey]['channel_id'] = $dataVar['channel_id'];
                $list[$datakey]['channel_name'] = $dataVar['channel_name'];
            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //招生目标 -- >> 招生活动管理
    function sellActivity($paramArray)
    {
        $schoolOne = $this->DataControl->selectOne("select school_id,school_branch,school_cnname from smc_school where company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' ");

        $datawhere = "a.company_id = '{$paramArray['company_id']}' and ( a.activity_pattern = '0' or a.activity_pattern = '1') ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (a.activity_name like '%{$paramArray['keyword']}%' or m.marketer_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '' && isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $paramArray['activity_starttime'] = $paramArray['activity_starttime'] . " 00:00:00";
            $paramArray['activity_endtime'] = $paramArray['activity_endtime'] . " 23:59:59";
            $datawhere .= " and a.activity_starttime <= '{$paramArray['activity_endtime']}' and a.activity_endtime >= '{$paramArray['activity_starttime']}'";
        } elseif (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '') {
            $paramArray['activity_starttime'] = $paramArray['activity_starttime'] . " 00:00:00";
            $datawhere .= " and a.activity_endtime >= '{$paramArray['activity_starttime']}'";
        } elseif (isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $paramArray['activity_endtime'] = $paramArray['activity_endtime'] . " 23:59:59";
            $datawhere .= " and a.activity_starttime <= '{$paramArray['activity_endtime']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and (a.marketer_id = '{$paramArray['found_marketer_id']}' or  s.staffer_id  = '{$paramArray['found_marketer_id']}' )";
        }
        //活动来源  0 自己录入的  1 集团录入的
        if (isset($paramArray['activity_type']) && $paramArray['activity_type'] != '') {
            $datawhere .= " and a.activity_type = '{$paramArray['activity_type']}'";
        }
        //活动来源  0普招模式1验券模式
        if (isset($paramArray['activity_pattern']) && $paramArray['activity_pattern'] != '') {
            $datawhere .= " and a.activity_pattern = '{$paramArray['activity_pattern']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '') {
            $datawhere .= " and a.frommedia_name = '{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '') {
            $datawhere .= " and a.channel_id = '{$paramArray['channel_id']}'";
        }
        //学校 筛选
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and h.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(a.activity_id)  as datanum
                 FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE {$datawhere}";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }
        $sqlfields = " a.activity_id,a.activity_type,a.activity_pattern,a.activity_istemp,a.activity_tempurl,a.activitytemp_id,a.activity_sharedesc,a.activity_shareimg,a.activity_name,a.marketer_id,m.marketer_name,a.activity_starttime,a.activity_endtime,a.activity_type,a.package_branch,s.staffer_cnname,h.school_id,a.frommedia_name,a.channel_id,(select c.channel_name from crm_code_channel as c where a.channel_id = c.channel_id) as channel_name,s.staffer_enname ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE {$datawhere}
                ORDER BY activity_id DESC";


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                //发布人
                if ($val['activity_type'] == '1') {
                    $val['marketer_name'] = ($val['staffer_cnname'] == null) ? '' : $val['staffer_cnname'] . ((isset($val['staffer_enname']) && $val['staffer_enname'] != '') ? '-' . $val['staffer_enname'] : '');
                } else {
                    $val['marketer_name'] = ($val['marketer_name'] == null) ? '' : $val['marketer_name'] . ((isset($val['staffer_enname']) && $val['staffer_enname'] != '') ? '-' . $val['staffer_enname'] : '');
                }

                $val['activity_typename'] = $val['activity_type'] == '0' ? $this->LgStringSwitch('学校') : $this->LgStringSwitch('集团');
                if ($val['activity_pattern'] == '0') {
                    $val['activity_pattern'] = $this->LgStringSwitch('普通活动');
                } elseif ($val['activity_pattern'] == '1') {
                    $val['activity_pattern'] = $this->LgStringSwitch('招生券活动');
                }
                $val['activity_name'] = $val['activity_name'] == null ? '' : $val['activity_name'];
                $val['activity_starttime'] = date("Y-m-d", strtotime($val['activity_starttime'])) . $this->LgStringSwitch(" 到 ") . date("Y-m-d", strtotime($val['activity_endtime']));
                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                              LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$val['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_tracestatus <> '-1'  and c.client_tracestatus <> '-2'");
                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$val['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'
                                                             and c.marketer_id = '{$paramArray['marketer_id']}'
                                                             and c.marketer_id <> ''");

                $actnum = $actnum['num'];
                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $val['clientnum'] = $clientcount;
                //活动来源的意向客户
                $val['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(DISTINCT c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $val['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $val['percentconversion'] = (sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100) . "%";
                } else {
                    $val['percentconversion'] = (0) . "%";
                }

                //二维码
                if ($val['activity_id'] == '89') {
                    $val['qrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=http://m.kidcastle.com/tbnxd/index.html";
                } else {
                    $activityOne = array();
                    if ($val['activity_istemp'] == '0' && $val['activity_tempurl'] != '' && $val['activity_tempurl'] != '0') {//自定义
                        $activityOne['activity_SchoolUrl'] = $val['activity_tempurl'] . "?activity_id={$val['activity_id']}&school_id={$val['school_id']}&language_type={$paramArray['language_type']}";
                        $activityOne['activity_PersonalUrl'] = $val['activity_tempurl'] . "?activity_id={$val['activity_id']}&school_id={$val['school_id']}&marketer_id={$val['marketer_id']}&language_type={$paramArray['language_type']}";
                    } else {//模板
                        $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url", "activitytemp_id='{$val['activitytemp_id']}'");
                        if ($_SERVER['SERVER_NAME'] != 'crmapi.kedingdang.com') {
                            $activitytempOne['activitytemp_url'] = str_replace("https://crmshare.kedingdang.com", "http://crmshare.kcclassin.com", $activitytempOne['activitytemp_url']);
                        }
                        $activityOne['activity_SchoolUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$val['activity_id']}&school_id={$val['school_id']}&language_type={$paramArray['language_type']}";
                        $activityOne['activity_PersonalUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$val['activity_id']}&school_id={$val['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}";
                    }

                    $val['qrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_SchoolUrl']);//学校二维码
                    $val['personalqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_PersonalUrl']);//个人二维码


                    $QRcodeUrl = "activity_id={$val['activity_id']}&school_id={$val['school_id']}&language_type={$paramArray['language_type']}";
                    $personalqrcode = "activity_id={$val['activity_id']}&school_id={$val['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}";
                    $val['QRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getQRcode?" . $QRcodeUrl;
                    $val['personalQRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getQRcode?" . $personalqrcode;

                    $val['qrcodeurl'] = $activityOne['activity_SchoolUrl'];//学校二维码
                    $val['personalqrcodeurl'] = $activityOne['activity_PersonalUrl'];//个人二维码
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];//招生活动名称
                    $datearray['activity_starttime'] = $dateexcelvar['activity_starttime'];//活动时间
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];//有效名单数
                    $datearray['actnum'] = $dateexcelvar['actnum'];//活动意向客户
                    $datearray['officialnum'] = $dateexcelvar['officialnum'];//转正人数
                    $datearray['percentconversion'] = $dateexcelvar['percentconversion'];//转化率
                    $datearray['qrcodeurl'] = $dateexcelvar['qrcodeurl'];//学校二维码地址
                    $datearray['personalqrcodeurl'] = $dateexcelvar['personalqrcodeurl'];//个人二维码地址
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];//发布人
                    $datearray['activity_typename'] = $dateexcelvar['activity_typename'];//活动来源
                    $datearray['activity_pattern'] = $dateexcelvar['activity_pattern'];//招生活动模式
                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];//渠道类型
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道明细
//                    $datearray['QRcodeUrl'] = $dateexcelvar['QRcodeUrl'];//学校小程序地址
//                    $datearray['personalQRcodeUrl'] = $dateexcelvar['personalQRcodeUrl'];//个人小程序地址
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$paramArray['school_id']}'");
            $excelheader = $this->LgArraySwitch(array('招生活动名称', '活动时间', '有效名单数', '活动意向客户', '转正人数', '转化率', '学校二维码地址', '个人二维码地址', '发布人', '活动来源', '招生活动模式', '渠道类型', '渠道明细'));
            $excelfileds = array('activity_name', 'activity_starttime', 'clientnum', 'actnum', 'officialnum', 'percentconversion', 'qrcodeurl', 'personalqrcodeurl', 'marketer_name', 'activity_typename', 'activity_pattern', 'frommedia_name', 'channel_name');
            $fielname = $this->LgStringSwitch("招生活动管理列表");
            $schoolOne['school_cnname'] = $this->LgStringSwitch($schoolOne['school_cnname']);

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
        }

        if (is_array($dataList)) {
            $list = array();
            foreach ($dataList as $datakey => $dataVar) {
                $list[$datakey]['activity_id'] = $dataVar['activity_id'];
                $list[$datakey]['marketer_id'] = $dataVar['marketer_id'];
                $list[$datakey]['activity_type'] = $dataVar['activity_type'];
                $list[$datakey]['activity_typename'] = $dataVar['activity_type'] == '0' ? $this->LgStringSwitch('学校') : $this->LgStringSwitch('集团');
                if ($dataVar['activity_pattern'] == '0') {
                    $list[$datakey]['activity_pattern'] = $this->LgStringSwitch('普通活动');
                } elseif ($dataVar['activity_pattern'] == '1') {
                    $list[$datakey]['activity_pattern'] = $this->LgStringSwitch('招生券活动');
                }
                $list[$datakey]['activity_name'] = $dataVar['activity_name'] == null ? '' : $dataVar['activity_name'];
                $list[$datakey]['activity_starttime'] = date("Y-m-d", strtotime($dataVar['activity_starttime'])) . $this->LgStringSwitch(" 到 ") . date("Y-m-d", strtotime($dataVar['activity_endtime']));
                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                              LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_tracestatus <> '-1'");
                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and s.school_id = '{$paramArray['school_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'
                                                             and c.marketer_id = '{$paramArray['marketer_id']}'
                                                             and c.marketer_id <> ''");

                $actnum = $actnum['num'];
                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $list[$datakey]['clientnum'] = $clientcount;
                //活动来源的意向客户
                $list[$datakey]['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count( DISTINCT c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $list[$datakey]['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $list[$datakey]['percentconversion'] = (sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100) . "%";
                } else {
                    $list[$datakey]['percentconversion'] = (0) . "%";
                }

                //二维码
                if ($dataVar['activity_id'] == '89') {
                    $list[$datakey]['qrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=http://m.kidcastle.com/tbnxd/index.html";
                } else {
                    $activityOne = array();
                    if ($dataVar['activity_istemp'] == '0' && $dataVar['activity_tempurl'] != '' && $dataVar['activity_tempurl'] != '0') {//自定义
                        $activityOne['activity_SchoolUrl'] = $dataVar['activity_tempurl'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&language_type={$paramArray['language_type']}";
                        $activityOne['activity_PersonalUrl'] = $dataVar['activity_tempurl'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&marketer_id={$dataVar['marketer_id']}&language_type={$paramArray['language_type']}";
                    } else {//模板
                        $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url", "activitytemp_id='{$dataVar['activitytemp_id']}'");
                        if ($_SERVER['SERVER_NAME'] != 'crmapi.kedingdang.com') {
                            $activitytempOne['activitytemp_url'] = str_replace("https://crmshare.kedingdang.com", "http://crmshare.kcclassin.com", $activitytempOne['activitytemp_url']);
                        }
                        $activityOne['activity_SchoolUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&language_type={$paramArray['language_type']}";
                        $activityOne['activity_PersonalUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}";
                    }

                    $list[$datakey]['qrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_SchoolUrl']);//学校二维码
                    $list[$datakey]['personalqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_PersonalUrl']);//个人二维码


                    $QRcodeUrl = "activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&language_type={$paramArray['language_type']}";
                    $personalqrcode = "activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}";
                    if ($_SERVER['SERVER_NAME'] != 'crmapi.kedingdang.com') {
                        $list[$datakey]['QRcodeUrl'] = "http://" . $_SERVER['SERVER_NAME'] . "/Api/getQRcode?" . $QRcodeUrl;
                        $list[$datakey]['personalQRcodeUrl'] = "http://" . $_SERVER['SERVER_NAME'] . "/Api/getQRcode?" . $personalqrcode;
                    } else {
                        $list[$datakey]['QRcodeUrl'] = "https://" . $_SERVER['SERVER_NAME'] . "/Api/getQRcode?" . $QRcodeUrl;
                        $list[$datakey]['personalQRcodeUrl'] = "https://" . $_SERVER['SERVER_NAME'] . "/Api/getQRcode?" . $personalqrcode;
                    }
//                    $list[$datakey]['QRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getQRcode?" . $QRcodeUrl;
//                    $list[$datakey]['personalQRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getQRcode?" . $personalqrcode;

                    $list[$datakey]['qrcodeurl'] = $activityOne['activity_SchoolUrl'];//学校二维码
                    $list[$datakey]['personalqrcodeurl'] = $activityOne['activity_PersonalUrl'];//个人二维码
                }
                //发布人
                if ($dataVar['activity_type'] == '1') {
                    $list[$datakey]['marketer_name'] = $dataVar['staffer_cnname'] == null ? '' : $dataVar['staffer_cnname'] . ((isset($dataVar['staffer_enname']) && $dataVar['staffer_enname'] != '') ? '-' . $dataVar['staffer_enname'] : '');
                } else {
                    $list[$datakey]['marketer_name'] = $dataVar['marketer_name'] == null ? '' : $dataVar['marketer_name'] . ((isset($dataVar['staffer_enname']) && $dataVar['staffer_enname'] != '') ? '-' . $dataVar['staffer_enname'] : '');
                }
                $list[$datakey]['frommedia_name'] = $dataVar['frommedia_name'];
                $list[$datakey]['channel_id'] = $dataVar['channel_id'];
                $list[$datakey]['channel_name'] = $dataVar['channel_name'];

//                $promotion = $this->DataControl->selectOne("SELECT COUNT(ap.promotion_id) as num FROM crm_sell_activity_promotion as ap
//                                                                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = ap.promotion_id
//                                                                LEFT JOIN crm_ground_promotion_open as op ON op.company_id = p.company_id AND op.school_id = p.school_id AND op.promotion_id = p.promotion_id
//                                                                WHERE ap.activity_id = '{$dataVar['activity_id']}' AND op.open_status = 1");

                $school_idone = $paramArray['school_id']?$paramArray['school_id']:0;

                $promotion = $this->DataControl->selectOne("SELECT COUNT(ap.promotion_id) as num FROM crm_sell_activity_promotion as ap 
                                                                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = ap.promotion_id
                                                                LEFT JOIN crm_ground_promotion_open as op ON op.company_id = p.company_id AND op.school_id = '{$school_idone}' AND op.promotion_id = p.promotion_id
                                                                WHERE ap.activity_id = '{$dataVar['activity_id']}' AND ap.school_id = '{$school_idone}' AND op.open_status = 1");
                if ($promotion['num']) {
                    $list[$datakey]['promotion_qrcode'] = true;
                } else {
                    $list[$datakey]['promotion_qrcode'] = false;
                }

                $list[$datakey]['now_marketer_name'] = $this->marketerOne['marketer_name'];
                $list[$datakey]['now_school_cnname'] = $schoolOne['school_cnname'];
            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //招生活动 -- >> 某个活动分享的 图片和描述
    function getActivityShareApi($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_sell_activity", "activity_shareimg,activity_sharedesc", " activity_id = '{$paramArray['activity_id']}' ");
        return $activityOne;
    }

    //招生目标 -- >> 复制活动的详细情况
    function copyActivityOneApi($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_sell_activity", " * ", " activity_id = '{$paramArray['activity_id']}' ");
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,company_id,staffer_id", "marketer_id='{$paramArray['marketer_id']}'");
        $data = array();
        $data['company_id'] = $marketerOne['company_id'];// 公司
        $data['staffer_id'] = $marketerOne['staffer_id'];// 职工
        $data['marketer_id'] = $paramArray['marketer_id'];// 只职工
        $data['activity_type'] = '0';// 活动来源
        $data['activity_pattern'] = $activityOne['activity_pattern'];//招生活动模式
        $data['activity_name'] = $activityOne['activity_name'] . $this->LgStringSwitch('复制');//名称
        $data['activity_theme'] = $activityOne['activity_theme'];//活动招生标题
        $data['activity_starttime'] = $activityOne['activity_starttime'];//开始时间
        $data['activity_endtime'] = $activityOne['activity_endtime'];//结束时间
        $data['activity_istemp'] = $activityOne['activity_istemp'];//是否使用招生模板
        $data['activitytemp_id'] = $activityOne['activitytemp_id'];//招生模板id
        $data['activity_tempurl'] = $activityOne['activity_tempurl'];//自定义招生模板链接
        $data['activity_contacttel'] = $activityOne['activity_contacttel'];//联系方式
        $data['activity_address'] = $activityOne['activity_address'];//活动地址
        $data['activity_img'] = $activityOne['activity_img'];//主图
        $data['activity_demoimg'] = $activityOne['activity_demoimg'];//海报活动 (
        $data['activity_content'] = $activityOne['activity_content'];//活动详情
        $data['activity_rule'] = $activityOne['activity_rule'];//活动规则
        $data['activity_aboutus'] = $activityOne['activity_aboutus'];//关于我们
        $data['activity_customcontent'] = $activityOne['activity_customcontent'];//自定义详情
        $data['activity_sharedesc'] = $activityOne['activity_sharedesc'];//课叮铛分享描述
        $data['activity_shareimg'] = $activityOne['activity_shareimg'];//微信分享小图标
        $data['frommedia_name'] = $activityOne['frommedia_name'];//来源渠道信息
        $data['channel_id'] = $activityOne['channel_id'];//渠道明细id
        $data['activity_codetype'] = $activityOne['activity_codetype'];//二维码类型  0 默认(地推码)  1 企微

        $data['package_branch'] = $activityOne['package_branch'];//
        $data['activity_createtime'] = time();
        $activityid = $this->DataControl->insertData('crm_sell_activity', $data);
        $data['activity_id'] = $activityid;

        $dataschool = array();
        $dataschool['company_id'] = $paramArray['company_id'];//来源渠道信息
        $dataschool['activity_id'] = $activityid;//来源渠道信息
        $dataschool['school_id'] = $paramArray['school_id'];//来源渠道信息
        $this->DataControl->insertData('crm_sell_activity_school', $dataschool);

        if ($activityid) {

            $promotion = $this->DataControl->selectClear("SELECT promotion_id FROM crm_sell_activity_promotion WHERE activity_id = '{$paramArray['activity_id']}'");
            if ($promotion) {
                foreach ($promotion as $value) {
                    $datapromotion = array();
                    $datapromotion['activity_id'] = $activityid;
                    $datapromotion['promotion_id'] = $value['promotion_id'];
                    $this->DataControl->insertData('crm_sell_activity_promotion', $datapromotion);
                }
            }
            return $data;
        } else {
            return false;
        }
    }

    //招生目标 -- >> 某个活动的详细情况
    function goalActivityOneApi($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_sell_activity", " * ", " activity_id = '{$paramArray['activity_id']}' ");
        if ($activityOne['activity_type'] == '1') {
            $marketerOne = $this->DataControl->selectOne("select s.staffer_cnname from smc_staffer as s  
                                                    WHERE s.staffer_id = '{$activityOne['staffer_id']}' ");
            $activityOne['marketer_name'] = $marketerOne['staffer_cnname'];
        } else {
            $marketerOne = $this->DataControl->selectOne("select m.marketer_name from crm_marketer as m  
                                                    WHERE m.marketer_id = '{$activityOne['marketer_id']}' ");
            $activityOne['marketer_name'] = $marketerOne['marketer_name'];
        }
        $activityOne['activity_createtime'] = date("Y-m-d H:i:s", $activityOne['activity_createtime']);
        if ($activityOne['activity_id'] == '89') {
            $activityOne['activity_appUrl'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=http://m.kidcastle.com/tbnxd/index.html";
        } else {
            if ($activityOne['activity_istemp'] == '0' && $activityOne['activity_tempurl'] != '' && $activityOne['activity_tempurl'] != '0') {//自定义
                $activityOne['activity_SchoolUrl'] = $activityOne['activity_tempurl'] . "?activity_id={$activityOne['activity_id']}&school_id={$activityOne['school_id']}&language_type={$paramArray['language_type']}";//学校链接地址&activity_sharedesc={$activityOne['activity_sharedesc']}&activity_shareimg={$activityOne['activity_shareimg']}
                $activityOne['activity_PersonalUrl'] = $activityOne['activity_tempurl'] . "?activity_id={$activityOne['activity_id']}&school_id={$activityOne['school_id']}&marketer_id={$activityOne['marketer_id']}&language_type={$paramArray['language_type']}";//个人链接地址&activity_sharedesc={$activityOne['activity_sharedesc']}&activity_shareimg={$activityOne['activity_shareimg']}
            } else {//模板
                if ($activityOne['activity_pattern'] == '2') {//海报活动

                    $activityOne['activity_SchoolUrl'] = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$paramArray['activity_id']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";//学校链接地址
                    $activityOne['activity_PersonalUrl'] = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$paramArray['activity_id']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&marketer_id={$paramArray['marketer_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";//个人链接地址

                } else {
                    $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url,activitytemp_styleimg,activitytemp_bannerimg,activitytemp_name
", "activitytemp_id='{$activityOne['activitytemp_id']}'");
                    if ($_SERVER['SERVER_NAME'] !== 'crmapi.kedingdang.com' && $_SERVER['SERVER_NAME'] !== 'gmcapi.kedingdang.com') {
                        $activitytempOne['activitytemp_url'] = str_replace("https://crmshare.kedingdang.com", "http://crmshare.kcclassin.com", $activitytempOne['activitytemp_url']);
                    }
                    $activityOne['activity_SchoolUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$activityOne['activity_id']}&school_id={$activityOne['school_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";//学校链接地址&activity_sharedesc={$activityOne['activity_sharedesc']}&activity_shareimg={$activityOne['activity_shareimg']}
                    $activityOne['activity_PersonalUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$activityOne['activity_id']}&school_id={$activityOne['school_id']}&marketer_id={$activityOne['marketer_id']}&language_type={$paramArray['language_type']}&isschoolcrm=1";//个人链接地址&activity_sharedesc={$activityOne['activity_sharedesc']}&activity_shareimg={$activityOne['activity_shareimg']}
                }
            }
            $activityOne['activitytemp_styleimg'] = $activitytempOne['activitytemp_styleimg'] != '' ? $activitytempOne['activitytemp_styleimg'] : '';
            $activityOne['activitytemp_bannerimg'] = $activitytempOne['activitytemp_bannerimg'] != '' ? $activitytempOne['activitytemp_bannerimg'] : '';
            $activityOne['activitytemp_name'] = $activitytempOne['activitytemp_name'] != '' ? $activitytempOne['activitytemp_name'] : '';

            $activityOne['activity_appSchoolUrl'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_SchoolUrl']);//学校二维码
            $activityOne['activity_appPersonalSchoolUrl'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_PersonalUrl']);//个人二维码
        }
        $activityOne['activitytemp_id'] = ($activityOne['activitytemp_id'] == '0' ? '' : $activityOne['activitytemp_id']);
        if ($activityOne['channel_id'] == '0') {
            $activityOne['channel_id'] = '';
        }
        return $activityOne;
    }

    //招生目标 -- >> 添加活动
    function addGoalActivityAction($paramArray)
    {
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,company_id,staffer_id", "marketer_id='{$paramArray['marketer_id']}'");
        $data = array();
        $data['company_id'] = $marketerOne['company_id'];// 公司
        $data['staffer_id'] = $marketerOne['staffer_id'];// 职工
        $data['marketer_id'] = $paramArray['marketer_id'];// 只职工
        $data['activity_type'] = '0';// 活动来源
        $data['activity_name'] = $paramArray['activity_name'];//名称
        $data['activity_theme'] = $paramArray['activity_theme'];//活动招生标题
        $data['activity_starttime'] = $paramArray['activity_starttime'];//开始时间
        $data['activity_endtime'] = $paramArray['activity_endtime'];//结束时间
        $data['activity_istemp'] = $paramArray['activity_istemp'];//是否使用招生模板
        $data['activitytemp_id'] = $paramArray['activitytemp_id'];//招生模板id
        $data['activity_tempurl'] = $paramArray['activity_tempurl'];//自定义招生模板链接
        $data['activity_contacttel'] = $paramArray['activity_contacttel'];//联系方式
        $data['activity_address'] = $paramArray['activity_address'];//活动地址
        $data['activity_img'] = $paramArray['activity_img'];//主图
        $data['activity_content'] = $paramArray['activity_content'];//活动详情
        $data['activity_rule'] = $paramArray['activity_rule'];//活动规则
        $data['activity_aboutus'] = $paramArray['activity_aboutus'];//关于我们
        $data['activity_customcontent'] = $paramArray['activity_customcontent'];//自定义详情
        $data['activity_sharedesc'] = $paramArray['activity_sharedesc'];//课叮铛分享描述
        $data['activity_shareimg'] = $paramArray['activity_shareimg'];//微信分享小图标
        $data['activity_imgwidth'] = $paramArray['activity_imgwidth'];//主图宽
        $data['activity_imgheight'] = $paramArray['activity_imgheight'];//主图高

        $data['activity_demoimg'] = $paramArray['activity_demoimg'];//海报活动专用  的展示图（非镂空）

        $data['activity_pattern'] = $paramArray['activity_pattern'];//招生活动模式：0普招模式1验券模式 2海报活动
        $data['channel_id'] = $paramArray['channel_id'];//来源渠道信息
        $data['frommedia_name'] = $paramArray['frommedia_name'];//来源渠道信息
        $data['activity_codetype'] = $paramArray['activity_codetype'];//二维码类型  0 默认(地推码)  1 企微

        $data['activity_issex'] = $paramArray['activity_issex'];//
        $data['activity_issex_must'] = $paramArray['activity_issex_must'];//
        $data['activity_isbirthday'] = $paramArray['activity_isbirthday'];//
        $data['activity_isbirthday_must'] = $paramArray['activity_isbirthday_must'];//
        $data['activity_isaddress'] = $paramArray['activity_isaddress'];//
        $data['activity_isaddress_must'] = $paramArray['activity_isaddress_must'];//

        $data['package_branch'] = $paramArray['package_branch'];//

        $data['activity_createtime'] = time();
        $activityid = $this->DataControl->insertData('crm_sell_activity', $data);
        if ($activityid) {
            $company_id = $this->DataControl->selectOne("select company_id from smc_school where school_id = '{$paramArray['school_id']}' ");
            $dataSchool = array();
            $dataSchool['company_id'] = $company_id['company_id'];
            $dataSchool['activity_id'] = $activityid;
            $dataSchool['school_id'] = $paramArray['school_id'];
            $this->DataControl->insertData('crm_sell_activity_school', $dataSchool);

            if ($paramArray['promotion_json']) {
                $this->addGroundPromotionAction($paramArray, $activityid);
            }

            $actdata = array();
            $actdata['activity_id'] = $activityid;
            return $actdata;
        } else {
            return false;
        }
    }

    //招生目标 -- 修改招生目标
    function updateGoalActivityAction($paramArray, $account_class = '0',$activity_type='0')
    {
        $data = array();
        $data['activity_name'] = $paramArray['activity_name'];//名称
        $data['activity_theme'] = $paramArray['activity_theme'];//活动招生标题
        $data['activity_starttime'] = $paramArray['activity_starttime'];//开始时间
        $data['activity_endtime'] = $paramArray['activity_endtime'];//结束时间
        $data['activity_istemp'] = $paramArray['activity_istemp'];//是否使用招生模板
        $data['activitytemp_id'] = $paramArray['activitytemp_id'];//招生模板id
        $data['activity_tempurl'] = $paramArray['activity_tempurl'];//自定义招生模板链接
        $data['activity_contacttel'] = $paramArray['activity_contacttel'];//联系方式
        $data['activity_address'] = $paramArray['activity_address'];//活动地址
        $data['activity_img'] = $paramArray['activity_img'];//主图
        $data['activity_content'] = $paramArray['activity_content'];//活动详情
        $data['activity_rule'] = $paramArray['activity_rule'];//活动规则
        $data['activity_aboutus'] = $paramArray['activity_aboutus'];//关于我们
        $data['activity_customcontent'] = $paramArray['activity_customcontent'];//自定义详情
        $data['activity_sharedesc'] = $paramArray['activity_sharedesc'];//课叮铛分享描述
        $data['activity_shareimg'] = $paramArray['activity_shareimg'];//微信分享小图标
        $data['activity_imgwidth'] = $paramArray['activity_imgwidth'];//主图宽
        $data['activity_imgheight'] = $paramArray['activity_imgheight'];//主图高

        $data['activity_demoimg'] = $paramArray['activity_demoimg'];//海报活动专用  的展示图（非镂空）
        $data['activity_pattern'] = $paramArray['activity_pattern'];//招生活动模式：0普招模式1验券模式
        $data['channel_id'] = $paramArray['channel_id'];//来源渠道信息
        $data['frommedia_name'] = $paramArray['frommedia_name'];//来源渠道信息
        $data['activity_codetype'] = $paramArray['activity_codetype'];//二维码类型  0 默认(地推码)  1 企微

        $data['activity_issex'] = $paramArray['activity_issex'];//
        $data['activity_issex_must'] = $paramArray['activity_issex_must'];//
        $data['activity_isbirthday'] = $paramArray['activity_isbirthday'];//
        $data['activity_isbirthday_must'] = $paramArray['activity_isbirthday_must'];//
        $data['activity_isaddress'] = $paramArray['activity_isaddress'];//
        $data['activity_isaddress_must'] = $paramArray['activity_isaddress_must'];//

        $data['package_branch'] = $paramArray['package_branch'];//

        $data['activity_updatetime'] = time();
        if($activity_type == '1' && $paramArray['school_id'] > 1) {
            $istrue = 1;
        }else{
            if ($account_class == '1') {
                $istrue = $this->DataControl->updateData("crm_sell_activity", "activity_id = '{$paramArray['activity_id']}' and company_id = '{$paramArray['company_id']}' ", $data);
            } else {
                $istrue = $this->DataControl->updateData("crm_sell_activity", "activity_id = '{$paramArray['activity_id']}' and marketer_id = '{$paramArray['marketer_id']}' ", $data);
            }
        }
        if ($istrue) {
            if ($paramArray['promotion_json']) {
                if (!$paramArray['school_id']) {
                    $paramArray['school_id'] = 0;
                }
                //3、选择A校，编辑成功后，现实地推二维码，切换为B校，切换成功，编辑地推人员，A校地推二维码不显示   ******** （ 不能直接清楚历史的数据）
                $this->DataControl->delData("crm_sell_activity_promotion", "activity_id = '{$paramArray['activity_id']}' and school_id = '{$paramArray['school_id']}' ");

                $this->addGroundPromotionAction($paramArray, $paramArray['activity_id']);
            }
            return $data;
        } else {
            return false;
        }
    }

    //招生目标 -- >> 某个活动的详细情况  //== 手机活动展示页面
    function goalPhoneActivityOneApi($paramArray)
    {
        $activityOne = $this->DataControl->selectOne(" select a.activity_id,a.activity_name,a.marketer_id,a.activity_type,m.marketer_name,m.marketer_mobile,a.activity_starttime,a.activity_endtime,a.activity_img,a.activity_content,a.activity_createtime,a.activity_type,s.staffer_cnname,s.staffer_mobile,g.company_id,g.company_cnname,g.company_logo
                                                      from crm_sell_activity as a 
                                                      LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                                                      LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                                                      LEFT JOIN gmc_company as g ON a.company_id = g.company_id
                                                      where a.activity_id = '{$paramArray['activity_id']}' ");
        if ($activityOne['activity_type'] == '0') {
            $marketerOne = $this->DataControl->selectOne("select s.school_id,s.school_address from crm_sell_activity_school as h  
                                                LEFT JOIN smc_school as s ON h.school_id = s.school_id
                                                WHERE h.activity_id = '{$activityOne['activity_id']}' limit 0,1 ");
            $activityOne['school_id'] = $marketerOne['school_id'];
            $activityOne['school_address'] = $marketerOne['school_address'];
        }
        if ($activityOne['activity_type'] == '1') {
            $activityOne['marketer_name'] = $activityOne['staffer_cnname'];
            $activityOne['marketer_mobile'] = $activityOne['staffer_mobile'];
        }

        return $activityOne;
    }

    //招生目标 -- 删除某个招生活动
    function delGoalActivityAction($activity_id, $marketer_id, $account_class = 0, $company_id = '')
    {
        if ($account_class == '1') {
            $delactid = $this->DataControl->delData('crm_sell_activity', "activity_id='{$activity_id}' and company_id='{$company_id}' ");
        } else {
            $delactid = $this->DataControl->delData('crm_sell_activity', "activity_id='{$activity_id}' and marketer_id='{$marketer_id}' ");
        }
        if ($delactid) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }

    //招生目标 -- 删除某个招生活动（删除集团活动)
    function delComGoalActivityAction($activity_id, $marketer_id, $account_class = 0, $company_id = '')
    {
        if ($account_class == '1') {
            $delactid = $this->DataControl->delData('crm_sell_activity', "activity_id='{$activity_id}' and company_id='{$company_id}' ");
        } else {
            $delactid = $this->DataControl->delData('crm_sell_activity', "activity_id='{$activity_id}' and staffer_id='{$marketer_id}' ");
        }
        if ($delactid) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }


    //招生活动 -- >> 地推人员管理     $iscom 是否是集团列表 1 是 0 否
    function getGroundPromotion($paramArray,$iscom = '0')
    {
        $datawhere = "p.company_id = '{$paramArray['company_id']}'";
        $registerwhere = "o.company_id = '{$paramArray['company_id']}'";

        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and p.promotion_isprohibit = '0'  ";
            //******** 杰瑞 说学校添加的也可以在其他学校展示出来
//            and (p.school_id = '0' or n.school_id = '{$paramArray['school_id']}' )
        }
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (p.promotion_name like '%{$paramArray['keyword']}%' or p.promotion_mobile like '%{$paramArray['keyword']}%' or p.promotion_jobnumber like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['open_status']) && $paramArray['open_status'] != '') {
            $datawhere .= " and n.open_status = '{$paramArray['open_status']}'";
        }
        if (isset($paramArray['promotion_type']) && $paramArray['promotion_type'] !== '') {
            $datawhere .= " and p.promotion_type = '{$paramArray['promotion_type']}'";
        }
        if (isset($paramArray['promotion_isprohibit']) && $paramArray['promotion_isprohibit'] !== '') {
            $datawhere .= " and p.promotion_isprohibit = '{$paramArray['promotion_isprohibit']}'";
        }
//        if (isset($paramArray['open_status']) && $paramArray['open_status'] !== '') {
//            $datawhere .= " and p.open_status = '{$paramArray['open_status']}'";
//        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '100';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            if($iscom == '1') {
                $count = $this->DataControl->selectClear("SELECT p.promotion_id 
                FROM crm_ground_promotion as p 
                left join crm_ground_promotion_open as n ON p.company_id = n.company_id AND p.promotion_id = n.promotion_id 
                WHERE {$datawhere}  
                GROUP BY p.promotion_id ");
            }else{
                $count = $this->DataControl->selectClear("SELECT p.promotion_id 
                FROM crm_ground_promotion as p 
                left join crm_ground_promotion_open as n ON p.company_id = n.company_id AND p.promotion_id = n.promotion_id AND n.school_id = '{$paramArray['school_id']}' 
                WHERE {$datawhere}  
                GROUP BY p.promotion_id ");
            }
            if($count){
                $count = count($count);
            }else{
                $count = 0;
            }
//            $count = $count['num'] + 0;
        } else {
            $count = 0;
        }
        if($iscom == '1') {
            $sql = "SELECT p.*, 
                (SELECT COUNT(c.client_id) FROM crm_client as c WHERE c.promotion_id = p.promotion_id) as client_allnum,
                (SELECT COUNT(c.client_id) FROM crm_client as c WHERE c.promotion_id = p.promotion_id AND c.client_tracestatus >= 0) as client_allvalidnum,
                (SELECT COUNT(s.student_id) FROM smc_student_registerinfo as o,smc_student as s,crm_client as c WHERE o.student_id = s.student_id and s.from_client_id = c.client_id and c.promotion_id = p.promotion_id and {$registerwhere}) as positivenum, 
			(SELECT GROUP_CONCAT(s.school_cnname) FROM crm_ground_promotion_open as o,smc_school as s WHERE o.promotion_id = p.promotion_id and o.open_status = '1' and o.school_id = s.school_id limit 0,1) as school_allname  
                FROM crm_ground_promotion as p 
                left join crm_ground_promotion_open as n ON p.company_id = n.company_id AND p.promotion_id = n.promotion_id 
                WHERE {$datawhere}  
                GROUP BY p.promotion_id 
                ORDER BY p.promotion_id DESC";
        }else{
            $sql = "SELECT p.*,n.open_status, 
                (SELECT COUNT(c.client_id) FROM crm_client as c WHERE c.promotion_id = p.promotion_id) as client_allnum,
                (SELECT COUNT(c.client_id) FROM crm_client as c WHERE c.promotion_id = p.promotion_id AND c.client_tracestatus >= 0) as client_allvalidnum,
                (SELECT COUNT(s.student_id) FROM smc_student_registerinfo as o,smc_student as s,crm_client as c WHERE o.student_id = s.student_id and s.from_client_id = c.client_id and c.promotion_id = p.promotion_id and {$registerwhere}) as positivenum, 
			(SELECT GROUP_CONCAT(s.school_cnname) FROM crm_ground_promotion_open as o,smc_school as s WHERE o.promotion_id = p.promotion_id and o.open_status = '1' and o.school_id = s.school_id limit 0,1) as school_allname  
                FROM crm_ground_promotion as p 
                left join crm_ground_promotion_open as n ON p.company_id = n.company_id AND p.promotion_id = n.promotion_id AND n.school_id = '{$paramArray['school_id']}' 
                WHERE {$datawhere}  
                GROUP BY p.promotion_id 
                ORDER BY p.promotion_id DESC";
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                $status = $this->LgArraySwitch(array('0' => '未使用', '1' => '已使用'));
                $statustype = $this->LgArraySwitch(array('0' => '兼职', '1' => '销售', '2' => '专员'));
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['promotion_name'] = $dateexcelvar['promotion_name'];//姓名
                    $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber'];//地推工号
                    $datearray['promotion_type_name'] = $statustype[$dateexcelvar['promotion_type']];//地推类型
//                    $datearray['promotion_mobile'] = $dateexcelvar['promotion_mobile'];//联系电话
                    $datearray['promotion_mobile'] =  preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['promotion_mobile']);//联系电话
                    $datearray['school_allname'] = $dateexcelvar['school_allname'];//负责校区
                    $datearray['promotion_bankcard'] = $dateexcelvar['promotion_bankcard'];//银行卡号
                    $datearray['promotion_bank'] = $dateexcelvar['promotion_bank'];//开户行
                    $datearray['promotion_isopen'] = $status[$dateexcelvar['open_status']];//是否使用
                    if ($dateexcelvar['client_allvalidnum'] && $dateexcelvar['client_allnum']) {
                        $datearray['client_allvalid_rate'] = (sprintf("%.2f", $dateexcelvar['client_allvalidnum'] / $dateexcelvar['client_allnum']) * 100) . "%";
                    } else {
                        $datearray['client_allvalid_rate'] = "0%";
                    }
                    $datearray['client_allnum'] = $dateexcelvar['client_allnum'] ? $dateexcelvar['client_allnum'] : "0";//毛名单数
                    $datearray['client_allvalidnum'] = $dateexcelvar['client_allvalidnum'] ? $dateexcelvar['client_allvalidnum'] : "0";//有效名单数
                    $datearray['positivenum'] = $dateexcelvar['positivenum'] ? $dateexcelvar['positivenum'] : "0";//报名名单数
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('姓名', '地推工号', '地推类型','联系电话', '负责校区', '银行卡号', '开户行', '是否使用', '毛名单数', '有效名单数', '名单有效率', '报名名单数'));
            $excelfileds = array('promotion_name', 'promotion_jobnumber', 'promotion_type_name', 'promotion_mobile', 'school_allname','promotion_bankcard', 'promotion_bank', 'promotion_isopen', 'client_allnum', 'client_allvalidnum', 'client_allvalid_rate', 'positivenum');

            $fielname = $this->LgStringSwitch("地推人员管理");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
        }

        if (is_array($dataList)) {
            $statustype = $this->LgArraySwitch(array('0' => '兼职', '1' => '销售', '2' => '专员'));
            foreach ($dataList as &$value) {
//                $value['promotion_mobile'] =  preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['promotion_mobile']);//联系电话

                if ($this->isGmcPost($paramArray['re_postbe_id'], $paramArray['company_id']) == true) {
                    $value['promotion_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['promotion_mobile']);
                } else {
                    $value['promotion_mobile'] = $value['promotion_mobile'];
                }
                if ($value['school_id'] == '0') {
                    $value['school_type'] = "集团";
                } else {
                    $value['school_type'] = "校区";
                }
                if ($value['client_allvalidnum'] && $value['client_allnum']) {
                    $value['client_allvalid_rate'] = (sprintf("%.2f", $value['client_allvalidnum'] / $value['client_allnum']) * 100) . "%";
                } else {
                    $value['client_allvalid_rate'] = "0%";
                }
                $value['promotion_type_name'] = $statustype[$value['promotion_type']];//地推类型
                $value['client_allnum'] = $value['client_allnum'] ? $value['client_allnum'] : '0';
                $value['client_allvalidnum'] = $value['client_allvalidnum'] ? $value['client_allvalidnum'] : '0';
                $value['positivenum'] = $value['positivenum'] ? $value['positivenum'] : '0';
                if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != '') {
//                    $onepro = $this->DataControl->selectOne(" select a.activity_id from crm_sell_activity_promotion as a,crm_ground_promotion_open as b
//                            where a.promotion_id = '{$value['promotion_id']}' and a.activity_id = '{$paramArray['activity_id']}'
//                            and a.promotion_id = b.promotion_id and b.school_id = '{$paramArray['school_id']}' and b.open_status = '1'
//                            ");
//                    if($onepro){

                    $theschoolid = $paramArray['school_id']>1?$paramArray['school_id']:0;
                    if ($this->DataControl->getOne("crm_sell_activity_promotion", "promotion_id = '{$value['promotion_id']}' and activity_id = '{$paramArray['activity_id']}' and school_id = '{$theschoolid}' ")) {
                        $value['is_Check'] = true;
                    } else {
                        $value['is_Check'] = false;
                    }
                }
            }
        } else {
            $dataList = array();
        }
        $result = array();
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    //招生活动 -- >> 添加推广人员
    function addGroundPromotionAction($paramArray, $activity_id = 0,$iscom = 0)
    {
        $schoolOne = $this->DataControl->selectOne(" select * from smc_school where school_id = '{$paramArray['school_id']}' ");
        $promotion_json = json_decode(stripslashes($paramArray['promotion_json']), 1);
        if ($promotion_json) {
            if (!$paramArray['school_id']) {
                $paramArray['school_id'] = 0;
            }
            foreach ($promotion_json as $value) {
                if($value['promotion_id'] > '1') {
                    $promotion_id = $value['promotion_id'];
                }else{
                    //如果校存在手机号一样的地推人员，获取编号   ---- ********
                    $parampro = array();
                    $parampro['mobile'] = $value['promotion_mobile'];
                    $parampro['company_id'] = $paramArray['company_id'];
                    $getBackurl = request_by_curl("https://kidapi.kedingdang.com/Crm/getSeekPromotion", dataEncode($parampro), "GET");
//                    $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/getSeekPromotion", dataEncode($parampro), "GET");
                    $bakData = json_decode($getBackurl, true);
                    if ($bakData['error'] == '0') {
                        $value['promotion_jobnumber'] =  $bakData['result']['promotion_jobnumber'];
                    }else {
                        //地推人员编号规则校园检索代码+100以后的自增数字  (20210105 喆哥定的自动生成编号)
                        $jobnum = ($schoolOne['school_enname'] ? $schoolOne['school_enname'] : $schoolOne['school_branch']);
                        $pronum = $this->DataControl->selectOne(" select count(a.promotion_id) as allnum 
                        from crm_ground_promotion as a 
                        LEFT JOIN crm_ground_promotion_open as b ON a.promotion_id = b.promotion_id 
                        where (b.school_id = '{$paramArray['school_id']}' or a.school_id = '{$paramArray['school_id']}') and a.promotion_jobnumber like '{$jobnum}%' ");
                        $value['promotion_jobnumber'] = $jobnum . (101 + $pronum['allnum']);
                    }

                    if ($iscom == 1) {//集团操作 -- 逻辑判断处理
                        if (isset($value['promotion_mobile']) && $value['promotion_mobile'] != '' && isset($value['promotion_jobnumber']) && $value['promotion_jobnumber'] != '') {
                            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$paramArray['company_id']}' and school_id = '0' and  (promotion_mobile = '{$value['promotion_mobile']}' or promotion_jobnumber = '{$value['promotion_jobnumber']}') ");
                            if ($promotionOne) {
                                ajax_return(array('error' => '1', 'errortip' => "本集团人员重复，请编辑数据！", 'result' => array()));
                            }
                            $promotionTwo = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$paramArray['company_id']}' and school_id <> '0' and (promotion_mobile = '{$value['promotion_mobile']}' or promotion_jobnumber = '{$value['promotion_jobnumber']}')", "ORDER BY promotion_mobile DESC,promotion_id DESC");
                            if ($promotionTwo) {
                                ajax_return(array('error' => '1', 'errortip' => "他校人员重复，请编辑数据！", 'result' => array("promotion_name" => $promotionTwo['promotion_name'])));
                            }
                        } else {
                            ajax_return(array('error' => '1', 'errortip' => "推广人电话和工号必须传值", 'result' => array()));
                        }
                    } else {//学校操作 -- 逻辑判断处理
                        if (isset($value['promotion_mobile']) && $value['promotion_mobile'] != '' && isset($value['promotion_jobnumber']) && $value['promotion_jobnumber'] != '') {
                            //本校人员重复
                            $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and (promotion_mobile = '{$value['promotion_mobile']}' or promotion_jobnumber = '{$value['promotion_jobnumber']}') ");
                            if ($promotionOne) {
                                ajax_return(array('error' => '1', 'errortip' => "本校人员重复", 'result' => array()));
                            }

                            //若该人员在集团未添加，在A校区已添加，在B校区新增输入手机号或地推工号后自动将地推工号和联系电话带入不可修改，其他信息可更改
                            $promotionTwo = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_name", "company_id = '{$paramArray['company_id']}' and (promotion_mobile = '{$value['promotion_mobile']}' or promotion_jobnumber = '{$value['promotion_jobnumber']}') and school_id <> '{$paramArray['school_id']}' and school_id > '0' ", "ORDER BY promotion_mobile DESC,promotion_id DESC");
                            if ($promotionTwo) {
                                //两个关键信息是否完全一致
                                if (!$this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id,promotion_name", "company_id = '{$paramArray['company_id']}' and promotion_mobile = '{$value['promotion_mobile']}' and promotion_jobnumber = '{$value['promotion_jobnumber']}' and school_id <> '{$paramArray['school_id']}' and school_id > '0' ", "ORDER BY promotion_mobile DESC,promotion_id DESC")) {//非不一致时
                                    ajax_return(array('error' => '1', 'errortip' => "他校人员重复，请核对手机号和工号后重新录入！", 'result' => array("promotion_name" => $promotionTwo['promotion_name'])));
                                }
                                //一致时
                                if (!$this->DataControl->getOne("crm_ground_promotion_open", "company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and promotion_id = '{$promotionTwo['promotion_id']}'")) {
                                    //开启学校
                                    $list = array();
                                    $list['company_id'] = $paramArray['company_id'];
                                    $list['school_id'] = $paramArray['school_id'];
                                    $list['promotion_id'] = $promotionTwo['promotion_id'];
                                    $this->DataControl->insertData('crm_ground_promotion_open', $list);
                                } else {
                                    //更改主表修改时间 -- 方便同步到 地推宝
                                    $this->DataControl->updateData("crm_ground_promotion", "company_id = '{$paramArray['company_id']}' and promotion_id = '{$promotionTwo['promotion_id']}' ", array('promotion_isprohibit' => '0', 'promotion_updatetime' => time()));
                                    //开启学校
                                    $this->DataControl->updateData("crm_ground_promotion_open", "company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and promotion_id = '{$promotionTwo['promotion_id']}' ", array('open_status' => '1'));
                                }
                                ajax_return(array('error' => '1', 'errortip' => "他校人员重复，已经帮您开启地推人员权限！", 'result' => array("promotion_name" => $promotionTwo['promotion_name'])));
                            }

                            //若该人员在集团已添加-集团未使用/已使用且未禁用
                            $promotionThree = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$paramArray['company_id']}' and (promotion_mobile = '{$value['promotion_mobile']}' or promotion_jobnumber = '{$value['promotion_jobnumber']}') and school_id = '0' and promotion_isprohibit = '0' ", "ORDER BY promotion_mobile DESC,promotion_id DESC");
                            if ($promotionThree) {
                                ajax_return(array('error' => '1', 'errortip' => "您添加的人员与集团地推人员信息重复，无需重复添加，列表开启使用即可。", 'result' => array()));
                            }

                            //若该人员在集团已添加-集团已禁用
                            $promotionFour = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_name", "company_id = '{$paramArray['company_id']}' and (promotion_mobile = '{$value['promotion_mobile']}' or promotion_jobnumber = '{$value['promotion_jobnumber']}') and school_id = '0' and promotion_isprohibit = '1' ", "ORDER BY promotion_mobile DESC,promotion_id DESC");
                            if ($promotionFour) {//激活 集团添加的名单
                                $this->DataControl->updateData("crm_ground_promotion", "promotion_mobile = '{$value['promotion_mobile']}' and promotion_jobnumber = '{$value['promotion_jobnumber']}' and company_id = '{$paramArray['company_id']}'  ", array('promotion_isprohibit' => '0', 'promotion_updatetime' => time()));
                                ajax_return(array('error' => '1', 'errortip' => "您添加的人员与集团地推人员信息重复，无需重复添加，已经激活成功，列表开启使用即可。", 'result' => array()));
                            }
                        } else {
                            ajax_return(array('error' => '1', 'errortip' => "推广人电话和工号必须传值", 'result' => array()));
                        }
                    }

//                    //添加地推人员名单时 检查是否和园务重复，编号是否一致
//                    $parampro = array();
//                    $parampro['mobile'] = $value['promotion_mobile'];
//                    $parampro['jobnumber'] = $value['promotion_jobnumber'];
//                    $parampro['company_id'] = $paramArray['company_id'];
//                    $getBackurl = request_by_curl("http://kidapi.kcclassin.com/Crm/seekPromotion", dataEncode($parampro), "GET");
////                $getBackurl = request_by_curl("http://kidapi.kidmanageapi102.com/Crm/seekPromotion", dataEncode($parampro), "GET");
//                    $bakData = json_decode($getBackurl, true);
//                    if ($bakData['error'] != '0') {
//                        $this->result = $bakData['result'];
//                        continue;
//                    }
                    if (!$value['promotion_name'] || !$value['promotion_mobile'] || !$value['promotion_jobnumber']) {
                        continue;
                    }

                    $promotion = $this->DataControl->getFieldOne("crm_ground_promotion", "promotion_id", "company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and (promotion_jobnumber = '{$value['promotion_jobnumber']}' or promotion_mobile = '{$value['promotion_mobile']}')");
                    if ($promotion) {
                        $promotion_id = $promotion['promotion_id'];
                    } else {
                        $data = array();
                        $data['company_id'] = $paramArray['company_id'];
                        $data['school_id'] = $paramArray['school_id'];
                        $data['marketer_id'] = $paramArray['marketer_id'];
                        $data['promotion_type'] = $value['promotion_type'];
                        $data['promotion_mode'] = $value['promotion_mode'];
                        $data['promotion_salarycycle'] = $value['promotion_salarycycle'];
                        $data['promotion_name'] = $value['promotion_name'];
                        $data['promotion_sex'] = $value['promotion_sex'];
                        $data['promotion_mobile'] = $value['promotion_mobile'];

                        $data['promotion_bakpass'] = substr($value['promotion_mobile'], -6);
                        $data['promotion_pass'] = md5(substr($value['promotion_mobile'], -6));

                        $data['promotion_jobnumber'] = $value['promotion_jobnumber'];
                        $data['promotion_bankcard'] = $value['promotion_bankcard'];
                        $data['promotion_bank'] = $value['promotion_bank'];
                        $data['promotion_idcard'] = $value['promotion_idcard'];
                        $data['promotion_idcardfile'] = $value['promotion_idcardfile'];
                        $data['promotion_protocolfile'] = $value['promotion_protocolfile'];
                        $data['promotion_createtime'] = time();
                        $promotion_id = $this->DataControl->insertData('crm_ground_promotion', $data);
                    }
                }
                if (!$this->DataControl->getOne("crm_ground_promotion_open", "company_id = '{$paramArray['company_id']}' and school_id = '{$paramArray['school_id']}' and promotion_id = '{$promotion_id}'")) {
                    $list = array();
                    $list['company_id'] = $paramArray['company_id'];
                    $list['school_id'] = $paramArray['school_id'];
                    $list['promotion_id'] = $promotion_id;
                    $this->DataControl->insertData('crm_ground_promotion_open', $list);
                }
                if ($activity_id) {
                    if (!$this->DataControl->getOne("crm_sell_activity_promotion", "activity_id = '{$activity_id}' and promotion_id = '{$promotion_id}' and school_id = '{$paramArray['school_id']}'")) {
                        $info = array();
                        $info['activity_id'] = $activity_id;
                        $info['school_id'] = $paramArray['school_id'];
                        $info['promotion_id'] = $promotion_id;
                        $this->DataControl->insertData('crm_sell_activity_promotion', $info);
                    }
                }
            }
        }
        if ($promotion_id) {
            return true;
        } else {
            return false;
        }
    }

    //招生活动 -- >> 编辑推广人员
    function editGroundPromotionAction($paramArray)
    {
        $promotionOne = $this->DataControl->getFieldOne("crm_ground_promotion","school_id","promotion_id = '{$paramArray['promotion_id']}'");
        if($paramArray['school_id'] && $promotionOne['school_id'] !== $paramArray['school_id']){
            if($promotionOne['school_id'] == 0){
                $this->errortip = "集团地推人员禁止单校修改资料信息";
            }else{
                $this->errortip = "非本校地推人员禁止单校修改资料信息";
            }
            return  false;
        }

        $data = array();
        $data['promotion_mode'] = $paramArray['promotion_mode'];
        $data['promotion_salarycycle'] = $paramArray['promotion_salarycycle'];
//        $data['promotion_name'] = $paramArray['promotion_name'];
        $data['promotion_sex'] = $paramArray['promotion_sex'];
//        $data['promotion_mobile'] = $paramArray['promotion_mobile'];
//        $data['promotion_jobnumber'] = $paramArray['promotion_jobnumber'];
        $data['promotion_bankcard'] = $paramArray['promotion_bankcard'];
        $data['promotion_bank'] = $paramArray['promotion_bank'];
        $data['promotion_idcard'] = $paramArray['promotion_idcard'];
        $data['promotion_idcardfile'] = $paramArray['promotion_idcardfile'];
        $data['promotion_protocolfile'] = $paramArray['promotion_protocolfile'];
        $data['promotion_updatetime'] = time();

        if ($this->DataControl->updateData("crm_ground_promotion", "promotion_id = '{$paramArray['promotion_id']}'", $data)) {
            return true;
        } else {
            $this->errortip = "编辑地推人员失败";
            return false;
        }
    }

    //招生管理 -- >> 地推二维码 -- 企业微信二维码参数解析
    function getPromotionQRcodeParamApi($paramArray) {
        if(isset($paramArray['qudao']) && $paramArray['qudao'] != ''){
            $qudao = explode('_',$paramArray['qudao']);
            $countall = count($qudao);
            if($countall == '5'){
                $data = array();
                $data['activity_id'] = $qudao['1'];
                $data['school_id'] = $qudao['2'];
                $data['marketer_id'] = ($qudao['3'] == '1')?$qudao['4']:0;
                $data['promotion_id'] = ($qudao['3'] == '2')?$qudao['4']:0;
                return $data;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
    //招生活动 -- >> 地推二维码
    function GroundPromotionQRcode($paramArray)
    {
        $school_id = $paramArray['school_id']?$paramArray['school_id']:0;

        //口述需求 后期修改 ********
        $sql = "SELECT  a.activity_istemp,a.activity_tempurl,a.activitytemp_id,p.promotion_id,p.promotion_name,p.promotion_mobile,a.activity_codetype 
                 FROM   crm_sell_activity as a 
                 LEFT JOIN  crm_sell_activity_promotion as ap ON ap.activity_id = a.activity_id 
                 LEFT JOIN  crm_ground_promotion as p ON p.promotion_id = ap.promotion_id 
                 LEFT JOIN crm_ground_promotion_open as o ON ap.promotion_id = o.promotion_id 
                 WHERE  a.activity_id = '{$paramArray['activity_id']}' 
                 and ap.school_id = '{$school_id}'
                 AND p.promotion_id IS NOT NULL  
                 and o.open_status = '1'  
                 group by ap.promotion_id  ";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            $activityOne = array();
            foreach ($dataList as &$dataVar) {
                if ($dataVar['activity_istemp'] == '0' && $dataVar['activity_tempurl']) {
                    //自定义
                    if ($paramArray['school_id']) {
                        $activityOne['activity_PersonalUrl'] = $dataVar['activity_tempurl'] . "?activity_id={$paramArray['activity_id']}&school_id={$paramArray['school_id']}&promotion_id={$dataVar['promotion_id']}&language_type={$paramArray['language_type']}";
                    } else {
                        $activityOne['activity_PersonalUrl'] = $dataVar['activity_tempurl'] . "?activity_id={$paramArray['activity_id']}&promotion_id={$dataVar['promotion_id']}&language_type={$paramArray['language_type']}";
                    }
                } else {
                    //模板
                    $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url", "activitytemp_id='{$dataVar['activitytemp_id']}'");
                    if ($_SERVER['SERVER_NAME'] != 'crmshare.kedingdang.com') {
                        $activitytemp_url = str_replace("https://crmshare.kedingdang.com", "http://crmshare.kcclassin.com", $activitytempOne['activitytemp_url']);
                    }
                    if ($paramArray['school_id']) {
                        $activityOne['activity_PersonalUrl'] = $activitytemp_url . "?activity_id={$paramArray['activity_id']}&school_id={$paramArray['school_id']}&promotion_id={$dataVar['promotion_id']}&language_type={$paramArray['language_type']}";
                    } else {
                        $activityOne['activity_PersonalUrl'] = $activitytemp_url . "?activity_id={$paramArray['activity_id']}&promotion_id={$dataVar['promotion_id']}&language_type={$paramArray['language_type']}";
                    }
                }

                $dataVar['personalqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_PersonalUrl']);//个人二维码
                $dataVar['personalqrcodeurl'] = $activityOne['activity_PersonalUrl'];//个人的活动地址


                //判单是不是企微码
                if($dataVar['activity_codetype'] == '1' && $school_id > 0 && $paramArray['company_id'] == '8888'){
                    $dataVar['personalQRcodeUrl']='';
                    $schoolOne = $this->DataControl->selectOne("select school_id,school_branch from smc_school where company_id = '8888' and school_id = '{$school_id}' ");
                    if($schoolOne){
                        $data = array();
                        $data['xiaoqubianhao'] = $schoolOne['school_branch'];
                        $data['qudao'] = '1_'.$paramArray['activity_id'].'_'.$school_id.'_2_'.$dataVar['promotion_id'];//（1校2园）-（活动ID）-（学校ID）-（职工类型 1 职工 2 地推人）-（职工ID）
                        $getBackurl = request_by_curl("https://eduappv2.kidcastle.com.cn/api/kddqiwei/lianxiwoerweima", dataEncode($data), "GET");
                        $bakData = json_decode($getBackurl, true);
                        if ($bakData['error'] == '0') {
                            $dataVar['personalQRcodeUrl'] = $bakData['data']['xiaoqu'][$schoolOne['school_branch']]['erweimadizhi'];
                        }
                    }
                }else{
                    //小程序二维码
                    if ($paramArray['school_id']) {
                        $personalqrcode = "activity_id={$paramArray['activity_id']}&school_id={$paramArray['school_id']}&promotion_id={$dataVar['promotion_id']}&language_type={$paramArray['language_type']}&iswidth=100%";
                    } else {
                        $personalqrcode = "activity_id={$paramArray['activity_id']}&promotion_id={$dataVar['promotion_id']}&language_type={$paramArray['language_type']}&iswidth=100%";
                    }

                    if ($_SERVER['SERVER_NAME'] == 'crmapi.kedingdang.com' || $_SERVER['SERVER_NAME'] == 'gmcapi.kedingdang.com') {
                        $dataVar['personalQRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getQRcode?" . $personalqrcode;
                    }else{
                        $dataVar['personalQRcodeUrl'] = "http://crmapi.kcclassin.com/Api/getQRcode?" . $personalqrcode;
                    }
                }
            }
        }

        return $dataList;
    }

    public function __call($method, $args)
    {

    }
}
