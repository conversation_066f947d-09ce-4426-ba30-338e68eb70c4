<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/12
 * Time: 22:59
 */

namespace Model\Crm;

class DdMessageModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    const DdMisDomain = "http://**************:8083";

    function __construct()
    {
        parent::__construct();
    }

    //添加消息推送日志
    function addSendDdMessageLog($from_id,$uuid,$subjson){
        $data = array();
        $data['from_id'] = $from_id;
        $data['noticelog_uuid'] = $uuid;
        $data['noticelog_json'] = $subjson;
        $data['noticelog_createtime'] = time();
        $this->DataControl->insertData("crm_dd_noticelog", $data);
    }
    //添加消息推送错误返回日志
    function addSendDdMessageErrorLog($from_id,$uuid,$subjson,$back_id,$back_requestid){
        $data = array();
        $data['from_id'] = $from_id;

        $data['schedulelog_back_id'] = $back_id;
        $data['schedulelog_back_requestid'] = $back_requestid;

        $data['schedulelog_uuid'] = $uuid;
        $data['schedulelog_json'] = $subjson;
        $data['schedulelog_createtime'] = time();
        $this->DataControl->insertData("crm_dd_noticelog", $data);
    }

    //发送消息类 MarkDown 格式
    function sendNoticeMarkDown($paramArray){
        $param = [
            "source" => 1,
            "trigger" => 1,
            "service_channel" => 1,
            "categrate" => [100,102],
            "userid_list" => $paramArray['userid_list'],
            "title" => $paramArray['title'],
            "content" => $paramArray['content'],
        ];
//            "userid_list" => "619268945",
//            "title" => "这是一个测试",
//            "content" => "校区：显示校区名称  \n  客户姓名：xx姓名  \n  手机号：182****9761  \n  渠道来源：空军-大众点评，渠道通路+渠道明细  \n  名单进入时间：名单进入时间"

        $uuid = generateUUID();
        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Req-id:".$uuid;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest(self::DdMisDomain."/api/msg/notify/markdown", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);
        if($bakData['code'] == 200){

            //消息推送记录
            $this->addSendDdMessageLog($paramArray['kddfrom_id'],$uuid,json_encode($param,JSON_UNESCAPED_UNICODE));

            $this->error = 0;
            $this->errortip = "推送成功";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "推送失败";
            return false;
        }
    }

    //通知消息类 -- 发送 卡片 消息
    function sendNoticeCard($paramArray){
        $param = [
            "source" => 1,
            "trigger" => 1,
            "service_channel" => 2,
            "categrate" => [100,102],
            "userid_list" => $paramArray['userid_list'],
            "title" => $paramArray['title'],
            "content" => $paramArray['content'],
            "card_type" => $paramArray['card_type']?$paramArray['card_type']:'1',
            "card" => [
                "single_title" => $paramArray['single_title'],
                "single_url" => $paramArray['single_url'],
            ],
        ];
//            "userid_list" => "619268945",
//            "title" => "这是一个测试11",
//            "content" => "校区：显示校区名称11  \n  客户姓名：xx姓名  \n  手机号：182****9761  \n  渠道来源：空军-大众点评，渠道通路+渠道明细  \n  名单进入时间：名单进入时间",
//            "card_type" => 1,
//            "card" => [
//            "single_title" => "看看去1",
//            "single_url" => "dingtalk://dingtalkclient/page/link?url=".$gotourl."&pc_slide=false",

        $uuid = generateUUID();
        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Req-id:".$uuid;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest(self::DdMisDomain."/api/msg/notify/card", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);

        if($bakData['code'] == 200){

            //消息推送记录
            $this->addSendDdMessageLog($paramArray['kddfrom_id'],$uuid,json_encode($param,JSON_UNESCAPED_UNICODE));

            $this->error = 0;
            $this->errortip = "推送成功";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "推送失败";
            return false;
        }
    }



    //通知消息类 -- 发送 待办 消息
    function sendScheduleTask($paramArray){
        $gotourl = "https://www.kedingdang.com";

        $param = [
            "source" => 1,
            "trigger" => 1,
            "service_channel" => 2,
            "categrate" => [100,102],
            "userid_list" => "619268945",
            "title" => "这是一个测试11",
            "content" => "校区：显示校区名称11  \n  客户姓名：xx姓名  \n  手机号：182****9761  \n  渠道来源：空军-大众点评，渠道通路+渠道明细  \n  名单进入时间：名单进入时间",
            "card_type" => 1,
            "card" => [
                "single_title" => "看看去1",
                "single_url" => "dingtalk://dingtalkclient/page/link?url=".$gotourl."&pc_slide=false",
            ],
        ];

        $uuid = generateUUID();
        $header = array();
        $header[] = "Accept: application/json";
        $header[] = "Content-type: application/json;charset='utf-8'";
        $header[] = "Req-id:".$uuid;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest(self::DdMisDomain."/api/msg/notify/card", json_encode($param), "POST",$header);
        $bakData = json_decode($getBackJson, true);

        if($bakData['code'] == 200){

            //待办推送记录
            $this->addSendDdMessageErrorLog($paramArray['from_id'],$uuid,json_encode($param,JSON_UNESCAPED_UNICODE),$bakData['data']['id'],$bakData['data']['requestId']);

            $this->error = 0;
            $this->errortip = "推送成功";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "推送失败";
            return false;
        }
    }


}