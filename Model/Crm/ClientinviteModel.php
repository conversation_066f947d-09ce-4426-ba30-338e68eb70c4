<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Crm;

class  ClientinviteModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $marketerOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $marketer_id = 0;//操作人
    public $publicarray = array();
    public $company_isassist = 0;
    public $isGmcPost = null;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    //对应参数
    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist", "company_id='{$publicarray['company_id']}'");
            $this->company_isassist = $companyOne['company_isassist'];
            $this->isGmcPost = $this->isGmcPost($publicarray['re_postbe_id'], $publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['marketer_id'])) {
            if (!$this->verdictStaffer($publicarray['marketer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($marketer_id)
    {
        $this->marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id,marketer_name,marketer_mobile", "marketer_id = '{$marketer_id}'");
        if (!$this->marketerOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    /**
     * @param $paramArray
     * @return array
     *   获取客户柜询列表
     */
    function getClientinviteList($paramArray)
    {
        $datawhere = " 1  and ci.school_id='{$paramArray['school_id']}' and ci.company_id='{$paramArray['company_id']}'  ";
        $stu_datawhere = "si.school_id ='{$paramArray['school_id']}' and si.company_id='{$paramArray['company_id']}'";

        //不是高管权限 只显示自己负责的   //有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场 20230606
        if(isset($paramArray['postbe_crmuserlevel']) && $paramArray['postbe_crmuserlevel'] == 2){
            $this->error = true;
            $this->errortip = "您没有权限查看";
            return array();
        }elseif (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] != 1  && $paramArray['postbe_crmuserlevel'] != 3)) {
            $datawhere .= " and ( m.marketer_id ='{$paramArray['marketer_id']}' 
                or (select pl.principal_id from crm_client_principal as pl where pl.client_id = ci.client_id and pl.principal_ismajor = 0 and pl.principal_leave = 0 and ci.school_id = pl.school_id and pl.marketer_id ='{$paramArray['marketer_id']}' limit 0,1  )) ";
            $stu_datawhere .= " and ( m.marketer_id ='{$paramArray['marketer_id']}'  
                or (select pl.principal_id from crm_student_principal as pl where pl.student_id = si.student_id and pl.principal_ismajor = 0 and pl.principal_leave = 0 and si.school_id = pl.school_id and pl.marketer_id ='{$paramArray['marketer_id']}'  limit 0,1  )) ";
        }

        if (isset($paramArray['main_marketer_id']) && $paramArray['main_marketer_id'] !== "") {
            $datawhere .= " and m.marketer_id ={$paramArray['main_marketer_id']}";
            $stu_datawhere .= " and m.marketer_id ={$paramArray['main_marketer_id']}";
        }
        if (isset($paramArray['crm_stu_type']) && $paramArray['crm_stu_type'] !== '') {
            if ($paramArray['crm_stu_type'] == 1) {
                $stu_datawhere .= " and si.invite_id=0";
            } elseif ($paramArray['crm_stu_type'] == 2) {
                $datawhere .= " and ci.invite_id=0";
            }
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= " and ci.coursetype_id='{$paramArray['coursetype_id']}' ";
            $stu_datawhere .= " and si.coursetype_id='{$paramArray['coursetype_id']}' ";
        }
        if (isset($paramArray['invite_isvisit']) && $paramArray['invite_isvisit'] !== '') {
            $datawhere .= " and ci.invite_isvisit='{$paramArray['invite_isvisit']}' ";
            $stu_datawhere .= " and si.invite_isvisit='{$paramArray['invite_isvisit']}' ";
        }
        //针对全部里边 前端要求换个名字筛选
        if (isset($paramArray['invite_allisvisit']) && $paramArray['invite_allisvisit'] !== '') {
            $datawhere .= " and ci.invite_isvisit='{$paramArray['invite_allisvisit']}' ";
            $stu_datawhere .= " and si.invite_isvisit='{$paramArray['invite_allisvisit']}' ";
        }
//        else {
//            $datawhere .= " and ci.invite_isvisit='0' ";
//            $stu_datawhere .= " and si.invite_isvisit='0' ";
//        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_frompage like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            $stu_datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_cnname like '%{$paramArray['keyword']}%' or pt.parenter_mobile like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_student_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=s.student_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['invite_visittime']) && $paramArray['invite_visittime'] !== "") {
            $paramArray['invite_visittime'] = explode(',', "{$paramArray['invite_visittime']}");
            if ($paramArray['invite_visittime'][0] == $paramArray['invite_visittime'][1]) {
                $paramArray['invite_visittime'][0] = date('Y-m-d', strtotime($paramArray['invite_visittime'][0]));
                $datawhere .= " and ci.invite_visittime = '{$paramArray['invite_visittime'][0]}'";
                $stu_datawhere .= " and si.invite_visittime = '{$paramArray['invite_visittime'][0]}'";
            } else {
                $paramArray['invite_visittime'][0] = date('Y-m-d', strtotime($paramArray['invite_visittime'][0]));
                $paramArray['invite_visittime'][1] = date('Y-m-d', strtotime($paramArray['invite_visittime'][1]));
                $datawhere .= " and ci.invite_visittime>='{$paramArray['invite_visittime'][0]}' and ci.invite_visittime<= '{$paramArray['invite_visittime'][1]}'";
                $stu_datawhere .= " and si.invite_visittime>='{$paramArray['invite_visittime'][0]}' and si.invite_visittime<= '{$paramArray['invite_visittime'][1]}'";
            }
        }
        if (isset($paramArray['invite_starttime']) && $paramArray['invite_starttime'] !== "" && isset($paramArray['invite_endtime']) && $paramArray['invite_endtime'] !== "") {
            $datawhere .= " and DATE_FORMAT(ci.invite_visittime,'%Y-%m-%d')  <= '{$paramArray['invite_endtime']}'  and DATE_FORMAT(ci.invite_visittime,'%Y-%m-%d') >= '{$paramArray['invite_starttime']}' ";
            $stu_datawhere .= " and DATE_FORMAT(si.invite_visittime,'%Y-%m-%d')  <= '{$paramArray['invite_endtime']}'  and si.invite_visittime >= '{$paramArray['invite_starttime']}' ";
        } elseif (isset($paramArray['invite_starttime']) && $paramArray['invite_starttime'] !== "") {
            $datawhere .= "  and DATE_FORMAT(ci.invite_visittime,'%Y-%m-%d') >= '{$paramArray['invite_starttime']}'";
            $stu_datawhere .= "  and DATE_FORMAT(si.invite_visittime,'%Y-%m-%d') >= '{$paramArray['invite_starttime']}'";
        } elseif (isset($paramArray['invite_endtime']) && $paramArray['invite_endtime'] !== "") {
            $datawhere .= " and DATE_FORMAT(ci.invite_visittime,'%Y-%m-%d') <= '{$paramArray['invite_endtime']}'";
            $stu_datawhere .= " and DATE_FORMAT(si.invite_visittime,'%Y-%m-%d') <= '{$paramArray['invite_endtime']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sqlfields = 'c.client_id,c.client_cnname,c.client_enname,c.client_sex,c.client_age,c.client_mobile,c.client_tag,c.client_tracestatus,c.client_remark,c.client_sponsor,c.client_address,c.client_intention_level,c.client_source,c.client_createtime,c.client_email';
        $sqlfields .= ",ci.invite_id,ci.invite_genre,ci.invite_visittime,ci.invite_isvisit,(ci.invite_isvisit) as is_visit_autition,ci.receiver_name,ci.invite_novisitreason,ci.coursetype_id,(select cu.coursetype_cnname from smc_code_coursetype as cu where cu.coursetype_id = ci.coursetype_id) as coursetype_cnname ";
        $sqlfields .= ",s.school_cnname,m.marketer_name,sf.staffer_enname as marketer_name_en,nl.nearschool_name,sf.staffer_enname,invite_createtime,'0' as invite_type,mr.marketer_name as marketer_writername  ";
        $stu_field = "s.student_id as client_id,s.student_cnname as client_cnname,s.student_enname as client_enname,s.student_sex as client_sex,'' as client_age,pt.parenter_mobile as client_mobile,'' as client_tag,'' as client_tracestatus,'' as client_remark,'' as client_sponsor,'' as client_address,cs.student_intention_level as client_intention_level,'' as client_source,s.student_createtime as client_createtime,pt.parenter_email as client_email";
        $stu_field .= ",si.invite_id,si.invite_genre,si.invite_visittime,si.invite_isvisit,(si.invite_isvisit) as is_visit_autition,si.receiver_name,si.invite_novisitreason,si.coursetype_id,(select cu.coursetype_cnname from smc_code_coursetype as cu where cu.coursetype_id = si.coursetype_id) as coursetype_cnname";
        $stu_field .= ",sl.school_cnname,m.marketer_name,sf.staffer_enname as marketer_name_en,'' as nearschool_name,sf.staffer_enname,invite_createtime,'1' as invite_type ,'' as marketer_writername ";
        $sql = "select  {$sqlfields},
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id, 
                (select cg.conversionlog_id from crm_client_conversionlog as cg,smc_course as cu where cg.school_id='{$paramArray['school_id']}' and cg.client_id = ci.client_id and cg.course_branch = cu.course_branch AND cu.coursetype_id = '{$paramArray['cg_coursetype_id']}' order by cg.conversionlog_time desc limit 0,1) as conversionlog_id,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) as marketer_name,
                (select GROUP_CONCAT(o.coursecat_branch) FROM crm_client_intention as i LEFT JOIN smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
				WHERE i.client_id=c.client_id ) as course_cnname,
				(select p.parenter_cnname
				FROM crm_client_family as f
				LEFT JOIN  smc_parenter as p ON p.parenter_id = f.parenter_id
				WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
				(select positivelog_id from crm_client_positivelog as g where g.client_id=ci.client_id) as positivelog_id,
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=c.channel_id limit 0,1 ) as channel_name,
                (select r.region_name  from smc_code_region as r where c.province_id=r.region_id ) as province_name,
				(select r.region_name  from smc_code_region as r where c.city_id=r.region_id ) as city_name,
				(select r.region_name  from smc_code_region as r where c.area_id=r.region_id ) as area_name,
				(select sa.activity_name  from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name,
			    (select group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
				from crm_client_principal as p
				Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
				left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
				WHERE p.client_id = c.client_id and p.principal_leave= 0 and p.principal_ismajor=0 and  p.school_id={$paramArray['school_id']}  ) as fu_marketer_name,
				IFNULL(gp.promotion_jobnumber,'--') as promotion_jobnumber,gp.promotion_type as post_name,c.client_frompage
				from crm_client_invite as ci
				left JOIN crm_client as c ON ci.client_id = c.client_id
				left JOIN smc_school as s ON s.school_id = ci.school_id
				left JOIN crm_client_principal as p ON p.client_id = ci.client_id and principal_ismajor = 1 and p.principal_leave = 0 and ci.school_id = p.school_id
				left JOIN crm_marketer as m ON m.marketer_id = p.marketer_id
				left JOIN crm_marketer as mr ON mr.marketer_id = c.marketer_id
				left JOIN smc_staffer as sf On sf.staffer_id = m.staffer_id
				left JOIN crm_code_nearschool as nl ON nl.nearschool_id = c.nearschool_id
				left JOIN crm_ground_promotion as gp ON gp.promotion_id = c.promotion_id
				where {$datawhere}  
				UNION 
				select 
				{$stu_field}, '' as gmcschoolenter_id,'' as gmcIndschoolenter_id,'' as conversionlog_id,
				concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) as marketer_name,
				(select GROUP_CONCAT(oo.coursecat_branch) FROM crm_student_intention as ii LEFT JOIN smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id
				WHERE ii.student_id=s.student_id ) as course_cnname,
				pt.parenter_cnname, '' as positivelog_id, '' as channel_name, '' as province_name, '' as city_name, '' as area_name, '' as activity_name,
				(select  group_concat(concat(mk.marketer_name ,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END ) ) )
				from crm_student_principal as p
				Left JOIN  crm_marketer as mk ON mk.marketer_id = p.marketer_id
				left join smc_staffer as sf On sf.staffer_id = mk.staffer_id
				WHERE p.student_id = si.student_id and p.principal_leave= 0 and p.principal_ismajor=0 and  p.school_id={$paramArray['school_id']}  ) as fu_marketer_name,
				'--' as promotion_jobnumber,null as post_name,'' as client_frompage
				from crm_student_invite as si
				left join smc_student as s On s.student_id = si.student_id
				left join smc_school as sl ON sl.school_id = si.school_id
				left join crm_student_principal as p ON p.student_id = s.student_id and p.principal_ismajor = 1 and p.principal_leave = 0 and si.school_id = p.school_id
				left join crm_marketer as m ON m.marketer_id = p.marketer_id
				left join smc_student_family as f ON f.student_id = s.student_id and f.family_isdefault = 1
				left join smc_parenter as pt On pt.parenter_id = f.parenter_id 
				left join crm_student as cs On cs.student_id = s.student_id
				left join smc_staffer as sf ON sf.staffer_id = m.staffer_id
				where {$stu_datawhere}  
				ORDER BY invite_createtime DESC
				";

        //统计数量
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear($sql);

            if ($all_num) {
                $allnums = count($all_num) + 0;
            } else {
                $allnums = 0;
            }
            $data['allnums'] = $allnums;
        }

        $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");

        $genre = $this->LgArraySwitch(array('0' => '邀约-普通柜询', '1' => '邀约-能力测试', '2' => '来访-推带到访', '3' => '来访-主动到访'));//0普通柜询1能力测试 2推带到访 3 主动到访
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            if ($dateexcelarray) {
//                $genre = $this->LgArraySwitch(array('0'=>'邀约-普通柜询','1'=>'邀约-能力测试','2'=>'来访-推带到访','3'=>'来访-主动到访'));//0普通柜询1能力测试 2推带到访 3 主动到访
                foreach ($dateexcelarray as $key => $value) {
                    if ($value['is_visit_autition'] == '1') {
                        $dateexcelarray[$key]['invite_isvisit_name'] = '已到访';
                    } else if ($value['is_visit_autition'] == '-1') {
                        $dateexcelarray[$key]['invite_isvisit_name'] = '未到访';
                    } else if ($value['is_visit_autition'] == '0') {
                        $dateexcelarray[$key]['invite_isvisit_name'] = '待确认';
                    } else if ($value['is_visit_autition'] == '2') {
                        $dateexcelarray[$key]['invite_isvisit_name'] = '已改约';
                    }
                    $dateexcelarray[$key]['invite_genre'] = $genre[$value['invite_genre']];
                    $dateexcelarray[$key]['invite_type_name'] = $value['invite_type'] == '0' ? $this->LgStringSwitch("新生跟踪") : $this->LgStringSwitch("流失跟踪");
                    $dateexcelarray[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);

                    if($value['invite_isvisit'] == 2){
                        $dateexcelarray[$key]['invite_novisitreason_two'] = $value['invite_novisitreason'];
                        $dateexcelarray[$key]['invite_novisitreason'] = '--';
                    }else{
                        $dateexcelarray[$key]['invite_novisitreason_two'] = '--';
                    }

                    if (is_null($value['post_name'])) {
                        $dateexcelarray[$key]['post_name'] = '--';
                    } else {
                        if ($value['post_name'] == 0) {
                            $dateexcelarray[$key]['post_name'] = '市场';
                        } else {
                            $dateexcelarray[$key]['post_name'] = '销售';
                        }
                    }
                    if (!$value['client_frompage']) {
                        $dateexcelarray[$key]['client_frompage'] = '--';
                    }
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_tag'] = $this->LgStringSwitch($dateexcelvar['client_tag']);
                    $datearray['invite_type_name'] = $dateexcelvar['invite_type_name'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['invite_novisitreason'] = $dateexcelvar['invite_novisitreason'];
                    $datearray['invite_novisitreason_two'] = $dateexcelvar['invite_novisitreason_two'];
                    $datearray['invite_genre'] = $dateexcelvar['invite_genre'];
                    $datearray['invite_visittime'] = $dateexcelvar['invite_visittime'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
//                    if ($staffone['account_class'] == '1') {
//                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
//                    } else {
//                        $datearray['client_mobile'] = str_replace(substr($dateexcelvar['client_mobile'], 3, 4), '****', $dateexcelvar['client_mobile']);;
                        $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);;;
//                    }
                    $datearray['client_email'] = $dateexcelvar['client_email'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];

                    $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];

                    $datearray['receiver_name'] = $dateexcelvar['receiver_name'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['city_name'] = $dateexcelvar['city_name'];
                    $datearray['area_name'] = $dateexcelvar['area_name'];
                    $datearray['nearschool_name'] = $dateexcelvar['nearschool_name'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
//                    $datearray['marketer_writername'] = $dateexcelvar['marketer_writername'];
                    $outexceldate[] = $datearray;
                }
            }

            if ($this->company_isassist == 0) {
                $excelheader = $this->LgArraySwitch(array('姓名', '英文名', '性别', '年龄', '标签', '学员类型', '意向星级', '柜询班组', '未到访原因', '改约原因', '柜询类型', '柜询日期', '主要联系人', '主要联系电话','Email', '意向课程', '地推工号', '职务', '接触点', '接待人', '主要负责人', '活动', '渠道类型', '渠道明细', '推荐人', '联系地址', '省', '市', '区', '附近的学校', '备注', "创建时间"));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'invite_type_name', 'client_intention_level', 'coursetype_cnname', 'invite_novisitreason', 'invite_novisitreason_two', 'invite_genre', 'invite_visittime', 'family_cnname', 'client_mobile','client_email', 'course_cnname', 'promotion_jobnumber', 'post_name', 'client_frompage', 'receiver_name', 'marketer_name', "activity_name", "client_source", "channel_name", "client_sponsor", 'client_address', 'province_name', 'city_name', 'area_name', 'nearschool_name', 'client_remark', 'client_createtime');
            } else {
                $excelheader = $this->LgArraySwitch(array('姓名', '英文名', '性别', '年龄', '标签', '学员类型', '意向星级', '柜询班组', '未到访原因', '改约原因', '柜询类型', '柜询日期', '主要联系人', '主要联系电话','Email', '意向课程', '地推工号', '职务', '接触点', '接待人', '主要负责人', '协助负责人', '活动', '渠道类型', '渠道明细', '推荐人', '联系地址', '省', '市', '区', '附近的学校', '备注', "创建时间"));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'invite_type_name', 'client_intention_level', 'coursetype_cnname', 'invite_novisitreason', 'invite_novisitreason_two', 'invite_genre', 'invite_visittime', 'family_cnname', 'client_mobile','client_email', 'course_cnname', 'promotion_jobnumber', 'post_name', 'client_frompage', 'receiver_name', 'marketer_name', "fu_marketer_name", "activity_name", "client_source", "channel_name", "client_sponsor", 'client_address', 'province_name', 'city_name', 'area_name', 'nearschool_name', 'client_remark', 'client_createtime');
            }
            $fielname = $this->LgStringSwitch("柜询名单记录表");
//
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $clientinviteList = $this->DataControl->selectClear($sql);
            if ($clientinviteList) {
                foreach ($clientinviteList as $key => $value) {
                    if ($value['is_visit_autition'] == '1') {
                        $clientinviteList[$key]['invite_isvisit_name'] = '已到访';
                    } else if ($value['is_visit_autition'] == '-1') {
                        $clientinviteList[$key]['invite_isvisit_name'] = '未到访';
                    } else if ($value['is_visit_autition'] == '0') {
                        $clientinviteList[$key]['invite_isvisit_name'] = '待确认';
                    } else if ($value['is_visit_autition'] == '2') {
                        $clientinviteList[$key]['invite_isvisit_name'] = '已改约';
                    }
                    if (!is_null($value['course_cnname'])) {
                        $clientinviteList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                    } else {
                        $clientinviteList[$key]['course_cnname'] = array();
                    }

                    $clientinviteList[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                    $clientinviteList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                    $clientinviteList[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    $clientinviteList[$key]['invite_genre'] = $genre[$value['invite_genre']];
                    if (is_null($value['coursetype_cnname']) || $value['coursetype_cnname'] == '') {
                        $clientinviteList[$key]['coursetype_cnname'] = '--';
                    } else {
                        $clientinviteList[$key]['coursetype_cnname'] = $value['coursetype_cnname'];
                    }
                    if($value['invite_isvisit'] == 2){
                        $clientinviteList[$key]['invite_novisitreason_two'] = $value['invite_novisitreason'];
                        $clientinviteList[$key]['invite_novisitreason'] = '--';
                    }else{
                        $clientinviteList[$key]['invite_novisitreason_two'] = '--';
                    }
                    // 是否禁止跟进按钮
                    if ($value['client_tracestatus'] == '-1' || $value['client_tracestatus'] == '-2' || $value['client_tracestatus'] == '4') {
                        $clientinviteList[$key]['is_forbid'] = '1';
                    } else {
                        $clientinviteList[$key]['is_forbid'] = '0';
                    }
                    if ($clientinviteList[$key]['positivelog_id']) {
                        $clientinviteList[$key]['positivelog_name'] = "已报名";
                    } else {
                        $clientinviteList[$key]['positivelog_name'] = "未报名";
                    }
                    //筛选 报名的班组是否报名
                    if (isset($paramArray['cg_coursetype_id']) && $paramArray['cg_coursetype_id'] > 1) {
                        if ($clientinviteList[$key]['conversionlog_id'] > 1) {
                            $clientinviteList[$key]['conversionlog_id_name'] = "已报名";
                        } else {
                            $clientinviteList[$key]['conversionlog_id_name'] = "未报名";
                        }
                    } else {
                        $clientinviteList[$key]['conversionlog_id_name'] = "--";
                    }
                    $clientinviteList[$key]['invite_type_name'] = $value['invite_type'] == '0' ? $this->LgStringSwitch("新生跟踪") : $this->LgStringSwitch("流失跟踪");

                    if ($this->isGmcPost == true) {
//                        $clientinviteList[$key]['client_mobile'] = str_replace(substr($value['client_mobile'], 3, 4), '****', $value['client_mobile']);
                        $clientinviteList[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);
                    } else {
                        $clientinviteList[$key]['client_mobile'] = $value['client_mobile'];
                    }
                    $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                    if ($isoutCity) {
                        if ($isoutCity == 'local') {
                            $clientinviteList[$key]['isoutmobile'] = 0;
                        } else {
                            $clientinviteList[$key]['isoutmobile'] = 1;
                        }
                    } else {
                        $clientinviteList[$key]['isoutmobile'] = -1;
                    }
                    if ($value['client_tag']) {
                        $clientinviteList[$key]['client_tag'] = explode(',', $this->LgStringSwitch($value['client_tag']));
                    } else {
                        $clientinviteList[$key]['client_tag'] = array();
                    }
                    if (is_null($value['post_name'])) {
                        $clientinviteList[$key]['post_name'] = '--';
                    } else {
                        if ($value['post_name'] == 0) {
                            $clientinviteList[$key]['post_name'] = '市场';
                        } else {
                            $clientinviteList[$key]['post_name'] = '销售';
                        }
                    }
                    if (!$value['client_frompage']) {
                        $clientinviteList[$key]['client_frompage'] = '--';
                    }
                }
            }
            $data['list'] = $clientinviteList;
            return $data;
        }
    }

    /**
     * @param $paramArray
     * @return array
     *   获取客户试听列表
     */
    function getClientauditionList($paramArray)
    {
        $datawhere = " 1  and ci.school_id='{$paramArray['school_id']}' and ci.company_id='{$paramArray['company_id']}' ";
        $stu_datawhere = " 1  and ci.school_id='{$paramArray['school_id']}' and ci.company_id='{$paramArray['company_id']}' ";

        //不是高管权限 只显示自己负责的
        if(isset($paramArray['postbe_crmuserlevel']) && $paramArray['postbe_crmuserlevel'] == 2){
            $this->error = true;
            $this->errortip = "您没有权限查看";
            return array();
        }elseif (isset($paramArray['postbe_crmuserlevel']) && ($paramArray['postbe_crmuserlevel'] != 1  && $paramArray['postbe_crmuserlevel'] != 3)) {
            $datawhere .= " and ( m.marketer_id ='{$paramArray['marketer_id']}' 
                or (select pl.principal_id from crm_client_principal as pl where pl.client_id = ci.client_id and pl.principal_ismajor = 0 and pl.principal_leave = 0 and ci.school_id = pl.school_id and pl.marketer_id ='{$paramArray['marketer_id']}' limit 0,1  )) ";
            $stu_datawhere .= " and ( m.marketer_id ='{$paramArray['marketer_id']}'  
                or (select pl.principal_id from crm_student_principal as pl where pl.student_id = ci.student_id and pl.principal_ismajor = 0 and pl.principal_leave = 0 and ci.school_id = pl.school_id and pl.marketer_id ='{$paramArray['marketer_id']}' limit 0,1  )) ";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_frompage like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            $stu_datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or pt.parenter_mobile like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_student_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=s.student_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= " and ci.coursetype_id='{$paramArray['coursetype_id']}' ";
            $stu_datawhere .= " and ci.coursetype_id='{$paramArray['coursetype_id']}' ";
        }
        if (isset($paramArray['audition_isvisit']) && $paramArray['audition_isvisit'] !== "") {
            $datawhere .= " and ci.audition_isvisit ='{$paramArray['audition_isvisit']}'";
            $stu_datawhere .= " and ci.audition_isvisit ='{$paramArray['audition_isvisit']}'";
        }
        //针对全部里边 前端要求换个名字筛选
        if (isset($paramArray['audition_allisvisit']) && $paramArray['audition_allisvisit'] !== "") {
            $datawhere .= " and ci.audition_isvisit ='{$paramArray['audition_allisvisit']}'";
            $stu_datawhere .= " and ci.audition_isvisit ='{$paramArray['audition_allisvisit']}'";
        }
//        else {
//            $datawhere .= " and ci.audition_isvisit = '0'";
//            $stu_datawhere .= " and ci.audition_isvisit = '0'";
//        }

        $datahaving = '';
        if (isset($paramArray['crm_stu_type']) && $paramArray['crm_stu_type'] !== '') {
            if ($paramArray['crm_stu_type'] == 1) {
                $datahaving .= " HAVING isstuclient='0' ";
            } elseif ($paramArray['crm_stu_type'] == 2) {
                $stu_datawhere .= " and cs.student_type=1";
                $datahaving .= " HAVING isstuclient='1' ";
            } elseif ($paramArray['crm_stu_type'] == 3) {
                $stu_datawhere .= " and cs.student_type=0";
                $datahaving .= " HAVING isstuclient='1' ";
            }
        }

        if (isset($paramArray['main_marketer_id']) && $paramArray['main_marketer_id'] !== "") {
            $datawhere .= " and m.marketer_id ='{$paramArray['main_marketer_id']}'";
            $stu_datawhere .= " and m.marketer_id ='{$paramArray['main_marketer_id']}'";
        }

        if (isset($paramArray['audition_genre']) && $paramArray['audition_genre'] !== "") {
            $datawhere .= " and ci.audition_genre ='{$paramArray['audition_genre']}'";
            $stu_datawhere .= " and ci.audition_genre ='{$paramArray['audition_genre']}'";
        }

        //20200114 97修改
        if (isset($paramArray['audition_starttime']) && $paramArray['audition_starttime'] !== "" && isset($paramArray['audition_endtime']) && $paramArray['audition_endtime'] !== "") {
            $datawhere .= " and ci.audition_visittime  <= '{$paramArray['audition_endtime']} 23:59:59'  and ci.audition_visittime >= '{$paramArray['audition_starttime']}' ";
            $stu_datawhere .= " and ci.audition_visittime  <= '{$paramArray['audition_endtime']} 23:59:59'  and ci.audition_visittime >= '{$paramArray['audition_starttime']}' ";
        } elseif (isset($paramArray['audition_starttime']) && $paramArray['audition_starttime'] !== "") {
            $datawhere .= " and ci.audition_visittime >= '{$paramArray['audition_starttime']}'";
            $stu_datawhere .= " and ci.audition_visittime >= '{$paramArray['audition_starttime']}'";
        } elseif (isset($paramArray['audition_endtime']) && $paramArray['audition_endtime'] !== "") {
            $datawhere .= " and ci.audition_visittime <= '{$paramArray['audition_endtime']} 23:59:59'";
            $stu_datawhere .= " and ci.audition_visittime <= '{$paramArray['audition_endtime']} 23:59:59'";
        }
        $datawhereclass = $datawhere;
        $stu_datawhereclass = $stu_datawhere;

        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== "") {
            $datawhere .= " and ci.class_id ='{$paramArray['class_id']}'";
            $stu_datawhere .= " and ci.class_id ='{$paramArray['class_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        //统计数量
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear("
				select ci.audition_id,'0' as isstuclient 
				from crm_client_audition as ci
				LEFT JOIN crm_client as c  ON ci.client_id=c.client_id
				LEFT JOIN smc_school as s ON s.school_id = ci.school_id
				LEFT JOIN crm_client_principal as p ON p.client_id = ci.client_id and  principal_ismajor = 1   and p.principal_leave = 0 and ci.school_id=p.school_id
				LEFT JOIN crm_marketer as m ON m.marketer_id = p.marketer_id
				where {$datawhere} {$datahaving}
				union 
				select 
				 ci.audition_id,'1' as isstuclient 
				from crm_student_audition as ci
				LEFT JOIN smc_student as s ON ci.student_id=s.student_id
				LEFT JOIN smc_class as sc ON sc.class_id = ci.class_id
				LEFT JOIN smc_school as sl ON sl.school_id = ci.school_id
				LEFT JOIN crm_student_principal as p ON p.student_id = ci.student_id and principal_ismajor = 1 and p.principal_leave = 0 and ci.school_id=p.school_id
				LEFT JOIN crm_marketer as m ON m.marketer_id = p.marketer_id
				LEFT JOIN smc_staffer as sf ON  sf.staffer_id = m.staffer_id
				left join smc_student_family as f ON f.student_id = s.student_id and f.family_isdefault =1
                left join smc_parenter as pt On pt.parenter_id =f.parenter_id 
                left join crm_student as cs On cs.student_id=s.student_id
				WHERE {$stu_datawhere}  {$datahaving}
				
				 ");
            $allnums = count($all_num);
            if ($all_num) {
                $data['allnums'] = $allnums;
            } else {
                $data['allnums'] = 0;
            }
        }
        $sqlfields = 'c.client_id,c.client_cnname,c.client_enname,c.client_tracestatus,c.client_sex,c.client_age,c.client_tag,c.client_mobile,c.client_oh_month,c.client_push_month,c.client_email,
        c.client_address,c.client_intention_level,c.client_source,c.client_createtime';
        $sqlfields .= ",ci.receiver_name,ci.audition_id,ci.class_cnname,ci.audition_visittime,(ci.audition_isvisit) as is_visit_autition,ci.audition_genre,ci.audition_novisitreason,
        m.marketer_name,nr.nearschool_name,s.school_cnname,'0' as audition_type,mr.marketer_name as marketer_writername,ci.coursetype_id,
        (select cu.coursetype_cnname from smc_code_coursetype as cu where cu.coursetype_id = ci.coursetype_id) as coursetype_cnname,'' as student_type";

        $stu_sqlfield = " 
        s.student_id as client_id,
        s.student_cnname as client_cnname,
        s.student_enname as client_enname,
         '' as client_tracestatus,
         s.student_sex as client_sex,
        '' as  client_age,
        '' as  client_tag,
        pt.parenter_mobile as client_mobile,
        '' as client_oh_month,
        '' as client_push_month,
        pt.parenter_email as client_email,
        '' as client_address,
        cs.student_intention_level as client_intention_level,
        '' as client_source,
        s.student_createtime as client_createtime
        ";
        $stu_sqlfield .= "
         ,ci.receiver_name,
        ci.audition_id,
        ci.class_cnname,
        ci.audition_visittime,
        (ci.audition_isvisit) as is_visit_autition,
        ci.audition_genre, 
         ci.audition_novisitreason,
         m.marketer_name,
         '' as nearschool_name,
        sl.school_cnname,
         '1' as audition_type,
         '' as marketer_writername,
         ci.coursetype_id,
         (select cu.coursetype_cnname from smc_code_coursetype as cu where cu.coursetype_id = ci.coursetype_id) as coursetype_cnname,
         cs.student_type
        ";
        $sql = "select {$sqlfields},'0' as isstuclient,
                (select cg.conversionlog_id from crm_client_conversionlog as cg,smc_course as cu where cg.school_id='{$paramArray['school_id']}' and cg.client_id = ci.client_id and cg.course_branch = cu.course_branch AND cu.coursetype_id = '{$paramArray['cg_coursetype_id']}' order by cg.conversionlog_time desc limit 0,1) as conversionlog_id,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_gmcdirectschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcschoolenter_id,
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id,
                concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) as marketer_name,
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=c.channel_id limit 0,1 ) as channel_name,
			    (select GROUP_CONCAT(o.coursecat_branch) FROM crm_client_intention as i LEFT JOIN  smc_code_coursecat as o ON o.coursecat_id=i.coursecat_id
				WHERE i.client_id=c.client_id ) as course_cnname,
				(select sa.activity_name from crm_sell_activity as sa where c.activity_id=sa.activity_id ) as activity_name,
				(select ch.channel_name from crm_code_channel as ch where ch.channel_id=c.channel_id ) as channel_name,
				(select r.region_name from smc_code_region as r where c.province_id=r.region_id ) as province_name,
				(select r.region_name from smc_code_region as r where c.city_id=r.region_id ) as city_name,
				(select r.region_name from smc_code_region as r where c.area_id=r.region_id ) as area_name,
				(select group_concat( concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )) from crm_client_principal as pl,crm_marketer as mr,smc_staffer as sf where sf.staffer_id=mr.staffer_id and mr.marketer_id = pl.marketer_id and pl.client_id=ci.client_id and pl.principal_ismajor = 0 and pl.principal_leave = 0 and ci.school_id=pl.school_id) as fu_marketer_name,
				(select p.parenter_cnname from crm_client_family as f,smc_parenter as p where f.parenter_id =p.parenter_id and f.client_id=ci.client_id and f.family_isdefault =1 limit 0,1 ) as parenter_cnname,
				(select positivelog_id from crm_client_positivelog as g where g.client_id=c.client_id limit 0,1 ) as positivelog_id,
				IFNULL(gp.promotion_jobnumber,'--') as promotion_jobnumber,gp.promotion_type as post_name,c.client_frompage
				from crm_client_audition as ci
				LEFT JOIN crm_client as c ON ci.client_id = c.client_id
				LEFT JOIN smc_class as sc ON sc.class_id = ci.class_id
				LEFT JOIN smc_school as s ON s.school_id = ci.school_id
				LEFT JOIN crm_client_principal as p ON p.client_id = ci.client_id and principal_ismajor = 1 and p.principal_leave = 0 and ci.school_id = p.school_id
				LEFT JOIN crm_marketer as m ON m.marketer_id = p.marketer_id
				LEFT JOIN crm_marketer as mr ON mr.marketer_id = c.marketer_id
				LEFT JOIN smc_staffer as sf ON sf.staffer_id = m.staffer_id
				LEFT JOIN crm_code_nearschool as nr ON nr.nearschool_id = c.nearschool_id
				LEFT JOIN crm_ground_promotion as gp ON gp.promotion_id = c.promotion_id
				WHERE {$datawhere} {$datahaving}  
				union 
				select 
				 {$stu_sqlfield},'1' as isstuclient,'' as conversionlog_id ,'' as gmcschoolenter_id,'' as gmcIndschoolenter_id,concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) as marketer_name,
				 '' as channel_name, 
				(select GROUP_CONCAT(oo.coursecat_branch) FROM crm_student_intention as ii LEFT JOIN smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.student_id=s.student_id ) as  course_cnname ,'' as activity_name, '' as channel_name,'' as province_name, '' as city_name, '' as area_name,
				(select group_concat( concat(sf.staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )) from crm_student_principal as pl,crm_marketer as mr,smc_staffer as sf where sf.staffer_id=mr.staffer_id and mr.marketer_id = pl.marketer_id and pl.student_id=ci.student_id and pl.principal_ismajor = 0 and pl.principal_leave = 0 and ci.school_id=pl.school_id) as fu_marketer_name,pt.parenter_cnname,'' as positivelog_id,'--' as promotion_jobnumber,null as post_name,'' as client_frompage
				from crm_student_audition as ci
				LEFT JOIN smc_student as s ON ci.student_id = s.student_id
				LEFT JOIN smc_class as sc ON sc.class_id = ci.class_id
				LEFT JOIN smc_school as sl ON sl.school_id = ci.school_id
				LEFT JOIN crm_student_principal as p ON p.student_id = ci.student_id and principal_ismajor = 1 and p.principal_leave = 0 and ci.school_id = p.school_id
				LEFT JOIN crm_marketer as m ON m.marketer_id = p.marketer_id
				LEFT JOIN smc_staffer as sf ON  sf.staffer_id = m.staffer_id
				LEFT JOIN smc_student_family as f ON f.student_id = s.student_id and f.family_isdefault = 1
                LEFT JOIN smc_parenter as pt On pt.parenter_id = f.parenter_id 
                LEFT JOIN crm_student as cs On cs.student_id = s.student_id
				WHERE {$stu_datawhere} {$datahaving} 
				order by audition_visittime DESC 
				";

        $staffone = $this->DataControl->selectOne("select s.account_class 
                    from crm_marketer as c 
                    LEFT JOIN smc_staffer as s ON c.staffer_id = s.staffer_id  
                    WHERE s.staffer_id = c.staffer_id and c.company_id = '{$paramArray['company_id']}' and c.marketer_id = '{$paramArray['marketer_id']}'
                    limit 0,1");
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            if ($dateexcelarray) {
                foreach ($dateexcelarray as $key => $value) {
                    $dateexcelarray[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                    $dateexcelarray[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                    if ($value['isstuclient'] == 0) {
                        $dateexcelarray[$key]['audition_type_name'] = $this->LgStringSwitch('新生跟踪');
                    } else {
                        if ($value['student_type'] == 0) {
                            $dateexcelarray[$key]['audition_type_name'] = $this->LgStringSwitch('校内跟踪');
                        } else {
                            $dateexcelarray[$key]['audition_type_name'] = $this->LgStringSwitch('流失跟踪');
                        }
                    }
                    $dateexcelarray[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    if ($value['audition_genre'] == 1) {
                        $dateexcelarray[$key]['audition_genre'] = $this->LgStringSwitch('插班试听');
                    } elseif ($value['audition_genre'] == 0) {
                        $dateexcelarray[$key]['audition_genre'] = $this->LgStringSwitch('普通公开课试听');
                    } elseif ($value['audition_genre'] == 2) {
                        $dateexcelarray[$key]['audition_genre'] = $this->LgStringSwitch('试读公开课试听');
                    }
                    if ($value['is_visit_autition'] == 0) {
                        $dateexcelarray[$key]['visitstate'] = $this->LgStringSwitch('待确认');
                    } elseif ($value['is_visit_autition'] == 1) {
                        $dateexcelarray[$key]['visitstate'] = $this->LgStringSwitch('已试听');
                    } elseif ($value['is_visit_autition'] == -1) {
                        $dateexcelarray[$key]['visitstate'] = $this->LgStringSwitch('未试听');
                    } elseif ($value['is_visit_autition'] == 2) {
                        $dateexcelarray[$key]['visitstate'] = $this->LgStringSwitch('已改约');
                    }
                    if($value['is_visit_autition'] == 2){
                        $dateexcelarray[$key]['audition_novisitreason_two'] = $value['audition_novisitreason'];
                        $dateexcelarray[$key]['audition_novisitreason'] = '--';
                    }else{
                        $dateexcelarray[$key]['audition_novisitreason_two'] = '--';
                    }
                    if (is_null($value['post_name'])) {
                        $dateexcelarray[$key]['post_name'] = '--';
                    } else {
                        if ($value['post_name'] == 0) {
                            $dateexcelarray[$key]['post_name'] = '市场';
                        } else {
                            $dateexcelarray[$key]['post_name'] = '销售';
                        }
                    }
                    if (!$value['client_frompage']) {
                        $dateexcelarray[$key]['client_frompage'] = '--';
                    }
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_tag'] = $this->LgStringSwitch($dateexcelvar['client_tag']);
                    $datearray['audition_type_name'] = $dateexcelvar['audition_type_name'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['audition_novisitreason'] = $dateexcelvar['audition_novisitreason'];
                    $datearray['audition_novisitreason_two'] = $dateexcelvar['audition_novisitreason_two'];
                    $datearray['audition_genre'] = $dateexcelvar['audition_genre'];
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
//                    if ($staffone['account_class'] == '1') {
//                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
//                    } else {
//                        $datearray['client_mobile'] = str_replace(substr($dateexcelvar['client_mobile'], 3, 4), '****', $dateexcelvar['client_mobile']);;
                        $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);
//                    }
                    $datearray['client_email'] = $dateexcelvar['client_email'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];

                    $datearray['promotion_jobnumber'] = $dateexcelvar['promotion_jobnumber'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];

                    $datearray['receiver_name'] = $dateexcelvar['receiver_name'];
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];
                    if ($this->company_isassist == 1) {
                        $datearray['fu_marketer_name'] = $dateexcelvar['fu_marketer_name'];
                    }
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['client_source'] = $dateexcelvar['client_source'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_sponsor'] = $dateexcelvar['client_sponsor'];
                    $datearray['client_address'] = $dateexcelvar['client_address'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['city_name'] = $dateexcelvar['city_name'];
                    $datearray['area_name'] = $dateexcelvar['area_name'];
                    $datearray['nearschool_name'] = $dateexcelvar['nearschool_name'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];
                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    //  $datearray['marketer_writername'] = $dateexcelvar['marketer_writername'];
                    $outexceldate[] = $datearray;
                }
            }
            if ($this->company_isassist == 1) {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '标签', '学员', '意向星级', '试听班组', '未试听原因', '改约原因', '试听类型', '试听日期', '试听班级', '主要联系人', '主要联系电话','Email', '意向课程', '地推工号', '职务', '接触点', '接待人', '主要负责人', '协助负责人', '活动', '渠道类型', '渠道明细', '推荐人', '联系地址', '省', '市', '区', '附近学校', '备注', "名单创建时间"));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'audition_type_name', 'client_intention_level', 'coursetype_cnname', 'audition_novisitreason', 'audition_novisitreason_two', 'audition_genre', 'audition_visittime', 'class_cnname', 'parenter_cnname', 'client_mobile','client_email', 'course_cnname', 'promotion_jobnumber', 'post_name', 'client_frompage', 'receiver_name', 'marketer_name', 'fu_marketer_name', 'activity_name', 'client_source', 'channel_name', 'client_sponsor', 'client_address', 'province_name', 'city_name', 'area_name', 'nearschool_name', 'client_remark', "client_createtime");
            } else {
                $excelheader = $this->LgArraySwitch(array('中文名', '英文名', '性别', '年龄', '标签', '学员', '意向星级', '试听班组', '未试听原因', '改约原因', '试听类型', '试听日期', '试听班级', '主要联系人', '主要联系电话','Email', '意向课程', '地推工号', '职务', '接触点', '接待人', '主要负责人', '活动', '渠道类型', '渠道明细', '推荐人', '联系地址', '省', '市', '区', '附近学校', '备注', "名单创建时间"));
                $excelfileds = array('client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_tag', 'audition_type_name', 'client_intention_level', 'coursetype_cnname', 'audition_novisitreason', 'audition_novisitreason_two', 'audition_genre', 'audition_visittime', 'class_cnname', 'parenter_cnname', 'client_mobile','client_email', 'course_cnname', 'promotion_jobnumber', 'post_name', 'client_frompage', 'receiver_name', 'marketer_name', 'activity_name', 'client_source', 'channel_name', 'client_sponsor', 'client_address', 'province_name', 'city_name', 'area_name', 'nearschool_name', 'client_remark', "client_createtime");
            }


            $fielname = $this->LgStringSwitch("试听记录明细");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num} ";
            $auditionList = $this->DataControl->selectClear($sql);
            if ($auditionList) {
                foreach ($auditionList as $key => $value) {
                    $auditionList[$key]['isgmcschoolenter'] = ($value['gmcschoolenter_id'] > 0) ? '1' : '0';
                    $auditionList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                    if ($value['isstuclient'] == 0) {
                        $auditionList[$key]['audition_type_name'] = $this->LgStringSwitch('新生跟踪');
                    } else {
                        if ($value['student_type'] == 0) {
                            $auditionList[$key]['audition_type_name'] = $this->LgStringSwitch('校内跟踪');
                        } else {
                            $auditionList[$key]['audition_type_name'] = $this->LgStringSwitch('流失跟踪');
                        }
                    }
                    $auditionList[$key]['client_createtime'] = date("Y-m-d", $value['client_createtime']);
                    if (is_null($value['coursetype_cnname']) || $value['coursetype_cnname'] == '') {
                        $auditionList[$key]['coursetype_cnname'] = '--';
                    } else {
                        $auditionList[$key]['coursetype_cnname'] = $value['coursetype_cnname'];
                    }
                    if (!is_null($value['course_cnname'])) {
                        $auditionList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                    } else {
                        $auditionList[$key]['course_cnname'] = array();
                    }

                    if ($value['audition_genre'] == 1) {
                        $auditionList[$key]['audition_genre'] = $this->LgStringSwitch('插班试听');
                    } elseif ($value['audition_genre'] == 0) {
                        $auditionList[$key]['audition_genre'] = $this->LgStringSwitch('普通公开课试听');
                    } elseif ($value['audition_genre'] == 2) {
                        $auditionList[$key]['audition_genre'] = $this->LgStringSwitch('试读公开课试听');
                    }
                    if ($value['is_visit_autition'] == 0) {
                        $auditionList[$key]['visitstate'] = $this->LgStringSwitch('待确认');
                    } elseif ($value['is_visit_autition'] == 1) {
                        $auditionList[$key]['visitstate'] = $this->LgStringSwitch('已试听');
                    } elseif ($value['is_visit_autition'] == -1) {
                        $auditionList[$key]['visitstate'] = $this->LgStringSwitch('未试听');
                    } elseif ($value['is_visit_autition'] == 2) {
                        $auditionList[$key]['visitstate'] = $this->LgStringSwitch('已改约');
                    }
                    if($value['is_visit_autition'] == 2){
                        $auditionList[$key]['audition_novisitreason_two'] = $value['audition_novisitreason'];
                        $auditionList[$key]['audition_novisitreason'] = '--';
                    }else{
                        $auditionList[$key]['audition_novisitreason_two'] = '--';
                    }
                    if ($value['positivelog_id']) {
                        $auditionList[$key]['positivelog_name'] = $this->LgStringSwitch('已报名');
                    } else {
                        $auditionList[$key]['positivelog_name'] = $this->LgStringSwitch('未报名');
                    }
                    //筛选 报名的班组是否报名
                    if (isset($paramArray['cg_coursetype_id']) && $paramArray['cg_coursetype_id'] > 1) {
                        if ($auditionList[$key]['conversionlog_id'] > 1) {
                            $auditionList[$key]['conversionlog_id_name'] = "已报名";
                        } else {
                            $auditionList[$key]['conversionlog_id_name'] = "未报名";
                        }
                    } else {
                        $auditionList[$key]['conversionlog_id_name'] = "--";
                    }
                    // 是否禁止跟进按钮
                    if ($value['client_tracestatus'] == '-1' || $value['client_tracestatus'] == '-2' || $value['client_tracestatus'] == '4') {
                        $auditionList[$key]['is_forbid'] = '1';
                    } else {
                        $auditionList[$key]['is_forbid'] = '0';
                    }

                    if ($this->isGmcPost == true) {
//                        $auditionList[$key]['client_mobile'] = str_replace(substr($value['client_mobile'], 3, 4), '****', $value['client_mobile']);
                        $auditionList[$key]['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $value['client_mobile']);;
                    } else {
                        $auditionList[$key]['client_mobile'] = $value['client_mobile'];
                    }
                    $isoutCity = $this->isoutmobile($paramArray['school_id'], $value['client_mobile'], $value['client_id']);
                    if ($isoutCity) {
                        if ($isoutCity == 'local') {
                            $auditionList[$key]['isoutmobile'] = 0;
                        } else {
                            $auditionList[$key]['isoutmobile'] = 1;
                        }
                    } else {
                        $auditionList[$key]['isoutmobile'] = -1;
                    }

                    if ($value['client_tag']) {
                        $auditionList[$key]['client_tag'] = explode(',', $this->LgStringSwitch($value['client_tag']));
                    } else {
                        $auditionList[$key]['client_tag'] = array();
                    }

                    if (is_null($value['post_name'])) {
                        $auditionList[$key]['post_name'] = '--';
                    } else {
                        if ($value['post_name'] == 0) {
                            $auditionList[$key]['post_name'] = '市场';
                        } else {
                            $auditionList[$key]['post_name'] = '销售';
                        }
                    }
                    if (!$value['client_frompage']) {
                        $auditionList[$key]['client_frompage'] = '--';
                    }
                }
                $data['list'] = $auditionList;
            } else {
                $data['list'] = array();
            }

            $classsql = " select * from (select sc.class_id,sc.class_cnname,sc.class_enname,'0' as isstuclient 
                from crm_client_audition as ci
                LEFT JOIN crm_client as c ON ci.client_id=c.client_id
                LEFT JOIN smc_class as sc ON sc.class_id = ci.class_id 
                LEFT JOIN crm_client_principal as p ON p.client_id = ci.client_id and principal_ismajor = 1 and p.principal_leave = 0 and ci.school_id=p.school_id
                LEFT JOIN crm_marketer as m ON m.marketer_id = p.marketer_id 
				where {$datawhereclass} and sc.class_id is not null  {$datahaving} 
				union 
				select sc.class_id,sc.class_cnname,sc.class_enname ,'1' as isstuclient 
				from crm_student_audition as ci
                LEFT JOIN smc_student as s ON ci.student_id=s.student_id
                LEFT JOIN smc_class as sc ON sc.class_id = ci.class_id 
                LEFT JOIN crm_student_principal as p ON p.student_id = ci.student_id and principal_ismajor = 1 and p.principal_leave = 0 and ci.school_id=p.school_id
                LEFT JOIN crm_marketer as m ON m.marketer_id = p.marketer_id 
                left join smc_student_family as f ON f.student_id = s.student_id and f.family_isdefault =1
                left join smc_parenter as pt On pt.parenter_id =f.parenter_id  
                left join crm_student as cs On cs.student_id=s.student_id
				where {$stu_datawhereclass} and sc.class_id is not null  {$datahaving} ) as t group by t.class_id 
				";
            $classList = $this->DataControl->selectClear($classsql);
            if ($classList) {
                $data['classList'] = $classList;
            } else {
                $data['classList'] = array();
            }
            return $data;
        }
    }

    /**
     * @param $paramArray
     * @return array|bool
     *  设置试听是否到访
     *
     */
    function setIsaudition($paramArray, $from = 'crm')
    {
        if (!isset($paramArray['audition_type']) || $paramArray['audition_type'] === '') {
            $paramArray['audition_type'] = 0;
        }

        if ($paramArray['audition_isvisit'] == '-1' && trim($paramArray['audition_novisitreason']) == '') {
            $this->error = '1';
            $this->errortip = "取消试听需要填写原因!";
            $this->result = array();
            return false;
        }

        if (isset($paramArray['audition_type']) && $paramArray['audition_type'] == '1' && $from == 'crm') {
            $data = array();
            // 是否到访   // id   //到访原因   -1-未到访 1-到访
            if ($paramArray['audition_isvisit'] == 1) {
                $data['audition_isvisit'] = $paramArray['audition_isvisit'];
                $data['audition_updatetime'] = time();
            } elseif ($paramArray['audition_isvisit'] == -1) {
                $data['audition_isvisit'] = $paramArray['audition_isvisit'];
                $data['audition_novisitreason'] = $paramArray['audition_novisitreason'];
                $data['audition_updatetime'] = time();
            }
            $inviteOne = $this->DataControl->getFieldOne('crm_student_audition', "audition_visittime", "audition_id='{$paramArray['audition_id']}'");
            if (($inviteOne['audition_visittime'] > date("Y-m-d H:i:s")) && $paramArray['audition_isvisit'] == 1) {
                $this->error = '1';
                $this->errortip = "试听时间是{$inviteOne['audition_visittime']}，请勿提前设置到访!!";
                $this->result = array();
                return false;
            }

            $auditionOne = $this->DataControl->selectOne("select *  from crm_student_audition  where  audition_id='{$paramArray['audition_id']}'");

            $audHourOne = $this->DataControl->selectOne("select *  from smc_class_hour_audition  where  class_id='{$auditionOne['class_id']}' and  hour_id='{$auditionOne['hour_id']}' and client_id='{$auditionOne['client_id']}' and audition_isvisit='0' ");
            if ($audHourOne) {
                $this->DataControl->updateData('smc_class_hour_audition', "audition_id='{$audHourOne['audition_id']}'", $data);
            }

            if ($auditionOne && $this->DataControl->updateData('crm_student_audition', "audition_id='{$paramArray['audition_id']}'", $data)) {
                $markerOne = $this->DataControl->selectOne("select marketer_name from  crm_marketer where marketer_id='{$paramArray['marketer_id']}' ");
                $dataTrack = array();
                $dataTrack['school_id'] = $paramArray['school_id'];
                $dataTrack['student_id'] = $auditionOne['student_id'];
                $dataTrack['marketer_id'] = $paramArray['marketer_id'];
                $dataTrack['marketer_name'] = $markerOne['marketer_name'];
                $dataTrack['track_validinc'] = 1;
                $dataTrack['track_createtime'] = time();
                if ($paramArray['audition_isvisit'] == 1) {
                    $dataTrack['track_followmode'] = 2;
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("确认试听");
                    $dataTrack['track_note'] = $this->LgStringSwitch("确认试听成功");
                } else {
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("取消试听");
                    $dataTrack['track_note'] = $this->LgStringSwitch("取消试听");
                }
                if ($this->DataControl->insertData("crm_student_track", $dataTrack)) {
                    $this->DataControl->updateData("crm_student", "student_id='{$auditionOne['student_id']}'", array("student_tracestatus" => 3));
                }
                $principal = $this->DataControl->selectOne("select p.principal_id from crm_student_audition as d left join crm_student_principal as p on p.student_id = d.student_id where d.audition_id = '{$paramArray['audition_id']}' and p.marketer_id = '{$paramArray['marketer_id']}' and p.principal_leave = 0");
                $is_principal = $principal ? true : false;
                $this->error = '0';
                $this->errortip = "试听确认成功!";
                $this->result = array("is_principal" => $is_principal);
                return true;
            } else {
                $this->error = '1';
                $this->errortip = "试听确认失败!";
                $this->result = array();
                return false;
            }
        } else {
            $data = array();
            // 是否到访   // id   //到访原因   -1-未到访 1-到访
            if ($paramArray['audition_isvisit'] == 1) {
                $data['audition_isvisit'] = $paramArray['audition_isvisit'];

            } elseif ($paramArray['audition_isvisit'] == -1) {
                $data['audition_isvisit'] = $paramArray['audition_isvisit'];
                $data['audition_novisitreason'] = $paramArray['audition_novisitreason'];
            }
            $data['audition_updatetime'] = time();

            $inviteOne = $this->DataControl->getFieldOne("crm_client_audition", "client_id,audition_visittime,outthree_bookid", "audition_id='{$paramArray['audition_id']}'");
            if (($inviteOne['audition_visittime'] > date("Y-m-d H:i:s")) && $paramArray['audition_isvisit'] == 1) {
                $this->error = '1';
                $this->errortip = "试听时间是{$inviteOne['audition_visittime']}，请勿提前设置到访!!";
                $this->result = array();
                return false;
            }

            $principalOne = $this->DataControl->getFieldOne('crm_client_principal', "principal_id", " client_id='{$inviteOne['client_id']}' and school_id = '{$paramArray['school_id']}' and principal_leave = '0' and marketer_id > 0 ");
            if (!$principalOne) {
                $this->error = '1';
                $this->errortip = "该名单暂未分配CRM负责人不可确认是否试听，请先分配负责人后再进行跟进!";
                $this->result = array();
                return false;
            }

            $auditionOne = $this->DataControl->selectOne("select *  from crm_client_audition  where  audition_id='{$paramArray['audition_id']}'");
            $audHourOne = $this->DataControl->selectOne("select *  from smc_class_hour_audition  where  class_id='{$auditionOne['class_id']}' and  hour_id='{$auditionOne['hour_id']}' and client_id='{$auditionOne['client_id']}' and audition_isvisit='0' ");
            if ($audHourOne) {
                    $this->DataControl->updateData('smc_class_hour_audition', "audition_id='{$audHourOne['audition_id']}'", $data);
            }

            $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_id='{$auditionOne['client_id']}'");
            if ($auditionOne && $this->DataControl->updateData("crm_client_audition", "audition_id='{$paramArray['audition_id']}'", $data)) {
                if ($from == 'gmc') {
                    $staffOne = $this->DataControl->selectOne("select marketer_name as staffer_cnname,marketer_id as staffer_id from  crm_marketer where staffer_id='{$paramArray['staffer_id']}' ");
                    if (!$staffOne) {
                        $staffOne = $this->DataControl->selectOne("select staffer_cnname,staffer_id from  smc_staffer where staffer_id='{$paramArray['staffer_id']}' ");
                    }
                } else {
                    $markerOne = $this->DataControl->selectOne("select marketer_name from  crm_marketer where marketer_id='{$paramArray['marketer_id']}' ");
                }
                $dataTrack = array();
                if ($from == 'gmc') {
                    $dataTrack['school_id'] = 0;
                    $dataTrack['client_id'] = $inviteOne['client_id'];
                    $dataTrack['marketer_id'] = $staffOne['staffer_id'];
                    $dataTrack['marketer_name'] = $staffOne['staffer_cnname'];
                    $dataTrack['track_isgmcactive'] = 1;
                } else {
                    $dataTrack['school_id'] = $paramArray['school_id'];
                    $dataTrack['client_id'] = $auditionOne['client_id'];
                    $dataTrack['marketer_id'] = $paramArray['marketer_id'];
                    $dataTrack['marketer_name'] = $markerOne['marketer_name'];
                }

                $dataTrack['track_validinc'] = 1;
                $dataTrack['track_createtime'] = time();
                $clientData = array();
                if ($paramArray['audition_isvisit'] == 1) {
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("确认试听");
                    $dataTrack['track_note'] = $this->LgStringSwitch("确认试听成功");
                    if ($clientOne['client_tracestatus'] < 3) {
                        $clientData['client_tracestatus'] = '3';
                    }
                } else {
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("取消试听");
                    $dataTrack['track_note'] = $this->LgStringSwitch("取消试听");
                }

                if ($from == 'gmc') {
                    $dataTrack['track_note'] .= "(集团操作)";
                }
                $this->DataControl->insertData("crm_client_track", $dataTrack);
                $clientData['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$auditionOne['client_id']}'", $clientData);
                $principal = $this->DataControl->selectOne("select p.principal_id from crm_client_audition as d left join crm_client_principal as p on p.client_id = d.client_id where d.audition_id = '{$paramArray['audition_id']}' and p.marketer_id = '{$paramArray['marketer_id']}' and p.principal_leave = 0");
                $is_principal = $principal ? true : false;
                $this->error = '0';
                if ($paramArray['audition_isvisit'] == 1) {
                    $this->errortip = "试听确认成功!";
                } else {
                    $this->errortip = "试听取消成功!";
                }
                $this->result = array("is_principal" => $is_principal);
                return true;
            } else {
                $this->error = '1';
                $this->errortip = "试听确认失败!";
                $this->result = array();
                return false;
            }
        }
    }


    /**
     * 批量设置试听到访
     * author: ling
     * 对应接口文档 0001
     * @param $paramArray
     * @param $bool
     */
    function batchSettingAudition($paramArray)
    {
        $Client_Array = json_decode($paramArray['client_ids'], 1);
        if (is_array($Client_Array) && count($Client_Array) > 0) {
            foreach ($Client_Array as $val) {
                $data = array();
                $data['audition_isvisit'] = $paramArray['audition_isvisit'];
                $data['audition_id'] = $val['audition_id'];
                $data['audition_novisitreason'] = $paramArray['audition_novisitreason'];
                $data['marketer_id'] = $paramArray['marketer_id'];
                $data['school_id'] = $paramArray['school_id'];
                $this->setIsaudition($data);
            }
            $this->error = '0';
            $this->errortip = "设置成功!";
            return true;
        } else {
            $this->error = '1';
            $this->errortip = "设置失败!";
            return false;
        }
    }

    /**
     * @param $paramArray
     * @return bool
     * 设置柜询是否到访
     */
    function setIsVisit($paramArray, $from = 'crm')
    {
        if (!isset($paramArray['invite_type']) || $paramArray['invite_type'] === '') {
            $paramArray['invite_type'] = 0;
        }
        if ($paramArray['invite_isvisit'] == '-1' && trim($paramArray['invite_novisitreason']) == '') {
            $this->error = '1';
            $this->errortip = "取消柜询，需填写取消原因!";
            $this->result = array();
            return false;
        }

        if (isset($paramArray['invite_type']) && $paramArray['invite_type'] == '1' && $from == 'crm') {
            //在校学员跟踪
            // 是否到访   // id   //到访原因   -1-未到访 1-到访
            if ($paramArray['invite_isvisit'] == 1) {
                $data['invite_isvisit'] = $paramArray['invite_isvisit'];
            } elseif ($paramArray['invite_isvisit'] == -1) {
                $data['invite_isvisit'] = $paramArray['invite_isvisit'];
                $data['invite_novisitreason'] = $paramArray['invite_novisitreason'];
            }

            $inviteOne = $this->DataControl->getFieldOne('crm_student_invite', "student_id,invite_visittime", "invite_id='{$paramArray['invite_id']}'");
            if ($inviteOne['invite_visittime'] > date("Y-m-d H:i:s") && $paramArray['invite_isvisit'] == 1) {
                $this->error = '1';
                $this->errortip = "柜询时间是{$inviteOne['invite_visittime']}，请勿提前设置到访!";
                $this->result = array();
                return false;
            }
            if ($inviteOne && $this->DataControl->updateData('crm_student_invite', "invite_id='{$paramArray['invite_id']}'", $data)) {

                $markerOne = $this->DataControl->selectOne("select marketer_name from  crm_marketer where marketer_id='{$paramArray['marketer_id']}' ");
                $dataTrack = array();
                $dataTrack['school_id'] = $paramArray['school_id'];
                $dataTrack['student_id'] = $inviteOne['student_id'];
                $dataTrack['marketer_id'] = $paramArray['marketer_id'];
                $dataTrack['marketer_name'] = $markerOne['marketer_name'];
                $dataTrack['track_validinc'] = 1;
                $dataTrack['track_createtime'] = time();
                if ($paramArray['invite_isvisit'] == 1) {
                    $dataTrack['track_followmode'] = 1;
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("确认柜询");
                    $dataTrack['track_note'] = $this->LgStringSwitch("确认柜询成功");
                } else {
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("取消柜询");
                    $dataTrack['track_note'] = $this->LgStringSwitch("取消柜询");
                }
                $this->DataControl->insertData("crm_student_track", $dataTrack);

                $clientOne = $this->DataControl->getFieldOne("crm_student", "student_id,student_tracestatus", "student_id='{$inviteOne['student_id']}'");
                $clientData = array();
                if ($clientOne['student_tracestatus'] < 2) {
                    $clientData['student_tracestatus'] = '2';
                } else {
                    $clientData['student_tracestatus'] = $clientOne['student_tracestatus'];
                }
                $this->DataControl->updateData("crm_student", "student_id='{$inviteOne['student_id']}'", $clientData);

                $principal = $this->DataControl->selectOne("select p.principal_id from crm_student_invite as i left join crm_student_principal as p on p.student_id = i.student_id where i.invite_id = '{$paramArray['invite_id']}' and p.marketer_id = '{$paramArray['marketer_id']}' and p.principal_leave = 0");
                $is_principal = $principal ? true : false;
                $this->error = '0';
                $this->errortip = "设置成功";
                $this->result = array("is_principal" => $is_principal);
                return true;
            } else {
                $this->error = '1';
                $this->errortip = "设置失败";
                $this->result = array();
                return false;
            }
        } else {
            //CRM客户跟踪
            $data = array();
            if ($paramArray['invite_isvisit'] == 1) {
                $data['invite_isvisit'] = $paramArray['invite_isvisit'];
            } elseif ($paramArray['invite_isvisit'] == -1) {
                $data['invite_isvisit'] = $paramArray['invite_isvisit'];
                $data['invite_novisitreason'] = $paramArray['invite_novisitreason'];
            }
            $data['invite_updatetime'] = time();

            $inviteOne = $this->DataControl->getFieldOne("crm_client_invite", "client_id,outthree_bookid,invite_visittime", "invite_id='{$paramArray['invite_id']}'");
            if (!$inviteOne) {
                $this->error = '1';
                $this->errortip = "柜询信息不存在";
                $this->result = array();
                return false;
            }
            $principalOne = $this->DataControl->getFieldOne('crm_client_principal', "principal_id", " client_id='{$inviteOne['client_id']}' and school_id = '{$paramArray['school_id']}' and principal_leave = '0' and marketer_id > 0 ");
            if (!$principalOne) {
                $this->error = '1';
                $this->errortip = "该名单暂未分配CRM负责人不可确认是否到访，请先分配负责人后再进行跟进!";
                $this->result = array();
                return false;
            }

            if (($paramArray['invite_genre'] == '2' || $paramArray['invite_genre'] == '3') && $paramArray['isadminzhudong'] = '1') {

            } else {
                if ($inviteOne['invite_visittime'] > date("Y-m-d H:i:s") && $paramArray['invite_isvisit'] == 1) {
                    $this->error = '1';
                    $this->errortip = "柜询时间是{$inviteOne['invite_visittime']}，请勿提前设置到访!";
                    $this->result = array();
                    return false;
                }
            }
            $this->errortip = $this->DataControl->updateData("crm_client_invite", "invite_id='{$paramArray['invite_id']}'", $data);
            if ($from == 'gmc') {
                $staffOne = $this->DataControl->selectOne("select marketer_name as staffer_cnname,marketer_id as staffer_id from  crm_marketer where staffer_id='{$paramArray['staffer_id']}' ");
                if (!$staffOne) {
                    $staffOne = $this->DataControl->selectOne("select staffer_cnname,staffer_id from  smc_staffer where staffer_id='{$paramArray['staffer_id']}' ");
                }
            } else {
                $markerOne = $this->DataControl->selectOne("select marketer_name,marketer_id from  crm_marketer where marketer_id='{$paramArray['marketer_id']}' ");
            }
            $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_id='{$inviteOne['client_id']}'");
            $dataTrack = array();
            if ($from == 'gmc') {
                $dataTrack['school_id'] = 0;
                $dataTrack['client_id'] = $inviteOne['client_id'];
                $dataTrack['marketer_id'] = $staffOne['staffer_id'];
                $dataTrack['marketer_name'] = $staffOne['staffer_cnname'];
                $dataTrack['track_isgmcactive'] = 1;
            } else {
                $dataTrack['school_id'] = $paramArray['school_id'];
                $dataTrack['client_id'] = $inviteOne['client_id'];
                $dataTrack['marketer_id'] = $markerOne['marketer_id'];
                $dataTrack['marketer_name'] = $markerOne['marketer_name'];
            }
            $dataTrack['track_validinc'] = 1;
            $dataTrack['track_createtime'] = time();
            $clientData = array();
            if ($paramArray['invite_isvisit'] == 1) {
                $dataTrack['track_linktype'] = $this->LgStringSwitch("确认柜询");
                $dataTrack['track_note'] = $this->LgStringSwitch("确认柜询成功");
                if ($clientOne['client_tracestatus'] < 2) {
                    $clientData['client_tracestatus'] = '2';
                }
            } else {
                $dataTrack['track_linktype'] = $this->LgStringSwitch("取消柜询");
                $dataTrack['track_note'] = $this->LgStringSwitch("取消柜询");
            }
            if ($from == 'gmc') {
                $dataTrack['track_note'] .= $this->LgStringSwitch("(集团操作)");
            }

            $this->DataControl->insertData("crm_client_track", $dataTrack);
            $clientData['client_updatetime'] = time();
            $this->DataControl->updateData("crm_client", "client_id='{$inviteOne['client_id']}'", $clientData);
            $principal = $this->DataControl->selectOne("select p.principal_id from crm_client_invite as i left join crm_client_principal as p on p.client_id = i.client_id where i.invite_id = '{$paramArray['invite_id']}' and p.marketer_id = '{$paramArray['marketer_id']}' and p.principal_leave = 0");
            $is_principal = $principal ? true : false;
            $this->error = '0';
            if ($paramArray['invite_isvisit'] == 1) {
                $this->errortip = "确认柜询成功";
            } else {
                $this->errortip = "取消柜询成功";
            }
            $this->result = array("is_principal" => $is_principal);
            return true;
        }
    }

    /**
     * 批量设置柜询到访成功
     * author: ling
     * 对应接口文档 0001
     */
    function batchSetIsVisit($paramArray)
    {
        $Client_Array = json_decode($paramArray['client_ids'], 1);
        if (is_array($Client_Array) && count($Client_Array) > 0) {
            foreach ($Client_Array as $val) {
                $data = array();
                $data['invite_isvisit'] = $paramArray['invite_isvisit'];
                $data['invite_id'] = $val['invite_id'];
                $data['invite_novisitreason'] = $paramArray['invite_novisitreason'];
                $data['marketer_id'] = $paramArray['marketer_id'];
                $data['school_id'] = $paramArray['school_id'];
                $this->setIsVisit($data);
            }
            $this->error = '0';
            $this->errortip = "设置成功!";
            return true;
        } else {
            $this->error = '1';
            $this->errortip = "设置失败!";
            return false;
        }
    }


    //招生柜询报表 --  97
    function inviteForms($paramArray)
    {
        $datawhere = " 1 ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (g.goal_name like '%{$paramArray['keyword']}%' or m.marketer_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
//        if(isset($paramArray['goal_starttime']) && $paramArray['goal_starttime'] != ''){
//            $datawhere .= " and g.goal_starttime <= '{$paramArray['goal_starttime']}'";
//        }
//        if(isset($paramArray['goal_endtime']) && $paramArray['goal_endtime'] != ''){
//            $datawhere .= " and g.goal_endtime >= '{$paramArray['goal_endtime']}'";
//        }
        if (isset($paramArray['goal_starttime']) && $paramArray['goal_starttime'] != '') {
            $datawhere .= " and g.goal_starttime >= '{$paramArray['goal_starttime']}'";
        }
        if (isset($paramArray['goal_endtime']) && $paramArray['goal_endtime'] != '') {
            $datawhere .= " and g.goal_starttime <= '{$paramArray['goal_endtime']}'";
        }
        //学校 筛选
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and g.school_id = '{$paramArray['school_id']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and g.marketer_id = '{$paramArray['found_marketer_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(g.goal_id) as datanum
                FROM crm_sell_goal as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere}";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }

        if (isset($paramArray['orderby']) && $paramArray['orderby'] != '') {
            $orderby = $paramArray['orderby'];
        } else {
            $orderby = " g.goal_id ";
        }
        if (isset($paramArray['sort']) && $paramArray['sort'] != '') {
            $sort = $paramArray['sort'];
        } else {
            $sort = "DESC";
        }
        $orderby = "ORDER BY {$orderby} {$sort}";

        $sqlfields = " g.goal_id,g.goal_name,g.marketer_id,g.goal_starttime,g.goal_endtime,m.marketer_name,g.goal_mintrack,g.goal_normtrack,g.goal_mininvite,g.goal_norminvite,g.goal_minofficial,g.goal_normofficial ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_goal as g
                LEFT JOIN crm_marketer as m ON g.marketer_id = m.marketer_id
                WHERE {$datawhere} {$orderby}
                limit {$pagestart},{$num}";
        $goalList = $this->DataControl->selectClear($sql);

        $result = array();
        $result["datalist"] = $goalList;
        $result["count"] = $count;
        return $result;
    }


}