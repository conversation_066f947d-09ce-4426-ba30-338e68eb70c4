<?php
/**
 * 跨界活动-微信
 */

namespace Model\Crm;


class TransboundaryWechatModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    protected $code;
    protected $encryptedData;
    protected $iv;

    const appId = 'wx550be80d3f68edb1';
    const appSecret = '049263d2b71d977bc8186179dbbdd795';
    const nonceStr = 'kidcastle';
    const mch_id = '';
    const key = '';

    /**
     * 通过微信code换取用户信息
     * @param $paramArray
     * @return array|false|\mix|string
     */
    function wxLogin($paramArray)
    {
        $this->encryptedData = $paramArray['encryptedData'];
        $this->iv = $paramArray['iv'];

        $param = array(
            'appid' => self::appId,
            'secret' => self::appSecret,
            'grant_type' => "authorization_code",
            'js_code' =>$paramArray['code']
        );
        $getBackurl = request_by_curl("https://api.weixin.qq.com/sns/jscode2session", dataEncode($param), "GET");
        $json_play = new \Webjson();
        $wxResult = $json_play->decode($getBackurl, "1");
//        $wxResult = json_decode($getBackurl, true);
        if (empty($wxResult)) {
            // 为什么以empty判断是否错误，这是根据微信返回
            // 这种情况通常是由于传入不合法的code
            $res = array('error' => 1, 'errortip' => "获取session_key及openID时异常，微信内部错误");
            ajax_return($res);

        }else{
            $loginFail = array_key_exists('errcode', $wxResult);
            if ($loginFail) {
                $res = array('error' => 1, 'errortip'=>'微信报错!','msg' => $wxResult['errmsg'],'errorCode'=>$wxResult['errcode']);
                ajax_return($res);

            }else{
                $mobileInfo = $this->getMobile($wxResult['session_key'],$this->encryptedData,$this->iv);
                $wxResult['phoneNumber'] = $mobileInfo['phoneNumber'];

                return $wxResult;
            }

        }

    }

    /**
     * 获取手机号
     * @param $session_key
     * @param $session_key
     * @return mixed
     */
    function getMobile($session_key,$encryptedData,$iv)
    {
        if(empty($session_key))
        {
            ajax_return(array('error' => 1, 'errortip'=>'获取session_key时异常,session_key为空!'));

        }

        include_once "Core/Tools/Wxcode/wxBizDataCrypt.php";

        $pc = new \WXBizDataCrypt(self::appId,$session_key);
        $errCode = $pc->decryptData($encryptedData,$iv,$data);
        if ($errCode == 0) {
            return json_decode($data,true);

        } else {
            ajax_return(array('error' => 1, 'errortip'=>'微信获取手机号错误!','errorCode'=>$errCode));

        }

    }

}