<?php
/**
 * 跨界活动-商户端
 */

namespace Model\Crm;


class TransboundaryBusinessModel extends modelTpl
{
    public $error = 0;
    public $errortip = "success";
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    /**
     * 获取商户信息
     * @param $paramArray
     * @return false|mixed
     */
    function getBusiness($paramArray)//openid,session_key,phoneNumber
    {
        //查询手机号是否为负责人手机号
        $activityData = $this->DataControl->selectOne("SELECT activity_id FROM crm_transboundary_activity WHERE business_mobile='{$paramArray['phoneNumber']}'");
        if(!$activityData){
            ajax_return(array('error' => 1, 'errortip' => '您不是指定商户,暂无查看权限!', 'result' => array()));
        }else{
            //如果是负责人 查询openid
            $businessInfo = $this->DataControl->selectOne("SELECT business_openid,business_id,business_mobile,business_nickname,business_img FROM crm_transboundary_business WHERE business_mobile='{$paramArray['phoneNumber']}'");

            if($businessInfo){
                //已存在商户
                if(!$businessInfo['business_openid']){//是否存在openid
                    $this->DataControl->updateData("crm_transboundary_business"," business_mobile='{$paramArray['phoneNumber']}' ",['business_openid' => $paramArray['openid']]);
                }
                //数组中过滤掉openid字段信息
                unset($businessInfo['business_openid']);

                return $businessInfo;
            }else{
                //不存在则创建并返回信息
                $data = array();
                $data['business_openid'] = $paramArray['openid'];
                $data['business_mobile'] = $paramArray['phoneNumber'];
                $data['business_createtime'] = time();
                $id = $this->DataControl->insertData("crm_transboundary_business", $data);
                if($id){
                    $newBusinessInfo = $this->DataControl->selectOne("SELECT business_id,business_mobile FROM crm_transboundary_business WHERE business_id='{$id}'");
                }else{
                    ajax_return(array('error' => 1, 'errortip' => '添加失败,稍后再试!', 'result' => array()));
                }

                return $newBusinessInfo;
            }
        }
    }

    /**
     * 商家活动列表
     * @param $paramArray
     * @return bool
     */
    public function activityList($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $activityList = $this->DataControl->selectClear("
        SELECT 
            a.activity_id,a.activity_name,a.activity_startdate,a.activity_enddate,a.activity_settleamount,a.activity_paymentamount,a.activity_status 
        FROM 
            crm_transboundary_activity as a
        WHERE 
            a.business_id = '{$paramArray['business_id']}'
        ORDER BY 
            a.activity_createtime DESC 
        LIMIT {$pagestart},{$num} 
        ");
        if ($activityList) {
            foreach ($activityList as &$value) {
                $value['activity_unsettleamount'] = (string)(($value['activity_paymentamount']*100 - $value['activity_settleamount']*100)/100);//未结算金额
            }
        } else {
            $activityList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $count = $this->DataControl->selectOne("SELECT COUNT(a.activity_id) AS num FROM crm_transboundary_activity as a WHERE a.business_id = '{$paramArray['business_id']}'");
            $count = $count['num']+0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = $activityList;
        $result["allnum"] = $count;

        if($activityList){
            $this->error = 0;
            $this->errortip = "商家活动列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无活动信息";
            $this->result = $result;
            return false;
        }
    }

    /**
     * 活动参与列表
     * @param $paramArray activity_id
     * @return bool
     */
    public function activityRecordList($paramArray)
    {
        if (empty($paramArray['activity_id'])) {
            $this->error = 1;
            $this->errortip = "参数缺失 活动ID";
            $this->result = [];
            return true;
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $dataList = $this->DataControl->selectClear("
        SELECT 
            u.user_nickname,u.user_mobile,ac.record_winningamount,ac.record_settlementstatus,ac.record_createtime
        FROM 
            crm_transboundary_activity_record AS ac 
            LEFT JOIN crm_transboundary_user AS u ON ac.user_id = u.user_id 
        WHERE 
            ac.activity_id = '{$paramArray['activity_id']}' 
        ORDER BY 
            ac.record_createtime DESC 
        LIMIT {$pagestart},{$num} 
        ");
        if ($dataList) {
            $status = array('0' => '未结算', '1' => '已结算');
            foreach ($dataList as &$value) {
                $value['user_mobile'] = substr_replace($value['user_mobile'],'****',3,4);
                if ($value['record_winningamount'] == '0.00') {
                    $value['record_winningamount'] = '未中奖';
                    $value['record_settlementstatus_name'] = '--';
                }else{
                    $value['record_settlementstatus_name'] = $status[$value['record_settlementstatus']];
                }
                $value['record_createdate'] = date('Y-m-d H:i:s', $value['record_createtime']);
            }
        } else {
            $dataList = array();
        }

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $count = $this->DataControl->selectOne("SELECT COUNT(ac.record_id) AS num FROM crm_transboundary_activity_record AS ac LEFT JOIN crm_transboundary_user AS u ON ac.user_id=u.user_id WHERE ac.activity_id='{$paramArray['activity_id']}'");
            $count = $count['num']+0;
        }else{
            $count = 0;
        }

        $activityOne = $this->DataControl->selectOne("SELECT a.activity_name,a.activity_paymentamount,a.activity_settleamount,a.activity_startdate,a.activity_enddate,
(SELECT COUNT(r.record_id) FROM crm_transboundary_activity_record AS r WHERE r.activity_id = a.activity_id) as record_num
FROM crm_transboundary_activity as a WHERE a.activity_id='{$paramArray['activity_id']}'");
        if($activityOne){
            $activityOne['activity_unsettleamount'] = (string)(($activityOne['activity_paymentamount']*100 - $activityOne['activity_settleamount']*100)/100);//未结算金额
            $activityOne['activity_startdate'] = date("m.d", strtotime($activityOne['activity_startdate']));
            $activityOne['activity_enddate'] = date("m.d", strtotime($activityOne['activity_enddate']));
        }

        $result = array();
        $result["datalist"] = $dataList;
        $result["activity"] = $activityOne;
        $result["allnum"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "活动参与列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无参与信息";
            $this->result = $result;
            return false;
        }
    }
}