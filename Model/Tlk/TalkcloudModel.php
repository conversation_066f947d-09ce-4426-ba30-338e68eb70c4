<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model\Tlk;

class TalkcloudModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $apiUrl = "https://global.talk-cloud.net/";
    public $apiKey = "4j7UDVpdHXgosq9z";
    public $ackUrl = "https://api.roadofcloud.net/";
    public $ackKey = "lQGr9SZXAWUMHMP3";
//吉的堡网校    fyjx  122042
//陕西向桂教育科技有限公司（吉的堡） shjdbjy     11171
//陕西向桂教育科技有限公司加盟校区（吉的堡）  shjdbjm   100558
    function __construct($company_id = '8888')
    {
        parent::__construct();
        if ($company_id != '0') {
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_tkyapikey", "company_id = '{$company_id}'");
            if ($companyOne) {
                $this->apiKey = $companyOne['company_tkyapikey'];
            }
        }
    }
    //1.房间部分
    //创建房间
    //测试程序需要的curl start
    function curl_post($url = '', $postdata = '', $options = array())
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    function curl_get($url = '', $options = array())
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    function _requestjson($url, $putArray = null, $putType = "POST")
    {
        $sendUrl = $url;
        if ($putType == 'FILE') {
            $apiinfo = $this->curl_post($sendUrl, $putArray);
        } else {
            $apiinfo = request_by_curl($sendUrl, dataEncode($putArray), $putType, array());
        }
        if ($apiinfo) {
            if ($arr = json_decode($apiinfo, true)) {
                $apilog = array();
                $apilog['apilog_url'] = $sendUrl;
                if(isset($putArray['serial'])){
                    $apilog['apilog_serial'] = $putArray['serial'];
                }
                $apilog['apilog_errorcode'] = 0;
                $apilog['apilog_type'] = $putType;
                $apilog['apilog_parameter'] = json_encode($putArray) ;
                $apilog['apilog_bakjson'] = $apiinfo;
                $apilog['apilog_createtime'] = time() ;
                $this->DataControl->insertData("tkl_apilog",$apilog);
                return $arr;
            } else {
                $apilog = array();
                $apilog['apilog_url'] = $sendUrl;
                if(isset($putArray['serial'])){
                    $apilog['apilog_serial'] = $putArray['serial'];
                }
                $apilog['apilog_errorcode'] = 1;
                $apilog['apilog_type'] = $putType;
                $apilog['apilog_parameter'] = json_encode($putArray) ;
                $apilog['apilog_bakjson'] = $apiinfo;
                $apilog['apilog_createtime'] = time() ;
                $this->DataControl->insertData("tkl_apilog",$apilog);
                return false;
            }
        } else {
            $apilog = array();
            $apilog['apilog_url'] = $sendUrl;
            if(isset($putArray['serial'])){
                $apilog['apilog_serial'] = $putArray['serial'];
            }
            $apilog['apilog_errorcode'] = 2;
            $apilog['apilog_type'] = $putType;
            $apilog['apilog_parameter'] = json_encode($putArray);
            $apilog['apilog_createtime'] = time() ;
            $this->DataControl->insertData("tkl_apilog",$apilog);
            return false;
        }
    }


    /**
     * 获取 token WebAPI 加密使用
     * @param $postData
     * @param $key
     * @param $secretkey
     * @return string
     */
    function getToken($postData, $key, $secretkey)
    {
        // 剔除此2项，不进行字符串生成
        unset($postData['token']);
        unset($postData['key']);
        // 将body体里除了token的参数 打成一个字符串
        $dataStr = $postData ? $this->getString($postData) : '';
        // 生成新token 和用户 生成的token做对比
        // 生成规则 md5(企业key + body打成的字符串 + 企业自定义的随机字符串);
        $newToken = md5(md5($key . $dataStr) . md5($secretkey));
        return $newToken;
    }

    /**
     * [getstring 将对应不规则数组转成一个字符串]
     * <AUTHOR> <[email address]>
     * @param  [type] $data [description]
     * @param  [type] $str  [description]
     * @return [type]       [description]
     */
    function getString($data, $str = '')
    {
        ksort($data);
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                if ($value) {
                    // htmlspecialchars_decode form 表单提交特殊字符被转译，如果json格式提交就不需如此处理
                    $str .= htmlspecialchars_decode($key);
                    $str = $this->getString($value, $str);
                }
            } else {
                if (($value || $value === 0 || $value === '0') && $value !== null && $value !== false && $value !== true) {
                    $str .= htmlspecialchars_decode($key) . htmlspecialchars_decode($value);
                }
            }
        }
        return $str;
    }


    //测试程序需要的curl end
    //创建房间
    public function exroomCreate($putArray)
    {
        $postdata = array();
        $postdata['roomname'] = $putArray['roomname']; //必填 房间名称
        $postdata['starttime'] = $putArray['starttime'];  //必填 房间开始时间戳(精确到秒)
        $postdata['endtime'] = $putArray['endtime']; //必填 房间结束时间(精确到秒)
        $postdata['chairmanpwd'] = $putArray['chairmanpwd']; //必填 主席密码	必填，4=<长度<=20
        $postdata['assistantpwd'] = $putArray['assistantpwd']; //必填 助教密码	必填，4=<长度<=20
        $postdata['patrolpwd'] = $putArray['patrolpwd']; //必填 巡课密码	必填，4=<长度<=20
        $postdata['passwordrequired'] = $putArray['passwordrequired']; //选填 房间是否需要普通密码 0:否、1:是
        $postdata['confuserpwd'] = $putArray['confuserpwd']; //房间普通用户密码 passwordrequired = 1 时必填(4=<长度<=10)
        $postdata['videotype'] = 1; //选填 视频分辨率 0：流畅   1：标清 2：高清   3：超清
        $postdata['videoframerate'] = 10; //必填 视频帧数
        $postdata['autoopenav'] = $putArray['autoopenav']; //必填 自动开启
        if ($putArray['fromclass'] == '0') {
            $postdata['maxvideo'] = $putArray['maxvideo']; //上台人数
            $url = $this->apiUrl . "WebAPI/roomcreate/";
            $postdata['key'] = $this->apiKey;  //必填 企业id  authkey
            $postdata['roomtype'] = $putArray['roomtype']; //必填 房间类型
            $postdata['allowsidelinuuser'] = 1; //选填 是否允许旁听用户参加 0:否、1:是
            $postdata['sidelineuserpwd'] = $putArray['sidelineuserpwd']; //旁听用户密码 4=<长度<=10 allowsidelineuser = 1 时必填
        } else {
            $postdata['maxvideo'] = $putArray['maxvideo']; //上台人数
            $postdata['sharedesk'] = $putArray['sharedesk']; //是否开启桌面共享
            $url = $this->ackUrl . "/WebAPI/CreateRoom/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }

        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //修改房间
    public function exroomModify($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial']; //必填 房间号
        $postdata['roomname'] = $putArray['roomname']; //必填 房间名
        $postdata['starttime'] = $putArray['starttime'];  //必填 房间开始时间戳(精确到秒)
        $postdata['endtime'] = $putArray['endtime']; //必填 房间结束时间(精确到秒)
        $postdata['chairmanpwd'] = $putArray['chairmanpwd']; //必填 主席密码	必填，4=<长度<=20
        $postdata['assistantpwd'] = $putArray['assistantpwd']; //必填 助教密码	必填，4=<长度<=20
        $postdata['passwordrequired'] = 1; //选填 房间是否需要普通密码 0:否、1:是
        $postdata['patrolpwd'] = $putArray['patrolpwd']; //必填 巡课密码	必填，4=<长度<=20
        $postdata['passwordrequired'] = $putArray['passwordrequired']; //选填 房间是否需要普通密码 0:否、1:是
        $postdata['confuserpwd'] = $putArray['confuserpwd']; //房间普通用户密码 passwordrequired = 1 时必填(4=<长度<=10)
        $postdata['videotype'] = 1; //选填 视频分辨率 0：流畅   1：标清 2：高清   3：超清
        $postdata['videoframerate'] = 10; //必填 视频帧数
        $postdata['autoopenav'] = $putArray['autoopenav']; //必填 自动开启
        if ($putArray['fromclass'] == '0') {
            $postdata['maxvideo'] = $putArray['maxvideo']; //上台人数
            $url = $this->apiUrl . "WebAPI/roommodify/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
            $postdata['allowsidelinuuser'] = 1; //选填 是否允许旁听用户参加 0:否、1:是
            $postdata['sidelineuserpwd'] = $putArray['sidelineuserpwd']; //旁听用户密码 4=<长度<=10 allowsidelineuser = 1 时必填
        } else {
            $postdata['maxvideo'] = $putArray['maxvideo']; //上台人数
            $postdata['sharedesk'] = $putArray['sharedesk']; //是否开启桌面共享
            $url = $this->ackUrl . "/WebAPI/ModifyRoom/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //修改房间时间
    public function exroomTimesModify($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial']; //必填 房间号
        $postdata['roomname'] = $putArray['roomname']; //必填 房间名
        $postdata['starttime'] = $putArray['starttime'];  //必填 房间开始时间戳(精确到秒)
        $postdata['endtime'] = $putArray['endtime']; //必填 房间结束时间(精确到秒)
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/roommodify/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $postdata['sharedesk'] = $putArray['sharedesk']; //是否开启桌面共享
            $url = $this->ackUrl . "/WebAPI/ModifyRoom/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //删除房间
    public function exroomDelete($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial'];//必填 房间号
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/roomdelete/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/DeleteRoom/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //删除录制文件
    public function exroomVideoDelete($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial'];//必填 房间号
        if ($putArray['fromclass'] == '0') {
            $postdata['recordtitle'] = $putArray['recordtitle'];//必填 房间号
            $url = $this->apiUrl . "WebAPI/roomdeleterecord/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $recordtitle = array("{$putArray['recordtitle']}");
            $postdata['recordtitle'] = $recordtitle;//必填 房间号
            $url = $this->ackUrl . "/WebAPI/CleanLeaveScreenMedia/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    /*//得到房间列表
    public function ex_getroomlist()
    {
        $url = $this->apiUrl."WebAPI/getroomlist/";
        $postdata['key'] = $this->apiKey;  //必填  企业id authkey
        $postdata['roomtype'] = 0; //选填 房间类型 0:表示全部房间 1：表示当前房间 2：未来房间 3：历史房间

        $resultdata = $this->curl_post($url,$postdata);
        echo($resultdata);
    }*/
    /*//得到某个时间范围内的房间
    public function ex_getroombytime()
    {
        $url = $this->apiUrl."WebAPI/getroombytime/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $starttime = gmmktime(); //当前时间戳
        $postdata['starttime'] = $starttime; //必填 房间开始时间戳(精确到秒)
        $postdata['endtime'] = $starttime+24*3600; //必填 房间结束时间戳(精确到秒)

        $resultdata = $this->curl_post($url,$postdata);
        echo($resultdata);
    }*/
    //得到某个房间的详细信息
    public function getExroominfo($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial'];//必填 房间号
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/getroom/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/GetRoomInfo/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }
    //文档上传
    function exuploadFile($putArray)
    {
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/uploadfile/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/uploadfile/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
        }

        $postdata['isopen'] = 1; //选填 是否是公开文档 0：表示非公开文档 1：表示公开文档公开文档表示公司的其他房间都可以关联此文档，非公开文档表示只对本房间
        if (isset($putArray['serial']) && $putArray['serial'] !== '') {
            $postdata['serial'] = $putArray['serial']; //必填 房间号
        }
        $postdata['conversion'] = 1; //必填 是否转换
        $postdata['isdefault'] = 0; //必填 是否转换
        $postdata['dynamicppt'] = 1;
        if (class_exists('\CURLFile')) {
            $RealTitleID = $putArray['filedata']['name'];
            $postdata['filedata'] = new \CURLFile($putArray['filedata']['tmp_name'], $_FILES['filedata']['type'], $RealTitleID);
        } else {
            $postdata['filedata'] = '@' . $putArray['filedata']['tmp_name'];
        }
        $resultdata = $this->_requestjson($url, $postdata, 'FILE');
        return $resultdata;
    }

    //删除文档
    public function roomDeletefile($putArray)
    {
        $postdata = array();
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/roomdeletefile/";
            $postdata['fileidarr[]'] = $putArray['fileid']; //必填 房间号
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/roomdeletefile/";
            $fileid = array("{$putArray['fileid']}");
            $postdata['fileidarr'] = $fileid; //必填 房间号
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //删除文档
    public function exroomDeletefile($putArray)
    {
        $postdata = array();
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/deletefile/";
            $postdata['fileidarr[]'] = $putArray['fileid']; //必填 房间号
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/DeleteFile/";
            $fileid = array("{$putArray['fileid']}");
            $postdata['fileidarr'] = $fileid; //必填 房间号
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间录制文件列表
    public function exroomonGetrecordlist($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial'];
        $postdata['starttime'] = $putArray['starttime'];
        $postdata['endtime'] = $putArray['endtime'];
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/getrecordlist/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/getrecordlist/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间流量费用
    public function exroomonGetRoomInfo($putArray, $page = 1)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial'];
        $postdata['starttime'] = $putArray['starttime'];
        $postdata['endtime'] = $putArray['endtime'];
        if ($putArray['fromclass'] == '0') {
            return array();
            //$url = $this->apiUrl . "WebAPI/getrecordlist/";
            //$postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $postdata['page'] = $page;
            $url = $this->ackUrl . "/billserver/Billweb/getRoomInfo";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间录制文件列表
    public function exroomonGetvideolist($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial'];
        if ($putArray['starttime']) {
            $postdata['starttime'] = $putArray['starttime'];
        }
        if ($putArray['endtime']) {
            $postdata['endtime'] = $putArray['endtime'];
        }
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/getrecordlist/";
            $postdata['recordtype'] = 1;
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/getrecordlist/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //房间关联文档 返回4105没找到fileidarr字段
    public function ex_roombindfile($putArray)
    {
        $url = $this->apiUrl . "WebAPI/roombindfile/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = $putArray['serial']; //必填 房间号
        if (is_array($putArray['fileidarr']) && count($putArray['fileidarr']) > 0) {
            for ($i = 0; $i < count($putArray['fileidarr']); $i++) {
                $postdata['fileidarr[' . $i . ']'] = $putArray['fileidarr'][$i];
            }
        }
        $resultdata = $this->curl_post($url, $postdata);
        return $resultdata;
    }

    //房间关联文档 返回4105没找到fileidarr字段
    public function ex_roomgetfilepath($putArray)
    {
        $url = $this->apiUrl . "WebAPI/getfilepath/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['fileid'] = $putArray['fileid']; //必填 课件ID
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间文档列表
    public function exroomonGetroomfile($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial']; //选填 房间号 如果填写就表示获取某个房间的在线人数，如果不填就表示获取全部房间的在线人数
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/getroomfile/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/GetRoomFile/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }
        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间在线用户数
    public function exroomonLinenum($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial']; //选填 房间号 如果填写就表示获取某个房间的在线人数，如果不填就表示获取全部房间的在线人数
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/roomonlinenum/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
            $postdata['type'] = $putArray['type']; //选填 参加用户类型 0: 当前在线人数  1：登录人数
        } else {
            $url = $this->ackUrl . "/WebAPI/GetRoomOnlinenum/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }

        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间用户登入登出情况
    public function exgetLogininfo($putArray)
    {
        $postdata = array();
        $postdata['serial'] = $putArray['serial']; //必填 房间号
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/getlogininfo/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            //$postdata['page'] = 200; //必填 房间号
            if (isset($putArray['starttime'])) {
                $postdata['starttime'] = $putArray['starttime'];
            }
            if (isset($putArray['endtime'])) {
                $postdata['endtime'] = $putArray['endtime'];
            }
            $url = $this->ackUrl . "/WebAPI/GetLoginInfo/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
            $postdata['token'] = $this->getToken($postdata, $this->ackKey, '123456');  //必填 企业id  authkey
        }

        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //获取房间当前在线用户信息  返回-1
    public function exgetonLineuser($putArray)
    {
        if ($putArray['fromclass'] == '0') {
            $url = $this->apiUrl . "WebAPI/getonlineuser/";
            $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        } else {
            $url = $this->ackUrl . "/WebAPI/GetOnlineUserInfo/";
            $postdata['key'] = $this->ackKey;  //必填 企业id  authkey
        }
        $postdata['serial'] = $putArray['serial']; //必填 房间号

        $resultdata = $this->_requestjson($url, $postdata, 'POST');
        return $resultdata;
    }

    //设置房间流
    public function ex_setstream()
    {
        $url = $this->apiUrl . "WebAPI/setstream/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = 531150968; //必填 房间号
        $postdata['streamid'] = 2000000010; //必填 流id 取值范围： 2000000000 - 2099999999
        $postdata['name'] = 'xiaoming'; //必填 房间中的显示名

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //获取房间流
    public function ex_getstream()
    {
        $url = $this->apiUrl . "WebAPI/getstream/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = 531150968; //必填 房间号

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //删除房间流
    public function ex_deletestream()
    {
        $url = $this->apiUrl . "WebAPI/deletestream/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = 531150968; //必填 房间号
        $postdata['streamid'] = 2000000010; //必填 流id 取值范围： 2000000000 - 2099999999

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //房间中显示url
    public function ex_roomrelaurl()
    {
        $url = $this->apiUrl . "WebAPI/roomrelaurl/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = 531150968; //必填 房间号
        $postdata['linkname'] = 'admin'; //必填 关联名
        $postdata['linkurl'] = 'http://127.0.0.1/WebAPI/roomrelaurl'; //必填 关联url

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //2.登录部分
    //获取房间登录地址（服务器端调用）
    public function ex_createloginkey()
    {
        $url = $this->apiUrl . "WebAPI/createloginkey/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = 531150968; //必填 房间号
        $postdata['nickname'] = 'admin'; //必填 昵称 (昵称如果是中文需要进行UTF8编码，可使用urlencode)
        $postdata['usertype'] = 1; //选填 用户类型 0是普通交互用户，1是主席，2是旁听用户(usertype优先级比密码高)
        $postdata['password'] = '123456'; //房间密码 如果房间（PasswordRequired=1）那么可输入相应密码
        $postdata['invisibleuser'] = 0; //选填 用户是否隐身 1表示隐身， 0表示不隐身

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //登录房间(用户浏览器访问)
    public function ex_loginroom()
    {
        $url = $this->apiUrl . "WebAPI/loginroom/";
        $postdata['loginkey'] = 'fa59298dae4f132297d31800d0cfcfdd05c78e22';  //必填 登录key 来自 登录房间（服务器）返回值，
        $postdata['serial'] = 277340593; //必填 房间号

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //创建以及加入房间；直接进入房间
    public function ex_entry()
    {
        $url = $this->apiUrl . "WebAPI/entry/";
        //$postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = 545505353; //必填 房间号 非0开始的数字串， 请保证房间号唯一
        $postdata['roomname'] = 'text'; //选填 房间名
        $str = "崔建佳（公主坟测试）";
        $postdata['username'] = urlencode($str); //必填 用户名 用户在房间中显示的名称；使用UTF8编码，特殊字符需使用urlencode转义
        $postdata['usertype'] = 2; //必填，0：主讲(教师 )  1：助教    2: 学员   3：直播用户  4:巡检员
        $postdata['pid'] = 0; //选填, 第三方系统的用户id；默认值为0
        $time = gmmktime(); //当前时间戳
        $postdata['ts'] = $time; //必填，当前GMT时间戳，int格式
        $postdata['auth'] = $this->apiKey; //必填，auth值为MD5(key + ts + serial + usertype)其中key为双方协商的接口密钥：默认值为：4j7UDVpdHXgosq9z
        //$postdata['chairmanpwd'] = '123456'; //选填 主席密码 默认值为0。格式为：128位AES加密串，加密密钥默认为4j7UDVpdHXgosq9z
        $postdata['passwordrequired'] = 1; //选填 房间是否需要普通密码 0:否、1:是
        $postdata['userpassword'] = 'b2bc8b4adcf954055bc1c79379157992'; //选填 房间普通用户密码 passwordrequired = 1 时必填(4位以上)。普通用户密码；默认值为0。格式为：128位AES加密串，加密密钥默认为4j7UDVpdHXgosq9z
        $postdata['sidelineuserpwd'] = '0'; //直播用密码 选填, 直播用户密码；默认值为0。格式为：128位AES加密串，加密密钥默认为4j7UDVpdHXgosq9z
        $postdata['logintype'] = 0; //选填 登录方式 0:客户端   1：表示falsh登录2：表示自动选择，如果安装了客户端，则启动客户端，否则使用flash
        $postdata['domain'] = 'www'; //公司域名
        $postdata['invisibleuser'] = 1; //选填 用户是否隐身 1表示隐身， 0表示不隐身
        $postdata['donotauth'] = 1; //选填 用户是否隐身 1表示隐身， 0表示不隐身

        //$this->setRequestProperty("contentType", "UTF-8");
        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //2.4 登录房间(直接启动PC客户端或移动app进入房间)
    public function ex_directloginroom()
    {

    }
    //3.用户部分
    //创建用户
    public function ex_usercreate()
    {
        $url = $this->apiUrl . "WebAPI/usercreate/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['account'] = 'xiaoming'; //必填 用户账号 （保证唯一）
        $postdata['userpwd'] = '123456'; //必填 用户面
        $postdata['firstname'] = 'xiaoming'; //必填 姓名

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //修改用户
    public function ex_usermodify()
    {
        $url = $this->apiUrl . "WebAPI/usermodify/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['account'] = 'xiaoming'; //必填 用户账号 （保证唯一）
        $postdata['userpwd'] = '123456'; //必填 用户面
        $postdata['firstname'] = 'xiaoming'; //必填 姓名

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //删除用户
    public function ex_userdelete()
    {
        $url = $this->apiUrl . "WebAPI/userdelete/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['account'] = 'xiaoming'; //必填 用户账号 （保证唯一）

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //获取用户
    public function ex_getuser()
    {
        $url = $this->apiUrl . "WebAPI/getuser/";
        $postdata['key'] = $this->apiKey;  //必填 企业id  authkey
        $postdata['account'] = 'xiaoming'; //必填 用户账号 （保证唯一）

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //4.组部分
    //创建组
    public function ex_groupcreate()
    {
        $url = $this->apiUrl . "WebAPI/groupcreate/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['groupname'] = 'zuming'; //必填 组名
        $postdata['userarr'] = 'xiaoming1,xiaoming2'; //选填 组中的成员 账号数组

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //修改组
    public function ex_groupmodify()
    {
        $url = $this->apiUrl . "WebAPI/groupmodify/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['groupid'] = 125654; //必填 组id
        $postdata['groupname'] = 'zuming'; //必填 组名

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //增加组中成员
    public function ex_addgroupuser()
    {
        $url = $this->apiUrl . "WebAPI/addgroupuser/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['groupid'] = 125654; //必填 组id
        $postdata['userarr'] = 'xiaoming,admin'; //组中的成员 账号数组

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //删除组成员
    public function ex_deletegroupuser()
    {
        $url = $this->apiUrl . "WebAPI/deletegroupuser/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['groupid'] = 125654; //必填 组id
        $postdata['userarr'] = 'xiaoming'; //选填 组中的成员 账号数组

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //获取组
    public function ex_getgroup()
    {
        $url = $this->apiUrl . "WebAPI/getgroup/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['groupid'] = 125654; //必填 组id

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //删除组
    public function ex_groupdelete()
    {
        $url = $this->apiUrl . "WebAPI/groupdelete/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['groupid'] = 125654; //必填 组id

        $resultdata = $this->curl_post($url, $postdata);
        echo($resultdata);
    }

    //aes 加密，解密
    public function ex_aestest()
    {
        //$ckey = '2NIZjlgmXqwbW016';0bb147de3666563b588430ebd78d18bf
        //$chairmansourcepwd = 'zx8380000';
        $ckey = I('key');
        $chairmansourcepwd = I('pwd');
        $ChairmanPWD = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $ckey, $chairmansourcepwd, MCRYPT_MODE_ECB);
//        var_dump('encrypt:');
//        var_dump(bin2hex($ChairmanPWD));
        $bin_str = pack("H*", bin2hex($ChairmanPWD));
        $orgtext = trim(mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $ckey, $bin_str, MCRYPT_MODE_ECB));
//        var_dump('decrypt:');
//        var_dump($orgtext);
    }

    //答题数据
    public function getStuAnswers($putArray)
    {
        if(!$putArray['serial']){
            echo "拓课云教室号必须传参";
            die;
        }

        //1555444745   289182707  889949439
        $url = $this->apiUrl . "WebAPI/getRoomAnswerRecord/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = $putArray['serial'];
        $resultdata = $this->curl_post($url, $postdata);

        $DataInfo =  json_decode($resultdata, true);
//        print_r($DataInfo);die;
        if(!$DataInfo['data']){
            //标记已经获取
            $mlog = array();
            $mlog['log_ischeck'] = 1;
            $mlog['log_checktime'] = time();
            $mlog['log_notes'] = '无数据';
            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$putArray['log_id']}' and log_type = '2' ",$mlog);

            echo '本节课没有答题记录';
            die;
        }
        //防止重复录入
        $questONE = $this->DataControl->selectOne("select count(question_id) as allnum from tkl_lineclass_hourrooms_question where hourrooms_threenumber = '{$putArray['serial']}' ");
        if($questONE['allnum'] > 0 && $questONE['allnum'] == $DataInfo['data']['teacherAnswerCount']){
            echo "题目已经录入过了";
            die;
        }elseif($questONE['allnum'] > 0 ){
            $this->DataControl->delData("tkl_lineclass_hourrooms_question"," hourrooms_threenumber='{$putArray['serial']}' ");
        }
        //判断本节课是否又答题互动
        if($DataInfo['data']['teacherAnswerCount'] >= 1){
            //先存储题目
            $teacherAnswerList = $DataInfo['data']['teacherAnswerList'];
            //$questCount = count($teacherAnswerList);//答题总数
            $questCount = $DataInfo['data']['teacherAnswerCount'];//答题总数
            if($questCount>0){
                foreach ($teacherAnswerList as $teacherAnswerListVar){
                    $questdata = array();
                    $questdata['hourrooms_threenumber'] = $putArray['serial'];

                    $questdata['question_quesID'] = $teacherAnswerListVar['quesID'];
                    $questdata['question_userId'] = $teacherAnswerListVar['userId'];
                    $questdata['question_userName'] = $teacherAnswerListVar['userName'];
                    $questdata['question_addcreateTime'] = $teacherAnswerListVar['createTime'];
                    $questdata['question_questionDesc'] = $teacherAnswerListVar['questionDesc'];
                    $questdata['question_optionsCount'] = $teacherAnswerListVar['optionsCount'];
                    $questdata['question_questionType'] = $teacherAnswerListVar['questionType'];
                    $questdata['question_rightOptions'] = json_encode($teacherAnswerListVar['rightOptions'],JSON_UNESCAPED_UNICODE);

                    $questdata['question_studentAnswerCount'] = $teacherAnswerListVar['studentAnswerCount'];
                    $questdata['question_studentAnswerRightCount'] = $teacherAnswerListVar['studentAnswerRightCount'];

                    $questdata['question_createtime'] = time();
                    $questdata['question_updatatime'] = time();
                    $quesID = $this->DataControl->insertData("tkl_lineclass_hourrooms_question",$questdata);
                    //学生答题明细
                    $studentAnswerList = $teacherAnswerListVar['studentAnswerList'];
                    if($studentAnswerList){
                        foreach ($studentAnswerList as $studentAnswerListVar){
                            $studata = array();
                            $studata['question_id'] = $quesID;
                            $studata['hourrooms_threenumber'] = $putArray['serial'];

                            $studata['question_quesID'] = $studentAnswerListVar['quesID'];
                            $studata['stulist_userId'] = $studentAnswerListVar['userId'];
                            $studata['stulist_userName'] = $studentAnswerListVar['userName'];
                            $studata['stulist_isRight'] = $studentAnswerListVar['isRight'];
                            $studata['stulist_options'] = json_encode($studentAnswerListVar['options'],JSON_UNESCAPED_UNICODE);
                            $studata['stulist_addcreateTime'] = $studentAnswerListVar['createTime'];

                            $studata['stulist_createtime'] = time();
                            $studata['stulist_updatatime'] = time();
                            $stulistID = $this->DataControl->insertData("tkl_lineclass_hourrooms_question_stulist",$studata);
                        }
                    }else{
                        echo '暂无学生答题明细记录';
                    }
                }

                //标记已经获取
                $mlog = array();
                $mlog['log_ischeck'] = 1;
                $mlog['log_checktime'] = time();
                $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$putArray['log_id']}' and log_type = '2' ",$mlog);

                echo '已经记录';
            }else{
                echo '暂无答题记录';
            }

        }else{
            echo '暂无答题记录';
        }
    }

    //红包
    public function getStuRedpacket($putArray)
    {
        if(!$putArray['serial']){
            echo "拓课云教室号必须传参";
            die;
        }
        //1555444745   289182707  889949439
        $url = $this->apiUrl . "WebAPI/getRedpacketRecordList/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = $putArray['serial'];
        $postdata['page'] = $putArray['page'];
        $resultdata = $this->curl_post($url, $postdata);

        $DataInfo =  json_decode($resultdata, true);
//        print_r($DataInfo);die;

        if(!$DataInfo['data'] || $DataInfo['totalCount'] == '0'){
            //标记已经获取
            $mlog = array();
            $mlog['log_ischeck'] = 1;
            $mlog['log_checktime'] = time();
            $mlog['log_notes'] = '无数据';
            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$putArray['log_id']}' and log_type = '3' ",$mlog);

            echo '本节课没红包记录';
            die;
        }

        //防止重复录入
        $redONE = $this->DataControl->selectOne("select count(redpacket_id) as allnum from tkl_lineclass_hourrooms_redpacket where hourrooms_threenumber = '{$putArray['serial']}' ");
        if($redONE['allnum'] > 0 && $redONE['allnum'] == $DataInfo['totalCount']){
            echo "红包已经录入过了";
            die;
        }elseif($redONE['allnum'] > 0 && $putArray['page'] == '1'){
            $this->DataControl->delData("tkl_lineclass_hourrooms_redpacket"," hourrooms_threenumber='{$putArray['serial']}' ");
        }
        //判断本节课是否有红包记录
        $Times=$putArray['page'];
        if($DataInfo['totalCount'] >= 1 && $DataInfo['data']){
            foreach ($DataInfo['data'] as $infodatavar) {
                $reddata = array();
                $reddata['hourrooms_threenumber'] = $putArray['serial'];

                $reddata['redpacket_packetId'] = $infodatavar['packetId'];
                $reddata['redpacket_userId'] = $infodatavar['userId'];
                $reddata['redpacket_userName'] = $infodatavar['userName'];
                $reddata['redpacket_addcreateTime'] = $infodatavar['createTime'];
                $reddata['redpacket_packetNum'] = $infodatavar['packetNum'];

                $reddata['redpacket_createtime'] = time();
                $reddata['redpacket_updatatime'] = time();
                $redID = $this->DataControl->insertData("tkl_lineclass_hourrooms_redpacket", $reddata);
            }

            if($DataInfo['totalCount'] > ($Times*200)){
                $Times++;
                $data = array();
                $data['serial'] = $putArray['serial'];
                $data['page'] = $Times;
                $data['log_id'] = $putArray['log_id'];
                $this->getStuRedpacket($data);
            }

            //标记已经获取
            $mlog = array();
            $mlog['log_ischeck'] = 1;
            $mlog['log_checktime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$putArray['log_id']}' and log_type = '3' ",$mlog);
            return '红包记录存储成功';
        }else{
            return '暂无红包记录';
        }
    }

    //礼物
    public function getStuGift($putArray)
    {
        if(!$putArray['serial']){
            echo "拓课云教室号必须传参";
            die;
        }
        $DataInfo = array();
        //1555444745   289182707  889949439
        $url = $this->apiUrl . "WebAPI/getUserGiftDetail/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = $putArray['serial'];
        $postdata['page'] = $putArray['page'];
        $resultdata = $this->curl_post($url, $postdata);

        $DataInfo =  json_decode($resultdata, true);
//        print_r($DataInfo);die;

        if(!$DataInfo['dataList'] || $DataInfo['totalCount'] == '0'){
            //标记已经获取
            $mlog = array();
            $mlog['log_ischeck'] = 1;
            $mlog['log_checktime'] = time();
            $mlog['log_notes'] = '无数据';
            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$putArray['log_id']}' and log_type = '4' ",$mlog);

            echo '本节课没有礼物记录';
            die;
        }

        //防止重复录入
        $giftONE = $this->DataControl->selectOne("select count(gift_id) as allnum from tkl_lineclass_hourrooms_gift where hourrooms_threenumber = '{$putArray['serial']}' ");
        if($giftONE['allnum'] > 0 && $giftONE['allnum'] == $DataInfo['totalCount']){
            echo "礼物已经录入过了";
            die;
        }elseif($giftONE['allnum'] > 0 && $putArray['page'] == '1'){
            $this->DataControl->delData("tkl_lineclass_hourrooms_gift"," hourrooms_threenumber='{$putArray['serial']}' ");
        }

        //判断本节课是否有红包记录
        $Times=$putArray['page'];
        if($DataInfo['dataList']){

            foreach ($DataInfo['dataList'] as $infodatavar) {
                $reddata = array();
                $reddata['hourrooms_threenumber'] = $putArray['serial'];

                $reddata['gift_sendid'] = $infodatavar['sendid'];
                $reddata['gift_sendname'] = $infodatavar['sendname'];
                $reddata['gift_receiveid'] = $infodatavar['receiveid'];
                $reddata['gift_receivename'] = $infodatavar['receivename'];
                $reddata['gift_giftnumber'] = $infodatavar['giftnumber'];
                $reddata['gift_create_time'] = $infodatavar['create_time'];

                $reddata['gift_createtime'] = time();
                $reddata['gift_updatatime'] = time();
                $giftID = $this->DataControl->insertData("tkl_lineclass_hourrooms_gift", $reddata);
            }

//            echo $DataInfo['totalCount'].'--';
//            echo $Times*200;
//            die;
            if($DataInfo['totalCount'] > ($Times*200)){
                $Times++;
                $data = array();
                $data['serial'] = $putArray['serial'];
                $data['page'] = $Times;
                $data['log_id'] = $putArray['log_id'];
                $this->getStuGift($data);
            }

            //标记已经获取
            $mlog = array();
            $mlog['log_ischeck'] = 1;
            $mlog['log_checktime'] = time();
            $this->DataControl->updateData("tkl_lineclass_hourrooms_check_log","log_id = '{$putArray['log_id']}' and log_type = '4' ",$mlog);
            return '礼物记录存储成功';
        }else{
            return '暂无礼物记录';
        }
    }

    //获取举手和上台学生列表
    public function getStuRoomActiveUser($putArray)
    {
        if(!$putArray['serial']){
            echo "拓课云教室号必须传参";
            die;
        }
        $DataInfo = array();
        //1555444745   289182707  889949439
        $url = $this->apiUrl . "WebAPI/getRoomActiveUser/";
        $postdata['key'] = $this->apiKey;  //必填 企业id authkey
        $postdata['serial'] = $putArray['serial'];
        $resultdata = $this->curl_post($url, $postdata);

        $DataInfo =  json_decode($resultdata, true);

        $result = array();
        if($DataInfo['result'] == '0' && $DataInfo['data']){
            $result['publishstate'] = array_column($DataInfo['data'],'publishstate','userid');
            $result['raisehand'] = array_column($DataInfo['data'],'raisehand','userid');
        }

        return $result;
    }

}