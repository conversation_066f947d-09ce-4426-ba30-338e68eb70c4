<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model;

class WscShopCouponsModel extends modelTpl{
    public $couApplyOne = array();//优惠券申请
    public $error = false;
    public $errortip = false;
    public $oktip = false;//提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct() {
        parent::__construct ();
    }

    function getCouponsApplyOne($apply_id){
        $ApplyOne = $this->DataControl->getOne('smc_student_coupons_apply',"apply_id={$apply_id}");
        $studentOne = $this->DataControl->getFieldOne('smc_student',"student_branch,student_cnname,student_sex,student_birthday","student_id={$ApplyOne['student_id']}");
        $schoolOne = $this->DataControl->getFieldOne('smc_school','school_shortname,school_branch',"school_id={$ApplyOne['school_id']}");
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter",'parenter_cnname,parenter_mobile',"parenter_id={$ApplyOne['parenter_id']}");
        $ApplyOne['student_branch1'] = $studentOne['student_branch'];
        $ApplyOne['student_cnname1'] = $studentOne['student_cnname'];
        $ApplyOne['student_sex1'] = $studentOne['student_sex'];
        $ApplyOne['student_birthday1'] = $studentOne['student_birthday'];
        $ApplyOne['school_shortname1'] = $schoolOne['school_shortname'];
        $ApplyOne['school_branch1'] = $schoolOne['school_branch'];
        $ApplyOne['parenter_cnname1'] = $parenterOne['parenter_cnname'];
        $ApplyOne['parenter_mobile1'] = $parenterOne['parenter_mobile'];
        $className = "getApply_".$ApplyOne['applytype_branch'];
        $this->$className($ApplyOne);
    }

    function getApply_wscyuangong($ApplyOne){
        if($ApplyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_staff',"apply_id={$ApplyOne['apply_id']}");

            $sql = " select s.student_id,s.student_branch,s.student_cnname,s.student_sex,s.student_birthday,TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ) AS student_age,c.school_cnname,c.school_shortname,c.school_branch,c.school_id 
                from smc_student as s
                LEFT JOIN smc_student_enrolled as t on s.student_id = t.student_id 
                LEFT JOIN smc_school as c ON c.school_id = t.school_id 
                WHERE s.student_id = '{$siblings['student_id']}' and  t.enrolled_status <> '-1' and  t.enrolled_status <> '2' 
                GROUP BY s.student_id ";
            $stuONE = $this->DataControl->selectOne($sql);
            $ApplyOne['student_branch2'] = $stuONE['student_branch'];
            $ApplyOne['student_cnname2'] = $stuONE['student_cnname'];
            $ApplyOne['student_sex2'] = $stuONE['student_sex'];
            $ApplyOne['student_birthday2'] = $stuONE['student_birthday'];
            $ApplyOne['school_shortname2'] = $stuONE['school_shortname'];

            $ApplyOne['staff_parentname'] = $siblings['staff_parentname'];
            $ApplyOne['staff_parentmobile'] = $siblings['staff_parentmobile'];
            $ApplyOne['relative_name'] = $siblings['relative_name'];
            $ApplyOne['relative_mobile'] = $siblings['relative_mobile'];
        }
        $this->couApplyOne = $ApplyOne;
    }

    function getApply_wscqinqi($ApplyOne){
        if($ApplyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_staff',"apply_id={$ApplyOne['apply_id']}");

            $sql = " select s.student_id,s.student_branch,s.student_cnname,s.student_sex,s.student_birthday,TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ) AS student_age,c.school_cnname,c.school_shortname,c.school_branch,c.school_id 
                from smc_student as s
                LEFT JOIN smc_student_enrolled as t on s.student_id = t.student_id 
                LEFT JOIN smc_school as c ON c.school_id = t.school_id 
                WHERE s.student_id = '{$siblings['student_id']}' and  t.enrolled_status <> '-1' and  t.enrolled_status <> '2' 
                GROUP BY s.student_id ";
            $stuONE = $this->DataControl->selectOne($sql);
            $ApplyOne['student_branch2'] = $stuONE['student_branch'];
            $ApplyOne['student_cnname2'] = $stuONE['student_cnname'];
            $ApplyOne['student_sex2'] = $stuONE['student_sex'];
            $ApplyOne['student_birthday2'] = $stuONE['student_birthday'];
            $ApplyOne['school_shortname2'] = $stuONE['school_shortname'];

            $ApplyOne['staff_parentname'] = $siblings['staff_parentname'];
            $ApplyOne['staff_parentmobile'] = $siblings['staff_parentmobile'];
            $ApplyOne['relative_name'] = $siblings['relative_name'];
            $ApplyOne['relative_mobile'] = $siblings['relative_mobile'];
        }
        $this->couApplyOne = $ApplyOne;
    }

    function getApply_wscneibu($ApplyOne){
        if($ApplyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_staff',"apply_id={$ApplyOne['apply_id']}");

            $sql = " select s.student_id,s.student_branch,s.student_cnname,s.student_sex,s.student_birthday,TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ) AS student_age,c.school_cnname,c.school_shortname,c.school_branch,c.school_id 
                from smc_student as s
                LEFT JOIN smc_student_enrolled as t on s.student_id = t.student_id 
                LEFT JOIN smc_school as c ON c.school_id = t.school_id 
                WHERE s.student_id = '{$siblings['student_id']}' and  t.enrolled_status <> '-1' and  t.enrolled_status <> '2' 
                GROUP BY s.student_id ";
            $stuONE = $this->DataControl->selectOne($sql);
            $ApplyOne['student_branch2'] = $stuONE['student_branch'];
            $ApplyOne['student_cnname2'] = $stuONE['student_cnname'];
            $ApplyOne['student_sex2'] = $stuONE['student_sex'];
            $ApplyOne['student_birthday2'] = $stuONE['student_birthday'];
            $ApplyOne['school_shortname2'] = $stuONE['school_shortname'];

            $ApplyOne['staff_parentname'] = $siblings['staff_parentname'];
            $ApplyOne['staff_parentmobile'] = $siblings['staff_parentmobile'];
            $ApplyOne['relative_name'] = $siblings['relative_name'];
            $ApplyOne['relative_mobile'] = $siblings['relative_mobile'];
        }
        $this->couApplyOne = $ApplyOne;
    }

    function getApply_wsctongbao($ApplyOne){
        if($ApplyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_siblings',"apply_id={$ApplyOne['apply_id']}");

            $ApplyOne['student_prove_img'] = $siblings['student_prove_img'];
            $ApplyOne['siblings_parentname'] = $siblings['siblings_parentname'];
            $ApplyOne['siblings_parentmobile'] = $siblings['siblings_parentmobile'];
            $ApplyOne['siblings_cnname'] = $siblings['siblings_cnname'];
            $ApplyOne['siblings_branch'] = $siblings['siblings_branch'];
            $ApplyOne['siblings_prove_img'] = $siblings['siblings_prove_img'];
        }
        $this->couApplyOne = $ApplyOne;
    }

    function getApply_wscshangpin($ApplyOne){
        if($ApplyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_procou',"apply_id={$ApplyOne['apply_id']}");

            $ApplyOne['procou_scoreproveimg'] = $siblings['procou_scoreproveimg'];
            $ApplyOne['procou_scoreprove'] = $siblings['procou_scoreprove'];
        }
        $this->couApplyOne = $ApplyOne;
    }

    function getApply_wsctuijian($ApplyOne){
        if($ApplyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_recommend',"apply_id={$ApplyOne['apply_id']}");
            $ApplyOne['recommend_cnname'] = $siblings['recommend_cnname'];
            $ApplyOne['recommend_parentname'] = $siblings['recommend_parentname'];
            $ApplyOne['recommend_parentmobile'] = $siblings['recommend_parentmobile'];

            $ApplyOne['recommend_sex'] = $siblings['recommend_sex'];
            $ApplyOne['recommend_birthday'] = $siblings['recommend_birthday'];
            $ApplyOne['recommend_address'] = $siblings['recommend_address'];
            $onestu = $this->DataControl->selectOne("select v.student_branch,s.school_shortname   
                    FROM crm_client as c
                    LEFT JOIN crm_client_conversionlog as v ON c.client_id = v.client_id 
                    LEFT JOIN smc_school as s ON v.school_id = s.school_id 
                    WHERE c.client_cnname = '{$siblings['recommend_cnname']}' 
                    and c.client_mobile = '{$ApplyOne['recommend_parentmobile']}' 
                    and v.conversionlog_id <> '' limit 0,1");
            if($onestu){
                $ApplyOne['student_branch2'] = $onestu['student_branch'];
                $ApplyOne['school_shortname2'] = $onestu['school_shortname'];
            }
        }
        $this->couApplyOne = $ApplyOne;
    }








    function issueStudentCoupons($apply_id,$score_type,$playinc=0,$playreason=''){
        if($this->DataControl->getFieldOne('smc_student_coupons','coupons_id',"apply_id={$apply_id}")){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$apply_id}",array('apply_status'=>3,'apply_refusereson'=>$playreason));
            $this->error = true;
            $this->errortip = "通过成功";
            return false;
        }
        $applyOne = $this->DataControl->getOne('smc_student_coupons_apply',"apply_id={$apply_id}");

        $applytypeOne = $this->DataControl->selectOne(" select * from smc_code_couponsapplytype WHERE applytype_branch = '{$applyOne['applytype_branch']}' ");
        $applyOne['couponsrules_id'] = $applytypeOne['couponsrules_id'];

        $className = "issueStudent_".$applyOne['applytype_branch'];
        return $this->$className($applyOne,$score_type,$playinc,$playreason);
    }

    function issueStudent_wsctongbao($applyOne,$score_type=0,$playinc=0,$playreason='')
    {
        $data = array();
        do {
            $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
        } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
        $data['company_id'] = 8888;
        $data['apply_id'] = $applyOne['apply_id'];
        $data['coupons_pid'] = $couponspid_get;
        $data['student_id'] = $applyOne['student_id'];
        $data['coupons_class'] = 2;
        $data['coupons_name'] = '同胞优惠券';
        $data['coupons_type'] = 1;
        $data['coupons_playclass'] = 1;
        $data['coupons_reason'] = "(新微商城)后台审核通过并发券";
        $data['couponsrules_id'] = $applyOne['couponsrules_id'];
        if($playinc == '0'){
            $data['coupons_discount'] = 9/10;
        }elseif($playinc == '1'){
            $data['coupons_discount'] = 9.5/10;
        }
//        $data['coupons_price'] = '300';
//        $data['coupons_minprice'] = 300;
        $data['coupons_exittime'] = strtotime('+1 year');
        $data['coupons_bindingtime'] = time();
        $data['coupons_createtime'] = time();
        $last_id = $this->DataControl->insertData("smc_student_coupons", $data);
        if($last_id){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$applyOne['apply_id']}",array('apply_status'=>3,'apply_discount'=>$data['coupons_discount'],'apply_refusereson'=>$playreason));
            return true;
        }else{
            return false;
        }
    }

    function issueStudent_wscyuangong($applyOne,$score_type=0,$playinc=0,$playreason='')
    {
        $data = array();
        do {
            $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
        } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
        $data['company_id'] = 8888;
        $data['apply_id'] = $applyOne['apply_id'];
        $data['coupons_pid'] = $couponspid_get;
        $data['student_id'] = $applyOne['student_id'];
        $data['coupons_class'] = 2;
        $data['coupons_name'] = '员工子女优惠券';
        $data['coupons_type'] = 1;
        $data['coupons_playclass'] = 1;
        $data['coupons_reason'] = "(新微商城)后台审核通过并发券";
        $data['couponsrules_id'] = $applyOne['couponsrules_id'];
        $data['coupons_discount'] = $playinc/10;
//        $data['coupons_price'] = '300';
//        $data['coupons_minprice'] = 300;
        $data['coupons_exittime'] = strtotime('+1 year');
        $data['coupons_bindingtime'] = time();
        $data['coupons_createtime'] = time();
        $last_id = $this->DataControl->insertData("smc_student_coupons", $data);
        if($last_id){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$applyOne['apply_id']}",array('apply_status'=>3,'apply_discount'=>$data['coupons_discount'],'apply_refusereson'=>$playreason));
            return true;
        }else{
            return false;
        }
    }

    function issueStudent_wscneibu($applyOne,$score_type=0,$playinc=0,$playreason='')
    {
        $data = array();
        do {
            $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
        } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
        $data['company_id'] = 8888;
        $data['apply_id'] = $applyOne['apply_id'];
        $data['coupons_pid'] = $couponspid_get;
        $data['student_id'] = $applyOne['student_id'];
        $data['coupons_class'] = 2;
        $data['coupons_name'] = '内部折扣优惠券';
        $data['coupons_type'] = 1;
        $data['coupons_playclass'] = 1;
        $data['coupons_reason'] = "(新微商城)后台审核通过并发券";
        $data['couponsrules_id'] = $applyOne['couponsrules_id'];
        $data['coupons_discount'] = $playinc/10;
//        $data['coupons_price'] = '300';
//        $data['coupons_minprice'] = 300;
        $data['coupons_exittime'] = strtotime('+1 year');
        $data['coupons_bindingtime'] = time();
        $data['coupons_createtime'] = time();
        $last_id = $this->DataControl->insertData("smc_student_coupons", $data);
        if($last_id){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$applyOne['apply_id']}",array('apply_status'=>3,'apply_discount'=>$data['coupons_discount'],'apply_refusereson'=>$playreason));
            return true;
        }else{
            return false;
        }
    }

    function issueStudent_wscqinqi($applyOne,$score_type=0,$playinc=0,$playreason='')
    {
        $data = array();
        do {
            $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
        } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
        $data['company_id'] = 8888;
        $data['apply_id'] = $applyOne['apply_id'];
        $data['coupons_pid'] = $couponspid_get;
        $data['student_id'] = $applyOne['student_id'];
        $data['coupons_class'] = 2;
        $data['coupons_name'] = '员工亲戚子女优惠券';
        $data['coupons_type'] = 1;
        $data['coupons_playclass'] = 1;
        $data['coupons_reason'] = "(新微商城)后台审核通过并发券";
        $data['couponsrules_id'] = $applyOne['couponsrules_id'];
        $data['coupons_discount'] = $playinc/10;
//        $data['coupons_price'] = '300';
//        $data['coupons_minprice'] = 300;
        $data['coupons_exittime'] = strtotime('+1 year');
        $data['coupons_bindingtime'] = time();
        $data['coupons_createtime'] = time();
        $last_id = $this->DataControl->insertData("smc_student_coupons", $data);
        if($last_id){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$applyOne['apply_id']}",array('apply_status'=>3,'apply_discount'=>$data['coupons_discount'],'apply_refusereson'=>$playreason));
            return true;
        }else{
            return false;
        }
    }

    function issueStudent_wscshangpin($applyOne,$score_type=0,$playinc=0,$playreason='')
    {
        if($applyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_procou',"apply_id={$applyOne['apply_id']}");
            $ApplyOne['procou_scoreproveimg'] = $siblings['procou_scoreproveimg'];
            $ApplyOne['procou_scoreprove'] = $siblings['procou_scoreprove'];
        }

        $data = array();
        do {
            $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
        } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
        $data['company_id'] = 8888;
        $data['apply_id'] = $applyOne['apply_id'];
        $data['coupons_pid'] = $couponspid_get;
        $data['student_id'] = $applyOne['student_id'];
        $data['coupons_range'] = 1;
        $data['coupons_class'] = 2;
        $data['coupons_name'] = '商品优惠券';
        $data['coupons_type'] = 0;
        $data['coupons_playclass'] = 1;
        $data['coupons_reason'] = "(新微商城)后台审核通过并发券";
        $data['couponsrules_id'] = $applyOne['couponsrules_id'];
//        $data['coupons_discount'] = $playinc;
        if ($applyOne['procou_scoreprove'] == 'A+' || $applyOne['procou_scoreprove'] == '优+' || $applyOne['procou_scoreprove'] == '100') {
            $data['coupons_price'] = 50;
        } else {
            $data['coupons_price'] = 20;
        }
        $data['coupons_minprice'] = 0;
//        $data['coupons_price'] = '300';
//        $data['coupons_minprice'] = 300;
        $data['coupons_exittime'] = strtotime('+1 year');
        $data['coupons_bindingtime'] = time();
        $data['coupons_createtime'] = time();
        $last_id = $this->DataControl->insertData("smc_student_coupons", $data);
        if($last_id){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$applyOne['apply_id']}",array('apply_status'=>3,'apply_price'=>$data['coupons_price'],'apply_refusereson'=>$playreason));
            return true;
        }else{
            return false;
        }
    }

    function issueStudent_wsctuijian($applyOne,$score_type=0,$playinc=0,$playreason='')
    {
        if($applyOne){
            $siblings = $this->DataControl->getOne('shop_student_coupons_apply_recommend',"apply_id={$applyOne['apply_id']}");
            $ApplyOne['recommend_cnname'] = $siblings['recommend_cnname'];
            $ApplyOne['recommend_parentname'] = $siblings['recommend_parentname'];
            $ApplyOne['recommend_parentmobile'] = $siblings['recommend_parentmobile'];
            $ApplyOne['recommend_sex'] = $siblings['recommend_sex'];
            $ApplyOne['recommend_birthday'] = $siblings['recommend_birthday'];
            $ApplyOne['recommend_address'] = $siblings['recommend_address'];
        }

        $data = array();
        do {
            $couponspid_get = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
        } while ($this->DataControl->getFieldOne("smc_student_coupons", "coupons_id", "coupons_pid='{$couponspid_get}'"));
        $data['company_id'] = 8888;
        $data['apply_id'] = $applyOne['apply_id'];
        $data['coupons_pid'] = $couponspid_get;
        $data['student_id'] = $applyOne['student_id'];
        $data['coupons_range'] = 2;
        $data['coupons_class'] = 2;
        $data['coupons_name'] = '教育优惠券';
        $data['coupons_type'] = 0;
        $data['coupons_playclass'] = 1;
        $data['coupons_reason'] = "(新微商城)推荐新生，后台审核通过并发券";
        $data['couponsrules_id'] = $applyOne['couponsrules_id'];
        if ($applyOne['procou_scoreprove'] == 'A+') {
            $data['coupons_price'] = 300;
        }elseif($applyOne['procou_scoreprove'] == '优+'){
            $data['coupons_price'] = 300;
        } else {
            $data['coupons_price'] = 300;
        }
        $data['coupons_minprice'] = 0;
        $data['coupons_exittime'] = strtotime('+1 year');
        $data['coupons_bindingtime'] = time();
        $data['coupons_createtime'] = time();
        $last_id = $this->DataControl->insertData("smc_student_coupons", $data);
        if($last_id){
            $this->DataControl->updateData('smc_student_coupons_apply',"apply_id={$applyOne['apply_id']}",array('apply_status'=>3,'apply_price'=>$data['coupons_price'],'apply_refusereson'=>$playreason));
            return true;
        }else{
            return false;
        }
    }


}