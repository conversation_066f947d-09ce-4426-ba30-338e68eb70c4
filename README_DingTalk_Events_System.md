# 钉钉事件消息系统 v2.0

## 📖 简介

钉钉事件消息系统是一个基于事件驱动的消息通知架构，支持多种消息类型的统一发送和管理。

## ✨ 核心特性

- 🎯 **事件驱动** - 所有消息都通过事件机制触发
- 📝 **模板化** - 支持消息模板和变量替换
- 🔄 **多消息类型** - 卡片、文本、Markdown、OA、待办任务
- 🌐 **跳转控制** - 支持外部浏览器、内部浏览器等跳转模式
- 📊 **完整日志** - 记录事件触发、发送结果、用户明细
- ⚡ **高可用** - 支持失败重试和错误处理

## 🚀 快速开始

### 1. 数据库初始化

```sql
-- 执行数据库初始化脚本
source Model/Public/DingTalkEvents_Database_Fixed.sql;
```

### 2. 基本使用

```php
// 引入钉钉事件消息类
require_once 'Model/Public/DingTalkEventsModel.php';
$dingTalkEvents = new \Model\PublicModel\DingTalkEventsModel();

// 构建事件数据
$eventData = array(
    'title' => '测试消息',
    'content' => '这是一条测试消息',
    'detail_url' => 'https://www.example.com'
);

// 构建接收人
$recipients = array(
    array(
        'dd_user_id' => '619268940',
        'staffer_id' => 0,
        'name' => '张三'
    )
);

// 触发事件
$result = $dingTalkEvents->triggerEvent('general_card', $eventData, $recipients);
```

## 📁 文件结构

```
Model/Public/
├── DingTalkEventsModel.php              # 事件管理核心类
├── DingTalkEventsSenderModel.php        # 消息发送处理类
├── DingTalkEvents_Database_Fixed.sql    # 数据库初始化脚本
└── DingTalk_Events_System_Documentation.md  # 完整文档
```

## 📋 支持的消息类型

| 消息类型 | 说明 | 特点 |
|---------|------|------|
| **card** | 卡片消息 | 支持标题、内容、按钮，可点击跳转 |
| **text** | 文本消息 | 纯文本内容，简单直接 |
| **markdown** | Markdown消息 | 支持Markdown格式，富文本显示 |
| **oa** | OA消息 | 办公消息格式，支持PC和移动端链接 |
| **todo** | 待办任务 | 可创建待办任务，支持完成状态跟踪 |

## 🗄️ 数据库表结构

| 表名 | 作用 |
|------|------|
| **dd_config_event** | 事件配置表 |
| **dd_message_template** | 消息模板表 |
| **dd_events** | 事件日志表 |
| **dd_events_misuser** | 消息用户明细表 |
| **dd_events_todouser** | 待办用户明细表 |
| **dd_todo_tasks** | 钉钉待办任务表 |

## 📚 文档

详细文档请查看：[DingTalk_Events_System_Documentation.md](Model/Public/DingTalk_Events_System_Documentation.md)

## 🔧 技术要求

- **PHP**: 5.6+
- **MySQL**: 5.7+
- **依赖**: 消息中台系统

## 📝 版本信息

- **当前版本**: v2.0
- **发布日期**: 2025-08-03
- **维护团队**: 系统开发团队

## 🎯 使用示例

### 发送卡片消息

```php
$eventData = array(
    'title' => '新客户报名通知',
    'content' => '客户张三已成功报名课程',
    'detail_url' => 'https://crm.example.com/client/123'
);

$result = $dingTalkEvents->triggerEvent('crm_new_client', $eventData, $recipients);
```

### 创建待办任务

```php
$eventData = array(
    'title' => '客户跟进提醒',
    'content' => '请及时跟进客户张三的报名情况',
    'detail_url' => 'https://crm.example.com/follow/456'
);

$result = $dingTalkEvents->triggerEvent('crm_follow_reminder', $eventData, $recipients);
```

---

**📄 更多详细信息请查看完整文档**
