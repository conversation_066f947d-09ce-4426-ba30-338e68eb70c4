集团
CREATE TABLE `gmc_company` (
`company_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '序号',
`company_shortname` varchar(80) DEFAULT '' COMMENT '企业简称',
`company_cnname` varchar(80) DEFAULT '' COMMENT '企业中文名称',
`company_enname` varchar(80) DEFAULT '' COMMENT '企业英文全称',
`company_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
`company_position` varchar(40) DEFAULT NULL COMMENT '联系人职务',
`company_mobile` varchar(40) DEFAULT NULL COMMENT '联系人电话',
`company_address` varchar(120) DEFAULT '' COMMENT '所在地址',
`company_logo` varchar(255) DEFAULT '' COMMENT '企业LOGO',
`company_email` varchar(120) DEFAULT '' COMMENT '联系邮箱',
`company_phone` varchar(20) DEFAULT '' COMMENT '电话',
`company_fax` varchar(20) DEFAULT '' COMMENT '传真',
`company_homeurl` varchar(120) DEFAULT '' COMMENT '企业官网',
`company_brief` varchar(255) DEFAULT '' COMMENT '公司简介',
`company_status` tinyint(1) DEFAULT '0' COMMENT '0 待审核  1 已通过 -1 已过期',
`company_addtime` int(10) DEFAULT '0' COMMENT '添加时间',
`company_updatetime` int(10) DEFAULT '0' COMMENT '修改时间 ',
PRIMARY KEY (`company_id`),
KEY `company_cnname` (`company_cnname`),
KEY `company_status` (`company_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='集团中心-集团明细';


职工
CREATE TABLE `smc_staffer` (
`staffer_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '教师ID',
`company_id` int(11) DEFAULT '0' COMMENT '所属集团',
`postrole_id` int(11) DEFAULT '0' COMMENT '所属集团角色',
`staffer_img` varchar(200) DEFAULT '' COMMENT '教师头像',
`account_class` tinyint(1) DEFAULT '0' COMMENT '0普通账号1管理员账号',
`staffer_branch` varchar(80) NOT NULL DEFAULT '' COMMENT '教师编号',
`staffer_cnname` varchar(120) NOT NULL DEFAULT '' COMMENT '中文名称',
`staffer_enname` varchar(120) NOT NULL DEFAULT '' COMMENT '英文名称',
`staffer_birthday` varchar(40) NOT NULL DEFAULT '' COMMENT '生日',
`staffer_sex` varchar(40) NOT NULL DEFAULT '' COMMENT '性别',
`staffer_idcard` varchar(120) DEFAULT '' COMMENT '身份证号码',
`staffer_mobile` varchar(120) DEFAULT '' COMMENT '手机号码',
`staffer_pass` varchar(80) NOT NULL DEFAULT '' COMMENT '密码',
`staffer_bakpass` varchar(80) DEFAULT NULL COMMENT '备注密码',
`staffer_jointime` int(11) DEFAULT '0' COMMENT '入职时间',
`staffer_leave` tinyint(1) DEFAULT '0' COMMENT '是否离职  0未离开 1已离开',
`staffer_leavetime` int(11) DEFAULT '0' COMMENT '离职时间',
`staffer_leavecause` int(11) DEFAULT '0' COMMENT '离职原因',
`staffer_tokencode` varchar(20) DEFAULT NULL COMMENT '加密码',
`staffer_tokenencrypt` varchar(120) DEFAULT NULL COMMENT '加密值',
`staffer_lasttime` int(11) DEFAULT '0' COMMENT '最后登录时间',
`staffer_lastip` varchar(40) DEFAULT '' COMMENT '最后登录ip',
`staffer_updatetime` int(11) DEFAULT '0' COMMENT '更新时间',
`staffer_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
PRIMARY KEY (`staffer_id`),
UNIQUE KEY `staffer_mobile` (`company_id`,`staffer_mobile`),
UNIQUE KEY `staffer_branch` (`company_id`,`staffer_branch`),
KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='校务系统-员工信息表';

职工异动明细

CREATE TABLE `gmc_staffer_postchangeslog` (
`postchangeslog_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`school_id` int(120) DEFAULT '0' COMMENT '学校ID,集团职务明细为0',
`postchangeslog_type` int(2) DEFAULT '0' COMMENT '异动类型',
`postchangeslog_note` varchar(120) NOT NULL DEFAULT '' COMMENT '异动备注',
`postchangeslog_day` varchar(30) NOT NULL DEFAULT '' COMMENT '异动日期',
`postchangeslog_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
PRIMARY KEY (`postchangeslog_id`),
KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='集团中心-职工异动明细';




组织机构 （1,2,3）
CREATE TABLE `gmc_company_organize` (
`organize_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '机构编号',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`father_id` int(11) DEFAULT '0' COMMENT '上级机构 id',
`organize_class` tinyint(1) DEFAULT NULL COMMENT '机构类型：0运营机构1职能机构',
`organize_cnname` varchar(80) DEFAULT NULL COMMENT '机构中文名称',
`organize_enname` varchar(80) DEFAULT NULL COMMENT '机构英文名称',
`organize_createtime` varchar(50) DEFAULT NULL COMMENT '机构录入时间',
`organize_updatetime` varchar(50) DEFAULT NULL COMMENT '机构信息修改时间',
PRIMARY KEY (`organize_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1804 DEFAULT CHARSET=utf8 COMMENT='集团中心-组织架构表';



组织机构管辖校园表
CREATE TABLE `gmc_company_organizeschool` (
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`school_id` int(120) DEFAULT '0' COMMENT '学校ID,集团职务明细为0',
KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='集团中心-组织机构管辖校园表';

校园 （总容量）
CREATE TABLE `smc_school` (
`school_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`district_id` int(11) DEFAULT '0' COMMENT '所属集团区域ID',
`school_branch` varchar(80) NOT NULL DEFAULT '' COMMENT '校区编号',
`school_shortname` varchar(120) NOT NULL DEFAULT '' COMMENT '校园简称',
`school_cnname` varchar(120) NOT NULL DEFAULT '' COMMENT '校园名称称',
`school_enname` varchar(120) NOT NULL DEFAULT '' COMMENT '学校英文名称',
`school_province` double(6,0) DEFAULT '0' COMMENT '省',
`school_city` double(6,0) NOT NULL DEFAULT '0' COMMENT '所在城市',
`school_area` double(6,0) NOT NULL DEFAULT '0' COMMENT '所在区域',
`school_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
`school_updatatime` int(11) DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`school_id`),
UNIQUE KEY `school_branch` (`school_branch`,`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='校务系统-校园明细表';



集团职务明细表
CREATE TABLE `gmc_company_post` (
`post_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`post_type` tinyint(2) DEFAULT '0' COMMENT '类型 0-集团职务 1-校园职务',
`company_id` int(10) DEFAULT '0' COMMENT '公司ID',
`postrole_id` int(11) DEFAULT '0' COMMENT '默认所属集团角色',
`post_name` varchar(200) DEFAULT '' COMMENT '职务名称',
`post_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
`post_updatetime` int(11) DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`post_id`),
KEY `company_id` (`company_id`,`post_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='职务管理表';

职工集团职务表

CREATE TABLE `gmc_staffer_postbe` (
`postbe_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`company_id` int(10) DEFAULT '0' COMMENT '公司ID',
`school_id` int(120) DEFAULT '0' COMMENT '学校ID,集团职务明细为0',
`post_id` int(10) DEFAULT '0' COMMENT '职务ID',
`postpart_id` int(120) DEFAULT '0' COMMENT '角色ID,集团职务明细为0',
`postbe_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任职状态1任职0失职',
`postbe_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
`postbe_losetime` int(11) DEFAULT '0' COMMENT '失职时间',
PRIMARY KEY (`postbe_id`),
KEY `company_id` (`company_id`),
KEY `school_id` (`school_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='集团-职工集团职务表';



职工校园职务表（默认校园角色）
CREATE TABLE `gmc_company_post` (
`post_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`post_type` tinyint(2) DEFAULT '0' COMMENT '类型 0-集团职务 1-校园职务',
`company_id` int(10) DEFAULT '0' COMMENT '公司ID',
`postrole_id` int(11) DEFAULT '0' COMMENT '默认所属集团角色',
`post_name` varchar(200) DEFAULT '' COMMENT '职务名称',
`post_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
`post_updatetime` int(11) DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`post_id`),
KEY `company_id` (`company_id`,`post_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='职务管理表';

校园职务明细表 （绑定角色）
CREATE TABLE `gmc_staffer_postbe` (
`postbe_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`company_id` int(10) DEFAULT '0' COMMENT '公司ID',
`school_id` int(120) DEFAULT '0' COMMENT '学校ID,集团职务明细为0',
`post_id` int(10) DEFAULT '0' COMMENT '职务ID',
`postpart_id` int(120) DEFAULT '0' COMMENT '角色ID,集团职务明细为0',
`postbe_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任职状态1任职0失职',
`postbe_createtime` int(11) DEFAULT '0' COMMENT '创建时间',
`postbe_losetime` int(11) DEFAULT '0' COMMENT '失职时间',
PRIMARY KEY (`postbe_id`),
KEY `company_id` (`company_id`),
KEY `school_id` (`school_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='集团-职工集团职务表';


职工信息附表
sms_staffer_info

校园区域表（集团设置）
CREATE TABLE `gmc_company_district` (
`district_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '课程ID',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`district_cnname` varchar(80) DEFAULT '' COMMENT '区域名称',
`district_sort` int(4) DEFAULT '0' COMMENT '区域排序',
PRIMARY KEY (`district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='集团中心-学校区域设定';


校园类型表（集团设置）

CREATE TABLE `sms_code_schooltype` (
`schooltype_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '校园类型ID',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`schooltype_name` varchar(40) DEFAULT '' COMMENT '校园类型名称',
PRIMARY KEY (`schooltype_id`),
UNIQUE KEY `schooltype_name` (`schooltype_name`,`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='集团中心-校园类型';



离职原因表（集团设置）
CREATE TABLE `sms_code_reasonsleaving` (
`reasonsleaving_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '校园类型ID',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`reasonsleaving_note` varchar(120) DEFAULT '' COMMENT '离职原因',
PRIMARY KEY (`reasonsleaving_id`),
UNIQUE KEY `reasonsleaving_note` (`reasonsleaving_note`,`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='集团中心-离职原因';

职等明细表（集团设置）
CREATE TABLE `gmc_company_postlevel` (
`postlevel_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '校园类型ID',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`postlevel_cnname` varchar(120) DEFAULT '' COMMENT '职等中文名称',
`postlevel_enname` varchar(120) DEFAULT '' COMMENT '职等英文名称',
PRIMARY KEY (`postlevel_id`),
UNIQUE KEY `postlevel_cnname` (`postlevel_cnname`,`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='集团中心-职等名称';

集团角色表
CREATE TABLE `gmc_company_postrole` (
`postrole_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '校园类型ID',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`postrole_name` varchar(120) DEFAULT '' COMMENT '角色名称',
PRIMARY KEY (`postrole_id`),
UNIQUE KEY `postrole_name` (`postrole_name`,`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='集团中心-集团角色名称';

校园角色表
CREATE TABLE `smc_school_postpart` (
`postpart_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '校园类型ID',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`postpart_name` varchar(120) DEFAULT '' COMMENT '角色名称',
PRIMARY KEY (`postpart_id`),
UNIQUE KEY `postpart_name` (`postpart_name`,`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='集团中心-校园角色名称';

职工操作日日志

CREATE TABLE `sms_staffer_worklog` (
`worklog_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '序号',
`company_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属公司',
`school_id` int(120) DEFAULT '0' COMMENT '校务系统操作学校ID',
`staffer_id` int(8) DEFAULT '0' COMMENT '职工ID',
`worklog_module` varchar(50) DEFAULT '' COMMENT '操作模块',
`worklog_type` varchar(120) DEFAULT '' COMMENT '操作类型',
`worklog_content` text COMMENT '操作内容',
`worklog_ip` varchar(80) NOT NULL DEFAULT '' COMMENT 'ip记录',
`worklog__time` int(11) NOT NULL DEFAULT '0' COMMENT '操作时间',
PRIMARY KEY (`worklog_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='集团中心-职工操作日志';







