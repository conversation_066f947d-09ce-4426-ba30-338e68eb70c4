<?php

class TP_yyToken implements ArrayAccess
{

    public $string = '';

    public $metadata = array();

    public function __construct($s, $m = array())
    {
        if ($s instanceof TP_yyToken) {
            $this->string = $s->string;
            $this->metadata = $s->metadata;
        } else {
            $this->string = (string) $s;
            if ($m instanceof TP_yyToken) {
                $this->metadata = $m->metadata;
            } elseif (is_array($m)) {
                $this->metadata = $m;
            }
        }
    }

    public function __toString()
    {
        return $this->string;
    }

    public function offsetExists($offset)
    {
        return isset($this->metadata[$offset]);
    }

    public function offsetGet($offset)
    {
        return $this->metadata[$offset];
    }

    public function offsetSet($offset, $value)
    {
        if ($offset === null) {
            if (isset($value[0])) {
                $x = ($value instanceof TP_yyToken) ? $value->metadata : $value;
                $this->metadata = array_merge($this->metadata, $x);

                return;
            }
            $offset = count($this->metadata);
        }
        if ($value === null) {
            return;
        }
        if ($value instanceof TP_yyToken) {
            if ($value->metadata) {
                $this->metadata[$offset] = $value->metadata;
            }
        } elseif ($value) {
            $this->metadata[$offset] = $value;
        }
    }

    public function offsetUnset($offset)
    {
        unset($this->metadata[$offset]);
    }
}

class TP_yyStackEntry
{

    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                     ** number for the token at this stack level */
    public $minor; /* The user-supplied minor token value.  This
                     ** is the value of the token  */
}

;

#line 13 "../smarty/lexer/smarty_internal_templateparser.y"

/**
 * Smarty Internal Plugin Templateparser
 *
 * This is the template parser.
 * It is generated from the smarty_internal_templateparser.y file
 *
 * @package    Smarty
 * @subpackage Compiler
 * <AUTHOR> Tews
 */
class Smarty_Internal_Templateparser
{

    #line 26 "../smarty/lexer/smarty_internal_templateparser.y"

    const Err1 = "Security error: Call to private object member not allowed";

    const Err2 = "Security error: Call to dynamic object member not allowed";

    const Err3 = "PHP in template not allowed. Use SmartyBC to enable it";

    /**
     * result status
     *
     * @var bool
     */
    public $successful = true;

    /**
     * return value
     *
     * @var mixed
     */
    public $retvalue = 0;

    /**
     * counter for prefix code
     *
     * @var int
     */
    public static $prefix_number = 0;

    /**
     * @var
     */
    public $yymajor;

    /**
     * last index of array variable
     *
     * @var mixed
     */
    public $last_index;

    /**
     * last variable name
     *
     * @var string
     */
    public $last_variable;

    /**
     * root parse tree buffer
     *
     * @var Smarty_Internal_ParseTree
     */
    public $root_buffer;

    /**
     * current parse tree object
     *
     * @var Smarty_Internal_ParseTree
     */
    public $current_buffer;

    /**
     * lexer object
     *
     * @var Smarty_Internal_Templatelexer
     */
    private $lex;

    /**
     * internal error flag
     *
     * @var bool
     */
    private $internalError = false;

    /**
     * {strip} status
     *
     * @var bool
     */
    public $strip = false;

    /**
     * compiler object
     *
     * @var Smarty_Internal_TemplateCompilerBase
     */
    public $compiler = null;

    /**
     * smarty object
     *
     * @var Smarty
     */
    public $smarty = null;

    /**
     * template object
     *
     * @var Smarty_Internal_Template
     */
    public $template = null;

    /**
     * block nesting level
     *
     * @var int
     */
    public $block_nesting_level = 0;

    /**
     * security object
     *
     * @var Smarty_Security
     */
    private $security = null;

    /**
     * constructor
     *
     * @param Smarty_Internal_Templatelexer        $lex
     * @param Smarty_Internal_TemplateCompilerBase $compiler
     */
    function __construct(Smarty_Internal_Templatelexer $lex, Smarty_Internal_TemplateCompilerBase $compiler)
    {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->template = $this->compiler->template;
        $this->smarty = $this->template->smarty;
        $this->security = isset($this->smarty->security_policy) ? $this->smarty->security_policy : false;
        $this->current_buffer = $this->root_buffer = new Smarty_Internal_ParseTree_Template($this);
    }

    /**
     * insert PHP code in current buffer
     *
     * @param string $code
     */
    public function insertPhpCode($code)
    {
        $this->current_buffer->append_subtree(new Smarty_Internal_ParseTree_Tag($this, $code));
    }

    /**
     *  merge PHP code with prefix code and return parse tree tag object
     *
     * @param string $code
     *
     * @return Smarty_Internal_ParseTree_Tag
     */
    public function mergePrefixCode($code)
    {
        $tmp = '';
        foreach ($this->compiler->prefix_code as $preCode) {
            $tmp = empty($tmp) ? $preCode : $this->compiler->appendCode($tmp, $preCode);
        }
        $this->compiler->prefix_code = array();
        $tmp = empty($tmp) ? $code : $this->compiler->appendCode($tmp, $code);
        return new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp, true));
    }

    const TP_VERT = 1;

    const TP_COLON = 2;

    const TP_PHP = 3;

    const TP_TEXT = 4;

    const TP_STRIPON = 5;

    const TP_STRIPOFF = 6;

    const TP_BLOCKSOURCE = 7;

    const TP_LITERALSTART = 8;

    const TP_LITERALEND = 9;

    const TP_LITERAL = 10;

    const TP_RDEL = 11;

    const TP_SIMPELOUTPUT = 12;

    const TP_LDEL = 13;

    const TP_DOLLARID = 14;

    const TP_EQUAL = 15;

    const TP_SIMPLETAG = 16;

    const TP_ID = 17;

    const TP_PTR = 18;

    const TP_LDELIF = 19;

    const TP_LDELFOR = 20;

    const TP_SEMICOLON = 21;

    const TP_INCDEC = 22;

    const TP_TO = 23;

    const TP_STEP = 24;

    const TP_LDELFOREACH = 25;

    const TP_SPACE = 26;

    const TP_AS = 27;

    const TP_APTR = 28;

    const TP_LDELSETFILTER = 29;

    const TP_SMARTYBLOCKCHILDPARENT = 30;

    const TP_CLOSETAG = 31;

    const TP_LDELSLASH = 32;

    const TP_ATTR = 33;

    const TP_INTEGER = 34;

    const TP_COMMA = 35;

    const TP_OPENP = 36;

    const TP_CLOSEP = 37;

    const TP_MATH = 38;

    const TP_UNIMATH = 39;

    const TP_ISIN = 40;

    const TP_INSTANCEOF = 41;

    const TP_QMARK = 42;

    const TP_NOT = 43;

    const TP_TYPECAST = 44;

    const TP_HEX = 45;

    const TP_DOT = 46;

    const TP_SINGLEQUOTESTRING = 47;

    const TP_DOUBLECOLON = 48;

    const TP_NAMESPACE = 49;

    const TP_AT = 50;

    const TP_HATCH = 51;

    const TP_OPENB = 52;

    const TP_CLOSEB = 53;

    const TP_DOLLAR = 54;

    const TP_LOGOP = 55;

    const TP_TLOGOP = 56;

    const TP_SINGLECOND = 57;

    const TP_QUOTE = 58;

    const TP_BACKTICK = 59;

    const YY_NO_ACTION = 533;

    const YY_ACCEPT_ACTION = 532;

    const YY_ERROR_ACTION = 531;

    const YY_SZ_ACTTAB = 2133;

    static public $yy_action = array(292, 9, 133, 449, 280, 70, 208, 2, 84, 268, 11, 97, 155, 113, 267, 449, 366, 224,
        303, 264, 217, 279, 231, 26, 21, 172, 274, 42, 190, 304, 17, 43, 39, 269, 223, 298, 13, 209, 194, 81, 1, 143,
        317, 90, 148, 105, 52, 292, 9, 132, 93, 280, 200, 279, 2, 84, 35, 325, 91, 156, 113, 218, 219, 207, 224, 105,
        264, 217, 279, 205, 194, 21, 268, 11, 42, 262, 26, 267, 43, 39, 269, 223, 233, 17, 209, 194, 81, 1, 252, 317,
        186, 141, 101, 52, 292, 9, 134, 192, 280, 213, 279, 2, 84, 35, 325, 208, 12, 113, 235, 101, 220, 224, 3, 264,
        217, 361, 231, 194, 21, 218, 258, 42, 110, 105, 16, 43, 39, 269, 223, 298, 26, 209, 448, 81, 1, 7, 317, 17, 332,
        110, 52, 292, 9, 134, 448, 280, 197, 118, 2, 84, 286, 287, 288, 227, 113, 27, 229, 305, 224, 316, 264, 217, 232,
        231, 229, 21, 136, 120, 42, 241, 329, 102, 43, 39, 269, 223, 298, 243, 209, 137, 81, 1, 260, 317, 208, 10, 186,
        52, 292, 9, 134, 101, 280, 199, 406, 2, 84, 85, 313, 35, 325, 113, 27, 81, 248, 224, 317, 264, 217, 406, 212,
        25, 21, 194, 101, 42, 406, 317, 461, 43, 39, 269, 223, 298, 461, 209, 263, 81, 1, 318, 317, 92, 150, 22, 52,
        292, 9, 135, 140, 280, 213, 279, 2, 84, 110, 163, 94, 159, 113, 183, 296, 294, 224, 448, 264, 217, 279, 231,
        194, 21, 263, 191, 42, 309, 221, 448, 43, 39, 269, 223, 298, 316, 209, 194, 81, 1, 229, 317, 317, 163, 185, 52,
        292, 9, 131, 153, 280, 213, 194, 2, 84, 330, 268, 11, 279, 113, 24, 267, 221, 224, 30, 264, 217, 259, 231, 254,
        5, 194, 265, 42, 302, 192, 83, 43, 39, 269, 223, 298, 158, 209, 208, 81, 1, 6, 317, 4, 157, 279, 52, 292, 9,
        136, 154, 280, 213, 279, 2, 84, 27, 101, 247, 279, 113, 310, 192, 26, 224, 324, 264, 217, 170, 231, 17, 31, 121,
        180, 42, 139, 192, 279, 43, 39, 269, 223, 298, 162, 209, 137, 81, 1, 179, 317, 266, 10, 279, 52, 292, 9, 134,
        95, 280, 202, 194, 2, 84, 263, 266, 218, 222, 113, 146, 105, 266, 224, 208, 264, 217, 317, 231, 19, 21, 208, 32,
        42, 163, 178, 17, 43, 39, 269, 223, 298, 160, 209, 279, 81, 1, 107, 317, 184, 304, 279, 52, 292, 9, 134, 266,
        280, 213, 182, 2, 84, 230, 461, 336, 242, 113, 181, 192, 461, 224, 208, 264, 217, 145, 198, 319, 21, 189, 304,
        42, 124, 149, 279, 43, 39, 269, 223, 298, 322, 209, 279, 81, 1, 194, 317, 121, 208, 34, 52, 292, 9, 136, 266,
        280, 213, 138, 2, 84, 208, 37, 265, 152, 113, 236, 295, 37, 224, 297, 264, 217, 279, 231, 175, 31, 276, 261, 42,
        249, 187, 251, 43, 39, 269, 223, 298, 193, 209, 192, 81, 477, 477, 317, 404, 275, 477, 52, 234, 114, 270, 285,
        281, 282, 283, 289, 183, 12, 208, 404, 292, 9, 33, 331, 280, 284, 404, 2, 84, 448, 271, 250, 173, 113, 166, 98,
        246, 224, 111, 264, 217, 448, 20, 323, 221, 6, 477, 477, 229, 214, 291, 477, 115, 69, 109, 40, 41, 38, 102, 268,
        11, 278, 335, 168, 267, 211, 277, 299, 144, 260, 188, 263, 327, 328, 334, 293, 323, 279, 169, 206, 312, 229,
        214, 87, 477, 115, 69, 109, 266, 266, 108, 102, 165, 208, 278, 335, 119, 226, 211, 277, 299, 88, 260, 406, 89,
        323, 274, 86, 290, 300, 229, 214, 333, 311, 128, 59, 106, 222, 406, 151, 102, 171, 164, 278, 335, 406, 96, 211,
        277, 299, 323, 260, 300, 186, 174, 229, 214, 268, 11, 128, 65, 109, 267, 279, 300, 102, 35, 325, 278, 335, 300,
        26, 211, 277, 299, 300, 260, 300, 17, 323, 194, 300, 300, 216, 229, 214, 300, 300, 128, 65, 109, 300, 300, 300,
        102, 300, 208, 278, 335, 448, 300, 211, 277, 299, 300, 260, 400, 300, 323, 240, 300, 448, 215, 229, 214, 208,
        253, 128, 49, 106, 112, 26, 300, 102, 300, 403, 278, 335, 17, 300, 211, 277, 299, 323, 260, 300, 300, 300, 229,
        214, 403, 300, 128, 65, 109, 300, 300, 403, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300,
        300, 323, 300, 300, 300, 210, 229, 214, 300, 300, 128, 59, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300,
        300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 64, 109, 300, 300,
        300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 323, 260, 300, 300, 300, 229, 214, 300, 300, 128, 74,
        109, 300, 36, 239, 102, 300, 176, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300,
        300, 229, 78, 300, 300, 82, 44, 104, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 317, 260,
        300, 300, 323, 300, 300, 300, 300, 229, 80, 300, 300, 82, 47, 104, 300, 300, 300, 102, 300, 300, 278, 335, 300,
        300, 211, 277, 299, 323, 260, 300, 300, 300, 229, 214, 300, 300, 99, 54, 109, 300, 300, 300, 102, 300, 300, 278,
        335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 61, 109,
        300, 300, 300, 102, 300, 300, 278, 335, 314, 300, 211, 277, 299, 300, 260, 300, 292, 8, 315, 300, 280, 300, 300,
        2, 84, 300, 18, 201, 300, 113, 272, 300, 300, 224, 323, 264, 217, 300, 300, 229, 214, 300, 300, 128, 66, 109,
        300, 273, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 323, 260, 300, 301, 23, 229, 214, 300, 317,
        128, 72, 109, 300, 300, 300, 102, 300, 300, 278, 335, 314, 300, 211, 277, 299, 300, 260, 300, 292, 8, 315, 300,
        280, 300, 300, 2, 84, 300, 300, 300, 300, 113, 300, 300, 300, 224, 323, 264, 217, 300, 300, 229, 214, 300, 300,
        128, 63, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 323, 260, 300, 300, 23, 229, 214,
        300, 300, 128, 45, 109, 300, 300, 300, 102, 268, 11, 278, 335, 300, 267, 211, 277, 299, 300, 260, 300, 300, 323,
        26, 300, 147, 300, 229, 204, 300, 17, 117, 53, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277,
        299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 75, 109, 300, 300, 300, 102, 300,
        300, 278, 335, 300, 300, 211, 277, 299, 323, 260, 300, 300, 300, 229, 214, 300, 300, 128, 56, 109, 300, 300,
        300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214,
        300, 300, 116, 50, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300,
        323, 300, 300, 300, 300, 229, 214, 300, 300, 103, 67, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300,
        211, 277, 299, 323, 260, 300, 300, 300, 229, 214, 300, 300, 128, 46, 109, 300, 300, 300, 102, 300, 300, 278,
        335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 76, 109,
        300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300,
        229, 214, 300, 300, 128, 73, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 323, 260,
        300, 300, 300, 229, 214, 300, 300, 128, 77, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277,
        299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 79, 109, 300, 300, 300, 102, 300,
        300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128,
        58, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 323, 260, 300, 300, 300, 229, 203,
        300, 300, 128, 55, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300,
        323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 62, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300,
        211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 60, 109, 300, 300, 300,
        102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 323, 260, 300, 300, 300, 229, 214, 300, 300, 128, 71, 109,
        300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260, 300, 300, 323, 300, 300, 300, 300,
        229, 214, 300, 300, 128, 57, 109, 300, 300, 300, 102, 300, 300, 278, 335, 300, 300, 211, 277, 299, 300, 260,
        300, 300, 323, 300, 300, 300, 300, 229, 214, 300, 300, 128, 48, 109, 300, 300, 300, 102, 300, 300, 278, 335,
        300, 300, 211, 277, 299, 323, 260, 300, 300, 300, 229, 214, 300, 300, 100, 68, 109, 300, 300, 300, 102, 300,
        300, 278, 335, 300, 410, 211, 277, 299, 300, 260, 300, 300, 323, 300, 410, 300, 410, 229, 237, 410, 300, 122,
        300, 109, 300, 300, 410, 102, 410, 300, 410, 326, 300, 300, 211, 277, 299, 300, 260, 221, 300, 323, 300, 208,
        300, 300, 229, 237, 416, 416, 130, 300, 109, 300, 300, 300, 102, 300, 300, 300, 255, 300, 300, 211, 277, 299,
        29, 260, 26, 532, 51, 244, 287, 288, 227, 17, 300, 229, 225, 300, 40, 41, 38, 448, 300, 416, 416, 416, 477, 477,
        225, 15, 300, 477, 461, 448, 300, 327, 328, 334, 477, 477, 416, 416, 416, 477, 461, 208, 300, 300, 300, 300, 40,
        41, 38, 300, 300, 307, 300, 300, 300, 300, 461, 300, 461, 300, 477, 300, 461, 327, 328, 334, 26, 300, 461, 300,
        461, 225, 477, 17, 461, 320, 300, 300, 40, 41, 38, 477, 477, 300, 32, 300, 477, 461, 300, 300, 300, 300, 300,
        300, 300, 327, 328, 334, 300, 300, 300, 323, 300, 300, 300, 300, 229, 237, 300, 300, 127, 300, 109, 300, 300,
        461, 102, 461, 300, 477, 300, 461, 300, 211, 277, 299, 300, 260, 14, 300, 300, 323, 300, 300, 300, 300, 229,
        237, 477, 477, 126, 300, 109, 477, 461, 300, 102, 300, 300, 300, 300, 300, 300, 211, 277, 299, 300, 260, 300,
        300, 323, 300, 300, 300, 300, 229, 237, 300, 300, 123, 300, 109, 461, 300, 461, 102, 477, 300, 461, 300, 300,
        300, 211, 277, 299, 300, 260, 300, 323, 300, 300, 300, 300, 229, 237, 300, 300, 129, 300, 109, 300, 300, 300,
        102, 300, 300, 300, 300, 300, 300, 211, 277, 299, 208, 260, 225, 300, 208, 323, 300, 300, 300, 300, 229, 237,
        477, 477, 125, 300, 109, 477, 461, 300, 102, 300, 300, 300, 300, 26, 300, 211, 277, 299, 300, 260, 17, 300, 208,
        300, 300, 40, 41, 38, 256, 40, 41, 38, 245, 208, 461, 300, 461, 300, 477, 300, 461, 300, 327, 328, 334, 208,
        327, 328, 334, 300, 300, 300, 300, 300, 300, 257, 300, 208, 300, 40, 41, 38, 300, 300, 300, 300, 300, 195, 300,
        228, 40, 41, 38, 300, 300, 300, 327, 328, 334, 300, 208, 300, 40, 41, 38, 208, 28, 327, 328, 334, 196, 208, 300,
        300, 40, 41, 38, 300, 300, 327, 328, 334, 300, 300, 300, 300, 300, 300, 300, 300, 300, 327, 328, 334, 300, 300,
        300, 40, 41, 38, 208, 300, 40, 41, 38, 208, 300, 300, 40, 41, 38, 300, 477, 477, 327, 328, 334, 477, 461, 327,
        328, 334, 300, 321, 300, 327, 328, 334, 208, 300, 300, 167, 300, 300, 300, 300, 300, 40, 41, 38, 300, 300, 40,
        41, 38, 300, 461, 300, 461, 300, 477, 300, 461, 300, 327, 328, 334, 300, 308, 327, 328, 334, 208, 300, 208, 40,
        41, 38, 300, 300, 300, 300, 372, 300, 358, 300, 300, 300, 300, 238, 300, 300, 327, 328, 334, 263, 300, 26, 300,
        26, 300, 300, 186, 177, 17, 300, 17, 448, 300, 300, 186, 161, 279, 300, 163, 35, 325, 208, 300, 448, 279, 186,
        142, 35, 325, 300, 300, 306, 300, 194, 300, 279, 300, 300, 35, 325, 300, 194, 263, 300, 300, 300, 26, 300, 300,
        300, 300, 300, 194, 17, 300, 300, 300, 300, 300, 300, 300, 163,);

    static public $yy_lookahead = array(12, 13, 14, 36, 16, 17, 1, 19, 20, 12, 13, 71, 72, 25, 17, 48, 11, 29, 30, 31,
        32, 81, 34, 26, 36, 28, 92, 39, 94, 95, 33, 43, 44, 45, 46, 47, 21, 49, 98, 51, 52, 75, 54, 71, 72, 79, 58, 12,
        13, 14, 35, 16, 17, 81, 19, 20, 84, 85, 71, 72, 25, 75, 76, 77, 29, 79, 31, 32, 81, 34, 98, 36, 12, 13, 39, 17,
        26, 17, 43, 44, 45, 46, 47, 33, 49, 98, 51, 52, 53, 54, 71, 72, 18, 58, 12, 13, 14, 98, 16, 17, 81, 19, 20, 84,
        85, 1, 15, 25, 50, 18, 50, 29, 36, 31, 32, 11, 34, 98, 36, 75, 76, 39, 48, 79, 28, 43, 44, 45, 46, 47, 26, 49,
        36, 51, 52, 36, 54, 33, 17, 48, 58, 12, 13, 14, 48, 16, 17, 48, 19, 20, 63, 64, 65, 66, 25, 35, 69, 37, 29, 64,
        31, 32, 70, 34, 69, 36, 14, 75, 39, 17, 49, 79, 43, 44, 45, 46, 47, 14, 49, 46, 51, 52, 90, 54, 1, 52, 71, 58,
        12, 13, 14, 18, 16, 17, 11, 19, 20, 102, 103, 84, 85, 25, 35, 51, 37, 29, 54, 31, 32, 26, 34, 15, 36, 98, 18,
        39, 33, 54, 46, 43, 44, 45, 46, 47, 52, 49, 22, 51, 52, 95, 54, 71, 72, 15, 58, 12, 13, 14, 14, 16, 17, 81, 19,
        20, 48, 41, 71, 72, 25, 8, 9, 10, 29, 36, 31, 32, 81, 34, 98, 36, 22, 71, 39, 59, 46, 48, 43, 44, 45, 46, 47,
        64, 49, 98, 51, 52, 69, 54, 54, 41, 71, 58, 12, 13, 14, 72, 16, 17, 98, 19, 20, 53, 12, 13, 81, 25, 15, 17, 46,
        29, 13, 31, 32, 22, 34, 53, 36, 98, 99, 39, 103, 98, 17, 43, 44, 45, 46, 47, 72, 49, 1, 51, 52, 36, 54, 35, 72,
        81, 58, 12, 13, 14, 72, 16, 17, 81, 19, 20, 35, 18, 37, 81, 25, 53, 98, 26, 29, 89, 31, 32, 72, 34, 33, 36, 96,
        71, 39, 14, 98, 81, 43, 44, 45, 46, 47, 72, 49, 46, 51, 52, 91, 54, 93, 52, 81, 58, 12, 13, 14, 36, 16, 17, 98,
        19, 20, 22, 93, 75, 76, 25, 91, 79, 93, 29, 1, 31, 32, 54, 34, 26, 36, 1, 15, 39, 41, 72, 33, 43, 44, 45, 46,
        47, 72, 49, 81, 51, 52, 79, 54, 94, 95, 81, 58, 12, 13, 14, 93, 16, 17, 14, 19, 20, 17, 46, 96, 17, 25, 71, 98,
        52, 29, 1, 31, 32, 72, 34, 53, 36, 94, 95, 39, 17, 72, 81, 43, 44, 45, 46, 47, 89, 49, 81, 51, 52, 98, 54, 96,
        1, 28, 58, 12, 13, 14, 93, 16, 17, 79, 19, 20, 1, 2, 99, 72, 25, 18, 65, 2, 29, 68, 31, 32, 81, 34, 51, 36, 34,
        17, 39, 53, 80, 37, 43, 44, 45, 46, 47, 17, 49, 98, 51, 12, 13, 54, 11, 17, 17, 58, 15, 17, 34, 3, 4, 5, 6, 7,
        8, 15, 1, 26, 12, 13, 23, 17, 16, 4, 33, 19, 20, 36, 17, 53, 51, 25, 91, 80, 17, 29, 21, 31, 32, 48, 42, 64, 46,
        36, 12, 13, 69, 70, 11, 17, 73, 74, 75, 38, 39, 40, 79, 12, 13, 82, 83, 91, 17, 86, 87, 88, 72, 90, 80, 22, 55,
        56, 57, 81, 64, 81, 91, 100, 101, 69, 70, 79, 50, 73, 74, 75, 93, 93, 67, 79, 91, 1, 82, 83, 78, 50, 86, 87, 88,
        79, 90, 11, 79, 64, 92, 79, 9, 104, 69, 70, 86, 101, 73, 74, 75, 76, 26, 27, 79, 91, 91, 82, 83, 33, 91, 86, 87,
        88, 64, 90, 104, 71, 72, 69, 70, 12, 13, 73, 74, 75, 17, 81, 104, 79, 84, 85, 82, 83, 104, 26, 86, 87, 88, 104,
        90, 104, 33, 64, 98, 104, 104, 97, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 1, 82, 83, 36, 104, 86,
        87, 88, 104, 90, 11, 104, 64, 46, 104, 48, 97, 69, 70, 1, 53, 73, 74, 75, 76, 26, 104, 79, 104, 11, 82, 83, 33,
        104, 86, 87, 88, 64, 90, 104, 104, 104, 69, 70, 26, 104, 73, 74, 75, 104, 104, 33, 79, 104, 104, 82, 83, 104,
        104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 97, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104,
        104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104,
        104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104,
        13, 14, 79, 104, 17, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104,
        73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 54, 90, 104, 104, 64, 104, 104, 104, 104,
        69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 104, 104,
        69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64,
        104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 4, 104, 86, 87, 88, 104,
        90, 104, 12, 13, 14, 104, 16, 104, 104, 19, 20, 104, 13, 14, 104, 25, 17, 104, 104, 29, 64, 31, 32, 104, 104,
        69, 70, 104, 104, 73, 74, 75, 104, 34, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 58, 59, 69,
        70, 104, 54, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 4, 104, 86, 87, 88, 104, 90, 104, 12, 13, 14, 104,
        16, 104, 104, 19, 20, 104, 104, 104, 104, 25, 104, 104, 104, 29, 64, 31, 32, 104, 104, 69, 70, 104, 104, 73, 74,
        75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 58, 59, 69, 70, 104, 104, 73, 74,
        75, 104, 104, 104, 79, 12, 13, 82, 83, 104, 17, 86, 87, 88, 104, 90, 104, 104, 64, 26, 104, 28, 104, 69, 70,
        104, 33, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104,
        104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104,
        104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104,
        104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86,
        87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104,
        82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104,
        82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104,
        104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104,
        73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 104, 104, 69, 70, 104, 104,
        73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104,
        104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104,
        64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88,
        64, 90, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88,
        104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83,
        104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104,
        79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 104, 104, 69, 70, 104, 104, 73, 74, 75, 104, 104, 104,
        79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 74,
        75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69,
        70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 104, 86, 87, 88, 64, 90, 104, 104, 104, 69,
        70, 104, 104, 73, 74, 75, 104, 104, 104, 79, 104, 104, 82, 83, 104, 11, 86, 87, 88, 104, 90, 104, 104, 64, 104,
        21, 104, 23, 69, 70, 26, 104, 73, 104, 75, 104, 104, 33, 79, 35, 104, 37, 83, 104, 104, 86, 87, 88, 104, 90, 46,
        104, 64, 104, 1, 104, 104, 69, 70, 1, 2, 73, 104, 75, 104, 104, 104, 79, 104, 104, 104, 83, 104, 104, 86, 87,
        88, 24, 90, 26, 61, 62, 63, 64, 65, 66, 33, 104, 69, 2, 104, 38, 39, 40, 36, 104, 38, 39, 40, 12, 13, 2, 15,
        104, 17, 18, 48, 104, 55, 56, 57, 12, 13, 55, 56, 57, 17, 18, 1, 104, 104, 104, 104, 38, 39, 40, 104, 104, 11,
        104, 104, 104, 104, 46, 104, 48, 104, 50, 104, 52, 55, 56, 57, 26, 104, 46, 104, 48, 2, 50, 33, 52, 53, 104,
        104, 38, 39, 40, 12, 13, 104, 15, 104, 17, 18, 104, 104, 104, 104, 104, 104, 104, 55, 56, 57, 104, 104, 104, 64,
        104, 104, 104, 104, 69, 70, 104, 104, 73, 104, 75, 104, 104, 46, 79, 48, 104, 50, 104, 52, 104, 86, 87, 88, 104,
        90, 2, 104, 104, 64, 104, 104, 104, 104, 69, 70, 12, 13, 73, 104, 75, 17, 18, 104, 79, 104, 104, 104, 104, 104,
        104, 86, 87, 88, 104, 90, 104, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 104, 75, 46, 104, 48, 79, 50,
        104, 52, 104, 104, 104, 86, 87, 88, 104, 90, 104, 64, 104, 104, 104, 104, 69, 70, 104, 104, 73, 104, 75, 104,
        104, 104, 79, 104, 104, 104, 104, 104, 104, 86, 87, 88, 1, 90, 2, 104, 1, 64, 104, 104, 104, 104, 69, 70, 12,
        13, 73, 104, 75, 17, 18, 104, 79, 104, 104, 104, 104, 26, 104, 86, 87, 88, 104, 90, 33, 104, 1, 104, 104, 38,
        39, 40, 37, 38, 39, 40, 11, 1, 46, 104, 48, 104, 50, 104, 52, 104, 55, 56, 57, 1, 55, 56, 57, 104, 104, 104,
        104, 104, 104, 11, 104, 1, 104, 38, 39, 40, 104, 104, 104, 104, 104, 11, 104, 37, 38, 39, 40, 104, 104, 104, 55,
        56, 57, 104, 1, 104, 38, 39, 40, 1, 2, 55, 56, 57, 11, 1, 104, 104, 38, 39, 40, 104, 104, 55, 56, 57, 104, 104,
        104, 104, 104, 104, 104, 104, 104, 55, 56, 57, 104, 104, 104, 38, 39, 40, 1, 104, 38, 39, 40, 1, 104, 104, 38,
        39, 40, 104, 12, 13, 55, 56, 57, 17, 18, 55, 56, 57, 104, 53, 104, 55, 56, 57, 1, 104, 104, 27, 104, 104, 104,
        104, 104, 38, 39, 40, 104, 104, 38, 39, 40, 104, 46, 104, 48, 104, 50, 104, 52, 104, 55, 56, 57, 104, 59, 55,
        56, 57, 1, 104, 1, 38, 39, 40, 104, 104, 104, 104, 11, 104, 11, 104, 104, 104, 104, 18, 104, 104, 55, 56, 57,
        22, 104, 26, 104, 26, 104, 104, 71, 72, 33, 104, 33, 36, 104, 104, 71, 72, 81, 104, 41, 84, 85, 1, 104, 48, 81,
        71, 72, 84, 85, 104, 104, 11, 104, 98, 104, 81, 104, 104, 84, 85, 104, 98, 22, 104, 104, 104, 26, 104, 104, 104,
        104, 104, 98, 33, 104, 104, 104, 104, 104, 104, 104, 41,);

    const YY_SHIFT_USE_DFLT = - 34;

    const YY_SHIFT_MAX = 242;

    static public $yy_shift_ofst = array(517, 364, 270, 82, 364, 270, 82, 82, - 12, - 12, 35, 82, 82, 82, 129, 82, 82,
        82, 82, 176, 223, 82, 82, 82, 82, 82, 176, 82, 82, 82, 82, 82, 411, 82, 82, 82, 82, 317, 317, 458, 458, 458,
        458, 458, 1680, 1616, 1852, 1852, 1852, 1852, 1852, 517, 1004, 1955, 1989, 1984, 1909, 526, 1886, 1897, 1944,
        1921, 1856, 1949, 2012, 2012, 2012, 2012, 2012, 2012, 2046, 2012, 2012, 2012, 2012, 2012, 2012, 1648, 2091,
        1648, 2048, 152, 104, 319, 343, 933, - 3, 1063, 634, 634, 684, 684, 319, 343, 319, 343, 321, 319, 478, 601, 703,
        805, 60, 183, 196, 280, 91, 280, 241, 74, 415, 224, 50, 373, 466, 440, 5, 393, 415, 50, 173, 173, 400, 400, 400,
        400, 400, 400, 400, 400, - 34, 1709, 1662, 1650, 1853, 1766, 1985, 942, 556, 387, 498, 50, 50, 281, 50, 50, 133,
        163, 50, 50, 50, 163, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 121, 133, 133, 133, 163, 133, 133, 50, 133,
        163, 172, 50, 172, 287, 50, 50, 133, 400, 400, 76, 608, 173, 400, 400, 484, 484, 173, 173, 400, - 34, - 34,
        - 34, - 34, - 34, 1621, 1567, 502, 652, 543, 96, 204, 238, 252, 290, 15, 58, - 33, 120, 99, 218, 217, 363, 167,
        303, 501, 511, 508, 515, 485, 463, 461, 489, 497, 522, 530, 509, 548, 518, 507, 490, 487, 528, 479, 434, 558,
        295, 76, 418, 442, 445,);

    const YY_REDUCE_USE_DFLT = - 67;

    const YY_REDUCE_MAX = 196;

    static public $yy_reduce_ofst = array(1582, 488, 633, 658, 521, 550, 575, 604, 770, 799, 1024, 1302, 1410, 1439,
        1244, 1107, 1190, 1493, 1078, 824, 970, 687, 924, 1327, 1219, 1161, 1136, 1053, 1273, 1468, 1385, 1356, 899,
        995, 741, 716, 853, 1551, 1522, 1764, 1794, 1736, 1707, 1677, - 28, 571, 19, - 28, 2006, 2014, 2025, 87, 95,
        115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 115, 175, 115, 115, 115, 115,
        115, 115, 115, 160, 115, 160, 92, - 13, - 60, - 14, 207, 333, 380, 505, 293, 246, 213, 260, 44, 410, 312, - 66,
        340, 209, 190, 190, 338, 299, 190, 325, 299, 325, 279, 420, 325, 258, - 34, 278, 254, 366, 190, 190, 190, 370,
        372, 325, 354, 190, 190, 284, 190, 190, 190, 190, 190, 190, 496, 496, 496, 496, 496, 496, 397, 504, 496, 496,
        503, 503, 527, 503, 503, 523, 531, 503, 503, 503, 513, 503, 503, 503, 503, 503, 503, 503, 503, 503, 503, 503,
        535, 523, 523, 523, 537, 523, 523, 503, 523, 534, 510, 503, 539, 540, 503, 503, 523, - 1, - 1, 544, 532, 134,
        - 1, - 1, 382, 382, 134, 134, - 1, 499, 464, 419, 452, 481,);

    static public $yyExpectedTokens = array(array(3, 4, 5, 6, 7, 8, 12, 13, 16, 19, 20, 25, 29, 31, 32,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 30, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 30, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 53, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 52, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 54, 58,),
        array(12, 13, 14, 16, 17, 19, 20, 25, 29, 31, 32, 34, 36, 39, 43, 44, 45, 46, 47, 49, 51, 54, 58,),
        array(1, 11, 26, 33, 38, 39, 40, 55, 56, 57,), array(1, 24, 26, 33, 38, 39, 40, 55, 56, 57,),
        array(1, 26, 33, 38, 39, 40, 55, 56, 57,), array(1, 26, 33, 38, 39, 40, 55, 56, 57,),
        array(1, 26, 33, 38, 39, 40, 55, 56, 57,), array(1, 26, 33, 38, 39, 40, 55, 56, 57,),
        array(1, 26, 33, 38, 39, 40, 55, 56, 57,), array(3, 4, 5, 6, 7, 8, 12, 13, 16, 19, 20, 25, 29, 31, 32,),
        array(4, 12, 13, 14, 16, 19, 20, 25, 29, 31, 32, 58, 59,), array(1, 38, 39, 40, 53, 55, 56, 57,),
        array(1, 27, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57, 59,),
        array(1, 11, 38, 39, 40, 55, 56, 57,), array(1, 21, 38, 39, 40, 55, 56, 57,),
        array(1, 11, 38, 39, 40, 55, 56, 57,), array(1, 37, 38, 39, 40, 55, 56, 57,),
        array(1, 11, 38, 39, 40, 55, 56, 57,), array(1, 11, 38, 39, 40, 55, 56, 57,),
        array(1, 37, 38, 39, 40, 55, 56, 57,), array(1, 2, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,),
        array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,),
        array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,), array(1, 11, 18, 26, 33, 36, 48,),
        array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,),
        array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,), array(1, 38, 39, 40, 55, 56, 57,),
        array(38, 39, 40, 55, 56, 57,), array(1, 11, 22, 26, 33, 41,), array(38, 39, 40, 55, 56, 57,),
        array(1, 11, 22, 26, 33, 41,), array(14, 17, 51, 54,), array(1, 11, 26, 33,), array(1, 26, 33,),
        array(14, 36, 54,), array(4, 12, 13, 14, 16, 19, 20, 25, 29, 31, 32, 58, 59,), array(12, 13, 17, 26, 28, 33,),
        array(12, 13, 17, 26, 28, 33,), array(12, 13, 17, 26, 33,), array(12, 13, 17, 26, 33,), array(1, 11, 26, 33,),
        array(1, 11, 26, 33,), array(1, 26, 33,), array(14, 36, 54,), array(1, 26, 33,), array(14, 36, 54,),
        array(18, 46, 52,), array(1, 26, 33,), array(1, 2,), array(1, 11, 26, 27, 33,), array(1, 11, 26, 33,),
        array(13, 14, 17, 54,), array(12, 13, 17, 50,), array(1, 11, 26, 33,), array(15, 18, 48,), array(12, 13, 17,),
        array(15, 18, 48,), array(12, 13, 17,), array(8, 9, 10,), array(18, 48,), array(14, 17,), array(14, 54,),
        array(26, 33,), array(26, 33,), array(1, 18,), array(1, 28,), array(1, 11,), array(1, 53,), array(14, 17,),
        array(26, 33,), array(18,), array(18,), array(1,), array(1,), array(1,), array(1,), array(1,), array(1,),
        array(1,), array(1,), array(), array(2, 12, 13, 15, 17, 18, 46, 48, 50, 52,),
        array(2, 12, 13, 17, 18, 46, 48, 50, 52, 53,), array(2, 12, 13, 15, 17, 18, 46, 48, 50, 52,),
        array(2, 12, 13, 17, 18, 46, 48, 50, 52,), array(2, 12, 13, 17, 18, 46, 48, 50, 52,),
        array(12, 13, 17, 18, 46, 48, 50, 52,), array(13, 14, 17, 34, 54,), array(12, 13, 17, 50,), array(15, 46, 52,),
        array(12, 13, 17,), array(26, 33,), array(26, 33,), array(15, 22,), array(26, 33,), array(26, 33,),
        array(46, 52,), array(14, 54,), array(26, 33,), array(26, 33,), array(26, 33,), array(14, 54,), array(26, 33,),
        array(26, 33,), array(26, 33,), array(26, 33,), array(26, 33,), array(26, 33,), array(26, 33,), array(26, 33,),
        array(26, 33,), array(26, 33,), array(26, 33,), array(17, 49,), array(46, 52,), array(46, 52,), array(46, 52,),
        array(14, 54,), array(46, 52,), array(46, 52,), array(26, 33,), array(46, 52,), array(14, 54,), array(46, 52,),
        array(26, 33,), array(46, 52,), array(13, 36,), array(26, 33,), array(26, 33,), array(46, 52,), array(1,),
        array(1,), array(36,), array(9,), array(18,), array(1,), array(1,), array(2,), array(2,), array(18,),
        array(18,), array(1,), array(), array(), array(), array(), array(),
        array(1, 2, 36, 38, 39, 40, 48, 55, 56, 57,), array(11, 21, 23, 26, 33, 35, 37, 46,),
        array(11, 15, 26, 33, 36, 48,), array(36, 46, 48, 53,), array(12, 13, 17, 50,), array(28, 36, 48,),
        array(22, 41, 59,), array(22, 41, 53,), array(46, 53,), array(35, 53,), array(21, 35,), array(17, 50,),
        array(36, 48,), array(35, 37,), array(36, 48,), array(15, 46,), array(36, 48,), array(22, 41,), array(35, 37,),
        array(35, 37,), array(17,), array(15,), array(23,), array(17,), array(34,), array(37,), array(34,), array(17,),
        array(17,), array(17,), array(4,), array(42,), array(11,), array(36,), array(46,), array(51,), array(53,),
        array(17,), array(17,), array(17,), array(22,), array(17,), array(36,), array(17,), array(51,), array(53,),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),);

    static public $yy_default = array(340, 516, 531, 496, 531, 531, 496, 496, 531, 531, 531, 531, 531, 531, 531, 531,
        531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531, 531,
        531, 531, 531, 531, 531, 531, 531, 400, 400, 400, 400, 376, 367, 337, 531, 531, 405, 531, 531, 531, 531, 531,
        531, 531, 531, 531, 421, 495, 411, 405, 402, 519, 438, 412, 407, 381, 517, 494, 518, 426, 428, 427, 428, 531,
        414, 400, 531, 531, 400, 400, 400, 400, 420, 445, 400, 531, 400, 531, 508, 400, 390, 414, 414, 531, 461, 414,
        451, 461, 451, 461, 531, 451, 531, 531, 378, 400, 394, 414, 414, 414, 531, 400, 451, 505, 424, 418, 396, 417,
        429, 430, 414, 431, 503, 450, 450, 450, 450, 450, 450, 531, 463, 477, 461, 369, 368, 531, 387, 380, 459, 531,
        365, 386, 360, 531, 373, 362, 359, 374, 363, 385, 364, 371, 375, 377, 389, 531, 486, 457, 488, 531, 489, 458,
        379, 455, 531, 456, 383, 454, 461, 384, 388, 487, 397, 395, 461, 353, 483, 391, 420, 498, 497, 506, 509, 445,
        502, 502, 502, 461, 461, 438, 434, 438, 438, 462, 438, 428, 428, 434, 531, 531, 531, 446, 531, 531, 434, 438,
        428, 531, 531, 531, 531, 408, 531, 436, 531, 531, 531, 531, 531, 344, 440, 531, 507, 434, 531, 441, 531, 531,
        531, 428, 531, 477, 531, 531, 531, 477, 338, 482, 401, 492, 491, 469, 470, 413, 476, 468, 471, 504, 440, 467,
        409, 382, 453, 499, 500, 432, 393, 501, 479, 480, 481, 433, 435, 464, 465, 466, 460, 416, 437, 439, 415, 399,
        370, 345, 347, 348, 346, 343, 339, 341, 342, 349, 350, 356, 357, 398, 355, 354, 351, 352, 441, 442, 520, 521,
        522, 392, 484, 493, 527, 528, 525, 524, 513, 515, 514, 523, 530, 526, 529, 478, 485, 474, 472, 475, 447, 444,
        443, 422, 423, 510, 511, 449, 473, 452, 448, 425, 512, 419, 490,);

    const YYNOCODE = 105;

    const YYSTACKDEPTH = 500;

    const YYNSTATE = 337;

    const YYNRULE = 194;

    const YYERRORSYMBOL = 60;

    const YYERRSYMDT = 'yy0';

    const YYFALLBACK = 0;

    public static $yyFallback = array();

    public function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        $this->yyTraceFILE = $TraceFILE;
        $this->yyTracePrompt = $zTracePrompt;
    }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

    public $yyTraceFILE;

    public $yyTracePrompt;

    public $yyidx;                    /* Index of top element in stack */
    public $yyerrcnt;                 /* Shifts left before out of the error */
    public $yystack = array();  /* The parser's stack */

    public $yyTokenName = array('$', 'VERT', 'COLON', 'PHP', 'TEXT', 'STRIPON', 'STRIPOFF', 'BLOCKSOURCE',
        'LITERALSTART', 'LITERALEND', 'LITERAL', 'RDEL', 'SIMPELOUTPUT', 'LDEL', 'DOLLARID', 'EQUAL', 'SIMPLETAG', 'ID',
        'PTR', 'LDELIF', 'LDELFOR', 'SEMICOLON', 'INCDEC', 'TO', 'STEP', 'LDELFOREACH', 'SPACE', 'AS', 'APTR',
        'LDELSETFILTER', 'SMARTYBLOCKCHILDPARENT', 'CLOSETAG', 'LDELSLASH', 'ATTR', 'INTEGER', 'COMMA', 'OPENP',
        'CLOSEP', 'MATH', 'UNIMATH', 'ISIN', 'INSTANCEOF', 'QMARK', 'NOT', 'TYPECAST', 'HEX', 'DOT',
        'SINGLEQUOTESTRING', 'DOUBLECOLON', 'NAMESPACE', 'AT', 'HATCH', 'OPENB', 'CLOSEB', 'DOLLAR', 'LOGOP', 'TLOGOP',
        'SINGLECOND', 'QUOTE', 'BACKTICK', 'error', 'start', 'template', 'template_element', 'smartytag', 'literal',
        'text_content', 'literal_elements', 'literal_element', 'tag', 'variable', 'modifierlist', 'attributes', 'value',
        'expr', 'varindexed', 'statement', 'statements', 'foraction', 'varvar', 'modparameters', 'attribute', 'ternary',
        'array', 'lop', 'scond', 'ns1', 'function', 'doublequoted_with_quotes', 'static_class_access', 'object',
        'arrayindex', 'indexdef', 'varvarele', 'objectchain', 'objectelement', 'method', 'params', 'modifier',
        'modparameter', 'arrayelements', 'arrayelement', 'doublequoted', 'doublequotedcontent',);

    public static $yyRuleName = array('start ::= template', 'template ::= template_element',
        'template ::= template template_element', 'template ::=', 'template_element ::= smartytag',
        'template_element ::= literal', 'template_element ::= PHP', 'template_element ::= text_content',
        'text_content ::= TEXT', 'text_content ::= text_content TEXT', 'template_element ::= STRIPON',
        'template_element ::= STRIPOFF', 'template_element ::= BLOCKSOURCE', 'literal ::= LITERALSTART LITERALEND',
        'literal ::= LITERALSTART literal_elements LITERALEND', 'literal_elements ::= literal_elements literal_element',
        'literal_elements ::=', 'literal_element ::= literal', 'literal_element ::= LITERAL', 'smartytag ::= tag RDEL',
        'smartytag ::= SIMPELOUTPUT', 'tag ::= LDEL variable', 'tag ::= LDEL variable modifierlist attributes',
        'tag ::= LDEL variable attributes', 'tag ::= LDEL value', 'tag ::= LDEL value modifierlist attributes',
        'tag ::= LDEL value attributes', 'tag ::= LDEL expr modifierlist attributes', 'tag ::= LDEL expr attributes',
        'tag ::= LDEL DOLLARID EQUAL value', 'tag ::= LDEL DOLLARID EQUAL expr',
        'tag ::= LDEL DOLLARID EQUAL expr attributes', 'tag ::= LDEL varindexed EQUAL expr attributes',
        'smartytag ::= SIMPLETAG', 'tag ::= LDEL ID attributes', 'tag ::= LDEL ID',
        'tag ::= LDEL ID modifierlist attributes', 'tag ::= LDEL ID PTR ID attributes',
        'tag ::= LDEL ID PTR ID modifierlist attributes', 'tag ::= LDELIF expr', 'tag ::= LDELIF expr attributes',
        'tag ::= LDELIF statement', 'tag ::= LDELIF statement attributes',
        'tag ::= LDELFOR statements SEMICOLON expr SEMICOLON varindexed foraction attributes',
        'foraction ::= EQUAL expr', 'foraction ::= INCDEC', 'tag ::= LDELFOR statement TO expr attributes',
        'tag ::= LDELFOR statement TO expr STEP expr attributes', 'tag ::= LDELFOREACH attributes',
        'tag ::= LDELFOREACH SPACE value AS varvar attributes',
        'tag ::= LDELFOREACH SPACE value AS varvar APTR varvar attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar APTR varvar attributes', 'tag ::= LDELSETFILTER ID modparameters',
        'tag ::= LDELSETFILTER ID modparameters modifierlist', 'tag ::= LDEL SMARTYBLOCKCHILDPARENT',
        'smartytag ::= CLOSETAG', 'tag ::= LDELSLASH ID', 'tag ::= LDELSLASH ID modifierlist',
        'tag ::= LDELSLASH ID PTR ID', 'tag ::= LDELSLASH ID PTR ID modifierlist',
        'attributes ::= attributes attribute', 'attributes ::= attribute', 'attributes ::=',
        'attribute ::= SPACE ID EQUAL ID', 'attribute ::= ATTR expr', 'attribute ::= ATTR value',
        'attribute ::= SPACE ID', 'attribute ::= SPACE expr', 'attribute ::= SPACE value',
        'attribute ::= SPACE INTEGER EQUAL expr', 'statements ::= statement',
        'statements ::= statements COMMA statement', 'statement ::= DOLLARID EQUAL INTEGER',
        'statement ::= DOLLARID EQUAL expr', 'statement ::= varindexed EQUAL expr',
        'statement ::= OPENP statement CLOSEP', 'expr ::= value', 'expr ::= ternary', 'expr ::= DOLLARID COLON ID',
        'expr ::= expr MATH value', 'expr ::= expr UNIMATH value', 'expr ::= array', 'expr ::= expr modifierlist',
        'expr ::= expr lop expr', 'expr ::= expr scond', 'expr ::= expr ISIN array', 'expr ::= expr ISIN value',
        'expr ::= variable INSTANCEOF ns1', 'ternary ::= OPENP expr CLOSEP QMARK DOLLARID COLON expr',
        'ternary ::= OPENP expr CLOSEP QMARK expr COLON expr', 'value ::= variable', 'value ::= UNIMATH value',
        'value ::= NOT value', 'value ::= TYPECAST value', 'value ::= variable INCDEC', 'value ::= HEX',
        'value ::= INTEGER', 'value ::= INTEGER DOT INTEGER', 'value ::= INTEGER DOT', 'value ::= DOT INTEGER',
        'value ::= ID', 'value ::= function', 'value ::= OPENP expr CLOSEP', 'value ::= SINGLEQUOTESTRING',
        'value ::= doublequoted_with_quotes', 'value ::= varindexed DOUBLECOLON static_class_access',
        'value ::= smartytag', 'value ::= value modifierlist', 'value ::= NAMESPACE',
        'value ::= ns1 DOUBLECOLON static_class_access', 'ns1 ::= ID', 'ns1 ::= NAMESPACE', 'variable ::= DOLLARID',
        'variable ::= varindexed', 'variable ::= varvar AT ID', 'variable ::= object', 'variable ::= HATCH ID HATCH',
        'variable ::= HATCH ID HATCH arrayindex', 'variable ::= HATCH variable HATCH',
        'variable ::= HATCH variable HATCH arrayindex', 'varindexed ::= DOLLARID arrayindex',
        'varindexed ::= varvar arrayindex', 'arrayindex ::= arrayindex indexdef', 'arrayindex ::=',
        'indexdef ::= DOT DOLLARID', 'indexdef ::= DOT varvar', 'indexdef ::= DOT varvar AT ID', 'indexdef ::= DOT ID',
        'indexdef ::= DOT INTEGER', 'indexdef ::= DOT LDEL expr RDEL', 'indexdef ::= OPENB ID CLOSEB',
        'indexdef ::= OPENB ID DOT ID CLOSEB', 'indexdef ::= OPENB SINGLEQUOTESTRING CLOSEB',
        'indexdef ::= OPENB INTEGER CLOSEB', 'indexdef ::= OPENB DOLLARID CLOSEB', 'indexdef ::= OPENB variable CLOSEB',
        'indexdef ::= OPENB value CLOSEB', 'indexdef ::= OPENB expr CLOSEB', 'indexdef ::= OPENB CLOSEB',
        'varvar ::= DOLLARID', 'varvar ::= DOLLAR', 'varvar ::= varvar varvarele', 'varvarele ::= ID',
        'varvarele ::= SIMPELOUTPUT', 'varvarele ::= LDEL expr RDEL', 'object ::= varindexed objectchain',
        'objectchain ::= objectelement', 'objectchain ::= objectchain objectelement',
        'objectelement ::= PTR ID arrayindex', 'objectelement ::= PTR varvar arrayindex',
        'objectelement ::= PTR LDEL expr RDEL arrayindex', 'objectelement ::= PTR ID LDEL expr RDEL arrayindex',
        'objectelement ::= PTR method', 'function ::= ns1 OPENP params CLOSEP', 'method ::= ID OPENP params CLOSEP',
        'method ::= DOLLARID OPENP params CLOSEP', 'params ::= params COMMA expr', 'params ::= expr', 'params ::=',
        'modifierlist ::= modifierlist modifier modparameters', 'modifierlist ::= modifier modparameters',
        'modifier ::= VERT AT ID', 'modifier ::= VERT ID', 'modparameters ::= modparameters modparameter',
        'modparameters ::=', 'modparameter ::= COLON value', 'modparameter ::= COLON array',
        'static_class_access ::= method', 'static_class_access ::= method objectchain', 'static_class_access ::= ID',
        'static_class_access ::= DOLLARID arrayindex', 'static_class_access ::= DOLLARID arrayindex objectchain',
        'lop ::= LOGOP', 'lop ::= TLOGOP', 'scond ::= SINGLECOND', 'array ::= OPENB arrayelements CLOSEB',
        'arrayelements ::= arrayelement', 'arrayelements ::= arrayelements COMMA arrayelement', 'arrayelements ::=',
        'arrayelement ::= value APTR expr', 'arrayelement ::= ID APTR expr', 'arrayelement ::= expr',
        'doublequoted_with_quotes ::= QUOTE QUOTE', 'doublequoted_with_quotes ::= QUOTE doublequoted QUOTE',
        'doublequoted ::= doublequoted doublequotedcontent', 'doublequoted ::= doublequotedcontent',
        'doublequotedcontent ::= BACKTICK variable BACKTICK', 'doublequotedcontent ::= BACKTICK expr BACKTICK',
        'doublequotedcontent ::= DOLLARID', 'doublequotedcontent ::= LDEL variable RDEL',
        'doublequotedcontent ::= LDEL expr RDEL', 'doublequotedcontent ::= smartytag', 'doublequotedcontent ::= TEXT',);

    public function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[$tokenType];
        } else {
            return "Unknown";
        }
    }

    public static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:
                break;   /* If no destructor action specified: do nothing */
        }
    }

    public function yy_pop_parser_stack()
    {
        if (empty($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if ($this->yyTraceFILE && $this->yyidx >= 0) {
            fwrite($this->yyTraceFILE, $this->yyTracePrompt . 'Popping ' . $this->yyTokenName[$yytos->major] . "\n");
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx --;

        return $yymajor;
    }

    public function __destruct()
    {
        while ($this->yystack !== Array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    public function yy_get_expected_tokens($token)
    {
        static $res3 = array();
        static $res4 = array();
        $state = $this->yystack[$this->yyidx]->stateno;
        $expected = self::$yyExpectedTokens[$state];
        if (isset($res3[$state][$token])) {
            if ($res3[$state][$token]) {
                return $expected;
            }
        } else {
            if ($res3[$state][$token] = in_array($token, self::$yyExpectedTokens[$state], true)) {
                return $expected;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done ++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno][1];
                    $nextstate = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, self::$yyRuleInfo[$yyruleno][0]);
                    if (isset(self::$yyExpectedTokens[$nextstate])) {
                        $expected = array_merge($expected, self::$yyExpectedTokens[$nextstate]);
                        if (isset($res4[$nextstate][$token])) {
                            if ($res4[$nextstate][$token]) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        } else {
                            if ($res4[$nextstate][$token] = in_array($token, self::$yyExpectedTokens[$nextstate], true)) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx ++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno][0];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return array_unique($expected);
    }

    public function yy_is_expected_token($token)
    {
        static $res = array();
        static $res2 = array();
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[$this->yyidx]->stateno;
        if (isset($res[$state][$token])) {
            if ($res[$state][$token]) {
                return true;
            }
        } else {
            if ($res[$state][$token] = in_array($token, self::$yyExpectedTokens[$state], true)) {
                return true;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done ++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno][1];
                    $nextstate = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, self::$yyRuleInfo[$yyruleno][0]);
                    if (isset($res2[$nextstate][$token])) {
                        if ($res2[$nextstate][$token]) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    } else {
                        if ($res2[$nextstate][$token] = (isset(self::$yyExpectedTokens[$nextstate]) && in_array($token, self::$yyExpectedTokens[$nextstate], true))) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx ++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno][0];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return true;
    }

    public function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[$this->yyidx]->stateno;

        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[$stateno])) {
            // no shift actions
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_shift_ofst[$stateno];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB || self::$yy_lookahead[$i] != $iLookAhead) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback) && ($iFallback = self::$yyFallback[$iLookAhead]) != 0) {
                if ($this->yyTraceFILE) {
                    fwrite($this->yyTraceFILE, $this->yyTracePrompt . "FALLBACK " . $this->yyTokenName[$iLookAhead] . " => " . $this->yyTokenName[$iFallback] . "\n");
                }

                return $this->yy_find_shift_action($iFallback);
            }

            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */

        if (!isset(self::$yy_reduce_ofst[$stateno])) {
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_reduce_ofst[$stateno];
        if ($i == self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB || self::$yy_lookahead[$i] != $iLookAhead) {
            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx ++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx --;
            if ($this->yyTraceFILE) {
                fprintf($this->yyTraceFILE, "%sStack Overflow!\n", $this->yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
            #line 190 "../smarty/lexer/smarty_internal_templateparser.y"

            $this->internalError = true;
            $this->compiler->trigger_template_error("Stack overflow in template parser");

            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        $this->yystack[] = $yytos;
        if ($this->yyTraceFILE && $this->yyidx > 0) {
            fprintf($this->yyTraceFILE, "%sShift %d\n", $this->yyTracePrompt, $yyNewState);
            fprintf($this->yyTraceFILE, "%sStack:", $this->yyTracePrompt);
            for ($i = 1; $i <= $this->yyidx; $i ++) {
                fprintf($this->yyTraceFILE, " %s", $this->yyTokenName[$this->yystack[$i]->major]);
            }
            fwrite($this->yyTraceFILE, "\n");
        }
    }

    public static $yyRuleInfo = array(array(0 => 61, 1 => 1), array(0 => 62, 1 => 1), array(0 => 62, 1 => 2),
        array(0 => 62, 1 => 0), array(0 => 63, 1 => 1), array(0 => 63, 1 => 1), array(0 => 63, 1 => 1),
        array(0 => 63, 1 => 1), array(0 => 66, 1 => 1), array(0 => 66, 1 => 2), array(0 => 63, 1 => 1),
        array(0 => 63, 1 => 1), array(0 => 63, 1 => 1), array(0 => 65, 1 => 2), array(0 => 65, 1 => 3),
        array(0 => 67, 1 => 2), array(0 => 67, 1 => 0), array(0 => 68, 1 => 1), array(0 => 68, 1 => 1),
        array(0 => 64, 1 => 2), array(0 => 64, 1 => 1), array(0 => 69, 1 => 2), array(0 => 69, 1 => 4),
        array(0 => 69, 1 => 3), array(0 => 69, 1 => 2), array(0 => 69, 1 => 4), array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 4), array(0 => 69, 1 => 3), array(0 => 69, 1 => 4), array(0 => 69, 1 => 4),
        array(0 => 69, 1 => 5), array(0 => 69, 1 => 5), array(0 => 64, 1 => 1), array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 2), array(0 => 69, 1 => 4), array(0 => 69, 1 => 5), array(0 => 69, 1 => 6),
        array(0 => 69, 1 => 2), array(0 => 69, 1 => 3), array(0 => 69, 1 => 2), array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 8), array(0 => 78, 1 => 2), array(0 => 78, 1 => 1), array(0 => 69, 1 => 5),
        array(0 => 69, 1 => 7), array(0 => 69, 1 => 2), array(0 => 69, 1 => 6), array(0 => 69, 1 => 8),
        array(0 => 69, 1 => 6), array(0 => 69, 1 => 8), array(0 => 69, 1 => 3), array(0 => 69, 1 => 4),
        array(0 => 69, 1 => 2), array(0 => 64, 1 => 1), array(0 => 69, 1 => 2), array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 4), array(0 => 69, 1 => 5), array(0 => 72, 1 => 2), array(0 => 72, 1 => 1),
        array(0 => 72, 1 => 0), array(0 => 81, 1 => 4), array(0 => 81, 1 => 2), array(0 => 81, 1 => 2),
        array(0 => 81, 1 => 2), array(0 => 81, 1 => 2), array(0 => 81, 1 => 2), array(0 => 81, 1 => 4),
        array(0 => 77, 1 => 1), array(0 => 77, 1 => 3), array(0 => 76, 1 => 3), array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3), array(0 => 76, 1 => 3), array(0 => 74, 1 => 1), array(0 => 74, 1 => 1),
        array(0 => 74, 1 => 3), array(0 => 74, 1 => 3), array(0 => 74, 1 => 3), array(0 => 74, 1 => 1),
        array(0 => 74, 1 => 2), array(0 => 74, 1 => 3), array(0 => 74, 1 => 2), array(0 => 74, 1 => 3),
        array(0 => 74, 1 => 3), array(0 => 74, 1 => 3), array(0 => 82, 1 => 7), array(0 => 82, 1 => 7),
        array(0 => 73, 1 => 1), array(0 => 73, 1 => 2), array(0 => 73, 1 => 2), array(0 => 73, 1 => 2),
        array(0 => 73, 1 => 2), array(0 => 73, 1 => 1), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3),
        array(0 => 73, 1 => 2), array(0 => 73, 1 => 2), array(0 => 73, 1 => 1), array(0 => 73, 1 => 1),
        array(0 => 73, 1 => 3), array(0 => 73, 1 => 1), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3),
        array(0 => 73, 1 => 1), array(0 => 73, 1 => 2), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3),
        array(0 => 86, 1 => 1), array(0 => 86, 1 => 1), array(0 => 70, 1 => 1), array(0 => 70, 1 => 1),
        array(0 => 70, 1 => 3), array(0 => 70, 1 => 1), array(0 => 70, 1 => 3), array(0 => 70, 1 => 4),
        array(0 => 70, 1 => 3), array(0 => 70, 1 => 4), array(0 => 75, 1 => 2), array(0 => 75, 1 => 2),
        array(0 => 91, 1 => 2), array(0 => 91, 1 => 0), array(0 => 92, 1 => 2), array(0 => 92, 1 => 2),
        array(0 => 92, 1 => 4), array(0 => 92, 1 => 2), array(0 => 92, 1 => 2), array(0 => 92, 1 => 4),
        array(0 => 92, 1 => 3), array(0 => 92, 1 => 5), array(0 => 92, 1 => 3), array(0 => 92, 1 => 3),
        array(0 => 92, 1 => 3), array(0 => 92, 1 => 3), array(0 => 92, 1 => 3), array(0 => 92, 1 => 3),
        array(0 => 92, 1 => 2), array(0 => 79, 1 => 1), array(0 => 79, 1 => 1), array(0 => 79, 1 => 2),
        array(0 => 93, 1 => 1), array(0 => 93, 1 => 1), array(0 => 93, 1 => 3), array(0 => 90, 1 => 2),
        array(0 => 94, 1 => 1), array(0 => 94, 1 => 2), array(0 => 95, 1 => 3), array(0 => 95, 1 => 3),
        array(0 => 95, 1 => 5), array(0 => 95, 1 => 6), array(0 => 95, 1 => 2), array(0 => 87, 1 => 4),
        array(0 => 96, 1 => 4), array(0 => 96, 1 => 4), array(0 => 97, 1 => 3), array(0 => 97, 1 => 1),
        array(0 => 97, 1 => 0), array(0 => 71, 1 => 3), array(0 => 71, 1 => 2), array(0 => 98, 1 => 3),
        array(0 => 98, 1 => 2), array(0 => 80, 1 => 2), array(0 => 80, 1 => 0), array(0 => 99, 1 => 2),
        array(0 => 99, 1 => 2), array(0 => 89, 1 => 1), array(0 => 89, 1 => 2), array(0 => 89, 1 => 1),
        array(0 => 89, 1 => 2), array(0 => 89, 1 => 3), array(0 => 84, 1 => 1), array(0 => 84, 1 => 1),
        array(0 => 85, 1 => 1), array(0 => 83, 1 => 3), array(0 => 100, 1 => 1), array(0 => 100, 1 => 3),
        array(0 => 100, 1 => 0), array(0 => 101, 1 => 3), array(0 => 101, 1 => 3), array(0 => 101, 1 => 1),
        array(0 => 88, 1 => 2), array(0 => 88, 1 => 3), array(0 => 102, 1 => 2), array(0 => 102, 1 => 1),
        array(0 => 103, 1 => 3), array(0 => 103, 1 => 3), array(0 => 103, 1 => 1), array(0 => 103, 1 => 3),
        array(0 => 103, 1 => 3), array(0 => 103, 1 => 1), array(0 => 103, 1 => 1),);

    public static $yyReduceMap = array(0   => 0, 1 => 1, 2 => 2, 4 => 4, 5 => 5, 6 => 6, 7 => 7, 8 => 8, 17 => 8,
                                       18  => 8, 45 => 8, 68 => 8, 69 => 8, 77 => 8, 78 => 8, 82 => 8, 91 => 8, 96 => 8,
                                       97  => 8, 102 => 8, 104 => 8, 105 => 8, 109 => 8, 111 => 8, 112 => 8, 116 => 8,
                                       177 => 8, 182 => 8, 9 => 9, 10 => 10, 11 => 11, 12 => 12, 13 => 13, 16 => 13,
                                       14  => 14, 76 => 14, 15 => 15, 92 => 15, 94 => 15, 95 => 15, 123 => 15, 19 => 19,
                                       20  => 20, 21 => 21, 24 => 21, 22 => 22, 25 => 22, 23 => 23, 26 => 23, 28 => 23,
                                       27  => 27, 29 => 29, 30 => 29, 31 => 31, 32 => 32, 33 => 33, 34 => 34, 35 => 35,
                                       36  => 36, 37 => 37, 38 => 38, 39 => 39, 40 => 40, 42 => 40, 41 => 41, 43 => 43,
                                       44  => 44, 46 => 46, 47 => 47, 48 => 48, 49 => 49, 51 => 49, 50 => 50, 52 => 50,
                                       53  => 53, 54 => 54, 55 => 55, 56 => 56, 57 => 57, 58 => 58, 59 => 59, 60 => 60,
                                       61  => 61, 62 => 62, 71 => 62, 158 => 62, 162 => 62, 166 => 62, 167 => 62,
                                       63  => 63, 159 => 63, 165 => 63, 64 => 64, 65 => 65, 66 => 65, 67 => 67,
                                       143 => 67, 70 => 70, 72 => 72, 73 => 73, 74 => 73, 75 => 75, 79 => 79, 80 => 80,
                                       81  => 80, 83 => 83, 108 => 83, 84 => 84, 85 => 85, 86 => 86, 87 => 87, 88 => 88,
                                       89  => 89, 90 => 90, 93 => 93, 98 => 98, 99 => 99, 100 => 100, 101 => 101,
                                       103 => 103, 106 => 106, 107 => 107, 110 => 110, 113 => 113, 114 => 114,
                                       115 => 115, 117 => 117, 118 => 118, 119 => 119, 120 => 120, 121 => 121,
                                       122 => 122, 124 => 124, 179 => 124, 125 => 125, 126 => 126, 127 => 127,
                                       128 => 128, 129 => 129, 130 => 130, 138 => 130, 131 => 131, 132 => 132,
                                       133 => 133, 134 => 133, 136 => 133, 137 => 133, 135 => 135, 139 => 139,
                                       140 => 140, 141 => 141, 183 => 141, 142 => 142, 144 => 144, 145 => 145,
                                       146 => 146, 147 => 147, 148 => 148, 149 => 149, 150 => 150, 151 => 151,
                                       152 => 152, 153 => 153, 154 => 154, 155 => 155, 156 => 156, 157 => 157,
                                       160 => 160, 161 => 161, 163 => 163, 164 => 164, 168 => 168, 169 => 169,
                                       170 => 170, 171 => 171, 172 => 172, 173 => 173, 174 => 174, 175 => 175,
                                       176 => 176, 178 => 178, 180 => 180, 181 => 181, 184 => 184, 185 => 185,
                                       186 => 186, 187 => 187, 188 => 187, 190 => 187, 189 => 189, 191 => 191,
                                       192 => 192, 193 => 193,);

    #line 201 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r0()
    {
        $this->_retvalue = $this->root_buffer->to_smarty_php();
    }

    #line 209 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r1()
    {
        if ($this->yystack[$this->yyidx + 0]->minor != null) {
            $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 216 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r2()
    {
        if ($this->yystack[$this->yyidx + 0]->minor != null) {
            // because of possible code injection
            $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 230 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r4()
    {
        if ($this->compiler->has_code) {
            $this->_retvalue = $this->mergePrefixCode($this->yystack[$this->yyidx + 0]->minor);
        } else {
            $this->_retvalue = null;
        }
        $this->compiler->has_variable_string = false;
        $this->block_nesting_level = count($this->compiler->_tag_stack);
    }

    #line 241 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r5()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Text($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 245 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r6()
    {
        $code = $this->compiler->compileTag('private_php', array(array('code' => $this->yystack[$this->yyidx + 0]->minor),
            array('type' => $this->lex->phpType)), array());
        if ($this->compiler->has_code && !empty($code)) {
            $tmp = '';
            foreach ($this->compiler->prefix_code as $code) {
                $tmp .= $code;
            }
            $this->compiler->prefix_code = array();
            $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp . $code, true));
        } else {
            $this->_retvalue = null;
        }
    }

    #line 256 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r7()
    {
        $this->_retvalue = $this->compiler->processText($this->yystack[$this->yyidx + 0]->minor);
    }

    #line 260 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r8()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 264 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r9()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 269 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r10()
    {
        $this->strip = true;
    }

    #line 273 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r11()
    {
        $this->strip = false;
    }

    #line 277 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r12()
    {
        if ($this->strip) {
            SMARTY_INTERNAL_COMPILE_BLOCK::blockSource($this->compiler, preg_replace('![\t ]*[\r\n]+[\t ]*!', '', $this->yystack[$this->yyidx + 0]->minor));
        } else {
            SMARTY_INTERNAL_COMPILE_BLOCK::blockSource($this->compiler, $this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 286 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r13()
    {
        $this->_retvalue = '';
    }

    #line 290 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r14()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }

    #line 294 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r15()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 310 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r19()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }

    #line 316 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r20()
    {
        $var = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length), ' $');
        if (preg_match('/^(.*)(\s+nocache)$/', $var, $match)) {
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', array('nocache'), array('value' => $this->compiler->compileVariable('\'' . $match[1] . '\'')));
        } else {
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->compiler->compileVariable('\'' . $var . '\'')));
        }
    }

    #line 326 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r21()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 330 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r22()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value'        => $this->yystack[$this->yyidx + - 2]->minor,
                                                                                                                                  'modifierlist' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 334 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r23()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 348 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r27()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value'        => $this->yystack[$this->yyidx + - 2]->minor,
                                                                                                                                  'modifierlist' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 361 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r29()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array(array('value' => $this->yystack[$this->yyidx + 0]->minor),
            array('var' => '\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '\'')));
    }

    #line 369 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r31()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array_merge(array(array('value' => $this->yystack[$this->yyidx + - 1]->minor),
                                                                                 array('var' => '\'' . substr($this->yystack[$this->yyidx + - 3]->minor, 1) . '\'')), $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 373 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r32()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array_merge(array(array('value' => $this->yystack[$this->yyidx + - 1]->minor),
                                                                                 array('var' => $this->yystack[$this->yyidx + - 3]->minor['var'])), $this->yystack[$this->yyidx + 0]->minor), array('smarty_internal_index' => $this->yystack[$this->yyidx + - 3]->minor['smarty_internal_index']));
    }

    #line 378 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r33()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length));
        if ($tag == 'strip') {
            $this->strip = true;
            $this->_retvalue = null;;
        } else {
            if (defined($tag)) {
                if ($this->security) {
                    $this->security->isTrustedConstant($tag, $this->compiler);
                }
                $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $tag));
            } else {
                if (preg_match('/^(.*)(\s+nocache)$/', $tag, $match)) {
                    $this->_retvalue = $this->compiler->compileTag($match[1], array('nocache'));
                } else {
                    $this->_retvalue = $this->compiler->compileTag($tag, array());
                }
            }
        }
    }

    #line 400 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r34()
    {
        if (defined($this->yystack[$this->yyidx + - 1]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + - 1]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 1]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 410 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r35()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->yystack[$this->yyidx + 0]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor, array());
        }
    }

    #line 423 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r36()
    {
        if (defined($this->yystack[$this->yyidx + - 2]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + - 2]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value'        => $this->yystack[$this->yyidx + - 2]->minor,
                                                                                                                                      'modifierlist' => $this->yystack[$this->yyidx + - 1]->minor));
        } else {
            $this->_retvalue = '<?php ob_start();?>' . $this->compiler->compileTag($this->yystack[$this->yyidx + - 2]->minor, $this->yystack[$this->yyidx + 0]->minor) . '<?php echo ';
            $this->_retvalue .= $this->compiler->compileTag('private_modifier', array(), array('modifierlist' => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                               'value'        => 'ob_get_clean()')) . ';?>';
        }
    }

    #line 436 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r37()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 3]->minor, $this->yystack[$this->yyidx + 0]->minor, array('object_method' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 441 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r38()
    {
        $this->_retvalue = '<?php ob_start();?>' . $this->compiler->compileTag($this->yystack[$this->yyidx + - 4]->minor, $this->yystack[$this->yyidx + 0]->minor, array('object_method' => $this->yystack[$this->yyidx + - 2]->minor)) . '<?php echo ';
        $this->_retvalue .= $this->compiler->compileTag('private_modifier', array(), array('modifierlist' => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                           'value'        => 'ob_get_clean()')) . ';?>';
    }

    #line 447 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r39()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 1]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, array(), array('if condition' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 452 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r40()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 2]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, $this->yystack[$this->yyidx + 0]->minor, array('if condition' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 457 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r41()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 1]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, array(), array('if condition' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 468 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r43()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 6]->minor),
            array('ifexp' => $this->yystack[$this->yyidx + - 4]->minor),
            array('var' => $this->yystack[$this->yyidx + - 2]->minor),
            array('step' => $this->yystack[$this->yyidx + - 1]->minor))), 1);
    }

    #line 472 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r44()
    {
        $this->_retvalue = '=' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 480 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r46()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 3]->minor),
            array('to' => $this->yystack[$this->yyidx + - 1]->minor))), 0);
    }

    #line 484 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r47()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 5]->minor),
            array('to' => $this->yystack[$this->yyidx + - 3]->minor),
            array('step' => $this->yystack[$this->yyidx + - 1]->minor))), 0);
    }

    #line 489 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r48()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 494 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r49()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('from' => $this->yystack[$this->yyidx + - 3]->minor),
            array('item' => $this->yystack[$this->yyidx + - 1]->minor))));
    }

    #line 498 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r50()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('from' => $this->yystack[$this->yyidx + - 5]->minor),
            array('item' => $this->yystack[$this->yyidx + - 1]->minor),
            array('key' => $this->yystack[$this->yyidx + - 3]->minor))));
    }

    #line 511 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r53()
    {
        $this->_retvalue = $this->compiler->compileTag('setfilter', array(), array('modifier_list' => array(array_merge(array($this->yystack[$this->yyidx + - 1]->minor), $this->yystack[$this->yyidx + 0]->minor))));
    }

    #line 515 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r54()
    {
        $this->_retvalue = $this->compiler->compileTag('setfilter', array(), array('modifier_list' => array_merge(array(array_merge(array($this->yystack[$this->yyidx + - 2]->minor), $this->yystack[$this->yyidx + - 1]->minor)), $this->yystack[$this->yyidx + 0]->minor)));
    }

    #line 520 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r55()
    {
        $j = strrpos($this->yystack[$this->yyidx + 0]->minor, '.');
        if ($this->yystack[$this->yyidx + 0]->minor[$j + 1] == 'c') {
            // {$smarty.block.child}
            $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileChildBlock($this->compiler);
        } else {
            // {$smarty.block.parent}
            $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileParentBlock($this->compiler);
        }
    }

    #line 533 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r56()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length), ' /');
        if ($tag == 'strip') {
            $this->strip = false;
            $this->_retvalue = null;
        } else {
            $this->_retvalue = $this->compiler->compileTag($tag . 'close', array());
        }
    }

    #line 542 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r57()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor . 'close', array());
    }

    #line 546 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r58()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 1]->minor . 'close', array(), array('modifier_list' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 551 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r59()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 2]->minor . 'close', array(), array('object_method' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 555 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r60()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 3]->minor . 'close', array(), array('object_method' => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                                                           'modifier_list' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 563 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r61()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
        $this->_retvalue[] = $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 569 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r62()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }

    #line 574 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r63()
    {
        $this->_retvalue = array();
    }

    #line 579 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r64()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => $this->yystack[$this->yyidx + 0]->minor);
        } else {
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'');
        }
    }

    #line 590 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r65()
    {
        $this->_retvalue = array(trim($this->yystack[$this->yyidx + - 1]->minor, " =\n\r\t") => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 598 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r67()
    {
        $this->_retvalue = '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'';
    }

    #line 610 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r70()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 623 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r72()
    {
        $this->yystack[$this->yyidx + - 2]->minor[] = $this->yystack[$this->yyidx + 0]->minor;
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor;
    }

    #line 628 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r73()
    {
        $this->_retvalue = array('var'   => '\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '\'',
                                 'value' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 635 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r75()
    {
        $this->_retvalue = array('var'   => $this->yystack[$this->yyidx + - 2]->minor,
                                 'value' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 659 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r79()
    {
        $this->_retvalue = '$_smarty_tpl->getStreamVariable(\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '://' . $this->yystack[$this->yyidx + 0]->minor . '\')';
    }

    #line 664 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r80()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . trim($this->yystack[$this->yyidx + - 1]->minor) . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 678 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r83()
    {
        $this->_retvalue = $this->compiler->compileTag('private_modifier', array(), array('value'        => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                          'modifierlist' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 684 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r84()
    {
        $this->_retvalue = (isset($this->yystack[$this->yyidx + - 1]->minor['pre']) ? $this->yystack[$this->yyidx + - 1]->minor['pre'] : '') . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor['op'] . $this->yystack[$this->yyidx + 0]->minor . (isset($this->yystack[$this->yyidx + - 1]->minor['pre']) ? ')' : '');
    }

    #line 687 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r85()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 691 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r86()
    {
        $this->_retvalue = 'in_array(' . $this->yystack[$this->yyidx + - 2]->minor . ',' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }

    #line 695 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r87()
    {
        $this->_retvalue = 'in_array(' . $this->yystack[$this->yyidx + - 2]->minor . ',(array)' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }

    #line 699 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r88()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 707 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r89()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 5]->minor . ' ? ' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '\'') . ' : ' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 711 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r90()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 5]->minor . ' ? ' . $this->yystack[$this->yyidx + - 2]->minor . ' : ' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 726 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r93()
    {
        $this->_retvalue = '!' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 747 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r98()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '.' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 751 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r99()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . '.';
    }

    #line 755 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r100()
    {
        $this->_retvalue = '.' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 760 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r101()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'';
        }
    }

    #line 777 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r103()
    {
        $this->_retvalue = "(" . $this->yystack[$this->yyidx + - 1]->minor . ")";
    }

    #line 792 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r106()
    {
        self::$prefix_number ++;
        if ($this->yystack[$this->yyidx + - 2]->minor['var'] == '\'smarty\'') {
            $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . ' = ' . $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + - 2]->minor['smarty_internal_index']) . ';?>';
        } else {
            $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . ' = ' . $this->compiler->compileVariable($this->yystack[$this->yyidx + - 2]->minor['var']) . $this->yystack[$this->yyidx + - 2]->minor['smarty_internal_index'] . ';?>';
        }
        $this->_retvalue = '$_tmp' . self::$prefix_number . '::' . $this->yystack[$this->yyidx + 0]->minor[0] . $this->yystack[$this->yyidx + 0]->minor[1];
    }

    #line 803 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r107()
    {
        self::$prefix_number ++;
        $tmp = $this->compiler->appendCode('<?php ob_start();?>', $this->yystack[$this->yyidx + 0]->minor);
        $this->compiler->prefix_code[] = $this->compiler->appendCode($tmp, '<?php $_tmp' . self::$prefix_number . '=ob_get_clean();?>');
        $this->_retvalue = '$_tmp' . self::$prefix_number;
    }

    #line 820 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r110()
    {
        if (!in_array(strtolower($this->yystack[$this->yyidx + - 2]->minor), array('self',
                'parent')) && (!$this->security || $this->security->isTrustedStaticClassAccess($this->yystack[$this->yyidx + - 2]->minor, $this->yystack[$this->yyidx + 0]->minor, $this->compiler))
        ) {
            if (isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor])) {
                $this->_retvalue = $this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor] . '::' . $this->yystack[$this->yyidx + 0]->minor[0] . $this->yystack[$this->yyidx + 0]->minor[1];
            } else {
                $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '::' . $this->yystack[$this->yyidx + 0]->minor[0] . $this->yystack[$this->yyidx + 0]->minor[1];
            }
        } else {
            $this->compiler->trigger_template_error("static class '" . $this->yystack[$this->yyidx + - 2]->minor . "' is undefined or not allowed by security setting");
        }
    }

    #line 854 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r113()
    {
        $this->_retvalue = $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\'');
    }

    #line 857 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r114()
    {
        if ($this->yystack[$this->yyidx + 0]->minor['var'] == '\'smarty\'') {
            $smarty_var = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index']);
            $this->_retvalue = $smarty_var;
        } else {
            // used for array reset,next,prev,end,current 
            $this->last_variable = $this->yystack[$this->yyidx + 0]->minor['var'];
            $this->last_index = $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
            $this->_retvalue = $this->compiler->compileVariable($this->yystack[$this->yyidx + 0]->minor['var']) . $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
        }
    }

    #line 870 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r115()
    {
        $this->_retvalue = '$_smarty_tpl->tpl_vars[' . $this->yystack[$this->yyidx + - 2]->minor . ']->' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 880 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r117()
    {
        $this->_retvalue = '$_smarty_tpl->getConfigVariable( \'' . $this->yystack[$this->yyidx + - 1]->minor . '\')';
    }

    #line 884 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r118()
    {
        $this->_retvalue = '(is_array($tmp = $_smarty_tpl->getConfigVariable( \'' . $this->yystack[$this->yyidx + - 2]->minor . '\')) ? $tmp' . $this->yystack[$this->yyidx + 0]->minor . ' :null)';
    }

    #line 888 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r119()
    {
        $this->_retvalue = '$_smarty_tpl->getConfigVariable( ' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 892 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r120()
    {
        $this->_retvalue = '(is_array($tmp = $_smarty_tpl->getConfigVariable( ' . $this->yystack[$this->yyidx + - 2]->minor . ')) ? $tmp' . $this->yystack[$this->yyidx + 0]->minor . ' : null)';
    }

    #line 896 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r121()
    {
        $this->_retvalue = array('var'                   => '\'' . substr($this->yystack[$this->yyidx + - 1]->minor, 1) . '\'',
                                 'smarty_internal_index' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 899 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r122()
    {
        $this->_retvalue = array('var'                   => $this->yystack[$this->yyidx + - 1]->minor,
                                 'smarty_internal_index' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 912 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r124()
    {
        return;
    }

    #line 918 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r125()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\'') . ']';
    }

    #line 921 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r126()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable($this->yystack[$this->yyidx + 0]->minor) . ']';
    }

    #line 925 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r127()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable($this->yystack[$this->yyidx + - 2]->minor) . '->' . $this->yystack[$this->yyidx + 0]->minor . ']';
    }

    #line 929 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r128()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = '[' . $this->yystack[$this->yyidx + 0]->minor . ']';
        } else {
            $this->_retvalue = "['" . $this->yystack[$this->yyidx + 0]->minor . "']";
        }
    }

    #line 940 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r129()
    {
        $this->_retvalue = '[' . $this->yystack[$this->yyidx + 0]->minor . ']';
    }

    #line 945 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r130()
    {
        $this->_retvalue = '[' . $this->yystack[$this->yyidx + - 1]->minor . ']';
    }

    #line 950 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r131()
    {
        $this->_retvalue = '[' . $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' . $this->yystack[$this->yyidx + - 1]->minor . '\'][\'index\']') . ']';
    }

    #line 954 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r132()
    {
        $this->_retvalue = '[' . $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' . $this->yystack[$this->yyidx + - 3]->minor . '\'][\'' . $this->yystack[$this->yyidx + - 1]->minor . '\']') . ']';
    }

    #line 957 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r133()
    {
        $this->_retvalue = '[' . $this->yystack[$this->yyidx + - 1]->minor . ']';
    }

    #line 963 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r135()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + - 1]->minor, 1) . '\'') . ']';;
    }

    #line 979 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r139()
    {
        $this->_retvalue = '[]';
    }

    #line 989 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r140()
    {
        $this->_retvalue = '\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\'';
    }

    #line 993 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r141()
    {
        $this->_retvalue = "''";
    }

    #line 998 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r142()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . '.' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1006 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r144()
    {
        $var = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length), ' $');
        $this->_retvalue = $this->compiler->compileVariable('\'' . $var . '\'');
    }

    #line 1012 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r145()
    {
        $this->_retvalue = '(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 1019 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r146()
    {
        if ($this->yystack[$this->yyidx + - 1]->minor['var'] == '\'smarty\'') {
            $this->_retvalue = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + - 1]->minor['smarty_internal_index']) . $this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = $this->compiler->compileVariable($this->yystack[$this->yyidx + - 1]->minor['var']) . $this->yystack[$this->yyidx + - 1]->minor['smarty_internal_index'] . $this->yystack[$this->yyidx + 0]->minor;
        }
    }

    #line 1028 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r147()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1033 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r148()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1038 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r149()
    {
        if ($this->security && substr($this->yystack[$this->yyidx + - 1]->minor, 0, 1) == '_') {
            $this->compiler->trigger_template_error(self::Err1);
        }
        $this->_retvalue = '->' . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1045 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r150()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{' . $this->compiler->compileVariable($this->yystack[$this->yyidx + - 1]->minor) . $this->yystack[$this->yyidx + 0]->minor . '}';
    }

    #line 1052 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r151()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + 0]->minor . '}';
    }

    #line 1059 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r152()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{\'' . $this->yystack[$this->yyidx + - 4]->minor . '\'.' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + 0]->minor . '}';
    }

    #line 1067 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r153()
    {
        $this->_retvalue = '->' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1075 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r154()
    {
        if (!$this->security || $this->security->isTrustedPhpFunction($this->yystack[$this->yyidx + - 3]->minor, $this->compiler)) {
            if (strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'isset') === 0 || strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'empty') === 0 || strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'array') === 0 || is_callable($this->yystack[$this->yyidx + - 3]->minor)) {
                $func_name = strtolower($this->yystack[$this->yyidx + - 3]->minor);
                if ($func_name == 'isset') {
                    if (count($this->yystack[$this->yyidx + - 1]->minor) == 0) {
                        $this->compiler->trigger_template_error('Illegal number of paramer in "isset()"');
                    }
                    $par = implode(',', $this->yystack[$this->yyidx + - 1]->minor);
                    if (strncasecmp($par, '$_smarty_tpl->getConfigVariable', strlen('$_smarty_tpl->getConfigVariable')) === 0) {
                        self::$prefix_number ++;
                        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . str_replace(')', ', false)', $par) . ';?>';
                        $isset_par = '$_tmp' . self::$prefix_number;
                    } else {
                        $isset_par = str_replace("')->value", "',null,true,false)->value", $par);
                    }
                    $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . $isset_par . ")";
                } elseif (in_array($func_name, array('empty', 'reset', 'current', 'end', 'prev', 'next'))) {
                    if (count($this->yystack[$this->yyidx + - 1]->minor) != 1) {
                        $this->compiler->trigger_template_error('Illegal number of paramer in "empty()"');
                    }
                    if ($func_name == 'empty') {
                        $this->_retvalue = $func_name . '(' . str_replace("')->value", "',null,true,false)->value", $this->yystack[$this->yyidx + - 1]->minor[0]) . ')';
                    } else {
                        $this->_retvalue = $func_name . '(' . $this->yystack[$this->yyidx + - 1]->minor[0] . ')';
                    }
                } else {
                    $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ")";
                }
            } else {
                $this->compiler->trigger_template_error("unknown function \"" . $this->yystack[$this->yyidx + - 3]->minor . "\"");
            }
        }
    }

    #line 1114 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r155()
    {
        if ($this->security && substr($this->yystack[$this->yyidx + - 3]->minor, 0, 1) == '_') {
            $this->compiler->trigger_template_error(self::Err1);
        }
        $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ")";
    }

    #line 1121 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r156()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        self::$prefix_number ++;
        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + - 3]->minor, 1) . '\'') . ';?>';
        $this->_retvalue = '$_tmp' . self::$prefix_number . '(' . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ')';
    }

    #line 1132 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r157()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 2]->minor, array($this->yystack[$this->yyidx + 0]->minor));
    }

    #line 1149 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r160()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 2]->minor, array(array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor)));
    }

    #line 1153 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r161()
    {
        $this->_retvalue = array(array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 1161 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r163()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1169 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r164()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1188 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r168()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor, '', 'method');
    }

    #line 1193 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r169()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor,
            'method');
    }

    #line 1198 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r170()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor, '');
    }

    #line 1203 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r171()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor,
            'property');
    }

    #line 1208 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r172()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor,
            $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor, 'property');
    }

    #line 1214 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r173()
    {
        $this->_retvalue['op'] = ' ' . trim($this->yystack[$this->yyidx + 0]->minor) . ' ';
    }

    #line 1218 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r174()
    {
        static $lops = array('eq'          => array('op' => ' == ', 'pre' => null),
                             'ne'          => array('op' => ' != ', 'pre' => null),
                             'neq'         => array('op' => ' != ', 'pre' => null),
                             'gt'          => array('op' => ' > ', 'pre' => null),
                             'ge'          => array('op' => ' >= ', 'pre' => null),
                             'gte'         => array('op' => ' >= ', 'pre' => null),
                             'lt'          => array('op' => ' < ', 'pre' => null),
                             'le'          => array('op' => ' <= ', 'pre' => null),
                             'lte'         => array('op' => ' <= ', 'pre' => null),
                             'mod'         => array('op' => ' % ', 'pre' => null),
                             'and'         => array('op' => ' && ', 'pre' => null),
                             'or'          => array('op' => ' || ', 'pre' => null),
                             'xor'         => array('op' => ' xor ', 'pre' => null),
                             'isdivby'     => array('op' => ' % ', 'pre' => '!('),
                             'isnotdivby'  => array('op' => ' % ', 'pre' => '('),
                             'isevenby'    => array('op' => ' / ', 'pre' => '!(1 & '),
                             'isnotevenby' => array('op' => ' / ', 'pre' => '(1 & '),
                             'isoddby'     => array('op' => ' / ', 'pre' => '(1 & '),
                             'isnotoddby'  => array('op' => ' / ', 'pre' => '!(1 & '),);
        $op = strtolower(str_replace(' ', '', $this->yystack[$this->yyidx + 0]->minor));
        $this->_retvalue = $lops[$op];
    }

    #line 1244 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r175()
    {
        static $scond = array('iseven'   => '!(1 & ', 'isnoteven' => '(1 & ', 'isodd' => '(1 & ',
                              'isnotodd' => '!(1 & ',);
        $op = strtolower(str_replace(' ', '', $this->yystack[$this->yyidx + 0]->minor));
        $this->_retvalue = $scond[$op];
    }

    #line 1258 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r176()
    {
        $this->_retvalue = 'array(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 1266 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r178()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . ',' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1274 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r180()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '=>' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1278 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r181()
    {
        $this->_retvalue = '\'' . $this->yystack[$this->yyidx + - 2]->minor . '\'=>' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1294 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r184()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor->to_smarty_php();
    }

    #line 1299 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r185()
    {
        $this->yystack[$this->yyidx + - 1]->minor->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }

    #line 1304 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r186()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Dq($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1308 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r187()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code($this, '(string)' . $this->yystack[$this->yyidx + - 1]->minor);
    }

    #line 1316 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r189()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code($this, '(string)$_smarty_tpl->tpl_vars[\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\']->value');
    }

    #line 1324 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r191()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code($this, '(string)(' . $this->yystack[$this->yyidx + - 1]->minor . ')');
    }

    #line 1328 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r192()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1332 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r193()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_DqContent($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    private $_retvalue;

    public function yy_reduce($yyruleno)
    {
        if ($this->yyTraceFILE && $yyruleno >= 0 && $yyruleno < count(self::$yyRuleName)) {
            fprintf($this->yyTraceFILE, "%sReduce (%d) [%s].\n", $this->yyTracePrompt, $yyruleno, self::$yyRuleName[$yyruleno]);
        }

        $this->_retvalue = $yy_lefthand_side = null;
        if (isset(self::$yyReduceMap[$yyruleno])) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[$yyruleno]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[$yyruleno][0];
        $yysize = self::$yyRuleInfo[$yyruleno][1];
        $this->yyidx -= $yysize;
        for ($i = $yysize; $i; $i --) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!$this->yyTraceFILE && $yysize) {
                $this->yyidx ++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[$this->yyidx] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact == self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    public function yy_parse_failed()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sFail!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    public function yy_syntax_error($yymajor, $TOKEN)
    {
        #line 183 "../smarty/lexer/smarty_internal_templateparser.y"

        $this->internalError = true;
        $this->yymajor = $yymajor;
        $this->compiler->trigger_template_error();
    }

    public function yy_accept()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sAccept!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
        #line 176 "../smarty/lexer/smarty_internal_templateparser.y"

        $this->successful = !$this->internalError;
        $this->internalError = false;
        $this->retvalue = $this->_retvalue;
    }

    public function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */

        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = - 1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            $this->yystack[] = $x;
        }
        $yyendofinput = ($yymajor == 0);

        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sInput %s\n", $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
        }

        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL && !$this->yy_is_expected_token($yymajor)) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt --;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact == self::YY_ERROR_ACTION) {
                if ($this->yyTraceFILE) {
                    fprintf($this->yyTraceFILE, "%sSyntax Error!\n", $this->yyTracePrompt);
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[$this->yyidx]->major;
                    if ($yymx == self::YYERRORSYMBOL || $yyerrorhit) {
                        if ($this->yyTraceFILE) {
                            fprintf($this->yyTraceFILE, "%sDiscard input token %s\n", $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 && $yymx != self::YYERRORSYMBOL && ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE) {
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor == 0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx != self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor != self::YYNOCODE && $this->yyidx >= 0);
    }
}

