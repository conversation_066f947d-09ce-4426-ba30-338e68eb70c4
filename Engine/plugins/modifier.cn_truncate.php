<?php
/**
 * Smarty plugin
 * Smarty Interception of Chinese characters plugin
 *
 * time:     2011-6-23<br>
 * <AUTHOR>
 * @param string
 * @return integer
 */
function   smarty_modifier_cn_truncate($string,$length,$etc='...',$keep_first_style = true)   
{   
  $result   =   '';
  $string   =   html_entity_decode(trim(strip_tags($string)),   ENT_QUOTES,   'UTF-8');     
  //$string   =   trim($string);
  $strlen   =   strlen($string);
  for($i   =   0;   (($i   <   $strlen)   &&   ($length   >   0));   $i++)   
  {   
		  if($number   =   strpos(str_pad(decbin(ord(substr($string,   $i,   1))),   8,   '0',   STR_PAD_LEFT),   '0'))   
		  {   
				  if($length   <   1.0)   
				  {
					  break;   
				  }
				  $result   .=   substr($string,   $i,   $number);
				  $length   -=   1.0;
				  $i   +=   $number   -   1;   
		  }   
		  else   
		  {   
				  $result   .=   substr($string,   $i,   1); 
				  $length   -=   0.5;
		  }   
  } 
  $result   =   htmlspecialchars($result,   ENT_QUOTES,   'UTF-8');
  if($i   <   $strlen)   
  {   
		  $result   .=   $etc;   
  } 
  return   $result;   
}   
?>